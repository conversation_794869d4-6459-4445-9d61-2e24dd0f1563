import http from '../common/request/request'

// 查询购物车
export const queryShoppingCart = () => {
  return http({
    url: 'basic/shoppingOnlineStore/queryShoppingCart',
    param: {
      openid: uni.getStorageSync('wxInfo').openId,
    },
    method: 'post',
  })
}

// 查询购物车(新)
export const queryShoppingCartGroupByDrugStore = () => {
  return http({
    url: 'basic/shoppingOnlineStore/queryShoppingCartGroupByDrugStore',
    param: {
      openid: uni.getStorageSync('wxInfo').openId,
    },
    method: 'post',
  })
}

// 添加购物车
export const addShoppingCart = (param) => {
  param.openid = uni.getStorageSync('wxInfo').openId
  return http({
    url: 'basic/shoppingOnlineStore/addShoppingCart',
    param,
    method: 'post',
  })
}

// 修改购物车
export const updateShoppingCart = (param) => {
  param.openid = uni.getStorageSync('wxInfo').openId
  return http({
    url: 'basic/shoppingOnlineStore/updateShoppingCart',
    param,
    method: 'post',
  })
}

// 删除购物车
export const deleteShoppingCart = (arr) => {
  return http({
    url: 'basic/shoppingOnlineStore/deleteShoppingCart',
    param: {
      openid: uni.getStorageSync('wxInfo').openId,
      yfkcIdList: arr,
    },
    method: 'post',
  })
}

// 订单详情
export const queryOrderByShoppingCart = (param) => {
  return http({
    url: 'basic/shoppingOnlineStore/queryOrderByShoppingCart',
    param: {
      openid: uni.getStorageSync('wxInfo').openId,
    },
    method: 'post',
  })
}

// 处方调剂费
export const queryPrescriptionAdjustMoney = (param) => {
  return http({
    url: 'basic/shoppingOnlineStore/queryPrescriptionAdjustMoney',
    param,
    method: 'post',
  })
}

// 自费提示
export const querySelfExtractionTips = () => {
  return http({
    url: 'basic/shoppingOnlineStore/querySelfExtractionTips',
    method: 'post',
  })
}

// 支付提示
export const queryToPayTips = () => {
  return http({
    url: 'basic/shoppingOnlineStore/queryToPayTips',
    method: 'post',
  })
}

// 物流提示
export const queryLogisticsTips = () => {
  return http({
    url: 'basic/shoppingOnlineStore/queryLogisticsTips',
    method: 'post',
  })
}

// 搜索药品
export const onlineDrugPurchaseQuery = (param) => {
  param.openid = uni.getStorageSync('wxInfo').openId
  return http({
    url: 'basic/drugStorageYf/onlineDrugPurchaseQuery',
    param,
    method: 'post',
  })
}

// 药品列表 线下购药
export const getOfflineDrug = (param) => {
  // param.openid = uni.getStorageSync("wxInfo").openId;
  return http({
    url: 'basic/dicDrugController/getOfflineDrug',
    param,
    method: 'post',
  })
}

// 药品列表 经纬度
export const onlineDrugPurchaseQueryByLatLng = (param) => {
  param.openid = uni.getStorageSync('wxInfo').openId
  return http({
    url: 'basic/drugStorageYf/onlineDrugPurchaseQueryByLatLng',
    param,
    method: 'post',
  })
}

// 查询药品分类
export const getDrugStoreTypeList = (param) => {
  return http({
    url: 'basic/drugStoreType/getDrugStoreTypeList',
    param,
    method: 'post',
  })
}

// 购物车支付
export const createMallOrder = (param) => {
  return http({
    url: 'order/onlineMallOrder/createMallOrder',
    param,
    method: 'post',
  })
}

// 查询支付状态（提交需求）
export const queryMallOrderStatus = (orderNo) => {
  return http({
    url: 'order/onlineMallOrder/queryMallOrderStatus',
    param: {
      orderNo,
    },
    method: 'post',
  })
}

/**
 * 查询订单详情快捷商城 (新)
 * @param {Array} drugStoreIdList 药店id列表
 * @param {String} openid 患者openid
 * @returns {Object} 详情
 * */
export const queryOrderByShoppingCartFastOnline = (drugStoreIdList, isOff) => {
  let openid = uni.getStorageSync('wxInfo').openId
  return http({
    url: 'basic/shoppingOnlineStore/queryOrderByShoppingCartFastOnline',
    param: {
      drugStoreIdList,
      openid,
      isOff,
    },
    method: 'post',
  })
}

/**
 * 查询药品实际支付金额和应付金额
 * @param {String} quan 数量
 * @param {String} yfkcId 药品库存id
 * @returns 药品总价 优惠价
 * */
export const queryPayByKcIdAndQuan = (param) => {
  return http({
    url: 'basic/shoppingOnlineStore/queryPayByKcIdAndQuan',
    param,
    method: 'post',
  })
}

/**
 * 验证购物车库存
 * @param {Array} drugStoreIdList 药店id列表
 * @returns {null} 成功可下一步 报错则停止
 * */
export const getShoppingCartOrderCantPay = (drugStoreIdList) => {
  let openid = uni.getStorageSync('wxInfo').openId
  return http({
    url: 'basic/shoppingOnlineStore/getShoppingCartOrderCantPay',
    param: {
      openid,
      drugStoreIdList,
    },
    method: 'post',
  })
}

/**
 * 创建商城快捷购药订单
 */
export const createFastMallOrder = (param) => {
  return http({
    url: 'order/fastOnlineMallOrder/createFastMallOrder',
    param,
    method: 'post',
  })
}

/**
 * 修改订单但不修改金额
 */
export const updateOrderButNotUpdateMoney = (param) => {
  return http({
    url: 'order/fastOnlineMallOrder/updateOrderButNotUpdateMoney',
    param,
    method: 'post',
  })
}

/**
 * 查询商城快捷购药订单(修改订单)
 */
export const queryUpdateFastMallOrder = (orderNo) => {
  return http({
    url: 'order/fastOnlineMallOrder/queryUpdateFastMallOrder',
    param: { orderNo },
    method: 'post',
  })
}

/**
 * 查询商城快捷购药订单(再次购买)
 */
export const queryFastMallOrder = (orderNo) => {
  return http({
    url: 'order/fastOnlineMallOrder/queryFastMallOrder',
    param: { orderNo },
    method: 'post',
  })
}

/**
 * 再次购买添加购物车商品
 */
export const secondAddShoppingCart = (orderNo) => {
  return http({
    url: 'basic/shoppingOnlineStore/secondAddShoppingCart',
    param: { orderNo },
    method: 'post',
  })
}

/**
 * 查询订单是否在审核中
 */
export const queryFastMallOrderStatusIschecking = (orderNo) => {
  return http({
    url: 'order/fastOnlineMallOrder/queryFastMallOrderStatusIschecking',
    param: { orderNo },
    method: 'post',
  })
}

/**
 * 查询商城快捷购药订单退费
 */
export const tradeRefundFastMallOrder = (orderNo) => {
  return http({
    url: 'order/fastOnlineMallOrder/tradeRefundFastMallOrder',
    param: { orderNo },
    method: 'post',
  })
}

/**
 * 修改商城快捷购药订单
 */
export const updateFastMallOrder = (param) => {
  return http({
    url: 'order/fastOnlineMallOrder/updateFastMallOrder',
    param,
    method: 'post',
  })
}

/**
 * 从订单列表去支付
 */
export const secondSaveFastMallOrder = (param) => {
  return http({
    url: 'order/fastOnlineMallOrder/secondSaveFastMallOrder',
    param,
    method: 'post',
  })
}

/**
 * 商城订单
 */
export const queryOrderFastMall = (param) => {
  return http({
    url: 'order/payOrderPatient/queryOrderFastMall',
    param,
    method: 'post',
  })
}

/**
 * 修改订单补开发票
 */
export const updateOrderInvoice = (param) => {
  return http({
    url: 'order/fastOnlineMallOrder/updateOrderInvoice',
    param,
    method: 'post',
  })
}

/**
 * 新商城购药支付状态查询
 */

export const queryFastMallOrderStatus = (orderNo) => {
  return http({
    url: 'order/fastOnlineMallOrder/queryFastMallOrderStatus',
    param: { orderNo },
    method: 'post',
  })
}

/**
 * 退费申请
 * @param {Array} applicationPictureList 图片列表
 * @param {String} orderNo 订单号
 * @param {String} reasonsForapplication 退款理由
 */
export const fastMallOrderRefundAudit = (param) => {
  return http({
    url: 'order/fastOnlineMallOrder/fastMallOrderRefundAudit',
    param,
    method: 'post',
  })
}

/**
 * 获取配置选项
 * @param {String} codeType 类型
 */
export const getSysCodeByType = (codeType) => {
  return http({
    url: '/basic/syscode/getSysCodeByType',
    method: 'post',
    param: {
      codeType,
    },
  })
}

/**
 * 退费申请
 * @param {Array} applicationPictureList 图片列表
 * @param {String} orderNo 订单号
 * @param {String} reasonsForapplication 退款理由
 */
export const tradeRefundAndAddYf = (param) => {
  return http({
    url: 'order/payOrder/tradeRefundAndAddYf',
    param,
    method: 'post',
  })
}
/**
 * 查询店员配送信息
 * @param {string} orderNo 订单号
 */
export const queryClerkDelivery = (orderNo) => {
  return http({
    url: 'order/fastOnlineMallOrder/queryClerkDelivery',
    method: 'post',
    param: {
      orderNo,
    },
  })
}

/**
 * 确认收货
 * @param {string} orderNo 订单号
 */
export const confirmGoods = (orderNo) => {
  return http({
    url: 'order/fastOnlineMallOrder/confirmGoods',
    method: 'post',
    param: {
      orderNo,
    },
  })
}

/**
 * 新商城 订单详情
 * @param {string} orderNo 订单编号
 */
export const orderFastMallNewList = (orderNo) => {
  return http({
    url: 'order/payOrderPatient/orderFastMallNewList',
    method: 'post',
    param: {
      orderNo,
    },
  })
}

export const getCurrentCAType = () => {
  return http({
    url: 'business/proPrescriptionController/getCurrentCAType',
    method: 'post',
    param: {},
  })
}

// 申请退费
export const refundAndAddYf = (data) => {
  return http({
    url: 'business/paymentBusiness/refundAndAddYf',
    method: 'post',
    param: data,
  })
}

// 取消支付
export const cancelOrderzf = (data) => {
  return http({
    url: 'order/payOrder/cancelOrder',
    method: 'post',
    param: data,
  })
}
export const getLogisticsCost = (param = {}) => {
  return http({
    url: 'basic/shoppingOnlineStore/getLogisticsCost',
    param,
    method: 'post',
  })
}
export const createScanCodeOrderNew = (param) => {
  return http({
    url: 'order/scanCodePrescribing/createScanCodeOrderNew',
    param,
    method: 'post',
  })
}
export const updateScanCodeRegister = (param) => {
  return http({
    url: 'business/paymentBusiness/updateScanCodeRegister',
    param,
    method: 'post',
  })
}
export const queryQuickPrescriptionSM = (param) => {
  return http({
    url: 'business/quickPrescription/queryQuickPrescriptionSM',
    param,
    method: 'post',
  })
}
export const getDrugStoreInfoPage2 = (param = {}) => {
  return http({
    url: 'basic/dicDrugStore/getDrugStoreInfoPage',
    param,
    method: 'post',
  })
}
export const getLogisticsCostByDrugStore = (param = {}) => {
  return http({
    url: 'basic/shoppingOnlineStore/getLogisticsCostByDrugStore',
    param,
    method: 'post',
  })
}

/**
 * 查询药品适应症
 * @param {Array} drugIds 药品id列表
 */
export const batchGetDrugAdaptation = (param) => {
  return http({
    url: 'basic/dicDrugController/getDrugIndication',
    param,
    method: 'post',
  })
}
