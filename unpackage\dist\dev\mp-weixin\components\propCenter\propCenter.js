"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  props: {
    type: {
      type: Number,
      default: 1
    },
    buttontype: {
      type: Boolean,
      default: false
    },
    buttonCount: {
      type: Number,
      default: 5
    },
    buttonText: {
      type: String,
      default: "确定"
    }
  },
  data() {
    return {};
  },
  mounted() {
  },
  methods: {
    modelConfirm() {
      this.$emit("confirmPropCenter");
    },
    modelCancel() {
      this.$emit("cancelPropCenter");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.type == "2"
  }, $props.type == "2" ? {
    b: common_vendor.o((...args) => $options.modelCancel && $options.modelCancel(...args)),
    c: common_vendor.o((...args) => $options.modelConfirm && $options.modelConfirm(...args))
  } : $props.type == "100" ? {
    e: common_vendor.o((...args) => $options.modelConfirm && $options.modelConfirm(...args))
  } : $props.type == "500" ? {} : {
    g: common_vendor.t($props.buttonText + ($props.buttontype ? "(" + $props.buttonCount + "s)" : "")),
    h: common_vendor.o((...args) => $options.modelConfirm && $options.modelConfirm(...args)),
    i: $props.buttontype == true ? 1 : ""
  }, {
    d: $props.type == "100",
    f: $props.type == "500"
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-bbdbdb82"]]);
wx.createComponent(Component);
