"use strict";
const plugins_xeUtils_map = require("./map.js");
const plugins_xeUtils_isArray = require("./isArray.js");
function deepGetObj(obj, path) {
  var index = 0;
  var len = path.length;
  while (obj && index < len) {
    obj = obj[path[index++]];
  }
  return len && obj ? obj : 0;
}
function invoke(list, path) {
  var func;
  var args = arguments;
  var params = [];
  var paths = [];
  var index = 2;
  var len = args.length;
  for (; index < len; index++) {
    params.push(args[index]);
  }
  if (plugins_xeUtils_isArray.isArray(path)) {
    len = path.length - 1;
    for (index = 0; index < len; index++) {
      paths.push(path[index]);
    }
    path = path[len];
  }
  return plugins_xeUtils_map.map(list, function(context) {
    if (paths.length) {
      context = deepGetObj(context, paths);
    }
    func = context[path] || path;
    if (func && func.apply) {
      return func.apply(context, params);
    }
  });
}
exports.invoke = invoke;
