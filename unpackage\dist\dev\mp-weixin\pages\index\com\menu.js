"use strict";
const common_vendor = require("../../../common/vendor.js");
const mixins_login = require("../../../mixins/login.js");
const _sfc_main = {
  name: "<PERSON>u",
  mixins: [mixins_login.Login],
  props: {
    isAll: {
      type: <PERSON><PERSON>an,
      default: false
    }
  },
  data() {
    let allList = [
      {
        id: 3,
        title: "互医复诊",
        info: "互医复诊",
        icon: "/static/doc/8.png",
        background: "linear-gradient(180deg, rgba(254, 160, 76, 1) 0%, rgba(255, 209, 156, 1) 100%)",
        path: "/pages/register/docList2/index?flag=2"
      },
      {
        id: 2,
        title: "健康咨询",
        info: "健康咨询",
        icon: "/static/doc/路径 5.png",
        background: "linear-gradient(170.85deg, rgba(135, 79, 240, 1) 0%, rgba(197, 187, 242, 1) 100%)",
        path: "/pages/register/docList2/index?flag=1"
      },
      {
        id: 4,
        title: "快速续方",
        info: "快速续方",
        icon: "/static/doc/路径 9.png",
        path: "/pages/quickContinuation/index",
        background: "linear-gradient(180deg, rgba(46, 145, 232, 1) 0%, rgba(179, 219, 255, 1) 100%)"
      },
      {
        id: 5,
        title: "正元门诊",
        info: "正元门诊",
        icon: "/static/doc/路径 8.png",
        background: "linear-gradient(180deg, rgba(60, 184, 163, 1) 0%, rgba(176, 237, 208, 1) 100%)",
        path: "",
        isInDev: true
      }
      // {
      //   id: 1,
      //   title: '智能导诊',
      //   info: '智能快速答复',
      //   icon: '/static/images/index/dz.png',
      //   path: '/pages/guidance/index',
      // },
      // {
      //   id: 1,
      //   title: '健康科普',
      //   info: '健康科普',
      //   background:"linear-gradient(180deg, rgba(60, 184, 163, 1) 0%, rgba(176, 237, 208, 1) 100%)",
      //   icon: '/static/doc/wz.png',
      //   path: '/pages/article/index',
      //   width:'42rpx',
      //   height:'48rpx'
      // },
      // {
      //   id: 4,
      //   title: '医疗瘦身',
      //   info: '在线快捷续方',
      //   icon: '/static/images/index/mbxf.png',
      //   path: '/pages/disease/index',
      // },
    ];
    return {
      list: allList
    };
  },
  methods: {
    toPath(item) {
      common_vendor.index.removeStorageSync("checkCf");
      common_vendor.index.removeStorageSync("addPatientFlag");
      this.$store.commit("setDiagList", []);
      this.$store.commit("setDrugList", []);
      if (item.isInDev) {
        common_vendor.index.showToast({
          title: "正在开发中",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      if (item.id < 4) {
        common_vendor.index.navigateTo({ url: item.path });
        return;
      }
      if (!this.hasInfo())
        return;
      common_vendor.index.navigateTo({ url: item.path });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($data.list, (item, index, i0) => {
      return common_vendor.e({
        a: $props.isAll || item.id != 1
      }, $props.isAll || item.id != 1 ? {
        b: item.icon,
        c: item.width || "60rpx",
        d: item.height || "46rpx",
        e: item.background,
        f: common_vendor.t(item.title),
        g: !$props.isAll ? 1 : "",
        h: common_vendor.o(($event) => $options.toPath(item), index),
        i: index
      } : {});
    })
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-6a3572d7"]]);
wx.createComponent(Component);
