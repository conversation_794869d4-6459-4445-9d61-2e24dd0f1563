"use strict";
require("../common/vendor.js");
const common_request_request = require("../common/request/request.js");
function findOrderByMsgid(msgid) {
  return common_request_request.http({
    url: "order/payOrder/findOrderByMsgid",
    param: {
      msgid
    },
    method: "post"
  });
}
function findOrderByBusinessId(param) {
  return common_request_request.http({
    url: "order/payOrder/findOrderByBusinessId",
    param,
    method: "post"
  });
}
function getPatientChatListSM(param) {
  return common_request_request.http({
    url: "basic/proPatientBusiness/getPatientChatListSM",
    param,
    method: "post"
  });
}
function getPatientChatSM(param) {
  return common_request_request.http({
    url: "basic/proPatientBusiness/getPatientChatSM",
    param,
    method: "post"
  });
}
exports.findOrderByBusinessId = findOrderByBusinessId;
exports.findOrderByMsgid = findOrderByMsgid;
exports.getPatientChatListSM = getPatientChatListSM;
exports.getPatientChatSM = getPatientChatSM;
