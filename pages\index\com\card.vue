<template>
  <!-- 卡片 -->
  <view class="card">
    <!-- 单个 -->
    <view
      class="item"
      v-for="(item, index) in list"
      :key="index"
      @click="toDetail(item)"
    >
      <!-- 顶部 -->
      <view class="type">
        <view class="left">
          <!-- 图标 -->
          <image
            class="icon"
            v-if="item.callType == 5"
            src="/static/home/<USER>"
          />

          <image
            class="icon"
            v-if="item.callType == 6"
            src="/static/home/<USER>"
          />
          <image
            class="icon"
            v-if="item.callType == 1"
            src="/static/home/<USER>"
          />
          <image
              class="icon"
              v-if="item.callType == 7"
              src="/static/home/<USER>"
          />
          <image
            class="icon"
            v-if="item.callType == 3"
            src="/static/home/<USER>"
          />
          <text>患者：{{ item.patientName }}</text>
        </view>
        <view class="status">
          <text>{{ item.statusName }}</text>
        </view>
      </view>

      <!-- 内容 -->
      <view class="item_cont">
        <view class="cont_item">
          <text class="bold" v-if="item.callType == 5"
            >检验项目：{{ item.lisItemName }}</text
          >
          <text class="bold" v-if="item.callType == 6"
            >检查项目：{{ item.pacsTypeName }}</text
          >
          <text class="bold" v-if="item.callType == 1||item.callType == 7"
            >{{ item.docName }}({{ item.deptName }})</text
          >
        </view>

        <view class="cont_item">
          <text v-if="item.callType == 5 || item.callType == 6">{{
            item.orgName
          }}</text>
          <text v-if="item.callType == 1 || item.callType == 3||item.callType == 7">{{
            item.hosName
          }}</text>
        </view>

        <view class="cont_item">
          <text v-if="item.callType == 3">就诊人：{{ item.patientName }}</text>
          <text v-if="item.callType == 5 || item.callType == 6"
            >{{ item.docName }}({{ item.deptName }})</text
          >
        </view>

        <view class="cont_item">
          <text>问诊类型：{{ item.receiveTypeName }}</text>
        </view>
      </view>

      <!-- 时间 -->
      <view class="item_time">
        <block v-if="item.status == 0">
          <block v-if="item.callType == 3">
            处方有效期为<text>{{ unit }}</text
            >天，请及时购药
          </block>
        </block>

        <block v-if="item.status == 1">
          <block v-if="item.callType == 1">
            请尽快支付，超时未支付将自动取消挂号
          </block>

          <block v-if="item.callType == 3">
            正在加急给您发货，请耐心等待
          </block>

          <block v-if="item.callType == 5">
            检验单有效期为<text>{{ unit }}</text
            >天，请及时购买
          </block>

          <block v-if="item.callType == 6">
            检查单有效期为<text>{{ unit }}</text
            >天，请及时购买
          </block>
        </block>

        <block v-if="item.status == 2">
          <block
            v-if="
              item.callType == 1 || item.callType == 5 || item.callType == 6
            "
          >
            请于<text>{{ item.appointSignTime }}</text
            >进行签到
          </block>

          <block v-if="item.callType == 3">
            请尽快到店取药，超时未取，将不会为您保留药品
          </block>
        </block>

        <block v-if="item.status == 3">
          <!-- 待收货 -->
          <block v-if="item.callType == 3">
            已为您发货，点击查看物流信息
          </block>

          <!-- 待检查/验 -->
          <block
            v-if="
              item.callType == 6 || (item.callType == 5 && item.lisType == 1)
            "
          >
            您当前为第 <text>{{ item.queueNumber }}</text> 位，前面还有
            {{ item.frontNum||0 }} 位，请耐心等待
          </block>

          <block v-if="item.callType == 5 && item.lisType == 2">
            样本已签收，等待检验中，请耐心等待
          </block>
        </block>

        <block v-if="item.status == 4">
          <!-- 待接诊 -->
          <block v-if="item.callType == 1">
            您当前为第 <text>{{ item.queueNumber }}</text> 位，前面还有
            {{ item.frontNum ||0}} 位，请耐心等待
          </block>

          <block v-if="item.callType == 5 || item.callType == 6">
            正在加急出报告，请耐心等待
          </block>
        </block>

        <block v-if="item.status == 5">
          <block v-if="item.callType == 1"> 正在问诊中 </block>
        </block>

        <block v-if="item.status == 7">
          <block v-if="item.callType == 5">
            您当前为第 <text>{{ item.queueNumber }}</text> 位，前面还有
            {{ item.frontNum||0 }} 位，请耐心等待
          </block>
        </block>

        <block v-if="item.status == 8">
          <block v-if="item.callType == 5">
            采样完成，正在加急向检验中心配送
          </block>
        </block>

        <block v-if="item.status == 9">
          <block v-if="item.callType == 5"> 样本已发送，请耐心等待 </block>
        </block>
      </view>
<!--      &lt;!&ndash; 底部环形 &ndash;&gt;-->
<!--      <view class="item_footer">-->
<!--        <text v-for="item in 20" :key="item" />-->
<!--      </view>-->
    </view>
  </view>
</template>

<script>
import { getPatientChatSM} from "@/api/order";

export default {
  name: 'Card',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    unit: {
      type: [String, Number],
      default: 7,
    },
  },
  data() {
    return {
      info:{}
    };
  },
  methods: {
    async toDetail(item) {
      this.info={...item}
      console.log('item',item)
      let {
        regId,
        callType,
        pliId,
        ppiId,
        businessId,
        status,
        paymentType,
        hosId,
        docId,
        patientId,
        docName,
        isOffLine,
      } = item;
      patientId = patientId.toLocaleLowerCase();
      if(item.callType==7){

      }else{
        docId = docId.toLocaleLowerCase();
      }
      if (hosId) uni.setStorageSync('hosId', hosId);
      let url;
      switch (callType) {
        // 挂号
        case '1':
          // 小于4 或者是线下
          if (status < 4 || isOffLine == 1) {
            url = '/pages/personalCenter/diagnosisRecord/detail?id=' + regId;
          } else {
            let obj = {
              docId,
              patientId,
              docName,
            };
            this.$store.commit('setEmptyList', obj);
            let chatList = this.$store.getters.getChatList;
            let list = this.$store.getters.getChatListDoc;
            let id = patientId + ',' + docId;
            let item;
            // 如果是数组
            this.$store.commit('SET_CHATLIST', []);//清空(解决只显示该患者与当前医生的消息)
            if (Array.isArray(chatList)) {
              if (list.length) {
                for (let i = 0; i < list.length; i++) {
                  let v = list[i];
                  if (v.id == id) {
                    item = v;
                    break;
                  }
                }
              } else {
                await this.$store.dispatch('getChatListId', {
                  chatId: id,
                });
                item = this.$store.getters.getChatList;
              }
            } else {
              await this.$store.dispatch('getChatListId', {
                chatId: id,
              });
              item = this.$store.getters.getChatList;
            }
            const res = await getPatientChatSM({
              regId,
              userId:uni.getStorageSync("userId"),
            })

            item.dataVal = res.data||{}
            let pars = {
              docId,
              docName,
              patientId,
              userId: uni.getStorageSync('userId'),
            };

            item = { ...item, ...pars };
            console.log('item',item)
            this.$store.commit('SET_CHATLIST', item);
            uni.setStorageSync('chatItem', item.dataVal);
            let param = {
              docId,
            };
            url = '/pages/chatList/chatDetail?param=' + JSON.stringify(param);
            if(this.info.groupId){
              url = "/pages/chatList/scanChatDetail?param=" + JSON.stringify(param);
            }
          }
          break;
        case '7':
          // 小于4 或者是线下
          if (status < 4 || isOffLine == 1) {
            url = '/pages/personalCenter/diagnosisRecord/detail?id=' + regId+`&flag=ksxf`;
          } else {
            let obj = {
              docId,
              patientId,
              docName,
            };
            this.$store.commit('setEmptyList', obj);
            let chatList = this.$store.getters.getChatList;
            let list = this.$store.getters.getChatListDoc;
            let id = patientId + ',' + docId;
            let item;
            // 如果是数组
            this.$store.commit('SET_CHATLIST', []);//清空(解决只显示该患者与当前医生的消息)
            if (Array.isArray(chatList)) {
              if (list.length) {
                for (let i = 0; i < list.length; i++) {
                  let v = list[i];
                  if (v.id == id) {
                    item = v;
                    break;
                  }
                }
              } else {
                await this.$store.dispatch('getChatListId', {
                  chatId: id,
                });
                item = this.$store.getters.getChatList;
              }
            } else {
              await this.$store.dispatch('getChatListId', {
                chatId: id,
              });
              item = this.$store.getters.getChatList;
            }
            const res = await getPatientChatSM({
              regId,
              userId:uni.getStorageSync("userId"),
            })


            item.dataVal = res.data||{};

            let pars = {
              docId,
              docName,
              patientId,
              userId: uni.getStorageSync('userId'),
            };

            item = { ...item, ...pars };
            this.$store.commit('SET_CHATLIST', item);
            uni.setStorageSync('chatItem', item.dataVal);
            let param = {
              docId,
            };
            // url = '/pages/chatList/chatDetail?param=' + JSON.stringify(param);
            if(this.info.groupId){
              url = "/pages/chatList/scanChatDetail?param=" + JSON.stringify(param);
            }
          }
          break;
        // 处方
        case '3':
          // 待支付
          if (status == 0) {
            url =
              '/pages/prescription/prescriptionDetail?businessId=' + businessId;
          } else {
            url = '/pages/prescription/preDetail?businessId=' + businessId;
          }
          break;
        // 检验
        case '5':
          if (status == 1) {
            if (!paymentType) {
              url = '/pages/inspect/lisOrder?id=' + pliId;
            } else if (paymentType == 1) {
              url = '/pages/inspect/lisDetails?id=' + pliId;
            } else {
              url = '/pages/inspect/pay/lis?id=' + pliId;
            }
          } else {
            url = '/pages/inspect/lisDetails?id=' + pliId;
          }
          break;
        // 检查
        case '6':
          if (status == 1) {
            if (!paymentType) {
              url = '/pages/inspect/pacsOrder?id=' + ppiId;
            } else if (paymentType == 1) {
              url = '/pages/inspect/pacsDetails?id=' + ppiId;
            } else {
              url = '/pages/inspect/pay/pacs?id=' + ppiId;
            }
          } else {
            url = '/pages/inspect/pacsDetails?id=' + ppiId;
          }
          break;
        default:
          url = '';
          break;
      }
      if (!url) return;
      uni.navigateTo({ url });
    },
  },
};
</script>

<style lang="scss" scoped>

.card {

  .item {
    padding: 0 32rpx;
    border-radius: 8rpx;
    font-size: 28rpx;
    position: relative;
    margin-bottom: 24rpx;
border-radius: 8px;
background: linear-gradient(135.9deg, rgba(133, 155, 255, 1) 0%, rgba(234, 246, 255, 1) 100%);
    &:last-child {
      margin-bottom: 0;
    }

    &::before {
      left: -15rpx;
    }

    &::after {
      right: -15rpx;
    }

    .type {
      height: 80rpx;
      border-bottom: 1px solid #ebebeb;
      @include flex(lr);

      .left {
        color: white;
        @include flex;
        position: relative;
        left: -50rpx;

        .icon {
          width: 92rpx;
          height: 60rpx;
          margin-right: 16rpx;
          position: relative;
          top: 6rpx;
        }

        text {
          font-weight: bold;
        }
      }

      .status {
        width: 84rpx;
        height: 40rpx;
        opacity: 1;
        border-radius: 30rpx;
        background: rgba(133, 155, 255, 1);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 20rpx;
      }
    }

    .item_cont {
      padding: 10rpx 0;
      border-bottom: 1px solid #ebebeb;

      .cont_item {
        line-height: 50rpx;
        color: #ffffff;

        .bold {
          font-weight: bold;
          color: #ffffff;
        }
      }
    }

    .item_time {
      line-height: 80rpx;
      color:white;
      text {
        color: #ff3b30;
      }
    }

    .item_footer {
      width: 100%;
      @include flex(lr);
      padding: 0 15rpx;
      position: absolute;
      bottom: -10rpx;
      left: 0;

      text {
        width: 20rpx;
        height: 20rpx;
        border-radius: 50%;
        background: #f5f5f5;
      }
    }
  }
}
</style>
