"use strict";
const plugins_xeUtils_staticEncodeURIComponent = require("./staticEncodeURIComponent.js");
const plugins_xeUtils_each = require("./each.js");
const plugins_xeUtils_isArray = require("./isArray.js");
const plugins_xeUtils_isNull = require("./isNull.js");
const plugins_xeUtils_isUndefined = require("./isUndefined.js");
const plugins_xeUtils_isPlainObject = require("./isPlainObject.js");
function stringifyParams(resultVal, resultKey, isArr) {
  var _arr;
  var result = [];
  plugins_xeUtils_each.each(resultVal, function(item, key) {
    _arr = plugins_xeUtils_isArray.isArray(item);
    if (plugins_xeUtils_isPlainObject.isPlainObject(item) || _arr) {
      result = result.concat(stringifyParams(item, resultKey + "[" + key + "]", _arr));
    } else {
      result.push(plugins_xeUtils_staticEncodeURIComponent.staticEncodeURIComponent(resultKey + "[" + (isArr ? "" : key) + "]") + "=" + plugins_xeUtils_staticEncodeURIComponent.staticEncodeURIComponent(plugins_xeUtils_isNull.isNull(item) ? "" : item));
    }
  });
  return result;
}
function serialize(query) {
  var _arr;
  var params = [];
  plugins_xeUtils_each.each(query, function(item, key) {
    if (!plugins_xeUtils_isUndefined.isUndefined(item)) {
      _arr = plugins_xeUtils_isArray.isArray(item);
      if (plugins_xeUtils_isPlainObject.isPlainObject(item) || _arr) {
        params = params.concat(stringifyParams(item, key, _arr));
      } else {
        params.push(plugins_xeUtils_staticEncodeURIComponent.staticEncodeURIComponent(key) + "=" + plugins_xeUtils_staticEncodeURIComponent.staticEncodeURIComponent(plugins_xeUtils_isNull.isNull(item) ? "" : item));
      }
    }
  });
  return params.join("&").replace(/%20/g, "+");
}
exports.serialize = serialize;
