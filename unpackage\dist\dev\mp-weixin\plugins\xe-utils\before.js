"use strict";
const plugins_xeUtils_slice = require("./slice.js");
function before(count, callback, context) {
  var runCount = 0;
  var rests = [];
  context = context || this;
  return function() {
    var args = arguments;
    runCount++;
    if (runCount < count) {
      rests.push(args[0]);
      callback.apply(context, [rests].concat(plugins_xeUtils_slice.slice(args)));
    }
  };
}
exports.before = before;
