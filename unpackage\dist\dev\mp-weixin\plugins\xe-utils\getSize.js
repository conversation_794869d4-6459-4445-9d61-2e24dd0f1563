"use strict";
const plugins_xeUtils_isArray = require("./isArray.js");
const plugins_xeUtils_isString = require("./isString.js");
const plugins_xeUtils_each = require("./each.js");
function getSize(obj) {
  var len = 0;
  if (plugins_xeUtils_isString.isString(obj) || plugins_xeUtils_isArray.isArray(obj)) {
    return obj.length;
  }
  plugins_xeUtils_each.each(obj, function() {
    len++;
  });
  return len;
}
exports.getSize = getSize;
