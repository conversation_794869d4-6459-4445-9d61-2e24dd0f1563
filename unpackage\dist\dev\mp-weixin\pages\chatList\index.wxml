<view class="page data-v-a0119522"><view class="tab_title data-v-a0119522"><view class="tab_title_box data-v-a0119522" bindtap="{{c}}"><view class="{{['tab_title_font', 'data-v-a0119522', a]}}">我的问诊</view><uni-badge wx:if="{{b}}" class="tab_title_abdge1 data-v-a0119522" u-i="a0119522-0" bind:__l="__l" u-p="{{b}}"/></view><view class="tab_title_box data-v-a0119522" bindtap="{{e}}"><view class="{{['tab_title_font', 'data-v-a0119522', d]}}">AI医生助手 </view></view><view class="tab_title_box data-v-a0119522" bindtap="{{i}}"><view class="{{['tab_title_font', 'data-v-a0119522', f]}}">其他消息</view><uni-badge wx:if="{{g}}" class="tab_title_abdge1 data-v-a0119522" u-i="a0119522-1" bind:__l="__l" u-p="{{h}}"/></view></view><view wx:if="{{j}}" class="home-page data-v-a0119522" style="height:calc(100% - 65px)"><view class="bg_top data-v-a0119522" style="height:100%"><view class="search data-v-a0119522"><uni-search-bar wx:if="{{n}}" class="data-v-a0119522" bindcancel="{{k}}" bindconfirm="{{l}}" u-i="a0119522-2" bind:__l="__l" bindupdateModelValue="{{m}}" u-p="{{n}}"></uni-search-bar><view class="data-v-a0119522" style="color:rgba(135, 79, 240, 1)" bindtap="{{o}}"><text class="data-v-a0119522" style="margin-right:12px">|</text> 搜索 </view></view><view wx:if="{{p}}" class="empty data-v-a0119522"><image src="{{q}}" class="emptyImg data-v-a0119522"></image><view class="data-v-a0119522">暂无沟通信息</view></view><scroll-view scroll-y="true" class="scroll-Y data-v-a0119522" bindscrolltolower="{{s}}"><view wx:for="{{r}}" wx:for-item="item" wx:key="s" class="data-v-a0119522"><view wx:if="{{item.a}}" class="chatList data-v-a0119522" bindtap="{{item.r}}"><view class="header_img data-v-a0119522"><image src="{{item.b}}" class="userImg data-v-a0119522"/></view><view class="info data-v-a0119522"><view class="info_title data-v-a0119522"><view class="patientInfo data-v-a0119522"><text class="docName data-v-a0119522">{{item.c}}</text><text class="deptName data-v-a0119522">{{item.d}}</text></view><view class="data-v-a0119522"><text wx:if="{{item.e}}" class="status data-v-a0119522" style="color:rgba(234, 115, 47, 1);background:rgba(253, 241, 233, 1)">待签到</text><text wx:if="{{item.f}}" class="status data-v-a0119522" style="color:rgba(165, 129, 77, 1);background:rgba(255, 239, 215, 1)">待接诊</text><text wx:if="{{item.g}}" class="status data-v-a0119522" style="color:rgba(44, 199, 147, 1);background:rgba(232, 250, 244, 1)">接诊中</text><text wx:if="{{item.h}}" class="status data-v-a0119522" style="color:rgba(211, 65, 63, 1);background:rgba(253, 236, 237, 1)">就诊结束</text><text wx:if="{{item.i}}" class="status data-v-a0119522" style="color:rgba(59, 96, 239, 1);background:rgba(235, 240, 254, 1)">已退诊</text><text wx:if="{{item.j}}" class="status data-v-a0119522" style="color:rgba(134, 138, 141, 1);background:rgba(244, 244, 244, 1)">已失效</text></view></view><view class="data-v-a0119522" style="display:flex;justify-content:space-between;border-bottom:1px solid #eaeaea;padding-bottom:5px"><view class="description data-v-a0119522"> 就诊人姓名：{{item.k}}</view><view class="data-v-a0119522"><text wx:if="{{item.l}}" class="visitType data-v-a0119522">图文</text><text wx:if="{{item.m}}" class="visitType data-v-a0119522">语音</text><text wx:if="{{item.n}}" class="visitType data-v-a0119522">视频</text><text wx:if="{{item.o}}" class="visitType data-v-a0119522">患者管理</text></view></view><view class="contentInfo data-v-a0119522"><view class="fitrstChild data-v-a0119522"><view class="lastContent data-v-a0119522"><rich-text class="data-v-a0119522" nodes="{{item.p}}"/></view></view><text class="hours data-v-a0119522">{{item.q}}</text></view></view></view></view></scroll-view></view></view><view wx:if="{{t}}" class="data-v-a0119522"><view class="ai-assistant-page data-v-a0119522"><scroll-view scroll-x="true" class="patient-filter-scroll data-v-a0119522"><view class="patient-filter data-v-a0119522"><view class="{{['patient-item', 'data-v-a0119522', v && 'active']}}" bindtap="{{w}}">全部</view><view wx:for="{{x}}" wx:for-item="patient" wx:key="b" class="{{['patient-item', 'data-v-a0119522', patient.c && 'active']}}" bindtap="{{patient.d}}">{{patient.a}}</view></view></scroll-view><view class="ai-message-list data-v-a0119522"><view wx:if="{{y}}" class="empty data-v-a0119522"><image src="{{z}}" class="emptyImg data-v-a0119522"></image><view class="data-v-a0119522">暂无消息</view></view><scroll-view scroll-y="true" class="ai-scroll-view data-v-a0119522" bindscrolltolower="{{C}}"><view wx:for="{{A}}" wx:for-item="item" wx:key="e" class="ai-message-item data-v-a0119522" bindtap="{{item.f}}"><view class="ai-message-content data-v-a0119522"><image src="{{B}}" class="tips data-v-a0119522"/><view class="ai-message-info data-v-a0119522"><view class="ai-message-title data-v-a0119522">{{item.a}}</view><view class="ai-message-details data-v-a0119522"><text class="ai-message-patient data-v-a0119522">就诊人：{{item.b}}</text><text class="ai-message-time data-v-a0119522">{{item.c}}</text></view></view></view><view wx:if="{{item.d}}" class="ai-message-status data-v-a0119522"><view class="unread-dot data-v-a0119522"></view></view></view></scroll-view></view></view></view><view wx:if="{{D}}" class="data-v-a0119522"><sys-tem-list class="data-v-a0119522" u-i="a0119522-3" bind:__l="__l"/></view><view class="k_page data-v-a0119522" hidden="{{!E}}" bindtap="{{F}}"></view></view>