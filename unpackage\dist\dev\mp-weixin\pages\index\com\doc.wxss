/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*
	字体颜色相关
*/
/*
	背景色 分割线色
*/
/*
	主题色
*/
/*
		渐变可调角度
*/
/*
	定义弹性布局
 */
/*
	单行文本溢出隐藏
*/
.docImg_box_unOnline.data-v-5fac3b5e {
  border: 1px solid #CCCCCC;
}
.docImg_box_Online.data-v-5fac3b5e {
  border: 1px solid #0AC2B2;
}
.icons.data-v-5fac3b5e {
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  background: #00baad;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10rpx;
}
.icons .com_icon.data-v-5fac3b5e {
  width: 12rpx;
  height: 16rpx;
}
.docName_desp_image.data-v-5fac3b5e {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
}
.rec_item.data-v-5fac3b5e {
  width: 335rpx;
  padding: 20rpx;
  border-radius: 16rpx;
  background: #f7f8fc;
  box-sizing: border-box;
  border-bottom: 0px !important;
}
.rec_item .rec-head.data-v-5fac3b5e {
  display: flex;
  align-items: center;
}
.rec_item .docName.data-v-5fac3b5e {
  font-size: 28rpx;
  font-weight: bold;
  color: black;
}
.rec_item .docName_desps.data-v-5fac3b5e {
  margin-top: 20rpx;
}
.rec_item .docName_desp.data-v-5fac3b5e {
  color: #999999;
  font-size: 24rpx;
  line-height: 36rpx;
  display: flex;
  align-items: center;
}
.rec_item .docName_dept.data-v-5fac3b5e {
  font-size: 28rpx;
  color: #666666;
}
.rec_item .doc_head.data-v-5fac3b5e {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  flex: none;
  object-fit: cover;
}
.rec_item .doc_desc.data-v-5fac3b5e {
  flex: 1;
  overflow: hidden;
}
.rec_item .doc_desc .desc_top.data-v-5fac3b5e {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.rec_item .doc_desc .desc_top .left.data-v-5fac3b5e {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
.rec_item .doc_desc .desc_top .left text.data-v-5fac3b5e:last-child {
  margin-left: 24rpx;
}
.rec_item .doc_desc .desc_top .right.data-v-5fac3b5e {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
}
.rec_item .doc_desc .desc_top .right text.data-v-5fac3b5e {
  width: 88rpx;
  height: 36rpx;
  color: #fff;
  font-size: 26rpx;
  border-radius: 8rpx;
  text-align: center;
}
.rec_item .doc_desc .desc_top .right text.audio.data-v-5fac3b5e {
  background-color: #836aff;
}
[data-theme=nx] .rec_item .doc_desc .desc_top .right text.audio.data-v-5fac3b5e {
  background-color: #107dff;
}
[data-theme=test] .rec_item .doc_desc .desc_top .right text.audio.data-v-5fac3b5e {
  background-color: #2db99d;
}
.rec_item .doc_desc .desc_top .right text.tw.data-v-5fac3b5e {
  background-color: #ffb541;
}
.rec_item .doc_desc .desc_two.data-v-5fac3b5e {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #333;
}
.rec_item .doc_desc .desc_two .host.data-v-5fac3b5e {
  margin-left: 24rpx;
}
.rec_item .doc_desc .desc_num.data-v-5fac3b5e {
  margin-top: 8rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 24rpx;
  color: #a6aab2;
}
.rec_item .doc_desc .desc_tag.data-v-5fac3b5e {
  margin-top: 16rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 16rpx;
  flex-wrap: wrap;
}
.rec_item .doc_desc .desc_tag text.data-v-5fac3b5e {
  padding: 0 12rpx;
  height: 36rpx;
  border-radius: 18rpx;
  background-color: #e8f6fd;
  color: #836aff;
  font-size: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
[data-theme=nx] .rec_item .doc_desc .desc_tag text.data-v-5fac3b5e {
  color: #107dff;
}
[data-theme=test] .rec_item .doc_desc .desc_tag text.data-v-5fac3b5e {
  color: #2db99d;
}