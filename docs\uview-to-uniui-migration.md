# u-view 到 uni-ui 组件迁移文档

## 🎯 迁移目标
将 `pages/register/thatDayRegister/diseaseDetail/index.vue` 文件中的 u-view 组件替换为 uni-ui 组件，提高跨平台兼容性。

## 📝 主要修改内容

### 1. u-popup → uni-popup

**原来的 u-view 组件：**
```vue
<u-popup v-model="isFirstVisitHos" mode="bottom">
  <view>
    <!-- 内容 -->
  </view>
</u-popup>
```

**新的 uni-ui 组件：**
```vue
<uni-popup ref="hospitalPopup" :show="isFirstVisitHos" type="bottom" @change="onPopupChange">
  <view class="popup-content">
    <!-- 内容 -->
  </view>
</uni-popup>
```

**主要差异：**
- `v-model` → `:show` + `@change` 事件
- `mode` → `type`
- 需要通过 `ref` 手动控制显示/隐藏
- 需要添加 `popup-content` 样式类

### 2. u-search → uni-search-bar

**原来的 u-view 组件：**
```vue
<u-search 
  placeholder="请输入" 
  v-model="keyword" 
  :show-action="false" 
  shape="round" 
  @search="getSearch"
></u-search>
```

**新的 uni-ui 组件：**
```vue
<uni-search-bar 
  :radius="100" 
  placeholder="请输入" 
  v-model="keyword" 
  @input="onSearchInput"
  @confirm="getSearch"
  @clear="onSearchClear"
></uni-search-bar>
```

**主要差异：**
- `shape="round"` → `:radius="100"`
- `@search` → `@confirm`
- 新增 `@input` 和 `@clear` 事件处理

## 🔧 新增的方法

### uni-popup 相关方法
```javascript
// 弹窗状态变化处理
onPopupChange(e) {
  this.isFirstVisitHos = e.show;
},

// 关闭弹窗
closePopup() {
  this.isFirstVisitHos = false;
  this.$refs.hospitalPopup.close();
},
```

### uni-search-bar 相关方法
```javascript
// 搜索输入处理
onSearchInput(value) {
  this.keyword = value;
},

// 清空搜索处理
onSearchClear() {
  this.keyword = '';
  this.getHosList(); // 清空搜索时重新加载所有医院
},
```

### 修改的方法
```javascript
// 确认选择医院
sureEvent() {
  if (this.selectedHospitalIndex === -1) {
    this.info.firstVisitHos = this.hosList[0]?.name;
  } else {
    this.info.firstVisitHos = this.hosList[this.selectedHospitalIndex]?.name;
  }
  this.closePopup(); // 使用新的关闭方法
},
```

## 🎨 新增的样式

```scss
.popup-content {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  overflow: hidden;
}

.popup-top {
  display: flex;
  align-items: center;
  line-height: 50px;
  justify-content: space-between;
  padding: 0 20px;
  border-bottom: 1px solid #eee;
  
  .success {
    color: #15a0e6;
  }
  
  .cancel {
    color: #666;
  }
}
```

## ✨ 功能特性

### uni-popup 特性
- ✅ 支持多种弹出方向（top、bottom、center、left、right）
- ✅ 支持遮罩层点击关闭
- ✅ 支持动画效果
- ✅ 更好的跨平台兼容性
- ✅ 支持安全区域适配

### uni-search-bar 特性
- ✅ 支持圆角设置
- ✅ 支持清空按钮
- ✅ 支持实时输入监听
- ✅ 支持确认搜索
- ✅ 更好的小程序兼容性

## 🚀 使用方法

### 弹窗控制
```javascript
// 显示弹窗
this.isFirstVisitHos = true;

// 隐藏弹窗
this.closePopup();

// 或者直接操作 ref
this.$refs.hospitalPopup.open();
this.$refs.hospitalPopup.close();
```

### 搜索功能
```javascript
// 监听输入
onSearchInput(value) {
  // 实时处理输入内容
  this.keyword = value;
}

// 确认搜索
getSearch(value) {
  // 执行搜索逻辑
  this.performSearch(value);
}

// 清空搜索
onSearchClear() {
  // 重置搜索状态
  this.keyword = '';
  this.resetSearchResults();
}
```

## ⚠️ 注意事项

1. **引入组件**：确保项目中已正确引入 uni-ui 组件库
2. **样式适配**：uni-ui 组件的默认样式可能与 u-view 不同，需要适当调整
3. **事件处理**：uni-ui 的事件名称和参数可能与 u-view 不同
4. **平台兼容**：uni-ui 在不同平台的表现更一致，但仍需测试
5. **性能优化**：uni-ui 组件通常有更好的性能表现

## 🧪 测试建议

1. **功能测试**：
   - 弹窗的显示/隐藏
   - 搜索功能的输入/清空/确认
   - 医院选择功能

2. **平台测试**：
   - H5 浏览器
   - 微信小程序
   - 支付宝小程序
   - App（Android/iOS）

3. **交互测试**：
   - 点击遮罩关闭弹窗
   - 搜索框的焦点状态
   - 滚动加载更多

## 📋 后续优化

1. 可以考虑将弹窗逻辑封装成独立组件
2. 添加搜索防抖功能提升性能
3. 优化样式以适配不同屏幕尺寸
4. 添加无障碍访问支持
