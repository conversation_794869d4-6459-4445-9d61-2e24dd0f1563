"use strict";
const plugins_xeUtils_each = require("./each.js");
function filter(obj, iterate, context) {
  var result = [];
  if (obj && iterate) {
    if (obj.filter) {
      return obj.filter(iterate, context);
    }
    plugins_xeUtils_each.each(obj, function(val, key) {
      if (iterate.call(context, val, key, obj)) {
        result.push(val);
      }
    });
  }
  return result;
}
exports.filter = filter;
