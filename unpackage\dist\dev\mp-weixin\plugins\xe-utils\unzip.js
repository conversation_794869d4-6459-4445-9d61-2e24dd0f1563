"use strict";
const plugins_xeUtils_pluck = require("./pluck.js");
const plugins_xeUtils_max = require("./max.js");
function unzip(arrays) {
  var index, maxItem, len;
  var result = [];
  if (arrays && arrays.length) {
    index = 0;
    maxItem = plugins_xeUtils_max.max(arrays, function(item) {
      return item ? item.length : 0;
    });
    for (len = maxItem ? maxItem.length : 0; index < len; index++) {
      result.push(plugins_xeUtils_pluck.pluck(arrays, index));
    }
  }
  return result;
}
exports.unzip = unzip;
