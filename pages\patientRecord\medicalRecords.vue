<template>
  <view class="meadicalRecords">
    <view class="medRecordCard">
      <!-- 医生信息 -->
      <Docter :infoDetail="docInfo" />

      <view class="patientCard">
        <view
          class="cardInfo"
          v-for="(item, index) in muList"
          :key="index"
          @click="openPage(item, index)"
        >
          <image :src="item.path" class="imgIcon"></image>
          <view>
            {{ item.name }}
          </view>
        </view>
      </view>
    </view>
    <steps :talk="list" :pageParam="pageParam"></steps>
  </view>
</template>

<script>
import steps from '@/components/steps/index.vue';
import myJsTools from '@/common/js/myJsTools.js';
import Docter from '@/components/doctor_header/doctor_header.vue';
import { getDocInfoById } from '@/api/chatCardDetail';
import { getRegList } from '@/api/patient.js';

export default {
  components: {
    steps,
    Docter,
  },
  data() {
    return {
      pageParam: {},
      muList: [
        {
          path: '../../static/images/meterial/meterial_1.png',
          name: '聊天记录',
          type: 'chatHistory',
        },
        {
          path: '../../static/images/meterial/meterial_2.png',
          name: '基本情况',
          type: 'basic',
        },
        {
          path: '../../static/images/meterial/meterial_3.png',
          name: '问诊记录',
          type: 'wzRecord',
        },
      ],
      listQuery: {
        page: 1,
        limit: 100,
        patientId: '',
        docId: '',
      },
      //诊疗记录列表
      list: [],
      // 医生信息
      docInfo: {},
    };
  },

  onLoad(options) {
    this.pageParam = JSON.parse(options.param);
    this.listQuery.docId = this.pageParam.docId;
    this.listQuery.patientId = this.pageParam.patientId;
  },
  created() {
    this.getList();
    this.getDocInfo();
  },
  methods: {
    openPage(item) {
      let { docId, patientId, docImg, patientImg } = this.pageParam;
      let obj = {
        docId,
        patientId,
        patientImg: patientImg || '',
        docImg: docImg || '',
      };
      if (item.type == 'chatHistory') {
        uni.navigateTo({
          url: '/pages/chatList/hisChatList?param=' + JSON.stringify(obj),
        });
      } else if (item.type == 'basic') {
        uni.navigateTo({
          url:
            '/pages/patientRecord/basicInfo?param=' +
            JSON.stringify(this.pageParam),
        });
      } else if (item.type == 'wzRecord') {
        uni.navigateTo({
          url:
            '/pages/patientRecord/interviewRecord?param=' +
            JSON.stringify(this.pageParam),
        });
      }
    },
    goBack() {
      uni.navigateBack({
        delta: 1,
      });
    },
    getDocInfo() {
      getDocInfoById({
        docId: this.pageParam.docId,
      }).then((res) => {
        if (res.code != 20000) return;
        let data = res.data;
        if (data.docImg) {
          myJsTools.downAndSaveImg(data.docImg, (url) => {
            data.docImgUrl = data.docImg;
            data.docImg = url;
          });
        }
        if (data.lableName) {
          data.docLable = data.lableName.split(',');
        }
        this.docInfo = data;
      });
    },
    getList() {
      getRegList(this.listQuery).then((res) => {
        if (res.data.rows) {
          let arr = res.data.rows;
          arr.forEach((v) => {
            // 显示的日期
            v.date = v.inquiryTime.substring(5);
            // 年份
            v.year = v.inquiryTime.slice(0, 4);
          });
          this.list = arr;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .uni-navbar {
  position: sticky;
  top: 0;
}
.docName {
  color: #333333;
  font-size: 34rpx;
  line-height: 34rpx;
  height: 34rpx;
  text-align: center;
}

.doctor_box_top {
  padding: 24upx 28upx;
  margin-top: 24rpx;
  border-radius: 16upx;
}

.info {
  font-size: 24rpx;
  color: #333333;
  line-height: 28rpx;
  height: 28rpx;
  margin-top: 10rpx;
}

.backIcon {
  width: 44rpx;
  height: 44rpx;
  margin-top: 32rpx;
  margin-left: 18rpx;
}

.medRecordCard {
  margin: 0 32rpx;
}

.patientCard {
  margin-top: 22rpx;
  background-color: #ffffff;
  color: #666666;
  font-size: 22rpx;
  display: flex;
  padding: 32rpx 0;

  view {
    text-align: center;
    flex: 1;
  }

  image {
    width: 64rpx;
    height: 64rpx;
    margin-bottom: 6rpx;
  }
}
</style>
