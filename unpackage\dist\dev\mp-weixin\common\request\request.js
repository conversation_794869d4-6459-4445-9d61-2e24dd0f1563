"use strict";
const common_vendor = require("../vendor.js");
const common_js_pay = require("../js/pay.js");
const env = require("../../env.js");
let baseUrl = env.env.baseUrl;
const http = ({ url = "", param = {}, ...other } = {}) => {
  console.log(param, url, "===========接口参数");
  return new Promise((resolve, reject) => {
    if (other.method == "post" && !url.includes("api/") && other.isNeedToStringParam !== false) {
      param = reSetData(param);
    }
    common_vendor.index.request({
      url: getUrl(url),
      data: param,
      timeout: 3e5,
      header: {
        Authorization: common_vendor.index.getStorageSync("proPfInfo").token || "",
        hosId: common_vendor.index.getStorageSync("hosId") || "",
        firmId: env.env.firmId,
        version: env.env.version,
        clientType: env.env.clientType
      },
      ...other,
      success: (res) => {
        common_vendor.index.hideLoading();
        console.log(res, "接口返回结果");
        if (url.includes("api/")) {
          resolve(res);
          return;
        }
        let arr = [2e4, 20011, 20014, 20015, 20019, 20020];
        if (res.data && (res.data.message || "").includes("存在该医生下的快速续方")) {
          common_vendor.index.showToast({
            icon: "none",
            title: res.data.message,
            duration: 2e3
          });
          setTimeout(() => {
            common_vendor.index.navigateBack({
              delta: 1
            });
          }, 1e3);
          reject(res);
          return;
        }
        if (res.statusCode >= 200 && res.statusCode < 300) {
          console.log("返回");
          if (arr.includes(res.data.code)) {
            resolve(res.data);
          } else {
            common_js_pay.Toast(res.data.message);
            reject(res);
          }
        } else {
          reject(res);
        }
      },
      fail: (err) => {
        common_vendor.index.hideLoading();
        common_js_pay.Toast(err.errMsg);
        reject(err);
      }
    });
  });
};
const getUrl = (url) => {
  if (url.includes("api/")) {
    return url;
  }
  if (url.indexOf("://") == -1) {
    url = baseUrl + url;
  }
  return url;
};
const reSetData = (requestData) => {
  let timestamp = (/* @__PURE__ */ new Date()).getTime() + "";
  let nonce = guid();
  let token_info = common_vendor.index.getStorageSync("proPfInfo").token;
  if (requestData) {
    common_vendor.gBase64.encode(JSON.stringify(requestData));
  } else {
    let map = {};
    common_vendor.gBase64.encode(JSON.stringify(map));
  }
  let data = {
    data: JSON.stringify(requestData)
  };
  data.timestamp = timestamp;
  data.nonce = nonce;
  if (token_info) {
    data.token = token_info;
  }
  let s = "";
  Object.keys(data).sort().forEach((k) => {
    if (data[k] && data[k].length > 0) {
      if (s.length > 0) {
        s += "&";
      }
      s += k + "=" + data[k];
    }
  });
  let md5Data = common_vendor.md5(s).toLocaleUpperCase();
  data.sign = md5Data;
  return data;
};
const guid = () => {
  return S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4();
};
const S4 = () => {
  return ((1 + Math.random()) * 65536 | 0).toString(16).substring(1);
};
exports.http = http;
