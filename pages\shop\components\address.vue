<template>
  <!-- 自提样式 -->
  <view class="address">
    <view class="title">
      <text v-if="detail.isDefault">默认</text>
      <text v-if="detail.lableName">{{ detail.lableName }}</text>
      <block v-if="detail.addressArea">
        {{ detail.addressArea }} {{ detail.addressDetail }}
      </block>
      <block v-else>{{ detail.deliveryAddressDetail }}</block>
    </view>
    <view class="user" @click="click">
      <text>{{ detail.deliveryName }}</text>
      <text class="phone">{{ phone(detail.telNo) }}</text>
      <uni-icons type="arrowright" color="#333" size="20"></uni-icons>
    </view>
  </view>
</template>

<script>
import myJsTools from '@/common/js/myJsTools.js';
export default {
  name: 'Addreess',
  props: {
    detail: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  methods: {
    click() {
      this.$emit('click');
    },
    phone(tel) {
      return myJsTools.phone(tel);
    },
  },
};
</script>

<style lang="scss" scoped>
.address {
  width: 100%;
  padding: 0 32rpx;

  .title {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;

    text {
      display: inline-block;
      height: 32rpx;
      padding: 0 6rpx;
      background-color: #666;
      color: #fff;
      font-size: 22rpx;
      border-radius: 4rpx;
      margin-right: 10rpx;
    }
  }

  .user {
    @include flex(lr);
    margin-top: 18rpx;

    text {
      font-size: 26rpx;
      color: #666;
    }

    .phone {
      flex: 1;
      padding-left: 20rpx;
    }
  }
}
</style>
