"use strict";
const plugins_xeUtils_getWhatMonth = require("./getWhatMonth.js");
const plugins_xeUtils_toStringDate = require("./toStringDate.js");
const plugins_xeUtils_isValidDate = require("./isValidDate.js");
function getQuarterNumber(date) {
  var month = date.getMonth();
  if (month < 3) {
    return 1;
  } else if (month < 6) {
    return 2;
  } else if (month < 9) {
    return 3;
  }
  return 4;
}
function getWhatQuarter(date, offset, day) {
  var currMonth, monthOffset = offset && !isNaN(offset) ? offset * 3 : 0;
  date = plugins_xeUtils_toStringDate.toStringDate(date);
  if (plugins_xeUtils_isValidDate.isValidDate(date)) {
    currMonth = (getQuarterNumber(date) - 1) * 3;
    date.setMonth(currMonth);
    return plugins_xeUtils_getWhatMonth.getWhatMonth(date, monthOffset, day);
  }
  return date;
}
exports.getWhatQuarter = getWhatQuarter;
