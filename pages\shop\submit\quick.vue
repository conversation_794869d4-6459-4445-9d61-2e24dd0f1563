<template>
  <!-- 提交订单 -->
  <view class="order_submit">
    <!-- 选择患者 -->
    <view class="sele_warp">
      <view class="sele_but" @click="toPatient" v-show="!patientInfo">
        <text>请选择就诊人</text>
        <uni-icons type="arrowright" color="#333" size="20"></uni-icons>
      </view>

      <!-- 患者信息 -->
      <view class="user_info" v-show="patientInfo" @click="toPatient">
        <!-- 头像 -->
        <UniImage :src="patientInfo.patientImg"
          v-if="patientInfo.patientImg"
          data-src="/static/images/docHead.png"
          alt=""
          class="user_head"
        />
        <image
          src="/static/images/docHead.png"
          v-else
          alt=""
          class="user_head"
        />
        <!-- 信息 -->
        <view class="user_desc">
          <view class="user_name">{{ patientInfo.patientName }}</view>
          <view class="user_other">
            <image
              v-show="patientInfo.sexCode == 1"
              src="/static/shop/nan.png"
            ></image>
            <image
              v-show="patientInfo.sexCode == 2"
              src="/static/shop/nv.png"
            ></image>
            <text>{{ patientInfo.age }}岁</text>
          </view>
        </view>
        <!-- 箭头 -->
        <uni-icons type="arrowright" color="#333" size="20"></uni-icons>
      </view>

      <view class="sele_info">
        <text>提示</text>
        <view class="info_text">请选择需使用药品的患者</view>
      </view>
    </view>

    <!-- 上传照片 -->
    <view class="sele_warp">
      <view class="sele_but">
        <text>疾病类型</text>
      </view>

      <view class="textarea">
        <textarea
          maxlength="100"
          placeholder="请填写疾病类型"
          @input="(e) => (drugLableListStr = e.detail.value)"
          :value="drugLableListStr"
        ></textarea>

        <text class="n">-{{ strLength }}</text>
      </view>

      <!-- @click="cloosImgTop" -->
      <view class="sele_but act" :class="{ act: fileList.length }">
        <text>请上传处方或病历照片（最多6张）</text>
        <!-- <uni-icons type="arrowright" color="#333" size="20"></uni-icons> -->
      </view>

      <!-- 图片 -->
      <view class="img_list" v-show="fileList.length || true">
        <view class="img_item" v-for="(item, index) in fileList" :key="index">
          <image
            :src="item.url"
            @click="preview(item)"
            mode="aspectFit"
            class="img"
          ></image>
          <!-- 删除按钮 -->
          <view class="del" @click="delFile(index)">
            <uni-icons type="close" color="#fff" size="24"></uni-icons>
          </view>
        </view>
        <!-- 上传 -->
        <view class="img_item" @click="cloosImg" v-if="fileList.length < 6">
          <image src="/static/images/question/image-upload.png" />
          <text>上传图片</text>
        </view>
      </view>

      <view class="sele_info">
        <text>提示</text>
        <view class="info_text"
          >请上传处方(必要)或病历相关图片，并保证选择的用药人和处方用药人一致。</view
        >
      </view>
    </view>

    <!-- 药品药品 -->
    <DRUGLIST
      @logist="logist"
      :showTelNo="delivery == 0 ? true : false"
      :telNo="drugDetail.telNo"
      :list="orderList"
      @showTip="showTip"
    />

    <!-- 选择配送 单药店 可选择自提或物流 -->
    <view class="sele_warp" v-if="isOnly">
      <view class="sele_but" @click="seleType">
        <text @click.stop="setTip"
          >请选择配送方式
          <uni-icons
            type="help"
            color="#333"
            v-if="delivery > -1"
            size="14"
          ></uni-icons>
        </text>
        <view class="right">
          <text v-if="delivery == 0">统一配送</text>
          <text v-if="delivery == 1">自提</text>
          <text v-if="delivery == 2">同城配送</text>
          <uni-icons type="arrowright" color="#333" size="20"></uni-icons>
        </view>
      </view>

      <!-- 物流选择 -->
      <view class="logistics" v-if="delivery == 0">
        <view class="item" @click="setLogistType(0)">
          <text class="name">普通快递</text>
          <view class="right">
            <text>￥{{ orderList[0].ordinaryLogMoney | toFixed }}</text>
            <image
              src="/static/shop/sele_act.png"
              v-if="logisticsType == 0"
            ></image>
            <image src="/static/shop/sele.png" v-else></image>
          </view>
        </view>

<!--        <view class="item" @click="setLogistType(1)">-->
<!--          <text class="name">顺丰快递</text>-->
<!--          <view class="right">-->
<!--            <text>￥{{ orderList[0].specialLogMoney | toFixed }}</text>-->
<!--            <image-->
<!--              src="/static/shop/sele_act.png"-->
<!--              v-if="logisticsType == 1"-->
<!--            ></image>-->
<!--            <image src="/static/shop/sele.png" v-else></image>-->
<!--          </view>-->
<!--        </view>-->
      </view>

      <!-- 配送 -->
      <ADDRESS
        v-if="delivery == 0 && address"
        :detail="address"
        @click="toAddress"
      />

      <!-- 自提 -->
      <PHARMACY v-if="delivery == 1 && drugDetail" :detail="drugDetail" />

      <view class="sele_info">
        <text>提示</text>
        <view class="info_text"
          >不同的配送方式，价格会有差异，请以实际支付为标准</view
        >
      </view>
    </view>

    <!-- 多药店 只能物流 -->
    <view class="sele_warp" v-if="isZY">
      <view class="sele_but">
        <text @click.stop="showTip('wl')"
          >请选择配送方式
          <uni-icons type="help" color="#333" size="14"></uni-icons>
        </text>
        <view class="right"></view>
      </view>

      <!-- 物流选择 -->
      <view class="logistics">
        <view class="item" @click="setLogistType(0)">
          <text class="name">普通快递</text>
          <view class="right">
            <text>￥{{ orderList[0].ordinaryLogMoney | toFixed }}</text>
            <image
              src="/static/shop/sele_act.png"
              v-if="logisticsType == 0"
            ></image>
            <image src="/static/shop/sele.png" v-else></image>
          </view>
        </view>

<!--        <view class="item" @click="setLogistType(1)">-->
<!--          <text class="name">顺丰快递</text>-->
<!--          <view class="right">-->
<!--            <text>￥{{ orderList[0].specialLogMoney | toFixed }}</text>-->
<!--            <image-->
<!--              src="/static/shop/sele_act.png"-->
<!--              v-if="logisticsType == 1"-->
<!--            ></image>-->
<!--            <image src="/static/shop/sele.png" v-else></image>-->
<!--          </view>-->
<!--        </view>-->
      </view>
    </view>

    <!-- 选择地址 -->
    <view class="sele_warp" v-if="!isOnly">
      <view class="sele_but" @click="toAddress" v-if="!address">
        <text>请选择地址</text>
        <view class="right">
          <uni-icons type="arrowright" color="#333" size="20"></uni-icons>
        </view>
      </view>

      <!-- 配送 -->
      <ADDRESS v-if="address" :detail="address" @click="toAddress" />

      <view class="sele_info">
        <text>提示</text>
        <view class="info_text"
          >不同的配送方式，价格会有差异，请以实际支付为标准</view
        >
      </view>
    </view>

    <!-- 自提时间 -->
    <view class="invoice date" v-show="delivery > -1">
      <text v-if="delivery == 1">自提时间</text>
      <text v-else>期望配送时间</text>
      <view class="right">
        <picker
          class="right"
          mode="date"
          :value="getGoodsTime"
          :start="time.start"
          :end="time.end"
          @change="dateChange"
        >
          <view class="flex">
            <text v-if="!getGoodsTime"></text>
            <text v-else>{{ getGoodsTime }}</text>
            <uni-icons type="arrowright" color="#333" size="20"></uni-icons>
          </view>
        </picker>
      </view>
    </view>

    <!-- 调剂费 -->
    <view class="sele_warp">
      <view class="sele_but" @click="showTip('tjf')">
        <text>处方调剂费</text>
        <view class="right">
          <text class="name">￥{{ adjustRealyMoney | toFixed }}</text>
          <uni-icons type="arrowright" color="#333" size="20"></uni-icons>
        </view>
      </view>
      <view class="sele_info">
        <text class="red">平台优惠：{{ adjustDeMoney | toFixed }}</text>
      </view>
    </view>

    <!-- 发票信息 -->
    <view class="invoice" @click="toInvoice">
      <text>发票信息</text>
      <view class="right">
        <text v-if="!invoice.invoiceType">请选择发票</text>
        <text v-else-if="invoice.invoiceType == 3">未选择</text>
        <text v-else>
          <block v-if="invoice.invoiceType == 1">
            个人（{{ invoice.invoiceTitle }}）
          </block>
          <block v-else>{{ invoice.invoiceTitle }}</block>
        </text>
        <uni-icons type="arrowright" color="#333" size="20"></uni-icons>
      </view>
    </view>

    <!-- 自提支付方式 -->
    <view class="sele_pay" v-if="delivery == 1 && payList.length">
      <view class="item" @click="setPayType(1)">
        <text>微信支付</text>
        <image v-show="payType == 6" src="/static/images/question/radio.png" />
        <image
          v-show="payType == 1"
          src="/static/images/question/radio_active.png"
        />
      </view>

      <view class="item" @click="setPayType(6)">
        <text>到店支付</text>
        <image v-show="payType == 1" src="/static/images/question/radio.png" />
        <image
          v-show="payType == 6"
          src="/static/images/question/radio_active.png"
        />
      </view>
    </view>

    <!-- 快递支付方式 -->
    <view class="sele_warp" v-if="delivery == 0 && payList.length">
      <!-- 选择方式 -->
      <view class="logistics pay">
        <view class="item" @click="setPayType(1)">
          <text class="name">微信支付</text>
          <view class="right">
            <image src="/static/shop/sele_act.png" v-if="payType == 1"></image>
            <image src="/static/shop/sele.png" v-else></image>
          </view>
        </view>

        <view class="item" @click="setPayType(7)">
          <text class="name">货到付款：POS刷卡、现金</text>
          <view class="right">
            <image src="/static/shop/sele_act.png" v-if="payType == 7"></image>
            <image src="/static/shop/sele.png" v-else></image>
          </view>
        </view>
      </view>
    </view>

    <!-- 不支持在线支付 -->
    <view class="invoice" v-if="delivery > -1 && !payList.length">
      <text>支付方式</text>
      <view class="right">
        <text v-if="delivery == 0">货到付款：POS刷卡、现金</text>
        <text v-else>到店支付</text>
      </view>
    </view>

    <!-- 底部 -->
    <view class="footer">
      <view class="count">
        合计：<text>￥{{ total | toFixed }}</text>
      </view>
      <!-- 按钮 -->
      <view class="but" @click="send">去支付</view>
    </view>

    <!-- 弹框 -->
    <uni-popup ref="popup" type="bottom">
      <view class="pop">
        <view class="pop_title">
          <text>{{ tip.tipsTitle }}</text>
          <uni-icons type="closeempty" @click="hideTip"></uni-icons>
        </view>
        <view class="pop_text">
          {{ tip.tipsContent }}
        </view>
      </view>
    </uni-popup>

    <!-- 支付提示 -->
    <PROPCENTER
      v-if="showPayTip"
      :type="2"
      @cancelPropCenter="showPayTip = false"
      @confirmPropCenter="setParam"
    >
      <view class="pay_tip">
        <view class="title">{{ payTip.tipsTitle }}</view>
        <view class="cont" v-html="payTip.tipsContent"></view>
      </view>
    </PROPCENTER>

    <!-- 跨省药品列表 -->
    <PROPCENTER
      v-if="showDrugList"
      :type="2"
      @cancelPropCenter="showDrugList = false"
      @confirmPropCenter="intercept"
    >
      <view class="across_provinces">
        <view class="title">药品提醒</view>
        <!-- 药品列表 -->
        <LIST :list="drugList" />

        <!-- 文案 -->
        <view class="info">
          您购买的药品因为特殊原因无法进行跨省配送，请您重新修改药品进行提交需求
        </view>
      </view>
    </PROPCENTER>

    <!-- 货到付款 -->
    <uni-popup ref="showDelivery" type="center">
      <view class="delivery_pay">
        <view class="title">提示</view>
        <!-- 文案 -->
        <view class="text">该药店暂不支持线上付款，是否货到付款</view>

        <!-- 按钮 -->
        <button class="send" @click="setCashOnDelivery">确定货到付款</button>
        <view class="info" @click="$refs.showDelivery.close()">取消</view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import {
  queryPrescriptionAdjustMoney,
  querySelfExtractionTips,
  queryToPayTips,
  queryLogisticsTips,
  queryFastMallOrderStatus,
  queryOrderByShoppingCartFastOnline,
  createFastMallOrder,
} from '@/api/shop.js';
import { getReceiptWay } from '@/api/order';
import { uploadImg } from '@/api/oss.js';
import { findDrugStoreDetail } from '@/api/base';
import myJsTools from '@/common/js/myJsTools.js';
import DRUGLIST from '../components/pharmacyDrug.vue';
import PHARMACY from '../components/pharmacy.vue';
import ADDRESS from '../components/address.vue';
import LIST from '../components/drugList.vue';
import PROPCENTER from '@/components/propCenter/propCenter.vue';
import { Toast } from '@/common/js/pay.js';
import { getDate } from '@/common/js/myJsTools';
import { getCityByLocation } from '../map';

let timer = null;

export default {
  name: 'Quick',
  components: {
    LIST,
    PHARMACY,
    DRUGLIST,
    ADDRESS,
    PROPCENTER,
  },
  data() {
    return {
      // 最终支付方式 1 微信 2 支付宝 6 到店支付 7 货到付款
      callType: 0,
      // 药店id列表
      drugStoreIdList: [],
      // 自提时候的支付方式 1 在线 0 到店支付
      payType: 1,
      // 拦截药品列表
      drugList: [],
      // 是否跨省
      isAcrossProvinces: 0,
      // 显示跨省药品
      showDrugList: false,
      // 疾病标签
      drugLableListStr: '',
      // 自提时间
      getGoodsTime: '',
      // 时间限制
      time: {
        start: getDate(),
        end: getDate('day', 7),
      },
      // 显示支付提示
      showPayTip: false,
      // 是否只有一个药店
      isOnly: false,
      // 患者信息
      patientInfo: '',
      // 调剂费优惠
      adjustDeMoney: 0,
      // 实际调剂费
      adjustRealyMoney: 0,
      // 调剂费原价
      adjustMoney: 0,
      // 处方总金额
      totalMoney: 0,
      // 药店信息
      drugDetail: {},
      // 自提地址
      address: null,
      // 快递类型
      logisticsType: 0,
      // 订单列表
      orderList: [],
      // 处方调剂费提示信息
      adjustMoneyTip: '',
      // 支付提示
      payTip: '',
      // 物流提示
      logisticsTip: '',
      // 自提提示
      ztTip: '',
      // 物流费用
      logist_count: 0,
      // 要提示的消息
      tip: '',
      // 图片数组
      fileList: [],
      // 配送方式 0 快递 1 自提
      delivery: -1,
      // 是否自营
      isZY: false,
      // 查询次数
      num: 3,
      // 支付方式
      payList: [],
      // 发票相关
      invoice: {},
    };
  },
  computed: {
    strLength() {
      return 100 - Number(this.drugLableListStr?.length || 0);
    },
    // 费用总计
    total() {
      // 如果只有一个药店 并且选择快递
      if (this.isOnly && this.delivery == 0) {
        let n =
          Number(this.totalMoney) * 100 +
          Number(this.adjustRealyMoney) * 100 +
          Number(this.logist_count) * 100;
        return (n / 100).toFixed(2);
      }
      // 单药店 自提 无需物流费
      if (this.isOnly && this.delivery != 0) {
        let n =
          Number(this.totalMoney) * 100 + Number(this.adjustRealyMoney) * 100;
        return (n / 100).toFixed(2);
      }
      // 多药店 只能物流
      if (!this.isOnly) {
        let n =
          Number(this.totalMoney) * 100 +
          Number(this.adjustRealyMoney) * 100 +
          Number(this.logist_count) * 100;
        return (n / 100).toFixed(2);
      }
    },
  },
  onLoad({ id }) {
    this.drugStoreIdList = [id];
    uni.removeStorageSync('shop_patient');
    uni.removeStorageSync('shop_address');

    this.getDetail(id);
    this.getAdjustMoney();
    this.getLogisTip();
    this.getPayTip();
    this.getZtTip();
  },
  onShow() {
    let patientInfo = uni.getStorageSync('shop_patient');
    let address = uni.getStorageSync('shop_address');
    if (patientInfo) {
      this.patientInfo = patientInfo;
    }
    if (address) {
      this.address = address;
    }
  },
  methods: {
    // 药品拦截点击确定 把当前定位修改为已选择的收货地址
    async intercept() {
      const { latitude, longitude, addressArea, addressDetail } = this.address;
      let { province } = await getCityByLocation(latitude + ',' + longitude);
      let obj = {
        lat: latitude,
        lng: longitude,
        city: addressArea + '' + addressDetail,
        province,
      };
      uni.setStorageSync('shop_city', obj);
      uni.switchTab({
        url: '/pages/shop/index',
      });
    },
    // 根据收货地址与药店地址 决定是否跨省
    async queryIsProvince() {
      // 药店所在省
      const { provinceName } = this.drugDetail;
      const { latitude, longitude } = this.address;
      // 收货地址省
      let { province } = await getCityByLocation(latitude + ',' + longitude);
      if (provinceName == province) return Promise.resolve(0);
      // 跨省为1
      return Promise.resolve(1);
    },
    // 设置货到付款
    setCashOnDelivery() {
      this.callType = 7;
      // 关闭弹窗
      this.$refs.showDelivery.close();
      this.callPay();
    },
    // 获取系统配置
    async getSystem(subjectId) {
      let { data } = await getReceiptWay({
        subjectId,
      });
      this.payList = data;
    },
    // 获取详情
    async getDetail(id) {
      let res = await queryOrderByShoppingCartFastOnline([id]);
      const {
        adjustDeMoney,
        adjustRealyMoney,
        adjustMoney,
        orderList,
        drugLableListStr,
      } = res.data;
      // 药品标签
      this.drugLableListStr = drugLableListStr;
      this.adjustDeMoney = Number(adjustDeMoney);
      this.adjustRealyMoney = Number(adjustRealyMoney);
      this.adjustMoney = Number(adjustMoney);
      let p = 0;
      orderList.forEach((v) => {
        p += Number(v.totalRealyMoney) * 100;
        // 默认普通快递
        v.sele_delivery = 0;
      });
      // 只有一条数据
      if (orderList.length == 1) {
        // 且只有一个药店
        if (orderList[0].drugStoreList.length == 1) {
          let id = orderList[0].drugStoreList[0].drugStoreID;
          this.isOnly = true;
          await this.getDurgDetail(id);
        } else {
          // 一条数据 多个药店 为自营
          this.isZY = true;
        }
      }
      this.totalMoney = p / 100;
      this.orderList = orderList;
      this.logistCount();
    },
    // 处方调剂费提示
    async getAdjustMoney() {
      let res = await queryPrescriptionAdjustMoney();
      this.adjustMoneyTip = res.data;
    },
    // 物流提示
    async getLogisTip() {
      let res = await queryLogisticsTips();
      this.logisticsTip = res.data;
    },
    // 自提提示
    async getZtTip() {
      let res = await querySelfExtractionTips();
      this.ztTip = res.data;
    },
    // 获取支付提示
    async getPayTip() {
      let res = await queryToPayTips();
      this.payTip = res.data;
    },
    // 获取药店详情
    async getDurgDetail(drugstoreId) {
      this.getSystem(drugstoreId);
      let res = await findDrugStoreDetail({
        drugstoreId,
      });
      this.drugDetail = res.data;
    },
    // 单药店物流自提提示
    setTip() {
      if (this.delivery == 1) {
        this.showTip('zt');
        return;
      }
      if (this.delivery == 0) {
        this.showTip('wl');
        return;
      }
    },
    // 单药店选择配送物流
    setLogistType(n) {
      this.logisticsType = n;
      let item = this.orderList[0];
      item.sele_delivery = n;
      this.$set(this.orderList, 0, item);
      this.logistCount();
    },
    // 图片预览
    preview(item) {
      uni.previewImage({
        urls: [item.url],
      });
    },
    // 监听选择物流方式
    logist(index, n) {
      let item = this.orderList[index];
      item.sele_delivery = n;
      // 改变物流方式
      this.$set(this.orderList, index, item);
      this.logistCount();
    },
    // 计算已选择物流费用
    logistCount() {
      let n = 0;
      this.orderList.forEach((v) => {
        // 普通
        if (v.sele_delivery == 0) {
          n += Number(v.ordinaryLogMoney) * 100;
        }
        // 其他物流
        if (v.sele_delivery == 1) {
          n += Number(v.specialLogMoney) * 100;
        }
      });
      this.logist_count = n / 100;
    },
    // 选择配送方式
    seleType() {
      let that = this;
      const length = this.payList.length;
      let typeList=['统一配送', '自提']
      if(this.drugDetail){
        if (this.drugDetail.isCandelivery == null && this.drugDetail.isCanself == null) {
          typeList = [];
        } else {
          if (this.drugDetail.isCandelivery == "1" && this.drugDetail.isCanself == "0") {
            typeList = ["统一配送"];
          } else if (
              this.drugDetail.isCanself == "1" &&
              this.drugDetail.isCandelivery == "0"
          ) {
            typeList = ["自提"];
          } else if (
              this.drugDetail.isCanself == "1" &&
              this.drugDetail.isCandelivery == "1"
          ) {
            typeList = ["统一配送", "自提"];
          }
          
          // 添加同城配送选项
          if (this.drugDetail.isCanintracity == "1") {
            typeList.push("同城配送");
          }
        }
      }
      uni.showActionSheet({
        itemList: typeList,
        success(res) {
          const selected = typeList[res.tapIndex];
          if (selected === "统一配送") {
            that.toAddress();
            that.delivery = 0;
            if (length) {
              that.payType = 1;
            } else {
              that.payType = 7;
            }
          } else if (selected === "自提") {
            that.delivery = 1;
            if (length) {
              that.payType = 1;
            } else {
              that.payType = 6;
            }
          } else if (selected === "同城配送") {
            that.delivery = 2;
            Toast("同城配送暂时无法下单");
            if (length) {
              that.payType = 1;
            } else {
              that.payType = 7;
            }
          }
        },
      });
    },
    // 去选择收货地址
    toAddress() {
      // 选择快递地址
      uni.navigateTo({
        url: '/pages/address/index?action=shop',
      });
    },
    // 提示消息
    showTip(str) {
      if (str == 'wl') {
        this.tip = this.logisticsTip;
      }
      if (str == 'tjf') {
        this.tip = this.adjustMoneyTip;
      }
      if (str == 'zt') {
        this.tip = this.ztTip;
      }
      this.$refs.popup.open();
    },
    // 隐藏提示
    hideTip() {
      this.$refs.popup.close();
    },
    // 选择患者
    toPatient() {
      uni.navigateTo({
        url: '/pages/personalCenter/patientManage/index?action=shop',
      });
    },
    // 上传图片
    async upImg(obj) {
      let para = {
        folderType: 11,
        imgBody: obj.base64,
        // 患者id
        otherId: this.patientInfo.patientId,
      };
      let res = await uploadImg(para);
      // 图片名称
      return res.data.url;
    },
    // 移除图片
    delFile(n) {
      this.fileList.splice(n, 1);
    },
    // 初次选择
    cloosImgTop() {
      if (!this.fileList.length) {
        this.cloosImg();
      }
    },
    // 选择图片
    cloosImg() {
      const that = this;
      uni.chooseImage({
        count: 1,
        success: function(res) {
          // 读取图片
          const file = res.tempFiles[0];
          myJsTools.setImgZip(file, (dataUrl) => {
            that.fileList.push({
              base64: dataUrl.split(',')[1],
              url: dataUrl,
              name: '',
            });
          });
        },
      });
    },
    // 自提时间变化
    dateChange(e) {
      this.getGoodsTime = e.detail.value;
    },
    // 跳转发票信息
    toInvoice() {
      uni.navigateTo({
        url: '/pages/shop/invoice/index',
      });
    },
    // 切换支付形式
    setPayType(n) {
      if (this.payType == n) return;
      this.payType = n;
    },
    // 点击去支付
    send() {
      if (!this.patientInfo) {
        Toast('请选择就诊人');
        return;
      }
      if(this.drugDetail.isCanintracity == "1"){
        if(this.delivery == 2){
          Toast("同城配送暂时无法下单");
          return;
        }
      }
      // 判断是否包含处方药
      let list = this.orderList[0].drugStoreList[0].shoppingCartList;

      // 存在处方药
      if (!list.every((item) => item.otc === '1')) {
        if (!this.fileList.length) {
          Toast('请上传处方或病历照片');
          return;
        }
      }

      // 如果只有一个药店
      if (this.isOnly) {
        // 判断是否选择配送方式
        if (this.delivery == -1) {
          Toast('请选择配送方式');
          return;
        }
        if (this.delivery == 0) {
          if (!this.address) {
            Toast('请选择收货地址');
            return;
          }
        }
      } else {
        if (!this.address) {
          Toast('请选择收货地址');
          return;
        }
      }

      // 判断配送时间
      if (!this.getGoodsTime) {
        let str = this.delivery == 1 ? '请选择自提时间' : '请选择期望配送时间';
        Toast(str);
        return;
      }

      // 未选择发票
      if (!this.invoice?.invoiceType) {
        Toast('请选择发票');
        return;
      }

      let that = this;

      // 如果选在线支付
      if (this.payType == 1) {
        // 只有一个方式
        if (this.payList.length == 1) {
          let item = this.payList[0];
          this.callId = item.appid;
          // 是否微信支付
          if (item.receiptType == 1) {
            this.callType = 1;
          } else {
            this.callType = 2;
          }
          // 显示提示
          this.showPayTip = true;
          return;
        }

        uni.showActionSheet({
          itemList: ['微信支付', '支付宝支付'],
          success(res) {
            let item;
            if (res.tapIndex == 0) {
              that.callType = 1;
              let obj = that.payList.find((it) => it.receiptType == 1);
              item = obj.appid;
            } else {
              that.callType = 2;
              let obj = that.payList.find((it) => it.receiptType == 2);
              item = obj.appid;
            }
            that.callId = item;
            // 显示提示
            that.showPayTip = true;
          },
        });
        return;
      }

      this.callType = this.payType;

      // 显示提示
      this.showPayTip = true;
      return;
    },
    // 抽离发起支付接口参数
    setParam() {
      this.showPayTip = false;

      this.callPay();
    },
    // 拼参数
    async callPay() {
      uni.showLoading({
        mask: true,
      });

      // 物流方式 1 配送 2 自提
      let deliveryType = 1;

      // 只有一个药店
      if (this.orderList.length == 1) {
        // delivery: 0-统一配送, 1-自提, 2-同城配送
        // deliveryType: 1-配送, 2-自提, 3-同城配送
        if (this.delivery == 2) {
          deliveryType = 3;  // 同城配送
        } else {
          deliveryType = this.delivery + 1;
        }
      }

      let images = [];

      // 如果存在图片
      if (this.fileList.length) {
        // 循环上传图片
        for (let i = 0; i < this.fileList.length; i++) {
          try {
            let url = await this.upImg(this.fileList[i]);
            images.push(url);
          } catch (e) {
            console.log('图片上传失败', e);
          }
        }
        // this.images = images;
      }

      // 收货地址
      const address = this.address;

      const logistics = {
        // 药店id
        drugStoreId: this.drugStoreIdList[0],
        // 配送方式
        deliveryType,
        deliveryAddressDetail:
          deliveryType == 1
            ? address.addressArea + '' + address.addressDetail
            : '',
        // 收件人
        deliveryName: deliveryType == 1 ? address.deliveryName : '',
        deliveryTelNo: deliveryType == 1 ? address.telNo : '',
        // 物流方式
        logisticsCustomName: this.logisticsType == 0 ? '普通物流' : '顺丰物流',
        // 物流费用
        logisticsCustomCost: deliveryType == 1 ? this.logist_count : 0,
        // 代煎费
        djCost: 0,
      };

      let payType = this.callType;

      if (deliveryType == 1) {
        // 是否跨省
        this.isAcrossProvinces = await this.queryIsProvince();
      }

      // 无需支付
      if (this.total <= 0) {
        payType = 0;
      }

      // 如果未选择
      if (this.invoice.invoiceType == 3) {
        this.invoice.invoiceType = '';
      }

      // 整合参数
      let obj = {
        appid: uni.getStorageSync('appId'),
        // 经度
        longitude: address?.longitude,
        // 纬度
        latitude: address?.latitude,
        // 医诺卫scanid
        scanid: uni.getStorageSync('ynw_scanid'),
        // 医诺卫userId
        userid: uni.getStorageSync('ynw_patientId'),
        // 医诺卫openId
        ynwopenid: uni.getStorageSync('ynw_openid'),
        // 物流相关
        logistics: [logistics],
        // 支付方id
        callId: this.callId || '',
        // 调剂费用实际支付
        dispensingFee: this.adjustRealyMoney,
        // 调剂费优惠
        disDispensingFee: this.adjustDeMoney,
        // 调剂费应付
        shouldDispensingFee: this.adjustMoney,
        openid: uni.getStorageSync('wxInfo').openId,
        // 药品金额
        orderMoney: this.orderList[0].totalRealyMoney,
        // 图片
        diagnosisImg: images.length ? JSON.stringify(images) : '',
        // 病症
        diagnosisInfo: this.drugLableListStr,
        // 患者名称
        patientName: this.patientInfo.patientName,
        patientId: this.patientInfo.patientId,
        payType,
        // 最终合计金额
        totalMoney: this.total,
        // 药店id列表
        drugStoreIdList: this.drugStoreIdList,
        // 发票信息
        ...this.invoice,
        // 自提/配送日期
        getGoodsTime: this.getGoodsTime,
        // 是否跨省
        isAcrossProvinces: this.isAcrossProvinces,
      };

      try {
        let res = await createFastMallOrder(obj);

        const { orderNo, orderStatus, canPay, drugList } = res.data;

        // 药品拦截
        if (canPay == 0) {
          this.drugList = drugList;
          this.showDrugList = true;
          uni.hideLoading();
          return;
        }

        // 无需支付
        if (payType == 0 || payType == 6 || payType == 7) {
          // 重新查询购物车。
          this.$store.dispatch('shop/getCartList');
          // 调用成功 直接跳转成功页面
          if (orderStatus == 2) {
            uni.reLaunch({
              url: '../result/success?id=' + orderNo + '&type=' + payType,
            });
          }
          return;
        }

        // 如果是微信支付
        if (payType == 1) {
          // 调用微信支付
          this.toPay(res.data);
        } else {
          uni.navigateTo({
            url:
              '/pages/pay/pay?isNewShop=1&price=' +
              this.total +
              '&orderNo=' +
              res.data.orderNo +
              '&url=' +
              btoa(res.data.url),
          });
        }
        uni.hideLoading();
      } catch (e) {
        console.log('发起支付失败', e);
        uni.hideLoading();
      }
    },
    // 支付
    toPay(info) {
      // 重新查询购物车。
      this.$store.dispatch('shop/getCartList');
      const orderNo = info.orderNo;
      delete info.orderNo;
      delete info.canPay;
      // 调用微信支付
      let that = this;
      WeixinJSBridge.invoke('getBrandWCPayRequest', info, (res) => {
        if (res.err_msg == 'get_brand_wcpay_request:ok') {
          // 查询状态
          that.getPayStatus(orderNo);
          // 调用三次
          timer = setInterval(() => {
            that.getPayStatus(orderNo);
          }, 2000);
        } else {
          Toast('取消支付');
          uni.redirectTo({
            url: '/pages/shopOrder/detail?orderNo=' + orderNo,
          });
        }
      });
    },
    // 查询支付状态
    async getPayStatus(orderNo) {
      // 根据订单号查询
      let res = await queryFastMallOrderStatus(orderNo);
      this.num--;

      if (res.data.orderStatus == 2) {
        clearInterval(timer);
        timer = null;
        this.num = 3;
        uni.reLaunch({
          url: '../result/success?id=' + orderNo + '&type=2',
        });
      }

      // 查询过三次
      if (this.num <= 0) {
        clearInterval(timer);
        // 应该跳转到失败
        if (res.data.orderStatus == 1) {
          uni.navigateTo({
            url: '../result/fail?isNewShop=1&orderNo=' + orderNo,
          });
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}

// 跨省
.across_provinces {
  .title {
    padding-bottom: 24rpx;
    font-size: 28rpx;
  }

  .info {
    padding: 24rpx 10rpx;
    font-size: 28rpx;
    line-height: 42rpx;
    font-weight: normal;
  }
}

// 发票
.invoice {
  @include flex(lr);
  margin-bottom: 24rpx;
  height: 80rpx;
  border-radius: 8rpx;
  background-color: #fff;
  font-size: 28rpx;
  padding: 0 24rpx;

  &.date {
    .right {
      flex: 1;
      justify-content: flex-end;

      .flex {
        @include flex;
      }
    }
  }

  .right {
    @include flex;

    text {
      color: #666;
    }
  }
}

// 货到付款
.delivery_pay {
  width: 592rpx;
  @include flex(lr);
  padding: 32rpx;
  box-sizing: border-box;
  flex-direction: column;
  border-radius: 16rpx;
  background-color: #fff;

  .title {
    font-size: 32rpx;
    font-weight: bold;
  }

  .text {
    font-size: 28rpx;
    line-height: 140rpx;
  }

  .send {
    width: 334rpx;
    height: 84rpx;
    @include flex;
    @include bg_theme;
    border-radius: 42rpx;
    font-size: 32rpx;
    color: #fff;
    font-weight: bold;
  }

  .info {
    font-size: 28rpx;
    color: #666;
    margin-top: 24rpx;
  }
}

// 选择支付方式
.sele_pay {
  background-color: #fff;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 12rpx;

  .item {
    height: 88rpx;
    padding: 0 24rpx;
    @include flex(lr);
    font-size: 28rpx;
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    image {
      width: 32rpx;
      height: 32rpx;
    }
  }
}

::v-deep .wrapper .block {
  height: auto;
}

.pay_tip {
  .title {
    font-size: 32rpx;
  }

  .cont {
    padding: 10rpx 0 20rpx;
    font-size: 28rpx;
    text-align: left;
  }
}

.order_submit {
  padding: 24rpx 32rpx 120rpx;
  background-color: #f5f5f5;

  .sele_warp {
    width: 100%;
    background-color: #fff;
    border-radius: 8rpx;
    font-size: 28rpx;
    padding: 0 24rpx;
    margin-bottom: 24rpx;

    .textarea {
      width: 100%;
      height: 138rpx;
      border-radius: 8rpx;
      border: 1px solid #f5f5f5;
      position: relative;

      .n {
        position: absolute;
        right: 12rpx;
        bottom: 12rpx;
        font-size: 24rpx;
        color: #999;
      }

      textarea {
        width: 100%;
        height: 100%;
        padding: 12rpx;
        font-size: 24rpx;
      }
    }

    .sele_but {
      @include flex(lr);
      height: 88rpx;

      &.act {
        .uni-icons {
          transform: rotate(90deg);
        }
      }

      .uni-icons {
        transition: all 0.3s;
        margin-left: 10rpx;
      }

      .right {
        @include flex;

        .name {
          color: #666;
        }
      }
    }

    .img_list {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-gap: 20rpx;
      padding-bottom: 20rpx;

      .img_item {
        width: 200rpx;
        height: 200rpx;
        border-radius: 8rpx;
        border: 1px dashed #eee;
        position: relative;
        @include flex;
        flex-direction: column;

        image {
          width: 64rpx;
          height: 64rpx;
        }

        text {
          color: #999;
        }

        .img {
          width: 100%;
          height: 100%;
        }

        .del {
          background-color: rgba($color: #000000, $alpha: 0.3);
          position: absolute;
          border-radius: 50%;
          top: 0;
          right: 0;
        }
      }
    }

    .sele_info {
      @include flex;
      align-items: flex-start;
      padding-bottom: 20rpx;

      text {
        flex: none;
        width: 110rpx;

        &.red {
          color: red;
          flex: 1;
        }
      }

      .info_text {
        flex: 1;
        color: #999;
      }
    }

    .logistics {
      width: 100%;
      padding-bottom: 24rpx;

      &.pay {
        padding-top: 24rpx;
      }

      .item {
        height: 76rpx;
        padding: 0 24rpx;
        @include flex(lr);
        background-color: #f5f5f5;
        border-radius: 8rpx;

        &:last-child {
          margin-top: 24rpx;
        }

        .name {
          font-size: 28rpx;
        }

        .right {
          @include flex;

          text {
            font-size: 28rpx;
            color: red;
            padding-right: 30rpx;
          }

          image {
            width: 32rpx;
            height: 32rpx;
          }
        }
      }
    }
  }

  .warp {
    margin-bottom: 24rpx;
  }

  .user_info {
    @include flex(lr);
    padding: 18rpx 0;

    .user_head {
      width: 128rpx;
      height: 128rpx;
      border-radius: 8rpx;
      flex: none;
    }

    .user_desc {
      flex: 1;
      padding-left: 22rpx;

      .user_name {
        font-size: 32rpx;
        font-weight: bold;
      }

      .user_other {
        @include flex(left);
        padding-top: 10rpx;

        image {
          width: 32rpx;
          height: 32rpx;
        }

        text {
          padding-left: 10rpx;
          color: #666;
          font-size: 24rpx;
        }
      }
    }

    .uni-icons {
      flex: none;
    }
  }
}

// 覆盖样式
.pharmacy_detail {
  padding: 20rpx 0;
}

.address {
  padding: 20rpx 0;
}

.footer {
  width: 100%;
  height: 104rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 1;
  background-color: #fff;
  padding: 0 32rpx;
  @include flex(lr);
  box-shadow: 0px 4rpx 8rpx 0px rgba(0, 0, 0, 0.5);

  .count {
    font-size: 28rpx;
    color: #333;

    text {
      color: red;
    }
  }

  .but {
    width: 160rpx;
    height: 60rpx;
    color: #fff;
    @include flex;
    @include bg_theme;
    font-size: 28rpx;
    border-radius: 30rpx;
  }
}

.pop {
  padding: 0 32rpx 50rpx;
  background-color: #fff;
  border-radius: 16rpx 16rpx 0px 0px;

  .pop_title {
    height: 88rpx;
    @include flex;
    position: relative;
    font-size: 32rpx;

    .uni-icons {
      position: absolute;
      right: 0;
    }
  }

  .pop_text {
    color: #999;
    font-size: 24rpx;
    line-height: 40rpx;
  }
}
</style>
