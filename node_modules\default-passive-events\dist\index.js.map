{"version": 3, "file": "index.js", "sources": ["../src/index.js", "../src/utils.js"], "sourcesContent": ["import { eventListenerOptionsSupported } from './utils';\n\nconst defaultOptions = {\n  passive: true,\n  capture: false\n};\nconst supportedPassiveTypes = [\n  'scroll', 'wheel',\n  'touchstart', 'touchmove', 'touchenter', 'touchend', 'touchleave',\n  'mouseout', 'mouseleave', 'mouseup', 'mousedown', 'mousemove', 'mouseenter', 'mousewheel', 'mouseover'\n];\nconst getDefaultPassiveOption = (passive, eventName) => {\n  if (passive !== undefined) return passive;\n\n  return supportedPassiveTypes.indexOf(eventName) === -1 ? false : defaultOptions.passive;\n};\n\nconst getWritableOptions = (options) => {\n  const passiveDescriptor = Object.getOwnPropertyDescriptor(options, 'passive');\n    \n  return passiveDescriptor && passiveDescriptor.writable !== true && passiveDescriptor.set === undefined\n    ? Object.assign({}, options)\n    : options;\n};\n\nconst overwriteAddEvent = (superMethod) => {\n  EventTarget.prototype.addEventListener = function (type, listener, options) {\n    const usesListenerOptions = typeof options === 'object' && options !== null;\n    const useCapture          = usesListenerOptions ? options.capture : options;\n\n    options         = usesListenerOptions ? getWritableOptions(options) : {};\n    options.passive = getDefaultPassiveOption(options.passive, type);\n    options.capture = useCapture === undefined ? defaultOptions.capture : useCapture;\n\n    superMethod.call(this, type, listener, options);\n  };\n\n  EventTarget.prototype.addEventListener._original = superMethod;\n};\n\nconst supportsPassive = eventListenerOptionsSupported();\n\nif (supportsPassive) {\n  const addEvent = EventTarget.prototype.addEventListener;\n  overwriteAddEvent(addEvent);\n}\n", "export const eventListenerOptionsSupported = () => {\n  let supported = false;\n\n  try {\n    const opts = Object.defineProperty({}, 'passive', {\n      get() {\n        supported = true;\n      }\n    });\n\n    window.addEventListener('test', null, opts);\n    window.removeEventListener('test', null, opts);\n  } catch (e) {}\n\n  return supported;\n}\n"], "names": ["superMethod", "supportedPassiveTypes", "supported", "opts", "Object", "defineProperty", "get", "window", "addEventListener", "removeEventListener", "e", "eventListenerOptionsSupported", "addEvent", "EventTarget", "prototype", "type", "listener", "options", "passive", "usesListenerOptions", "useCapture", "capture", "passiveDescriptor", "getOwnPropertyDescriptor", "writable", "undefined", "set", "assign", "getWritableOptions", "indexOf", "call", "this", "_original"], "mappings": "IAyB2BA,EAnBrBC,EAAwB,CAC5B,SAAU,QACV,aAAc,YAAa,aAAc,WAAY,aACrD,WAAY,aAAc,UAAW,YAAa,YAAa,aAAc,aAAc,aAiC7F,GC1C6C,WAC3C,IAAIC,GAAY,EAEhB,IACE,IAAMC,EAAOC,OAAOC,eAAe,GAAI,UAAW,CAChDC,eACEJ,GAAY,KAIhBK,OAAOC,iBAAiB,OAAQ,KAAML,GACtCI,OAAOE,oBAAoB,OAAQ,KAAMN,GACzC,MAAOO,IAET,OAAOR,ED0BeS,GAEH,CACnB,IAAMC,EAAWC,YAAYC,UAAUN,iBAlBdR,EAmBPY,EAlBlBC,YAAYC,UAAUN,iBAAmB,SAAUO,EAAMC,EAAUC,GACjE,IAhB6BC,EAgBvBC,EAAyC,iBAAZF,GAAoC,OAAZA,EACrDG,EAAsBD,EAAsBF,EAAQI,QAAUJ,GAEpEA,EAAkBE,EAbK,SAACF,GAC1B,IAAMK,EAAoBlB,OAAOmB,yBAAyBN,EAAS,WAEnE,OAAOK,IAAoD,IAA/BA,EAAkBE,eAA+CC,IAA1BH,EAAkBI,IACjFtB,OAAOuB,OAAO,GAAIV,GAClBA,EAQsCW,CAAmBX,GAAW,IAC9DC,aAnBMO,KADeP,EAoBaD,EAAQC,SAnBlBA,GAEmB,IAA9CjB,EAAsB4B,QAiBgCd,KA5BpD,EA6BPE,EAAQI,aAAyBI,IAAfL,GAAoDA,EAEtEpB,EAAY8B,KAAKC,KAAMhB,EAAMC,EAAUC,IAGzCJ,YAAYC,UAAUN,iBAAiBwB,UAAYhC"}