<template>
  <view class="evaluation">
    <view class="evaluationDiv">
      <view class="jzSatisficing">
        <view class="mydView">
          <text>就诊满意度</text>
          <rate
            @change="rateChange($event, '0')"
            size="44"
            :star_empty="'/static/images/question/assess.png'"
            :star_fill="'/static/images/question/assess_active.png'"
          ></rate>
        </view>
        <textarea
          class="evalText"
          v-model="mreDescription"
          placeholder-style="color:#999999;font-size:14px"
          maxlength="200"
          placeholder="本次服务是否满意，说说你的心得吧"
        />
        <view class="uploader">
          <template v-for="(item, index) in mreImgList">
            <view class="img-box">
              <image class="img-list" :src="item.url" mode="aspectFill"></image>
            </view>
          </template>
          <view
            v-if="mreImgList.length < 9"
            class="img-box uploadImg"
            @click="chooseImage"
          >
            <view>
              <image
                src="/static/images/pic.png"
                mode=""
                class="picIcon"
              ></image>
            </view>
            <view>上传图片</view>
          </view>
        </view>
      </view>

      <view class="jzSatisficing otherView" v-if="type.length">
        <view class="mydView" v-if="type.includes('5')">
          <text>取药满意度</text>
          <rate
            @change="rateChange($event, '1')"
            size="44"
            :star_empty="'/static/images/question/assess.png'"
            :star_fill="'/static/images/question/assess_active.png'"
          ></rate>
        </view>
        <view class="mydView wlEval" v-if="type.includes('4')">
          <text>物流满意度</text>
          <rate
            @change="rateChange($event, '2')"
            size="44"
            :star_empty="'/static/images/question/assess.png'"
            :star_fill="'/static/images/question/assess_active.png'"
          ></rate>
        </view>
      </view>
    </view>

    <FooterButton @click="commitEval">提交</FooterButton>
  </view>
</template>

<script>
import { Toast } from '@/common/js/pay.js';
import rate from '@/components/rate/rate.vue';
import FooterButton from '@/components/footer_button/button.vue';
import { saveEvaluate, queryEvaluateType } from '@/api/order.js';
import { uploadImg } from '@/api/oss.js';
import myJsTools from '@/common/js/myJsTools.js';
export default {
  components: { rate, FooterButton },
  data() {
    return {
      pageParam: {},
      jzSatisficing: '',
      qySatisficing: '',
      wlSatisficing: '',
      mreDescription: '',
      mreImgList: [],
      type: [],
    };
  },
  onLoad(v) {
    this.pageParam = v;
    this.getType();
  },
  methods: {
    // 获取评价类型
    async getType() {
      let { data } = await queryEvaluateType(this.pageParam.regId);
      this.type = data.type;
    },
    rateChange({ value }, type) {
      if (type == '0') {
        this.jzSatisficing = value;
      } else if (type == '1') {
        this.qySatisficing = value;
      } else if (type == '2') {
        this.wlSatisficing = value;
      }
    },
    // 上传图片
    chooseImage() {
      var _this = this;
      uni.chooseImage({
        count: 1, //默认9
        sizeType: ['compressed'],
        sourceType: ['album'], //从相册选择
        success: (res) => {
          let mreImgList = this.mreImgList;
          if (res.tempFiles.length > 0) {
            for (let i = 0; i < res.tempFiles.length; i++) {
              let el = res.tempFiles[i];
              myJsTools.setImgZip(el, (dataUrl) => {
                mreImgList.push({
                  base64: dataUrl.split(',')[1],
                  url: dataUrl,
                  name: '',
                });
              });
            }
          }
          this.mreImgList = mreImgList;
        },
      });
    },

    async commitEval() {
      // 就诊满意度
      if (!this.jzSatisficing) {
        Toast('请对就诊满意度评价');
        return;
      }
      if (!this.mreDescription) {
        Toast('评价不可为空');
        return;
      }
      // 取药满意度
      if (!this.qySatisficing && this.type.includes('5')) {
        Toast('请对取药满意度评价');
        return;
      }
      // 物流满意度
      if (!this.wlSatisficing && this.type.includes('4')) {
        Toast('请对物流满意度评价');
        return;
      }

      uni.showLoading({
        mask: true,
      });

      let mreImgList = this.mreImgList;
      let mreImgOssList = [];
      if (mreImgList.length > 0) {
        for (let i = 0; i < mreImgList.length; i++) {
          let res = await this.uploadImgFun(mreImgList[i]);
          mreImgOssList.push(res.data.url);
        }
      }

      const { docId, orderNo } = this.pageParam;

      let obj = {
        docId,
        orderNo,
        evaluates: {
          1: this.mreDescription,
          2: this.mreImgList.length > 0 ? JSON.stringify(mreImgOssList) : '',
          3: this.jzSatisficing,
          4: this.wlSatisficing,
          5: this.qySatisficing,
        },
      };
      try {
        await saveEvaluate(obj);
        uni.hideLoading();
        uni.setStorageSync('evaluaAction', 'evaluaSuccess');
        Toast('评价成功');
        setTimeout(() => {
          uni.navigateBack({
            delta: 1,
          });
        }, 500);
      } catch (error) {
        uni.hideLoading();
      }
    },

    // 将图片上传到oss
    async uploadImgFun(element) {
      let para = {
        folderType: 16,
        imgBody: element.base64,
        otherId: this.pageParam.orderNo,
      };
      return await uploadImg(para);
    },
  },
};
</script>

<style scoped lang="scss">
.evaluationDiv {
  padding: 24rpx 32rpx;
  .jzSatisficing {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    padding: 28rpx 24rpx;
    .mydView {
      display: flex;
      align-items: center;
      color: #333333;
      font-size: 32rpx;
      text {
        margin-right: 24rpx;
      }
    }
  }
}
::v-deep.rate-media .rate-media-body {
  margin-top: 0;
}
::v-deep.rate-media {
  width: 65%;
}
.evalText {
  margin-top: 20rpx;
  font-size: 14px;
}

.uploader {
  display: flex;
  align-items: center;
  flex-direction: row;
  flex-wrap: wrap;

  .img-box {
    width: 200rpx;
    height: 200rpx;
    border-radius: 8px;
    border: 1px dashed rgba(151, 151, 151, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18rpx;
    text-align: center;
    color: #666666;
    margin-top: 20rpx;
    .img-list {
      display: block;
      width: 100%;
      height: 100%;
    }
  }

  .img-box:not(:nth-child(3n + 1)) {
    margin-left: 10rpx;
  }

  .uploadImg {
    width: 200rpx;
    border: 1rpx dashed #979797;
    border-radius: 8rpx;
    text-align: center;
    color: #333333;
    font-size: 24rpx;
    display: flex;
    align-items: center;
    flex-direction: column;
    image {
      width: 64rpx;
      height: 60rpx;
      margin: 16rpx auto;
    }
  }
}

uni-textarea {
  height: 180rpx;
}
.wlEval,
.otherView {
  margin-top: 24rpx;
}
</style>
