!function(r,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.websdk=e():r.websdk=e()}(self,(function(){return(self.webpackChunkwebsdk=self.webpackChunkwebsdk||[]).push([[280],{308:function(r,e,t){t.r(e),t.d(e,{addUsersToChatRoom:function(){return y},addUsersToChatRoomAllowlist:function(){return Y},addUsersToChatRoomWhitelist:function(){return W},blockChatRoomMember:function(){return U},blockChatRoomMembers:function(){return x},chatRoomBlockMulti:function(){return k},chatRoomBlockSingle:function(){return S},createChatRoom:function(){return m},deleteChatRoomSharedFile:function(){return cr},destroyChatRoom:function(){return l},disableSendChatRoomMsg:function(){return J},enableSendChatRoomMsg:function(){return V},fetchChatRoomAnnouncement:function(){return or},fetchChatRoomSharedFileList:function(){return ir},getChatRoomAdmin:function(){return O},getChatRoomAllowlist:function(){return $},getChatRoomAttributes:function(){return ur},getChatRoomBlacklist:function(){return K},getChatRoomBlacklistNew:function(){return H},getChatRoomBlocklist:function(){return G},getChatRoomDetails:function(){return h},getChatRoomMembers:function(){return j},getChatRoomMuteList:function(){return D},getChatRoomMuted:function(){return w},getChatRoomMutelist:function(){return z},getChatRoomSharedFilelist:function(){return sr},getChatRoomWhitelist:function(){return Z},getChatRooms:function(){return u},getJoinedChatRooms:function(){return pr},isChatRoomWhiteUser:function(){return rr},isInChatRoomAllowlist:function(){return er},isInChatRoomMutelist:function(){return tr},joinChatRoom:function(){return I},leaveChatRoom:function(){return C},listChatRoomMember:function(){return E},listChatRoomMembers:function(){return g},modifyChatRoom:function(){return d},muteChatRoomMember:function(){return N},quitChatRoom:function(){return T},removeChatRoomAdmin:function(){return A},removeChatRoomAllowlistMember:function(){return X},removeChatRoomAttribute:function(){return dr},removeChatRoomAttributes:function(){return hr},removeChatRoomBlockMulti:function(){return P},removeChatRoomBlockSingle:function(){return B},removeChatRoomMember:function(){return f},removeChatRoomMembers:function(){return v},removeChatRoomWhitelistMember:function(){return q},removeMultiChatRoomMember:function(){return R},removeMuteChatRoomMember:function(){return M},removeSingleChatRoomMember:function(){return p},rmUsersFromChatRoomWhitelist:function(){return Q},setChatRoomAdmin:function(){return b},setChatRoomAttribute:function(){return lr},setChatRoomAttributes:function(){return mr},unblockChatRoomMember:function(){return F},unblockChatRoomMembers:function(){return L},unmuteChatRoomMember:function(){return _},updateChatRoomAnnouncement:function(){return ar},uploadChatRoomSharedFile:function(){return nr}}),t(2675),t(9463),t(2259),t(8706),t(4423),t(4346),t(3792),t(8598),t(2062),t(739),t(2010),t(6099),t(3362),t(7495),t(7764),t(8363),t(2953);var o=t(1531),a=t(8678),n=t(1750),c=t(2056),i=t(3893);function s(r){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},s(r)}function u(r){var e=this;if("number"!=typeof r.pagenum||"number"!=typeof r.pagesize)throw Error("Invalid parameter");var t=n.dO.call(this).error;if(t)return Promise.reject(t);var s={pagenum:r.pagenum||1,pagesize:r.pagesize||20},u=this.context,m=u.orgName,l=u.appName,h=u.accessToken,d={url:this.apiUrl+"/"+m+"/"+l+"/chatrooms",dataType:"json",type:"GET",headers:{Authorization:"Bearer "+h},data:s,success:function(e){"function"==typeof r.success&&r.success(e)},error:function(t){t.error&&t.error_description&&e.onError&&e.onError({type:o.C.WEBIM_CONNCTION_LOAD_CHATROOM_ERROR,message:t.error_description,data:t}),"function"==typeof r.error&&r.error(t)}};return c.vF.debug("Call getChatRooms",r),a.RD.call(this,d,i.jz.GET_CHATROOM_LIST)}function m(r){if("string"!=typeof r.name)throw Error("Invalid parameter");var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t={name:r.name,description:r.description,maxusers:r.maxusers,owner:this.user,members:r.members},o=this.context,s=o.orgName,u=o.appName,m=o.accessToken,l=o.jid,h={url:"".concat(this.apiUrl,"/").concat(s,"/").concat(u,"/chatrooms?resource=").concat(l.clientResource),dataType:"json",type:"POST",data:JSON.stringify(t),headers:{Authorization:"Bearer "+(r.token||m),"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call createChatRoom",r),a.RD.call(this,h,i.jz.CREATE_CHATROOM)}function l(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error("Invalid parameter");var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=t.jid,l={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatrooms/").concat(r.chatRoomId,"?resource=").concat(m.clientResource,"&version=v3"),dataType:"json",type:"DELETE",headers:{Authorization:"Bearer "+(r.token||u)},success:r.success,error:r.error};return c.vF.debug("Call destroyChatRoom",r),a.RD.call(this,l,i.jz.DESTROY_CHATROOM)}function h(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error("Invalid parameter");var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m={url:this.apiUrl+"/"+o+"/"+s+"/chatrooms/"+r.chatRoomId,dataType:"json",type:"GET",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call getChatRoomDetails",r),a.RD.call(this,m,i.jz.GET_CHATROOM_DETAIL)}function d(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error("Invalid parameter");var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=t.jid,l=r.chatRoomId,h={groupname:r.chatRoomName,description:r.description,maxusers:r.maxusers},d={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatrooms/").concat(l,"?resource=").concat(m.clientResource),type:"PUT",data:JSON.stringify(h),dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call modifyChatRoom",r),a.RD.call(this,d,i.jz.MODIFY_CHATROOM)}function p(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId||"string"!=typeof r.username)throw Error("Invalid parameter");var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=t.jid,l=r.chatRoomId,h=r.username,d={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatrooms/").concat(l,"/users/").concat(h,"?resource=").concat(m.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call removeChatRoomMember",r),a.RD.call(this,d,i.jz.REMOVE_CHATROOM_MEMBER)}var f=p;function R(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId||!Array.isArray(r.users))throw Error("Invalid parameter");var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=r.chatRoomId,o=r.users.join(","),s=this.context,u=s.orgName,m=s.appName,l=s.accessToken,h=s.jid,d={url:"".concat(this.apiUrl,"/").concat(u,"/").concat(m,"/chatrooms/").concat(t,"/users/").concat(o,"?resource=").concat(h.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+l,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call removeChatRoomMembers",r),a.RD.call(this,d,i.jz.MULTI_REMOVE_CHATROOM_MEMBER)}var v=R;function y(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId||!Array.isArray(r.users))throw Error("Invalid parameter");var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=r.chatRoomId,o={usernames:r.users},s=this.context,u=s.orgName,m=s.appName,l=s.accessToken,h=s.jid,d={url:"".concat(this.apiUrl,"/").concat(u,"/").concat(m,"/chatrooms/").concat(t,"/users?resource=").concat(h.clientResource),type:"POST",data:JSON.stringify(o),dataType:"json",headers:{Authorization:"Bearer "+l,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call addUsersToChatRoom",r),a.RD.call(this,d,i.jz.ADD_USERS_TO_CHATROOM)}function I(r){var e=r.roomId,t=r.message,a=void 0===t?"":t,i=r.ext,s=void 0===i?"":i,u=r.leaveOtherRooms,m=void 0!==u&&u,l=r.success,h=r.error;if("string"!=typeof e||""===e)throw Error("Invalid parameter roomId");if("string"!=typeof s)throw Error("Invalid parameter ext");if("boolean"!=typeof m)throw Error("Invalid parameter leaveOtherRooms");var d=n.dO.call(this).error;return d?Promise.reject(d):(c.vF.debug("Call joinChatRoom",r),this.logOut?Promise.reject({type:o.C.WEBIM_CONNECTION_CLOSED,message:"not login"}):this.mSync.handleChatRoom({roomId:e,ext:s,leaveOtherRooms:m,message:a,success:l,errorCb:h},"join"))}function T(r){if("string"!=typeof r.roomId||""===r.roomId)throw Error("Invalid parameter");var e=n.dO.call(this).error;return e?Promise.reject(e):(c.vF.debug("Call leaveChatRoom",r),this.logOut?Promise.reject({type:o.C.WEBIM_CONNECTION_CLOSED,message:"not login"}):this.mSync.handleChatRoom(r,"leave"))}var C=T;function E(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');if(isNaN(r.pageNum)||r.pageNum<=0)throw Error('The parameter "pageNum" should be a positive number');if(isNaN(r.pageSize)||r.pageSize<=0)throw Error('The parameter "pageSize" should be a positive number');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t={pagenum:r.pageNum,pagesize:r.pageSize},o=this.context,s=o.orgName,u=o.appName,m=o.accessToken,l={url:this.apiUrl+"/"+s+"/"+u+"/chatrooms/"+r.chatRoomId+"/users",dataType:"json",type:"GET",data:t,headers:{Authorization:"Bearer "+m,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call listChatRoomMembers",r),a.RD.call(this,l,i.jz.LIST_CHATROOM_MEMBERS)}var g=E;function j(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');if(r.cursor&&"string"!=typeof r.cursor)throw Error('Invalid parameter: "cursor"');if(r.limit&&"number"!=typeof r.limit)throw Error('Invalid parameter: "limit"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t={cursor:r.cursor,limit:r.limit||50,joined_time:!0},s=this.context,u=s.orgName,m=s.appName,l=s.accessToken,h={url:this.apiUrl+"/"+u+"/"+m+"/chatrooms/"+r.chatRoomId+"/users",dataType:"json",type:"GET",data:t,headers:{Authorization:"Bearer "+l,"Content-Type":"application/json"}};return c.vF.debug("Call getChatRoomMembers",r),a.RD.call(this,h,i.jz.LIST_GROUP_MEMBER).then((function(r){var e=r.data,t=r.cursor,a=e.map((function(r){for(var e in r)if(["member","admin","owner"].includes(e))return{role:e,userId:r[e],joinedTime:r.joined_time}}));return{type:o.C.REQUEST_SUCCESS,data:{cursor:t,members:a}}}))}function O(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=r.chatRoomId,l={url:this.apiUrl+"/"+o+"/"+s+"/chatrooms/"+m+"/admin",dataType:"json",type:"GET",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call getChatRoomAdmin",r),a.RD.call(this,l,i.jz.GET_CHATROOM_ADMIN)}function b(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');if("string"!=typeof r.username||""===r.username)throw Error('Invalid parameter: "username"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=t.jid,l=r.chatRoomId,h={newadmin:r.username},d={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatrooms/").concat(l,"/admin?resource=").concat(m.clientResource),type:"POST",dataType:"json",data:JSON.stringify(h),headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call setChatRoomAdmin",r),a.RD.call(this,d,i.jz.SET_CHATROOM_ADMIN)}function A(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');if("string"!=typeof r.username||""===r.username)throw Error('Invalid parameter: "username"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=t.jid,l=r.chatRoomId,h=r.username,d={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatrooms/").concat(l,"/admin/").concat(h,"?resource=").concat(m.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call removeChatRoomAdmin",r),a.RD.call(this,d,i.jz.REMOVE_CHATROOM_ADMIN)}function N(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof r.username||""===r.username)throw Error('Invalid parameter: "username"');if("number"!=typeof r.muteDuration)throw Error('Invalid parameter: "muteDuration"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=t.jid,l=r.chatRoomId,h={usernames:[r.username],mute_duration:r.muteDuration},d={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatrooms/").concat(l,"/mute?resource=").concat(m.clientResource),dataType:"json",type:"POST",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},data:JSON.stringify(h),success:r.success,error:r.error};return c.vF.debug("Call muteChatRoomMember",r),a.RD.call(this,d,i.jz.MUTE_CHATROOM_MEMBER)}function M(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');if("string"!=typeof r.username||""===r.username)throw Error('Invalid parameter: "username"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=t.jid,l=r.chatRoomId,h=r.username,d={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatrooms/").concat(l,"/mute/").concat(h,"?resource=").concat(m.clientResource),dataType:"json",type:"DELETE",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call unmuteChatRoomMember",r),a.RD.call(this,d,i.jz.REMOVE_MUTE_CHATROOM_MEMBER)}var _=M;function w(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=r.chatRoomId,l={url:this.apiUrl+"/"+o+"/"+s+"/chatrooms/"+m+"/mute",dataType:"json",type:"GET",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call getChatRoomMutelist",r),a.RD.call(this,l,i.jz.GET_MUTE_CHATROOM_MEMBERS)}var D=w,z=w;function S(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');if("string"!=typeof r.username||""===r.username)throw Error('Invalid parameter: "username"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=t.jid,l=r.chatRoomId,h=r.username,d={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatrooms/").concat(l,"/blocks/users/").concat(h,"?resource=").concat(m.clientResource),type:"POST",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call blockChatRoomMember",r),a.RD.call(this,d,i.jz.SET_CHATROOM_MEMBER_TO_BLACK)}var U=S;function k(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');if(!Array.isArray(r.usernames))throw Error('Invalid parameter: "usernames"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=t.jid,l=r.chatRoomId,h={usernames:r.usernames},d={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatrooms/").concat(l,"/blocks/users?resource=").concat(m.clientResource),data:JSON.stringify(h),type:"POST",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Chat blockChatRoomMembers:",d),a.RD.call(this,d,i.jz.MULTI_SET_CHATROOM_MEMBER_TO_BLACK)}var x=k;function B(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');if("string"!=typeof r.username||""===r.username)throw Error('Invalid parameter: "username"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=t.jid,l=r.chatRoomId,h=r.username,d={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatrooms/").concat(l,"/blocks/users/").concat(h,"?resource=").concat(m.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call unblockChatRoomMember",r),a.RD.call(this,d,i.jz.REMOVE_CHATROOM_MEMBER_BLACK)}var F=B;function P(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');if(!Array.isArray(r.usernames))throw Error('Invalid parameter: "usernames"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=t.jid,l=r.chatRoomId,h=r.usernames.join(","),d={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatrooms/").concat(l,"/blocks/users/").concat(h,"?resource=").concat(m.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call unblockChatRoomMembers",r),a.RD.call(this,d,i.jz.MULTI_REMOVE_CHATROOM_MEMBER_BLACK)}var L=P;function H(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=r.chatRoomId,l={url:this.apiUrl+"/"+o+"/"+s+"/chatrooms/"+m+"/blocks/users",type:"GET",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call getChatRoomBlocklist",r),a.RD.call(this,l,i.jz.GET_CHATROOM_BLOCK_MEMBERS)}var K=H,G=H;function J(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=t.jid,l=r.chatRoomId,h={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatrooms/").concat(l,"/ban?resource=").concat(m.clientResource),type:"POST",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call disableSendChatRoomMsg",r),a.RD.call(this,h,i.jz.DISABLED_CHATROOM_SEND_MSG)}function V(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=t.jid,l=r.chatRoomId,h={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatrooms/").concat(l,"/ban?resource=").concat(m.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call enableSendChatRoomMsg",r),a.RD.call(this,h,i.jz.ENABLE_CHATROOM_SEND_MSG)}function W(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');if(!Array.isArray(r.users))throw Error('Invalid parameter: "users"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=t.jid,l=r.chatRoomId,h={usernames:r.users},d={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatrooms/").concat(l,"/white/users?resource=").concat(m.clientResource),type:"POST",data:JSON.stringify(h),dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call addUsersToChatRoomWhitelist",r),a.RD.call(this,d,i.jz.ADD_USERS_TO_CHATROOM)}var Y=W;function Q(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');if("string"!=typeof r.userName||""===r.userName)throw Error('Invalid parameter: "userName"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=t.jid,l=r.chatRoomId,h={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatrooms/").concat(l,"/white/users/").concat(r.userName,"?resource=").concat(m.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call removeChatRoomAllowlistMember",r),a.RD.call(this,h,i.jz.REMOVE_CHATROOM_WHITE_USERS)}var q=Q,X=Q;function Z(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=r.chatRoomId,l={url:this.apiUrl+"/"+o+"/"+s+"/chatrooms/"+m+"/white/users",type:"GET",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call getChatRoomAllowlist",r),a.RD.call(this,l,i.jz.GET_CHATROOM_WHITE_USERS)}var $=Z;function rr(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');if("string"!=typeof r.userName||""===r.userName)throw Error('Invalid parameter: "userName"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=r.chatRoomId,l={url:this.apiUrl+"/"+o+"/"+s+"/chatrooms/"+m+"/white/users/"+r.userName,type:"GET",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call isInChatRoomAllowlist",r),a.RD.call(this,l,i.jz.CHECK_CHATROOM_WHITE_USER)}var er=rr;function tr(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,i=t.appName,s=t.accessToken,u=t.userId,m={url:this.apiUrl+"/"+o+"/"+i+"/sdk/chatrooms/"+r.chatRoomId+"/mute/"+u,dataType:"json",type:"GET",headers:{Authorization:"Bearer "+s}};return c.vF.debug("Call isInChatRoomMutelist",r),a.RD.call(this,m).then((function(r){return{type:r.type,data:r.data}}))}function or(r){if("string"!=typeof r.roomId||""===r.roomId)throw Error('Invalid parameter: "roomId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=r.roomId,l={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatrooms/").concat(m,"/announcement"),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call fetchChatRoomAnnouncement",r),a.RD.call(this,l,i.jz.GET_CHATROOM_ANN)}function ar(r){if("string"!=typeof r.roomId||""===r.roomId)throw Error('Invalid parameter: "roomId"');if("string"!=typeof r.announcement)throw Error('Invalid parameter: "announcement"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=t.jid,l=r.roomId,h={announcement:r.announcement},d={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatrooms/").concat(l,"/announcement?resource=").concat(m.clientResource),type:"POST",dataType:"json",data:JSON.stringify(h),headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call updateChatRoomAnnouncement:",r),a.RD.call(this,d,i.jz.UPDATE_CHATROOM_ANN)}function nr(r){var e;if("string"!=typeof r.roomId||""===r.roomId)throw Error('Invalid parameter: "roomId"');if("object"!==s(r.file))throw Error('Invalid parameter: "file"');var t=n.dO.call(this).error;if(t)return null===(e=r.onFileUploadError)||void 0===e?void 0:e.call(r,t);var o=this.context,u=o.orgName,m=o.appName,l=o.accessToken,h=o.jid,d=r.roomId;a.QM.call(this,{uploadUrl:"".concat(this.apiUrl,"/").concat(u,"/").concat(m,"/chatrooms/").concat(d,"/share_files?resource=").concat(h.clientResource),onFileUploadProgress:r.onFileUploadProgress,onFileUploadComplete:r.onFileUploadComplete,onFileUploadError:r.onFileUploadError,onFileUploadCanceled:r.onFileUploadCanceled,accessToken:l,apiUrl:this.apiUrl,file:r.file,appKey:this.context.appKey,to:d,chatType:"chatRoom"},i.jz.UPLOAD_CHATROOM_FILE),c.vF.debug("Call uploadChatRoomSharedFile",r)}function cr(r){if("string"!=typeof r.roomId||""===r.roomId)throw Error('Invalid parameter: "roomId"');if("string"!=typeof r.fileId||""===r.fileId)throw Error('Invalid parameter: "fileId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=t.jid,l=r.roomId,h=r.fileId,d={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatrooms/").concat(l,"/share_files/").concat(h,"?resource=").concat(m.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return c.vF.debug("Call deleteChatRoomSharedFile",r),a.RD.call(this,d,i.jz.DELETE_CHATROOM_FILE)}function ir(r){if("string"!=typeof r.roomId||""===r.roomId)throw Error('Invalid parameter: "roomId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=r.roomId,l={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatrooms/").concat(m,"/share_files"),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json",accept:"application/json"},success:r.success,error:r.error};return c.vF.debug("Call fetchChatRoomSharedFileList",r),a.RD.call(this,l,i.jz.GET_CHATROOM_FILES)}var sr=ir;function ur(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error("Invalid parameter chatRoomId: "+r.chatRoomId);if(r.attributeKeys&&!Array.isArray(r.attributeKeys))throw Error('"Invalid parameter attributeKeys": '+r.attributeKeys);var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=(t.jid,r.chatRoomId),l={keys:r.attributeKeys},h={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/metadata/chatroom/").concat(m),type:"POST",dataType:"json",data:JSON.stringify(l),headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"}};return c.vF.debug("Call getChatRoomAttributes:",r),a.RD.call(this,h,i.jz.GET_CHATROOM_ATTR).then((function(r){return{data:r.data,type:r.type}}))}function mr(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error("Invalid parameter chatRoomId: "+r.chatRoomId);if("object"!==s(r.attributes))throw Error("Invalid parameter attributes: "+r.attributes);var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,u=t.appName,m=t.accessToken,l=t.userId,h=r.chatRoomId,d=r.attributes,p=r.autoDelete,f=void 0===p||p,R=r.isForced?"/forced":"",v={metaData:d,autoDelete:f?"DELETE":"NO_DELETE"},y={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(u,"/metadata/chatroom/").concat(h,"/user/").concat(l)+R,type:"PUT",dataType:"json",data:JSON.stringify(v),headers:{Authorization:"Bearer "+m,"Content-Type":"application/json"}};return c.vF.debug("Call setChatRoomAttributes:",r),a.RD.call(this,y,i.jz.SET_CHATROOM_ATTR).then((function(r){return(0,n.fu)(r)}))}function lr(r){var e;if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error("Invalid parameter chatRoomId: "+r.chatRoomId);if("string"!=typeof r.attributeKey||""===r.attributeKey)throw Error("Invalid parameter attributeKey: "+r.attributeKey);if("string"!=typeof r.attributeValue||""===r.attributeValue)throw Error("Invalid parameter attributeValue: "+r.attributeValue);var t=n.dO.call(this).error;if(t)return Promise.reject(t);var o=this.context,s=o.orgName,u=o.appName,m=o.accessToken,l=o.userId,h=r.chatRoomId,d=r.attributeKey,p=r.attributeValue,f=r.autoDelete,R=void 0===f||f,v=r.isForced?"/forced":"",y={metaData:(e={},e[d]=p,e),autoDelete:R?"DELETE":"NO_DELETE"},I={url:"".concat(this.apiUrl,"/").concat(s,"/").concat(u,"/metadata/chatroom/").concat(h,"/user/").concat(l)+v,type:"PUT",dataType:"json",data:JSON.stringify(y),headers:{Authorization:"Bearer "+m,"Content-Type":"application/json"}};return c.vF.debug("Call setChatRoomAttribute:",r),a.RD.call(this,I,i.jz.SET_CHATROOM_ATTR).then((function(r){var e=(0,n.A4)(r);if(e)throw e}))}function hr(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error("Invalid parameter chatRoomId: "+r.chatRoomId);if(!Array.isArray(r.attributeKeys))throw Error('"Invalid parameter attributes": '+r.attributeKeys);var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=t.userId,l=r.chatRoomId,h=r.attributeKeys,d=r.isForced?"/forced":"",p={keys:h},f={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/metadata/chatroom/").concat(l,"/user/").concat(m)+d,type:"DELETE",dataType:"json",data:JSON.stringify(p),headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"}};return c.vF.debug("Call removeChatRoomAttributes:",r),a.RD.call(this,f,i.jz.DELETE_CHATROOM_ATTR).then((function(r){return(0,n.fu)(r)}))}function dr(r){if("string"!=typeof r.chatRoomId||""===r.chatRoomId)throw Error("Invalid parameter chatRoomId: "+r.chatRoomId);if("string"!=typeof r.attributeKey||""===r.attributeKey)throw Error('"Invalid parameter attributeKey": '+r.attributeKey);var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,m=t.userId,l=r.chatRoomId,h=r.attributeKey,d=r.isForced?"/forced":"",p={keys:[h]},f={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/metadata/chatroom/").concat(l,"/user/").concat(m)+d,type:"DELETE",dataType:"json",data:JSON.stringify(p),headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"}};return c.vF.debug("Call removeChatRoomAttribute:",r),a.RD.call(this,f,i.jz.DELETE_CHATROOM_ATTR).then((function(r){var e=(0,n.A4)(r);if(e)throw e}))}function pr(r){var e=this,t=r||{},o=t.pageNum,s=t.pageSize;if(isNaN(o)||o<=0)throw Error("Invalid parameter pageNum:".concat(o));if(isNaN(s)||s<=0)throw Error("Invalid parameter pageSize:".concat(s));var u=n.dO.call(this).error;if(u)return Promise.reject(u);var m={pagenum:o,pagesize:s,detail:!0},l=this.context,h=l.orgName,d=l.appName,p=l.accessToken,f={url:"".concat(this.apiUrl,"/").concat(h,"/").concat(d,"/users/").concat(this.user,"/joined_chatrooms"),dataType:"json",type:"GET",data:m,headers:{Authorization:"Bearer "+p,"Content-Type":"application/json"}};return c.vF.debug("Call getJoinedChatRooms",r),a.RD.call(this,f,i.jz.GET_USER_JOINED_CHATROOM).then((function(r){var t=(r.data||[]).map((function(r){var t=r.id,o=r.title,a=r.owner,n=r.created,c=r.description,i=r.max_users;return{id:t,name:o,owner:a.split("".concat(e.appKey,"_"))[1],created:n,description:c,maxusers:i}}));return{type:r.type,data:t}}))}},6682:function(r,e,t){var o=t(9565),a=t(8551),n=t(4901),c=t(2195),i=t(7323),s=TypeError;r.exports=function(r,e){var t=r.exec;if(n(t)){var u=o(t,r,e);return null!==u&&a(u),u}if("RegExp"===c(r))return o(i,r,e);throw new s("RegExp#exec called on incompatible receiver")}},7829:function(r,e,t){var o=t(8183).charAt;r.exports=function(r,e,t){return e+(t?o(r,e).length:1)}},8363:function(r,e,t){var o=t(9565),a=t(9504),n=t(9228),c=t(8551),i=t(34),s=t(7750),u=t(2293),m=t(7829),l=t(8014),h=t(655),d=t(5966),p=t(6682),f=t(8429),R=t(9039),v=f.UNSUPPORTED_Y,y=Math.min,I=a([].push),T=a("".slice),C=!R((function(){var r=/(?:)/,e=r.exec;r.exec=function(){return e.apply(this,arguments)};var t="ab".split(r);return 2!==t.length||"a"!==t[0]||"b"!==t[1]})),E="c"==="abbc".split(/(b)*/)[1]||4!=="test".split(/(?:)/,-1).length||2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length;n("split",(function(r,e,t){var a="0".split(void 0,0).length?function(r,t){return void 0===r&&0===t?[]:o(e,this,r,t)}:e;return[function(e,t){var n=s(this),c=i(e)?d(e,r):void 0;return c?o(c,e,n,t):o(a,h(n),e,t)},function(r,o){var n=c(this),i=h(r);if(!E){var s=t(a,n,i,o,a!==e);if(s.done)return s.value}var d=u(n,RegExp),f=n.unicode,R=(n.ignoreCase?"i":"")+(n.multiline?"m":"")+(n.unicode?"u":"")+(v?"g":"y"),C=new d(v?"^(?:"+n.source+")":n,R),g=void 0===o?4294967295:o>>>0;if(0===g)return[];if(0===i.length)return null===p(C,i)?[i]:[];for(var j=0,O=0,b=[];O<i.length;){C.lastIndex=v?0:O;var A,N=p(C,v?T(i,O):i);if(null===N||(A=y(l(C.lastIndex+(v?O:0)),i.length))===j)O=m(i,O,f);else{if(I(b,T(i,j,O)),b.length===g)return b;for(var M=1;M<=N.length-1;M++)if(I(b,N[M]),b.length===g)return b;O=j=A}}return I(b,T(i,j)),b}]}),E||!C,v)},9228:function(r,e,t){t(7495);var o=t(9565),a=t(6840),n=t(7323),c=t(9039),i=t(8227),s=t(6699),u=i("species"),m=RegExp.prototype;r.exports=function(r,e,t,l){var h=i(r),d=!c((function(){var e={};return e[h]=function(){return 7},7!==""[r](e)})),p=d&&!c((function(){var e=!1,t=/a/;return"split"===r&&((t={}).constructor={},t.constructor[u]=function(){return t},t.flags="",t[h]=/./[h]),t.exec=function(){return e=!0,null},t[h](""),!e}));if(!d||!p||t){var f=/./[h],R=e(h,""[r],(function(r,e,t,a,c){var i=e.exec;return i===n||i===m.exec?d&&!c?{done:!0,value:o(f,e,t,a)}:{done:!0,value:o(r,t,e,a)}:{done:!1}}));a(String.prototype,r,R[0]),a(m,h,R[1])}l&&s(m[h],"sham",!0)}}},function(r){return r(r.s=308)}])}));