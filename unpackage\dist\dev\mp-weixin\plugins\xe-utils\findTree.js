"use strict";
const plugins_xeUtils_helperCreateTreeFunc = require("./helperCreateTreeFunc.js");
function findTreeItem(parent, obj, iterate, context, path, node, parseChildren, opts) {
  if (obj) {
    var item, index, len, paths, nodes, match;
    for (index = 0, len = obj.length; index < len; index++) {
      item = obj[index];
      paths = path.concat(["" + index]);
      nodes = node.concat([item]);
      if (iterate.call(context, item, index, obj, paths, parent, nodes)) {
        return { index, item, path: paths, items: obj, parent, nodes };
      }
      if (parseChildren && item) {
        match = findTreeItem(item, item[parseChildren], iterate, context, paths.concat([parseChildren]), nodes, parseChildren);
        if (match) {
          return match;
        }
      }
    }
  }
}
var findTree = plugins_xeUtils_helperCreateTreeFunc.helperCreateTreeFunc(findTreeItem);
exports.findTree = findTree;
