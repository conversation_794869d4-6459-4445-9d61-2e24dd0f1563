/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*
	字体颜色相关
*/
/*
	背景色 分割线色
*/
/*
	主题色
*/
/*
		渐变可调角度
*/
/*
	定义弹性布局
 */
/*
	单行文本溢出隐藏
*/
.search-p.data-v-1cf27b2a {
  background: linear-gradient(90deg, #676bdf 0%, #8983e4 100%);
  padding: 32rpx 28rpx;
}
page.data-v-1cf27b2a {
  background: #f5f5f5;
}
.home_img.data-v-1cf27b2a {
  width: 100%;
  height: 130rpx;
  vertical-align: top;
}
.home.data-v-1cf27b2a {
  background: #ffffff;
  border-radius: 32rpx 32rpx 0 0;
  /* 消息列表卡片样式 */
  /* AI助手样式 */
}
.home .search.data-v-1cf27b2a {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 60rpx;
  border-radius: 44rpx;
  background-color: #fff;
  padding: 0 36rpx;
  font-size: 32rpx;
  color: #999;
  border: 2rpx solid #f5f5f5;
  background: rgba(255, 255, 255, 0.8);
}
.home .search image.data-v-1cf27b2a {
  width: 44rpx;
  height: 44rpx;
  margin-right: 18rpx;
}
.home .message-card.data-v-1cf27b2a {
  background: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  margin-top: 24rpx;
  margin-bottom: 24rpx;
  padding: 24rpx;
  overflow: hidden;
}
.home .message-card-header.data-v-1cf27b2a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.home .message-card-title.data-v-1cf27b2a {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}
.home .message-icon.data-v-1cf27b2a {
  width: 36rpx;
  height: 36rpx;
  margin-right: 8rpx;
}
.home .message-card-more.data-v-1cf27b2a {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #874ff0;
}
.home .message-list-scroll.data-v-1cf27b2a {
  max-height: 320rpx;
  overflow: auto;
}
.home .message-item.data-v-1cf27b2a {
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.home .message-item.data-v-1cf27b2a:last-child {
  border-bottom: none;
}
.home .message-item-title.data-v-1cf27b2a {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.home .message-tag.data-v-1cf27b2a {
  background: rgba(131, 106, 255, 0.1);
  color: #836AFF;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
  margin-right: 8rpx;
}
.home .message-title-text.data-v-1cf27b2a {
  font-size: 28rpx;
  color: #333333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}
.home .message-item-time.data-v-1cf27b2a {
  font-size: 24rpx;
  color: #999999;
  text-align: right;
}
.home .message-empty.data-v-1cf27b2a {
  padding: 30rpx 0;
  text-align: center;
  color: #999999;
  font-size: 28rpx;
}
.home .card.data-v-1cf27b2a {
  margin-top: 24rpx;
}
.home .card_more.data-v-1cf27b2a {
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #999;
  margin-top: 20rpx;
}
.home .doc_list.data-v-1cf27b2a {
  margin-top: 24rpx;
}
.home .ai-assistant.data-v-1cf27b2a {
  margin-top: 30rpx;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.home .ai-assistant-content.data-v-1cf27b2a {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.home .ai-title.data-v-1cf27b2a {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}
.home .zhe.data-v-1cf27b2a {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-direction: column;
}
.home .zhe .pop.data-v-1cf27b2a {
  width: 100vw;
  background-color: #f5f5f5;
  max-height: 70vh;
  overflow-y: scroll;
  padding: 0 0 120rpx;
  border-radius: 32rpx 32rpx 0 0;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.home .zhe .pop .warp.data-v-1cf27b2a {
  padding: 0 32rpx;
  width: 100%;
  box-sizing: border-box;
  flex: 1;
  overflow-y: scroll;
}
.home .zhe .pop .warp .card.data-v-1cf27b2a {
  margin-top: 0rpx;
}
.home .zhe .pop .fiexd.data-v-1cf27b2a {
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  z-index: 1;
  flex: none;
}
.home_position.data-v-1cf27b2a {
  position: absolute;
  top: 90rpx;
}