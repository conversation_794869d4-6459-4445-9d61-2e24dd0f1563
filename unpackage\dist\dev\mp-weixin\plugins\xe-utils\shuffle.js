"use strict";
const plugins_xeUtils_random = require("./random.js");
const plugins_xeUtils_values = require("./values.js");
function shuffle(array) {
  var index;
  var result = [];
  var list = plugins_xeUtils_values.values(array);
  var len = list.length - 1;
  for (; len >= 0; len--) {
    index = len > 0 ? plugins_xeUtils_random.random(0, len) : 0;
    result.push(list[index]);
    list.splice(index, 1);
  }
  return result;
}
exports.shuffle = shuffle;
