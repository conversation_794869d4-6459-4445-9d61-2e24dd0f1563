"use strict";
const plugins_xeUtils_isDate = require("./isDate.js");
const plugins_xeUtils_toStringDate = require("./toStringDate.js");
const plugins_xeUtils_helperNewDate = require("./helperNewDate.js");
function isLeapYear(date) {
  var year;
  var currentDate = date ? plugins_xeUtils_toStringDate.toStringDate(date) : plugins_xeUtils_helperNewDate.helperNewDate();
  if (plugins_xeUtils_isDate.isDate(currentDate)) {
    year = currentDate.getFullYear();
    return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);
  }
  return false;
}
exports.isLeapYear = isLeapYear;
