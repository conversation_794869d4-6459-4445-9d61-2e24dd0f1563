"use strict";
const plugins_xeUtils_staticParseInt = require("./staticParseInt.js");
const plugins_xeUtils_helperGetUTCDateTime = require("./helperGetUTCDateTime.js");
const plugins_xeUtils_helperGetDateTime = require("./helperGetDateTime.js");
const plugins_xeUtils_isString = require("./isString.js");
const plugins_xeUtils_isDate = require("./isDate.js");
function getParseRule(txt) {
  return "(\\d{" + txt + "})";
}
function toParseMs(num) {
  if (num < 10) {
    return num * 100;
  } else if (num < 100) {
    return num * 10;
  }
  return num;
}
function toParseNum(num) {
  return isNaN(num) ? num : plugins_xeUtils_staticParseInt.staticParseInt(num);
}
var d2 = getParseRule(2);
var d1or2 = getParseRule("1,2");
var d1or7 = getParseRule("1,7");
var d3or4 = getParseRule("3,4");
var place = ".{1}";
var d1Or2RE = place + d1or2;
var dzZ = "(([zZ])|([-+]\\d{2}:?\\d{2}))";
var defaulParseStrs = [d3or4, d1Or2RE, d1Or2RE, d1Or2RE, d1Or2RE, d1Or2RE, place + d1or7, dzZ];
var defaulParseREs = [];
for (var len = defaulParseStrs.length - 1; len >= 0; len--) {
  var rule = "";
  for (var i = 0; i < len + 1; i++) {
    rule += defaulParseStrs[i];
  }
  defaulParseREs.push(new RegExp("^" + rule + "$"));
}
function parseDefaultRules(str) {
  var matchRest, resMaps = {};
  for (var i = 0, dfrLen = defaulParseREs.length; i < dfrLen; i++) {
    matchRest = str.match(defaulParseREs[i]);
    if (matchRest) {
      resMaps.y = matchRest[1];
      resMaps.M = matchRest[2];
      resMaps.d = matchRest[3];
      resMaps.H = matchRest[4];
      resMaps.m = matchRest[5];
      resMaps.s = matchRest[6];
      resMaps.S = matchRest[7];
      resMaps.Z = matchRest[8];
      break;
    }
  }
  return resMaps;
}
var customParseStrs = [
  ["yyyy", d3or4],
  ["yy", d2],
  ["MM", d2],
  ["M", d1or2],
  ["dd", d2],
  ["d", d1or2],
  ["HH", d2],
  ["H", d1or2],
  ["mm", d2],
  ["m", d1or2],
  ["ss", d2],
  ["s", d1or2],
  ["SSS", getParseRule(3)],
  ["S", d1or7],
  ["Z", dzZ]
];
var parseRuleMaps = {};
var parseRuleKeys = ["\\[([^\\]]+)\\]"];
for (var i = 0; i < customParseStrs.length; i++) {
  var itemRule = customParseStrs[i];
  parseRuleMaps[itemRule[0]] = itemRule[1] + "?";
  parseRuleKeys.push(itemRule[0]);
}
var customParseRes = new RegExp(parseRuleKeys.join("|"), "g");
var cacheFormatMaps = {};
function parseCustomRules(str, format) {
  var cacheItem = cacheFormatMaps[format];
  if (!cacheItem) {
    var posIndexs = [];
    var re = format.replace(/([$(){}*+.?\\^|])/g, "\\$1").replace(customParseRes, function(text, val) {
      var firstChar = text.charAt(0);
      if (firstChar === "[") {
        return val;
      }
      posIndexs.push(firstChar);
      return parseRuleMaps[text];
    });
    cacheItem = cacheFormatMaps[format] = {
      _i: posIndexs,
      _r: new RegExp(re)
    };
  }
  var resMaps = {};
  var matchRest = str.match(cacheItem._r);
  if (matchRest) {
    var _i = cacheItem._i;
    for (var i = 1, len = matchRest.length; i < len; i++) {
      resMaps[_i[i - 1]] = matchRest[i];
    }
    return resMaps;
  }
  return resMaps;
}
function parseTimeZone(resMaps) {
  if (/^[zZ]/.test(resMaps.Z)) {
    return new Date(plugins_xeUtils_helperGetUTCDateTime.helperGetUTCDateTime(resMaps));
  } else {
    var matchRest = resMaps.Z.match(/([-+])(\d{2}):?(\d{2})/);
    if (matchRest) {
      return new Date(plugins_xeUtils_helperGetUTCDateTime.helperGetUTCDateTime(resMaps) - (matchRest[1] === "-" ? -1 : 1) * plugins_xeUtils_staticParseInt.staticParseInt(matchRest[2]) * 36e5 + plugins_xeUtils_staticParseInt.staticParseInt(matchRest[3]) * 6e4);
    }
  }
  return /* @__PURE__ */ new Date("");
}
function toStringDate(str, format) {
  if (str) {
    var isDType = plugins_xeUtils_isDate.isDate(str);
    if (isDType || !format && /^[0-9]{11,15}$/.test(str)) {
      return new Date(isDType ? plugins_xeUtils_helperGetDateTime.helperGetDateTime(str) : plugins_xeUtils_staticParseInt.staticParseInt(str));
    }
    if (plugins_xeUtils_isString.isString(str)) {
      var resMaps = format ? parseCustomRules(str, format) : parseDefaultRules(str);
      if (resMaps.y) {
        if (resMaps.M) {
          resMaps.M = toParseNum(resMaps.M) - 1;
        }
        if (resMaps.S) {
          resMaps.S = toParseMs(toParseNum(resMaps.S.substring(0, 3)));
        }
        if (resMaps.Z) {
          return parseTimeZone(resMaps);
        } else {
          return new Date(resMaps.y, resMaps.M || 0, resMaps.d || 1, resMaps.H || 0, resMaps.m || 0, resMaps.s || 0, resMaps.S || 0);
        }
      }
    }
  }
  return /* @__PURE__ */ new Date("");
}
exports.toStringDate = toStringDate;
