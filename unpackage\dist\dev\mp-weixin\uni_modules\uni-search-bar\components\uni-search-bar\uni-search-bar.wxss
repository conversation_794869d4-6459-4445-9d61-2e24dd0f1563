/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*
	字体颜色相关
*/
/*
	背景色 分割线色
*/
/*
	主题色
*/
/*
		渐变可调角度
*/
/*
	定义弹性布局
 */
/*
	单行文本溢出隐藏
*/
.uni-searchbar {
  display: flex;
  flex-direction: row;
  position: relative;
  padding: 10px;
}
.uni-searchbar__box {
  display: flex;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
  flex: 1;
  justify-content: center;
  flex-direction: row;
  align-items: center;
  height: 36px;
  padding: 5px 8px 5px 0px;
}
.uni-searchbar__box-icon-search {
  display: flex;
  flex-direction: row;
  padding: 0 8px;
  justify-content: center;
  align-items: center;
  color: #B3B3B3;
}
.uni-searchbar__box-search-input {
  flex: 1;
  font-size: 14px;
  color: #333;
}
.uni-searchbar__box-icon-clear {
  align-items: center;
  line-height: 24px;
  padding-left: 8px;
}
.uni-searchbar__text-placeholder {
  font-size: 14px;
  color: #B3B3B3;
  margin-left: 5px;
}
.uni-searchbar__cancel {
  padding-left: 10px;
  line-height: 36px;
  font-size: 14px;
  color: #333333;
}