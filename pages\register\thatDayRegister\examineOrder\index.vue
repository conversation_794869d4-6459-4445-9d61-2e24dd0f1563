<template>
  <!-- 支付结果 -->
  <view>
    <view class="page-container">
      <!-- 医生信息 -->
      <Docter :infoDetail="infoDetail" />

      <!-- 详情 -->
      <view class="bg_wh">
        <view class="person_info">
          <text>就诊人</text>
          <text>{{ orderDetail.patientName }}</text>
        </view>
        <view class="person_info">
          <text>问诊类型</text>
          <text>{{ orderDetail.visitTypeName }}</text>
        </view>
        <view class="person_info">
          <text>服务类型</text>
          <text>{{ orderDetail.isSubsequent == 0 ? '咨询' : '复诊' }}</text>
        </view>
        <!-- 问诊类型 时间 -->
        <view class="person_info">
          <text>{{
            orderDetail.visitTypeCode == 1 ? '问诊时间' : '预约时间'
          }}</text>
          <text>{{ orderDetail.inquiryTime }}</text>
        </view>
      </view>

      <view class="bg_wh particulars">
        <view class="title_fw"
          >费用明细（{{
            orderDetail.isMedicare == '1' ? '医保' : '自费'
          }}）</view
        >
        <template v-for="item in orderDetail.payList">
          <view class="left_right">
            <text>{{ item.priceDetailName }}</text>
            <text>{{ item.totalPay }}</text>
          </view>
        </template>
        <view style="text-align: right; padding-top: 40rpx">
          合计：<text style="color: #ff0707; font-weight: 500">{{
            orderDetail.totalPay
          }}</text>
        </view>
<!--        <view class="title_fw">医保担负明细</view>-->
<!--        <view class="left_right">-->
<!--          <text>医保统筹支付</text>-->
<!--          <text>0</text>-->
<!--        </view>-->
<!--        <view class="left_right">-->
<!--          <text>医保账户支付</text>-->
<!--          <text>0</text>-->
<!--        </view>-->
        <view class="title_fw">优惠券抵扣明细</view>
        <view class="left_right">
          <text>新用户立减</text>
          <text>0</text>
        </view>
      </view>
    </view>

    <FooterButton @click="goHome"> 返回主页 </FooterButton>
  </view>
</template>

<script>
import Docter from '@/components/doctor_header/doctor_header.vue';
import FooterButton from '@/components/footer_button/button.vue';
import { findDoctorByID, getRegisterOrderDetail } from '@/api/base.js';

import myJsTools from '@/common/js/myJsTools.js';
export default {
  components: {
    Docter,
    FooterButton,
  },
  data() {
    return {
      orderQueryInfo: '', // 上页传入的参数
      info: '',
      infoDetail: {},
      docDetail: {}, // 医生信息
      orderDetail: {},
    };
  },
  onLoad(option) {
    this.orderQueryInfo = JSON.parse(option.orderQueryInfo);
    this.info = uni.getStorageSync('registration');
    this.infoDetail = uni.getStorageSync('infoDetail');
    this.getfindDoctorByID();
    this.getRegisterOrderDetailFun();
  },
  methods: {
    // 返回首页
    goHome() {
      uni.switchTab({
        url: '/pages/index/index',
      });
    },
    // 获取医生信息
    async getfindDoctorByID() {
      let { docId, deptId } = this.orderQueryInfo;
      let para = {
        docId,
        deptId,
      };
      let res = await findDoctorByID(para);
      if (res.code != 20000) return;
      let data = res.data;
      this.docDetail = data;
      if (data.docImg) {
        myJsTools.downAndSaveImg(data.docImg, (url) => {
          this.docDetail.docImg = url;
        });
      }
    },

    // 获取订单详情
    async getRegisterOrderDetailFun() {
      let res = await getRegisterOrderDetail({
        regId: this.orderQueryInfo.regId,
      });
      this.orderDetail = res.data;
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  padding: 24rpx 32rpx 120rpx;
  box-sizing: border-box;
  overflow-y: auto;
  background-color: #f5f5f5;

  .doctor_box_top {
    padding: 28upx 24upx;
    border-radius: 16upx;
  }
}

/* 主体内容 */
.bg_wh {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 0 24rpx;
  margin-top: 24rpx;
  font-size: 28rpx;
}

.bg_wh:first-child {
  margin-top: 0;
}

/* 问诊信息 */
.patient_box {
  margin-top: 40rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: 0 32rpx;
}

.person_info {
  height: 92rpx;
  border-bottom: 1px solid #ebebeb;
  box-sizing: border-box;
  color: #333333;
  font-size: 28rpx;
  display: flex;
  align-items: center;
}

.person_info text:first-child {
  width: 180rpx;
}

.person_info:last-child {
  border-bottom: none;
}

/* 费用明细 */
.particulars {
  /* padding-top: 26rpx; */
  padding-bottom: 26rpx;
}

.title_fw {
  color: #333333;
  font-weight: 600;
  padding-top: 26rpx;
}

.left_right {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #999999;
  padding-top: 40rpx;
}

.left_right text:last-child {
  color: #333333;
}
</style>
