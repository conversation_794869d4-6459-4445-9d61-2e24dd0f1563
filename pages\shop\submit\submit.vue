<template>
  <!-- 提交订单 -->
  <view class="order_submit">
    <!-- 选择患者 -->
    <view class="sele_warp">
      <view class="sele_but" @click="toPatient" v-show="!patientInfo">
        <text>请选择就诊人</text>
        <uni-icons type="arrowright" color="#333" size="20"></uni-icons>
      </view>

      <!-- 患者信息 -->
      <view class="user_info" v-show="patientInfo" @click="toPatient">
        <!-- 头像 -->
        <img
          v-img="patientInfo.patientImg"
          v-if="patientInfo.patientImg"
          data-src="/static/images/docHead.png"
          alt=""
          class="user_head"
        />
        <image
          src="/static/images/docHead.png"
          v-else
          alt=""
          class="user_head"
        />
        <!-- 信息 -->
        <view class="user_desc">
          <view class="user_name">{{ patientInfo.patientName }}</view>
          <view class="user_other">
            <image
              v-show="patientInfo.sexCode == 1"
              src="/static/shop/nan.png"
            ></image>
            <image
              v-show="patientInfo.sexCode == 2"
              src="/static/shop/nv.png"
            ></image>
            <text>{{ patientInfo.age }}岁</text>
          </view>
        </view>
        <!-- 箭头 -->
        <uni-icons type="arrowright" color="#333" size="20"></uni-icons>
      </view>

      <view class="sele_info">
        <text>提示</text>
        <view class="info_text">请选择需使用药品的患者</view>
      </view>
    </view>

    <!-- 上传照片 -->
    <view class="sele_warp">
      <view
        class="sele_but"
        :class="{ act: fileList.length }"
        @click="cloosImgTop"
      >
        <text>请上传处方及病历照片</text>
        <uni-icons type="arrowright" color="#333" size="20"></uni-icons>
      </view>

      <!-- 图片 -->
      <view class="img_list" v-show="fileList.length">
        <view class="img_item" v-for="(item, index) in fileList" :key="index">
          <image
            :src="item.url"
            @click="preview(item)"
            mode="aspectFit"
            class="img"
          ></image>
          <!-- 删除按钮 -->
          <view class="del" @click="delFile(index)">
            <uni-icons type="close" color="#fff" size="24"></uni-icons>
          </view>
        </view>
        <!-- 上传 -->
        <view class="img_item" @click="cloosImg" v-if="fileList.length < 3">
          <text>+</text>
          <text>上传图片</text>
        </view>
      </view>

      <view class="sele_info">
        <text>提示</text>
        <view class="info_text"
          >请上传处方，平台药师审核或上传病历诊断页，由互联网医院为您开具处方</view
        >
      </view>
    </view>

    <!-- 药品药品 -->
    <DRUGLIST
      :isQuick="false"
      @logist="logist"
      :list="orderList"
      @showTip="showTips"
    />

    <!-- 选择配送 单药店 可选择自提或物流 -->
    <view class="sele_warp" v-if="isOnly">
      <view class="sele_but" @click="seleType">
        <text @click.stop="setTip"
          >请选择配送方式
          <uni-icons
            type="help"
            color="#333"
            v-if="delivery > -1"
            size="14"
          ></uni-icons>
        </text>
        <view class="right">
          <text v-if="delivery == 0">统一配送</text>
          <text v-if="delivery == 1">自提</text>
          <text v-if="delivery == 2">同城配送</text>
          <uni-icons type="arrowright" color="#333" size="20"></uni-icons>
        </view>
      </view>

      <!-- 物流选择 -->
      <view class="logistics" v-if="delivery == 0">
        <view class="item" @click="setLogistType(0)">
          <text class="name">普通快递</text>
          <view class="right">
            <text>￥{{ orderList[0].ordinaryLogMoney | toFixed }}</text>
            <image
              src="/static/shop/sele_act.png"
              v-if="logisticsType == 0"
            ></image>
            <image src="/static/shop/sele.png" v-else></image>
          </view>
        </view>

        <!--        <view class="item" @click="setLogistType(1)">-->
        <!--          <text class="name">顺丰快递</text>-->
        <!--          <view class="right">-->
        <!--            <text>￥{{ orderList[0].specialLogMoney | toFixed }}</text>-->
        <!--            <image-->
        <!--              src="/static/shop/sele_act.png"-->
        <!--              v-if="logisticsType == 1"-->
        <!--            ></image>-->
        <!--            <image src="/static/shop/sele.png" v-else></image>-->
        <!--          </view>-->
        <!--        </view>-->
      </view>

      <!-- 配送 -->
      <ADDRESS
        v-if="delivery == 0 && address"
        :detail="address"
        @click="toAddress"
      />

      <!-- 自提 -->
      <PHARMACY v-if="delivery == 1 && drugDetail" :detail="drugDetail" />

      <view class="sele_info">
        <text>提示</text>
        <view class="info_text"
          >不同的配送方式，价格会有差异，请以实际支付为标准</view
        >
      </view>
    </view>

    <!-- 多药店 只能物流 -->
    <view class="sele_warp" v-if="isZY">
      <view class="sele_but">
        <text @click.stop="showTips('wl')"
          >请选择配送方式
          <uni-icons type="help" color="#333" size="14"></uni-icons>
        </text>
        <view class="right"></view>
      </view>

      <!-- 物流选择 -->
      <view class="logistics">
        <view class="item" @click="setLogistType(0)">
          <text class="name">普通快递</text>
          <view class="right">
            <text>￥{{ orderList[0].ordinaryLogMoney | toFixed }}</text>
            <image
              src="/static/shop/sele_act.png"
              v-if="logisticsType == 0"
            ></image>
            <image src="/static/shop/sele.png" v-else></image>
          </view>
        </view>

<!--        <view class="item" @click="setLogistType(1)">-->
<!--          <text class="name">顺丰快递</text>-->
<!--          <view class="right">-->
<!--            <text>￥{{ orderList[0].specialLogMoney | toFixed }}</text>-->
<!--            <image-->
<!--              src="/static/shop/sele_act.png"-->
<!--              v-if="logisticsType == 1"-->
<!--            ></image>-->
<!--            <image src="/static/shop/sele.png" v-else></image>-->
<!--          </view>-->
<!--        </view>-->
      </view>
    </view>

    <!-- 选择地址 -->
    <view class="sele_warp" v-if="!isOnly">
      <view class="sele_but" @click="toAddress" v-if="!address">
        <text>请选择地址</text>
        <view class="right">
          <uni-icons type="arrowright" color="#333" size="20"></uni-icons>
        </view>
      </view>

      <!-- 配送 -->
      <ADDRESS v-if="address" :detail="address" @click="toAddress" />

      <view class="sele_info">
        <text>提示</text>
        <view class="info_text"
          >不同的配送方式，价格会有差异，请以实际支付为标准</view
        >
      </view>
    </view>

    <!-- 调剂费 -->
<!--    <view class="sele_warp">-->
<!--      <view class="sele_but" @click="showTips('tjf')">-->
<!--        <text>处方调剂费</text>-->
<!--        <view class="right">-->
<!--          <text class="name">￥{{ adjustRealyMoney | toFixed }}</text>-->
<!--          <uni-icons type="arrowright" color="#333" size="20"></uni-icons>-->
<!--        </view>-->
<!--      </view>-->
<!--      <view class="sele_info">-->
<!--        <text class="red">平台优惠：{{ adjustDeMoney | toFixed }}</text>-->
<!--      </view>-->
<!--    </view>-->

    <!-- 底部 -->
    <view class="footer">
      <view class="count">
        合计：<text>￥{{ total | toFixed }}</text>
      </view>
      <view class="right">
        <!-- 按钮 -->
        <!--        <view class="but pay" @click="send(1)">好友付</view>-->
        <view class="but" @click="send(0)">去支付</view>
      </view>
    </view>
    <uni-popup ref="kcPopup" background-color="#fff" type="center">
      <view class="kcPopup">
        <view class="kcPopup-title">温馨提示</view>
        <view class="kcPopup-content">
          <view style="margin-bottom: 10px"
            >以下药品库存不足，请您检查后再次提交~</view
          >
          <view class="kcPopup-content-box">
            <view
              class="kcPopup-content-item"
              v-for="(item, index) in storeKcNo"
              :key="index"
            >
              <img
                class="kcPopup-content-item-img"
                :src="item.drugImageUrl || '/static/shop/drug.png'"
              ></img>
              <view class="item-title"
                >仅剩{{ Math.floor(item.drugKc * 1) }}件</view
              >
            </view>
          </view>
        </view>
        <view class="kcPopup-content-btn" @click="closeKcPopup">我知道了</view>
      </view>
    </uni-popup>
    <!-- 弹框 -->
    <uni-popup ref="popup" type="bottom">
      <view class="pop">
        <view class="pop_title">
          <text>{{ tip.tipsTitle }}</text>
          <uni-icons type="closeempty" @click="hideTip"></uni-icons>
        </view>
        <view class="pop_text">
          {{ tip.tipsContent }}
        </view>
      </view>
    </uni-popup>

    <!-- 支付提示 -->
    <PROPCENTER
      v-if="showPayTip"
      :type="2"
      @cancelPropCenter="showPayTip = false"
      @confirmPropCenter="setParam"
    >
      <view class="pay_tip">
        <view class="title">{{ payTip.tipsTitle }}</view>
        <view class="cont" v-html="payTip.tipsContent"></view>
      </view>
    </PROPCENTER>

    <!-- 引导 -->
    <Tips v-show="showTip" @click="showTip = false" />
  </view>
</template>

<script>
import { getSysPlatformConfigByKeyList } from "@/api/base.js";
import {
  queryOrderByShoppingCartFastOnline,
  queryPrescriptionAdjustMoney,
  querySelfExtractionTips,
  queryToPayTips,
  queryLogisticsTips,
  createMallOrder,
  queryMallOrderStatus,
  getCurrentCAType,
} from "@/api/shop.js";
import { uploadImg } from "@/api/oss.js";
import { findDrugStoreDetail } from "@/api/base";
import myJsTools from "@/common/js/myJsTools.js";
import DRUGLIST from "../components/pharmacyDrug.vue";
import PHARMACY from "../components/pharmacy.vue";
import ADDRESS from "../components/address.vue";
import PROPCENTER from "@/components/propCenter/propCenter.vue";
import { Toast } from "@/common/js/pay.js";
import payment from "@/mixins/wx";
import { getHelpPayCreateResult } from "../../../api/order";

let timer = null;

export default {
  name: "Submit",
  mixins: [payment],
  components: {
    PHARMACY,
    DRUGLIST,
    ADDRESS,
    PROPCENTER,
  },
  data() {
    return {
      // 显示支付提示
      showPayTip: false,
      // 是否只有一个药店
      isOnly: false,
      // 患者信息
      patientInfo: "",
      // 调剂费
      adjustDeMoney: 0,
      // 实际调剂费
      adjustRealyMoney: 0,
      // 处方总金额
      totalMoney: 0,
      // 药店信息
      drugDetail: null,
      // 自提地址
      address: null,
      // 快递类型
      logisticsType: 0,
      // 订单列表
      orderList: [],
      // 处方调剂费提示信息
      adjustMoneyTip: "",
      // 支付提示
      payTip: "",
      // 物流提示
      logisticsTip: "",
      // 自提提示
      ztTip: "",
      // 物流费用
      logist_count: 0,
      // 要提示的消息
      tip: "",
      // 图片数组
      fileList: [],
      // 配送方式 0 快递 1 自提
      delivery: -1,
      // 是否自营
      isZY: false,
      // 查询次数
      num: 3,
      // 支付方式
      payList: [],
      // 优惠前金额
      old: 0,
      // 是否代付
      isHelpPay: 0,
      // 是否继续
      isNext: true,
      orderNo: "",
      storeKcNo: [],
      isOFF: "",
    };
  },
  computed: {
    // 费用总计
    total() {
      // 如果只有一个药店 并且选择快递
      if (this.isOnly && this.delivery == 0) {
        let n =
          Number(this.totalMoney) * 100 +
          Number(this.adjustRealyMoney) * 100 +
          Number(this.logist_count) * 100;
        return (n / 100).toFixed(2);
      }
      // 单药店 自提 无需物流费
      if (this.isOnly && this.delivery != 0) {
        let n =
          Number(this.totalMoney) * 100 + Number(this.adjustRealyMoney) * 100;
        return (n / 100).toFixed(2);
      }
      // 多药店 只能物流
      if (!this.isOnly) {
        let n =
          Number(this.totalMoney) * 100 +
          Number(this.adjustRealyMoney) * 100 +
          Number(this.logist_count) * 100;
        return (n / 100).toFixed(2);
      }
    },
  },
  onLoad(v) {
    // 药店列表
    let ids = JSON.parse(v.list);
    uni.removeStorageSync("shop_patient");
    uni.removeStorageSync("shop_address");
    this.getSystem();
    this.isOff=v.isOFF
    this.getDetail(ids);
    this.getAdjustMoney();
    this.getLogisTip();
    this.getPayTip();
    this.getZtTip();
  },
  onShow() {
    let patientInfo = uni.getStorageSync("shop_patient");
    let address = uni.getStorageSync("shop_address");
    if (patientInfo) {
      this.patientInfo = patientInfo;
    }
    if (address) {
      this.address = address;
    }
  },
  methods: {
    closeKcPopup(){
      this.$refs.kcPopup.close()
    },
    // 获取系统配置
    async getSystem() {
      let res = await getSysPlatformConfigByKeyList(["onlineMallPayId"]);
      let callId = res.data[0].configValue;
      // 收款方
      this.payList = callId.split(",");
    },
    // 获取详情
    async getDetail(list) {
      let res = await queryOrderByShoppingCartFastOnline(list,this.isOff);
      const { adjustDeMoney, adjustRealyMoney, orderList } = res.data;
      this.adjustDeMoney = Number(adjustDeMoney);
      this.adjustRealyMoney = Number(adjustRealyMoney);
      let p = 0;
      let old = 0;
      if(this.isOff==1){
        orderList.forEach(v=>{
          v.drugStoreList.forEach(a=>{
            a.shoppingCartList=a.shoppingCartList.filter(q=>q.otc!=0)
          })
        })
      }
      orderList.forEach((v) => {
        p += Number(v.totalRealyMoney) * 100;
        old += Number(v.totalMoney) * 100;
        // 默认普通快递
        v.sele_delivery = 0;
      });
      // 只有一条数据
      if (orderList.length == 1) {
        // 且只有一个药店
        if (orderList[0].drugStoreList.length == 1) {
          let id = orderList[0].drugStoreList[0].drugStoreID;
          this.isOnly = true;
          await this.getDurgDetail(id);
        } else {
          // 一条数据 多个药店 为自营
          this.isZY = true;
        }
      }
      this.totalMoney = p / 100;
      this.old = old / 100;


      this.orderList = orderList;
      this.logistCount();
    },
    // 处方调剂费提示
    async getAdjustMoney() {
      let res = await queryPrescriptionAdjustMoney();
      this.adjustMoneyTip = res.data;
    },
    // 物流提示
    async getLogisTip() {
      let res = await queryLogisticsTips();
      this.logisticsTip = res.data;
    },
    // 自提提示
    async getZtTip() {
      let res = await querySelfExtractionTips();
      this.ztTip = res.data;
    },
    // 获取支付提示
    async getPayTip() {
      let res = await queryToPayTips();
      this.payTip = res.data;
    },
    // 获取药店详情
    async getDurgDetail(drugstoreId) {
      let res = await findDrugStoreDetail({
        drugstoreId,
      });
      this.drugDetail = res.data;
    },
    // 单药店物流自提提示
    setTip() {
      if (this.delivery == 1) {
        this.showTips("zt");
        return;
      }
      if (this.delivery == 0) {
        this.showTips("wl");
        return;
      }
    },
    // 单药店选择配送物流
    setLogistType(n) {
      if (!this.isNext) return;
      this.logisticsType = n;
      let item = this.orderList[0];
      item.sele_delivery = n;
      this.$set(this.orderList, 0, item);
      this.logistCount();
    },
    // 图片预览
    preview(item) {
      uni.previewImage({
        urls: [item.url],
      });
    },
    // 监听选择物流方式
    logist(index, n) {
      if (!this.isNext) return;
      let item = this.orderList[index];
      item.sele_delivery = n;
      // 改变物流方式
      this.$set(this.orderList, index, item);
      this.logistCount();
    },
    // 计算已选择物流费用
    logistCount() {
      let n = 0;
      this.orderList.forEach((v) => {
        // 普通
        if (v.sele_delivery == 0) {
          n += Number(v.ordinaryLogMoney) * 100;
        }
        // 其他物流
        if (v.sele_delivery == 1) {
          n += Number(v.specialLogMoney) * 100;
        }
      });
      this.logist_count = n / 100;
    },
    // 选择配送方式
    seleType() {
      if (!this.isNext) return;
      let that = this;
      let typeList=[]
      if (this.drugDetail.isCandelivery == null && this.drugDetail.isCanself == null) {
        typeList = [];
      } else {
        if (this.drugDetail.isCandelivery == "1" && this.drugDetail.isCanself == "0") {
          typeList = ["统一配送"];
        } else if (
            this.drugDetail.isCanself == "1" &&
            this.drugDetail.isCandelivery == "0"
        ) {
          typeList = ["自提"];
        } else if (
            this.drugDetail.isCanself == "1" &&
            this.drugDetail.isCandelivery == "1"
        ) {
          typeList = ["统一配送", "自提"];
        }
        
        // 添加同城配送选项
        if (this.drugDetail.isCanintracity == "1") {
          typeList.push("同城配送");
        }
      }
      uni.showActionSheet({
        itemList:typeList|| ["统一配送", "自提"],
        success(res) {
          if (res.tapIndex == 0) {
            if(typeList[0]=='统一配送'){
              that.toAddress();
              that.delivery = 0;
            }
            if(typeList[0]=='自提'){
              that.delivery = 1;
            }
            if(typeList[0]=='同城配送'){
              that.delivery = 2;
            }
          } else if (res.tapIndex == 1) {
            if(typeList[1]=='自提'){
              that.delivery = 1;
            }
            if(typeList[1]=='同城配送'){
              that.delivery = 2;
            }
          } else if (res.tapIndex == 2) {
            that.toAddress();
            that.delivery = 2;  // 同城配送
          }
        },
      });
    },
    // 去选择收货地址
    toAddress() {
      if (!this.isNext) return;
      // 选择快递地址
      uni.navigateTo({
        url: "/pages/address/index?action=shop",
      });
    },
    // 提示消息
    showTips(str) {
      if (str == "wl") {
        this.tip = this.logisticsTip;
      }
      if (str == "tjf") {
        this.tip = this.adjustMoneyTip;
      }
      if (str == "zt") {
        this.tip = this.ztTip;
      }
      this.$refs.popup.open();
    },
    // 隐藏提示
    hideTip() {
      this.$refs.popup.close();
    },
    // 选择患者
    toPatient() {
      if (!this.isNext) return;
      uni.navigateTo({
        url: "/pages/personalCenter/patientManage/index?action=shop",
      });
    },
    // 上传图片
    async upImg(obj) {
      let para = {
        folderType: 11,
        imgBody: obj.base64,
        // 患者id
        otherId: this.patientInfo.patientId,
      };
      let res = await uploadImg(para);
      // 图片名称
      return res.data.url;
    },
    // 移除图片
    delFile(n) {
      if (!this.isNext) return;
      this.fileList.splice(n, 1);
    },
    // 初次选择
    cloosImgTop() {
      if (!this.fileList.length) {
        this.cloosImg();
      }
    },
    // 选择图片
    cloosImg() {
      if (!this.isNext) return;
      const that = this;
      uni.chooseImage({
        count: 1,
        success: function (res) {
          // 读取图片
          const file = res.tempFiles[0];
          myJsTools.setImgZip(file, (dataUrl) => {
            that.fileList.push({
              base64: dataUrl.split(",")[1],
              url: dataUrl,
              name: "",
            });
          });
        },
      });
    },
    // 点击去支付
    send(isHelpPay) {
      if (!this.patientInfo) {
        Toast("请选择就诊人");
        return;
      }
      if(this.drugDetail.isCanintracity == "1"){
        if(this.delivery == 2){
          Toast("同城配送暂时无法下单");
          return;
        }
      }
      // 如果只有一个药店
      if (this.isOnly) {
        // 判断是否选择配送方式
        if (this.delivery == -1) {
          Toast("请选择配送方式");
          return;
        }
        if (this.delivery == 0) {
          if (!this.address) {
            Toast("请选择收货地址");
            return;
          }
        }
      } else {
        if (!this.address) {
          Toast("请选择收货地址");
          return;
        }
      }

      // 无需支付
      if (this.total == 0) {
        this.callId = this.payList[0];
        // 显示提示
        this.showPayTip = true;
        return;
      }

      if (!this.payList.length) {
        Toast("未配置支付方式");
        return;
      }

      // 是否代付
      this.isHelpPay = isHelpPay;

      if (isHelpPay == 1) {
        this.callId = this.payList[0];
        // 显示提示
        this.showPayTip = true;
        return;
      }

      // 只有一个方式
      if (this.payList.length == 1) {
        this.callId = this.payList[0];
        // 显示提示
        this.showPayTip = true;
        return;
      }

      let that = this;
      uni.showActionSheet({
        itemList: ["微信支付", "支付宝支付"],
        success(res) {
          that.callId = that.payList[res.tapIndex];
          // 显示提示
          that.showPayTip = true;
        },
      });
    },
    // 拼参数
    async setParam() {
      this.showPayTip = false;

      uni.showLoading({
        mask: true,
      });

      // 物流方式 1 配送 2 自提
      let deliveryType = 1;

      // 多个药店 只能配送
      if (this.orderList.length > 1) {
        deliveryType = 1;
      }

      // 只有一个药店
      if (this.isOnly) {
        // delivery: 0-统一配送, 1-自提, 2-同城配送
        // deliveryType: 1-配送, 2-自提, 3-同城配送
        if (this.delivery == 2) {
          deliveryType = 3;  // 同城配送
        } else {
          deliveryType = this.delivery + 1;
        }
      }

      // 自营物流费
      let logisticsCost = 0;

      let images = [];

      // 如果存在图片
      if (this.fileList.length) {
        // 循环上传图片
        for (let i = 0; i < this.fileList.length; i++) {
          try {
            let url = await this.upImg(this.fileList[i]);
            images.push(url);
          } catch (e) {
            console.log("图片上传失败", e);
          }
        }
      }

      // 数组
      const oms = [];
      // 整合
      const list = [];

      // 优惠前所有金额 药品 + 快递费
      let totalMoney =
        (Number(this.logist_count) * 100 + Number(this.old) * 100) / 100;

      // 收货地址
      const address = this.address;

      // 药品库存ids
      let yfkcIdList = [];

      this.orderList.forEach((v) => {
        if (v.isProprietary == 1) {
          // 普通物流
          if (v.sele_delivery == 0) {
            logisticsCost = v.ordinaryLogMoney;
          } else {
            // 顺丰物流
            logisticsCost = v.specialLogMoney;
          }
        }
        v.drugStoreList.forEach((k) => {
          k.deliveryType = deliveryType;
          k.logisticsCustomName =
            v.sele_delivery == 0 ? "普通物流" : "顺丰物流";
          k.logisticsCost =
            v.sele_delivery == 0 ? v.ordinaryLogMoney : v.specialLogMoney;
        });
        // 拆分药店
        list.push(...v.drugStoreList);
      });

      // 计算药品费用
      list.forEach((v) => {
        const ls = [];
        v.shoppingCartList.forEach((k) => {
          yfkcIdList.push(k.yfkcId);
          ls.push({
            ...k,
            drugTotal: k.shoudlePay,
            drugTotalReal: k.realyPay,
          });
        });
        let merchantsOrderMoney = v.orderRealyMoney;
        let merchantsTotalMoney = v.orderMoney;
        let obj = {
          // 收货地址
          deliveryAddressDetail:
            deliveryType == 1
              ? address.addressArea + "" + address.addressDetail
              : "",
          deliveryName: deliveryType == 1 ? address.deliveryName : "",
          deliveryTelNo: deliveryType == 1 ? address.telNo : "",
          // 订单金额
          merchantsOrderMoney,
          // 应付金额
          merchantsTotalMoney,
          subjectId: v.drugStoreID,
          subjectName: v.drugStoreName,
          logisticsCustomName: deliveryType == 1 ? v.logisticsCustomName : "",
          logisticsCost: v.isProprietary == 1 ? 0 : v.logisticsCost,
          ls,
        };
        oms.push(obj);
      });

      let payType = 1;

      // 微信 / 支付宝
      if (this.callId.indexOf("wx") > -1) {
        payType = 1;
      } else {
        payType = 2;
      }

      if (this.isHelpPay == 1) {
        payType = "d";
      }

      // 无需支付
      if (this.total <= 0) {
        payType = 0;
      }

      // 整合参数
      let obj = {
        appid: uni.getStorageSync("appId"),
        // 药品库存ids
        yfkcIdList,
        callId: this.callId,
        // 调剂费用实际支付
        dispensingFee: this.adjustRealyMoney,
        // 自营单独放 非自营放数组
        proprietaryLogisticsCost: deliveryType == 2 ? 0 : logisticsCost,
        openid: uni.getStorageSync("wxInfo").openId,
        // 最终合计金额
        orderMoney: this.total,
        // 配送方式
        deliveryType,
        // 图片
        medicalImg: images.length ? JSON.stringify(images) : "",
        patientName: this.patientInfo.patientName,
        patientId: this.patientInfo.patientId,
        payType,
        totalMoney: deliveryType == 1 ? totalMoney : this.old,
        // 收件人
        deliveryName: deliveryType == 1 ? address.deliveryName : "",
        deliveryTelNo: deliveryType == 1 ? address.telNo : "",
        deliveryAddressDetail:
          deliveryType == 1
            ? address.addressArea + "" + address.addressDetail
            : "",
        oms,
        isHelpPay: this.isHelpPay,
        addressId:address?.addressId
      };
      console.log("参数", obj);
      let data = await getCurrentCAType();
      console.log(data.data);
      if (data.data - 0 == 2) return Toast("暂不支持");
      try {
        if(this.orderNo){
          if (this.isHelpPay == 1) {
            this.setShare(obj.orderMoney, this.orderNo, 2);
            this.showTip = true;
            // 重新查询购物车。
            this.$store.dispatch("shop/getCartList");
            this.isNext = false;
            uni.hideLoading();
            return;
          }else{
            getHelpPayCreateResult({
              orderNo: this.orderNo,
              openid: uni.getStorageSync("wxInfo").openId,
              callId: this.callId,
              payType:payType,
            }).then(res=>{
              this.payFun(res);
            })
          }

        }else{
          let res = await createMallOrder(obj);
          // 库存不足
          if(res.data.stock){
            this.storeKcNo=res.data.stock
            this.$refs.kcPopup.open()
            return
          }
          // 无需支付
          if (payType == 0) {
            const { orderNo, orderStatus } = res.data;
            // 重新查询购物车。
            this.$store.dispatch("shop/getCartList");
            // 调用成功 直接跳转成功页面
            if (orderStatus == 2) {
              uni.reLaunch({
                url: "../result/success?id=" + orderNo,
              });
            }
            return;
          }
          if (this.isHelpPay == 1) {
            this.setShare(obj.orderMoney, res.data.orderNo, 2);
            this.showTip = true;
            // 重新查询购物车。
            this.$store.dispatch("shop/getCartList");
            this.isNext = false;
            return;
          }
          this.orderNo = res.data.orderNo;
          this.payFun(res)
        }
      } catch (e) {
        console.log("发起支付失败", e);
        uni.hideLoading();
      }
    },
    payFun(res){
      // 如果是微信支付
      if (this.callId.indexOf("wx") > -1) {
        // 调用微信支付
        this.toPay(res.data);
      } else {
        uni.navigateTo({
          url:
              "/pages/pay/pay?price=" +
              this.total +
              "&orderNo=" +
              res.data.orderNo +
              "&url=" +
              btoa(res.data.url),
        });
      }
      uni.hideLoading();
    },
    // 支付
    toPay(info) {
      // 重新查询购物车。
      this.$store.dispatch("shop/getCartList");
      const orderNo = info.orderNo;
      delete info.orderNo;
      // 调用微信支付
      let that = this;
      WeixinJSBridge.invoke("getBrandWCPayRequest", info, (res) => {
        if (res.err_msg == "get_brand_wcpay_request:ok") {
          // 查询状态
          that.getPayStatus(orderNo);
          // 调用三次
          timer = setInterval(() => {
            that.getPayStatus(orderNo);
          }, 2000);
        } else {
          Toast("取消支付");
          uni.redirectTo({
            url:
              "/pages/order/detail/drugStatus?orderNo=" + orderNo + "&source=2",
          });
        }
      });
    },
    // 查询支付状态
    async getPayStatus(orderNo) {
      // 根据订单号查询
      let res = await queryMallOrderStatus(orderNo);
      this.num--;

      if (res.data.orderStatus == 2) {
        clearInterval(timer);
        timer = null;
        this.num = 3;
        uni.reLaunch({
          url: "../result/success?id=" + orderNo,
        });
      }

      // 查询过三次
      if (this.num <= 0) {
        clearInterval(timer);
        // 应该跳转到失败
        if (res.data.orderStatus == 1) {
          uni.navigateTo({
            url: "../result/fail?&orderNo=" + orderNo,
          });
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}

::v-deep .wrapper .block {
  height: auto;
}

.pay_tip {
  .title {
    font-size: 32rpx;
  }

  .cont {
    padding: 10rpx 0 20rpx;
    font-size: 28rpx;
    text-align: left;
  }
}

.order_submit {
  padding: 24rpx 32rpx 120rpx;
  background-color: #f5f5f5;

  .sele_warp {
    width: 100%;
    background-color: #fff;
    border-radius: 8rpx;
    font-size: 28rpx;
    padding: 0 24rpx;
    margin-bottom: 24rpx;

    .sele_but {
      @include flex(lr);
      height: 88rpx;

      &.act {
        .uni-icons {
          transform: rotate(90deg);
        }
      }

      .uni-icons {
        transition: all 0.3s;
        margin-left: 10rpx;
      }

      .right {
        @include flex;

        .name {
          color: #666;
        }
      }
    }

    .img_list {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-gap: 20rpx;
      padding-bottom: 20rpx;

      .img_item {
        width: 200rpx;
        height: 200rpx;
        border-radius: 8rpx;
        border: 1px dashed #eee;
        position: relative;
        @include flex;
        flex-direction: column;

        text {
          color: #999;
        }

        .img {
          width: 100%;
          height: 100%;
        }

        .del {
          background-color: rgba($color: #000000, $alpha: 0.3);
          position: absolute;
          border-radius: 50%;
          top: 0;
          right: 0;
        }
      }
    }

    .sele_info {
      @include flex;
      align-items: flex-start;
      padding-bottom: 20rpx;

      text {
        flex: none;
        width: 110rpx;

        &.red {
          color: red;
          flex: 1;
        }
      }

      .info_text {
        flex: 1;
        color: #999;
      }
    }

    .logistics {
      width: 100%;
      padding-bottom: 24rpx;

      .item {
        height: 76rpx;
        padding: 0 24rpx;
        @include flex(lr);
        background-color: #f5f5f5;
        border-radius: 8rpx;

        &:last-child {
          margin-top: 24rpx;
        }

        .name {
          font-size: 28rpx;
        }

        .right {
          @include flex;

          text {
            font-size: 28rpx;
            color: red;
            padding-right: 30rpx;
          }

          image {
            width: 32rpx;
            height: 32rpx;
          }
        }
      }
    }
  }

  .warp {
    margin-bottom: 24rpx;
  }

  .user_info {
    @include flex(lr);
    padding: 18rpx 0;

    .user_head {
      width: 128rpx;
      height: 128rpx;
      border-radius: 8rpx;
      flex: none;
    }

    .user_desc {
      flex: 1;
      padding-left: 22rpx;

      .user_name {
        font-size: 32rpx;
        font-weight: bold;
      }

      .user_other {
        @include flex(left);
        padding-top: 10rpx;

        image {
          width: 32rpx;
          height: 32rpx;
        }

        text {
          padding-left: 10rpx;
          color: #666;
          font-size: 24rpx;
        }
      }
    }

    .uni-icons {
      flex: none;
    }
  }
}

// 覆盖样式
.pharmacy_detail {
  padding: 20rpx 0;
}

.address {
  padding: 20rpx 0;
}

.footer {
  width: 100%;
  height: 104rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 1;
  background-color: #fff;
  padding: 0 32rpx;
  @include flex(lr);
  box-shadow: 0px 4rpx 8rpx 0px rgba(0, 0, 0, 0.5);

  .count {
    font-size: 28rpx;
    color: #333;

    text {
      color: red;
    }
  }

  .right {
    @include flex;

    .but {
      width: 160rpx;
      height: 60rpx;
      color: #fff;
      @include flex;
      @include bg_theme;
      font-size: 28rpx;
      border-radius: 30rpx;

      &.pay {
        background: #fff;
        @include border_theme;
        @include font_theme;
        margin-right: 32rpx;
      }
    }
  }
}

.pop {
  padding: 0 32rpx 50rpx;
  background-color: #fff;
  border-radius: 16rpx 16rpx 0px 0px;

  .pop_title {
    height: 88rpx;
    @include flex;
    position: relative;
    font-size: 32rpx;

    .uni-icons {
      position: absolute;
      right: 0;
    }
  }

  .pop_text {
    color: #999;
    font-size: 24rpx;
    line-height: 40rpx;
  }
}
.kcPopup{
  background: white;
  box-sizing: border-box;
  width: 80%;
  margin: auto;
  .kcPopup-title{
    text-align: center;
    line-height: 40px;
    font-size: 16px;
    font-weight: bold;
  }
}
.kcPopup-content{
  padding: 20px;
  box-sizing: border-box;
}
.kcPopup-content-box{
  display: flex;
  flex-wrap: wrap;
  .kcPopup-content-item{
    width: 30%;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
  }
  .item-title{
    position: absolute;
    bottom: 0px;
    width: 50px;
    left: 50%;
    transform: translateX(-50%);
    background: #adadad;
    color: #0287cb;
    font-size: 10px;
    padding: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    border-radius: 3px;
  }
}
.kcPopup-content-item-img{
  width: 50px;
  height: 50px;
}
.kcPopup-content-btn{
  color: #15a0e6;
  text-align: center;
  line-height: 40px;
  border-top: 1px solid #ddd;
}
</style>
