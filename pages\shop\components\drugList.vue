<template>
  <div class="list">
    <div class="item" v-for="item in list" :key="item.drugId">
      <UniImage v-if="item.drugImg"
        :src="item.drugImg"
        :data-src="errUrl"
        class="img"
        alt=""
      />
      <!-- 商品图片 -->
      <image
        src="/static/shop/drug.png"
        v-else
        mode="aspectFill"
        class="img"
      ></image>

      <div class="right">
        <p class="title">{{ item.drugName }}</p>
        <p class="info">规格: {{ item.gg }}</p>
        <p class="price">￥{{ item.price | toFixed }}</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DrugList',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      errUrl: require('../../../static/images/Pharmacy-default.png'),
    };
  },
};
</script>

<style lang="scss" scoped>
.list {
  width: 552rpx;
  padding: 16rpx;
  background-color: #f5f5f5;
  max-height: 466rpx;
  overflow-y: scroll;
  border-radius: 8rpx;

  .item {
    @include flex;
    padding: 16rpx;
    background-color: #fff;
    margin-bottom: 16rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .img {
      width: 100rpx;
      height: 100rpx;
      border-radius: 8rpx;
      object-fit: cover;
      flex: none;
    }

    .right {
      flex: 1;
      min-height: 100rpx;
      padding-left: 24rpx;
      text-align: left;
      @include flex(lr);
      flex-direction: column;
      align-items: stretch;

      .title {
        font-size: 28rpx;
        font-weight: bold;
      }

      .info {
        font-size: 24rpx;
        color: #999;
      }

      .price {
        color: red;
        font-size: 28rpx;
      }
    }
  }
}
</style>
