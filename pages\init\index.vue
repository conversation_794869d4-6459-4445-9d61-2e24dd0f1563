<template>
  <view class="page">
    <view class="initial-container">
      <view class="initial-content" @click="setLog">
        <!-- 图标 -->
        <image :src="icon" mode="widthFix"></image>
        <!-- 海大夫健康服务平台 -->
        <view class="text">神州(天津)互联网医院</view>
      </view>
    </view>
    <!-- 环信注册/登录失败弹框 -->
    <propCenter v-if="isModelToastShow" @confirmPropCenter="outLogin">
      网络异常，请退出程序，重新进入
    </propCenter>
  </view>
</template>

<script>
import myJsTools from '@/common/js/myJsTools'
import myTools from '@/utils/myJsTools'
import { queryRegisterStatus } from '@/api/user.js'
// 页面参数（将在 onLoad 中获取）
let PARAMS = {}

// 引入视频通话w
// import {emediaE} from '@/utils/WebIM.js'

import {
  getOpenId,
  getUsertInfo,
  updateHxidIsregistStatus,
  hisPatientLoginBindingUserInfo,
} from '@/api/user.js'

// 添加注释
import { getSysPlatformConfigByKeyList } from '@/api/base.js'
import env from '@/env.js'
import { findOrderByMsgid, findOrderByBusinessId } from '@/api/order.js'

import { getPrescriptionCard } from '@/api/chat.js'

import {
  checkLisAppointStatus,
  checkPacsAppointStatus,
  getOpenIdUserInfo,
} from '@/api/inspect'

// 测试合并
import propCenter from '@/components/propCenter/propCenter.vue'
import store from '@/store'
export default {
  components: {
    propCenter,
  },
  data() {
    return {
      appid: '',
      code: '',
      wxInfo: '',
      isModelToastShow: false,
      hosId: '',
      action: '',
      // 视频通话相关参数
      conferenceId: '',
      // regId
      regId: '',
      // 图标展示
      icon: store.getters.allEnv[store.getters.envKey].VUE_APP_SHARE,
      // 区分页面
      page: '', // repeat
      // 科室id
      deptId: '', // D82FF689BD374A5DA4898F4C42519755
      // 是否视频
      isVideo: 0,
      docId: '', // 017f66699f194eee9947cd065316598e
      // 医嘱id
      dpmpId: '', // f8a85c28621248b381b36ed51d895e9a,
      // 消息id
      msgid: '', // "abc1234567" || new Date().getTime(),
      // 检查单
      ppiId: '',
      // 检验单
      pliId: '',
      // 处方
      businessCode: '',
      // 复购
      businessId: '', // 46ded25f461d44aba1f6937118261240
      // 是否扫码开方
      isScanCode: false,
      // 点击次数
      n: 0,
      // 订单号
      orderNo: '',
      // 项目id
      projectId: '',
    }
  },
  created() {
    uni.setNavigationBarTitle({
      title: '互联网医院',
    })
  },
  async onLoad(options) {
    // 获取页面参数（小程序方式）
    PARAMS = options || {}
    console.log('页面参数:', PARAMS)
    // 获取系统配置
    await this.getConfigDetail()
    uni.setStorageSync('sourceCode', '0')

    //修改是否用本地信息
    let isZs = false

    if (this.$store.getters.envKey == 'pro') {
      isZs = true
    }

    if (isZs) {
      //如果是登录页面 直接跳转 不进行别的操作
      // 判断服务统治的类型,跳转不同的页面
      this.action = options.action || ''
      if (this.action == 'initlogin') {
        if (
          myTools.getItem('proPfInfo') &&
          myTools.getItem('proPfInfo').token
        ) {
          uni.reLaunch({
            url: '/pages/index/index',
          })
        } else {
          uni.redirectTo({
            url: '/pages/init/initlogin',
          })
        }
        return
      }
      // 清理临时变量
      await this.clear()

      // 正式
      this.appid = options.appid || ''

      this.$store.commit('setAppId', this.appid)

      this.code = options.code || ''

      this.docId = options.docId || ''

      this.projectId = options.projectId || ''

      this.hosId = options.hosId || ''
      uni.setStorageSync('hosId', this.hosId)

      this.isNew = options.isNew || '' // 判断是新用户还是老用户

      // 公众号底部菜单跳转
      this.officialAccount = options.officialAccount || ''

      console.log('查看参数', this.officialAccount)

      console.log('查看参数appid', this.appid)

      // 视频通话参数
      this.conferenceId = options.conferenceId || ''

      this.regId = options.regId || ''

      // 检查单id
      this.ppiId = options.ppiId || ''

      // 检验单
      this.pliId = options.pliId || ''

      // 企业微信跳转处方
      let page = options.page || ''

      //分享医生信息（新加）
      let callSource = options.qrCodeId || ''
      uni.setStorageSync('callSource', callSource || '')

      this.page = page

      if (page == 'ynw') {
        const { telNo, patientName, drugId, patientId, ynwOpenid, scanid } =
          PARAMS

        uni.setStorageSync('ynw_scanid', scanid || '')
        uni.setStorageSync('ynw_patientId', patientId || '')
        uni.setStorageSync('ynw_openid', ynwOpenid || '')
        uni.setStorageSync('ynw_drugId', drugId || '')
        uni.setStorageSync('ynw_patientName', patientName || '')
        uni.setStorageSync('ynw_telNo', telNo || '')
      }

      // 科室id
      this.deptId = options.deptId || ''

      // 是否视频通话
      this.isVideo = options.isVideo || '0'

      // 医嘱
      this.dpmpId = options.dpmpId || ''

      // 消息id
      this.msgid = options.msgid || ''

      // 商城使用订单号
      this.orderNo = options.orderNo || ''

      // 处方
      this.businessCode = options.businessCode || ''

      this.businessId = options.businessId || ''

      this.getOpenIdFun()
    } else {
      this.action = options.action || ''
      if (this.action == 'initlogin') {
        if (
          myTools.getItem('proPfInfo') &&
          myTools.getItem('proPfInfo').token
        ) {
          uni.reLaunch({
            url: '/pages/index/index',
          })
        } else {
          uni.redirectTo({
            url: '/pages/init/initlogin',
          })
        }
        return
      }
      // 清理临时变量
      await this.clear()
      // 测试
      // this.appid = "wxe1d5f70e314b9f58";  //医小鹿
      // this.appid = "wx1dff9c2b40f7bfe7"; //医时针
      // this.appid = "wx0b35099d56bdf2ed"; // ceshi
      this.appid = 'wx6683b06aeddb37ac' //生产
      // this.appid = "wxc158d6225b1f290a"; //医时针
      // this.appid = 'wx54529fae788f1776' //医时针
      this.hosId = 1

      this.$store.commit('setAppId', this.appid)

      this.wxInfo = {
        city: '',
        country: '天津',
        headimgurl: '/static/logo.png',
        nickname: '***',
        // openId: 'op9Ly7MKchkiJZqK_g0scmosUlnc', // qy  prod
        // openId: 'oO3Py6sURE3pOubN3cNDN5eXPo0k',// yxh  test
        // openId: 'op9Ly7PhJ_ykhyKcW55YEN_-tn-0', // yxh  prod
        // openId: 'oO3Py6klUB5MtdCZVdAH6DLXrbE8', // zzq test
        openId: 'oO3Py6inOMTr46Ze-7ljTOKvR6jY', // yh 测试环境
        sex: 0,
      }
      this.$store.commit('setWxInfo', this.wxInfo)
      this.getUserInfoFun()
    }
  },
  methods: {
    // 获取参数的兼容方法
    getParam(key, options = {}) {
      // 优先从 onLoad 的 options 中获取
      if (options[key]) {
        return options[key]
      }

      // 从全局 PARAMS 中获取
      if (PARAMS[key]) {
        return PARAMS[key]
      }

      // #ifdef H5
      // H5 环境下可以从 URL 中获取
      if (typeof window !== 'undefined' && window.location) {
        const urlParams = new URLSearchParams(window.location.search)
        return urlParams.get(key) || ''
      }
      // #endif

      return ''
    },

    // 开启打印
    setLog() {

    },
    // 清除本地临时存储变量
    async clear() {
      uni.removeStorageSync('wxInfo')
      uni.removeStorageSync('userId')
      uni.removeStorageSync('hosId')
      uni.removeStorageSync('registration')
      uni.removeStorageSync('patientId')
      uni.removeStorageSync('myUsername')
      uni.removeStorageSync('chatList')
      uni.removeStorageSync('appointReserveInfo')
      uni.removeStorageSync('wxInfo')
      uni.removeStorageSync('scanAddress')
      uni.removeStorageSync('appId')
      uni.removeStorageSync('drugArr')
      uni.removeStorageSync('selectAddress')
      uni.removeStorageSync('infoDetail')
      uni.removeStorageSync('wholeArg')
      uni.removeStorageSync('offline')
      uni.removeStorageSync('qcOffline')
      uni.removeStorageSync('shop_address')
      uni.removeStorageSync('chatItem')
      uni.removeStorageSync('appointInfoDetail')
      uni.removeStorageSync('patientInfo')
      uni.removeStorageSync('nowAddress')
      uni.removeStorageSync('nowHZinfo')
      uni.removeStorageSync('proPfInfo')
      uni.removeStorageSync('patientIdList')
      uni.removeStorageSync('shop_patient')
      uni.removeStorageSync('shop_city')
      uni.removeStorageSync('offlineAppoint')
      uni.removeStorageSync('hideTab')
      uni.removeStorageSync('initialFlag')
      uni.removeStorageSync('evaluaAction')
      uni.removeStorageSync('isShowWorkHosName')
      uni.removeStorageSync('showInvoice')
      // 医诺卫相关
      uni.removeStorageSync('ynw_scanid')
      uni.removeStorageSync('ynw_patientId')
      uni.removeStorageSync('ynw_openid')
      uni.removeStorageSync('ynw_drugId')
      uni.removeStorageSync('ynw_patientName')
      uni.removeStorageSync('ynw_telNo')
      //分享医生
      uni.removeStorageSync('callSource')

      return Promise.resolve()
    },
    // 查询扫码开方或者快捷开方
    async getConfigDetail() {
      let { data } = await getSysPlatformConfigByKeyList([
        'fast_prescribe',
        'patientSideDisplayOnlineMall',
        'show_doctor_practice_hospital',
        'patient_show_invoice',
      ])

      data.forEach((v) => {
        // 是否开启快捷开方
        if (v.configKey == 'fast_prescribe' && v.configValue == 0) {
          this.isScanCode = true
        }
        // 如果未开启 隐藏商城入口
        if (
          v.configKey == 'patientSideDisplayOnlineMall' &&
          v.configValue == 0
        ) {
          this.appendStyle()
        }

        if (
          v.configKey == 'show_doctor_practice_hospital' &&
          v.configValue == 1
        ) {
          uni.setStorageSync('isShowWorkHosName', true)
        }

        if (v.configKey == 'patient_show_invoice' && v.configValue == 1) {
          uni.setStorageSync('showInvoice', true)
        }
      })
    },
    // 隐藏商城入口
    appendStyle() {
      // #ifdef H5
      let style = document.createElement('style')
      let sty =
        'uni-tabbar .uni-tabbar__item:nth-of-type(4) { display: none !important; }'
      style.innerHTML = sty
      document.head.appendChild(style)
      // #endif

      // #ifdef MP || APP-PLUS
      // 小程序和App中通过设置tabBar来隐藏
      uni.hideTabBarRedDot({
        index: 3 // 第4个tab（索引从0开始）
      })
      // 或者可以考虑使用其他方式，比如动态设置tabBar
      // #endif
    },
    // 查询扫码开方状态
    async getStatus() {
      return await findOrderByMsgid(this.msgid)
    },
    // 获取openid,微信信息,是否关注
    async getOpenIdFun() {
      let para = {
        appid: this.appid,
        code: this.code,
      }
      //
      let res = await getOpenId(para)

      this.wxInfo = res.data
      if (!this.wxInfo) {
        this.wxInfo = {
          city: '',
          country: '天津',
          headimgurl: '/static/logo.png',
          nickname: '***',
          // openId: "oXVoe5uA6whF57-ij9qNE9IOTZ48",
          // openId: "oXVoe5rOnmnWUlq6DFkBP9cqbeuE",
          // openId: "opL5Fw22wX6jucu1JIcNEM6sMaDM",
          // openId: "oCAnn5pNqcT9oqXIQXFOm_AuX7Uc",
          openId: 'oXVoe5uA6whF57-ij9qNE9IOTZ48', // 张栋
          sex: 0,
        }
      }
      // 存储微信信息
      this.$store.commit('setWxInfo', this.wxInfo)
      this.getUserInfoFun()
    },
    // 获取用户信息
    async getUserInfoFun() {
      let para = {
        appid: this.appid,
        openid: this.wxInfo.openId,
      }

      if (this.page == 'ynw') {
        para.isThirdPart = 1
        para.thirdPatientId = uni.getStorageSync('ynw_patientId')
        para.thirdAppid = 'ynw'
        para.scanid = uni.getStorageSync('ynw_scanid')
        para.thirdPartyOpenid = uni.getStorageSync('ynw_openid')
      } else {
        para.isThirdPart = 0
      }

      try {
        // 请求用户信息
        let res = await getUsertInfo(para)

        if (res.data && res.data.userId) {
          let proPfInfo = res.data
          // 存储用户信息
          this.$store.commit('setProPfInfo', proPfInfo)

          // userId
          uni.setStorageSync('userId', proPfInfo.userId)

          // 用以注册环信(全部小写)
          let myUsername = proPfInfo.userId.toLowerCase()

          uni.setStorageSync('myUsername', myUsername)

          uni.setStorageSync('tel', res.data.telNo || '')

          // 该用户下所有的患者id
          uni.setStorageSync('patientIdList', proPfInfo.patientIdList || [])

          // 是否注册过
          let hxidIsregist = proPfInfo.hxidIsregist

          if (hxidIsregist == '1') {
            this.WebIMLogin()
          } else {
            this.WebIMRegister()
          }
          // this.toPath();
        } else {
          if (this.page == 'ynw') {
            let data = {
              isThirdPart: 1,
              thirdAppid: 'ynw',
              thirdPatientId: uni.getStorageSync('ynw_patientId'),
              scanid: uni.getStorageSync('ynw_scanid'),
              thirdPartyOpenid: uni.getStorageSync('ynw_openid'),
              appid: uni.getStorageSync('appId'),
              openid: uni.getStorageSync('wxInfo').openId,
              headImgUrl: uni.getStorageSync('wxInfo').headimgurl,
              telNo: uni.getStorageSync('ynw_telNo'),
              userName: uni.getStorageSync('ynw_patientName'),
            }
            let u = await hisPatientLoginBindingUserInfo(data)
            if (u.code != 20000) {
              uni.redirectTo({
                url: '/pages/login/login',
              })
              return
            }
            this.getUserInfoFun()
          } else {
            this.errToPath()
          }
        }
      } catch (e) {
        console.log(e, 'catch  getUserInfoFun')
        if (this.page == 'ynw') {
          return
        }
        this.errToPath()
      }
    },
    // 根据参数跳转
    async toPath() {
      console.log('查看逻辑是否到达这里action', this.action)
      console.log('查看逻辑是否到达这里officialAccount', this.officialAccount)
      console.log('查看逻辑是否到达这里appid', this.appid)
      console.log('查看逻辑是否到达这里code', this.code)
      let action = this.action
      if (action == 'aiBanner') {
        uni.navigateTo({
          url: '/pages/aiAssistant/previewImages',
        })
        return
      }
      // 默认首页
      let url = '/pages/index/index'
      let { data } = await getOpenIdUserInfo({
        appid: this.appid,
        openid: this.wxInfo.openId,
      })
      if (!data) {
        url = '/pages/login/login'
      } else {
        if (!data?.userId) {
          url = '/pages/login/login'
        }
      }
      console.log(action)
      // 存在action
      if (action) {
        // 扫码购药逻辑必须存在action
        if (!data) {
          url = '/pages/login/login'
          if (action === 'offline' || action === 'qcOffline') {
            url =
              '/pages/login/login?action=' +
              action +
              '&docId=' +
              this.docId +
              '&projectId=' +
              this.projectId
            uni.reLaunch({
              url,
            })
            return
          }
        } else {
          if (!data?.userId) {
            if (action === 'offline' || action === 'qcOffline') {
              url =
                '/pages/login/login?action=' +
                action +
                '&docId=' +
                this.docId +
                '&projectId=' +
                this.projectId
              uni.reLaunch({
                url,
              })
              return
            }
          }
        }

        // 6.18活动
        if (action == '618') {
          uni.setStorageSync('sourceCode', '618')
          if (!data) {
            url = '/pages/login/login?action=' + action
            uni.reLaunch({
              url,
            })
            return
          } else {
            if (!data?.userId) {
              url = '/pages/login/login?action=' + action
              uni.reLaunch({
                url,
              })
              return
            } else {
              url = '/pages/shop/index'
              uni.switchTab({
                url,
              })
              return
            }
          }
        }

        // 挂号详情
        if (action == 'register') {
          url = '/pages/personalCenter/diagnosisRecord/detail?id=' + this.regId
        } else if (action == 'chatList') {
          url = '/pages/chatList/index'
        } else if (action == 'visitList') {
          url = '/pages/personalCenter/diagnosisRecord/index' //结束通话跳转挂号页面
        } else if (action == 'visitDetailList') {
          url =
            '/pages/prescription/preDetail?businessId=' +
              (options.businessid || PARAMS.businessid || '') //发货通知跳转到处方详情页
        } else if (action == 'videoCall') {
          url =
            '/pages/chatList/videoCall?conferenceId=' +
            this.conferenceId +
            '&regId=' +
            this.regId +
            '&userId=' +
            uni.getStorageSync('userId') +
            '&token=' +
            uni.getStorageSync('proPfInfo').token +
            '&deptId=' +
            this.deptId +
            '&isVideo=' +
            this.isVideo +
            '&docId=' +
            this.docId +
            '&hosId=' +
            uni.getStorageSync('hosId')
        } else if (action == 'videoAgora') {
          url =
            '/pages/chatList/videoCallAgora?regId=' +
            this.regId +
            '&userId=' +
            uni.getStorageSync('userId') +
            '&token=' +
            uni.getStorageSync('proPfInfo').token +
            '&deptId=' +
            this.deptId +
            '&isVideo=' +
            this.isVideo +
            '&docId=' +
            this.docId +
            '&hosId=' +
            uni.getStorageSync('hosId')
        } else if (action == 'shopTab') {
          // 隐藏tab栏
          uni.setStorageSync('hideTab', 1)
          url = '/pages/shop/index'

          // 检查单详情
        } else if (action == 'pacs') {
          url = '/pages/inspect/pacsDetails?id=' + this.ppiId
          let {
            data: { paymentType, status },
          } = await checkPacsAppointStatus(this.ppiId)
          // 如果已选择支付方式 到院
          if (paymentType == 1) {
            url = '/pages/inspect/pacsDetails?id=' + this.ppiId

            // 在线支付未支付
          } else if (paymentType == 2 && status == 1) {
            url = '/pages/inspect/pay/pacs?id=' + this.ppiId

            // 未选择支付方式
          } else if (!paymentType) {
            url = '/pages/inspect/pacsOrder?id=' + this.ppiId

            // 其他情况 直接去详情
          } else {
            url = '/pages/inspect/pacsDetails?id=' + this.ppiId
          }
          // 检验单详情
        } else if (action == 'lis') {
          let {
            data: { paymentType, status },
          } = await checkLisAppointStatus(this.pliId)

          // 如果已选择支付方式 到院
          if (paymentType == 1) {
            url = '/pages/inspect/lisDetails?id=' + this.pliId

            // 在线支付未支付
          } else if (paymentType == 2 && status == 1) {
            url = '/pages/inspect/pay/lis?id=' + this.pliId

            // 未选择支付方式
          } else if (!paymentType) {
            url = '/pages/inspect/lisOrder?id=' + this.pliId

            // 其他情况 直接去详情
          } else {
            url = '/pages/inspect/lisDetails?id=' + this.pliId
          }

          // 处方
        } else if (action == 'pre') {
          // 需查询状态
          let {
            data: { payStatus, businessId },
          } = await getPrescriptionCard({
            businessCode: this.businessCode,
          })
          // 未支付
          if (payStatus == 0) {
            url =
              '/pages/prescription/prescriptionDetail?businessId=' +
              businessId +
              '&status=1'
          } else {
            url = '/pages/prescription/preDetail?businessId=' + businessId
          }
          // 商城订单
        } else if (action == 'shop') {
          url = '/pages/shopOrder/detail?orderNo=' + this.orderNo
          // 取消申请
        } else if (action == 'cancel') {
          url = '/pages/shopOrder/cancel?orderNo=' + this.orderNo
          //商品发货,取药，退款通知跳转到订单详情页
        } else if (action == 'visitOrderDetailList') {
          url = '/pages/order/detail/drugStatus?orderNo=' + this.orderNo //商品发货通知跳转到订单详情页
        } else if (action == 'offline') {
          url = '/pages/shop/scan?docId=' + this.docId
        } else if (action == 'qcOffline') {
          url =
            '/pages/scanCode/nutritionAssessment?docId=' +
            this.docId +
            '&projectId=' +
            this.projectId
        } else if (action != '') {
          url = action
        }
        uni.reLaunch({
          url,
        })
        return
      }

      // 公众号底部菜单跳转
      if (this.officialAccount) {
        const offPatn = [
          {
            type: 'KSXF',
            path: '/pages/quickContinuation/index',
          },
          {
            type: 'FZKF',
            path: '/pages/register/docList2/index?flag=2',
          },
          {
            type: 'JKZX',
            path: '/pages/register/docList2/index?flag=1',
          },
          {
            type: 'JKSC',
            path: '/pages/shop/index',
          },
          {
            type: 'WDYS',
            path: '/pages/personalCenter/myDocter/patients/patients?pageType=myDocter',
          },
          {
            type: 'WDDD',
            path: '/pages/order/index',
          },
          {
            type: 'GRZX',
            path: '/pages/personalCenter/personalInfo/index',
          },
          {
            type: 'aiBanner',
            path: '/pages/aiAssistant/previewImages',
          },
        ]
        const resultPath = offPatn.find(
          (item) => item.type === this.officialAccount
        )
        if (resultPath.path == '/pages/aiAssistant/previewImages') {
          uni.reLaunch({
            url: resultPath.path,
          })
          return
        }
        url = resultPath.path
        if (!data) {
          url = '/pages/login/login'
        } else {
          if (!data?.userId) {
            url = '/pages/login/login'
          }
        }
        uni.reLaunch({
          url,
        })
        return
      }

      // 去我的处方
      if (this.page == 'prescription') {
        url = '/pages/personalCenter/myPrescription/index'

        uni.reLaunch({
          url,
        })
        return
      }

      // 医诺卫相关
      if (this.page == 'ynw') {
        // 隐藏tab栏
        uni.setStorageSync('hideTab', 1)

        url = '/pages/shop/index'

        uni.reLaunch({
          url,
        })
        return
      }

      // 快捷开方 复购
      if (this.page == 'repeat') {
        let { data } = await findOrderByBusinessId({
          businessId: this.businessId,
        })
        // 不存在订单 或者未支付
        if (!data || data.orderStatus == 1) {
          url = '/pages/quick/repeat?businessId=' + this.businessId

          // 已支付 并且自提
        } else if (
          data.orderStatus == 3 &&
          (data.deliveryType == 2 || !data.deliveryType)
        ) {
          // 成功页
          url = '/pages/quick/result?orderNo=' + data.orderNo

          // 其他已支付状态
        } else if (data.deliveryType == 1 || data.orderStatus >= 3) {
          // 业务详情
          url = '/pages/prescription/preDetail?businessId=' + this.businessId
        }
        uni.reLaunch({
          url,
        })
        return
      }

      // 医嘱id 扫码开方
      if (this.dpmpId) {
        uni.setStorageSync('hosId', this.hosId)

        let userId = uni.getStorageSync('userId')

        let list = uni.getStorageSync('patientIdList') || []

        let res = await this.getStatus()

        // 如果存在之前关联订单
        if (res.data) {
          let { source } = res.data
          // 来自扫码
          if (source == 3) {
            this.isScanCode = true
          } else {
            this.isScanCode = false
          }
        }

        // 扫码开方
        if (this.isScanCode) {
          // 如果不存在订单 或没有用户 为新建
          if (!res.data || !userId) {
            // 扫码开方
            url =
              '/pages/scanCode/index?dpmpId=' +
              this.dpmpId +
              '&docId=' +
              this.docId +
              '&msgid=' +
              this.msgid
            // 未付款
          } else if (res.data.orderStatus == 1) {
            // 存在订单 去详情
            url = '/pages/scanCode/result?orderNo=' + res.data.orderNo

            // 已付款
          } else {
            url =
              '/pages/prescription/preDetail?businessId=' +
              res.data.prescriptionBusinessId
          }

          // 快捷开方
        } else {
          // 未关联 或未支付
          if (!res.data || res.data.orderStatus == 1) {
            url =
              '/pages/quick/index?dpmpId=' +
              this.dpmpId +
              '&docId=' +
              this.docId +
              '&msgid=' +
              this.msgid
            // 已支付 没有关联就诊人
          } else if (res.data.orderStatus > 1 && !res.data.patientId) {
            // 压根没有就诊人
            if (!list.length) {
              // 完善信息
              url =
                '/pages/quick/info?orderNo=' +
                res.data.orderNo +
                '&dpmpId=' +
                this.dpmpId
              // 存在不止一个就诊人
            } else {
              url =
                '/pages/quick/sele?orderNo=' +
                res.data.orderNo +
                '&dpmpId=' +
                this.dpmpId
            }
            // 如果存在就诊人id 支付 并且思自提 且待发药
          } else if (
            res.data.patientId &&
            res.data.deliveryType == 2 &&
            res.data.orderStatus == 3
          ) {
            // 支付成功页
            url = '/pages/quick/result?orderNo=' + res.data.orderNo
          } else {
            // 已支付 有就诊人 不是代发药 进业务详情
            url =
              '/pages/prescription/preDetail?businessId=' +
              res.data.prescriptionBusinessId
          }
        }

        uni.reLaunch({
          url,
        })
        return
      }

      // 医生首页
      if (this.docId && this.hosId) {
        console.log('查看扫码跳转url参数', 222222222222)
        uni.setStorageSync('hosId', this.hosId)
        await queryRegisterStatus({
          appid: this.appid,
          openid: this.wxInfo.openId,
        }).then((ret) => {
          console.log('查看扫码跳转url参数', ret)
          if (ret && Number(ret.data) === 1) {
            // 跳转新界面  新绑定医生界面
            url =
              '/pages/register/docHomePage/bindDoctor?action=' +
              'init' +
              '&docId=' +
              this.docId
          }
        })
      }
      uni.reLaunch({
        url,
      })
    },
    // 获取信息后跳转
    errToPath() {
      if (uni.getStorageSync('userId')) {
        uni.removeStorageSync('userId')
      }
      if (uni.getStorageSync('myUsername')) {
        uni.removeStorageSync('myUsername')
      }
      // 清空聊天记录
      this.$store.dispatch('clearDB')

      this.toPath()
    },
    // 环信注册
    WebIMRegister() {
      let _this = this
      let userId = uni.getStorageSync('userId')
      let options = {
        username: userId,
        password: userId,
        nickname: userId,
        appKey: uni.$im.config.appkey,
        success: function () {
          _this.updateHxidIsregistStatusFun()

          // _this.WebIMLogin();
        },
        error: function (err) {
          if (err.statusCode == 400) {
            console.log('查看注册错误信息', err)
            _this.updateHxidIsregistStatusFun()
            return
          }
          _this.isModelToastShow = true
        },
        apiUrl: _this.$im.config.apiURL,
      }
      _this.$im.conn.registerUser(options)
    },

    // 更改用户环信状态
    async updateHxidIsregistStatusFun() {
      let userId = uni.getStorageSync('userId')
      await updateHxidIsregistStatus({
        userId,
      })
      // 更改用户环信状态成功
      this.WebIMLogin()
    },

    // 环信登录
    WebIMLogin() {
      let _this = this
      let userId = uni.getStorageSync('userId')
      console.log('查看登录userId', userId)
      let options = {
        apiUrl: _this.$im.config.apiURL,
        user: userId,
        pwd: userId,
        grant_type: userId,
        appKey: _this.$im.config.appkey,
        success: function (res) {
          let memName = _this.$im.config.appkey + '_' + userId
          console.log('登录环信成功', userId)
          // 进入会议前 必须调用
          // emediaE.mgr.setIdentity(memName, res.access_token)

          _this.toPath()
        },
        error: function (err) {
          console.log('查看登录错误日志', err)
          _this.isModelToastShow = true
        },
      }
      _this.$im.conn
        .open(options)
        .then((res) => {
          let memName = _this.$im.config.appkey + '_' + userId
          console.log('登录环信成功', userId)
          // 进入会议前 必须调用
          // emediaE.mgr.setIdentity(memName, res.access_token)

          _this.toPath()
        })
        .catch((err) => {
          console.log('查看登录错误日志', err)
          _this.isModelToastShow = true
        })
    },

    // 环信注册/登录失败，点击确定，退出程序
    outLogin() {
      this.isModelToastShow = false
      // #ifdef H5
      if (typeof WeixinJSBridge !== 'undefined') {
        WeixinJSBridge.call('closeWindow')
      } else {
        // 如果不在微信环境中，可以尝试关闭窗口
        window.close()
      }
      // #endif

      // #ifdef MP
      // 小程序中无法直接退出，可以跳转到首页或显示提示
      uni.reLaunch({
        url: '/pages/index/index'
      })
      // #endif

      // #ifdef APP-PLUS
      // App中可以退出应用
      plus.runtime.quit()
      // #endif
    },
  },
}
</script>

<style scoped lang="scss">
.page {
  background-color: #fff;
}
/* 主体内容 */
.initial-content {
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.initial-content image {
  display: block;
  width: 170rpx;
  border-radius: 8rpx;
  margin: 0 auto;
}

.initial-content .title {
  font-size: 44rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  @include font_theme;
  margin-top: 28rpx;
  line-height: 60rpx;
}

.toast-text {
  margin-top: 20rpx;
}

.initial-content .text {
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  color: #4a4a4a;
  line-height: 60rpx;
}
</style>
