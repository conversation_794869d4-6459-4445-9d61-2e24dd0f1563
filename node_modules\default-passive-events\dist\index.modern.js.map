{"version": 3, "file": "index.modern.js", "sources": ["../src/index.js", "../src/utils.js"], "sourcesContent": ["import { eventListenerOptionsSupported } from './utils';\n\nconst defaultOptions = {\n  passive: true,\n  capture: false\n};\nconst supportedPassiveTypes = [\n  'scroll', 'wheel',\n  'touchstart', 'touchmove', 'touchenter', 'touchend', 'touchleave',\n  'mouseout', 'mouseleave', 'mouseup', 'mousedown', 'mousemove', 'mouseenter', 'mousewheel', 'mouseover'\n];\nconst getDefaultPassiveOption = (passive, eventName) => {\n  if (passive !== undefined) return passive;\n\n  return supportedPassiveTypes.indexOf(eventName) === -1 ? false : defaultOptions.passive;\n};\n\nconst getWritableOptions = (options) => {\n  const passiveDescriptor = Object.getOwnPropertyDescriptor(options, 'passive');\n    \n  return passiveDescriptor && passiveDescriptor.writable !== true && passiveDescriptor.set === undefined\n    ? Object.assign({}, options)\n    : options;\n};\n\nconst overwriteAddEvent = (superMethod) => {\n  EventTarget.prototype.addEventListener = function (type, listener, options) {\n    const usesListenerOptions = typeof options === 'object' && options !== null;\n    const useCapture          = usesListenerOptions ? options.capture : options;\n\n    options         = usesListenerOptions ? getWritableOptions(options) : {};\n    options.passive = getDefaultPassiveOption(options.passive, type);\n    options.capture = useCapture === undefined ? defaultOptions.capture : useCapture;\n\n    superMethod.call(this, type, listener, options);\n  };\n\n  EventTarget.prototype.addEventListener._original = superMethod;\n};\n\nconst supportsPassive = eventListenerOptionsSupported();\n\nif (supportsPassive) {\n  const addEvent = EventTarget.prototype.addEventListener;\n  overwriteAddEvent(addEvent);\n}\n", "export const eventListenerOptionsSupported = () => {\n  let supported = false;\n\n  try {\n    const opts = Object.defineProperty({}, 'passive', {\n      get() {\n        supported = true;\n      }\n    });\n\n    window.addEventListener('test', null, opts);\n    window.removeEventListener('test', null, opts);\n  } catch (e) {}\n\n  return supported;\n}\n"], "names": ["supportedPassiveTypes", "superMethod", "supported", "opts", "Object", "defineProperty", "get", "window", "addEventListener", "removeEventListener", "e", "eventListenerOptionsSupported", "EventTarget", "prototype", "type", "listener", "options", "usesListenerOptions", "useCapture", "capture", "passive", "passiveDescriptor", "getOwnPropertyDescriptor", "writable", "undefined", "set", "assign", "getWritableOptions", "indexOf", "call", "this", "_original"], "mappings": "MAMMA,EAAwB,CAC5B,SAAU,QACV,aAAc,YAAa,aAAc,WAAY,aACrD,WAAY,aAAc,UAAW,YAAa,YAAa,aAAc,aAAc,aAgBlEC,IAAAA,ECzBkB,MAC3C,IAAIC,GAAY,EAEhB,IACE,MAAMC,EAAOC,OAAOC,eAAe,GAAI,UAAW,CAChDC,MACEJ,GAAY,KAIhBK,OAAOC,iBAAiB,OAAQ,KAAML,GACtCI,OAAOE,oBAAoB,OAAQ,KAAMN,GACzC,MAAOO,IAET,OAAOR,GD0BeS,KAfGV,EAkBRW,YAAYC,UAAUL,iBAjBvCI,YAAYC,UAAUL,iBAAmB,SAAUM,EAAMC,EAAUC,GACjE,MAAMC,EAAyC,iBAAZD,GAAoC,OAAZA,EACrDE,EAAsBD,EAAsBD,EAAQG,QAAUH,EAjBxC,IAACI,GAmB7BJ,EAAkBC,EAbMD,CAAAA,IAC1B,MAAMK,EAAoBjB,OAAOkB,yBAAyBN,EAAS,WAEnE,OAAOK,IAAoD,IAA/BA,EAAkBE,eAA+CC,IAA1BH,EAAkBI,IACjFrB,OAAOsB,OAAO,GAAIV,GAClBA,GAQsCW,CAAmBX,GAAW,IAC9DI,aAnBMI,KADeJ,EAoBaJ,EAAQI,SAnBlBA,GAEmB,IAA9CpB,EAAsB4B,QAiBgCd,KA5BpD,EA6BPE,EAAQG,aAAyBK,IAAfN,GAAoDA,EAEtEjB,EAAY4B,KAAKC,KAAMhB,EAAMC,EAAUC,IAGzCJ,YAAYC,UAAUL,iBAAiBuB,UAAY9B"}