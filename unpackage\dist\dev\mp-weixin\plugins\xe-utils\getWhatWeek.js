"use strict";
const plugins_xeUtils_setupDefaults = require("./setupDefaults.js");
const plugins_xeUtils_staticDayTime = require("./staticDayTime.js");
const plugins_xeUtils_staticWeekTime = require("./staticWeekTime.js");
const plugins_xeUtils_helperGetDateTime = require("./helperGetDateTime.js");
const plugins_xeUtils_toStringDate = require("./toStringDate.js");
const plugins_xeUtils_isValidDate = require("./isValidDate.js");
const plugins_xeUtils_isNumber = require("./isNumber.js");
function getWhatWeek(date, offsetWeek, offsetDay, firstDay) {
  date = plugins_xeUtils_toStringDate.toStringDate(date);
  if (plugins_xeUtils_isValidDate.isValidDate(date)) {
    var hasCustomDay = plugins_xeUtils_isNumber.isNumber(offsetDay);
    var hasStartDay = plugins_xeUtils_isNumber.isNumber(firstDay);
    var whatDayTime = plugins_xeUtils_helperGetDateTime.helperGetDateTime(date);
    if (hasCustomDay || hasStartDay) {
      var viewStartDay = hasStartDay ? firstDay : plugins_xeUtils_setupDefaults.setupDefaults.firstDayOfWeek;
      var currentDay = date.getDay();
      var customDay = hasCustomDay ? offsetDay : currentDay;
      if (currentDay !== customDay) {
        var offsetNum = 0;
        if (viewStartDay > currentDay) {
          offsetNum = -(7 - viewStartDay + currentDay);
        } else if (viewStartDay < currentDay) {
          offsetNum = viewStartDay - currentDay;
        }
        if (customDay > viewStartDay) {
          whatDayTime += ((customDay === 0 ? 7 : customDay) - viewStartDay + offsetNum) * plugins_xeUtils_staticDayTime.staticDayTime;
        } else if (customDay < viewStartDay) {
          whatDayTime += (7 - viewStartDay + customDay + offsetNum) * plugins_xeUtils_staticDayTime.staticDayTime;
        } else {
          whatDayTime += offsetNum * plugins_xeUtils_staticDayTime.staticDayTime;
        }
      }
    }
    if (offsetWeek && !isNaN(offsetWeek)) {
      whatDayTime += offsetWeek * plugins_xeUtils_staticWeekTime.staticWeekTime;
    }
    return new Date(whatDayTime);
  }
  return date;
}
exports.getWhatWeek = getWhatWeek;
