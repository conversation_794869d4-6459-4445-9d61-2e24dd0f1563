"use strict";
const plugins_xeUtils_setupDefaults = require("./setupDefaults.js");
const plugins_xeUtils_each = require("./each.js");
const plugins_xeUtils_assign = require("./assign.js");
function unTreeList(result, array, opts) {
  var optChildren = opts.children;
  var optData = opts.data;
  var optClear = opts.clear;
  plugins_xeUtils_each.each(array, function(item) {
    var children = item[optChildren];
    if (optData) {
      item = item[optData];
    }
    result.push(item);
    if (children && children.length) {
      unTreeList(result, children, opts);
    }
    if (optClear) {
      delete item[optChildren];
    }
  });
  return result;
}
function toTreeArray(array, options) {
  return unTreeList([], array, plugins_xeUtils_assign.assign({}, plugins_xeUtils_setupDefaults.setupDefaults.treeOptions, options));
}
exports.toTreeArray = toTreeArray;
