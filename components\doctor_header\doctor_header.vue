<template>
  <!-- 医生主页头部 -->
  <view class="doctor_box_top">
    <image
      :src="
        infoDetail.docImg ? infoDetail.docImg : '/static/images/docHead.png'
      "
      mode="aspectFill"
      class="header_img"
    ></image>
    <view class="doctor_content_right">
      <view class="doctor-name">{{ infoDetail.docName }} · {{ infoDetail.docProf }}</view>
      <view class="font_col_666">{{ infoDetail.deptName }}</view>
      <!-- 标签 -->
      <!--      <view class="content_label">-->
      <!--        <block v-for="item in infoDetail.docLable">-->
      <!--          <text>{{ item.lableName || item }}</text>-->
      <!--        </block>-->
      <!--      </view>-->
    </view>
  </view>
</template>

<script>
export default {
  props: {
    infoDetail: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {};
  },
};
</script>

<style lang="scss" scoped>
.doctor-name{
  font-weight: bold;
  font-size: 12px;
}
/* 头部 */
.doctor_box_top {
  background-color: #ffffff;
  display: flex;
  align-items: center;
  padding: 30upx 58rpx;
  color: #333333;
  font-size: 26rpx;
  font-weight: 600;
  border-radius: 0px 0px 9.14px 9.14px;
  box-shadow: 0px 1px 4px rgba(121, 72, 234, 0.15);

  // 头像
  .header_img {
    width: 128rpx;
    height: 128rpx;
    border-radius: 12upx;
    margin-right: 30rpx;
    margin-left: 88rpx;
    border: 1px solid rgba(255, 255, 255, 0.6);
    flex: none;
  }

  .doctor_content_right {
    flex: 1;

    .content_label {
      padding-top: 10upx;
      @include flex(left);
      flex-wrap: wrap;

      text {
        font-family: PingFangSC-Regular, PingFang SC;
        background: #e8f6fd;
        border-radius: 18rpx;
        font-size: 22rpx;
        @include font_theme;
        margin-right: 20rpx;
        padding: 0 10rpx;
        @include flex;
      }
    }
  }
}

.font_col_666 {
  font-size: 12px;
  color: rgba(102, 102, 102, 1);
  padding-top: 20rpx;
  font-weight: normal !important;
}
</style>
