"use strict";
const common_vendor = require("../../../common/vendor.js");
const TITLE = () => "../../inspect/com/itemTitle.js";
const _sfc_main = {
  components: {
    TITLE
  },
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      errUrl: require("../../../static/images/docHead.png"),
      isShowWorkHosName: common_vendor.index.getStorageSync("isShowWorkHosName") || false
    };
  },
  methods: {
    lookOver() {
      common_vendor.index.navigateTo({
        url: "/pages/index/com/famousDocList"
      });
    },
    // 去医生主页
    toDoc(item) {
      common_vendor.index.setStorageSync("hosId", item.hosId);
      common_vendor.index.navigateTo({
        url: "/pages/register/docHomePage/index?docId=" + item.docId
      });
    }
  }
};
if (!Array) {
  const _component_TITLE = common_vendor.resolveComponent("TITLE");
  const _component_UniImage = common_vendor.resolveComponent("UniImage");
  (_component_TITLE + _component_UniImage)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.p({
      title: "名医推荐"
    }),
    b: common_vendor.o((...args) => $options.lookOver && $options.lookOver(...args)),
    c: common_vendor.f($props.list, (item, index, i0) => {
      return common_vendor.e({
        a: item.docImg
      }, item.docImg ? {
        b: "16bd5f8b-1-" + i0,
        c: common_vendor.p({
          src: item.docImg,
          ["default-src"]: $data.errUrl,
          ["error-src"]: $data.errUrl
        })
      } : {
        d: "16bd5f8b-2-" + i0,
        e: common_vendor.p({
          src: "/static/images/docHead.png"
        })
      }, {
        f: common_vendor.t(item.docName),
        g: common_vendor.t(item.deptName),
        h: common_vendor.t(item.docProf),
        i: common_vendor.t($data.isShowWorkHosName ? item.workHosName : item.hosName),
        j: item.docLable.length > 3
      }, item.docLable.length > 3 ? {
        k: common_vendor.f(item.docLable.slice(0, 3), (l, lk, i1) => {
          return {
            a: common_vendor.t(l.lableName)
          };
        })
      } : {
        l: common_vendor.f(item.docLable, (l, lk, i1) => {
          return {
            a: common_vendor.t(l.lableName)
          };
        })
      }, {
        m: common_vendor.o(($event) => $options.toDoc(item), index),
        n: index
      });
    })
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-16bd5f8b"]]);
wx.createComponent(Component);
