<template>
	<view class="">
		<uni-popup ref="popup" type="center">
			<view class="hxToast">
				<view class="block">
					<view class="title">提示</view>
					<view class="content">网络异常，请退出小程序，重新进入</view>
					<view class="out-btn">
						<button>确定</button>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>
<script>
	export default({
		data(){
			return{
			}
		},
		mounted(){
			this.$refs.popup.open();
		}
	})
</script>

<style scoped>
	.hxToast{
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100%;
	}
	
	.hxToast .block {
		width: 606rpx;
		box-sizing: border-box;
		background: rgba(255, 255, 255, 1);
		border-radius: 16rpx;
		overflow: hidden;
		padding: 94rpx 40rpx 40rpx 40rpx;
	}
	
	.hxToast .title {
		text-align: center;
		padding-top: 40rpx;
		font-size: 36rpx;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 500;
		color: rgba(51, 51, 51, 1);
		line-height: 50rpx;
	}
	
	.hxToast .content {
		padding: 16rpx 24rpx;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(51, 51, 51, 1);
		line-height: 42rpx;
		min-height: 100rpx;
	}
	
	.hxToast .out-btn {
		height: 96rpx;
		line-height: 96rpx;
		text-align: center;
		border-top: 2rpx solid #e5e5e5;
		font-size: 36rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(22, 119, 255, 1);
	}
</style>
