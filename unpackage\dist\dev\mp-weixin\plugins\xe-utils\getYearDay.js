"use strict";
const plugins_xeUtils_staticDayTime = require("./staticDayTime.js");
const plugins_xeUtils_staticStrFirst = require("./staticStrFirst.js");
const plugins_xeUtils_helperGetYMDTime = require("./helperGetYMDTime.js");
const plugins_xeUtils_getWhatYear = require("./getWhatYear.js");
const plugins_xeUtils_toStringDate = require("./toStringDate.js");
const plugins_xeUtils_isValidDate = require("./isValidDate.js");
function getYearDay(date) {
  date = plugins_xeUtils_toStringDate.toStringDate(date);
  if (plugins_xeUtils_isValidDate.isValidDate(date)) {
    return Math.floor((plugins_xeUtils_helperGetYMDTime.helperGetYMDTime(date) - plugins_xeUtils_helperGetYMDTime.helperGetYMDTime(plugins_xeUtils_getWhatYear.getWhatYear(date, 0, plugins_xeUtils_staticStrFirst.staticStrFirst))) / plugins_xeUtils_staticDayTime.staticDayTime) + 1;
  }
  return NaN;
}
exports.getYearDay = getYearDay;
