<template>
  <view class="referral">
    <doc-info
      @docInfo="getDocInfo"
      :docId="pageParam.docId"
      :deptId="pageParam.deptId"
      pageType="service"
    ></doc-info>
    <view class="custom">
      <view class="serviceInfo">
        <view class="tips">提示</view>
        <view class="tipsInfo"
          >根据您的病情给您转诊到{{ pageParam.docName }}医生。</view
        >
        <view class="serviceContent">
          <image src="../../static/images/chat/server.png" mode=""></image>
          <text>转诊服务</text>
          <text class="price"
            >￥{{ docDetail.referralPrice || '0' }}/{{ visitDuration }}小时
          </text>
        </view>
      </view>
      <view class="serviceDesp">
        <view class="title">服务说明</view>
        <view>
          互联网诊疗仅适用常见疾病、慢性病复诊患者，急重诊患者请前往实体医疗机构就诊。
          复诊时必须提供包含诊断的实体机构的病历资料
          针对同诊断复诊。复诊患者，医生将根据您的实际情况辩证开方、给出调理建议。
        </view>
        <view>
          在线复诊过程中，若由于您无法提供实体医疗机构
          的诊段证明，或医生无法在线得出您和您之前线下实体机构相同的诊断而无法为您开出处方和诊断的情况，将不会退还复诊费用
        </view>
      </view>

      <view class="agreeInfo">
        <checkbox
          value="val"
          @click="changeCheck"
          :checked="agreeVal"
          color="#fff"
          style="transform: scale(0.8)"
        />
        <text>同意</text
        ><text class="protocol" @click="openProtocol(8)"
          >《用户知情同意书》</text
        >
      </view>

      <view class="bottomView" v-if="pageParam.isSuccess == '0'">
        <!-- <view class="payBtn" @click="getPayInfo">
					立即支付
				</view> -->

        <FooterButton @click="getPayInfo">立即支付</FooterButton>
      </view>
    </view>
  </view>
</template>

<script>
import { Toast } from '@/common/js/pay.js';
import FooterButton from '@/components/footer_button/button.vue';
import DocInfo from '@/components/docInfo/docInfo';
import {
  findPatientByPatientId,
  getPrIdByReferral,
  saveRegister,
} from '@/api/chatCardDetail';
import {
  addCollectDoctor,
  addProsignagreement,
  findVisitAgreement,
  getIsExist,
  getRegisterPayInfo,
  queryRegisterPayStatus,
} from '@/api/base';
import date from '@/utils/date';
import * as myJsTools from '@/common/js/myJsTools';
import myTools from '@/utils/myJsTools';
import { getReceiptWay } from '@/api/order';
import { saveMedicalAuthorizeReferral } from '@/api/chat';

let num = 3;
export default {
  name: 'referral',
  components: {
    DocInfo,
    FooterButton,
  },
  data() {
    return {
      agreeVal: true,
      pageParam: '',
      val: '',
      userInfo: {}, //用户协议
      priceInfo: {}, //价格服务协议
      registerInfo: {}, //自定义服务接口返回信息
      docDetail: {}, //医生信息
      visitDuration: '', //出诊时长
      detail: '',
      priceList: '',
      ghId: '',
      payStatus: false,
      newRegId: '', //生成挂号信息之后的新的挂号id
      payList: [],
      payItem: '',
    };
  },
  async onLoad(option) {
    let param = JSON.parse(option.param);
    this.pageParam = param;
    this.visitDuration = myTools.getItem('inquiry_duration') || 24;
    await this.getPatientInfo();
    await this.getPrId();
    this.getVisitAgreement();
    this.getPayList();
  },
  methods: {
    // 获取支付方式
    async getPayList() {
      let hosId = uni.getStorageSync('hosId');
      let res = await getReceiptWay({
        subjectId: hosId,
      });
      if (res.code != 20000) return;
      this.payList = res.data;
    },

    //查看协议
    openProtocol(type) {
      uni.navigateTo({
        url: '/pages/protocol/index?type=' + type,
      });
    },

    //从组件获取医生信息
    getDocInfo(val) {
      this.docDetail = val;
    },

    // 获取就诊人信息
    async getPatientInfo() {
      let res = await findPatientByPatientId({
        patientId: this.pageParam.patientId,
      });
      this.registerInfo = res.data;
    },

    // 获取病情资料id
    async getPrId() {
      let res = await getPrIdByReferral({
        referralId: this.pageParam.referralId,
      });
      this.registerInfo.prId = res.data.prId;
    },

    // 获取协议信息
    async getVisitAgreement() {
      uni.showLoading({
        mask: true,
      });
      try {
        let res = await findVisitAgreement({
          agreementType: '1',
        });
        this.userInfo = res.data;
        uni.hideLoading();
      } catch (error) {
        uni.hideLoading();
      }
    },

    //改变checkbox状态
    changeCheck() {
      this.agreeVal = !this.agreeVal;
    },

    //获取支付信息
    async getPayInfo() {
      if (this.agreeVal) {
        let registerInfo = this.registerInfo;
        let docDetail = this.docDetail;
        registerInfo.isReferral = '1';
        registerInfo.referralId = this.pageParam.referralId;
        registerInfo.apw = 'a';
        registerInfo.deptId = this.pageParam.deptId;
        registerInfo.deptName = docDetail.deptName;
        registerInfo.details = [
          {
            isMedicare: '0',
            priceDetailName: '转诊费',
            totalPay: docDetail.referralPrice || '0.00',
            priceDetailId: '',
          },
        ];
        registerInfo.dntName = docDetail.dntName;
        registerInfo.docId = docDetail.docId;
        registerInfo.docImg = docDetail.docImg;
        registerInfo.docName = docDetail.docName;
        registerInfo.docTel = docDetail.telNo;
        registerInfo.visitDate = date.getFormatDate('-');
        registerInfo.visitDuration = this.visitDuration;
        registerInfo.visitTypeCode = '1';
        registerInfo.visitTypeName = '图文';
        registerInfo.week = date.getWeekByNow();
        registerInfo.isMedicare = '0';
        registerInfo.isSubsequent = '1';
        registerInfo.medicareType = '';
        registerInfo.regSource = '1';
        // 获取协议id
        let userInfo = this.userInfo;
        let list = {
          agreementId: userInfo.agreementId,
          agreementNama: userInfo.agreementName,
          agreementVersions: userInfo.agreementVersions,
          patientId: this.pageParam.patientId,
        };
        let res = await addProsignagreement(list);
        registerInfo.psaId = res.data.uuid;
        this.registerInfo = registerInfo;
        this.saveRegisterInfo();
      } else {
        Toast('请勾选协议');
      }
    },

    //生成挂号信息
    async saveRegisterInfo() {
      let registerInfo = this.registerInfo;
      let res = await saveRegister(registerInfo);
      let { reg, details } = res.data;
      let infoDetail = this.registerInfo;
      infoDetail.regId = reg.regId;
      infoDetail.cost = reg.selfPay;
      infoDetail.addTime = myJsTools.formatTime(new Date());
      uni.setStorageSync('infoDetail', infoDetail);
      this.newRegId = reg.regId;
      this.detail = reg;
      this.priceList = details;
      this.ghId = reg.ghId;
      this.selePay();
    },

    // 选择支付方式
    selePay() {
      let that = this;
      if (!this.payList.length) {
        Toast('当前商户还没有支付方式');
        return false;
      }
      let money = this.docDetail.referralPrice || 0;
      // 如果无需支付
      if (money == 0) {
        this.payItem = this.payList[0];
        this.getPay();
        return;
      }

      // 如果只有一种支付方式
      if (this.payList.length == 1) {
        this.payItem = this.payList[0];
        if (this.payItem.receiptType == 1) {
          this.getPay('wx');
        } else {
          this.getPay();
        }
        return;
      }

      // 选择支付方式
      uni.showActionSheet({
        itemList: ['微信支付', '支付宝支付'],
        success: function(res) {
          let index = res.tapIndex;
          // 微信支付
          if (index == 0) {
            that.payList.forEach((v) => {
              if (v.receiptType == 1) {
                that.payItem = v;
              }
            });
            if (!that.payItem) {
              uni.showModal({
                content: '暂不支持微信支付',
                showCancel: false,
              });
              return;
            }
            that.getPay('wx');
            return;
          } else {
            that.payList.forEach((v) => {
              if (v.receiptType == 2) {
                that.payItem = v;
              }
            });
            if (!that.payItem) {
              uni.showModal({
                content: '暂不支持支付宝支付',
                showCancel: false,
              });
              return;
            }
            that.getPay();
            return;
          }
        },
      });
    },

    // 点击支付
    getPay(type) {
      let me = this;
      let money = this.docDetail.referralPrice || 0;
      let payType;
      if (money > 0) {
        if (type == 'wx') {
          payType = 1;
        } else {
          payType = 2;
        }
      } else if (money == 0) {
        payType = '0';
      } else {
        Toast('金额不能小于0');
      }

      uni.showLoading({
        mask: true,
      });

      var para = {
        callId: this.payItem.appid,
        ghId: this.ghId,
        openid: uni.getStorageSync('wxInfo').openId,
        payType: payType,
      };

      getRegisterPayInfo(para)
        .then((response) => {
          uni.hideLoading();
          // 无需支付，返回成功，直接跳转
          if (response.data.success && response.data.success == '1') {
            uni.removeStorageSync('prId');
            me.getCollect();
            uni.reLaunch({
              url: '/pages/register/thatDayRegister/payState/index?flag=1',
            });
            return;
          }
          let info = response.data;

          if (type == 'wx') {
            // 微信支付
            this.wxPay(info);
            return;
          } else {
            uni.navigateTo({
              url:
                '/pages/pay/pay?price=' +
                money +
                '&ghId=' +
                this.ghId +
                '&url=' +
                btoa(info.url),
            });
          }
        })
        .catch(() => {
          uni.hideLoading();
        });
    },

    saveReferral() {
      let medicalAuthorizeReferralDTO = [
        {
          authorizeStatus: '2',
          authorizeType: '2',
          docId: this.pageParam.oldDocId,
          patientId: this.pageParam.patientId,
          regId: this.newRegId,
        },
        {
          authorizeStatus: '2',
          authorizeType: '2',
          docId: this.pageParam.docId,
          patientId: this.pageParam.patientId,
          regId: this.pageParam.regId,
        },
      ];
      saveMedicalAuthorizeReferral({
        medicalAuthorizeReferralDTO: medicalAuthorizeReferralDTO,
      });
    },

    // 微信支付
    wxPay(info) {
      let that = this;
      WeixinJSBridge.invoke('getBrandWCPayRequest', info, (res) => {
        if (res.err_msg == 'get_brand_wcpay_request:ok') {
          that.getState();
        } else {
          Toast('您已取消订单');
        }
      });
    },

    // 查询支付状态
    async getState() {
      let me = this;
      if (num <= 0) {
        me.goBack();
        return;
      }
      try {
        let res = await queryRegisterPayStatus({
          ghId: me.ghId,
        });
        num--;
        if (!res.data) {
          setTimeout(me.getState, 3000);
          return;
        }
        if (res.data.regStatus == '2') {
          uni.removeStorageSync('ghId');
          this.payStatus = true;
          me.getCollect();
          uni.reLaunch({
            url: '/pages/register/thatDayRegister/payState/index?flag=1',
          });
          return;
        }
        setTimeout(() => {
          me.getState();
        }, 3000);
        me.payStatus = false;
      } catch (error) {}
    },

    // 收藏医生
    async getCollect() {
      let res = await getIsExist({
        docId: this.docDetail.docId,
        openid: uni.getStorageSync('wxInfo').openId,
      });
      if (res.data != 1) {
        let obj = {
          docId: this.docDetail.docId,
          userId: uni.getStorageSync('userId'),
          openid: uni.getStorageSync('wxInfo').openId,
          appid: uni.getStorageSync('appId'),
        };
        await addCollectDoctor(obj);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.custom {
  padding: 40rpx 32rpx 200rpx 32rpx;
}

.serviceInfo {
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0px 0px 4rpx rgba(0, 0, 0, 0.1);
  padding: 24rpx 32rpx;

  .tips {
    color: #333333;
    font-size: 28rpx;
    line-height: 40rpx;
  }

  .tipsInfo {
    margin-top: 24rpx;
    color: #999999;
    font-size: 28rpx;
    line-height: 40rpx;
    padding-bottom: 24rpx;
    border-bottom: 0.5px solid #ebebeb;
  }

  .serviceContent {
    position: relative;
    color: #333333;
    font-size: 28rpx;
    display: flex;
    padding-top: 24rpx;
    align-items: center;
    font-weight: 600;

    uni-image {
      width: 80rpx;
      height: 80rpx;
      margin-right: 24rpx;
    }
  }
}

.serviceDesp {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 26rpx 32rpx;
  font-size: 28rpx;
  line-height: 40rpx;
  margin-top: 24rpx;
  color: #999999;

  .title {
    color: #333333;
    margin-bottom: 16rpx;
  }
}

.bottomView {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
}

.agreeInfo {
  font-size: 28rpx;
  color: #333333;
  text-align: center;
  padding: 22rpx 0;

  .protocol {
    @include font_theme;
  }

  ::v-deepuni-checkbox .uni-checkbox-input.uni-checkbox-input-checked {
    color: #fff;
    @include bg_theme;
    @include border_theme;
  }
}

.payBtn {
  @include bg_theme;
  height: 98rpx;
  line-height: 98rpx;
  color: #ffffff;
  font-size: 36rpx;
  width: 100%;

  text-align: center;
}

.price {
  color: #999999;
  font-size: 22rpx;
  position: absolute;
  right: 36rpx;
  top: 48rpx;
}
</style>
