<template>
  <view>
    <view class="lineVisit">
      <view class="tips">
        <text class="line"></text>
        <text>点击选择您要预约的日期</text>
        <view class="tagTips" v-if="false">
          <text class="whiteBor"></text><text>医院</text
          ><text class="blackBor"></text><text>个人</text>
        </view>
      </view>
      <uni-calendar
        v-if="hackReset"
        :insert="true"
        :lunar="false"
        :date="defaultDate"
        :start-date="nowDate"
        :end-date="endDate"
        :showMonth="false"
        :selected="selected"
        @change="change"
        @monthSwitch="changeMonth"
      />
      <view class="tips">
        <text class="line"></text>
        <text>请选择您要预约的时间段，在该时间内进行签到</text>
      </view>

      <view
        class="visitShow"
        v-if="subsequent.length > 0 || inquiryWay.length > 0"
      >
        <view class="tabs">
          <view
            class="tab"
            :class="active == 0 ? 'active' : ''"
            @click="active = 0"
          >
            <text>健康咨询</text>
          </view>
          <view
            class="tab"
            :class="active == 1 ? 'active' : ''"
            @click="active = 1"
          >
            <text>复诊挂号</text>
          </view>
        </view>

        <view class="zxList" v-if="active == 0">
          <block v-for="(item, index) in inquiryWay">
            <view
              class="zxListChild"
              :key="index"
              @click="appoint(item)"
              v-if="index < 3 || inquiryWayMore"
            >
              <text class="times"
                >{{ item.startTimeShow }}-{{ item.endTimeShow }}</text
              >
              <text class="visitType">{{ item.visitTypeName }}</text>
              <text class="num" v-if="item.limitNum > 0"
                >剩余人数：{{ item.limitNum }}</text
              >
              <text class="num red" v-if="item.limitNum == 0">已约满</text>
            </view>
          </block>
          <view class="empty" v-if="inquiryWay.length == 0">
            医生在今天暂无咨询排班，请选择其他时间
          </view>
          <view
            v-if="inquiryWay.length > 3 && !inquiryWayMore"
            class="moreLook"
            @click="inquiryWayMore = true"
          >
            查看更多
          </view>
        </view>

        <view class="fzist" v-if="active == 1">
          <block v-for="(item, index) in subsequent">
            <view
              class="fxListChild"
              :key="index"
              @click="appoint(item)"
              v-if="index < 3 || subsequent"
            >
              <text class="times"
                >{{ item.startTimeShow }}-{{ item.endTimeShow }}</text
              >
              <text class="visitType">{{ item.visitTypeName }}</text>
              <text class="num" v-if="item.limitNum > 0"
                >剩余人数：{{ item.limitNum }}</text
              >
              <text class="num red" v-if="item.limitNum == 0">已约满</text>
            </view>
          </block>
          <view class="empty" v-if="subsequent.length == 0">
            医生在今天暂无咨询排班，请选择其他时间
          </view>
          <view
            v-if="subsequent.length > 3 && !subsequentMore"
            class="moreLook"
            @click="subsequentMore = true"
          >
            查看更多
          </view>
        </view>
      </view>
      <view class="empty" v-else> 医生在今天暂无排班，请选择其他时间 </view>
    </view>
  </view>
</template>

<script>
import { Toast } from '@/common/js/pay.js';
import uniCalendar from '@/components/uni-calendar/uni-calendar.vue';
import { getDocAllVisitReal } from '@/api/chatCardDetail';
import { getReserveFlag } from '@/api/appoint.js';
import { getConfigInfo, findDoctorByID } from '@/api/base.js';
import myJsTools from '@/common/js/myJsTools.js';
export default {
  props: ['pageParam'],
  components: {
    uniCalendar,
  },
  data() {
    return {
      appointInfo: {},
      visitDates: [],
      visitDate: '',
      nowDate: '',
      selected: [],
      subsequent: '',
      subsequentAll: '',
      subsequentMore: false,
      inquiryWayAll: '',
      inquiryWay: '',
      inquiryWayMore: false,
      active: 0,
      endDate: '',
      defaultDate: '',
      hackReset: false,
      docInfo: {},
    };
  },
  created() {
    this.getReserveFlagFun();
    this.nowDate = myJsTools.getDate('day');
    this.defaultDate = this.nowDate;
    this.endDate = myJsTools.getDate('day', 7);
    this.getDocInfo();
  },
  methods: {
    // 获取医生信息
    async getDocInfo() {
      let res = await findDoctorByID({
        docId: this.pageParam.docId,
      });
      let data = res.data;
      if (data.lableName) {
        data.docLable = data.lableName.split(',');
      }
      if (data.docImg) {
        myJsTools.downAndSaveImg(data.docImg, (url) => {
          this.docImg = url;
        });
      }
      this.docInfo = Object.assign({}, data);
    },
    change(e) {
      let fulldate = e.fulldate;
      this.inquiryWay = [];
      this.subsequent = [];
      this.visitDate = fulldate;
      let visitDates = this.visitDates;
      for (let i = 0; i < visitDates.length; i++) {
        if (fulldate == visitDates[i]) {
          this.inquiryWay = [];
          this.subsequent = [];
          this.visitDate = visitDates[i];
          this.getInquiryWay();
          this.getSubsequent();
        }
      }
    },
    changeMonth(e) {},

    async getReserveFlagFun() {
      let appointInfo = {
        docId: this.pageParam.docId,
      };
      this.selected = [];
      let res = await getReserveFlag(appointInfo);
      let data = res.data;
      if (!data || data.length == '0') {
        this.hackReset = true;
        return;
      }
      this.visitDate = this.defaultDate = data[0].visit_date;
      this.hackReset = false;
      this.$nextTick(() => {
        this.hackReset = true;
      });

      let visitDates = [];
      for (let i = 0; i < data.length; i++) {
        visitDates.push(data[i].visit_date);
        if (this.nowDate == data[i].visit_date) {
          this.visitDate = data[i].visit_date;
          // this.getInquiryWay();
          // this.getSubsequent();
        }
        this.selected.push({
          date: data[i].visit_date,
          info: '预约',
          data: {
            custom: '自定义信息',
            name: '自定义消息头',
          },
        });
      }
      this.visitDates = visitDates;

      this.appointInfo = appointInfo;
      this.getInquiryWay();
      this.getSubsequent();
    },
    async getInquiryWay() {
      this.inquiryWay = [];
      let queryInfo = {
        consultationCode: 0,
        docId: this.pageParam.docId,
        visitDate: this.visitDate,
        visitTypeCode: '',
      };
      let res = await getDocAllVisitReal(queryInfo);
      let data = res.data;
      let list = [];
      for (let i = 0; i < data.length; i++) {
        let obj = data[i];
        obj.startTimeShow = obj.startTime.slice(0, 5);
        obj.endTimeShow = obj.endTime.slice(0, 5);
        list.push(obj);
      }
      this.inquiryWayAll = list;
      this.inquiryWay = list;
    },
    async getSubsequent() {
      this.subsequent = [];
      let queryInfo = {
        consultationCode: 1,
        docId: this.pageParam.docId,
        visitDate: this.visitDate,
        visitTypeCode: '',
      };
      let res = await getDocAllVisitReal(queryInfo);
      let data = res.data;
      let list = [];
      for (let i = 0; i < data.length; i++) {
        let obj = data[i];
        obj.startTimeShow = obj.startTime.slice(0, 5);
        obj.endTimeShow = obj.endTime.slice(0, 5);
        list.push(obj);
      }
      this.subsequentAll = list;
      this.subsequent = list;
    },
    async getWholeArg() {
      // 获取全局参数
      let res = await getConfigInfo();
      this.$store.commit('setWholeArg', res.data);
    },
    // 点击预约
    appoint(evt) {
      if (evt.limitNum == 0) return;
      // 当前日期
      let now = new Date(this.nowDate).getTime();
      // 当前点击日期
      let item = new Date(evt.visitDate).getTime();

      if (item < now) {
        Toast('当前时间不可预约');
        return;
      }

      // 如果预约是当天
      if (item == now) {
        let h = new Date().getHours();
        let m = new Date().getMinutes();
        let end = evt.endTimeShow.split(':');

        // 当前
        let n = h * 60 + m;
        // 选择
        let s = end[0] * 60 + Number(end[1]);

        // 如果当前时间 大于预约结束时间
        if (n >= s) {
          Toast('当前时间不可预约');
          return;
        }
      }

      this.getWholeArg();
      let msg = evt;
      let num = this.active;
      evt.type = num;
      evt.consultationCode = num;
      uni.setStorageSync('appointReserveInfo', evt);
      if (msg.limitNum == 0) {
        Toast('该医生已无号');
      } else {
        let infoDetail = {
          type: num,
          docId: this.docInfo.docId,
          docName: this.docInfo.docName,
          visitDuration: msg.visitDuration,
          visitPrice: msg.visitPrice,
          docProf: this.docInfo.docProf,
          deptName: this.docInfo.deptName,
          deptId: this.docInfo.deptId,
          docImg: this.docInfo.docImg,
          docImgCopy: this.docInfo.docImgCopy,
          docTel: this.docInfo.telNo,
          visitTypeCode: msg.visitTypeCode,
          visitTypeName: msg.visitTypeName,
          week: msg.week || '',
          apw: msg.apw || '',
          visitDate: msg.visitDate || '',
          docLable: this.docInfo.docLable,
          details: '',
          isSwitch: msg.isSwitch,
          vrId: msg.vrId || '',
          dntId: this.docInfo.dntId,
          dntName: this.docInfo.dntName,
        };
        if (
          infoDetail.visitTypeCode == '2' ||
          infoDetail.visitTypeCode == '3' ||
          infoDetail.visitTypeCode == '4'
        ) {
          infoDetail.consultationCode = infoDetail.type;
          uni.setStorageSync('appointInfoDetail', infoDetail);
          uni.navigateTo({
            url: '/pages/register/appointRegister/rulesPage/index',
          });
        } else {
          uni.setStorageSync('infoDetail', infoDetail);
          uni.navigateTo({
            url: '/pages/register/thatDayRegister/rulesPages/index',
          });
        }
      }
    },
  },
};
</script>

<style scoped lang="scss">
.lineVisit {
  padding-bottom: 30rpx;
  .tips {
    color: #333333;
    font-size: 28rpx;
    padding: 26rpx 32rpx;
    background: #ffffff;
    display: flex;
    align-items: center;
    position: relative;

    .line {
      width: 6rpx;
      height: 28rpx;
      margin-right: 16rpx;
      display: inline-block;
      @include bg_theme;
      border-radius: 2rpx;
    }

    .tagTips {
      position: absolute;
      right: 32rpx;
      top: 26rpx;

      .blackBor,
      .whiteBor {
        border-radius: 50%;
        width: 16rpx;
        height: 16rpx;
        border: 1px solid #cacaca;
        display: inline-block;
        margin-right: 6rpx;
        background: #ffffff;
      }

      .blackBor {
        margin-left: 56rpx;
        background: #000000;
        border: 1px solid #000000;
      }
    }
  }

  /* 日历样式 */
  ::v-deep.uni-calendar {
    width: 100%;
    overflow: hidden;
  }

  ::v-deep .uni-calendar-item--isDay-text span {
    // color: #333;
  }

  ::v-deep.uni-calendar__backtoday {
    display: none;
  }

  ::v-deep.uni-calendar__content {
    width: 100%;
    height: 100%;
  }

  ::v-deep.uni-calendar__box {
    width: 100%;
    height: 100%;
  }

  ::v-deep.uni-calendar-item__weeks-box-item {
    width: 48px;
    height: 48px;
  }

  ::v-deep .uni-calendar-item--isDay .uni-calendar-item--extra {
    color: #fff;
  }

  ::v-deep.uni-calendar-item--checked {
    background: #ff5050;

    .uni-calendar-item__weeks-box-item {
      background: inherit !important;
    }
  }

  ::v-deep.uni-calendar-item--isDay {
    background: #ff5050;
  }

  .visitShow {
    margin: 44rpx 32rpx 32rpx;
    box-shadow: 0 0 4rpx rgba(0, 0, 0, 0.1);
    border-radius: 16rpx;
    padding: 0 32rpx 1rpx;
    background: #ffffff;

    .tabs {
      display: flex;
      line-height: 48rpx;
      color: #666666;
      font-size: 34rpx;
      text-align: center;

      .tab {
        flex: 1;
        padding-top: 16rpx;
        position: relative;
      }

      .active {
        text:first-child {
          @include font_theme;
          display: inline-block;
          padding-bottom: 20rpx;
          @include border_theme(3px, bottom);
        }
      }
    }

    .zxList,
    .fzist {
      color: #333333;
      font-size: 32rpx;

      .zxListChild,
      .fxListChild {
        width: 100%;
        @include flex(lr);
        height: 120rpx;
        border-bottom: 0.5px solid #ebebeb;
      }

      .zxListChild:first-child,
      .fxListChild:first-child {
        padding-top: 0;
      }

      .zxListChild:last-child,
      .fxListChild:last-child {
        border: none;
      }

      .times {
        min-width: 200rpx;
        flex: none;
      }

      .visitType {
        display: inline-block;
        width: 90rpx;
        padding: 6rpx 0;
        flex: none;
        @include flex;
        color: #ffffff;
        @include bg_theme;
        border-radius: 8rpx;
        text-align: center;
        font-size: 24rpx;
      }

      .yyType {
        background: #23b067;
      }

      .num {
        font-size: 28rpx;
        padding-left: 20rpx;
        flex: 1;
        text-align: center;
      }

      .btn {
        display: inline-block;
        width: 130rpx;
        @include flex;
        height: 64rpx;
        color: #ffffff;
        font-size: 24rpx;
        border-radius: 32rpx;
        margin-left: 22rpx;
        @include linear;
        flex: none;
      }
    }

    .moreLook {
      background: #ffffff;
      @include border_theme;
      @include font_theme;
      font-size: 36rpx;
      margin-top: 20rpx;
      text-align: center;
      height: 92rpx;
      line-height: 92rpx;
      border-radius: 46rpx;
    }
  }

  .empty {
    color: #999999;
    font-size: 24rpx;
    text-align: center;
    height: 150rpx;
    @include flex;
    margin-bottom: 30rpx;
  }
}
</style>
