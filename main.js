import App from './App'
import './interceptor'
import { createSSRApp } from 'vue'
import store from './store'
import XEUtils from '@/plugins/xe-utils'
import UniImage from '@/components/common/UniImage.vue'
import WebIM from '@/utils/WebIM'
//解决控制台 touchstart 事件警告
import 'default-passive-events'
export function createApp() {
	const app = createSSRApp(App)
	app.use(store)
	app.config.globalProperties.$xeu = XEUtils
	app.config.globalProperties.$im = WebIM.default
	uni.$xeu = XEUtils
	uni.$im = WebIM.default

	// 全局注册 UniImage 组件
	app.component('UniImage', UniImage)

	return {
		app
	}
}