<template>
  <view class="example-container">
    <view class="section">
      <text class="section-title">基础用法</text>
      
      <!-- 使用 fileId -->
      <view class="example-item">
        <text class="label">fileId 转换:</text>
        <UniImage 
          :src="fileId" 
          :width="200" 
          :height="200"
          custom-class="example-image"
        />
      </view>
      
      <!-- 使用完整 URL -->
      <view class="example-item">
        <text class="label">完整 URL:</text>
        <UniImage 
          :src="imageUrl" 
          :width="200" 
          :height="200"
          custom-class="example-image"
        />
      </view>
    </view>

    <view class="section">
      <text class="section-title">高级功能</text>
      
      <!-- 点击预览 -->
      <view class="example-item">
        <text class="label">点击预览:</text>
        <UniImage 
          :src="fileId" 
          :width="200" 
          :height="200"
          :preview="true"
          :preview-urls="previewList"
          custom-class="example-image"
        />
      </view>
      
      <!-- 自定义默认图片 -->
      <view class="example-item">
        <text class="label">自定义默认图片:</text>
        <UniImage 
          :src="invalidFileId" 
          :width="200" 
          :height="200"
          default-src="/static/images/placeholder.png"
          error-src="/static/images/error.png"
          custom-class="example-image"
        />
      </view>
    </view>

    <view class="section">
      <text class="section-title">不同尺寸模式</text>
      
      <view class="mode-grid">
        <view class="mode-item" v-for="mode in imageModes" :key="mode.value">
          <text class="mode-label">{{ mode.label }}</text>
          <UniImage 
            :src="imageUrl" 
            :width="150" 
            :height="150"
            :mode="mode.value"
            custom-class="mode-image"
          />
        </view>
      </view>
    </view>

    <view class="section">
      <text class="section-title">事件处理</text>
      
      <view class="example-item">
        <text class="label">带事件监听:</text>
        <UniImage 
          :src="fileId" 
          :width="200" 
          :height="200"
          custom-class="example-image"
          @click="handleImageClick"
          @load="handleImageLoad"
          @error="handleImageError"
          @load-success="handleLoadSuccess"
          @load-error="handleLoadError"
        />
      </view>
    </view>

    <view class="section">
      <text class="section-title">列表场景</text>
      
      <view class="image-list">
        <view 
          class="list-item" 
          v-for="(item, index) in imageList" 
          :key="index"
        >
          <UniImage 
            :src="item.fileId" 
            :width="100" 
            :height="100"
            :preview="true"
            :preview-urls="imageList.map(img => img.url)"
            custom-class="list-image"
          />
          <text class="list-text">{{ item.name }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'UniImageExample',
  data() {
    return {
      fileId: 'your-file-id-here',
      imageUrl: 'https://example.com/image.jpg',
      invalidFileId: 'invalid-file-id',
      previewList: [
        'https://example.com/image1.jpg',
        'https://example.com/image2.jpg',
        'https://example.com/image3.jpg'
      ],
      imageModes: [
        { label: 'scaleToFill', value: 'scaleToFill' },
        { label: 'aspectFit', value: 'aspectFit' },
        { label: 'aspectFill', value: 'aspectFill' },
        { label: 'widthFix', value: 'widthFix' },
        { label: 'heightFix', value: 'heightFix' }
      ],
      imageList: [
        { fileId: 'file-id-1', name: '图片1', url: 'https://example.com/1.jpg' },
        { fileId: 'file-id-2', name: '图片2', url: 'https://example.com/2.jpg' },
        { fileId: 'file-id-3', name: '图片3', url: 'https://example.com/3.jpg' }
      ]
    }
  },
  methods: {
    handleImageClick(e) {
      console.log('图片被点击:', e)
      uni.showToast({
        title: '图片被点击',
        icon: 'none'
      })
    },
    
    handleImageLoad(e) {
      console.log('图片加载成功:', e)
    },
    
    handleImageError(e) {
      console.log('图片加载失败:', e)
      uni.showToast({
        title: '图片加载失败',
        icon: 'none'
      })
    },
    
    handleLoadSuccess(url) {
      console.log('fileId 转换成功:', url)
      uni.showToast({
        title: 'fileId 转换成功',
        icon: 'success'
      })
    },
    
    handleLoadError(error) {
      console.log('fileId 转换失败:', error)
      uni.showToast({
        title: 'fileId 转换失败',
        icon: 'error'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.example-container {
  padding: 20rpx;
}

.section {
  margin-bottom: 40rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    display: block;
  }
}

.example-item {
  margin-bottom: 30rpx;
  
  .label {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 10rpx;
    display: block;
  }
}

.example-image {
  border-radius: 10rpx;
  border: 2rpx solid #eee;
}

.mode-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.mode-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .mode-label {
    font-size: 24rpx;
    color: #666;
    margin-bottom: 10rpx;
  }
}

.mode-image {
  border-radius: 8rpx;
  border: 1rpx solid #ddd;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.list-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .list-image {
    border-radius: 8rpx;
  }
  
  .list-text {
    font-size: 24rpx;
    color: #666;
    margin-top: 10rpx;
    text-align: center;
  }
}
</style>
