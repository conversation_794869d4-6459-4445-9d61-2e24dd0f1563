<template>
  <view class="component-container">
    <view class="wrapper" v-if="!showOld">
      <view class="block_box">
        <!-- 图标 -->
        <image class="avatar" src="/static/images/index/info.png" />
        <!-- 内容 -->
        <view class="info">
          <view class="text">问诊前需完善你的个人信息</view>

          <view class="user-info">
            <view class="input-box">
              <text>姓名</text>
              <input
                type="text"
                :value="form.nama"
                v-model="form.name"
                placeholder="请输入姓名"
              />
            </view>
            <view class="input-box">
              <text>身份证号</text>
              <input
                type="text"
                :value="form.cardId"
                v-model="form.cardId"
                placeholder="请输入身份证号"
                @blur="cardIdBlur"
              />
            </view>
            <view class="input-box">
              <text>性别</text>
              <input
                type="text"
                :value="form.sex"
                v-model="form.sex"
                placeholder="-"
                disabled
              />
            </view>
            <view class="input-box">
              <text>年龄</text>
              <input
                type="text"
                :value="form.age"
                v-model="form.age"
                placeholder="-"
                disabled
              />
            </view>
            <view class="input-box">
              <text>手机号</text>
              <input
                type="text"
                :value="form.telNo"
                v-model="form.telNo"
                placeholder="请输入手机号"
              />
            </view>
            <view class="input-box">
              <text>验证码</text>
              <input
                type="text"
                :value="form.code"
                v-model="form.code"
                placeholder="请输入验证码"
                maxlength="6"
              />
              <button type="default" class="sendBtn" @click="sendCode">
                {{ codeinfo.btnText }}
              </button>
            </view>
          </view>

          <view>
            <button
              class="saveBtn"
              :class="isClickSave ? 'active' : ''"
              @click="isClickSave && bindData()"
            >
              保存
            </button>
          </view>

          <view>
            <button class="cancelBtn" @click="canelBind">跳过</button>
          </view>
        </view>
      </view>
    </view>
    <!-- 其他 -->
    <view class="wrapper" v-if="!bindInfo">
      <view class="hx-block">
        <view class="title"> 网络异常，请退出程序，重新进入 </view>
        <view class="btn_footer">
          <view @click="hxConfirm">确定</view>
        </view>
      </view>
    </view>

    <!-- 弹窗二次验证码 -->
    <view class="wrapper" v-if="showOld">
      <view class="old_phone">
        <!-- 20014 20015 -->
        <view class="old_title" v-if="!other">
          <view
            >系统检测到您的用户信息已被注册，已向{{
              setPhone(oldTelNo)
            }}发送验证码</view
          >
          <view>请填写验证码，核实您的信息</view>
        </view>
        <!-- 20019 -->
        <view class="old_title" v-else>
          <view
            >系统检测到您的就诊人信息已被其他用户绑定或与您填写的信息不一致，已向{{
              setPhone(oldTelNo)
            }}发送验证码</view
          >
          <view>请填写验证码，核实您的信息</view>
        </view>
        <view class="old_input">
          <text class="input_text">验证码</text>
          <!-- 输入框 -->
          <input
            type="number"
            :value="newCode.length >= index + 1 ? newCode[index] : ''"
            v-for="(item, index) in 6"
            disabled
            @click="isFocus = true"
            :key="index"
          />
        </view>
        <!-- 隐藏输入框 -->
        <input
          type="number"
          password
          @blur="isFocus = false"
          class="hide_input"
          v-model="newCode"
          maxlength="6"
          :focus="isFocus"
        />
        <!-- 按钮 -->
        <view class="old_but">
          <button @click="sendBind">提交</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
// 引入视频通话
const emedia = require('@/utils/WebIM.js')['emedia'];
import regex from '@/common/js/regex.js';
import myJsTools from '@/common/js/myJsTools.js';
import {
  sendCaptcha,
  hisPatientLoginBindingUserInfo,
  updateIsServiceReminderStatus,
  updateHxidIsregistStatus,
} from '@/api/user.js';
import {
  patientLogOneDataChangeTelNo,
  patientLogTwoData,
  verifyPatient,
} from '@/api/base.js';
export default {
  props: {},
  data() {
    return {
      // 旧手机号
      oldTelNo: '',
      // 新验证码
      newCode: '',
      // 用户id
      userId: '',
      // 重复数据
      existedAppid: '',

      // 接口返回的身份证号
      enterIdNo: '',
      // 不保留的id
      repeatIdNoUserId: '',
      // 要保留的id
      repeatTelNoUserId: '',
      isFocus: false,
      showOld: false,
      form: {
        name: '',
        cardId: '',
        sex: '',
        age: '',
        telNo: '',
        code: '',
        sexCode: '',
      },
      bindInfo: true,
      codeinfo: {
        sendAuthCode: true,
        auth_time: 0,
        btnText: '发送验证码',
      }, //验证码时间及是否可点击
      isClickSave: true,
      // 20019
      other: '',
    };
  },
  mounted() {

  },
  methods: {
    setPhone(str) {
      return myJsTools.phone(str);
    },
    // 校验身份证号,并获取用户性别,年龄
    cardIdBlur(e) {
      let cardId = e.detail.value;
      let form = this.form;
      if (regex.idNoBlur(cardId)) {
        if (regex.disCriCard(cardId, 'sex') == 2) {
          form.sex = '女';
        } else {
          form.sex = '男';
        }
        form.sexCode = regex.disCriCard(cardId, 'sex');
        form.age = regex.disCriCard(cardId, 'age');
      }
    },
    // 获取验证码
    sendCode() {
      let telNo = this.form.telNo;
      if (regex.telBlur(telNo)) {
        // 如果正在倒计时 防止点击
        if (this.codeinfo.auth_time > 0) return;
        sendCaptcha({
          telNo,
        }).then((res) => {
          // 发送验证码,倒数读秒
          this.codeinfo.auth_time = 60;
          var auth_timetimer = setInterval(() => {
            this.codeinfo.auth_time--;
            this.codeinfo.btnText = this.codeinfo.auth_time + 's后重新发送';
            if (this.codeinfo.auth_time <= 0) {
              this.codeinfo.sendAuthCode = true;
              // 重置为0
              this.codeinfo.auth_time = 0;
              this.codeinfo.btnText = '重新发送';
              clearInterval(auth_timetimer);
            }
          }, 1000);
        });
      }
    },
    bindData() {
      let _this = this;
      if (
        regex.nameBlur(this.form.name) &&
        regex.idNoBlur(this.form.cardId) &&
        regex.telBlur(this.form.telNo) &&
        regex.codeBlur(this.form.code)
      ) {
        _this.isClickSave = false;
        uni.showLoading({
          mask: true,
        });

        let obj = {
          appid: uni.getStorageSync('appId'),
          openid: uni.getStorageSync('wxInfo').openId,
          headImgUrl: uni.getStorageSync('wxInfo').headimgurl,
          age: this.form.age,
          idNo: this.form.cardId,
          captcha: this.form.code,
          sexCode: this.form.sexCode,
          sex: this.form.sex,
          userName: this.form.name,
          telNo: this.form.telNo,
          patientImg: '',
        };
        hisPatientLoginBindingUserInfo(obj)
          .then((res) => {
            let data = res.data;
            // 正常流程
            if (res.code == 20000) {
              uni.hideLoading();
              this.bindSuccess(res);
              return;
            }
            // 身份一样，手机号不一样
            if (res.code == 20014) {
              uni.hideLoading();
              this.oldTelNo = data.oldTelNo;
              this.userId = data.userId;
              this.existedAppid = data.existedAppid;
              // 向旧手机发送验证码
              this.sendPhoneCode(data.oldTelNo);
              this.showOld = true;
              return;
            }
            // 两条重复信息
            if (res.code == 20015) {
              uni.hideLoading();
              this.oldTelNo = data.oldTelNo;
              this.userId = data.userId;
              this.enterIdNo = data.enterIdNo;
              this.repeatIdNoUserId = data.repeatIdNoUserId;
              this.existedAppid = data.existedAppid;
              this.repeatTelNoUserId = data.repeatTelNoUserId;
              // 用户输入的身份证号
              this.enterIdNo = data.enterIdNo;
              // 向旧手机发送验证码
              this.sendPhoneCode(data.oldTelNo);
              this.newCode = '';
              this.showOld = true;
              return;
            }

            // 20019情况
            if (res.code == 20019) {
              uni.hideLoading();
              this.other = res.data;
              this.oldTelNo = data.oldTelNo;
              // 向旧手机发送验证码
              this.sendPhoneCode(data.oldTelNo);
              this.newCode = '';
              this.showOld = true;
            }
          })
          .catch((err) => {
            _this.isClickSave = true;
          });
      }
    },

    // 状态 20000 正常流程
    bindSuccess(res) {
      // 存储userId
      uni.setStorageSync('userId', res.data.userId);
      let myUsername = res.data.userId.toLowerCase();
      uni.setStorageSync('myUsername', myUsername);
      this.$store.commit('setProPfInfo', res.data);
      // 隐藏弹窗
      this.showOld = false;
      if (uni.getStorageSync('initialFlag') && res.data.isServiceReminder) {
        if (res.data.isServiceReminder == 1) {
          updateIsServiceReminderStatus({
            userId: res.data.userId,
          }).then((res) => {
            let proPfInfo = uni.getStorageSync('proPfInfo');
            proPfInfo.isServiceReminder = '0';
            this.$store.commit('setProPfInfo', proPfInfo);
          });
        }
      }
      let hxidIsregist = res.data.hxidIsregist;
      if (hxidIsregist == 1) {
        this.WebIMLogin();
      } else {
        this.WebIMRegister();
      }
    },

    // 状态 20014 执行
    bindErrorOne(res) {
      // 参数
      let obj = {
        appid: uni.getStorageSync('appId'),
        openid: uni.getStorageSync('wxInfo').openId,
        headImgUrl: uni.getStorageSync('wxInfo').headimgurl,
        newTelNo: this.form.telNo,
        existedAppid: this.existedAppid,
        telNo: this.oldTelNo,
        userId: this.userId,
        captcha: this.newCode,
      };
      patientLogOneDataChangeTelNo(obj).then((res) => {
        if (res.code == 20000) {
          this.bindSuccess(res);
          return;
        }
        if (res.code == 20019) {
          uni.hideLoading();
          this.other = res.data;
          this.oldTelNo = res.data.oldTelNo;
          // 向旧手机发送验证码
          this.sendPhoneCode(res.data.oldTelNo);
          this.newCode = '';
          this.showOld = true;
        }
      });
    },

    // 状态 20015 执行
    bindErrorTwo() {
      // 参数
      let obj = {
        appid: uni.getStorageSync('appId'),
        openid: uni.getStorageSync('wxInfo').openId,
        headImgUrl: uni.getStorageSync('wxInfo').headimgurl,
        existedAppid: this.existedAppid,
        telNo: this.oldTelNo,
        captcha: this.newCode,
        enterIdNo: this.enterIdNo,
        repeatIdNoUserId: this.repeatIdNoUserId,
        repeatTelNoUserId: this.repeatTelNoUserId,
      };
      patientLogTwoData(obj).then((res) => {
        if (res.code == 20000) {
          this.bindSuccess(res);
          return;
        }
        if (res.code == 20019) {
          uni.hideLoading();
          this.other = res.data;
          this.oldTelNo = res.data.oldTelNo;
          // 向旧手机发送验证码
          this.sendPhoneCode(res.data.oldTelNo);
          this.newCode = '';
          this.showOld = true;
        }
      });
    },

    // 状态 20019 执行
    bindErrorThere() {
      this.other.appid = uni.getStorageSync('appId');
      this.other.captcha = this.newCode;
      verifyPatient(this.other).then((res) => {
        if (res.code != 20000) return;
        this.bindSuccess(res);
      });
    },

    // 按钮触发
    sendBind() {
      if (!regex.codeBlur(this.newCode)) return;
      // 20019
      if (this.other) {
        this.bindErrorThere();
        // 20015
      } else if (this.enterIdNo && this.repeatIdNoUserId) {
        this.bindErrorTwo();
        // 20014
      } else {
        this.bindErrorOne();
      }
      uni.showLoading({
        title: '',
      });
    },

    // 环信注册
    WebIMRegister() {
      let _this = this;
      let options = {
        username: uni.getStorageSync('userId'),
        password: uni.getStorageSync('userId'),
        nickname: uni.getStorageSync('userId'),
        appKey: _this.$im.config.appkey,
        success: function() {
          _this.updateHxidIsregistStatusFun();
        },
        error: function(err) {
          // 关闭绑定信息弹框,打开环信提示框
          uni.hideLoading();
          _this.bindInfo = false;
        },
        apiUrl: _this.$im.config.apiURL,
      };
      _this.$im.conn.registerUser(options);
    },

    updateHxidIsregistStatusFun() {
      let userId = uni.getStorageSync('userId');
      updateHxidIsregistStatus({
        userId: userId,
      }).then((res) => {
        // 更改用户环信状态成功
        this.WebIMLogin();
      });
    },

    // 环信登录
    WebIMLogin() {
      let _this = this;
      let options = {
        apiUrl: _this.$im.config.apiURL,
        user: uni.getStorageSync('userId'),
        pwd: uni.getStorageSync('userId'),
        grant_type: uni.getStorageSync('userId'),
        appKey: _this.$im.config.appkey,
        success: function(res) {
          // _this.getWholeArg();

          let memName =
            _this.$im.config.appkey + '_' + uni.getStorageSync('userId');
          // 进入会议前 必须调用
          emedia.mgr.setIdentity(memName, res.access_token);
          uni.hideLoading();
          _this.$emit('hideBindInfo');
        },
        error: function(err) {
          console.log('环信登录失败', err);
          uni.hideLoading();
          // 关闭绑定信息弹框,打开环信提示框
          _this.bindInfo = false;
        },
      };
      _this.$im.conn.open(options);
    },
    // 跳过  关闭弹框
    canelBind() {
      this.$emit('hideBindInfo');
      // this.WebIMLogin()
    },
    // 点击环信弹框确定按钮时，关闭弹框，并退出程序
    hxConfirm() {
      this.$emit('hideBindInfo');
      WeixinJSBridge.call('closeWindow');
    },
    // 单独发送验证码
    sendPhoneCode(tel) {
      sendCaptcha({ telNo: tel });
    },
  },
};
</script>

<style scoped lang="scss">
.wrapper {
  position: fixed;
  width: 100%;
  height: 100vh;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.5);
  @include flex(center);
  z-index: 999;

  // 容器
  .block_box {
    width: 600upx;
    border-radius: 16upx;
    background-color: #fff;
    padding: 0 24upx;
    box-sizing: border-box;

    // 头像
    .avatar {
      width: 170upx;
      height: 170upx;
      display: block;
      margin: -85upx auto 0;
    }

    // 内容
    .info {
      .text {
        color: $k-title;
        font-weight: 500;
        font-size: 28upx;
        text-align: center;
      }
    }
  }
}

// 旧手机验证码
.old_phone {
  width: 600upx;
  padding: 32upx;
  background-color: #fff;
  border-radius: 16upx;
  box-sizing: border-box;

  .old_title {
    font-size: 32upx;
    color: $k-title;
    text-align: center;
    padding: 20upx 0;
    line-height: 48upx;
  }

  .old_input {
    height: 84upx;
    border-radius: 8upx;
    @include flex;

    .input_text {
      font-size: 28upx;
      color: $k-title;
      font-weight: bold;
      padding-right: 20upx;
    }

    input {
      width: 36upx;
      height: 44upx;
      font-size: 32upx;
      color: $k-title;
      line-height: 44upx;
      font-weight: bold;
      text-align: center;
      margin-right: 20upx;
      border-radius: 4upx;
      background-color: $k-page-bg-color;
    }
  }

  .hide_input {
    // width: 0;
    height: 0;
    opacity: 0;
    position: relative;
    top: -60upx;
  }

  .old_but {
    padding-top: 32upx;

    button {
      width: 248upx;
      height: 68upx;
      border-radius: 34upx;
      @include bg_theme;
      color: #fff;
      @include flex;
      font-size: 32upx;
    }
  }
}

.user-info {
  margin-top: 56upx;
}

.input-box {
  width: 100%;
  @include flex(left);
  margin-bottom: 16upx;
  height: 62upx;
  background: #f8f8f8;
  border-radius: 4upx;
  padding: 0 24upx;
  box-sizing: border-box;

  text {
    width: 132rpx;
    flex: none;
    font-size: 28rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: $k-title;
  }

  input {
    flex: 1;
    padding-left: 20upx;
    font-size: 28upx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: $k-sub-title;
  }

  // 验证码按钮
  .sendBtn {
    flex: none;
    font-size: 28upx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    @include font_theme;
    line-height: 60upx;
    padding: 0;
    text-align: right;
  }
}

// 提交按钮
.saveBtn {
  width: 334upx;
  @include flex;
  color: #fff;
  font-size: 32upx;
  font-weight: 500;
  background: #f5f5f5;
  border-radius: 42upx;
  margin-top: 32upx;

  &.active {
    @include bg_theme;
    color: #fff;
  }
}

// 跳过按钮
.cancelBtn {
  width: 334upx;
  font-size: 28upx;
  color: $k-sub-title;
}

/* 环信弹框 */
.wrapper .hx-block {
  width: 606rpx;
  box-sizing: border-box;
  background: #fff;
  border-radius: 16upx;
  overflow: hidden;
  padding: 94rpx 40rpx 40rpx 40rpx;
}

.hx-block .title {
  padding-bottom: 64rpx;
  color: $k-title;
  font-size: 32upx;
  font-weight: 600;
  white-space: normal;
  text-align: center;
}

.hx-block .btn_footer {
  width: 100%;
  @include flex(center);
  justify-content: space-around;
}

.hx-block .btn_footer view {
  width: 242rpx;
  height: 68rpx;
  border-radius: 46rpx;
  color: #ffffff;
  font-weight: 600;
  @include bg_theme;
  font-size: 28rpx;
  text-align: center;
  line-height: 68rpx;
  box-sizing: border-box;
}

.hx-block .btn_footer .cancel {
  @include border_theme;
  @include font_theme;
  background: transparent;
  margin-right: 36rpx;
}
</style>
