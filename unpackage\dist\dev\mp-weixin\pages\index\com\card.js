"use strict";
const common_vendor = require("../../../common/vendor.js");
const api_order = require("../../../api/order.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  name: "Card",
  props: {
    list: {
      type: Array,
      default: () => []
    },
    unit: {
      type: [String, Number],
      default: 7
    }
  },
  data() {
    return {
      info: {}
    };
  },
  methods: {
    async toDetail(item) {
      this.info = { ...item };
      console.log("item", item);
      let {
        regId,
        callType,
        pliId,
        ppiId,
        businessId,
        status,
        paymentType,
        hosId,
        docId,
        patientId,
        docName,
        isOffLine
      } = item;
      patientId = patientId.toLocaleLowerCase();
      if (item.callType == 7)
        ;
      else {
        docId = docId.toLocaleLowerCase();
      }
      if (hosId)
        common_vendor.index.setStorageSync("hosId", hosId);
      let url;
      switch (callType) {
        case "1":
          if (status < 4 || isOffLine == 1) {
            url = "/pages/personalCenter/diagnosisRecord/detail?id=" + regId;
          } else {
            let obj = {
              docId,
              patientId,
              docName
            };
            this.$store.commit("setEmptyList", obj);
            let chatList = this.$store.getters.getChatList;
            let list = this.$store.getters.getChatListDoc;
            let id = patientId + "," + docId;
            let item2;
            this.$store.commit("SET_CHATLIST", []);
            if (Array.isArray(chatList)) {
              if (list.length) {
                for (let i = 0; i < list.length; i++) {
                  let v = list[i];
                  if (v.id == id) {
                    item2 = v;
                    break;
                  }
                }
              } else {
                await this.$store.dispatch("getChatListId", {
                  chatId: id
                });
                item2 = this.$store.getters.getChatList;
              }
            } else {
              await this.$store.dispatch("getChatListId", {
                chatId: id
              });
              item2 = this.$store.getters.getChatList;
            }
            const res = await api_order.getPatientChatSM({
              regId,
              userId: common_vendor.index.getStorageSync("userId")
            });
            item2.dataVal = res.data || {};
            let pars = {
              docId,
              docName,
              patientId,
              userId: common_vendor.index.getStorageSync("userId")
            };
            item2 = { ...item2, ...pars };
            console.log("item", item2);
            this.$store.commit("SET_CHATLIST", item2);
            common_vendor.index.setStorageSync("chatItem", item2.dataVal);
            let param = {
              docId
            };
            url = "/pages/chatList/chatDetail?param=" + JSON.stringify(param);
            if (this.info.groupId) {
              url = "/pages/chatList/scanChatDetail?param=" + JSON.stringify(param);
            }
          }
          break;
        case "7":
          if (status < 4 || isOffLine == 1) {
            url = "/pages/personalCenter/diagnosisRecord/detail?id=" + regId + `&flag=ksxf`;
          } else {
            let obj = {
              docId,
              patientId,
              docName
            };
            this.$store.commit("setEmptyList", obj);
            let chatList = this.$store.getters.getChatList;
            let list = this.$store.getters.getChatListDoc;
            let id = patientId + "," + docId;
            let item2;
            this.$store.commit("SET_CHATLIST", []);
            if (Array.isArray(chatList)) {
              if (list.length) {
                for (let i = 0; i < list.length; i++) {
                  let v = list[i];
                  if (v.id == id) {
                    item2 = v;
                    break;
                  }
                }
              } else {
                await this.$store.dispatch("getChatListId", {
                  chatId: id
                });
                item2 = this.$store.getters.getChatList;
              }
            } else {
              await this.$store.dispatch("getChatListId", {
                chatId: id
              });
              item2 = this.$store.getters.getChatList;
            }
            const res = await api_order.getPatientChatSM({
              regId,
              userId: common_vendor.index.getStorageSync("userId")
            });
            item2.dataVal = res.data || {};
            let pars = {
              docId,
              docName,
              patientId,
              userId: common_vendor.index.getStorageSync("userId")
            };
            item2 = { ...item2, ...pars };
            this.$store.commit("SET_CHATLIST", item2);
            common_vendor.index.setStorageSync("chatItem", item2.dataVal);
            let param = {
              docId
            };
            if (this.info.groupId) {
              url = "/pages/chatList/scanChatDetail?param=" + JSON.stringify(param);
            }
          }
          break;
        case "3":
          if (status == 0) {
            url = "/pages/prescription/prescriptionDetail?businessId=" + businessId;
          } else {
            url = "/pages/prescription/preDetail?businessId=" + businessId;
          }
          break;
        case "5":
          if (status == 1) {
            if (!paymentType) {
              url = "/pages/inspect/lisOrder?id=" + pliId;
            } else if (paymentType == 1) {
              url = "/pages/inspect/lisDetails?id=" + pliId;
            } else {
              url = "/pages/inspect/pay/lis?id=" + pliId;
            }
          } else {
            url = "/pages/inspect/lisDetails?id=" + pliId;
          }
          break;
        case "6":
          if (status == 1) {
            if (!paymentType) {
              url = "/pages/inspect/pacsOrder?id=" + ppiId;
            } else if (paymentType == 1) {
              url = "/pages/inspect/pacsDetails?id=" + ppiId;
            } else {
              url = "/pages/inspect/pay/pacs?id=" + ppiId;
            }
          } else {
            url = "/pages/inspect/pacsDetails?id=" + ppiId;
          }
          break;
        default:
          url = "";
          break;
      }
      if (!url)
        return;
      common_vendor.index.navigateTo({ url });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($props.list, (item, index, i0) => {
      return common_vendor.e({
        a: item.callType == 5
      }, item.callType == 5 ? {
        b: common_assets._imports_0$2
      } : {}, {
        c: item.callType == 6
      }, item.callType == 6 ? {
        d: common_assets._imports_1$2
      } : {}, {
        e: item.callType == 1
      }, item.callType == 1 ? {
        f: common_assets._imports_2
      } : {}, {
        g: item.callType == 7
      }, item.callType == 7 ? {
        h: common_assets._imports_2
      } : {}, {
        i: item.callType == 3
      }, item.callType == 3 ? {
        j: common_assets._imports_3
      } : {}, {
        k: common_vendor.t(item.patientName),
        l: common_vendor.t(item.statusName),
        m: item.callType == 5
      }, item.callType == 5 ? {
        n: common_vendor.t(item.lisItemName)
      } : {}, {
        o: item.callType == 6
      }, item.callType == 6 ? {
        p: common_vendor.t(item.pacsTypeName)
      } : {}, {
        q: item.callType == 1 || item.callType == 7
      }, item.callType == 1 || item.callType == 7 ? {
        r: common_vendor.t(item.docName),
        s: common_vendor.t(item.deptName)
      } : {}, {
        t: item.callType == 5 || item.callType == 6
      }, item.callType == 5 || item.callType == 6 ? {
        v: common_vendor.t(item.orgName)
      } : {}, {
        w: item.callType == 1 || item.callType == 3 || item.callType == 7
      }, item.callType == 1 || item.callType == 3 || item.callType == 7 ? {
        x: common_vendor.t(item.hosName)
      } : {}, {
        y: item.callType == 3
      }, item.callType == 3 ? {
        z: common_vendor.t(item.patientName)
      } : {}, {
        A: item.callType == 5 || item.callType == 6
      }, item.callType == 5 || item.callType == 6 ? {
        B: common_vendor.t(item.docName),
        C: common_vendor.t(item.deptName)
      } : {}, {
        D: common_vendor.t(item.receiveTypeName),
        E: item.status == 0
      }, item.status == 0 ? common_vendor.e({
        F: item.callType == 3
      }, item.callType == 3 ? {
        G: common_vendor.t($props.unit)
      } : {}) : {}, {
        H: item.status == 1
      }, item.status == 1 ? common_vendor.e({
        I: item.callType == 1
      }, item.callType == 1 ? {} : {}, {
        J: item.callType == 3
      }, item.callType == 3 ? {} : {}, {
        K: item.callType == 5
      }, item.callType == 5 ? {
        L: common_vendor.t($props.unit)
      } : {}, {
        M: item.callType == 6
      }, item.callType == 6 ? {
        N: common_vendor.t($props.unit)
      } : {}) : {}, {
        O: item.status == 2
      }, item.status == 2 ? common_vendor.e({
        P: item.callType == 1 || item.callType == 5 || item.callType == 6
      }, item.callType == 1 || item.callType == 5 || item.callType == 6 ? {
        Q: common_vendor.t(item.appointSignTime)
      } : {}, {
        R: item.callType == 3
      }, item.callType == 3 ? {} : {}) : {}, {
        S: item.status == 3
      }, item.status == 3 ? common_vendor.e({
        T: item.callType == 3
      }, item.callType == 3 ? {} : {}, {
        U: item.callType == 6 || item.callType == 5 && item.lisType == 1
      }, item.callType == 6 || item.callType == 5 && item.lisType == 1 ? {
        V: common_vendor.t(item.queueNumber),
        W: common_vendor.t(item.frontNum || 0)
      } : {}, {
        X: item.callType == 5 && item.lisType == 2
      }, item.callType == 5 && item.lisType == 2 ? {} : {}) : {}, {
        Y: item.status == 4
      }, item.status == 4 ? common_vendor.e({
        Z: item.callType == 1
      }, item.callType == 1 ? {
        aa: common_vendor.t(item.queueNumber),
        ab: common_vendor.t(item.frontNum || 0)
      } : {}, {
        ac: item.callType == 5 || item.callType == 6
      }, item.callType == 5 || item.callType == 6 ? {} : {}) : {}, {
        ad: item.status == 5
      }, item.status == 5 ? common_vendor.e({
        ae: item.callType == 1
      }, item.callType == 1 ? {} : {}) : {}, {
        af: item.status == 7
      }, item.status == 7 ? common_vendor.e({
        ag: item.callType == 5
      }, item.callType == 5 ? {
        ah: common_vendor.t(item.queueNumber),
        ai: common_vendor.t(item.frontNum || 0)
      } : {}) : {}, {
        aj: item.status == 8
      }, item.status == 8 ? common_vendor.e({
        ak: item.callType == 5
      }, item.callType == 5 ? {} : {}) : {}, {
        al: item.status == 9
      }, item.status == 9 ? common_vendor.e({
        am: item.callType == 5
      }, item.callType == 5 ? {} : {}) : {}, {
        an: index,
        ao: common_vendor.o(($event) => $options.toDetail(item), index)
      });
    })
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-ea5d7690"]]);
wx.createComponent(Component);
