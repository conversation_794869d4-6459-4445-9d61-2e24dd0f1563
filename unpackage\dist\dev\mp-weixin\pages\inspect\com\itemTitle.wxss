/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*
	字体颜色相关
*/
/*
	背景色 分割线色
*/
/*
	主题色
*/
/*
		渐变可调角度
*/
/*
	定义弹性布局
 */
/*
	单行文本溢出隐藏
*/
.itemTitle.data-v-19fbd750 {
  font-size: 32rpx;
  font-weight: bold;
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.itemTitle .title.data-v-19fbd750 {
  padding-left: 18rpx;
  position: relative;
  line-height: 30rpx;
  margin-right: 12rpx;
}
.itemTitle .title.data-v-19fbd750::before {
  content: "";
  display: block;
  width: 6rpx;
  height: 100%;
  background-color: #836aff;
  position: absolute;
  border-radius: 4rpx;
  left: 0;
  top: 0;
}
[data-theme=nx] .itemTitle .title.data-v-19fbd750::before {
  background-color: #107dff;
}
[data-theme=test] .itemTitle .title.data-v-19fbd750::before {
  background-color: #2db99d;
}