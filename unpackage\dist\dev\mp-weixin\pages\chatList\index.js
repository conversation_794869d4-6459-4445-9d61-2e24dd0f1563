"use strict";
const common_vendor = require("../../common/vendor.js");
const api_chat = require("../../api/chat.js");
const utils_date = require("../../utils/date.js");
const api_qcAi = require("../../api/qcAi.js");
const api_user = require("../../api/user.js");
const common_js_myJsTools = require("../../common/js/myJsTools.js");
const api_order = require("../../api/order.js");
const store_index = require("../../store/index.js");
const common_assets = require("../../common/assets.js");
const SysTemList = () => "./sysTemList.js";
let { screenHeight, screenWidth } = common_vendor.index.getSystemInfoSync();
const _sfc_main = {
  components: {
    SysTemList
  },
  data() {
    return {
      chatNum: "0",
      sysNum: "0",
      query: {
        isOffLine: 0
      },
      docList: [],
      list: [],
      listQuery: {
        page: 1,
        limit: 10,
        sopIds: "",
        pinyinName: ""
      },
      userId: "",
      // 要操作的消息
      item: "",
      nowChatList: [],
      // 定时器
      timer: "",
      // left
      pageX: 0,
      // top
      pageY: 0,
      // 显示弹窗
      showTips: false,
      // 系统图标
      sys_icon: store_index.store.getters.allEnv[store_index.store.getters.envKey].VUE_APP_SHARE,
      searchValue: "",
      groupChatList: [],
      total: 0,
      patientList: [],
      selectedPatientId: "all",
      aiMessages: [],
      aiQueryParams: {
        page: 1,
        limit: 20,
        total: 0
      }
    };
  },
  computed: {
    // 监听接诊退诊
    jzStatus() {
      return this.$store.getters.getJzStatus;
    },
    chatAll() {
      console.log("update---chat----", this.$store.getters.getGroupChatAllList);
      return this.$store.getters.getGroupChatAllList;
    }
  },
  watch: {
    list(n, o) {
      if (n.length > o.length) {
        this.getDocList();
      }
    },
    jzStatus(n, o) {
      if (!n)
        return;
      let path = this.$route.path;
      if (path != "/pages/chatList/index")
        return;
      this.getDocList();
    },
    chatAll() {
      if (!this.chatAll.length)
        return;
      console.log("update---chat----", this.chatAll);
      this.groupChatList.forEach((v) => {
        if (v.groupId == this.chatAll[this.chatAll.length - 1].to) {
          v.newMsg = this.chatAll[this.chatAll.length - 1].data;
        }
      });
    }
  },
  async onShow() {
    var _a;
    this.$store.commit("SET_CHATLIST", []);
    this.listQuery.sopIds = common_vendor.index.getStorageSync("patientIdList");
    this.userId = common_vendor.index.getStorageSync("userId");
    this.listQuery.page = 1;
    this.groupChatList = [];
    this.getChatListSM();
    this.chatId = "admin,admin";
    let obj = {
      chatId: this.chatId
    };
    await this.$store.dispatch("getChatListId", obj);
    const chatList = this.$store.getters.getChatList || { chatRecordList: [] };
    const unReadNum = (_a = chatList == null ? void 0 : chatList.chatRecordList) == null ? void 0 : _a.filter(
      (v) => v.status != "read"
    ).length;
    this.sysNum = unReadNum > 99 ? "99" : String(unReadNum || 0);
    if (this.query.isOffLine === 3) {
      this.aiQueryParams.page = 1;
      this.getAiMessages();
      this.getPatientList();
    }
  },
  onPullDownRefresh() {
    this.listQuery.page = 1;
    this.listQuery.pinyinName = "";
    this.docList = [];
    this.getChatListSM();
    if (this.query.isOffLine === 3) {
      this.aiQueryParams.page = 1;
      this.getAiMessages().then(() => {
        common_vendor.index.stopPullDownRefresh();
      });
    }
  },
  methods: {
    goToAiChat() {
      common_vendor.index.navigateTo({
        url: "/pages/aiAssistant/chat"
      });
    },
    async getChatListSM() {
      var _a;
      const res = await api_order.getPatientChatListSM({
        page: this.listQuery.page,
        limit: this.listQuery.limit,
        docName: (_a = this.searchValue) == null ? void 0 : _a.value,
        userId: common_vendor.index.getStorageSync("userId")
      });
      this.total = res.data.total;
      if (this.listQuery.page > 1) {
        this.groupChatList = [...this.groupChatList, ...res.data.rows];
      } else {
        this.groupChatList = res.data.rows;
      }
    },
    // 滚动组件触底
    lower() {
      console.log("触底加载");
      if (this.groupChatList.length >= this.total)
        return;
      this.listQuery.page += 1;
      this.getChatListSM();
    },
    setNav(n) {
      if (this.query.isOffLine == n)
        return;
      this.query.isOffLine = n;
      if (n === 3) {
        this.aiMessages = [];
        this.aiQueryParams.page = 1;
        this.getAiMessages();
        this.getPatientList();
      }
    },
    async getAiMessages() {
      try {
        const userId = common_vendor.index.getStorageSync("userId");
        const params = {
          docId: userId,
          page: this.aiQueryParams.page,
          limit: this.aiQueryParams.limit,
          patientId: ""
        };
        if (this.selectedPatientId !== "all") {
          params.patientId = this.selectedPatientId;
        }
        const res = await api_qcAi.getSysPushResultRecord(params);
        if (res.data) {
          this.aiQueryParams.total = res.data.total || 0;
          const messages = (res.data.rows || []).map((item) => {
            return {
              ...item,
              extJson: JSON.parse(item.ext || "{}")
            };
          });
          if (this.aiQueryParams.page > 1) {
            this.aiMessages = [...this.aiMessages, ...messages];
          } else {
            this.aiMessages = messages;
          }
        }
      } catch (error) {
        console.error("获取AI消息列表失败", error);
        common_vendor.index.showToast({
          title: "获取消息失败",
          icon: "none"
        });
      }
    },
    async loadMoreAiMessages() {
      if (this.aiMessages.length >= this.aiQueryParams.total) {
        return;
      }
      this.aiQueryParams.page += 1;
      await this.getAiMessages();
    },
    handleAiMessageClick(item) {
      if (item.hxMessage) {
        common_vendor.index.navigateTo({
          url: `/pages/qcAi/report?id=${item.hxMessage}`
        });
      }
    },
    setImg() {
      this.list.forEach((item) => {
        if (item.docImg && !item.docImgCopy) {
          common_js_myJsTools.myJsTools.downAndSaveImg(item.docImg, (url) => {
            this.$set(item, "docImgCopy", url);
          });
        }
      });
    },
    touchStart(e, index) {
      this.item = index;
      this.showTips = false;
      let { touches } = e;
      if (touches.length > 1)
        return;
      this.timer = setTimeout(() => {
        if (touches.length > 1)
          return;
        let { pageX, pageY } = touches[0];
        if (screenHeight - pageY < 200) {
          pageY = screenHeight - 200;
        }
        if (screenWidth - pageX < 120) {
          pageX = screenWidth - 120;
        }
        this.pageX = pageX;
        this.pageY = pageY;
        this.showTips = true;
      }, 500);
    },
    touchMove() {
      clearTimeout(this.timer);
      this.timer = "";
      this.showTips = false;
    },
    touchEnd() {
    },
    bindClick(e, item) {
      if (e.index == 0) {
        if (e.content.text == "置顶") {
          this.setTop(item);
        } else {
          this.cancelTop(item);
        }
      } else if (e.index == 1) {
        this.delete(item);
      }
    },
    async setTop() {
      let list = this.chatList;
      list[this.item].isTop = true;
      let item = list[this.item];
      this.$store.commit("setAllChatList", list);
      let para = {};
      para.docId = item.dataVal.docId;
      para.isTopping = 1;
      para.patientId = item.dataVal.patientId;
      para.pcoType = 1;
      para.userId = common_vendor.index.getStorageSync("userId");
      await api_chat.updateChatList(para);
      this.getDocList();
    },
    async cancelTop(item) {
      let para = {
        docId: item.docId,
        isTopping: 0,
        patientId: item.patientId,
        pcoType: 1,
        userId: common_vendor.index.getStorageSync("userId")
      };
      await api_chat.updateChatList(para);
      this.getDocList();
    },
    // async del() {
    //   let item = this.list[this.item]
    //   await indexedDB.del(item.id)
    //   this.showTips = false
    //   this.$store.dispatch('getChatListDoc')
    // },
    getUnreadNum(item) {
      var userId = item.id.split(",")[0];
      var docId = item.id.split(",")[1];
      let chatList;
      this.chatList.map((item2) => {
        if (userId + "," + docId == item2.id) {
          chatList = item2;
        }
      });
      let unReadNum = 0;
      if (chatList.chatRecordList && chatList.chatRecordList.length > 0) {
        chatList.chatRecordList.forEach((msg) => {
          if (msg.status == "unread" && msg.type == "receive") {
            unReadNum++;
          }
        });
      }
      return unReadNum;
    },
    async getDocList() {
      let paramLists = [];
      this.chatList.map((item) => {
        let ids = item.id.split(",");
        paramLists.push({
          patientId: ids[0],
          docId: ids[1]
        });
      });
      if (paramLists.length == 0) {
        common_vendor.index.stopPullDownRefresh();
        return;
      }
      let res = await api_chat.basicgetPatientChatList({
        paramLists,
        page: this.listQuery.page,
        limit: this.listQuery.limit
      });
      let data = res.data.rows;
      let arr = JSON.parse(JSON.stringify(this.chatList));
      arr.map((item) => {
        data.map((el) => {
          let id = el.patientId.toLowerCase() + "," + el.docId.toLowerCase();
          if (item.id == id) {
            item.dataVal = el;
          }
        });
      });
      if (arr.length > 0) {
        this.$store.commit("setAllChatList", arr);
      }
      common_vendor.index.stopPullDownRefresh();
    },
    async openChatRoom(item, id) {
      common_vendor.index.setStorageSync("patientId", item.patientId);
      clearTimeout(this.timer);
      common_vendor.index.setStorageSync("chatItem", item);
      common_vendor.index.setStorageSync("hosId", item.hosId);
      let param = {
        docId: item.docId
      };
      let obj = {
        chatId: id
      };
      if (item.groupId) {
        let isPatientAdminChat = item.projectId ? "&isPatientAdminChat=1" : "";
        common_vendor.index.navigateTo({
          url: "/pages/chatList/scanChatDetail?param=" + JSON.stringify(param) + isPatientAdminChat
        });
        return;
      }
      await this.$store.dispatch("getChatListId", obj);
      common_vendor.index.navigateTo({
        url: "/pages/chatList/chatDetail?param=" + JSON.stringify(param)
      });
    },
    openAdminMsg(item) {
      item.patientId = "admin";
      item.docId = "admin";
      common_vendor.index.setStorageSync("chatItem", item);
      common_vendor.index.navigateTo({
        url: "/pages/chatList/sysTemList"
      });
    },
    customEmoji(value) {
      return `<image src="http://llootong.cn/cloud/hisImg/static/faces/${value}" style="width:15px;height:15px"></image>`;
    },
    renderTxt(txt = "") {
      let rnTxt = [];
      let match = null;
      const regex = /(\[.*?\])/g;
      let start = 0;
      let index = 0;
      while (match = regex.exec(txt)) {
        index = match.index;
        if (index > start) {
          rnTxt.push(txt.substring(start, index));
        }
        if (match[1] in this.$im.Emoji.map) {
          const v = this.$im.Emoji.map[match[1]];
          rnTxt.push(this.customEmoji(v));
        } else {
          rnTxt.push(match[1]);
        }
        start = index + match[1].length;
      }
      rnTxt.push(txt.substring(start, txt.length));
      return rnTxt.toString().replace(/,/g, "");
    },
    search(e) {
      this.listQuery.docName = this.searchValue;
      this.listQuery.page = 1;
      this.getChatListSM();
    },
    async reset() {
    },
    selectPatient(patientId) {
      if (this.selectedPatientId === patientId)
        return;
      this.selectedPatientId = patientId;
      this.aiQueryParams.page = 1;
      this.aiMessages = [];
      this.getAiMessages();
    },
    formatTime(timestamp) {
      if (!timestamp)
        return "";
      return utils_date.date.getDateDiff(timestamp);
    },
    async getPatientList() {
      try {
        const res = await api_user.getPatientList({
          userId: common_vendor.index.getStorageSync("userId")
        });
        if (res.data) {
          this.patientList = res.data;
        }
      } catch (error) {
        console.error("获取就诊人列表失败", error);
      }
    }
  }
};
if (!Array) {
  const _easycom_uni_badge2 = common_vendor.resolveComponent("uni-badge");
  const _easycom_uni_search_bar2 = common_vendor.resolveComponent("uni-search-bar");
  const _component_SysTemList = common_vendor.resolveComponent("SysTemList");
  (_easycom_uni_badge2 + _easycom_uni_search_bar2 + _component_SysTemList)();
}
const _easycom_uni_badge = () => "../../uni_modules/uni-badge/components/uni-badge/uni-badge.js";
const _easycom_uni_search_bar = () => "../../uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar.js";
if (!Math) {
  (_easycom_uni_badge + _easycom_uni_search_bar)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.n($data.query.isOffLine == 0 ? "active" : ""),
    b: common_vendor.p({
      text: 0,
      absolute: "rightTop",
      size: "small",
      ["is-dot"]: true
    }),
    c: common_vendor.o(($event) => $options.setNav(0)),
    d: common_vendor.n($data.query.isOffLine == 3 ? "active" : ""),
    e: common_vendor.o(($event) => $options.setNav(3)),
    f: common_vendor.n($data.query.isOffLine == 1 ? "active" : ""),
    g: $data.sysNum > 0
  }, $data.sysNum > 0 ? {
    h: common_vendor.p({
      text: 0,
      absolute: "rightTop",
      size: "small",
      ["is-dot"]: true
    })
  } : {}, {
    i: common_vendor.o(($event) => $options.setNav(1)),
    j: $data.query.isOffLine == 0
  }, $data.query.isOffLine == 0 ? common_vendor.e({
    k: common_vendor.o($options.reset),
    l: common_vendor.o($options.search),
    m: common_vendor.o(($event) => $data.searchValue = $event),
    n: common_vendor.p({
      radius: 100,
      cancelButton: "auto",
      bgColor: "#FFF",
      maxlength: "20",
      placeholder: "输入医生姓名进行查找",
      modelValue: $data.searchValue
    }),
    o: common_vendor.o((...args) => $options.search && $options.search(...args)),
    p: $data.groupChatList.length == 0
  }, $data.groupChatList.length == 0 ? {
    q: common_assets._imports_0$1
  } : {}, {
    r: common_vendor.f($data.groupChatList, (item, index, i0) => {
      return common_vendor.e({
        a: item.id != "admin,admin"
      }, item.id != "admin,admin" ? common_vendor.e({
        b: item.docImgUrl ? item.docImgUrl : "../../static/images/docHead.png",
        c: common_vendor.t(item.platformDocName || item.docName),
        d: common_vendor.t(item.deptName),
        e: item.ywStatus == "0"
      }, item.ywStatus == "0" ? {} : {}, {
        f: item.ywStatus == "1"
      }, item.ywStatus == "1" ? {} : {}, {
        g: item.ywStatus == "2"
      }, item.ywStatus == "2" ? {} : {}, {
        h: item.ywStatus == "3"
      }, item.ywStatus == "3" ? {} : {}, {
        i: item.ywStatus == "4"
      }, item.ywStatus == "4" ? {} : {}, {
        j: item.ywStatus == "5"
      }, item.ywStatus == "5" ? {} : {}, {
        k: common_vendor.t(item.patientName),
        l: item.visitTypeCode == 1
      }, item.visitTypeCode == 1 ? {} : {}, {
        m: item.visitTypeCode == 2
      }, item.visitTypeCode == 2 ? {} : {}, {
        n: item.visitTypeCode == 4
      }, item.visitTypeCode == 4 ? {} : {}, {
        o: item.projectId
      }, item.projectId ? {} : {}, {
        p: $options.renderTxt(item.latInfo),
        q: common_vendor.t(item.timeDiff),
        r: common_vendor.o(($event) => $options.openChatRoom(item, item.id), index)
      }) : {}, {
        s: index
      });
    }),
    s: common_vendor.o((...args) => $options.lower && $options.lower(...args))
  }) : {}, {
    t: $data.query.isOffLine == 3
  }, $data.query.isOffLine == 3 ? common_vendor.e({
    v: $data.selectedPatientId === "all" ? 1 : "",
    w: common_vendor.o(($event) => $options.selectPatient("all")),
    x: common_vendor.f($data.patientList, (patient, index, i0) => {
      return {
        a: common_vendor.t(patient.patientName),
        b: index,
        c: $data.selectedPatientId === patient.patientId ? 1 : "",
        d: common_vendor.o(($event) => $options.selectPatient(patient.patientId), index)
      };
    }),
    y: $data.aiMessages.length === 0
  }, $data.aiMessages.length === 0 ? {
    z: common_assets._imports_0$1
  } : {}, {
    A: common_vendor.f($data.aiMessages, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.wxTitle || "您的健康报告已生成，请查看"),
        b: common_vendor.t(item.extJson.patientName || ""),
        c: common_vendor.t(item.pushTime),
        d: item.status === 0
      }, item.status === 0 ? {} : {}, {
        e: index,
        f: common_vendor.o(($event) => $options.handleAiMessageClick(item), index)
      });
    }),
    B: common_assets._imports_1$1,
    C: common_vendor.o((...args) => $options.loadMoreAiMessages && $options.loadMoreAiMessages(...args))
  }) : {}, {
    D: $data.query.isOffLine == 1
  }, $data.query.isOffLine == 1 ? {} : {}, {
    E: $data.showTips,
    F: common_vendor.o(($event) => $data.showTips = false)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-a0119522"]]);
wx.createPage(MiniProgramPage);
