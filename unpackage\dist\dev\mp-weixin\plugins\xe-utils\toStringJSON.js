"use strict";
const plugins_xeUtils_isPlainObject = require("./isPlainObject.js");
const plugins_xeUtils_isString = require("./isString.js");
function toStringJSON(str) {
  if (plugins_xeUtils_isPlainObject.isPlainObject(str)) {
    return str;
  } else if (plugins_xeUtils_isString.isString(str)) {
    try {
      return JSON.parse(str);
    } catch (e) {
    }
  }
  return {};
}
exports.toStringJSON = toStringJSON;
