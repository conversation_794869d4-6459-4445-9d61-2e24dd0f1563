"use strict";
const plugins_xeUtils_isArray = require("./isArray.js");
const plugins_xeUtils_includes = require("./includes.js");
function includeArrays(array1, array2) {
  var len;
  var index = 0;
  if (plugins_xeUtils_isArray.isArray(array1) && plugins_xeUtils_isArray.isArray(array2)) {
    for (len = array2.length; index < len; index++) {
      if (!plugins_xeUtils_includes.includes(array1, array2[index])) {
        return false;
      }
    }
    return true;
  }
  return plugins_xeUtils_includes.includes(array1, array2);
}
exports.includeArrays = includeArrays;
