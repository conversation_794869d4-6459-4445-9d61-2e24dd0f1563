"use strict";
const plugins_xeUtils_isFunction = require("./isFunction.js");
const plugins_xeUtils_isString = require("./isString.js");
const plugins_xeUtils_isArray = require("./isArray.js");
const plugins_xeUtils_hasOwnProp = require("./hasOwnProp.js");
function helperCreateiterateIndexOf(callback) {
  return function(obj, iterate, context) {
    if (obj && plugins_xeUtils_isFunction.isFunction(iterate)) {
      if (plugins_xeUtils_isArray.isArray(obj) || plugins_xeUtils_isString.isString(obj)) {
        return callback(obj, iterate, context);
      }
      for (var key in obj) {
        if (plugins_xeUtils_hasOwnProp.hasOwnProp(obj, key)) {
          if (iterate.call(context, obj[key], key, obj)) {
            return key;
          }
        }
      }
    }
    return -1;
  };
}
exports.helperCreateiterateIndexOf = helperCreateiterateIndexOf;
