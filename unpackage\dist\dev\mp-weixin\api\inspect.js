"use strict";
const common_request_request = require("../common/request/request.js");
function getOpenIdUserInfo(param = {}) {
  return common_request_request.http({
    url: "basic/enterpriseWeChatRegister/judgeOpenIdIfExist",
    param,
    method: "post"
  });
}
function checkPacsAppointStatus(ppiId) {
  return common_request_request.http({
    url: "basic/proPacsInfoController/checkPacsAppointStatus",
    param: { ppiId },
    method: "post"
  });
}
function checkLisAppointStatus(pliId) {
  return common_request_request.http({
    url: "basic/proLisInfoController/checkLisAppointStatus",
    param: { pliId },
    method: "post"
  });
}
exports.checkLisAppointStatus = checkLisAppointStatus;
exports.checkPacsAppointStatus = checkPacsAppointStatus;
exports.getOpenIdUserInfo = getOpenIdUserInfo;
