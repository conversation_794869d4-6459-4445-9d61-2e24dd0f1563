import http from "../common/request/ossRequest.js";

//上传图片
export function uploadImg(param = {}) {
  return http({
    url: "ossUpload/uploadOssImg",
    param,
    method: "post",
    type: "oss",
  });
}

// 下载图片
export function downloadImg(param = {}) {
  return http({
    url: "ossDownLoad/downOssImg",
    param,
    method: "post",
    type: "oss",
  });
}

// 下载pdf
export function downPdf(param) {
  return http({
    url: "ossDownLoad/downLoadFileMany",
    param,
    method: "post",
  });
}

// 多个pdf下载合并成一个
export function downMergePDF(list) {
  let param = list.map((item) => {
    return {
      fileName: item.fileName,
    };
  });
  return http({
    method: "post",
    url: "ossDownLoad/downMergePDF",
    param,
  });
}
