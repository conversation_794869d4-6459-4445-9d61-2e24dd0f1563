"use strict";
const plugins_xeUtils_setupDefaults = require("./setupDefaults.js");
const plugins_xeUtils_toValueString = require("./toValueString.js");
const plugins_xeUtils_trim = require("./trim.js");
const plugins_xeUtils_get = require("./get.js");
function template(str, args, options) {
  return plugins_xeUtils_toValueString.toValueString(str).replace((options || plugins_xeUtils_setupDefaults.setupDefaults).tmplRE || /\{{2}([.\w[\]\s]+)\}{2}/g, function(match, key) {
    return plugins_xeUtils_get.get(args, plugins_xeUtils_trim.trim(key));
  });
}
exports.template = template;
