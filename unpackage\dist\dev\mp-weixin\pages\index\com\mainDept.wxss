/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*
	字体颜色相关
*/
/*
	背景色 分割线色
*/
/*
	主题色
*/
/*
		渐变可调角度
*/
/*
	定义弹性布局
 */
/*
	单行文本溢出隐藏
*/
.dept_list.data-v-c77bfb89 {
  background: #fff;
  border-radius: 8rpx;
  margin-bottom: 10px;
}
.dept_list .dept__title.data-v-c77bfb89 {
  padding: 0 12rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  display: flex;
  justify-content: space-between;
}
.dept_list .container.data-v-c77bfb89 {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  /* 每行4个元素 */
  gap: 20rpx;
  /* 固定间距 */
  justify-items: center;
  /* 居中对齐内容 */
  align-items: center;
  /* 垂直居中内容 */
}
.dept_list .item.data-v-c77bfb89 {
  width: 150rpx;
  height: 150rpx;
  display: flex;
  flex-direction: column;
  /* 垂直排列图标和文字 */
  justify-content: center;
  align-items: center;
  background: #f7f8fc;
  border-radius: 8px;
  /* 可选：设置圆角 */
}
.dept_list .item .doc_head.data-v-c77bfb89 {
  width: 50rpx;
  height: 50rpx;
}
.dept_list .item .doc_name.data-v-c77bfb89 {
  font-size: 24rpx;
}
.dept_list .list-box.data-v-c77bfb89 {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 21rpx;
  /* 设置元素间的间距 */
}
.dept_list .list-box .list-card.data-v-c77bfb89 {
  width: 160rpx;
  height: 160rpx;
  border-radius: 4px;
  background: #f7f8fc;
  text-align: center;
  padding: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.dept_list .list-box-1.data-v-c77bfb89 {
  width: 50%;
  text-align: center;
}
.dept_list .card_more.data-v-c77bfb89 {
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #874ff0;
}
.dept_list .doc_head.data-v-c77bfb89 {
  width: 104rpx;
  height: 104rpx;
  border-radius: 50%;
  flex: none;
  object-fit: cover;
}
.dept_list .doc_name.data-v-c77bfb89 {
  margin-top: 16rpx;
  color: #333333;
  text-align: center;
  font-weight: 400;
}