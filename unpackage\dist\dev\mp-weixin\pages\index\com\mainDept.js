"use strict";
const common_vendor = require("../../../common/vendor.js");
const api_base = require("../../../api/base.js");
const TITLE = () => "../../inspect/com/itemTitle.js";
const _sfc_main = {
  components: {
    TITLE
  },
  data() {
    return {
      deptList: []
    };
  },
  mounted() {
    this.findDiagTreatmentSubject();
  },
  methods: {
    async findDiagTreatmentSubject() {
      let { data } = await api_base.findDiagTreatmentSubject();
      this.deptList = data.length > 0 && data.filter((item) => {
        return item.homeDisplayFlag == "1";
      });
      this.deptList = this.deptList.map((v) => {
        if (v.deptImg && v.deptImg.includes("http")) {
          v.deptImgUrl = v.deptImg;
        }
        return {
          ...v,
          deptImgUrl: v.deptImgUrl
        };
      });
      console.log(this.deptList);
    },
    toDeptList(item) {
      common_vendor.index.navigateTo({ url: `/pages/register/mainDeptList/index?subjectCode=${item ? item.subjectCode : ""}` });
    }
  }
};
if (!Array) {
  const _component_TITLE = common_vendor.resolveComponent("TITLE");
  _component_TITLE();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.p({
      title: "诊疗科室"
    }),
    b: common_vendor.o(($event) => $options.toDeptList()),
    c: common_vendor.f($data.deptList, (item, k0, i0) => {
      return {
        a: item.deptImgUrl || "/static/images/index/dept.png",
        b: common_vendor.t(item.subjectName),
        c: common_vendor.o(($event) => $options.toDeptList(item), item.id),
        d: item.id
      };
    })
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-c77bfb89"]]);
wx.createComponent(Component);
