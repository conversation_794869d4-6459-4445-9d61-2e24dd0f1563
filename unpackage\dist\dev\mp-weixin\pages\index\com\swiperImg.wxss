/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*
	字体颜色相关
*/
/*
	背景色 分割线色
*/
/*
	主题色
*/
/*
		渐变可调角度
*/
/*
	定义弹性布局
 */
/*
	单行文本溢出隐藏
*/
.swiper.data-v-9d3f9f4c {
  background: #fff;
  border-radius: 8rpx;
}
.swiper_warp.data-v-9d3f9f4c {
  height: 240rpx;
}
.img-box.data-v-9d3f9f4c {
  width: 100%;
  height: 240rpx;
  object-fit: cover;
  border-bottom-left-radius: 28rpx;
  border-bottom-right-radius: 28rpx;
}