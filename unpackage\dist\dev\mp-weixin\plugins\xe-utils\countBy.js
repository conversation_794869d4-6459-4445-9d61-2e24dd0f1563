"use strict";
const plugins_xeUtils_groupBy = require("./groupBy.js");
const plugins_xeUtils_objectEach = require("./objectEach.js");
function countBy(obj, iterate, context) {
  var result = plugins_xeUtils_groupBy.groupBy(obj, iterate, context || this);
  plugins_xeUtils_objectEach.objectEach(result, function(item, key) {
    result[key] = item.length;
  });
  return result;
}
exports.countBy = countBy;
