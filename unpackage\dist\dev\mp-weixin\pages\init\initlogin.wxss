/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*
	字体颜色相关
*/
/*
	背景色 分割线色
*/
/*
	主题色
*/
/*
		渐变可调角度
*/
/*
	定义弹性布局
 */
/*
	单行文本溢出隐藏
*/
header.data-v-74c74a87 {
  text-align: center;
}
.icon.data-v-74c74a87 {
  width: 100px;
  height: 100px;
  display: inline-block;
  margin: 30% auto 48px;
}
.loginBody.data-v-74c74a87 {
  padding: 0 38px;
}
.login.data-v-74c74a87 {
  background: #ffffff;
  height: 100vh;
}
.publicBtn.data-v-74c74a87 {
  box-sizing: border-box;
  background: #14a0e6;
  line-height: 44rpx;
  color: #ffffff;
  font-weight: 500;
  font-size: 32rpx;
  width: 100%;
  border-radius: 66rpx;
  padding: 20rpx 0;
  margin-top: 108px;
}
.input_item.data-v-74c74a87 {
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  color: #333;
  height: 88rpx;
  padding: 0 30rpx;
  margin-bottom: 32rpx;
  border-radius: 23px;
}
.input_item input.data-v-74c74a87 {
  flex: 1;
}
.input_item.data-v-74c74a87 .uni-input-input {
  font-size: 14px !important;
}
.input_item .uni-input-placeholder.data-v-74c74a87 {
  color: #c1c1c1;
  font-size: 28rpx;
}
.input_item button.data-v-74c74a87 {
  min-width: 180rpx;
  flex: none;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #836aff;
  font-size: 30rpx;
  padding: 0;
}
[data-theme=nx] .input_item button.data-v-74c74a87 {
  color: #107dff;
}
[data-theme=test] .input_item button.data-v-74c74a87 {
  color: #2db99d;
}
.input_item button.act.data-v-74c74a87 {
  color: #999;
}
.positionBtn.data-v-74c74a87 {
  position: fixed;
  bottom: 0.32rem;
  left: 0px;
  background: #fff;
  padding: 10px 0px;
  text-align: center;
  width: 90%;
}
.positionBtn span.data-v-74c74a87 {
  margin-top: 12px;
  font-size: 12px;
  padding-left: 12px;
  display: inline-block;
}