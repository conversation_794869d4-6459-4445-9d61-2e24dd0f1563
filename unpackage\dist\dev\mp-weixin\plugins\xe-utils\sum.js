"use strict";
const plugins_xeUtils_helperNumberAdd = require("./helperNumberAdd.js");
const plugins_xeUtils_isFunction = require("./isFunction.js");
const plugins_xeUtils_each = require("./each.js");
const plugins_xeUtils_get = require("./get.js");
function sum(array, iterate, context) {
  var result = 0;
  plugins_xeUtils_each.each(array, iterate ? plugins_xeUtils_isFunction.isFunction(iterate) ? function() {
    result = plugins_xeUtils_helperNumberAdd.helperNumberAdd(result, iterate.apply(context, arguments));
  } : function(val) {
    result = plugins_xeUtils_helperNumberAdd.helperNumberAdd(result, plugins_xeUtils_get.get(val, iterate));
  } : function(val) {
    result = plugins_xeUtils_helperNumberAdd.helperNumberAdd(result, val);
  });
  return result;
}
exports.sum = sum;
