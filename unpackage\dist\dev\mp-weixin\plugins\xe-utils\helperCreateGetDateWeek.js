"use strict";
const plugins_xeUtils_setupDefaults = require("./setupDefaults.js");
const plugins_xeUtils_staticWeekTime = require("./staticWeekTime.js");
const plugins_xeUtils_isNumber = require("./isNumber.js");
const plugins_xeUtils_isValidDate = require("./isValidDate.js");
const plugins_xeUtils_getWhatWeek = require("./getWhatWeek.js");
const plugins_xeUtils_helperGetDateTime = require("./helperGetDateTime.js");
function helperCreateGetDateWeek(getStartDate) {
  return function(date, firstDay) {
    var viewStartDay = plugins_xeUtils_isNumber.isNumber(firstDay) ? firstDay : plugins_xeUtils_setupDefaults.setupDefaults.firstDayOfWeek;
    var targetDate = plugins_xeUtils_getWhatWeek.getWhatWeek(date, 0, viewStartDay, viewStartDay);
    if (plugins_xeUtils_isValidDate.isValidDate(targetDate)) {
      var targetOffsetDate = new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate());
      var targerStartDate = getStartDate(targetDate);
      var targetFirstDay = targerStartDate.getDay();
      if (targetFirstDay > viewStartDay) {
        targerStartDate.setDate(7 - targetFirstDay + viewStartDay + 1);
      }
      if (targetFirstDay < viewStartDay) {
        targerStartDate.setDate(viewStartDay - targetFirstDay + 1);
      }
      return Math.floor((plugins_xeUtils_helperGetDateTime.helperGetDateTime(targetOffsetDate) - plugins_xeUtils_helperGetDateTime.helperGetDateTime(targerStartDate)) / plugins_xeUtils_staticWeekTime.staticWeekTime + 1);
    }
    return NaN;
  };
}
exports.helperCreateGetDateWeek = helperCreateGetDateWeek;
