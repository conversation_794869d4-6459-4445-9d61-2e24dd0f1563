!function(r,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.websdk=e():r.websdk=e()}(self,(function(){return(self.webpackChunkwebsdk=self.webpackChunkwebsdk||[]).push([[936],{846:function(r,e,t){t.r(e),t.d(e,{acceptGroupInvite:function(){return W},acceptGroupJoinRequest:function(){return B},addUsersToGroupAllowlist:function(){return Ir},addUsersToGroupWhitelist:function(){return vr},agreeInviteIntoGroup:function(){return J},agreeJoinGroup:function(){return F},blockGroup:function(){return h},blockGroupMember:function(){return nr},blockGroupMembers:function(){return cr},blockGroupMessage:function(){return Br},blockGroupMessages:function(){return m},changeGroupOwner:function(){return j},changeOwner:function(){return T},createGroup:function(){return d},createGroupNew:function(){return l},createGroupVNext:function(){return g},deleteGroupSharedFile:function(){return Pr},destroyGroup:function(){return A},disableSendGroupMsg:function(){return mr},dissolveGroup:function(){return P},downloadGroupSharedFile:function(){return zr},enableSendGroupMsg:function(){return fr},fetchGroupAnnouncement:function(){return Cr},fetchGroupSharedFileList:function(){return Ar},getGroup:function(){return I},getGroupAdmin:function(){return O},getGroupAllowlist:function(){return Gr},getGroupBlacklist:function(){return gr},getGroupBlacklistNew:function(){return dr},getGroupBlocklist:function(){return hr},getGroupInfo:function(){return E},getGroupMemberAttributes:function(){return Mr},getGroupMembers:function(){return N},getGroupMembersAttributes:function(){return Dr},getGroupMsgReadUser:function(){return Ur},getGroupMuteList:function(){return tr},getGroupMutelist:function(){return or},getGroupSharedFilelist:function(){return Sr},getGroupWhitelist:function(){return Er},getJoinedGroups:function(){return y},getJoinedGroupsCount:function(){return Fr},getMuted:function(){return er},getPublicGroups:function(){return v},groupBlockMulti:function(){return ir},groupBlockSingle:function(){return ar},inviteToGroup:function(){return k},inviteUsersToGroup:function(){return M},isGroupWhiteUser:function(){return br},isInGroupAllowlist:function(){return Nr},isInGroupMutelist:function(){return Or},isInGroupWhiteList:function(){return Rr},joinGroup:function(){return D},leaveGroup:function(){return z},listGroupMember:function(){return b},listGroupMembers:function(){return R},listGroups:function(){return f},modifyGroup:function(){return G},mute:function(){return X},muteGroupMember:function(){return Z},quitGroup:function(){return S},rejectGroupInvite:function(){return K},rejectGroupJoinRequest:function(){return L},rejectInviteIntoGroup:function(){return V},rejectJoinGroup:function(){return x},removeAdmin:function(){return w},removeGroupAdmin:function(){return _},removeGroupAllowlistMember:function(){return jr},removeGroupBlockMulti:function(){return pr},removeGroupBlockSingle:function(){return sr},removeGroupMember:function(){return q},removeGroupMembers:function(){return Y},removeGroupWhitelistMember:function(){return Tr},removeMultiGroupMember:function(){return H},removeMute:function(){return $},removeSingleGroupMember:function(){return Q},rmUsersFromGroupWhitelist:function(){return yr},setAdmin:function(){return U},setGroupAdmin:function(){return C},setGroupMemberAttributes:function(){return kr},unblockGroupMember:function(){return ur},unblockGroupMembers:function(){return lr},unblockGroupMessage:function(){return xr},unmuteGroupMember:function(){return rr},updateGroupAnnouncement:function(){return wr},uploadGroupSharedFile:function(){return _r}}),t(2675),t(9463),t(2259),t(8706),t(1629),t(4423),t(4346),t(3792),t(8598),t(2062),t(739),t(2010),t(6099),t(3362),t(1699),t(7764),t(3500),t(2953);var o=t(1531),a=t(8678),n=t(1750),i=t(2056),c=t(3893);function s(r){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},s(r)}var u=function(r,e,t,o){return new(t||(t=Promise))((function(a,n){function i(r){try{s(o.next(r))}catch(r){n(r)}}function c(r){try{s(o.throw(r))}catch(r){n(r)}}function s(r){var e;r.done?a(r.value):(e=r.value,e instanceof t?e:new t((function(r){r(e)}))).then(i,c)}s((o=o.apply(r,e||[])).next())}))},p=function(r,e){var t,o,a,n,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return n={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(n[Symbol.iterator]=function(){return this}),n;function c(n){return function(c){return function(n){if(t)throw new TypeError("Generator is already executing.");for(;i;)try{if(t=1,o&&(a=2&n[0]?o.return:n[0]?o.throw||((a=o.return)&&a.call(o),0):o.next)&&!(a=a.call(o,n[1])).done)return a;switch(o=0,a&&(n=[2&n[0],a.value]),n[0]){case 0:case 1:a=n;break;case 4:return i.label++,{value:n[1],done:!1};case 5:i.label++,o=n[1],n=[0];continue;case 7:n=i.ops.pop(),i.trys.pop();continue;default:if(!((a=(a=i.trys).length>0&&a[a.length-1])||6!==n[0]&&2!==n[0])){i=0;continue}if(3===n[0]&&(!a||n[1]>a[0]&&n[1]<a[3])){i.label=n[1];break}if(6===n[0]&&i.label<a[1]){i.label=a[1],a=n;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(n);break}a[2]&&i.ops.pop(),i.trys.pop();continue}n=e.call(r,i)}catch(r){n=[6,r],o=0}finally{t=a=0}if(5&n[0])throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}([n,c])}}};function l(r){if(!r||!r.data)throw Error("Invalid parameter");var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups?resource=").concat(p.clientResource),dataType:"json",type:"POST",data:JSON.stringify({owner:this.user,groupname:r.data.groupname,avatar:r.data.avatar,desc:r.data.desc,members:r.data.members,public:r.data.public,approval:r.data.approval,allowinvites:r.data.allowinvites,invite_need_confirm:r.data.inviteNeedConfirm,maxusers:r.data.maxusers,custom:r.data.ext}),headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:function(e){r.success&&r.success(e)},error:r.error};return i.vF.debug("Call createGroup:",r),a.RD.call(this,l,c.jz.CREATE_GROUP)}var d=l;function g(r){var e,t,s,u,p,l,d,g,h;if(!r)throw Error("Invalid parameter");var m=n.dO.call(this).error;if(m)return Promise.reject(m);var f=this.context,v=f.orgName,I=f.appName,y=f.accessToken,T=f.jid,j={url:"".concat(this.apiUrl,"/").concat(v,"/").concat(I,"/chatgroups?resource=").concat(T.clientResource),dataType:"json",type:"POST",data:JSON.stringify({owner:this.user,groupname:null!==(e=r.groupName)&&void 0!==e?e:"",avatar:null!==(t=r.avatar)&&void 0!==t?t:"",desc:null!==(s=r.description)&&void 0!==s?s:"",members:r.members,public:null!==(u=r.isPublic)&&void 0!==u&&u,approval:null!==(p=r.needApprovalToJoin)&&void 0!==p&&p,allowinvites:null===(l=r.allowMemberToInvite)||void 0===l||l,invite_need_confirm:null!==(d=r.inviteNeedConfirm)&&void 0!==d&&d,maxusers:null!==(g=r.maxMemberCount)&&void 0!==g?g:200,custom:null!==(h=r.extension)&&void 0!==h?h:""}),headers:{Authorization:"Bearer "+y,"Content-Type":"application/json"}};return i.vF.debug("Call createGroupVNext:",r),a.RD.call(this,j,c.jz.CREATE_GROUP).then((function(r){return{type:o.C.REQUEST_SUCCESS,data:{groupId:r.data.groupid}}}))}function h(r){var e;if("string"!=typeof r.groupId||""===r.groupId)throw Error("Invalid parameter");var t=n.dO.call(this).error;if(t)return Promise.reject(t);var o=this.context,s=o.orgName,u=o.appName,p=o.accessToken,l=o.jid,d={entities:[(e={},e["notification_ignore_"+r.groupId]=!0,e)]},g={type:"PUT",url:"".concat(this.apiUrl,"/").concat(s,"/").concat(u,"/users/").concat(this.user,"?resource=").concat(l.clientResource),data:JSON.stringify(d),dataType:"json",headers:{Authorization:"Bearer "+p,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call blockGroupMessages",r),a.RD.call(this,g,c.jz.BLOCK_GROUP)}var m=h;function f(r){if("number"!=typeof r.limit)throw Error("Invalid parameter");var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p={limit:r.limit,cursor:r.cursor};r.cursor||delete p.cursor;var l={url:this.apiUrl+"/"+o+"/"+s+"/publicchatgroups",type:"GET",dataType:"json",data:p,headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call listGroups",r),a.RD.call(this,l,c.jz.LIST_GROUP)}var v=f;function I(r){var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p={url:this.apiUrl+"/"+o+"/"+s+"/users/"+this.user+"/joined_chatgroups",dataType:"json",type:"GET",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r&&(null==r?void 0:r.success),error:r&&(null==r?void 0:r.error)};return i.vF.debug("Call getJoinedGroups",r),a.RD.call(this,p,c.jz.GET_USER_GROUP)}function y(r){if("number"!=typeof r.pageNum||"number"!=typeof r.pageSize)throw Error('Invalid parameter: "pageNum or pageSize"');if(r.pageNum<0||r.pageSize<0)throw Error('"pageNum" should >= 0 and "pageSize" should >= 0');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=r.needAffiliations||r.needRole?"/chatgroups/user/".concat(this.user,"?pagenum=").concat(r.pageNum,"&pagesize=").concat(r.pageSize,"&needAffiliations=").concat(r.needAffiliations,"&needRole=").concat(r.needRole):"/users/".concat(this.user,"/joined_chatgroups?pagenum=").concat(r.pageNum,"&pagesize=").concat(r.pageSize),l={url:this.apiUrl+"/"+o+"/"+s+p,dataType:"json",type:"GET",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r&&(null==r?void 0:r.success),error:r&&(null==r?void 0:r.error)};return i.vF.debug("Call getGroup",r),a.RD.call(this,l,c.jz.GET_USER_GROUP).then((function(r){var e=r.uri,t=r.entities,o=[];return e.includes("joined_chatgroups")||(t.forEach((function(r){var e={affiliationsCount:r.affiliations_count,groupName:r.name,groupId:r.groupId,role:r.permission,disabled:r.disabled,approval:r.membersonly,allowInvites:r.allowinvites,description:r.description,maxUsers:r.maxusers,public:r.public};o.push(e)})),r.entities=o),r}))}function T(r){if("string"!=typeof r.groupId||"string"!=typeof r.newOwner)throw Error("Invalid parameter");var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t={newowner:r.newOwner},o=this.context,s=o.orgName,u=o.appName,p=o.accessToken,l=o.jid,d={url:"".concat(this.apiUrl,"/").concat(s,"/").concat(u,"/chatgroups/").concat(r.groupId,"?resource=").concat(l.clientResource),type:"PUT",dataType:"json",headers:{Authorization:"Bearer "+p,"Content-Type":"application/json"},data:JSON.stringify(t),success:r.success,error:r.error};return i.vF.debug("Call changeOwner",r),a.RD.call(this,d,c.jz.CHANGE_OWNER)}var j=T;function E(r){if("string"!=typeof r.groupId&&!Array.isArray(r.groupId))throw Error('Invalid parameter: "groupId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p={url:this.apiUrl+"/"+o+"/"+s+"/chatgroups/"+r.groupId+"?joined_time=true",type:"GET",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call getGroupInfo",r),a.RD.call(this,p,c.jz.GET_GROUP_INFO)}function G(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error("Invalid parameter");var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l=r.groupId,d={groupname:r.groupName,avatar:r.avatar,description:r.description,custom:r.ext},g={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(l,"?resource=").concat(p.clientResource),type:"PUT",data:JSON.stringify(d),dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call modifyGroup",r),a.RD.call(this,g,c.jz.MODIFY_GROUP)}function b(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');if(isNaN(r.pageNum)||r.pageNum<=0)throw Error('The parameter "pageNum" should be a positive number');if(isNaN(r.pageSize)||r.pageSize<=0)throw Error('The parameter "pageSize" should be a positive number');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t={pagenum:r.pageNum,pagesize:r.pageSize},o=this.context,s=o.orgName,u=o.appName,p=o.accessToken,l={url:this.apiUrl+"/"+s+"/"+u+"/chatgroups/"+r.groupId+"/users",dataType:"json",type:"GET",data:t,headers:{Authorization:"Bearer "+p,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call listGroupMember",r),a.RD.call(this,l,c.jz.LIST_GROUP_MEMBER)}var R=b;function N(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');if(r.cursor&&"string"!=typeof r.cursor)throw Error('Invalid parameter: "cursor"');if(r.limit&&"number"!=typeof r.limit)throw Error('Invalid parameter: "limit"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t={cursor:r.cursor,limit:r.limit||50,joined_time:!0},s=this.context,u=s.orgName,p=s.appName,l=s.accessToken,d={url:this.apiUrl+"/"+u+"/"+p+"/chatgroups/"+r.groupId+"/users",dataType:"json",type:"GET",data:t,headers:{Authorization:"Bearer "+l,"Content-Type":"application/json"}};return i.vF.debug("Call getGroupMembers",r),a.RD.call(this,d,c.jz.LIST_GROUP_MEMBER).then((function(r){var e=r.data,t=r.cursor,a=e.map((function(r){for(var e in r)if(["member","admin","owner"].includes(e))return{role:e,userId:r[e],joinedTime:r.joined_time}}));return{type:o.C.REQUEST_SUCCESS,data:{cursor:t,members:a}}}))}function O(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=r.groupId,l={url:this.apiUrl+"/"+o+"/"+s+"/chatgroups/"+p+"/admin",dataType:"json",type:"GET",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call getGroupAdmin",r),a.RD.call(this,l,c.jz.GET_GROUP_ADMIN)}function U(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof r.username||""===r.username)throw Error('Invalid parameter: "username"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l=r.groupId,d={newadmin:r.username},g={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(l,"/admin?resource=").concat(p.clientResource),type:"POST",dataType:"json",data:JSON.stringify(d),headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call setGroupAdmin",r),a.RD.call(this,g,c.jz.SET_GROUP_ADMIN)}var C=U;function w(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof r.username||""===r.username)throw Error('Invalid parameter: "username"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l=r.groupId,d=r.username,g={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(l,"/admin/").concat(d,"?resource=").concat(p.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call removeAdmin",r),a.RD.call(this,g,c.jz.REMOVE_GROUP_ADMIN)}var _=w;function P(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l=r.groupId,d={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(l,"?version=v3&resource=").concat(p.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call destroyGroup",r),a.RD.call(this,d,c.jz.DISSOLVE_GROUP)}var A=P;function S(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l=r.groupId,d={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(l,"/quit?resource=").concat(p.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call quitGroup",r),a.RD.call(this,d,c.jz.QUIT_GROUP)}var z=S;function k(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');if(!Array.isArray(r.users))throw Error('Invalid parameter: "users"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=r.groupId,o={usernames:r.users},s=this.context,u=s.orgName,p=s.appName,l=s.accessToken,d=s.jid,g={url:"".concat(this.apiUrl,"/").concat(u,"/").concat(p,"/chatgroups/").concat(t,"/invite?resource=").concat(d.clientResource),type:"POST",data:JSON.stringify(o),dataType:"json",headers:{Authorization:"Bearer "+l,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call inviteUsersToGroup",r),a.RD.call(this,g,c.jz.INVITE_TO_GROUP)}var M=k;function D(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(r.groupId,"/apply?resource=").concat(p.clientResource),type:"POST",dataType:"json",data:JSON.stringify({message:r.message||""}),headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call joinGroup",r),a.RD.call(this,l,c.jz.JOIN_GROUP)}function F(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof r.applicant||""===r.applicant)throw Error('Invalid parameter: "applicant"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l=r.groupId,d={applicant:r.applicant,verifyResult:!0,reason:"no clue"},g={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(l,"/apply_verify?resource=").concat(p.clientResource),type:"POST",dataType:"json",data:JSON.stringify(d),headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call agreeJoinGroup",r),a.RD.call(this,g,c.jz.AGREE_JOIN_GROUP)}var B=F;function x(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof r.applicant||""===r.applicant)throw Error('Invalid parameter: "applicant"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l=r.groupId,d={applicant:r.applicant,verifyResult:!1,reason:r.reason||""},g={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(l,"/apply_verify?resource=").concat(p.clientResource),type:"POST",dataType:"json",data:JSON.stringify(d),headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call rejectGroupJoinRequest",r),a.RD.call(this,g,c.jz.REJECT_JOIN_GROUP)}var L=x;function J(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof r.invitee||""===r.invitee)throw Error('Invalid parameter: "invitee"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l=r.groupId,d={invitee:r.invitee,verifyResult:!0},g={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(l,"/invite_verify?resource=").concat(p.clientResource),type:"POST",dataType:"json",data:JSON.stringify(d),headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call acceptGroupInvite",r),a.RD.call(this,g,c.jz.AGREE_INVITE_GROUP)}var W=J;function V(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof r.invitee||""===r.invitee)throw Error('Invalid parameter: "invitee"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l=r.groupId,d={invitee:r.invitee,verifyResult:!1},g={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(l,"/invite_verify?resource=").concat(p.clientResource),type:"POST",dataType:"json",data:JSON.stringify(d),headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call rejectGroupInvite",r),a.RD.call(this,g,c.jz.REJECT_INVITE_GROUP)}var K=V;function Q(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof r.username||""===r.username)throw Error('Invalid parameter: "username"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=r.groupId,o=r.username,s=this.context,u=s.orgName,p=s.appName,l=s.accessToken,d=s.jid,g={url:"".concat(this.apiUrl,"/").concat(u,"/").concat(p,"/chatgroups/").concat(t,"/users/").concat(o,"?resource=").concat(d.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+l,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call removeGroupMember",r),a.RD.call(this,g,c.jz.REMOVE_GROUP_MEMBER)}var q=Q;function H(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');if(!Array.isArray(r.users))throw Error('Invalid parameter: "users"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l=r.groupId,d=r.users.join(","),g={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(l,"/users/").concat(d,"?resource=").concat(p.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call removeGroupMembers",r),a.RD.call(this,g,c.jz.MULTI_REMOVE_GROUP_MEMBER)}var Y=H;function X(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');if(!(Array.isArray(r.username)||"string"==typeof r.username&&""!==r.username))throw Error('Invalid parameter: "username"');if("number"!=typeof r.muteDuration)throw Error('Invalid parameter: "muteDuration"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l=r.groupId,d={usernames:"string"==typeof r.username?[r.username]:r.username,mute_duration:r.muteDuration},g={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(l,"/mute?resource=").concat(p.clientResource),dataType:"json",type:"POST",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},data:JSON.stringify(d),success:r.success,error:r.error};return i.vF.debug("Call muteGroupMember",r),a.RD.call(this,g,c.jz.MUTE_GROUP_MEMBER)}var Z=X;function $(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');if(!(Array.isArray(r.username)||"string"==typeof r.username&&""!==r.username))throw Error('Invalid parameter: "username"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l=r.groupId,d=r.username,g={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(l,"/mute/").concat(d,"?resource=").concat(p.clientResource),dataType:"json",type:"DELETE",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call unmuteGroupMember",r),a.RD.call(this,g,c.jz.UNMUTE_GROUP_MEMBER)}var rr=$;function er(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=r.groupId,l={url:this.apiUrl+"/"+o+"/"+s+"/chatgroups/"+p+"/mute",dataType:"json",type:"GET",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call getGroupMuteList",r),a.RD.call(this,l,c.jz.GET_GROUP_MUTE_LIST)}var tr=er,or=er;function ar(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof r.username||""===r.username)throw Error('Invalid parameter: "username"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l=r.groupId,d=r.username,g={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(l,"/blocks/users/").concat(d,"?resource=").concat(p.clientResource),type:"POST",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call blockGroupMember",r),a.RD.call(this,g,c.jz.BLOCK_GROUP_MEMBER)}var nr=ar;function ir(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');if(!Array.isArray(r.usernames))throw Error('Invalid parameter: "usernames"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l=r.groupId,d={usernames:r.usernames},g={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(l,"/blocks/users?resource=").concat(p.clientResource),data:JSON.stringify(d),type:"POST",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call blockGroupMembers",r),a.RD.call(this,g,c.jz.BLOCK_GROUP_MEMBERS)}var cr=ir;function sr(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof r.username||""===r.username)throw Error('Invalid parameter: "username"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l=r.groupId,d=r.username,g={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(l,"/blocks/users/").concat(d,"?resource=").concat(p.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call unblockGroupMember",r),a.RD.call(this,g,c.jz.UNBLOCK_GROUP_MEMBER)}var ur=sr;function pr(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');if(!Array.isArray(r.usernames))throw Error('Invalid parameter: "usernames"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l=r.groupId,d=r.usernames.join(","),g={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(l,"/blocks/users/").concat(d,"?resource=").concat(p.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call unblockGroupMembers",r),a.RD.call(this,g,c.jz.UNBLOCK_GROUP_MEMBERS)}var lr=pr;function dr(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=r.groupId,l={url:this.apiUrl+"/"+o+"/"+s+"/chatgroups/"+p+"/blocks/users",type:"GET",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call getGroupBlacklist",r),a.RD.call(this,l,c.jz.GET_GROUP_BLACK_LIST)}var gr=dr,hr=dr;function mr(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l=r.groupId,d={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(l,"/ban?resource=").concat(p.clientResource),type:"POST",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call disableSendGroupMsg",r),a.RD.call(this,d,c.jz.DISABLED_SEND_GROUP_MSG)}function fr(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l=r.groupId,d={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(l,"/ban?resource=").concat(p.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call enableSendGroupMsg",r),a.RD.call(this,d,c.jz.ENABLE_SEND_GROUP_MSG)}function vr(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');if(!Array.isArray(r.users))throw Error('Invalid parameter: "users"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l=r.groupId,d={usernames:r.users},g={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(l,"/white/users?resource=").concat(p.clientResource),type:"POST",data:JSON.stringify(d),dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call addUsersToGroupWhitelist",r),a.RD.call(this,g,c.jz.ADD_USERS_TO_GROUP_WHITE)}var Ir=vr;function yr(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof r.userName||""===r.userName)throw Error('Invalid parameter: "userName"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l=r.groupId,d={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(l,"/white/users/").concat(r.userName,"?resource=").concat(p.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call removeGroupAllowlistMember",r),a.RD.call(this,d,c.jz.REMOVE_GROUP_WHITE_MEMBER)}var Tr=yr,jr=yr;function Er(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=r.groupId,l={url:this.apiUrl+"/"+o+"/"+s+"/chatgroups/"+p+"/white/users",type:"GET",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call getGroupAllowlist",r),a.RD.call(this,l,c.jz.GET_GROUP_WHITE_LIST)}var Gr=Er;function br(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof r.userName||""===r.userName)throw Error('Invalid parameter: "userName"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=r.groupId,l={url:this.apiUrl+"/"+o+"/"+s+"/chatgroups/"+p+"/white/users/"+r.userName,type:"GET",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call isInGroupAllowlist",r),a.RD.call(this,l,c.jz.IS_IN_GROUP_WHITE_LIST)}var Rr=br,Nr=br;function Or(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,c=t.appName,s=t.accessToken,u=t.userId,p={url:this.apiUrl+"/"+o+"/"+c+"/sdk/chatgroups/"+r.groupId+"/mute/"+u,dataType:"json",type:"GET",headers:{Authorization:"Bearer "+s}};return i.vF.debug("Call isInGroupMutelist",r),a.RD.call(this,p).then((function(r){return{type:r.type,data:r.data}}))}function Ur(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof r.msgId||""===r.msgId)throw Error('Invalid parameter: "msgId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p={url:this.apiUrl+"/"+o+"/"+s+"/chatgroups/"+r.groupId+"/acks/"+r.msgId,dataType:"json",type:"GET",data:{limit:500,key:void 0},headers:{Authorization:"Bearer "+u},success:r.success,error:r.error};return i.vF.debug("Call getGroupMsgReadUser",r),a.RD.call(this,p,c.jz.GET_GROUP_MSG_READ_USER)}function Cr(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=r.groupId,l={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(p,"/announcement"),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call fetchGroupAnnouncement",r),a.RD.call(this,l,c.jz.GET_GROUP_ANN)}function wr(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof r.announcement)throw Error('Invalid parameter: "announcement"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l=r.groupId,d={announcement:r.announcement},g={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(l,"/announcement?resource=").concat(p.clientResource),type:"POST",dataType:"json",data:JSON.stringify(d),headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call updateGroupAnnouncement",r),a.RD.call(this,g,c.jz.UPDATE_GROUP_ANN)}function _r(r){var e;if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');if("object"!==s(r.file))throw Error('Invalid parameter: "file"');var t=n.dO.call(this).error;if(t)return null===(e=r.onFileUploadError)||void 0===e?void 0:e.call(r,t);var o=this.context,u=o.orgName,p=o.appName,l=o.accessToken,d=o.jid,g=r.groupId;a.QM.call(this,{uploadUrl:"".concat(this.apiUrl,"/").concat(u,"/").concat(p,"/chatgroups/").concat(g,"/share_files?resource=").concat(d.clientResource),onFileUploadProgress:r.onFileUploadProgress,onFileUploadComplete:r.onFileUploadComplete,onFileUploadError:r.onFileUploadError,onFileUploadCanceled:r.onFileUploadCanceled,accessToken:l,apiUrl:this.apiUrl,file:r.file,appKey:this.context.appKey,to:g,chatType:"groupChat"},c.jz.UPLOAD_GROUP_FILE),i.vF.debug("Call uploadGroupSharedFile",r)}function Pr(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof r.fileId||""===r.fileId)throw Error('Invalid parameter: "file"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l=r.groupId,d=r.fileId,g={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(l,"/share_files/").concat(d,"?resource=").concat(p.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call deleteGroupSharedFile",r),a.RD.call(this,g,c.jz.DELETE_GROUP_FILE)}function Ar(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,o=t.orgName,s=t.appName,u=t.accessToken,p=r.pageNum||1,l=r.pageSize||10,d=r.groupId,g={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(s,"/chatgroups/").concat(d,"/share_files?pagenum=").concat(p,"&pagesize=").concat(l),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call getGroupSharedFilelist",r),a.RD.call(this,g,c.jz.GET_GROUP_FILE_LIST)}var Sr=Ar;function zr(r){var e=this.context,t=e.orgName,o=e.appName,n=e.accessToken,s=this.apiUrl,u=r.groupId,p=r.fileId;a.RG.call(this,{url:"".concat(s,"/").concat(t,"/").concat(o,"/chatgroups/").concat(u,"/share_files/").concat(p),onFileDownloadComplete:r.onFileDownloadComplete,onFileDownloadError:r.onFileDownloadError,accessToken:n,id:p,secret:r.secret},c.jz.DOWN_GROUP_FILE),i.vF.debug("Call downloadGroupSharedFile",r)}function kr(r){return u(this,void 0,void 0,(function(){var e,t,o,u,l,d,g,h,m,f,v;return p(this,(function(p){switch(p.label){case 0:if(e=r.groupId,t=r.userId,o=r.memberAttributes,"string"!=typeof e||""===e)throw Error('Invalid parameter: "groupId"');if("string"!=typeof t||""===t)throw Error('Invalid parameter: "userId"');if("object"!==s(o))throw Error('Invalid parameter: "memberAttributes"');return(u=n.dO.call(this).error)?[2,Promise.reject(u)]:(l=this.context,d=l.orgName,g=l.appName,h=l.accessToken,m=l.jid,f={metaData:o},v={url:"".concat(this.apiUrl,"/").concat(d,"/").concat(g,"/sdk/metadata/chatgroup/").concat(e,"/user/").concat(t,"?resource=").concat(m.clientResource),type:"PUT",dataType:"json",data:JSON.stringify(f),headers:{Authorization:"Bearer "+h,"Content-Type":"application/json"}},i.vF.debug("Call setGroupMemberAttributes",r),[4,a.RD.call(this,v,c.jz.SET_GROUP_MEMBER_ATTRS)]);case 1:return p.sent(),[2]}}))}))}function Mr(r){var e=r.groupId,t=r.userId;return Dr.call(this,{groupId:e,userIds:[t]}).then((function(r){var e;return{type:r.type,data:null===(e=r.data)||void 0===e?void 0:e[t]}}))}function Dr(r){var e=r.groupId,t=r.userIds,o=r.keys,s=void 0===o?[]:o;if("string"!=typeof e||""===e)throw Error('Invalid parameter: "groupId"');if(!Array.isArray(t)||(null==t?void 0:t.length)<=0)throw Error('Invalid parameter: "userIds"');if(!Array.isArray(s))throw Error('Invalid parameter: "keys"');var u=n.dO.call(this).error;if(u)return Promise.reject(u);var p=this.context,l=p.orgName,d=p.appName,g=p.accessToken,h={targets:t,properties:s},m={url:"".concat(this.apiUrl,"/").concat(l,"/").concat(d,"/sdk/metadata/chatgroup/").concat(e,"/get"),type:"POST",dataType:"json",data:JSON.stringify(h),headers:{Authorization:"Bearer "+g,"Content-Type":"application/json"}};return i.vF.debug("Call getGroupMembersAttributes",r),a.RD.call(this,m,c.jz.GET_GROUP_MEMBER_ATTR).then((function(r){return{type:r.type,data:(null==r?void 0:r.data)||{}}}))}function Fr(){var r=n.dO.call(this).error;if(r)return Promise.reject(r);var e=this.context,t=e.orgName,c=e.appName,s=e.accessToken,u={url:"".concat(this.apiUrl,"/").concat(t,"/").concat(c,"/chatgroups/user/").concat(this.context.userId,"/joined_count"),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+s,"Content-Type":"application/json"}};return i.vF.debug("Call getJoinedGroupsCount"),a.RD.call(this,u).then((function(r){return{type:o.C.REQUEST_SUCCESS,data:r.data||0}}))}function Br(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,c=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l=r.groupId,d={url:"".concat(this.apiUrl,"/").concat(c,"/").concat(s,"/chatgroups/").concat(l,"/shield?version=v3&resource=").concat(p.clientResource),type:"POST",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call blockGroupMessages",r),a.RD.call(this,d).then((function(r){return{type:o.C.REQUEST_SUCCESS,data:r.data||{}}}))}function xr(r){if("string"!=typeof r.groupId||""===r.groupId)throw Error('Invalid parameter: "groupId"');var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,c=t.orgName,s=t.appName,u=t.accessToken,p=t.jid,l=r.groupId,d={url:"".concat(this.apiUrl,"/").concat(c,"/").concat(s,"/chatgroups/").concat(l,"/shield?version=v3&resource=").concat(p.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:r.success,error:r.error};return i.vF.debug("Call unblockGroupMessages",r),a.RD.call(this,d).then((function(r){return{type:o.C.REQUEST_SUCCESS,data:r.data||{}}}))}}},function(r){return r(r.s=846)}])}));