import http from '../common/request/request';
// 查询用户收货地址(post请求1)
export function findAddressByUserId(param = {}) {
  return http({
    url: 'basic/dicdeliveryaddress/findAddressByUserId',
    param,
    method: 'post',
  });
}

// 查询收货地址标签
export function findAllLable(param = {}) {
  return http({
    url: '/basic/dicdeliveryaddress/findAllLable',
    param,
    method: 'post',
  });
}

// 添加地址(post请求)
export function dicdeliveryaddressAdd(param = {}) {
  return http({
    url: 'basic/dicdeliveryaddress/add',
    param,
    method: 'post',
  });
}
// 删除地址(post请求)
export function dicdeliveryaddressDelete(param = {}) {
  return http({
    url: 'basic/dicdeliveryaddress/delete',
    param,
    method: 'post',
  });
}
// 编辑地址(post请求)
export function dicdeliveryaddressUpdate(param = {}) {
  return http({
    url: 'basic/dicdeliveryaddress/update',
    param,
    method: 'post',
  });
}

// 设为默认(post请求)
export function updataIsDefault(param = {}) {
  return http({
    url: 'basic/dicdeliveryaddress/updataIsDefault',
    param,
    method: 'post',
  });
}
