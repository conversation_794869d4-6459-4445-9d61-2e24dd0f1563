"use strict";
const plugins_xeUtils_helperCreateTreeFunc = require("./helperCreateTreeFunc.js");
const plugins_xeUtils_map = require("./map.js");
function mapTreeItem(parent, obj, iterate, context, path, node, parseChildren, opts) {
  var paths, nodes, rest;
  var mapChildren = opts.mapChildren || parseChildren;
  return plugins_xeUtils_map.map(obj, function(item, index) {
    paths = path.concat(["" + index]);
    nodes = node.concat([item]);
    rest = iterate.call(context, item, index, obj, paths, parent, nodes);
    if (rest && item && parseChildren && item[parseChildren]) {
      rest[mapChildren] = mapTreeItem(item, item[parseChildren], iterate, context, paths, nodes, parseChildren, opts);
    }
    return rest;
  });
}
var mapTree = plugins_xeUtils_helperCreateTreeFunc.helperCreateTreeFunc(mapTreeItem);
exports.mapTree = mapTree;
