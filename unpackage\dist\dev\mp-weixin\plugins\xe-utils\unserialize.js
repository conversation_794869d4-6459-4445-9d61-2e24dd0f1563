"use strict";
const plugins_xeUtils_staticDecodeURIComponent = require("./staticDecodeURIComponent.js");
const plugins_xeUtils_arrayEach = require("./arrayEach.js");
const plugins_xeUtils_isString = require("./isString.js");
function unserialize(str) {
  var items;
  var result = {};
  if (str && plugins_xeUtils_isString.isString(str)) {
    plugins_xeUtils_arrayEach.arrayEach(str.split("&"), function(param) {
      items = param.split("=");
      result[plugins_xeUtils_staticDecodeURIComponent.staticDecodeURIComponent(items[0])] = plugins_xeUtils_staticDecodeURIComponent.staticDecodeURIComponent(items[1] || "");
    });
  }
  return result;
}
exports.unserialize = unserialize;
