<template>
  <!-- 药品卡片 -->
  <view class="drug_card">
    <!-- 信息 -->
    <view class="card" @click="toDrugDetail(item)">
      <view style="display: flex;flex-direction: column;align-items: center">
        <view style="position: relative">
          <UniImage v-if="item.drugImg"
              :src="item.drugImg"
              :data-src="errUrl"
              :class="['card_img',item.drugType=='025.9'?'gx':'']"
              alt=""
          />
          <image
              src="/static/shop/drug.png"
              v-else
              mode="aspectFill"
              :class="['card_img',item.drugType=='025.9'?'gx':'']"
          ></image>
          <view v-if="item.drugKc==0||!item.drugKc"  class="drug-coverUp coverUps">
            <img class="coverUp"  src="/static/ysq.png" alt="">
          </view>
          <view v-if="item.drugType=='025.9'" class="drug-coverUp cfCover">
            <view class="cover-title">
              处方药 <br>
              依规定不展示包装
            </view>
          </view>
        </view>
        <text class="count">库存{{ item.drugKc }}件</text>
      </view>
      <!-- 文字 -->
      <view class="card_text">
        <view style="display: flex">
          <view v-if="item.drugType!='025.8'&&item.drugType" class="drugTypeName" :style="getDrugTypeColor(item)">{{ item.drugTypeName||'' }}</view>
          <view class="text_title letter">{{ item.drugName }}</view>
        </view>
<!--        <view class="text_title">{{ item.drugName }}</view>-->
        <view class="text_info">规格：{{ item.gg }}</view>
        <view class="text_info"
          >药店：{{ item.drugstoreName
          }}{{ item.isProprietary == '1' ? '（自营）' : '' }}</view
        >
        <view class="active" v-show="item.activeName"
          >单品{{ item.activeName }}</view
        >
        <view class="text_price">
<!--          <text>单价：</text>-->
          ￥{{ item.price }}
        </view>
      </view>
    </view>
    <!-- 库存操作 -->
    <view class="drug_action">
      <!-- 操作 -->
      <view class="action" v-show="item.quan && item.drugKc">
        <image @click="reduce" class="icon" src="/static/shop/del.png"></image>
        <text class="num">{{ item.quan }}</text>
        <image @click="add" class="icon" src="/static/shop/add.png"></image>
      </view>
      <!-- 添加 有库存 且未选中 -->
      <view class="action" v-show="!item.quan && item.drugKc">
        <image @click="add" class="icon" src="/static/shop/add.png"></image>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'DrugCard',
  props: {
    index: Number,
    item: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      errUrl: require('../../../static/images/Pharmacy-default.png'),
    };
  },
  methods: {
    getDrugTypeColor(item){
      const map={
        '025.6': 'rgb(244, 92, 98)',
        '025.7': 'rgb(105, 211, 161)',
        '025.8': '#FFB800',
        '025.9': 'rgb(107, 202, 215)',
      }
      return `background:${map[item.drugType]} !important`;
    },
    add() {
      this.$emit('add', this.item, this.index);
    },
    reduce() {
      this.$emit('reduce', this.item, this.index);
    },
    toDrugDetail(item) {
      this.$store.commit("shop/SET_DrugDetailStore",{
        drugstoreName:item.drugstoreName,
        drugstoreId:item.drugstoreId
      })
      let { drugKc, quan, yfkcId, drugId } = item;
      uni.navigateTo({
        url:
          '/pages/shop/detail/drug?id=' +
          drugId +
          '&drugKc=' +
          drugKc +
          '&quan=' +
          quan +
          '&yfkcId=' +
          yfkcId+
            '&drugstoreName='+item.drugstoreName+
            '&drugstoreId='+item.drugstoreId
      });
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
   .count {
      min-width: 128rpx;
      text-align: center;
      font-size: 20rpx;
      color: #999;
     margin-top: 14rpx;
    }
.drug_card {
  width: 100%;
  padding: 24rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
position: relative;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  .card {
    @include flex;

    .card_img {
      width: 276rpx;
      height: 206rpx;
      border-radius: 8rpx;
    }

    .card_text {
      flex: 1;
      min-height: 128rpx;
      padding-left: 24rpx;
      @include flex(lr);
      flex-direction: column;
      align-items: stretch;

      .text_title {
        width: 250rpx;
        font-size: 28rpx;
        font-weight: bold;
        line-height: 40rpx;
      }

      .text_info {
        font-size: 20rpx;
        color: #999;
        line-height: 40rpx;
      }

      .active {
        color: red;
        font-size: 24rpx;
      }

      .text_price {
        font-size: 34rpx;
        color: #ff3b30;
        font-weight: bold;
        margin-top: 16rpx;
        text {
          font-size: 24rpx;
          font-weight: normal;
          color: #999;
        }
      }
    }
  }

  .drug_action {
    position: absolute;
    right: 20rpx;
    bottom: 30rpx;
    margin-top: 10rpx;
    @include flex(lr);
    height: 40rpx;



    .action {
      @include flex;

      .icon {
        width: 36rpx;
        height: 36rpx;
      }

      .num {
        width: 60rpx;
        height: 36rpx;
        @include flex;
        font-size: 28rpx;
        background-color: #fff;
        margin: 0 16rpx;
        border-radius: 4rpx;
      }
    }
  }
}
.drug-coverUp{
  width: 276rpx;
  height: 206rpx;
  background: rgba(255, 255, 255, 0.5);
  position: absolute;
  top: 0;
  left: 0;
  flex: 1;
}
.coverUp{
  width: 206rpx !important;
  height: 206rpx !important;
}
.coverUps{
display: flex;
  justify-content: center;
}
.cfCover{
  text-align: center;
  font-size: 10px;
  background: none !important;
  display: flex;
  align-items: center;
}
.gx{
  filter: blur(2px);
}
.cover-title{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
}
.drugTypeName{
  white-space: nowrap;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: red;
  color: white;
  padding:0 7px;
  border-radius: 6px;
  margin-right: 5px;
}
.letter{
  // 超出一行自动省略号
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
