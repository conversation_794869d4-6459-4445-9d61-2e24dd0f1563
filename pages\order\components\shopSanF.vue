<template>
  <!-- 在线购药 -->
  <view class="shop" @click="toPay">
    <!-- 订单号 -->
    <view class="order_num">
      <text v-if="item.payStatus == 1"> 提交时间：{{ item.addTime }}</text>
      <text v-if="item.payStatus == 3">
        <block v-if="item.deliveryType == 1">
          支付时间：{{ item.payTime }}
        </block>

        <block v-if="item.deliveryType == 2">
          发货时间：{{ item.deliveryTime }}
        </block>
      </text>
      <text v-if="item.payStatus == 4"> 发货时间：{{ item.deliveryTime }}</text>
      <text v-if="item.payStatus == 7"> 退费时间：{{ item.returnTime }}</text>
      <text v-if="item.payStatus == 10"> 收货时间：{{ item.receiveTime }}</text>
      <text class="status wait" v-if="item.payStatus == 1">待支付</text>
      <text class="status wait" v-if="item.payStatus == 2">已支付</text>
      <text class="status wait" v-if="item.payStatus == 3">
        <block v-if="item.deliveryType == 1"> 待发货 </block>

        <block v-if="item.deliveryType == 2"> 待取药 </block>
      </text>
      <text class="status wait" v-if="item.payStatus == 4">待收货</text>
      <text class="status close" v-if="item.payStatus == 9">交易关闭</text>
      <text class="status" v-if="item.payStatus == 7">已退费</text>
      <text class="status done" v-if="item.payStatus == 10">交易完成</text>
    </view>
    <!-- 订单号 -->
    <view class="order_num">
      <text>订单编号：{{ item.orderNo }}</text>
    </view>
    <!-- 药店 药品列表 -->
    <view
      class="pharmacy_list"
      v-for="(p, pn) in item.logisticsListMallVOS"
      :key="pn"
    >
      <view class="pharmacy">
        <!-- 物流名称 -->
        <text>{{ p.logisticsName }}---- {{ p.logisticsCode }}</text>
      </view>

      <!-- 药品列表 -->
      <view class="durg_list">
        <!-- 单个药品 -->
        <view class="durg_item" v-for="(d, dn) in p.drugShoppingList" :key="dn">
          <UniImage v-if="d.drugImg"
            :src="d.drugImg"
            :data-src="errUrl"
            class="left"
          />
          <image v-else class="left" src="/static/shop/drug.png"></image>
          <!-- 内容 -->
          <view class="right">
            <!-- 药品名称 -->
            <view class="drug_name">{{ d.drugName }}</view>
            <!-- 规格 -->
            <view class="drug_info">规格: {{ d.gg }}</view>
            <!-- 活动 -->
            <view class="drug_red" v-if="d.activeName"
              >单品{{ d.activeName }}</view
            >
            <!-- 价位数量 -->
            <view class="right_menu">
              <view class="price"
                >￥{{ d.drugRealMoney | toFixed }}
                <text class="del" v-if="d.drugShouldMoney != d.drugRealMoney"
                  >￥{{ d.drugShouldMoney | toFixed }}</text
                >
              </view>
              <text class="num">x{{ Number(d.quan) }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 根据状态 显示按钮 -->
      <view
        class="status_buts"
        v-if="
          (item.payStatus == 4 || item.payStatus == 10) &&
          item.deliveryType == 1
        "
      >
        <!-- 快递 -->
        <block v-if="item.deliveryType == 1">
          <text class="one" @click.stop="lockLogist(p)">查看物流</text>
          <!--          <text-->
          <!--            v-if="p.logisticsStatus == 1"-->
          <!--            @click.stop="confirmGood(p.merchantsOrderNo)"-->
          <!--            >确认收货</text-->
          <!--          >-->
          <!--          <text v-if="p.logisticsStatus == 0" @click.stop="toast">催物流</text>-->
        </block>
      </view>
    </view>
    <!-- 统计 -->
    <view class="count">
      <view class="count_price">
        总价：<text>￥{{ item.orderShouldMoney | toFixed }}</text>
      </view>
      <view class="count_price">
        优惠：<text>￥{{ item.orderDisMoney | toFixed }}</text>
      </view>
      <view class="count_price">
        实付款：<text>￥{{ item.orderRealMoney | toFixed }}</text>
      </view>
    </view>
    <!-- 待支付状态 -->
    <view class="pay_buts" v-if="item.payStatus == 1">
      <text @click.stop="toPay">去支付</text>
    </view>
    <!-- 待发货状态 -->
    <!--    <view class="pay_buts" v-if="item.payStatus == 3 && item.deliveryType == 1">-->
    <!--      <text @click.stop="applyRefund">申请退费</text>-->
    <!--    </view>-->
  </view>
</template>

<script>
import { refundAndAddYf } from "@/api/shop.js";
import { Toast } from "@/common/js/pay.js";
export default {
  name: "Shop",
  props: ["item", "pageType"],
  data() {
    return {
      errUrl: require("../../../static/shop/drug.png"),
    };
  },
  mounted() {
    console.log("查看传递详情字段11111", this.item);
  },
  methods: {
    // 提示
    toast() {
      Toast("已催物流");
    },
    // 确认收货
    confirmGood(merchantsOrderNo) {
      const { businessId, orderNo } = this.item;
      let obj = {
        merchantsOrderNo,
        businessId,
        orderNo,
      };
      this.$emit("confirmGood", obj);
    },
    // 查看物流
    lockLogist(item) {
      if (!item.logisticsCode) {
        Toast("未查询到物流单号");
        return;
      }
      console.log(item);
      uni.navigateTo({
        url:
          "/pages/personalCenter/myOrder/allOrder/prescriptionOrder/logisticsSanF?code=" +
          item.logisticsCode +
          "&yxOrderSn=" +
          this.item.yxOrderSn +
          "&name=" +
          item.logisticsName,
      });
    },
    getCount(list) {
      if (!list.length) return 0;
      let n = 0;
      list.forEach((v) => {
        v.drugShoppingOnlineOrderList.forEach((k) => {
          // n += Number(k.price) * Number(k.quan)
          n += Number(k.quan);
        });
      });

      return n;
    },
    // 点击去支付
    toPay() {
      if (this.pageType === "detail") return;
      uni.setStorageSync("hosId", this.item.hosId || "");
      uni.navigateTo({
        url:
          "/pages/order/detail/drugStatus?orderNo=" +
          this.item.orderNo +
          "&source=" +
          this.item.source,
      });
    },
    // 申请退费
    applyRefund() {
      console.log("111111", this.item);
      refundAndAddYf({ orderNo: this.item.orderNo }).then((res) => {
        uni.showToast({
          title: "申请退款成功！",
          icon: "none",
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.shop {
  width: 100%;
  padding: 0 24rpx 24rpx;
  background-color: #fff;
  margin-bottom: 24rpx;
  font-size: 28rpx;
  border-radius: 8rpx;
  box-shadow: 0 0 20rpx #ddd;

  .order_num {
    @include flex(lr);
    font-size: 28rpx;
    font-weight: bold;
    height: 88rpx;
    border-bottom: 1px solid #ebebeb;

    &:first-child {
      border: none;
      height: 60rpx;
      padding-top: 10rpx;
    }

    text::nth-child(2) {
      font-weight: normal;
    }

    .status {
      font-weight: normal;

      &.wait {
        color: red;
      }

      &.done {
        @include font_theme;
      }
    }
  }

  .pharmacy_list {
    border-bottom: 1px dashed #ebebeb;

    .pharmacy {
      @include flex(lr);
      font-size: 28rpx;
      font-weight: bold;
      height: 88rpx;
      border-bottom: 1px solid #ebebeb;

      text::nth-child(2) {
        font-weight: normal;
      }

      text {
        color: #333;

        &.wait {
          color: #ff5050;
        }

        &.close {
          color: #999;
        }

        &.done {
          @include font_theme;
        }
      }
    }

    .status_buts {
      @include flex(right);
      height: 88rpx;

      text {
        @include border_theme;
        @include flex;
        font-size: 26rpx;
        margin-left: 24rpx;
        width: 158rpx;
        height: 56rpx;
        border-radius: 28rpx;
        @include bg_theme;
        color: #fff;

        &.one {
          background: none;
          @include font_theme;
        }
      }
    }

    .durg_list {
      .durg_item {
        @include flex;
        padding: 24rpx 0;

        &:last-child {
          .right {
            border-bottom: none;
          }
        }

        .left {
          width: 128rpx;
          height: 128rpx;
          margin-right: 24rpx;
          border: 1px solid #eee;
          border-radius: 8rpx;
          flex: none;
        }

        .right {
          flex: 1;
          min-height: 128rpx;
          @include flex(lr);
          flex-direction: column;
          align-items: stretch;
          border-bottom: 1px solid #eee;

          .drug_name {
            flex: 1;
            font-size: 28rpx;
            font-weight: bold;
          }

          .drug_info {
            flex: 1;
            font-size: 24rpx;
            color: #999;
          }

          .drug_red {
            font-size: 24rpx;
            color: red;
            flex: 1;
          }

          .right_menu {
            flex: 2;
            @include flex(lr);
            align-items: flex-end;

            .price {
              font-size: 28rpx;
              color: #ff3b30;
              font-weight: bold;

              .del {
                font-size: 20rpx;
                color: #999;
                margin-left: 10rpx;
                text-decoration: line-through;
              }
            }

            .num {
              font-size: 28rpx;
            }
          }
        }
      }
    }
  }

  .count {
    @include flex(left);
    height: 88rpx;
    font-size: 28rpx;

    .count_num {
      color: #333;
    }

    .count_price {
      margin-right: 24rpx;
      text {
        color: #ff3b30;
        font-weight: bold;
      }
    }
  }

  .pay_buts {
    @include flex(right);

    text {
      @include flex;
      width: 158rpx;
      height: 56rpx;
      border-radius: 28rpx;
      @include bg_theme;
      color: #fff;
      font-size: 26rpx;
    }
  }
}
</style>
