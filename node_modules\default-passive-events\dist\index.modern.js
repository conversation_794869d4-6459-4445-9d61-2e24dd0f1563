const e=["scroll","wheel","touchstart","touchmove","touchenter","touchend","touchleave","mouseout","mouseleave","mouseup","mousedown","mousemove","mouseenter","mousewheel","mouseover"];var t;(()=>{let e=!1;try{const t=Object.defineProperty({},"passive",{get(){e=!0}});window.addEventListener("test",null,t),window.removeEventListener("test",null,t)}catch(e){}return e})()&&(t=EventTarget.prototype.addEventListener,EventTarget.prototype.addEventListener=function(o,n,r){const s="object"==typeof r&&null!==r,a=s?r.capture:r;var i;(r=s?(e=>{const t=Object.getOwnPropertyDescriptor(e,"passive");return t&&!0!==t.writable&&void 0===t.set?Object.assign({},e):e})(r):{}).passive=void 0!==(i=r.passive)?i:-1!==e.indexOf(o)&&!0,r.capture=void 0!==a&&a,t.call(this,o,n,r)},EventTarget.prototype.addEventListener._original=t);
//# sourceMappingURL=index.modern.js.map
