"use strict";
const plugins_xeUtils_slice = require("./slice.js");
function once(callback, context) {
  var done = false;
  var rest = null;
  var args = plugins_xeUtils_slice.slice(arguments, 2);
  return function() {
    if (done) {
      return rest;
    }
    rest = callback.apply(context, plugins_xeUtils_slice.slice(arguments).concat(args));
    done = true;
    return rest;
  };
}
exports.once = once;
