# init/index.vue 小程序参数获取迁移文档

## 🎯 迁移目标
将 `pages/init/index.vue` 中的参数获取方式从 H5 的 URL 参数获取改为适合小程序的 `onLoad(options)` 方式。

## 📝 主要修改内容

### 1. 参数获取方式改变

**原来的方式（H5）：**
```javascript
// 地址所有参数
const PARAMS = myJsTools.getUrlParam()

// 在方法中使用
this.appid = myJsTools.request('appid')
this.code = myJsTools.request('code')
```

**新的方式（小程序兼容）：**
```javascript
// 页面参数（将在 onLoad 中获取）
let PARAMS = {}

// 在 onLoad 中获取参数
async onLoad(options) {
  // 获取页面参数（小程序方式）
  PARAMS = options || {}
  console.log('页面参数:', PARAMS)
  
  // 直接从 options 中获取
  this.appid = options.appid || ''
  this.code = options.code || ''
}
```

### 2. 添加兼容性工具方法

```javascript
// 获取参数的兼容方法
getParam(key, options = {}) {
  // 优先从 onLoad 的 options 中获取
  if (options[key]) {
    return options[key]
  }
  
  // 从全局 PARAMS 中获取
  if (PARAMS[key]) {
    return PARAMS[key]
  }
  
  // #ifdef H5
  // H5 环境下可以从 URL 中获取
  if (typeof window !== 'undefined' && window.location) {
    const urlParams = new URLSearchParams(window.location.search)
    return urlParams.get(key) || ''
  }
  // #endif
  
  return ''
}
```

### 3. 跨平台兼容性处理

#### DOM 操作兼容性
```javascript
// 隐藏商城入口
appendStyle() {
  // #ifdef H5
  let style = document.createElement('style')
  let sty = 'uni-tabbar .uni-tabbar__item:nth-of-type(4) { display: none !important; }'
  style.innerHTML = sty
  document.head.appendChild(style)
  // #endif
  
  // #ifdef MP || APP-PLUS
  // 小程序和App中通过设置tabBar来隐藏
  uni.hideTabBarRedDot({
    index: 3 // 第4个tab（索引从0开始）
  })
  // #endif
}
```

#### 退出程序兼容性
```javascript
// 环信注册/登录失败，点击确定，退出程序
outLogin() {
  this.isModelToastShow = false
  // #ifdef H5
  if (typeof WeixinJSBridge !== 'undefined') {
    WeixinJSBridge.call('closeWindow')
  } else {
    window.close()
  }
  // #endif
  
  // #ifdef MP
  // 小程序中无法直接退出，可以跳转到首页
  uni.reLaunch({
    url: '/pages/index/index'
  })
  // #endif
  
  // #ifdef APP-PLUS
  // App中可以退出应用
  plus.runtime.quit()
  // #endif
}
```

## 🔄 已修改的参数

以下参数已从 `myJsTools.request()` 改为 `options.xxx`：

- `action` - 操作类型
- `appid` - 应用ID
- `code` - 微信授权码
- `docId` - 医生ID
- `projectId` - 项目ID
- `hosId` - 医院ID
- `isNew` - 是否新用户
- `officialAccount` - 公众号标识
- `conferenceId` - 会议ID
- `regId` - 挂号ID
- `ppiId` - 检查单ID
- `pliId` - 检验单ID
- `page` - 页面标识
- `qrCodeId` - 二维码ID
- `deptId` - 科室ID
- `isVideo` - 是否视频
- `dpmpId` - 医嘱ID
- `msgid` - 消息ID
- `orderNo` - 订单号
- `businessCode` - 业务代码
- `businessId` - 业务ID
- `businessid` - 业务ID（小写）

## 🚀 使用方法

### 页面跳转时传参
```javascript
// 跳转到 init 页面并传递参数
uni.navigateTo({
  url: '/pages/init/index?appid=wx123&code=abc&action=login'
})

// 或者使用 reLaunch
uni.reLaunch({
  url: '/pages/init/index?docId=123&hosId=456'
})
```

### 在页面中获取参数
```javascript
// 在 onLoad 中自动获取
async onLoad(options) {
  console.log('接收到的参数:', options)
  // options = { appid: 'wx123', code: 'abc', action: 'login' }
}
```

## ⚠️ 注意事项

1. **参数类型**：小程序中所有参数都是字符串类型，需要注意类型转换
2. **参数长度限制**：小程序对 URL 长度有限制，复杂参数建议通过其他方式传递
3. **特殊字符**：参数中的特殊字符需要进行 URL 编码
4. **兼容性测试**：需要在 H5、小程序、App 三个平台都进行测试

## 🧪 测试建议

1. **H5 测试**：确保原有的 URL 参数获取功能正常
2. **小程序测试**：测试页面跳转传参和参数接收
3. **App 测试**：验证 App 环境下的参数传递
4. **边界测试**：测试参数为空、特殊字符等边界情况

## 📋 后续优化

1. 可以考虑将参数获取逻辑封装成全局 mixin
2. 对于复杂参数，可以考虑使用 Vuex 或本地存储
3. 添加参数验证和默认值处理机制
