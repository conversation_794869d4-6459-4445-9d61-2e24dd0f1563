!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.websdk=t():e.websdk=t()}(self,(function(){return(self.webpackChunkwebsdk=self.webpackChunkwebsdk||[]).push([[472],{6676:function(e,t,r){r.r(t),r.d(t,{getSupportedLanguages:function(){return o},translateMessage:function(){return c}}),r(8706),r(4346),r(739),r(6099),r(3362);var a=r(1750),n=r(2056),s=r(8678);function o(){var e=a.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,r=t.orgName,o=t.appName,c=t.accessToken,u={url:"".concat(this.apiUrl,"/").concat(r,"/").concat(o,"/users/").concat(this.user,"/translate/support/language"),dataType:"json",type:"GET",headers:{Authorization:"Bearer "+c}};return n.vF.debug("Call getSupportedLanguages"),s.RD.call(this,u)}function c(e){if("string"!=typeof e.text||""===e.text)throw Error('Invalid parameter: "text"');if(!Array.isArray(e.languages))throw Error('Invalid parameter: "language"');var t=a.dO.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,c=r.appName,u=r.accessToken,i="".concat(this.apiUrl,"/").concat(o,"/").concat(c,"/users/").concat(this.user,"/translate"),l={text:e.text,to:e.languages},p={url:i,dataType:"json",type:"POST",data:JSON.stringify(l),headers:{Authorization:"Bearer "+u}};return n.vF.debug("Call translateMessage"),s.RD.call(this,p)}}},function(e){return e(e.s=6676)}])}));