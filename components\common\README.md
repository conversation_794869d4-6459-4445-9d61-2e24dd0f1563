# UniImage 通用图片组件

这是一个用于替代 `v-img` 自定义指令的通用图片组件，支持 fileId 转换为图片链接，完全兼容 uni-app 小程序开发。

## 功能特点

- ✅ 支持 fileId 自动转换为图片链接
- ✅ 支持完整 URL 直接显示
- ✅ 内置图片缓存机制，避免重复请求
- ✅ 支持默认图片和错误图片
- ✅ 支持点击预览功能
- ✅ 完全兼容小程序、H5、App
- ✅ 支持所有 image 组件的原生属性
- ✅ 支持自定义样式和尺寸

## 基本用法

### 1. 全局注册（推荐）

在 `main.js` 中注册：

```javascript
import UniImage from '@/components/common/UniImage.vue'

const app = createSSRApp(App)
app.component('UniImage', UniImage)
```

### 2. 局部引入

```vue
<script>
import UniImage from '@/components/common/UniImage.vue'

export default {
  components: {
    UniImage
  }
}
</script>
```

## 使用示例

### 基础用法

```vue
<template>
  <!-- 使用 fileId -->
  <UniImage :src="fileId" />
  
  <!-- 使用完整 URL -->
  <UniImage :src="imageUrl" />
  
  <!-- 设置尺寸 -->
  <UniImage 
    :src="fileId" 
    :width="200" 
    :height="200" 
  />
</template>
```

### 高级用法

```vue
<template>
  <!-- 启用点击预览 -->
  <UniImage 
    :src="fileId" 
    :preview="true"
    :preview-urls="imageList"
  />
  
  <!-- 自定义默认图片和错误图片 -->
  <UniImage 
    :src="fileId"
    default-src="/static/images/placeholder.png"
    error-src="/static/images/error.png"
  />
  
  <!-- 自定义样式 -->
  <UniImage 
    :src="fileId"
    custom-class="my-image"
    :custom-style="{ borderRadius: '10rpx' }"
    mode="aspectFit"
  />
</template>
```

### 替换原有的 v-img 指令

```vue
<!-- 原来的写法 -->
<img v-img="fileId" @click="handleClick" />

<!-- 新的写法 -->
<UniImage :src="fileId" @click="handleClick" />
```

## Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| src | String | '' | 图片源，可以是 fileId 或完整 URL |
| defaultSrc | String | '/static/images/default.png' | 默认图片 |
| errorSrc | String | '/static/images/error.png' | 错误时显示的图片 |
| mode | String | 'aspectFill' | 图片裁剪、缩放的模式 |
| lazyLoad | Boolean | true | 图片懒加载 |
| fadeShow | Boolean | true | 图片显示动画效果 |
| webp | Boolean | false | 是否启用 webp |
| showMenuByLongpress | Boolean | false | 长按显示菜单 |
| preview | Boolean | false | 是否启用点击预览 |
| previewUrls | Array | [] | 预览时的图片列表 |
| customClass | String | '' | 自定义样式类 |
| customStyle | Object | {} | 自定义样式 |
| width | String/Number | '' | 宽度 |
| height | String/Number | '' | 高度 |

## Events 事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| click | 点击图片时触发 | event |
| load | 图片加载成功时触发 | event |
| error | 图片加载失败时触发 | event |
| load-success | fileId 转换成功时触发 | url |
| load-error | fileId 转换失败时触发 | error |

## 迁移指南

### 从 v-img 指令迁移

1. **基础替换**：
   ```vue
   <!-- 旧写法 -->
   <img v-img="fileId" />
   
   <!-- 新写法 -->
   <UniImage :src="fileId" />
   ```

2. **带点击预览的替换**：
   ```vue
   <!-- 旧写法 -->
   <img v-img:click="fileId" />
   
   <!-- 新写法 -->
   <UniImage :src="fileId" :preview="true" />
   ```

3. **批量替换建议**：
   - 使用编辑器的查找替换功能
   - 查找：`<img v-img="([^"]+)"`
   - 替换：`<UniImage :src="$1"`
   - 然后手动调整其他属性

## 注意事项

1. 确保项目中已经配置了 `myJsTools.downAndSaveImg` 方法
2. 根据项目需要调整默认图片路径
3. 如果使用全局注册，确保在 `main.js` 中正确引入
4. 组件内部使用了全局缓存，可以有效避免重复请求相同的 fileId
