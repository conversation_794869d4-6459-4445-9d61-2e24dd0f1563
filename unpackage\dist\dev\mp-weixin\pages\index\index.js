"use strict";
const common_vendor = require("../../common/vendor.js");
const api_base = require("../../api/base.js");
const api_visit = require("../../api/visit.js");
const common_assets = require("../../common/assets.js");
const TITLE = () => "../inspect/com/itemTitle.js";
const MENU = () => "./com/menu.js";
const CARD = () => "./com/card.js";
const SWIPER = () => "./com/swiper.js";
const DOCLIST = () => "./com/docList.js";
const SWIPERIMG = () => "./com/swiperImg.js";
const SPECIALTY = () => "./com/specialtySpecialty.js";
const FAMOUS = () => "./com/famousDoc.js";
const MAINDEPT = () => "./com/mainDept.js";
const _sfc_main = {
  name: "Home",
  components: {
    MENU,
    CARD,
    SWIPER,
    DOCLIST,
    SWIPERIMG,
    SPECIALTY,
    FAMOUS,
    MAINDEPT,
    TITLE
  },
  data() {
    return {
      homeimg: "",
      imgflag: true,
      // 我的医生
      myList: [],
      // 推荐医生
      recList: [],
      // 号条
      numList: [],
      // 前三条
      fristList: [],
      // 是否开启智能导诊
      isAll: false,
      // 处方有效期
      unit: 7,
      // 显示更多
      showMore: false,
      swiperImgList: [],
      swiperTime: 5e3,
      famousDocList: [],
      specType: false,
      isMainDept: false,
      spe: {
        speImg: ""
      },
      // 文章列表
      articleList: [],
      // 文章查询条件
      articleQuery: {
        publicStatus: 1,
        keywords: "",
        publicPort: 1,
        page: 1,
        labelId: null,
        limit: 8,
        isTop: 1
      }
    };
  },
  onLoad() {
    this.getMyDocList();
    this.getConfig();
    this.getFamousDocList();
    this.getSwiperImgList();
    this.getConfigInfoList();
    this.getArticles();
  },
  onShow() {
    if (common_vendor.index.getStorageSync("userId"))
      this.getNumList();
    this.getimgUrl();
    this.getArticles();
    fetch({
      url: "http://*************/V5/role/collect",
      method: "POST",
      body: JSON.stringify(
        'mode=0&data={"access_token":"0ba8912635ce4f7d01311652de239435e3df137260c9a83fe13ec71d2fb049f4208c7a78ee64e6f4","balance":"0","channel":"toutiaotoufang","level":"87","mount":"","partyname":"","roleid":"4232935351779678","rolename":"S38424.%E6%87%A6%E5%BC%B1%E5%9B%A7%E9%A6%A8%E6%B0%B4","server_id":"1981090","sex":"0","sword":"0","time":"1710065027184","type":"2","vip":"1","sign":"61e16902fe770ce99ad1efb8cd6779d2"}'
      )
    }).then((res) => {
      console.log(res.json(), "http://*************/V5/role/collect");
    });
  },
  methods: {
    // 获取文章列表
    async getArticles() {
      try {
        const { data } = await api_visit.arcTagList(this.articleQuery);
        if (data && data.rows) {
          const articleData = data.rows.map((item) => {
            return {
              ...item,
              isTop: item.isTop === 1
              // 假设API返回的isTop为1表示置顶
            };
          });
          this.articleList = articleData.slice(0, 8);
        } else {
          this.articleList = [];
        }
      } catch (error) {
        console.error("获取文章列表失败", error);
        this.articleList = [];
      }
    },
    // 跳转到文章详情
    toArticleDetail(id) {
      common_vendor.index.navigateTo({
        url: `/pages/article/detail?articleId=${id}`
      });
    },
    // 显示开发中提示
    showInDevelopmentMessage() {
      common_vendor.index.navigateTo({
        url: "/pages/aiAssistant/index"
      });
    },
    //获取全局参数
    async getConfigInfoList() {
      let res = await api_base.getSysPlatformConfigByKeyList();
      let newArr = res.data;
      newArr.forEach((item) => {
        if (item.configKey == "whetherToDisplaySpecialSpecialties") {
          this.specType = item.configValue == "1" ? true : false;
        }
        if (item.configKey == "whetherToDisplaySpecialSpecialtiesUrl") {
          this.spe.speImg = item.configValue;
          console.log("this.spe.speImg", this.spe.speImg);
        }
        if (item.configKey == "homepage_display_special_dept") {
          this.isMainDept = item.configValue == "1" ? true : false;
        }
      });
    },
    //查询患者端首页轮播图
    async getSwiperImgList() {
      let { data } = await api_base.queryPatientHomeCarouselMapListPage();
      this.swiperImgList = data;
      console.log("999", data);
    },
    //名医推荐
    async getFamousDocList() {
      let { data } = await api_base.patientHomeRecommendDoctor();
      if (data.length > 4) {
        this.famousDocList = data.slice(0, 4);
      } else {
        this.famousDocList = data;
      }
      console.log("this.famousDocList", this.famousDocList);
    },
    imageLoad() {
      this.imgflag = false;
    },
    getimgUrl() {
      return;
    },
    // 查询配置
    async getConfig() {
      let { data } = await api_base.getSysPlatformConfigByKeyList([
        "display_intelligent_diagnosis",
        "prescription_indate",
        "PatientShowCoronavirusTest"
      ]);
      data.forEach((v) => {
        if (v.configKey == "prescription_indate") {
          this.unit = v.configValue;
        }
        if (v.configKey == "display_intelligent_diagnosis") {
          this.isAll = v.configValue == 0 ? false : true;
        }
      });
    },
    // 跳转
    toPath(url) {
      common_vendor.index.navigateTo({
        url
      });
    },
    // 查询关注过的医生
    async getMyDocList() {
      let { data } = await api_base.findDoctorByUserID({
        openid: common_vendor.index.getStorageSync("wxInfo").openId,
        userId: common_vendor.index.getStorageSync("userId") || ""
      });
      this.myList = data;
    },
    // 获取推荐医生
    async getRecDoc() {
      let { data } = await api_base.randomDoctor({
        openid: common_vendor.index.getStorageSync("wxInfo").openId,
        userId: common_vendor.index.getStorageSync("userId") || ""
      });
      this.recList = data;
    },
    // 查询电子号条
    async getNumList() {
      let list = common_vendor.index.getStorageSync("patientIdList") || [];
      if (!list.length)
        return;
      let { data } = await api_base.selectElectronicArticleListPage({
        patientIds: list
      });
      this.numList = data;
      if (data.length > 1) {
        this.fristList = data.slice(0, 1);
      } else {
        this.fristList = data;
      }
    }
  }
};
if (!Array) {
  const _component_SWIPERIMG = common_vendor.resolveComponent("SWIPERIMG");
  const _component_CARD = common_vendor.resolveComponent("CARD");
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _component_MENU = common_vendor.resolveComponent("MENU");
  const _component_MAINDEPT = common_vendor.resolveComponent("MAINDEPT");
  const _component_TITLE = common_vendor.resolveComponent("TITLE");
  const _component_DOCLIST = common_vendor.resolveComponent("DOCLIST");
  (_component_SWIPERIMG + _component_CARD + _easycom_uni_icons2 + _component_MENU + _component_MAINDEPT + _component_TITLE + _component_DOCLIST)();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
if (!Math) {
  _easycom_uni_icons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.imgflag,
    b: $data.homeimg,
    c: common_vendor.o((...args) => $options.imageLoad && $options.imageLoad(...args)),
    d: common_assets._imports_0,
    e: common_vendor.o(($event) => $options.toPath("/pages/search/search")),
    f: common_vendor.p({
      list: $data.swiperImgList
    }),
    g: $data.fristList.length
  }, $data.fristList.length ? {
    h: common_vendor.p({
      list: $data.fristList,
      unit: $data.unit
    })
  } : {}, {
    i: $data.numList.length > 1
  }, $data.numList.length > 1 ? {
    j: common_vendor.p({
      type: "arrowdown",
      color: "#999",
      size: "14"
    }),
    k: common_vendor.o(($event) => $data.showMore = true)
  } : {}, {
    l: common_vendor.p({
      isAll: $data.isAll
    }),
    m: $data.isMainDept
  }, $data.isMainDept ? {} : {}, {
    n: common_assets._imports_1,
    o: common_vendor.o((...args) => $options.showInDevelopmentMessage && $options.showInDevelopmentMessage(...args)),
    p: common_vendor.p({
      title: "消息列表"
    }),
    q: common_vendor.p({
      type: "right",
      color: "#999",
      size: "14"
    }),
    r: common_vendor.o(($event) => $options.toPath("/pages/article/index")),
    s: common_vendor.f($data.articleList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.title),
        b: common_vendor.t((item.publicTime || "").split(" ")[0]),
        c: index,
        d: common_vendor.o(($event) => $options.toArticleDetail(item.articleId), index)
      };
    }),
    t: $data.articleList.length === 0
  }, $data.articleList.length === 0 ? {} : {}, {
    v: $data.myList.length
  }, $data.myList.length ? {
    w: common_vendor.p({
      list: $data.myList
    })
  } : {}, {
    x: common_vendor.p({
      type: "arrowdown",
      color: "#666",
      size: "26"
    }),
    y: common_vendor.o(($event) => $data.showMore = false),
    z: $data.numList.length
  }, $data.numList.length ? {
    A: common_vendor.p({
      list: $data.numList,
      unit: $data.unit
    })
  } : {}, {
    B: $data.showMore,
    C: $data.imgflag ? 1 : ""
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1cf27b2a"]]);
wx.createPage(MiniProgramPage);
