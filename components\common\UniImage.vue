<template>
  <image
    :class="imageClass"
    :style="imageStyle"
    :src="currentSrc"
    :mode="mode"
    :lazy-load="lazyLoad"
    :fade-show="fadeShow"
    :webp="webp"
    :show-menu-by-longpress="showMenuByLongpress"
    @error="handleError"
    @load="handleLoad"
    @click="handleClick"
  />
</template>

<script>
import myJsTools from '@/common/js/myJsTools.js'

export default {
  name: 'UniImage',
  props: {
    // 图片源，可以是fileId或完整URL
    src: {
      type: String,
      default: ''
    },
    // 默认图片
    defaultSrc: {
      type: String,
      default: '/static/images/default.png'
    },
    // 错误时显示的图片
    errorSrc: {
      type: String,
      default: '/static/images/error.png'
    },
    // 图片裁剪、缩放的模式
    mode: {
      type: String,
      default: 'aspectFill'
    },
    // 图片懒加载
    lazyLoad: {
      type: Boolean,
      default: true
    },
    // 图片显示动画效果
    fadeShow: {
      type: Boolean,
      default: true
    },
    // 在系统不支持webp的情况下是否单独启用webp
    webp: {
      type: Boolean,
      default: false
    },
    // 开启长按图片显示识别小程序码菜单
    showMenuByLongpress: {
      type: Boolean,
      default: false
    },
    // 是否启用点击预览
    preview: {
      type: Boolean,
      default: false
    },
    // 预览时的图片列表（如果不传则使用当前图片）
    previewUrls: {
      type: Array,
      default: () => []
    },
    // 自定义样式类
    customClass: {
      type: String,
      default: ''
    },
    // 自定义样式
    customStyle: {
      type: Object,
      default: () => ({})
    },
    // 宽度
    width: {
      type: [String, Number],
      default: ''
    },
    // 高度
    height: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      currentSrc: '',
      isLoading: true,
      isError: false,
      imageUrlCache: {} // 静态缓存，所有实例共享
    }
  },
  computed: {
    imageClass() {
      return `uni-image ${this.customClass}`
    },
    imageStyle() {
      let style = { ...this.customStyle }
      
      if (this.width) {
        style.width = typeof this.width === 'number' ? `${this.width}rpx` : this.width
      }
      if (this.height) {
        style.height = typeof this.height === 'number' ? `${this.height}rpx` : this.height
      }
      
      return style
    }
  },
  watch: {
    src: {
      handler(newSrc) {
        this.loadImage(newSrc)
      },
      immediate: true
    }
  },
  // 使用全局缓存
  beforeCreate() {
    // 如果全局缓存不存在，创建一个
    if (!this.$root.$imageUrlCache) {
      this.$root.$imageUrlCache = {}
    }
    this.imageUrlCache = this.$root.$imageUrlCache
  },
  methods: {
    // 加载图片
    async loadImage(src) {
      if (!src || src === 'null') {
        this.currentSrc = this.defaultSrc
        return
      }

      // 如果已经是完整的URL，直接使用
      if (src.startsWith('http://') || src.startsWith('https://') || src.startsWith('/')) {
        this.currentSrc = src
        return
      }

      // 检查缓存
      if (this.imageUrlCache[src]) {
        this.currentSrc = this.imageUrlCache[src]
        return
      }

      // 设置默认图片，等待异步加载
      this.currentSrc = this.defaultSrc
      this.isLoading = true

      // 异步获取图片URL
      try {
        await myJsTools.downAndSaveImg(
          src,
          (url) => {
            if (url) {
              // 缓存URL
              this.imageUrlCache[src] = url
              this.currentSrc = url
              this.isLoading = false
              this.$emit('load-success', url)
            }
          },
          () => {
            console.log('图片加载失败:', src)
            this.imageUrlCache[src] = this.errorSrc
            this.currentSrc = this.errorSrc
            this.isLoading = false
            this.isError = true
            this.$emit('load-error', src)
          }
        )
      } catch (error) {
        console.error('加载图片失败:', error)
        this.imageUrlCache[src] = this.errorSrc
        this.currentSrc = this.errorSrc
        this.isLoading = false
        this.isError = true
        this.$emit('load-error', error)
      }
    },

    // 图片加载成功
    handleLoad(e) {
      this.isLoading = false
      this.isError = false
      this.$emit('load', e)
    },

    // 图片加载失败
    handleError(e) {
      this.isLoading = false
      this.isError = true
      this.currentSrc = this.errorSrc
      this.$emit('error', e)
    },

    // 点击事件
    handleClick(e) {
      this.$emit('click', e)
      
      // 如果启用预览功能
      if (this.preview && !this.isError) {
        this.previewImage()
      }
    },

    // 预览图片
    previewImage() {
      let urls = []
      let current = 0
      
      if (this.previewUrls.length > 0) {
        urls = this.previewUrls
        current = urls.indexOf(this.currentSrc)
        if (current === -1) current = 0
      } else {
        urls = [this.currentSrc]
        current = 0
      }

      uni.previewImage({
        urls: urls,
        current: current,
        fail: (err) => {
          console.error('预览图片失败:', err)
          uni.showToast({
            title: '预览失败',
            icon: 'none'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.uni-image {
  display: block;
  width: 100%;
  height: auto;
}
</style>
