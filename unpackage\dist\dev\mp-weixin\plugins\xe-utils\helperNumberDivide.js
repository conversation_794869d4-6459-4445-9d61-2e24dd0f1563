"use strict";
const plugins_xeUtils_helperNumberDecimal = require("./helperNumberDecimal.js");
const plugins_xeUtils_toNumberString = require("./toNumberString.js");
const plugins_xeUtils_multiply = require("./multiply.js");
function helperNumberDivide(divisor, dividend) {
  var str1 = plugins_xeUtils_toNumberString.toNumberString(divisor);
  var str2 = plugins_xeUtils_toNumberString.toNumberString(dividend);
  var divisorDecimal = plugins_xeUtils_helperNumberDecimal.helperNumberDecimal(str1);
  var dividendDecimal = plugins_xeUtils_helperNumberDecimal.helperNumberDecimal(str2);
  var powY = dividendDecimal - divisorDecimal;
  var isMinus = powY < 0;
  var multiplicand = Math.pow(10, isMinus ? Math.abs(powY) : powY);
  return plugins_xeUtils_multiply.multiply(str1.replace(".", "") / str2.replace(".", ""), isMinus ? 1 / multiplicand : multiplicand);
}
exports.helperNumberDivide = helperNumberDivide;
