"use strict";
const plugins_xeUtils_helperNumberDecimal = require("./helperNumberDecimal.js");
const plugins_xeUtils_toNumberString = require("./toNumberString.js");
function helperMultiply(multiplier, multiplicand) {
  var str1 = plugins_xeUtils_toNumberString.toNumberString(multiplier);
  var str2 = plugins_xeUtils_toNumberString.toNumberString(multiplicand);
  return parseInt(str1.replace(".", "")) * parseInt(str2.replace(".", "")) / Math.pow(10, plugins_xeUtils_helperNumberDecimal.helperNumberDecimal(str1) + plugins_xeUtils_helperNumberDecimal.helperNumberDecimal(str2));
}
exports.helperMultiply = helperMultiply;
