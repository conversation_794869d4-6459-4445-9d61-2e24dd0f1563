"use strict";
const plugins_xeUtils_arrayEach = require("./arrayEach.js");
const plugins_xeUtils_toArray = require("./toArray.js");
const plugins_xeUtils_map = require("./map.js");
const plugins_xeUtils_isArray = require("./isArray.js");
const plugins_xeUtils_isFunction = require("./isFunction.js");
const plugins_xeUtils_isPlainObject = require("./isPlainObject.js");
const plugins_xeUtils_isUndefined = require("./isUndefined.js");
const plugins_xeUtils_isNull = require("./isNull.js");
const plugins_xeUtils_eqNull = require("./eqNull.js");
const plugins_xeUtils_get = require("./get.js");
const plugins_xeUtils_property = require("./property.js");
var ORDER_PROP_ASC = "asc";
var ORDER_PROP_DESC = "desc";
function handleSort(v1, v2) {
  if (plugins_xeUtils_isUndefined.isUndefined(v1)) {
    return 1;
  }
  if (plugins_xeUtils_isNull.isNull(v1)) {
    return plugins_xeUtils_isUndefined.isUndefined(v2) ? -1 : 1;
  }
  return v1 && v1.localeCompare ? v1.localeCompare(v2) : v1 > v2 ? 1 : -1;
}
function buildMultiOrders(name, confs, compares) {
  return function(item1, item2) {
    var v1 = item1[name];
    var v2 = item2[name];
    if (v1 === v2) {
      return compares ? compares(item1, item2) : 0;
    }
    return confs.order === ORDER_PROP_DESC ? handleSort(v2, v1) : handleSort(v1, v2);
  };
}
function getSortConfs(arr, list, fieldConfs, context) {
  var sortConfs = [];
  fieldConfs = plugins_xeUtils_isArray.isArray(fieldConfs) ? fieldConfs : [fieldConfs];
  plugins_xeUtils_arrayEach.arrayEach(fieldConfs, function(handle, index) {
    if (handle) {
      var field = handle;
      var order;
      if (plugins_xeUtils_isArray.isArray(handle)) {
        field = handle[0];
        order = handle[1];
      } else if (plugins_xeUtils_isPlainObject.isPlainObject(handle)) {
        field = handle.field;
        order = handle.order;
      }
      sortConfs.push({
        field,
        order: order || ORDER_PROP_ASC
      });
      plugins_xeUtils_arrayEach.arrayEach(list, plugins_xeUtils_isFunction.isFunction(field) ? function(item, key) {
        item[index] = field.call(context, item.data, key, arr);
      } : function(item) {
        item[index] = field ? plugins_xeUtils_get.get(item.data, field) : item.data;
      });
    }
  });
  return sortConfs;
}
function orderBy(arr, fieldConfs, context) {
  if (arr) {
    if (plugins_xeUtils_eqNull.eqNull(fieldConfs)) {
      return plugins_xeUtils_toArray.toArray(arr).sort(handleSort);
    }
    var compares;
    var list = plugins_xeUtils_map.map(arr, function(item) {
      return { data: item };
    });
    var sortConfs = getSortConfs(arr, list, fieldConfs, context);
    var len = sortConfs.length - 1;
    while (len >= 0) {
      compares = buildMultiOrders(len, sortConfs[len], compares);
      len--;
    }
    if (compares) {
      list = list.sort(compares);
    }
    return plugins_xeUtils_map.map(list, plugins_xeUtils_property.property("data"));
  }
  return [];
}
exports.orderBy = orderBy;
