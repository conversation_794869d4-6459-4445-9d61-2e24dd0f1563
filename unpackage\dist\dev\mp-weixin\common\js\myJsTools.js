"use strict";
const common_vendor = require("../vendor.js");
const api_oss = require("../../api/oss.js");
const api_chat = require("../../api/chat.js");
const phone = (str) => {
  if (str.length < 11)
    return str;
  return str.substr(0, 3) + "****" + str.substr(7, 10);
};
const formatDuring = (mss) => {
  var days = parseInt(mss / (1e3 * 60 * 60 * 24));
  var hours = parseInt(mss % (1e3 * 60 * 60 * 24) / (1e3 * 60 * 60));
  var minutes = parseInt(mss % (1e3 * 60 * 60) / (1e3 * 60));
  var seconds = mss % (1e3 * 60) / 1e3;
  days = days == 0 ? "" : days + " 天 ";
  hours = hours == 0 ? "" : hours + " 小时 ";
  minutes = minutes == 0 ? "" : minutes + " 分钟 ";
  seconds = seconds == 0 ? "" : seconds + " 秒 ";
  return days + hours + minutes + seconds;
};
const formatTime = (date) => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  const second = date.getSeconds();
  return [year, month, day].map(formatNumber).join("/") + " " + [hour, minute, second].map(formatNumber).join(":");
};
const formatTimeDate = (date) => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  return [year, month, day].map(formatNumber).join("-");
};
const getDate = (type = null, number = 0) => {
  var nowdate = /* @__PURE__ */ new Date();
  switch (type) {
    case "day":
      nowdate.setTime(nowdate.getTime() + 24 * 3600 * 1e3 * number);
      var y = nowdate.getFullYear();
      var m = nowdate.getMonth() + 1;
      if (m < 10)
        m = "0" + m;
      var d = nowdate.getDate();
      if (d < 10)
        d = "0" + d;
      var retrundate = y + "-" + m + "-" + d;
      break;
    case "week":
      var weekdate = new Date(nowdate + 7 * 24 * 3600 * 1e3 * number);
      var y = weekdate.getFullYear();
      var m = weekdate.getMonth() + 1;
      if (m < 10)
        m = "0" + m;
      var d = weekdate.getDate();
      if (d < 10)
        d = "0" + d;
      var retrundate = y + "-" + m + "-" + d;
      break;
    case "month":
      nowdate.setMonth(nowdate.getMonth() + number);
      var y = nowdate.getFullYear();
      var m = nowdate.getMonth() + 1;
      if (m < 10)
        m = "0" + m;
      var d = nowdate.getDate();
      if (d < 10)
        d = "0" + d;
      var retrundate = y + "-" + m + "-" + d;
      break;
    case "year":
      nowdate.setFullYear(nowdate.getFullYear() + number);
      var y = nowdate.getFullYear();
      var m = nowdate.getMonth() + 1;
      var d = nowdate.getDate();
      var retrundate = y + "-" + m + "-" + d;
      break;
    default:
      var y = nowdate.getFullYear();
      var m = nowdate.getMonth() + 1;
      if (m < 10)
        m = "0" + m;
      var d = nowdate.getDate();
      if (d < 10)
        d = "0" + d;
      var retrundate = y + "-" + m + "-" + d;
  }
  return retrundate;
};
const formatNumber = (n) => {
  n = n.toString();
  return n[1] ? n : "0" + n;
};
const getBirthdayFromIdCard = function(idCard) {
  var birthday = "";
  if (idCard != null && idCard != "") {
    if (idCard.length == 15) {
      birthday = "19" + idCard.substr(6, 6);
    } else if (idCard.length == 18) {
      birthday = idCard.substr(6, 8);
    }
    birthday = birthday.replace(/(.{4})(.{2})/, "$1-$2-");
  }
  return birthday;
};
const getItem = function(key) {
  if (!window.localStorage) {
    alert("请检查浏览器版本，该版本不支持localStorage存储");
  } else {
    var ms = "mystorage";
    var storage = window.localStorage;
    var mydata = storage.getItem(ms);
    if (!mydata) {
      return false;
    }
    mydata = JSON.parse(mydata);
    return mydata.data[key];
  }
};
const downAndSaveImg = async (ossName, callback, errCallback) => {
  try {
    let res = await api_oss.downloadImg({
      imgName: ossName
    });
    if (res.code != 2e4)
      return;
    let url = res.data.url.split("-internal").join("").split("?")[0];
    if (url) {
      callback(url);
      return Promise.resolve(url);
    }
  } catch (error) {
    return Promise.reject("下载失败 -", ossName);
  }
};
const previewImg = (url) => {
  let src = url.split("?")[0];
  common_vendor.index.previewImage({
    urls: [src]
  });
};
const request = function(paras) {
  var url = location.href;
  console.log("查看当前链接", url);
  var paraString = url.substring(url.indexOf("?") + 1, url.length).split("&");
  var paraObj = {};
  for (var i = 0, j; j = paraString[i]; i++) {
    paraObj[j.substring(0, j.indexOf("=")).toLowerCase()] = decodeURIComponent(
      j.substring(j.indexOf("=") + 1, j.length)
    );
  }
  var returnValue = paraObj[paras.toLowerCase()];
  if (typeof returnValue == "undefined") {
    return "";
  } else {
    return returnValue;
  }
};
const getUrlParam = function() {
  var url = location.href;
  var paraString = url.substring(url.indexOf("?") + 1, url.length).split("&");
  var paraObj = {};
  for (var i = 0, j; j = paraString[i]; i++) {
    paraObj[j.substring(0, j.indexOf("="))] = decodeURIComponent(
      j.substring(j.indexOf("=") + 1, j.length)
    );
  }
  return paraObj;
};
let timer;
const msgReadFun = function(message) {
  clearTimeout(timer);
  timer = setTimeout(() => {
    let chatList = app.$store.getters.getChatList;
    chatList.chatRecordList.map((msg) => {
      var _a, _b;
      if (msg.mid == message.mid) {
        if (msg.status != "recall") {
          if (msg.status != "read") {
            let item = {
              patientId: (_a = msg.ext) == null ? void 0 : _a.patientId,
              patientName: (_b = msg.ext) == null ? void 0 : _b.patientName,
              userId: common_vendor.index.getStorageSync("userId"),
              type: "chat"
            };
            let param = {
              receiverIdArray: [msg.to],
              jgExpandMap: item,
              message: "患者给你发送了消息",
              patientId: item.patientId
            };
            api_chat.patientToDoctorSendMessageToMq(param);
          }
        }
      }
    });
  }, 2e3);
};
function setImgZip(obj, callback) {
  var file = obj;
  var reader = new FileReader();
  reader.readAsDataURL(file);
  reader.onload = function(e) {
    dealImage(
      this.result,
      {
        quality: 0.1
      },
      function(base) {
        var blob = dataURLtoBlob(base);
        var newFile = new File([blob], file.name, {
          type: file.type
        });
        let r = new FileReader();
        r.onload = function() {
          callback && callback(r.result);
        };
        r.readAsDataURL(newFile);
      }
    );
  };
}
function dataURLtoBlob(dataurl) {
  var arr = dataurl.split(","), mime = arr[0].match(/:(.*?);/)[1], bstr = atob(arr[1]), n = bstr.length, u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], {
    type: mime
  });
}
function dealImage(path, obj, callback) {
  var img = new Image();
  img.src = path;
  img.onload = function() {
    var that = this;
    var w = that.width, h = that.height, scale = w / h;
    w = obj.width || w;
    h = obj.height || w / scale;
    var quality = obj.quality || 0.1;
    var canvas = document.createElement("canvas");
    var ctx = canvas.getContext("2d");
    var anw = document.createAttribute("width");
    anw.nodeValue = w;
    var anh = document.createAttribute("height");
    anh.nodeValue = h;
    canvas.setAttributeNode(anw);
    canvas.setAttributeNode(anh);
    ctx.drawImage(that, 0, 0, w, h);
    if (obj.quality && obj.quality <= 1 && obj.quality > 0) {
      quality = obj.quality;
    }
    var base64 = canvas.toDataURL("image/jpeg", quality);
    callback(base64);
  };
}
const myJsTools = {
  phone,
  formatTime,
  getBirthdayFromIdCard,
  formatTimeDate,
  formatDuring,
  getDate,
  downAndSaveImg,
  request,
  msgReadFun,
  setImgZip,
  previewImg,
  getItem,
  getUrlParam
};
exports.myJsTools = myJsTools;
