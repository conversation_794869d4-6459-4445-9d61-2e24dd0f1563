!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.websdk=t():e.websdk=t()}(this,(function(){return function(){var e={83:function(e,t,r){e.exports=a;var o,n,i=r(101);function a(e,t,r,o,a,s){if(i.call(this,e,t,o,void 0,void 0,a,s),!n.isString(r))throw TypeError("keyType must be a string");this.keyType=r,this.resolvedKeyType=null,this.map=!0}((a.prototype=Object.create(i.prototype)).constructor=a).className="MapField",a.fromJSON=function(e,t){return new a(e,t.id,t.keyType,t.type,t.options,t.comment)},a.prototype.toJSON=function(e){var t=!!e&&Boolean(e.keepComments);return n.toObject(["keyType",this.keyType,"type",this.type,"id",this.id,"extend",this.extend,"options",this.options,"comment",t?this.comment:void 0])},a.prototype.resolve=function(){if(this.resolved)return this;if(void 0===o.mapKey[this.keyType])throw Error("invalid key type: "+this.keyType);return i.prototype.resolve.call(this)},a.d=function(e,t,r){return"function"==typeof r?r=n.decorateType(r).name:r&&"object"==typeof r&&(r=n.decorateEnum(r).name),function(o,i){n.decorateType(o.constructor).add(new a(i,e,t,r))}},a._configure=function(){o=r(452),n=r(629)}},87:function(e){"use strict";function t(){this._listeners={}}e.exports=t,t.prototype.on=function(e,t,r){return(this._listeners[e]||(this._listeners[e]=[])).push({fn:t,ctx:r||this}),this},t.prototype.off=function(e,t){if(void 0===e)this._listeners={};else if(void 0===t)this._listeners[e]=[];else for(var r=this._listeners[e],o=0;o<r.length;)r[o].fn===t?r.splice(o,1):++o;return this},t.prototype.emit=function(e){var t=this._listeners[e];if(t){for(var r=[],o=1;o<arguments.length;)r.push(arguments[o++]);for(o=0;o<t.length;)t[o].fn.apply(t[o++].ctx,r)}return this}},101:function(e,t,r){e.exports=u;var o,n,i,a,s=r(558);((u.prototype=Object.create(s.prototype)).constructor=u).className="Field";var c=/^required|optional|repeated$/;function u(e,t,r,o,a,u,l){if(i.isObject(o)?(l=a,u=o,o=a=void 0):i.isObject(a)&&(l=u,u=a,a=void 0),s.call(this,e,u),!i.isInteger(t)||t<0)throw TypeError("id must be a non-negative integer");if(!i.isString(r))throw TypeError("type must be a string");if(void 0!==o&&!c.test(o=o.toString().toLowerCase()))throw TypeError("rule must be a string rule");if(void 0!==a&&!i.isString(a))throw TypeError("extend must be a string");this.rule=o&&"optional"!==o?o:void 0,this.type=r,this.id=t,this.extend=a||void 0,this.required="required"===o,this.optional=!this.required,this.repeated="repeated"===o,this.map=!1,this.message=null,this.partOf=null,this.typeDefault=null,this.defaultValue=null,this.long=!!i.Long&&void 0!==n.long[r],this.bytes="bytes"===r,this.resolvedType=null,this.extensionField=null,this.declaringField=null,this._packed=null,this.comment=l}u.fromJSON=function(e,t){return new u(e,t.id,t.type,t.rule,t.extend,t.options,t.comment)},Object.defineProperty(u.prototype,"packed",{get:function(){return null===this._packed&&(this._packed=!1!==this.getOption("packed")),this._packed}}),u.prototype.setOption=function(e,t,r){return"packed"===e&&(this._packed=null),s.prototype.setOption.call(this,e,t,r)},u.prototype.toJSON=function(e){var t=!!e&&Boolean(e.keepComments);return i.toObject(["rule","optional"!==this.rule&&this.rule||void 0,"type",this.type,"id",this.id,"extend",this.extend,"options",this.options,"comment",t?this.comment:void 0])},u.prototype.resolve=function(){if(this.resolved)return this;if(void 0===(this.typeDefault=n.defaults[this.type])&&(this.resolvedType=(this.declaringField?this.declaringField.parent:this.parent).lookupTypeOrEnum(this.type),this.resolvedType instanceof a?this.typeDefault=null:this.typeDefault=this.resolvedType.values[Object.keys(this.resolvedType.values)[0]]),this.options&&null!=this.options.default&&(this.typeDefault=this.options.default,this.resolvedType instanceof o&&"string"==typeof this.typeDefault&&(this.typeDefault=this.resolvedType.values[this.typeDefault])),this.options&&(!0!==this.options.packed&&(void 0===this.options.packed||!this.resolvedType||this.resolvedType instanceof o)||delete this.options.packed,Object.keys(this.options).length||(this.options=void 0)),this.long)this.typeDefault=i.Long.fromNumber(this.typeDefault,"u"===this.type.charAt(0)),Object.freeze&&Object.freeze(this.typeDefault);else if(this.bytes&&"string"==typeof this.typeDefault){var e;i.utf8.write(this.typeDefault,e=i.newBuffer(i.utf8.length(this.typeDefault)),0),this.typeDefault=e}return this.map?this.defaultValue=i.emptyObject:this.repeated?this.defaultValue=i.emptyArray:this.defaultValue=this.typeDefault,this.parent instanceof a&&(this.parent.ctor.prototype[this.name]=this.defaultValue),s.prototype.resolve.call(this)},u.d=function(e,t,r,o){return"function"==typeof t?t=i.decorateType(t).name:t&&"object"==typeof t&&(t=i.decorateEnum(t).name),function(n,a){i.decorateType(n.constructor).add(new u(a,e,t,r,{default:o}))}},u._configure=function(){a=r(465),o=r(660),n=r(452),i=r(629)}},177:function(e,t,r){"use strict";e.exports=s;var o,n,i,a=r(694);function s(e,t){a.call(this,e,t),this.methods={},this._methodsArray=null}function c(e){return e._methodsArray=null,e}((s.prototype=Object.create(a.prototype)).constructor=s).className="Service",s.fromJSON=function(e,t){var r=new s(e,t.options);if(t.methods)for(var n=Object.keys(t.methods),i=0;i<n.length;++i)r.add(o.fromJSON(n[i],t.methods[n[i]]));return t.nested&&r.addJSON(t.nested),r.comment=t.comment,r},s.prototype.toJSON=function(e){var t=a.prototype.toJSON.call(this,e),r=!!e&&Boolean(e.keepComments);return n.toObject(["options",t&&t.options||void 0,"methods",a.arrayToJSON(this.methodsArray,e)||{},"nested",t&&t.nested||void 0,"comment",r?this.comment:void 0])},Object.defineProperty(s.prototype,"methodsArray",{get:function(){return this._methodsArray||(this._methodsArray=n.toArray(this.methods))}}),s.prototype.get=function(e){return this.methods[e]||a.prototype.get.call(this,e)},s.prototype.resolveAll=function(){for(var e=this.methodsArray,t=0;t<e.length;++t)e[t].resolve();return a.prototype.resolve.call(this)},s.prototype.add=function(e){if(this.get(e.name))throw Error("duplicate name '"+e.name+"' in "+this);return e instanceof o?(this.methods[e.name]=e,e.parent=this,c(this)):a.prototype.add.call(this,e)},s.prototype.remove=function(e){if(e instanceof o){if(this.methods[e.name]!==e)throw Error(e+" is not a member of "+this);return delete this.methods[e.name],e.parent=null,c(this)}return a.prototype.remove.call(this,e)},s.prototype.create=function(e,t,r){for(var o,a=new i.Service(e,t,r),s=0;s<this.methodsArray.length;++s){var c=n.lcFirst((o=this._methodsArray[s]).resolve().name).replace(/[^$\w_]/g,"");a[c]=n.codegen(["r","c"],n.isReserved(c)?c+"_":c)("return this.rpcCall(m,q,s,r,c)")({m:o,q:o.resolvedRequestType.ctor,s:o.resolvedResponseType.ctor})}return a},s._configure=function(){o=r(512),n=r(629),i=r(470)}},183:function(e,t,r){e.exports=n;var o=r(629);function n(e,t){this.lo=e>>>0,this.hi=t>>>0}var i=n.zero=new n(0,0);i.toNumber=function(){return 0},i.zzEncode=i.zzDecode=function(){return this},i.length=function(){return 1};var a=n.zeroHash="\0\0\0\0\0\0\0\0";n.fromNumber=function(e){if(0===e)return i;var t=e<0;t&&(e=-e);var r=e>>>0,o=(e-r)/4294967296>>>0;return t&&(o=~o>>>0,r=~r>>>0,++r>4294967295&&(r=0,++o>4294967295&&(o=0))),new n(r,o)},n.from=function(e){if("number"==typeof e)return n.fromNumber(e);if("string"==typeof e||e instanceof String){if(!o.Long)return n.fromNumber(parseInt(e,10));e=o.Long.fromString(e)}return e.low||e.high?new n(e.low>>>0,e.high>>>0):i},n.prototype.toNumber=function(e){if(!e&&this.hi>>>31){var t=1+~this.lo>>>0,r=~this.hi>>>0;return t||(r=r+1>>>0),-(t+4294967296*r)}return this.lo+4294967296*this.hi},n.prototype.toLong=function(e){return o.Long?new o.Long(0|this.lo,0|this.hi,Boolean(e)):{low:0|this.lo,high:0|this.hi,unsigned:Boolean(e)}};var s=String.prototype.charCodeAt;n.fromHash=function(e){return e===a?i:new n((s.call(e,0)|s.call(e,1)<<8|s.call(e,2)<<16|s.call(e,3)<<24)>>>0,(s.call(e,4)|s.call(e,5)<<8|s.call(e,6)<<16|s.call(e,7)<<24)>>>0)},n.prototype.toHash=function(){return String.fromCharCode(255&this.lo,this.lo>>>8&255,this.lo>>>16&255,this.lo>>>24,255&this.hi,this.hi>>>8&255,this.hi>>>16&255,this.hi>>>24)},n.prototype.zzEncode=function(){var e=this.hi>>31;return this.hi=((this.hi<<1|this.lo>>>31)^e)>>>0,this.lo=(this.lo<<1^e)>>>0,this},n.prototype.zzDecode=function(){var e=-(1&this.lo);return this.lo=((this.lo>>>1|this.hi<<31)^e)>>>0,this.hi=(this.hi>>>1^e)>>>0,this},n.prototype.length=function(){var e=this.lo,t=(this.lo>>>28|this.hi<<4)>>>0,r=this.hi>>>24;return 0===r?0===t?e<16384?e<128?1:2:e<2097152?3:4:t<16384?t<128?5:6:t<2097152?7:8:r<128?9:10}},220:function(e,t,r){e.exports=a;var o,n,i=r(558);function a(e,t,r,o){if(Array.isArray(t)||(r=t,t=void 0),i.call(this,e,r),void 0!==t&&!Array.isArray(t))throw TypeError("fieldNames must be an Array");this.oneof=t||[],this.fieldsArray=[],this.comment=o}function s(e){if(e.parent)for(var t=0;t<e.fieldsArray.length;++t)e.fieldsArray[t].parent||e.parent.add(e.fieldsArray[t])}((a.prototype=Object.create(i.prototype)).constructor=a).className="OneOf",a.fromJSON=function(e,t){return new a(e,t.oneof,t.options,t.comment)},a.prototype.toJSON=function(e){var t=!!e&&Boolean(e.keepComments);return n.toObject(["options",this.options,"oneof",this.oneof,"comment",t?this.comment:void 0])},a.prototype.add=function(e){if(!(e instanceof o))throw TypeError("field must be a Field");return e.parent&&e.parent!==this.parent&&e.parent.remove(e),this.oneof.push(e.name),this.fieldsArray.push(e),e.partOf=this,s(this),this},a.prototype.remove=function(e){if(!(e instanceof o))throw TypeError("field must be a Field");var t=this.fieldsArray.indexOf(e);if(t<0)throw Error(e+" is not a member of "+this);return this.fieldsArray.splice(t,1),(t=this.oneof.indexOf(e.name))>-1&&this.oneof.splice(t,1),e.partOf=null,this},a.prototype.onAdd=function(e){i.prototype.onAdd.call(this,e);for(var t=0;t<this.oneof.length;++t){var r=e.get(this.oneof[t]);r&&!r.partOf&&(r.partOf=this,this.fieldsArray.push(r))}s(this)},a.prototype.onRemove=function(e){for(var t,r=0;r<this.fieldsArray.length;++r)(t=this.fieldsArray[r]).parent&&t.parent.remove(t);i.prototype.onRemove.call(this,e)},a.d=function(){for(var e=new Array(arguments.length),t=0;t<arguments.length;)e[t]=arguments[t++];return function(t,r){n.decorateType(t.constructor).add(new a(r,e)),Object.defineProperty(t,r,{get:n.oneOfGetter(e),set:n.oneOfSetter(e)})}},a._configure=function(){o=r(101),n=r(629)}},229:function(e,t,r){var o,n;function i(e){return function(t){var r=t.Writer,i=t.types,a=t.util;return function(t,s){s=s||r.create();for(var c=e.fieldsArray.slice().sort(a.compareFieldsById),u=0;u<c.length;u++){var l=c[u],d=e._fieldsArray.indexOf(l),p=l.resolvedType instanceof o?"uint32":l.type,h=n.basic[p],f=t[l.name];if(l.resolvedType instanceof o&&"string"==typeof f&&(f=i[d].values[f]),l.map){if(null!=f&&t.hasOwnProperty(l.name))for(var m=Object.keys(f),g=0;g<m.length;++g)s.uint32((l.id<<3|2)>>>0).fork().uint32(8|n.mapKey[l.keyType])[l.keyType](m[g]),void 0===h?i[d].encode(f[m[g]],s.uint32(18).fork()).ldelim().ldelim():s.uint32(16|h)[p](f[m[g]]).ldelim()}else if(l.repeated){if(f&&f.length)if(l.packed&&void 0!==n.packed[p]){s.uint32((l.id<<3|2)>>>0).fork();for(var E=0;E<f.length;E++)s[p](f[E]);s.ldelim()}else for(var v=0;v<f.length;v++)void 0===h?l.resolvedType.group?i[d].encode(f[v],s.uint32((l.id<<3|3)>>>0)).uint32((l.id<<3|4)>>>0):i[d].encode(f[v],s.uint32((l.id<<3|2)>>>0).fork()).ldelim():s.uint32((l.id<<3|h)>>>0)[p](f[v])}else(!l.optional||null!=f&&t.hasOwnProperty(l.name))&&(l.optional||null!=f&&t.hasOwnProperty(l.name)||console.warn("注意啦!!!很大概率会报错 类型:",t.$type?t.$type.name:"不晓得","没有设置对应的属性:",l.name,"检查是不是proto文件属性设置为了required"),void 0===h?l.resolvedType.group?i[d].encode(f,s.uint32((l.id<<3|3)>>>0)).uint32((l.id<<3|4)>>>0):i[d].encode(f,s.uint32((l.id<<3|2)>>>0).fork()).ldelim():s.uint32((l.id<<3|h)>>>0)[p](f))}return s}}}e.exports=i,i._configure=function(){o=r(660),n=r(452)}},278:function(e,t,r){"use strict";var o;function n(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)this[t[r]]=e[t[r]]}e.exports=n,n.create=function(e){return this.$type.create(e)},n.encode=function(e,t){return arguments.length?1==arguments.length?this.$type.encode(arguments[0]):this.$type.encode(arguments[0],arguments[1]):this.$type.encode(this)},n.encodeDelimited=function(e,t){return this.$type.encodeDelimited(e,t)},n.decode=function(e){return this.$type.decode(e)},n.decodeDelimited=function(e){return this.$type.decodeDelimited(e)},n.verify=function(e){return this.$type.verify(e)},n.fromObject=function(e){return this.$type.fromObject(e)},n.toObject=function(e,t){return e=e||this,this.$type.toObject(e,t)},n.prototype.toJSON=function(){return this.$type.toObject(this,o.toJSONOptions)},n.set=function(e,t){n[e]=t},n.get=function(e){return n[e]},n._configure=function(){o=r(629)}},322:function(e,t,r){e.exports=u;var o,n=r(629),i=r(442);function a(e,t,r){this.fn=e,this.len=t,this.next=void 0,this.val=r}function s(){}function c(e){this.head=e.head,this.tail=e.tail,this.len=e.len,this.next=e.states}function u(){this.len=0,this.head=new a(s,0,0),this.tail=this.head,this.states=null}function l(e,t,r){t[r]=255&e}function d(e,t){this.len=e,this.next=void 0,this.val=t}function p(e,t,r){for(;e.hi;)t[r++]=127&e.lo|128,e.lo=(e.lo>>>7|e.hi<<25)>>>0,e.hi>>>=7;for(;e.lo>127;)t[r++]=127&e.lo|128,e.lo=e.lo>>>7;t[r++]=e.lo}function h(e,t,r){t[r]=255&e,t[r+1]=e>>>8&255,t[r+2]=e>>>16&255,t[r+3]=e>>>24}u.create=n.Buffer?function(){return(u.create=function(){return new(void 0)})()}:function(){return new u},u.alloc=function(e){return new n.Array(e)},n.Array!==Array&&(u.alloc=n.pool(u.alloc,n.Array.prototype.subarray)),u.prototype._push=function(e,t,r){return this.tail=this.tail.next=new a(e,t,r),this.len+=t,this},d.prototype=Object.create(a.prototype),d.prototype.fn=function(e,t,r){for(;e>127;)t[r++]=127&e|128,e>>>=7;t[r]=e},u.prototype.uint32=function(e){return this.len+=(this.tail=this.tail.next=new d((e>>>=0)<128?1:e<16384?2:e<2097152?3:e<268435456?4:5,e)).len,this},u.prototype.int32=function(e){return e<0?this._push(p,10,o.fromNumber(e)):this.uint32(e)},u.prototype.sint32=function(e){return this.uint32((e<<1^e>>31)>>>0)},u.prototype.uint64=function(e){var t=o.from(e);return this._push(p,t.length(),t)},u.prototype.int64=u.prototype.uint64,u.prototype.sint64=function(e){var t=o.from(e).zzEncode();return this._push(p,t.length(),t)},u.prototype.bool=function(e){return this._push(l,1,e?1:0)},u.prototype.fixed32=function(e){return this._push(h,4,e>>>0)},u.prototype.sfixed32=u.prototype.fixed32,u.prototype.fixed64=function(e){var t=o.from(e);return this._push(h,4,t.lo)._push(h,4,t.hi)},u.prototype.sfixed64=u.prototype.fixed64,u.prototype.float=function(e){return this._push(n.float.writeFloatLE,4,e)},u.prototype.double=function(e){return this._push(n.float.writeDoubleLE,8,e)};var f=n.Array.prototype.set?function(e,t,r){t.set(e,r)}:function(e,t,r){for(var o=0;o<e.length;++o)t[r+o]=e[o]};u.prototype.bytes=function(e){var t=e.length>>>0;if(!t)return this._push(l,1,0);if(n.isString(e)){var r=u.alloc(t=i.length(e));i.write(e,r,0),e=r}return this.uint32(t)._push(f,t,e)},u.prototype.string=function(e){var t=i.length(e);return t?this.uint32(t)._push(i.write,t,e):this._push(l,1,0)},u.prototype.fork=function(){return this.states=new c(this),this.head=this.tail=new a(s,0,0),this.len=0,this},u.prototype.reset=function(){return this.states?(this.head=this.states.head,this.tail=this.states.tail,this.len=this.states.len,this.states=this.states.next):(this.head=this.tail=new a(s,0,0),this.len=0),this},u.prototype.ldelim=function(){var e=this.head,t=this.tail,r=this.len;return this.reset().uint32(r),r&&(this.tail.next=e.next,this.tail=t,this.len+=r),this},u.prototype.finish=function(){for(var e=this.head.next,t=this.constructor.alloc(this.len),r=0;e;)e.fn(e.val,t,r),r+=e.len,e=e.next;return t},u._configure=function(){o=r(183),r(986),i=r(442)}},442:function(e){"use strict";var t=e.exports;t.length=function(e){for(var t=0,r=0,o=0;o<e.length;++o)(r=e.charCodeAt(o))<128?t+=1:r<2048?t+=2:55296==(64512&r)&&56320==(64512&e.charCodeAt(o+1))?(++o,t+=4):t+=3;return t},t.read=function(e,t,r){if(r-t<1)return"";for(var o,n=null,i=[],a=0;t<r;)(o=e[t++])<128?i[a++]=o:o>191&&o<224?i[a++]=(31&o)<<6|63&e[t++]:o>239&&o<365?(o=((7&o)<<18|(63&e[t++])<<12|(63&e[t++])<<6|63&e[t++])-65536,i[a++]=55296+(o>>10),i[a++]=56320+(1023&o)):i[a++]=(15&o)<<12|(63&e[t++])<<6|63&e[t++],a>8191&&((n||(n=[])).push(String.fromCharCode.apply(String,i)),a=0);return n?(a&&n.push(String.fromCharCode.apply(String,i.slice(0,a))),n.join("")):String.fromCharCode.apply(String,i.slice(0,a))},t.write=function(e,t,r){for(var o,n,i=r,a=0;a<e.length;++a)(o=e.charCodeAt(a))<128?t[r++]=o:o<2048?(t[r++]=o>>6|192,t[r++]=63&o|128):55296==(64512&o)&&56320==(64512&(n=e.charCodeAt(a+1)))?(o=65536+((1023&o)<<10)+(1023&n),++a,t[r++]=o>>18|240,t[r++]=o>>12&63|128,t[r++]=o>>6&63|128,t[r++]=63&o|128):(t[r++]=o>>12|224,t[r++]=o>>6&63|128,t[r++]=63&o|128);return r-i}},452:function(e,t,r){"use strict";var o=e.exports,n=r(629),i=["double","float","int32","uint32","sint32","fixed32","sfixed32","int64","uint64","sint64","fixed64","sfixed64","bool","string","bytes"];function a(e,t){var r=0,o={};for(t|=0;r<e.length;)o[i[r+t]]=e[r++];return o}o.basic=a([1,5,0,0,0,5,5,0,0,0,1,1,0,2,2]),o.defaults=a([0,0,0,0,0,0,0,0,0,0,0,0,!1,"",n.emptyArray,null]),o.long=a([0,0,0,1,1],7),o.mapKey=a([0,0,0,5,5,0,0,0,1,1,0,2],2),o.packed=a([1,5,0,0,0,5,5,0,0,0,1,1,0]),o._configure=function(){n=r(629)}},465:function(e,t,r){e.exports=v;var o,n,i,a,s,c,u,l,d,p,h,f,m,g,E=r(694);function v(e,t){E.call(this,e,t),this.fields={},this.oneofs=void 0,this.extensions=void 0,this.reserved=void 0,this.group=void 0,this._fieldsById=null,this._fieldsArray=null,this._oneofsArray=null,this._ctor=null}function y(e){return e._fieldsById=e._fieldsArray=e._oneofsArray=null,delete e.encode,delete e.decode,delete e.verify,e}((v.prototype=Object.create(E.prototype)).constructor=v).className="Type",Object.defineProperties(v.prototype,{fieldsById:{get:function(){if(this._fieldsById)return this._fieldsById;this._fieldsById={};for(var e=Object.keys(this.fields),t=0;t<e.length;++t){var r=this.fields[e[t]],o=r.id;if(this._fieldsById[o])throw Error("duplicate id "+o+" in "+this);this._fieldsById[o]=r}return this._fieldsById}},fieldsArray:{get:function(){return this._fieldsArray||(this._fieldsArray=u.toArray(this.fields))}},oneofsArray:{get:function(){return this._oneofsArray||(this._oneofsArray=u.toArray(this.oneofs))}},ctor:{get:function(){return this._ctor||(this.ctor=v.generateConstructor(this))},set:function(e){var t=e.prototype;t instanceof i||((e.prototype=new i).constructor=e,u.merge(e.prototype,t)),e.$type=e.prototype.$type=this,u.merge(e,i,!0),u.merge(e.prototype,i,!0),this._ctor=e;for(var r=0;r<this.fieldsArray.length;++r)this._fieldsArray[r].resolve();var o={};for(r=0;r<this.oneofsArray.length;++r){var n=this._oneofsArray[r].resolve().name,a=function(e){for(var t={},r=0;r<e.length;++r)t[e[r]]=0;return{setter:function(r){if(!(e.indexOf(r)<0)){t[r]=1;for(var o=0;o<e.length;++o)e[o]!==r&&delete this[e[o]]}},getter:function(){for(var e=Object.keys(this),r=e.length-1;r>-1;--r)if(1===t[e[r]]&&void 0!==this[e[r]]&&null!==this[e[r]])return e[r]}}}(this._oneofsArray[r].oneof);o[n]={get:a.getter,set:a.setter}}r&&Object.defineProperties(e.prototype,o)}}}),v.generateConstructor=function(e){return function(t){for(var r,o=0;o<e.fieldsArray.length;o++)(r=e._fieldsArray[o]).map?this[r.name]={}:r.repeated&&(this[r.name]=[]);if(t)for(var n=Object.keys(t),i=0;i<n.length;++i)null!=t[n[i]]&&(this[n[i]]=t[n[i]])}},v.fromJSON=function(e,t){var r=new v(e,t.options);r.extensions=t.extensions,r.reserved=t.reserved;for(var i=Object.keys(t.fields),s=0;s<i.length;++s)r.add((void 0!==t.fields[i[s]].keyType?g.fromJSON:n.fromJSON)(i[s],t.fields[i[s]]));if(t.oneofs)for(i=Object.keys(t.oneofs),s=0;s<i.length;++s)r.add(a.fromJSON(i[s],t.oneofs[i[s]]));if(t.nested)for(i=Object.keys(t.nested),s=0;s<i.length;++s){var c=t.nested[i[s]];r.add((void 0!==c.id?n.fromJSON:void 0!==c.fields?v.fromJSON:void 0!==c.values?o.fromJSON:void 0!==c.methods?h.fromJSON:E.fromJSON)(i[s],c))}return t.extensions&&t.extensions.length&&(r.extensions=t.extensions),t.reserved&&t.reserved.length&&(r.reserved=t.reserved),t.group&&(r.group=!0),t.comment&&(r.comment=t.comment),r},v.prototype.toJSON=function(e){var t=E.prototype.toJSON.call(this,e),r=!!e&&Boolean(e.keepComments);return{options:t&&t.options||void 0,oneofs:E.arrayToJSON(this.oneofsArray,e),fields:E.arrayToJSON(this.fieldsArray.filter((function(e){return!e.declaringField})),e)||{},extensions:this.extensions&&this.extensions.length?this.extensions:void 0,reserved:this.reserved&&this.reserved.length?this.reserved:void 0,group:this.group||void 0,nested:t&&t.nested||void 0,comment:r?this.comment:void 0}},v.prototype.resolveAll=function(){for(var e=this.fieldsArray,t=0;t<e.length;)e[t++].resolve();var r=this.oneofsArray;for(t=0;t<r.length;)r[t++].resolve();return E.prototype.resolveAll.call(this)},v.prototype.get=function(e){return this.fields[e]||this.oneofs&&this.oneofs[e]||this.nested&&this.nested[e]||null},v.prototype.add=function(e){if(this.get(e.name))throw Error("duplicate name '"+e.name+"' in "+this);if(e instanceof n&&void 0===e.extend){if(this._fieldsById&&this._fieldsById[e.id])throw Error("duplicate id "+e.id+" in "+this);if(this.isReservedId(e.id))throw Error("id "+e.id+" is reserved in "+this);if(this.isReservedName(e.name))throw Error("name '"+e.name+"' is reserved in "+this);return e.parent&&e.parent.remove(e),this.fields[e.name]=e,e.message=this,e.onAdd(this),y(this)}return e instanceof a?(this.oneofs||(this.oneofs={}),this.oneofs[e.name]=e,e.onAdd(this),y(this)):E.prototype.add.call(this,e)},v.prototype.remove=function(e){if(e instanceof n&&void 0===e.extend){if(!this.fields||this.fields[e.name]!==e)throw Error(e+" is not a member of "+this);return delete this.fields[e.name],e.parent=null,e.onRemove(this),y(this)}if(e instanceof a){if(!this.oneofs||this.oneofs[e.name]!==e)throw Error(e+" is not a member of "+this);return delete this.oneofs[e.name],e.parent=null,e.onRemove(this),y(this)}return E.prototype.remove.call(this,e)},v.prototype.isReservedId=function(e){return E.isReservedId(this.reserved,e)},v.prototype.isReservedName=function(e){return E.isReservedName(this.reserved,e)},v.prototype.create=function(e){return new this.ctor(e)},v.prototype.setup=function(){for(var e=this.fullName,t=[],r=0;r<this.fieldsArray.length;++r)t.push(this._fieldsArray[r].resolve().resolvedType);this.encode=d(this)({Writer:s,types:t,util:u}),this.decode=p(this)({Reader:c,types:t,util:u}),this.verify=l(this)({types:t,util:u}),this.fromObject=m.fromObject(this)({types:t,util:u}),this.toObject=m.toObject(this)({types:t,util:u});var o=f[e];if(o){var n=Object.create(this);n.fromObject=this.fromObject,this.fromObject=o.fromObject.bind(n),n.toObject=this.toObject,this.toObject=o.toObject.bind(n)}return this},v.prototype.encode=function(e,t){return this.setup().encode(e,t)},v.prototype.encodeDelimited=function(e,t){return this.encode(e,t&&t.len?t.fork():t).ldelim()},v.prototype.decode=function(e,t){return this.setup().decode(e,t)},v.prototype.decodeDelimited=function(e){return e instanceof c||(e=c.create(e)),this.decode(e,e.uint32())},v.prototype.verify=function(e){return this.setup().verify(e)},v.prototype.fromObject=function(e){return this.setup().fromObject(e)},v.prototype.toObject=function(e,t){return this.setup().toObject(e,t)},v.d=function(e){return function(t){u.decorateType(t,e)}},v._configure=function(){o=r(660),n=r(101),i=r(278),a=r(220),s=r(322),c=r(970),u=r(629),l=r(811),d=r(229),p=r(801),h=r(177),f=r(793),m=r(921),g=r(83)}},470:function(e,t,r){"use strict";e.exports=n;var o=r(629);function n(e,t,r){if("function"!=typeof e)throw TypeError("rpcImpl must be a function");o.EventEmitter.call(this),this.rpcImpl=e,this.requestDelimited=Boolean(t),this.responseDelimited=Boolean(r)}(n.prototype=Object.create(o.EventEmitter.prototype)).constructor=n,n.prototype.rpcCall=function e(t,r,n,i,a){if(!i)throw TypeError("request must be specified");var s=this;if(!a)return o.asPromise(e,s,t,r,n,i);if(s.rpcImpl)try{return s.rpcImpl(t,r[s.requestDelimited?"encodeDelimited":"encode"](i).finish(),(function(e,r){if(e)return s.emit("error",e,t),a(e);if(null!==r){if(!(r instanceof n))try{r=n[s.responseDelimited?"decodeDelimited":"decode"](r)}catch(e){return s.emit("error",e,t),a(e)}return s.emit("data",r,t),a(null,r)}s.end(!0)}))}catch(e){return s.emit("error",e,t),void setTimeout((function(){a(e)}),0)}else setTimeout((function(){a(Error("already ended"))}),0)},n.prototype.end=function(e){return this.rpcImpl&&(e||this.rpcImpl(null,null,null),this.rpcImpl=null,this.emit("end").off()),this}},496:function(e){e.exports=d;var t=/[\s{}=;:[\],'"()<>]/g,r=/(?:"([^"\\]*(?:\\.[^"\\]*)*)")/g,o=/(?:'([^'\\]*(?:\\.[^'\\]*)*)')/g,n=/^ *[*/]+ */,i=/^\s*\*?\/*/,a=/\n/g,s=/\s/,c=/\\(.?)/g,u={0:"\0",r:"\r",n:"\n",t:"\t"};function l(e){return e.replace(c,(function(e,t){switch(t){case"\\":case"":return t;default:return u[t]||""}}))}function d(e,c){e=e.toString();var u=0,d=e.length,p=1,h=null,f=null,m=0,g=!1,E=[],v=null;function y(e){return Error("illegal "+e+" (line "+p+")")}function _(t){return e.charAt(t)}function T(t,r){h=e.charAt(t++),m=p,g=!1;var o,s=t-(c?2:3);do{if(--s<0||"\n"===(o=e.charAt(s))){g=!0;break}}while(" "===o||"\t"===o);for(var u=e.substring(t,r).split(a),l=0;l<u.length;++l)u[l]=u[l].replace(c?i:n,"").trim();f=u.join("\n").trim()}function I(t){var r=O(t),o=e.substring(t,r);return/^\s*\/{1,2}/.test(o)}function O(e){for(var t=e;t<d&&"\n"!==_(t);)t++;return t}function R(){if(E.length>0)return E.shift();if(v)return function(){var t="'"===v?o:r;t.lastIndex=u-1;var n=t.exec(e);if(!n)throw y("string");return u=t.lastIndex,C(v),v=null,l(n[1])}();var n,i,a,h,f;do{if(u===d)return null;for(n=!1;s.test(a=_(u));)if("\n"===a&&++p,++u===d)return null;if("/"===_(u)){if(++u===d)throw y("comment");if("/"===_(u))if(c){if(h=u,f=!1,I(u)){f=!0;do{if((u=O(u))===d)break;u++}while(I(u))}else u=Math.min(d,O(u)+1);f&&T(h,u),p++,n=!0}else{for(f="/"===_(h=u+1);"\n"!==_(++u);)if(u===d)return null;++u,f&&T(h,u-1),++p,n=!0}else{if("*"!==(a=_(u)))return"/";h=u+1,f=c||"*"===_(h);do{if("\n"===a&&++p,++u===d)throw y("comment");i=a,a=_(u)}while("*"!==i||"/"!==a);++u,f&&T(h,u-2),n=!0}}}while(n);var m=u;if(t.lastIndex=0,!t.test(_(m++)))for(;m<d&&!t.test(_(m));)++m;var g=e.substring(u,u=m);return'"'!==g&&"'"!==g||(v=g),g}function C(e){E.push(e)}function N(){if(!E.length){var e=R();if(null===e)return null;C(e)}return E[0]}return Object.defineProperty({next:R,peek:N,push:C,skip:function(e,t){var r=N();if(r===e)return R(),!0;if(!t)throw y("token '"+r+"', '"+e+"' expected");return!1},cmnt:function(e){var t=null;return void 0===e?m===p-1&&(c||"*"===h||g)&&(t=f):(m<e&&N(),m!==e||g||!c&&"/"!==h||(t=f)),t}},"line",{get:function(){return p}})}d.unescape=l},512:function(e,t,r){"use strict";e.exports=i;var o,n=r(558);function i(e,t,r,i,a,s,c,u){if(o.isObject(a)?(c=a,a=s=void 0):o.isObject(s)&&(c=s,s=void 0),void 0!==t&&!o.isString(t))throw TypeError("type must be a string");if(!o.isString(r))throw TypeError("requestType must be a string");if(!o.isString(i))throw TypeError("responseType must be a string");n.call(this,e,c),this.type=t||"rpc",this.requestType=r,this.requestStream=!!a||void 0,this.responseType=i,this.responseStream=!!s||void 0,this.resolvedRequestType=null,this.resolvedResponseType=null,this.comment=u}((i.prototype=Object.create(n.prototype)).constructor=i).className="Method",i.fromJSON=function(e,t){return new i(e,t.type,t.requestType,t.responseType,t.requestStream,t.responseStream,t.options,t.comment)},i.prototype.toJSON=function(e){var t=!!e&&Boolean(e.keepComments);return o.toObject(["type","rpc"!==this.type&&this.type||void 0,"requestType",this.requestType,"requestStream",this.requestStream,"responseType",this.responseType,"responseStream",this.responseStream,"options",this.options,"comment",t?this.comment:void 0])},i.prototype.resolve=function(){return this.resolved?this:(this.resolvedRequestType=this.parent.lookupType(this.requestType),this.resolvedResponseType=this.parent.lookupType(this.responseType),n.prototype.resolve.call(this))},i._configure=function(){o=r(629)}},558:function(e,t,r){"use strict";var o,n;function i(e,t){if(!o.isString(e))throw TypeError("name must be a string");if(t&&!o.isObject(t))throw TypeError("options must be an object");this.options=t,this.name=e,this.parent=null,this.resolved=!1,this.comment=null,this.filename=null}e.exports=i,i.className="ReflectionObject",Object.defineProperties(i.prototype,{root:{get:function(){for(var e=this;null!==e.parent;)e=e.parent;return e}},fullName:{get:function(){for(var e=[this.name],t=this.parent;t;)e.unshift(t.name),t=t.parent;return e.join(".")}}}),i.prototype.toJSON=function(){throw Error()},i.prototype.onAdd=function(e){this.parent&&this.parent!==e&&this.parent.remove(this),this.parent=e,this.resolved=!1;var t=e.root;t instanceof n&&t._handleAdd(this)},i.prototype.onRemove=function(e){var t=e.root;t instanceof n&&t._handleRemove(this),this.parent=null,this.resolved=!1},i.prototype.resolve=function(){return this.resolved||this.root instanceof n&&(this.resolved=!0),this},i.prototype.getOption=function(e){if(this.options)return this.options[e]},i.prototype.setOption=function(e,t,r){return r&&this.options&&void 0!==this.options[e]||((this.options||(this.options={}))[e]=t),this},i.prototype.setOptions=function(e,t){if(e)for(var r=Object.keys(e),o=0;o<r.length;++o)this.setOption(r[o],e[r[o]],t);return this},i.prototype.toString=function(){var e=this.constructor.className,t=this.fullName;return t.length?e+" "+t:e},i._configure=function(e){n=r(773),o=r(629)}},570:function(e){e.exports=r;var t=null;try{t=new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([0,97,115,109,1,0,0,0,1,13,2,96,0,1,127,96,4,127,127,127,127,1,127,3,7,6,0,1,1,1,1,1,6,6,1,127,1,65,0,11,7,50,6,3,109,117,108,0,1,5,100,105,118,95,115,0,2,5,100,105,118,95,117,0,3,5,114,101,109,95,115,0,4,5,114,101,109,95,117,0,5,8,103,101,116,95,104,105,103,104,0,0,10,191,1,6,4,0,35,0,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,126,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,127,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,128,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,129,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,130,34,4,66,32,135,167,36,0,32,4,167,11])),{}).exports}catch(e){}function r(e,t,r){this.low=0|e,this.high=0|t,this.unsigned=!!r}function o(e){return!0===(e&&e.__isLong__)}r.prototype.__isLong__,Object.defineProperty(r.prototype,"__isLong__",{value:!0}),r.isLong=o;var n={},i={};function a(e,t){var r,o,a;return t?(a=0<=(e>>>=0)&&e<256)&&(o=i[e])?o:(r=c(e,(0|e)<0?-1:0,!0),a&&(i[e]=r),r):(a=-128<=(e|=0)&&e<128)&&(o=n[e])?o:(r=c(e,e<0?-1:0,!1),a&&(n[e]=r),r)}function s(e,t){if(isNaN(e))return t?E:g;if(t){if(e<0)return E;if(e>=h)return I}else{if(e<=-f)return O;if(e+1>=f)return T}return e<0?s(-e,t).neg():c(e%p|0,e/p|0,t)}function c(e,t,o){return new r(e,t,o)}r.fromInt=a,r.fromNumber=s,r.fromBits=c;var u=Math.pow;function l(e,t,r){if(0===e.length)throw Error("empty string");if("NaN"===e||"Infinity"===e||"+Infinity"===e||"-Infinity"===e)return g;if("number"==typeof t?(r=t,t=!1):t=!!t,(r=r||10)<2||36<r)throw RangeError("radix");var o;if((o=e.indexOf("-"))>0)throw Error("interior hyphen");if(0===o)return l(e.substring(1),t,r).neg();for(var n=s(u(r,8)),i=g,a=0;a<e.length;a+=8){var c=Math.min(8,e.length-a),d=parseInt(e.substring(a,a+c),r);if(c<8){var p=s(u(r,c));i=i.mul(p).add(s(d))}else i=(i=i.mul(n)).add(s(d))}return i.unsigned=t,i}function d(e,t){return"number"==typeof e?s(e,t):"string"==typeof e?l(e,t):c(e.low,e.high,"boolean"==typeof t?t:e.unsigned)}r.fromString=l,r.fromValue=d;var p=4294967296,h=p*p,f=h/2,m=a(1<<24),g=a(0);r.ZERO=g;var E=a(0,!0);r.UZERO=E;var v=a(1);r.ONE=v;var y=a(1,!0);r.UONE=y;var _=a(-1);r.NEG_ONE=_;var T=c(-1,2147483647,!1);r.MAX_VALUE=T;var I=c(-1,-1,!0);r.MAX_UNSIGNED_VALUE=I;var O=c(0,-2147483648,!1);r.MIN_VALUE=O;var R=r.prototype;R.toInt=function(){return this.unsigned?this.low>>>0:this.low},R.toNumber=function(){return this.unsigned?(this.high>>>0)*p+(this.low>>>0):this.high*p+(this.low>>>0)},R.toString=function(e){if((e=e||10)<2||36<e)throw RangeError("radix");if(this.isZero())return"0";if(this.isNegative()){if(this.eq(O)){var t=s(e),r=this.div(t),o=r.mul(t).sub(this);return r.toString(e)+o.toInt().toString(e)}return"-"+this.neg().toString(e)}for(var n=s(u(e,6),this.unsigned),i=this,a="";;){var c=i.div(n),l=(i.sub(c.mul(n)).toInt()>>>0).toString(e);if((i=c).isZero())return l+a;for(;l.length<6;)l="0"+l;a=""+l+a}},R.getHighBits=function(){return this.high},R.getHighBitsUnsigned=function(){return this.high>>>0},R.getLowBits=function(){return this.low},R.getLowBitsUnsigned=function(){return this.low>>>0},R.getNumBitsAbs=function(){if(this.isNegative())return this.eq(O)?64:this.neg().getNumBitsAbs();for(var e=0!=this.high?this.high:this.low,t=31;t>0&&!(e&1<<t);t--);return 0!=this.high?t+33:t+1},R.isZero=function(){return 0===this.high&&0===this.low},R.eqz=R.isZero,R.isNegative=function(){return!this.unsigned&&this.high<0},R.isPositive=function(){return this.unsigned||this.high>=0},R.isOdd=function(){return!(1&~this.low)},R.isEven=function(){return!(1&this.low)},R.equals=function(e){return o(e)||(e=d(e)),(this.unsigned===e.unsigned||this.high>>>31!=1||e.high>>>31!=1)&&this.high===e.high&&this.low===e.low},R.eq=R.equals,R.notEquals=function(e){return!this.eq(e)},R.neq=R.notEquals,R.ne=R.notEquals,R.lessThan=function(e){return this.comp(e)<0},R.lt=R.lessThan,R.lessThanOrEqual=function(e){return this.comp(e)<=0},R.lte=R.lessThanOrEqual,R.le=R.lessThanOrEqual,R.greaterThan=function(e){return this.comp(e)>0},R.gt=R.greaterThan,R.greaterThanOrEqual=function(e){return this.comp(e)>=0},R.gte=R.greaterThanOrEqual,R.ge=R.greaterThanOrEqual,R.compare=function(e){if(o(e)||(e=d(e)),this.eq(e))return 0;var t=this.isNegative(),r=e.isNegative();return t&&!r?-1:!t&&r?1:this.unsigned?e.high>>>0>this.high>>>0||e.high===this.high&&e.low>>>0>this.low>>>0?-1:1:this.sub(e).isNegative()?-1:1},R.comp=R.compare,R.negate=function(){return!this.unsigned&&this.eq(O)?O:this.not().add(v)},R.neg=R.negate,R.add=function(e){o(e)||(e=d(e));var t=this.high>>>16,r=65535&this.high,n=this.low>>>16,i=65535&this.low,a=e.high>>>16,s=65535&e.high,u=e.low>>>16,l=0,p=0,h=0,f=0;return h+=(f+=i+(65535&e.low))>>>16,p+=(h+=n+u)>>>16,l+=(p+=r+s)>>>16,l+=t+a,c((h&=65535)<<16|(f&=65535),(l&=65535)<<16|(p&=65535),this.unsigned)},R.subtract=function(e){return o(e)||(e=d(e)),this.add(e.neg())},R.sub=R.subtract,R.multiply=function(e){if(this.isZero())return g;if(o(e)||(e=d(e)),t)return c(t.mul(this.low,this.high,e.low,e.high),t.get_high(),this.unsigned);if(e.isZero())return g;if(this.eq(O))return e.isOdd()?O:g;if(e.eq(O))return this.isOdd()?O:g;if(this.isNegative())return e.isNegative()?this.neg().mul(e.neg()):this.neg().mul(e).neg();if(e.isNegative())return this.mul(e.neg()).neg();if(this.lt(m)&&e.lt(m))return s(this.toNumber()*e.toNumber(),this.unsigned);var r=this.high>>>16,n=65535&this.high,i=this.low>>>16,a=65535&this.low,u=e.high>>>16,l=65535&e.high,p=e.low>>>16,h=65535&e.low,f=0,E=0,v=0,y=0;return v+=(y+=a*h)>>>16,E+=(v+=i*h)>>>16,v&=65535,E+=(v+=a*p)>>>16,f+=(E+=n*h)>>>16,E&=65535,f+=(E+=i*p)>>>16,E&=65535,f+=(E+=a*l)>>>16,f+=r*h+n*p+i*l+a*u,c((v&=65535)<<16|(y&=65535),(f&=65535)<<16|(E&=65535),this.unsigned)},R.mul=R.multiply,R.divide=function(e){if(o(e)||(e=d(e)),e.isZero())throw Error("division by zero");var r,n,i;if(t)return this.unsigned||-2147483648!==this.high||-1!==e.low||-1!==e.high?c((this.unsigned?t.div_u:t.div_s)(this.low,this.high,e.low,e.high),t.get_high(),this.unsigned):this;if(this.isZero())return this.unsigned?E:g;if(this.unsigned){if(e.unsigned||(e=e.toUnsigned()),e.gt(this))return E;if(e.gt(this.shru(1)))return y;i=E}else{if(this.eq(O))return e.eq(v)||e.eq(_)?O:e.eq(O)?v:(r=this.shr(1).div(e).shl(1)).eq(g)?e.isNegative()?v:_:(n=this.sub(e.mul(r)),i=r.add(n.div(e)));if(e.eq(O))return this.unsigned?E:g;if(this.isNegative())return e.isNegative()?this.neg().div(e.neg()):this.neg().div(e).neg();if(e.isNegative())return this.div(e.neg()).neg();i=g}for(n=this;n.gte(e);){r=Math.max(1,Math.floor(n.toNumber()/e.toNumber()));for(var a=Math.ceil(Math.log(r)/Math.LN2),l=a<=48?1:u(2,a-48),p=s(r),h=p.mul(e);h.isNegative()||h.gt(n);)h=(p=s(r-=l,this.unsigned)).mul(e);p.isZero()&&(p=v),i=i.add(p),n=n.sub(h)}return i},R.div=R.divide,R.modulo=function(e){return o(e)||(e=d(e)),t?c((this.unsigned?t.rem_u:t.rem_s)(this.low,this.high,e.low,e.high),t.get_high(),this.unsigned):this.sub(this.div(e).mul(e))},R.mod=R.modulo,R.rem=R.modulo,R.not=function(){return c(~this.low,~this.high,this.unsigned)},R.and=function(e){return o(e)||(e=d(e)),c(this.low&e.low,this.high&e.high,this.unsigned)},R.or=function(e){return o(e)||(e=d(e)),c(this.low|e.low,this.high|e.high,this.unsigned)},R.xor=function(e){return o(e)||(e=d(e)),c(this.low^e.low,this.high^e.high,this.unsigned)},R.shiftLeft=function(e){return o(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?c(this.low<<e,this.high<<e|this.low>>>32-e,this.unsigned):c(0,this.low<<e-32,this.unsigned)},R.shl=R.shiftLeft,R.shiftRight=function(e){return o(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?c(this.low>>>e|this.high<<32-e,this.high>>e,this.unsigned):c(this.high>>e-32,this.high>=0?0:-1,this.unsigned)},R.shr=R.shiftRight,R.shiftRightUnsigned=function(e){if(o(e)&&(e=e.toInt()),0==(e&=63))return this;var t=this.high;return e<32?c(this.low>>>e|t<<32-e,t>>>e,this.unsigned):c(32===e?t:t>>>e-32,0,this.unsigned)},R.shru=R.shiftRightUnsigned,R.shr_u=R.shiftRightUnsigned,R.toSigned=function(){return this.unsigned?c(this.low,this.high,!1):this},R.toUnsigned=function(){return this.unsigned?this:c(this.low,this.high,!0)},R.toBytes=function(e){return e?this.toBytesLE():this.toBytesBE()},R.toBytesLE=function(){var e=this.high,t=this.low;return[255&t,t>>>8&255,t>>>16&255,t>>>24,255&e,e>>>8&255,e>>>16&255,e>>>24]},R.toBytesBE=function(){var e=this.high,t=this.low;return[e>>>24,e>>>16&255,e>>>8&255,255&e,t>>>24,t>>>16&255,t>>>8&255,255&t]},r.fromBytes=function(e,t,o){return o?r.fromBytesLE(e,t):r.fromBytesBE(e,t)},r.fromBytesLE=function(e,t){return new r(e[0]|e[1]<<8|e[2]<<16|e[3]<<24,e[4]|e[5]<<8|e[6]<<16|e[7]<<24,t)},r.fromBytesBE=function(e,t){return new r(e[4]<<24|e[5]<<16|e[6]<<8|e[7],e[0]<<24|e[1]<<16|e[2]<<8|e[3],t)}},627:function(e){e.exports=function(e,t,r){var o=r||8192,n=o>>>1,i=null,a=o;return function(r){if(r<1||r>n)return e(r);a+r>o&&(i=e(o),a=0);var s=t.call(i,a,a+=r);return 7&a&&(a=1+(7|a)),s}}},629:function(e,t,r){var o=e.exports,n=r(896);o.LongBits=r(183),o.Long=r(879),o.pool=r(627),o.float=r(899),o.asPromise=r(880),o.EventEmitter=r(87),o.path=r(814),o.base64=r(986),o.utf8=r(442),o.compareFieldsById=function(e,t){return e.id-t.id},o.toArray=function(e){if(e){for(var t=Object.keys(e),r=new Array(t.length),o=0;o<t.length;)r[o]=e[t[o++]];return r}return[]},o.toObject=function(e){for(var t={},r=0;r<e.length;){var o=e[r++],n=e[r++];void 0!==n&&(t[o]=n)}return t},o.isString=function(e){return"string"==typeof e||e instanceof String},o.isReserved=function(e){return/^(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$/.test(e)},o.isObject=function(e){return e&&"object"==typeof e},o.Array="undefined"!=typeof Uint8Array?Uint8Array:Array,o.oneOfGetter=function(e){for(var t={},r=0;r<e.length;++r)t[e[r]]=1;return function(){for(var e=Object.keys(this),r=e.length-1;r>-1;--r)if(1===t[e[r]]&&void 0!==this[e[r]]&&null!==this[e[r]])return e[r]}},o.oneOfSetter=function(e){return function(t){for(var r=0;r<e.length;++r)e[r]!==t&&delete this[e[r]]}},o.merge=function(e,t,r){for(var o=Object.keys(t),n=0;n<o.length;++n)void 0!==e[o[n]]&&r||(e[o[n]]=t[o[n]]);return e},o.decorateType=function(e,t){if(e.$type)return t&&e.$type.name!==t&&(o.decorateRoot.remove(e.$type),e.$type.name=t,o.decorateRoot.add(e.$type)),e.$type;Type||(Type=r(465));var n=new Type(t||e.name);return o.decorateRoot.add(n),n.ctor=e,Object.defineProperty(e,"$type",{value:n,enumerable:!1}),Object.defineProperty(e.prototype,"$type",{value:n,enumerable:!1}),n},o.emptyArray=Object.freeze?Object.freeze([]):[],o.emptyObject=Object.freeze?Object.freeze({}):{},o.longToHash=function(e){return e?o.LongBits.from(e).toHash():o.LongBits.zeroHash},o.copy=function(e){if("object"!=typeof e)return e;var t={};for(var r in e)t[r]=e[r];return t},o.deepCopy=function e(t){if("object"!=typeof t)return t;var r={};for(var o in t)r[o]=e(t[o]);return r},o.ProtocolError=function(e){function t(e,r){if(!(this instanceof t))return new t(e,r);Object.defineProperty(this,"message",{get:function(){return e}}),Error.captureStackTrace?Error.captureStackTrace(this,t):Object.defineProperty(this,"stack",{value:(new Error).stack||""}),r&&merge(this,r)}return(t.prototype=Object.create(Error.prototype)).constructor=t,Object.defineProperty(t.prototype,"name",{get:function(){return e}}),t.prototype.toString=function(){return this.name+": "+this.message},t},o.toJSONOptions={longs:String,enums:String,bytes:String,json:!0},o.Buffer=null,o.newBuffer=function(e){return"number"==typeof e?new o.Array(e):"undefined"==typeof Uint8Array?e:new Uint8Array(e)},o.stringToBytes=function(e){var t,r,o=[];t=e.length;for(var n=0;n<t;n++)(r=e.charCodeAt(n))>=65536&&r<=1114111?(o.push(r>>18&7|240),o.push(r>>12&63|128),o.push(r>>6&63|128),o.push(63&r|128)):r>=2048&&r<=65535?(o.push(r>>12&15|224),o.push(r>>6&63|128),o.push(63&r|128)):r>=128&&r<=2047?(o.push(r>>6&31|192),o.push(63&r|128)):o.push(255&r);return o},o.byteToString=function(e){if("string"==typeof e)return e;for(var t="",r=e,o=0;o<r.length;o++){var n=r[o].toString(2),i=n.match(/^1+?(?=0)/);if(i&&8==n.length){for(var a=i[0].length,s=r[o].toString(2).slice(7-a),c=1;c<a;c++)s+=r[c+o].toString(2).slice(2);t+=String.fromCharCode(parseInt(s,2)),o+=a-1}else t+=String.fromCharCode(r[o])}return t},o.isInteger=Number.isInteger||function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e},Object.defineProperty(o,"decorateRoot",{get:function(){return n.decorated||(n.decorated=new(r(773)))}})},660:function(e,t,r){e.exports=i;var o=r(558);((i.prototype=Object.create(o.prototype)).constructor=i).className="Enum";var n=r(694);function i(e,t,r,n,i){if(o.call(this,e,r),t&&"object"!=typeof t)throw TypeError("values must be an object");if(this.valuesById={},this.values=Object.create(this.valuesById),this.comment=n,this.comments=i||{},this.reserved=void 0,t)for(var a=Object.keys(t),s=0;s<a.length;++s)"number"==typeof t[a[s]]&&(this.valuesById[this.values[a[s]]=t[a[s]]]=a[s])}i.fromJSON=function(e,t){var r=new i(e,t.values,t.options,t.comment,t.comments);return r.reserved=t.reserved,r},i.prototype.toJSON=function(e){var t=!!e&&Boolean(e.keepComments);return util.toObject(["options",this.options,"values",this.values,"reserved",this.reserved&&this.reserved.length?this.reserved:void 0,"comment",t?this.comment:void 0,"comments",t?this.comments:void 0])},i.prototype.add=function(e,t,r){if(!util.isString(e))throw TypeError("name must be a string");if(!util.isInteger(t))throw TypeError("id must be an integer");if(void 0!==this.values[e])throw Error("duplicate name '"+e+"' in "+this);if(this.isReservedId(t))throw Error("id "+t+" is reserved in "+this);if(this.isReservedName(e))throw Error("name '"+e+"' is reserved in "+this);if(void 0!==this.valuesById[t]){if(!this.options||!this.options.allow_alias)throw Error("duplicate id "+t+" in "+this);this.values[e]=t}else this.valuesById[this.values[e]=t]=e;return this.comments[e]=r||null,this},i.prototype.remove=function(e){if(!util.isString(e))throw TypeError("name must be a string");var t=this.values[e];if(null==t)throw Error("name '"+e+"' does not exist in "+this);return delete this.valuesById[t],delete this.values[e],delete this.comments[e],this},i.prototype.isReservedId=function(e){return n.isReservedId(this.reserved,e)},i.prototype.isReservedName=function(e){return n.isReservedName(this.reserved,e)}},694:function(e,t,r){e.exports=l;var o,n,i,a,s,c=r(558);function u(e,t){if(e&&e.length){for(var r={},o=0;o<e.length;++o)r[e[o].name]=e[o].toJSON(t);return r}}function l(e,t){c.call(this,e,t),this.nested=void 0,this._nestedArray=null}function d(e){return e._nestedArray=null,e}((l.prototype=Object.create(c.prototype)).constructor=l).className="Namespace",l.fromJSON=function(e,t){return new l(e,t.options).addJSON(t.nested)},l.arrayToJSON=u,l.isReservedId=function(e,t){if(e)for(var r=0;r<e.length;++r)if("string"!=typeof e[r]&&e[r][0]<=t&&e[r][1]>=t)return!0;return!1},l.isReservedName=function(e,t){if(e)for(var r=0;r<e.length;++r)if(e[r]===t)return!0;return!1},Object.defineProperty(l.prototype,"nestedArray",{get:function(){return this._nestedArray||(this._nestedArray=i.toArray(this.nested))}}),l.prototype.toJSON=function(e){return i.toObject(["options",this.options,"nested",u(this.nestedArray,e)])},l.prototype.addJSON=function(e){if(e)for(var t,r=Object.keys(e),i=0;i<r.length;++i)t=e[r[i]],this.add((void 0!==t.fields?a.fromJSON:void 0!==t.values?o.fromJSON:void 0!==t.methods?s.fromJSON:void 0!==t.id?n.fromJSON:l.fromJSON)(r[i],t));return this},l.prototype.get=function(e){return this.nested&&this.nested[e]||null},l.prototype.getEnum=function(e){if(this.nested&&this.nested[e]instanceof o)return this.nested[e].values;throw Error("no such enum: "+e)},l.prototype.add=function(e){if(!(e instanceof n&&void 0!==e.extend||e instanceof a||e instanceof o||e instanceof s||e instanceof l))throw TypeError("object must be a valid nested object");if(this.nested){var t=this.get(e.name);if(t){if(!(t instanceof l&&e instanceof l)||t instanceof a||t instanceof s)throw Error("duplicate name '"+e.name+"' in "+this);for(var r=t.nestedArray,i=0;i<r.length;++i)e.add(r[i]);this.remove(t),this.nested||(this.nested={}),e.setOptions(t.options,!0)}}else this.nested={};return this.nested[e.name]=e,e.onAdd(this),d(this)},l.prototype.remove=function(e){if(!(e instanceof c))throw TypeError("object must be a ReflectionObject");if(e.parent!==this)throw Error(e+" is not a member of "+this);return delete this.nested[e.name],Object.keys(this.nested).length||(this.nested=void 0),e.onRemove(this),d(this)},l.prototype.define=function(e,t){if(i.isString(e))e=e.split(".");else if(!Array.isArray(e))throw TypeError("illegal path");if(e&&e.length&&""===e[0])throw Error("path must be relative");for(var r=this;e.length>0;){var o=e.shift();if(r.nested&&r.nested[o]){if(!((r=r.nested[o])instanceof l))throw Error("path conflicts with non-namespace objects")}else r.add(r=new l(o))}return t&&r.addJSON(t),r},l.prototype.resolveAll=function(){for(var e=this.nestedArray,t=0;t<e.length;)e[t]instanceof l?e[t++].resolveAll():e[t++].resolve();return this.resolve()},l.prototype.lookup=function(e,t,r){if("boolean"==typeof t?(r=t,t=void 0):t&&!Array.isArray(t)&&(t=[t]),i.isString(e)&&e.length){if("."===e)return this.root;e=e.split(".")}else if(!e.length)return this;if(""===e[0])return this.root.lookup(e.slice(1),t);var o=this.get(e[0]);if(o){if(1===e.length){if(!t||t.indexOf(o.constructor)>-1)return o}else if(o instanceof l&&(o=o.lookup(e.slice(1),t,!0)))return o}else for(var n=0;n<this.nestedArray.length;++n)if(this._nestedArray[n]instanceof l&&(o=this._nestedArray[n].lookup(e,t,!0)))return o;return null===this.parent||r?null:this.parent.lookup(e,t)},l.prototype.lookupType=function(e){var t=this.lookup(e,[a]);if(!t)throw Error("no such type: "+e);return t},l.prototype.lookupEnum=function(e){var t=this.lookup(e,[o]);if(!t)throw Error("no such Enum '"+e+"' in "+this);return t},l.prototype.lookupTypeOrEnum=function(e){var t=this.lookup(e,[a,o]);if(!t)throw Error("no such Type or Enum '"+e+"' in "+this);return t},l.prototype.lookupService=function(e){var t=this.lookup(e,[s]);if(!t)throw Error("no such Service '"+e+"' in "+this);return t},l._configure=function(){o=r(660),n=r(101),i=r(629),a=r(465),s=r(177)}},773:function(e,t,r){e.exports=d;var o=r(694);((d.prototype=Object.create(o.prototype)).constructor=d).className="Root";var n,i,a,s=r(101),c=r(660),u=r(220),l=r(629);function d(e){o.call(this,"",e),this.deferred=[],this.files=[],this.names=[]}function p(){}d.fromJSON=function(e,t){return e="string"==typeof e?JSON.parse(e):e,t||(t=new d),e.options&&t.setOptions(e.options),t.addJSON(e.nested)},d.prototype.resolvePath=l.path.resolve,d.prototype.parseFromPbString=function e(t,r,o){"function"==typeof r&&(o=r,r=void 0);var n=this;if(!o)return l.asPromise(e,n,t,r);var s=null;if("string"==typeof t)s=JSON.parse(t);else{if("object"!=typeof t)return void console.log("pb格式转化失败");s=t}function c(e,t){if(o){var r=o;o=null,r(e,t)}}function u(e,t){try{if(l.isString(t)&&"{"===t.charAt(0)&&(t=JSON.parse(t)),l.isString(t)){i.filename=e;var o,a=i(t,n,r),s=0;if(a.imports)for(;s<a.imports.length;++s)d(o=a.imports[s]);if(a.weakImports){for(s=0;s<a.weakImports.length;++s)o=a.weakImports[s];d(o)}}else n.setOptions(t.options).addJSON(t.nested)}catch(e){c(e)}c(null,n)}function d(e){n.names.indexOf(e)>-1||(n.names.push(e),e in a&&u(e,a[e]))}u(s.name,s.pbJsonStr)},d.prototype.load=function e(t,r,o){"function"==typeof r&&(o=r,r=void 0);var n=this;if(!o)return l.asPromise(e,n,t,r);var s=o===p;function c(e,t){if(o){var r=o;if(o=null,s)throw e;r(e,t)}}function u(e,t){try{if(l.isString(t)&&"{"===t.charAt(0)&&(t=JSON.parse(t)),l.isString(t)){i.filename=e;var o,a=i(t,n,r),u=0;if(a.imports)for(;u<a.imports.length;++u)(o=n.resolvePath(e,a.imports[u]))&&d(o);if(a.weakImports)for(u=0;u<a.weakImports.length;++u)(o=n.resolvePath(e,a.weakImports[u]))&&d(o,!0)}else n.setOptions(t.options).addJSON(t.nested)}catch(e){c(e)}s||h||c(null,n)}function d(e,t){var r=e.lastIndexOf("google/protobuf/");if(r>-1){var i=e.substring(r);i in a&&(e=i)}if(!(n.files.indexOf(e)>-1))if(n.files.push(e),e in a)s?u(e,a[e]):(++h,setTimeout((function(){--h,u(e,a[e])})));else if(s){var d;try{d=l.fs.readFileSync(e).toString("utf8")}catch(e){return void(t||c(e))}u(e,d)}else++h,l.fetch(e,(function(r,i){--h,o&&(r?t?h||c(null,n):c(r):u(e,i))}))}var h=0;l.isString(t)&&(t=[t]);for(var f,m=0;m<t.length;++m)(f=n.resolvePath("",t[m]))&&d(f);if(s)return n;h||c(null,n)},d.prototype.loadSync=function(e,t){if(!l.isNode)throw Error("not supported");return this.load(e,t,p)},d.prototype.resolveAll=function(){if(this.deferred.length)throw Error("unresolvable extensions: "+this.deferred.map((function(e){return"'extend "+e.extend+"' in "+e.parent.fullName})).join(", "));return o.prototype.resolveAll.call(this)};var h=/^[A-Z]/;function f(e,t){var r=t.parent.lookup(t.extend);if(r){var o=new s(t.fullName,t.id,t.type,t.rule,void 0,t.options);return o.declaringField=t,t.extensionField=o,r.add(o),!0}return!1}d.prototype._handleAdd=function(e){if(e instanceof s)void 0===e.extend||e.extensionField||f(0,e)||this.deferred.push(e);else if(e instanceof c)h.test(e.name)&&(e.parent[e.name]=e.values);else if(!(e instanceof u)){if(e instanceof n)for(var t=0;t<this.deferred.length;)f(0,this.deferred[t])?this.deferred.splice(t,1):++t;for(var r=0;r<e.nestedArray.length;++r)this._handleAdd(e._nestedArray[r]);h.test(e.name)&&(e.parent[e.name]=e)}},d.prototype._handleRemove=function(e){if(e instanceof s){if(void 0!==e.extend)if(e.extensionField)e.extensionField.parent.remove(e.extensionField),e.extensionField=null;else{var t=this.deferred.indexOf(e);t>-1&&this.deferred.splice(t,1)}}else if(e instanceof c)h.test(e.name)&&delete e.parent[e.name];else if(e instanceof o){for(var r=0;r<e.nestedArray.length;++r)this._handleRemove(e._nestedArray[r]);h.test(e.name)&&delete e.parent[e.name]}},d._configure=function(){n=r(465),i=r(922),a=r(940),s=r(101),c=r(660),u=r(220),l=r(629)}},793:function(e,t,r){var o,n=t;n[".google.protobuf.Any"]={fromObject:function(e){if(e&&e["@type"]){var t=this.lookup(e["@type"]);if(t){var r="."===e["@type"].charAt(0)?e["@type"].substr(1):e["@type"];return this.create({type_url:"/"+r,value:t.encode(t.fromObject(e)).finish()})}}return this.fromObject(e)},toObject:function(e,t){if(t&&t.json&&e.type_url&&e.value){var r=e.type_url.substring(e.type_url.lastIndexOf("/")+1),n=this.lookup(r);n&&(e=n.decode(e.value))}if(!(e instanceof this.ctor)&&e instanceof o){var i=e.$type.toObject(e,t);return i["@type"]=e.$type.fullName,i}return this.toObject(e,t)}},n._configure=function(){o=r(278)}},801:function(e,t,r){var o,n,i;function a(e){return"missing required '"+e.name+"'"}function s(e){return function(t){var r=t.Reader,s=t.types,c=t.util;return function(t,u){t instanceof r||(t=r.create(t));for(var l,d=void 0===u?t.len:t.pos+u,p=new this.ctor;t.pos<d;){var h=t.uint32();if(e.group&&4==(7&h))break;for(var f=h>>>3,m=0,g=!1;m<e.fieldsArray.length;++m){var E=e._fieldsArray[m].resolve(),v=E.name,y=E.resolvedType instanceof o?"int32":E.type;if(f==E.id){if(g=!0,E.map)t.skip().pos++,p[v]===c.emptyObject&&(p[v]={}),l=t[E.keyType](),t.pos++,null!=n.long[E.keyType]?null==n.basic[y]?p[v]["object"==typeof l?c.longToHash(l):l]=s[m].decode(t,t.uint32()):p[v]["object"==typeof l?c.longToHash(l):l]=t[y]():null==n.basic[y]?p[v]=s[m].decode(t,t.uint32()):p[v]=t[y]();else if(E.repeated)if(p[v]&&p[v].length||(p[v]=[]),null!=n.packed[y]&&2==(7&h))for(var _=t.uint32()+t.pos;t.pos<_;)p[v].push(t[y]());else null==n.basic[y]?E.resolvedType.group?p[v].push(s[m].decode(t)):p[v].push(s[m].decode(t,t.uint32())):p[v].push(t[y]());else null==n.basic[y]?E.resolvedType.group?p[v]=s[m].decode(t):p[v]=s[m].decode(t,t.uint32()):p[v]=t[y]();break}}g||(console.log("t",h),t.skipType(7&h))}for(m=0;m<e._fieldsArray.length;++m){var T=e._fieldsArray[m];if(T.required&&!p.hasOwnProperty(T.name))throw i.ProtocolError(a(T),{instance:p})}return p}}}e.exports=s,s._configure=function(){o=r(660),n=r(452),i=r(629)}},811:function(e,t,r){var o,n;function i(e,t){return e.name+": "+t+(e.repeated&&"array"!==t?"[]":e.map&&"object"!==t?"{k:"+e.keyType+"}":"")+" expected"}function a(e,t,r,a){var s=a.types;if(e.resolvedType)if(e.resolvedType instanceof o){if(Object.keys(e.resolvedType.values).indexOf(r)<0)return i(e,"enum value")}else{var c=s[t].verify(r);if(c)return e.name+"."+c}else switch(e.type){case"int32":case"uint32":case"sint32":case"fixed32":case"sfixed32":if(!n.isInteger(r))return i(e,"integer");break;case"int64":case"uint64":case"sint64":case"fixed64":case"sfixed64":if(!(n.isInteger(r)||r&&n.isInteger(r.low)&&n.isInteger(r.high)))return i(e,"integer|Long");break;case"float":case"double":if("number"!=typeof r)return i(e,"number");break;case"bool":if("boolean"!=typeof r)return i(e,"boolean");break;case"string":if(!n.isString(r))return i(e,"string");break;case"bytes":if(!(r&&"number"==typeof r.length||n.isString(r)))return i(e,"buffer")}}function s(e,t){switch(e.keyType){case"int32":case"uint32":case"sint32":case"fixed32":case"sfixed32":if(!n.key32Re.test(t))return i(e,"integer key");break;case"int64":case"uint64":case"sint64":case"fixed64":case"sfixed64":if(!n.key64Re.test(t))return i(e,"integer|Long key");break;case"bool":if(!n.key2Re.test(t))return i(e,"boolean key")}}function c(e){return function(t){return function(r){var o;if("object"!=typeof r||null===r)return"object expected";var c,u={};e.oneofsArray.length&&(c={});for(var l=0;l<e.fieldsArray.length;++l){var d,p=e._fieldsArray[l].resolve(),h=r[p.name];if(!p.optional||null!=h&&r.hasOwnProperty(p.name))if(p.map){if(!n.isObject(h))return i(p,"object");var f=Object.keys(h);for(d=0;d<f.length;++d){if(o=s(p,f[d]))return o;if(o=a(p,l,h[f[d]],t))return o}}else if(p.repeated){if(!Array.isArray(h))return i(p,"array");for(d=0;d<h.length;++d)if(o=a(p,l,h[d],t))return o}else{if(p.partOf){var m=p.partOf.name;if(1===u[p.partOf.name]&&1===c[m])return p.partOf.name+": multiple values";c[m]=1}if(o=a(p,l,h,t))return o}}}}}e.exports=c,c._configure=function(){o=r(660),n=r(629)}},814:function(e){var t=e.exports,r=t.isAbsolute=function(e){return/^(?:\/|\w+:)/.test(e)},o=t.normalize=function(e){var t=(e=e.replace(/\\/g,"/").replace(/\/{2,}/g,"/")).split("/"),o=r(e),n="";o&&(n=t.shift()+"/");for(var i=0;i<t.length;)".."===t[i]?i>0&&".."!==t[i-1]?t.splice(--i,2):o?t.splice(i,1):++i:"."===t[i]?t.splice(i,1):++i;return n+t.join("/")};t.resolve=function(e,t,n){return n||(t=o(t)),r(t)?t:(n||(e=o(e)),(e=e.replace(/(?:\/|^)[^/]+$/,"")).length?o(e+"/"+t):t)}},879:function(e){e.exports=r;var t=null;try{t=new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([0,97,115,109,1,0,0,0,1,13,2,96,0,1,127,96,4,127,127,127,127,1,127,3,7,6,0,1,1,1,1,1,6,6,1,127,1,65,0,11,7,50,6,3,109,117,108,0,1,5,100,105,118,95,115,0,2,5,100,105,118,95,117,0,3,5,114,101,109,95,115,0,4,5,114,101,109,95,117,0,5,8,103,101,116,95,104,105,103,104,0,0,10,191,1,6,4,0,35,0,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,126,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,127,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,128,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,129,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,130,34,4,66,32,135,167,36,0,32,4,167,11])),{}).exports}catch(e){}function r(e,t,r){this.low=0|e,this.high=0|t,this.unsigned=!!r}function o(e){return!0===(e&&e.__isLong__)}r.prototype.__isLong__,Object.defineProperty(r.prototype,"__isLong__",{value:!0}),r.isLong=o;var n={},i={};function a(e,t){var r,o,a;return t?(a=0<=(e>>>=0)&&e<256)&&(o=i[e])?o:(r=c(e,(0|e)<0?-1:0,!0),a&&(i[e]=r),r):(a=-128<=(e|=0)&&e<128)&&(o=n[e])?o:(r=c(e,e<0?-1:0,!1),a&&(n[e]=r),r)}function s(e,t){if(isNaN(e))return t?E:g;if(t){if(e<0)return E;if(e>=h)return I}else{if(e<=-f)return O;if(e+1>=f)return T}return e<0?s(-e,t).neg():c(e%p|0,e/p|0,t)}function c(e,t,o){return new r(e,t,o)}r.fromInt=a,r.fromNumber=s,r.fromBits=c;var u=Math.pow;function l(e,t,r){if(0===e.length)throw Error("empty string");if("NaN"===e||"Infinity"===e||"+Infinity"===e||"-Infinity"===e)return g;if("number"==typeof t?(r=t,t=!1):t=!!t,(r=r||10)<2||36<r)throw RangeError("radix");var o;if((o=e.indexOf("-"))>0)throw Error("interior hyphen");if(0===o)return l(e.substring(1),t,r).neg();for(var n=s(u(r,8)),i=g,a=0;a<e.length;a+=8){var c=Math.min(8,e.length-a),d=parseInt(e.substring(a,a+c),r);if(c<8){var p=s(u(r,c));i=i.mul(p).add(s(d))}else i=(i=i.mul(n)).add(s(d))}return i.unsigned=t,i}function d(e,t){return"number"==typeof e?s(e,t):"string"==typeof e?l(e,t):c(e.low,e.high,"boolean"==typeof t?t:e.unsigned)}r.fromString=l,r.fromValue=d;var p=4294967296,h=p*p,f=h/2,m=a(1<<24),g=a(0);r.ZERO=g;var E=a(0,!0);r.UZERO=E;var v=a(1);r.ONE=v;var y=a(1,!0);r.UONE=y;var _=a(-1);r.NEG_ONE=_;var T=c(-1,2147483647,!1);r.MAX_VALUE=T;var I=c(-1,-1,!0);r.MAX_UNSIGNED_VALUE=I;var O=c(0,-2147483648,!1);r.MIN_VALUE=O;var R=r.prototype;R.toInt=function(){return this.unsigned?this.low>>>0:this.low},R.toNumber=function(){return this.unsigned?(this.high>>>0)*p+(this.low>>>0):this.high*p+(this.low>>>0)},R.toString=function(e){if((e=e||10)<2||36<e)throw RangeError("radix");if(this.isZero())return"0";if(this.isNegative()){if(this.eq(O)){var t=s(e),r=this.div(t),o=r.mul(t).sub(this);return r.toString(e)+o.toInt().toString(e)}return"-"+this.neg().toString(e)}for(var n=s(u(e,6),this.unsigned),i=this,a="";;){var c=i.div(n),l=(i.sub(c.mul(n)).toInt()>>>0).toString(e);if((i=c).isZero())return l+a;for(;l.length<6;)l="0"+l;a=""+l+a}},R.getHighBits=function(){return this.high},R.getHighBitsUnsigned=function(){return this.high>>>0},R.getLowBits=function(){return this.low},R.getLowBitsUnsigned=function(){return this.low>>>0},R.getNumBitsAbs=function(){if(this.isNegative())return this.eq(O)?64:this.neg().getNumBitsAbs();for(var e=0!=this.high?this.high:this.low,t=31;t>0&&!(e&1<<t);t--);return 0!=this.high?t+33:t+1},R.isZero=function(){return 0===this.high&&0===this.low},R.eqz=R.isZero,R.isNegative=function(){return!this.unsigned&&this.high<0},R.isPositive=function(){return this.unsigned||this.high>=0},R.isOdd=function(){return!(1&~this.low)},R.isEven=function(){return!(1&this.low)},R.equals=function(e){return o(e)||(e=d(e)),(this.unsigned===e.unsigned||this.high>>>31!=1||e.high>>>31!=1)&&this.high===e.high&&this.low===e.low},R.eq=R.equals,R.notEquals=function(e){return!this.eq(e)},R.neq=R.notEquals,R.ne=R.notEquals,R.lessThan=function(e){return this.comp(e)<0},R.lt=R.lessThan,R.lessThanOrEqual=function(e){return this.comp(e)<=0},R.lte=R.lessThanOrEqual,R.le=R.lessThanOrEqual,R.greaterThan=function(e){return this.comp(e)>0},R.gt=R.greaterThan,R.greaterThanOrEqual=function(e){return this.comp(e)>=0},R.gte=R.greaterThanOrEqual,R.ge=R.greaterThanOrEqual,R.compare=function(e){if(o(e)||(e=d(e)),this.eq(e))return 0;var t=this.isNegative(),r=e.isNegative();return t&&!r?-1:!t&&r?1:this.unsigned?e.high>>>0>this.high>>>0||e.high===this.high&&e.low>>>0>this.low>>>0?-1:1:this.sub(e).isNegative()?-1:1},R.comp=R.compare,R.negate=function(){return!this.unsigned&&this.eq(O)?O:this.not().add(v)},R.neg=R.negate,R.add=function(e){o(e)||(e=d(e));var t=this.high>>>16,r=65535&this.high,n=this.low>>>16,i=65535&this.low,a=e.high>>>16,s=65535&e.high,u=e.low>>>16,l=0,p=0,h=0,f=0;return h+=(f+=i+(65535&e.low))>>>16,p+=(h+=n+u)>>>16,l+=(p+=r+s)>>>16,l+=t+a,c((h&=65535)<<16|(f&=65535),(l&=65535)<<16|(p&=65535),this.unsigned)},R.subtract=function(e){return o(e)||(e=d(e)),this.add(e.neg())},R.sub=R.subtract,R.multiply=function(e){if(this.isZero())return g;if(o(e)||(e=d(e)),t)return c(t.mul(this.low,this.high,e.low,e.high),t.get_high(),this.unsigned);if(e.isZero())return g;if(this.eq(O))return e.isOdd()?O:g;if(e.eq(O))return this.isOdd()?O:g;if(this.isNegative())return e.isNegative()?this.neg().mul(e.neg()):this.neg().mul(e).neg();if(e.isNegative())return this.mul(e.neg()).neg();if(this.lt(m)&&e.lt(m))return s(this.toNumber()*e.toNumber(),this.unsigned);var r=this.high>>>16,n=65535&this.high,i=this.low>>>16,a=65535&this.low,u=e.high>>>16,l=65535&e.high,p=e.low>>>16,h=65535&e.low,f=0,E=0,v=0,y=0;return v+=(y+=a*h)>>>16,E+=(v+=i*h)>>>16,v&=65535,E+=(v+=a*p)>>>16,f+=(E+=n*h)>>>16,E&=65535,f+=(E+=i*p)>>>16,E&=65535,f+=(E+=a*l)>>>16,f+=r*h+n*p+i*l+a*u,c((v&=65535)<<16|(y&=65535),(f&=65535)<<16|(E&=65535),this.unsigned)},R.mul=R.multiply,R.divide=function(e){if(o(e)||(e=d(e)),e.isZero())throw Error("division by zero");var r,n,i;if(t)return this.unsigned||-2147483648!==this.high||-1!==e.low||-1!==e.high?c((this.unsigned?t.div_u:t.div_s)(this.low,this.high,e.low,e.high),t.get_high(),this.unsigned):this;if(this.isZero())return this.unsigned?E:g;if(this.unsigned){if(e.unsigned||(e=e.toUnsigned()),e.gt(this))return E;if(e.gt(this.shru(1)))return y;i=E}else{if(this.eq(O))return e.eq(v)||e.eq(_)?O:e.eq(O)?v:(r=this.shr(1).div(e).shl(1)).eq(g)?e.isNegative()?v:_:(n=this.sub(e.mul(r)),i=r.add(n.div(e)));if(e.eq(O))return this.unsigned?E:g;if(this.isNegative())return e.isNegative()?this.neg().div(e.neg()):this.neg().div(e).neg();if(e.isNegative())return this.div(e.neg()).neg();i=g}for(n=this;n.gte(e);){r=Math.max(1,Math.floor(n.toNumber()/e.toNumber()));for(var a=Math.ceil(Math.log(r)/Math.LN2),l=a<=48?1:u(2,a-48),p=s(r),h=p.mul(e);h.isNegative()||h.gt(n);)h=(p=s(r-=l,this.unsigned)).mul(e);p.isZero()&&(p=v),i=i.add(p),n=n.sub(h)}return i},R.div=R.divide,R.modulo=function(e){return o(e)||(e=d(e)),t?c((this.unsigned?t.rem_u:t.rem_s)(this.low,this.high,e.low,e.high),t.get_high(),this.unsigned):this.sub(this.div(e).mul(e))},R.mod=R.modulo,R.rem=R.modulo,R.not=function(){return c(~this.low,~this.high,this.unsigned)},R.and=function(e){return o(e)||(e=d(e)),c(this.low&e.low,this.high&e.high,this.unsigned)},R.or=function(e){return o(e)||(e=d(e)),c(this.low|e.low,this.high|e.high,this.unsigned)},R.xor=function(e){return o(e)||(e=d(e)),c(this.low^e.low,this.high^e.high,this.unsigned)},R.shiftLeft=function(e){return o(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?c(this.low<<e,this.high<<e|this.low>>>32-e,this.unsigned):c(0,this.low<<e-32,this.unsigned)},R.shl=R.shiftLeft,R.shiftRight=function(e){return o(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?c(this.low>>>e|this.high<<32-e,this.high>>e,this.unsigned):c(this.high>>e-32,this.high>=0?0:-1,this.unsigned)},R.shr=R.shiftRight,R.shiftRightUnsigned=function(e){if(o(e)&&(e=e.toInt()),0==(e&=63))return this;var t=this.high;return e<32?c(this.low>>>e|t<<32-e,t>>>e,this.unsigned):c(32===e?t:t>>>e-32,0,this.unsigned)},R.shru=R.shiftRightUnsigned,R.shr_u=R.shiftRightUnsigned,R.toSigned=function(){return this.unsigned?c(this.low,this.high,!1):this},R.toUnsigned=function(){return this.unsigned?this:c(this.low,this.high,!0)},R.toBytes=function(e){return e?this.toBytesLE():this.toBytesBE()},R.toBytesLE=function(){var e=this.high,t=this.low;return[255&t,t>>>8&255,t>>>16&255,t>>>24,255&e,e>>>8&255,e>>>16&255,e>>>24]},R.toBytesBE=function(){var e=this.high,t=this.low;return[e>>>24,e>>>16&255,e>>>8&255,255&e,t>>>24,t>>>16&255,t>>>8&255,255&t]},r.fromBytes=function(e,t,o){return o?r.fromBytesLE(e,t):r.fromBytesBE(e,t)},r.fromBytesLE=function(e,t){return new r(e[0]|e[1]<<8|e[2]<<16|e[3]<<24,e[4]|e[5]<<8|e[6]<<16|e[7]<<24,t)},r.fromBytesBE=function(e,t){return new r(e[4]<<24|e[5]<<16|e[6]<<8|e[7],e[0]<<24|e[1]<<16|e[2]<<8|e[3],t)}},880:function(e){"use strict";e.exports=function(e,t){for(var r=new Array(arguments.length-1),o=0,n=2,i=!0;n<arguments.length;)r[o++]=arguments[n++];return new Promise((function(n,a){r[o]=function(e){if(i)if(i=!1,e)a(e);else{for(var t=new Array(arguments.length-1),r=0;r<t.length;)t[r++]=arguments[r];n.apply(null,t)}};try{e.apply(t||null,r)}catch(e){i&&(i=!1,a(e))}}))}},887:function(e,t,r){var o,n,i;!function(a){"use strict";if(null!=t&&"number"!=typeof t.nodeType)e.exports=a();else if(null!=r.amdO)n=[],void 0===(i="function"==typeof(o=a)?o.apply(t,n):o)||(e.exports=i);else{var s=a(),c="undefined"!=typeof self?self:$.global;"function"!=typeof c.btoa&&(c.btoa=s.btoa),"function"!=typeof c.atob&&(c.atob=s.atob)}}((function(){"use strict";var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function t(e){this.message=e}return t.prototype=new Error,t.prototype.name="InvalidCharacterError",{btoa:function(r){for(var o,n,i=String(r),a=0,s=e,c="";i.charAt(0|a)||(s="=",a%1);c+=s.charAt(63&o>>8-a%1*8)){if((n=i.charCodeAt(a+=3/4))>255)throw new t("'btoa' failed: The string to be encoded contains characters outside of the Latin1 range.");o=o<<8|n}return c},atob:function(r){var o=String(r).replace(/[=]+$/,"");if(o.length%4==1)throw new t("'atob' failed: The string to be decoded is not correctly encoded.");for(var n,i,a=0,s=0,c="";i=o.charAt(s++);~i&&(n=a%4?64*n+i:i,a++%4)?c+=String.fromCharCode(255&n>>(-2*a&6)):0)i=e.indexOf(i);return c}}}))},896:function(e){e.exports={}},899:function(e){function t(e){return"undefined"!=typeof Float32Array?function(){var t=new Float32Array([-0]),r=new Uint8Array(t.buffer),o=128===r[3];function n(e,o,n){t[0]=e,o[n]=r[0],o[n+1]=r[1],o[n+2]=r[2],o[n+3]=r[3]}function i(e,o,n){t[0]=e,o[n]=r[3],o[n+1]=r[2],o[n+2]=r[1],o[n+3]=r[0]}function a(e,o){return r[0]=e[o],r[1]=e[o+1],r[2]=e[o+2],r[3]=e[o+3],t[0]}function s(e,o){return r[3]=e[o],r[2]=e[o+1],r[1]=e[o+2],r[0]=e[o+3],t[0]}e.writeFloatLE=o?n:i,e.writeFloatBE=o?i:n,e.readFloatLE=o?a:s,e.readFloatBE=o?s:a}():function(){function t(e,t,r,o){var n=t<0?1:0;if(n&&(t=-t),0===t)e(1/t>0?0:2147483648,r,o);else if(isNaN(t))e(2143289344,r,o);else if(t>34028234663852886e22)e((n<<31|2139095040)>>>0,r,o);else if(t<11754943508222875e-54)e((n<<31|Math.round(t/1401298464324817e-60))>>>0,r,o);else{var i=Math.floor(Math.log(t)/Math.LN2);e((n<<31|i+127<<23|8388607&Math.round(t*Math.pow(2,-i)*8388608))>>>0,r,o)}}function a(e,t,r){var o=e(t,r),n=2*(o>>31)+1,i=o>>>23&255,a=8388607&o;return 255===i?a?NaN:n*(1/0):0===i?1401298464324817e-60*n*a:n*Math.pow(2,i-150)*(a+8388608)}e.writeFloatLE=t.bind(null,r),e.writeFloatBE=t.bind(null,o),e.readFloatLE=a.bind(null,n),e.readFloatBE=a.bind(null,i)}(),"undefined"!=typeof Float64Array?function(){var t=new Float64Array([-0]),r=new Uint8Array(t.buffer),o=128===r[7];function n(e,o,n){t[0]=e,o[n]=r[0],o[n+1]=r[1],o[n+2]=r[2],o[n+3]=r[3],o[n+4]=r[4],o[n+5]=r[5],o[n+6]=r[6],o[n+7]=r[7]}function i(e,o,n){t[0]=e,o[n]=r[7],o[n+1]=r[6],o[n+2]=r[5],o[n+3]=r[4],o[n+4]=r[3],o[n+5]=r[2],o[n+6]=r[1],o[n+7]=r[0]}function a(e,o){return r[0]=e[o],r[1]=e[o+1],r[2]=e[o+2],r[3]=e[o+3],r[4]=e[o+4],r[5]=e[o+5],r[6]=e[o+6],r[7]=e[o+7],t[0]}function s(e,o){return r[7]=e[o],r[6]=e[o+1],r[5]=e[o+2],r[4]=e[o+3],r[3]=e[o+4],r[2]=e[o+5],r[1]=e[o+6],r[0]=e[o+7],t[0]}e.writeDoubleLE=o?n:i,e.writeDoubleBE=o?i:n,e.readDoubleLE=o?a:s,e.readDoubleBE=o?s:a}():function(){function t(e,t,r,o,n,i){var a=o<0?1:0;if(a&&(o=-o),0===o)e(0,n,i+t),e(1/o>0?0:2147483648,n,i+r);else if(isNaN(o))e(0,n,i+t),e(2146959360,n,i+r);else if(o>17976931348623157e292)e(0,n,i+t),e((a<<31|2146435072)>>>0,n,i+r);else{var s;if(o<22250738585072014e-324)e((s=o/5e-324)>>>0,n,i+t),e((a<<31|s/4294967296)>>>0,n,i+r);else{var c=Math.floor(Math.log(o)/Math.LN2);1024===c&&(c=1023),e(4503599627370496*(s=o*Math.pow(2,-c))>>>0,n,i+t),e((a<<31|c+1023<<20|1048576*s&1048575)>>>0,n,i+r)}}}function a(e,t,r,o,n){var i=e(o,n+t),a=e(o,n+r),s=2*(a>>31)+1,c=a>>>20&2047,u=4294967296*(1048575&a)+i;return 2047===c?u?NaN:s*(1/0):0===c?5e-324*s*u:s*Math.pow(2,c-1075)*(u+4503599627370496)}e.writeDoubleLE=t.bind(null,r,0,4),e.writeDoubleBE=t.bind(null,o,4,0),e.readDoubleLE=a.bind(null,n,0,4),e.readDoubleBE=a.bind(null,i,4,0)}(),e}function r(e,t,r){t[r]=255&e,t[r+1]=e>>>8&255,t[r+2]=e>>>16&255,t[r+3]=e>>>24}function o(e,t,r){t[r]=e>>>24,t[r+1]=e>>>16&255,t[r+2]=e>>>8&255,t[r+3]=255&e}function n(e,t){return(e[t]|e[t+1]<<8|e[t+2]<<16|e[t+3]<<24)>>>0}function i(e,t){return(e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3])>>>0}e.exports=t(t)},921:function(e,t,r){var o,n,i=e.exports;function a(e,t,r,i){var a=i.m,s=i.d,c=i.types,u=i.ksi,l=void 0!==u;if(e.resolvedType)if(e.resolvedType instanceof o){for(var d=l?s[r][u]:s[r],p=e.resolvedType.values,h=Object.keys(p),f=0;f<h.length;f++)if(!(e.repeated&&p[h[f]]===e.typeDefault||h[f]!=d&&p[h[f]]!=d)){l?a[r][u]=p[h[f]]:a[r]=p[h[f]];break}}else{if("object"!=typeof(l?s[r][u]:s[r]))throw TypeError(e.fullName+": object expected");l?a[r][u]=c[t].fromObject(s[r][u]):a[r]=c[t].fromObject(s[r])}else{var m=!1;switch(e.type){case"double":case"float":l?a[r][u]=Number(s[r][u]):a[r]=Number(s[r]);break;case"uint32":case"fixed32":l?a[r][u]=s[r][u]>>>0:a[r]=s[r]>>>0;break;case"int32":case"sint32":case"sfixed32":l?a[r][u]=0|s[r][u]:a[r]=0|s[r];break;case"uint64":m=!0;case"int64":case"sint64":case"fixed64":case"sfixed64":n.Long?l?a[r][u]=n.Long.fromValue(s[r][u]).unsigned=m:a[r]=n.Long.fromValue(s[r]).unsigned=m:"string"==typeof(l?s[r][u]:s[r])?l?a[r][u]=parseInt(s[r][u],10):a[r]=parseInt(s[r],10):"number"==typeof(l?s[r][u]:s[r])?l?a[r][u]=s[r][u]:a[r]=s[r]:"object"==typeof(l?s[r][u]:s[r])&&(l?a[r][u]=new n.LongBits(s[r][u].low>>>0,s[r][u].high>>>0).toNumber(m):a[r]=new n.LongBits(s[r].low>>>0,s[r].high>>>0).toNumber(m));break;case"bytes":"string"==typeof(l?s[r][u]:s[r])?l?n.base64.decode(s[r][u],a[r][u]=n.newBuffer(n.base64.length(s[r][u])),0):n.base64.decode(s[r],a[r]=n.newBuffer(n.base64.length(s[r])),0):(l?s[r][u]:s[r]).length&&(l?a[r][u]=s[r][u]:a[r]=s[r]);break;case"string":l?a[r][u]=String(s[r][u]):a[r]=String(s[r]);break;case"bool":l?a[r][u]=Boolean(s[r][u]):a[r]=Boolean(s[r])}}}function s(e,t,r,i){var a=i.m,s=i.d,c=i.types,u=i.ksi,l=i.o,d=void 0!==u;if(e.resolvedType)e.resolvedType instanceof o?d?s[r][u]=l.enums===String?c[t].values[a[r][u]]:a[r][u]:s[r]=l.enums===String?c[t].values[a[r]]:a[r]:d?s[r][u]=c[t].toObject(a[r][u],l):s[r]=c[t].toObject(a[r],l);else{var p=!1;switch(e.type){case"double":case"float":d?s[r][u]=l.json&&!isFinite(a[r][u])?String(a[r][u]):a[r][u]:s[r]=l.json&&!isFinite(a[r])?String(a[r]):a[r];break;case"uint64":p=!0;case"int64":case"sint64":case"fixed64":case"sfixed64":"number"==typeof a[r][u]?d?s[r][u]=l.longs===String?String(a[r][u]):a[r][u]:s[r]=l.longs===String?String(a[r]):a[r]:d?s[r][u]=l.longs===String?n.Long.prototype.toString.call(a[r][u]):l.longs===Number?new n.LongBits(a[r][u].low>>>0,a[r][u].high>>>0).toNumber(p):a[r][u]:s[r]=l.longs===String?n.Long.prototype.toString.call(a[r]):l.longs===Number?new n.LongBits(a[r].low>>>0,a[r].high>>>0).toNumber(p):a[r];break;case"bytes":d?s[r][u]=l.bytes===String?n.base64.encode(a[r][u],0,a[r][u].length):l.bytes===Array?Array.prototype.slice.call(a[r][u]):a[r][u]:s[r]=l.bytes===String?n.base64.encode(a[r],0,a[r].length):l.bytes===Array?Array.prototype.slice.call(a[r]):a[r];break;default:d?s[r][u]=a[r][u]:s[r]=a[r]}}}i._configure=function(){o=r(660),n=r(629)},i.fromObject=function(e){var t=e.fieldsArray;return function(e){return function(r){if(r instanceof this.ctor)return r;if(!t.length)return new this.ctor;for(var i=new this.ctor,s=0;s<t.length;++s){var c,u=t[s].resolve(),l=u.name;if(u.map){if(r[l]){if("object"!=typeof r[l])throw TypeError(u.fullName+": object expected");i[l]={}}var d=Object.keys(r[l]);for(c=0;c<d.length;++c)a(u,s,l,n.merge(n.copy(e),{m:i,d:r,ksi:d[c]}))}else if(u.repeated){if(r[l]){if(!Array.isArray(r[l]))throw TypeError(u.fullName+": array expected");for(i[l]=[],c=0;c<r[l].length;++c)a(u,s,l,n.merge(n.copy(e),{m:i,d:r,ksi:c}))}}else(u.resolvedType instanceof o||null!=r[l])&&a(u,s,l,n.merge(n.copy(e),{m:i,d:r}))}return i}}},i.toObject=function(e){var t=e.fieldsArray.slice().sort(n.compareFieldsById);return function(r){return t.length?function(i,a){a=a||{};for(var c,u,l={},d=[],p=[],h=[],f=0;f<t.length;++f)t[f].partOf||(t[f].resolve().repeated?d:t[f].map?p:h).push(t[f]);if(d.length&&(a.arrays||a.defaults))for(f=0;f<d.length;++f)l[d[f].name]=[];if(p.length&&(a.objects||a.defaults))for(f=0;f<p.length;++f)l[p[f].name]={};if(h.length&&a.defaults)for(f=0;f<h.length;++f)if(u=(c=h[f]).name,c.resolvedType instanceof o)l[u]=a.enums=String?c.resolvedType.valuesById[c.typeDefault]:c.typeDefault;else if(c.long)if(n.Long){var m=new n.Long(c.typeDefault.low,c.typeDefault.high,c.typeDefault.unsigned);l[u]=a.longs===String?m.toString():a.longs===Number?m.toNumber():m}else l[u]=a.longs===String?c.typeDefault.toString():c.typeDefault.toNumber();else c.bytes?l[u]=a.bytes===String?String.fromCharCode.apply(String,c.typeDefault):Array.prototype.slice.call(c.typeDefault).join("*..*").split("*..*"):l[u]=c.typeDefault;var g=!1;for(f=0;f<t.length;++f){u=(c=t[f]).name;var E,v,y=e._fieldsArray.indexOf(c);if(c.map){if(g||(g=!0),i[u]&&(E=Object.keys(i[u]).length))for(l[u]={},v=0;v<E.length;++v)s(c,y,u,n.merge(n.copy(r),{m:i,d:l,ksi:E[v],o:a}))}else if(c.repeated){if(i[u]&&i[u].length)for(l[u]=[],v=0;v<i[u].length;++v)s(c,y,u,n.merge(n.copy(r),{m:i,d:l,ksi:v,o:a}))}else null!=i[u]&&i.hasOwnProperty(u)&&s(c,y,u,n.merge(n.copy(r),{m:i,d:l,o:a})),c.partOf&&a.oneofs&&(l[c.partOf.name]=u)}return l}:function(){return{}}}}},922:function(e,t,r){"use strict";var o,n,i,a,s,c,u,l,d,p,h;e.exports=R,R.filename=null,R.defaults={keepCase:!1};var f=/^[1-9][0-9]*$/,m=/^-?[1-9][0-9]*$/,g=/^0[x][0-9a-fA-F]+$/,E=/^-?0[x][0-9a-fA-F]+$/,v=/^0[0-7]+$/,y=/^-?0[0-7]+$/,_=/^(?![eE])[0-9]*(?:\.[0-9]*)?(?:[eE][+-]?[0-9]+)?$/,T=/^[a-zA-Z_][a-zA-Z_0-9]*$/,I=/^(?:\.?[a-zA-Z_][a-zA-Z_0-9]*)+$/,O=/^(?:\.[a-zA-Z][a-zA-Z_0-9]*)+$/;function R(e,t,r){t instanceof n||(r=t,t=new n),r||(r=R.defaults);var C,N,S,A,b,M=o(e,r.alternateCommentMode||!1),U=M.next,w=M.push,L=M.peek,P=M.skip,D=M.cmnt,k=!0,x=!1,G=t,B=r.keepCase?function(e){return e}:h.camelCase;function H(e,t,r){var o=R.filename;return r||(R.filename=null),Error("illegal "+(t||"token")+" '"+e+"' ("+(o?o+", ":"")+"line "+M.line+")")}function j(){var e,t=[];do{if('"'!==(e=U())&&"'"!==e)throw H(e);t.push(U()),P(e),e=L()}while('"'===e||"'"===e);return t.join("")}function F(e){var t=U();switch(t){case"'":case'"':return w(t),j();case"true":case"TRUE":return!0;case"false":case"FALSE":return!1}try{return function(e){var t=1;switch("-"===e.charAt(0)&&(t=-1,e=e.substring(1)),e){case"inf":case"INF":case"Inf":return t*(1/0);case"nan":case"NAN":case"Nan":case"NaN":return NaN;case"0":return 0}if(f.test(e))return t*parseInt(e,10);if(g.test(e))return t*parseInt(e,16);if(v.test(e))return t*parseInt(e,8);if(_.test(e))return t*parseFloat(e);throw H(e,"number",!0)}(t)}catch(r){if(e&&I.test(t))return t;throw H(t,"value")}}function W(e,t){var r,o;do{!t||'"'!==(r=L())&&"'"!==r?e.push([o=K(U()),P("to",!0)?K(U()):o]):e.push(j())}while(P(",",!0));P(";")}function K(e,t){switch(e){case"max":case"MAX":case"Max":return *********;case"0":return 0}if(!t&&"-"===e.charAt(0))throw H(e,"id");if(m.test(e))return parseInt(e,10);if(E.test(e))return parseInt(e,16);if(y.test(e))return parseInt(e,8);throw H(e,"id")}function q(){if(void 0!==C)throw H("package");if(C=U(),!I.test(C))throw H(C,"name");G=G.define(C),P(";")}function V(){var e,t=L();switch(t){case"weak":e=S||(S=[]),U();break;case"public":U();default:e=N||(N=[])}t=j(),P(";"),e.push(t)}function z(){if(P("="),A=j(),!(x="proto3"===A)&&"proto2"!==A)throw H(A,"syntax");P(";")}function J(e,t){switch(t){case"option":return Q(e,t),P(";"),!0;case"message":return function(e,t){if(!T.test(t=U()))throw H(t,"type name");var r=new i(t);Y(r,(function(e){if(!J(r,e))switch(e){case"map":!function(e){P("<");var t=U();if(void 0===p.mapKey[t])throw H(t,"type");P(",");var r=U();if(!I.test(r))throw H(r,"type");P(">");var o=U();if(!T.test(o))throw H(o,"name");P("=");var n=new s(B(o),K(U()),t,r);Y(n,(function(e){if("option"!==e)throw H(e);Q(n,e),P(";")}),(function(){ee(n)})),e.add(n)}(r);break;case"required":case"optional":case"repeated":X(r,e);break;case"oneof":!function(e,t){if(!T.test(t=U()))throw H(t,"name");var r=new c(B(t));Y(r,(function(e){"option"===e?(Q(r,e),P(";")):(w(e),X(r,"optional"))})),e.add(r)}(r,e);break;case"extensions":W(r.extensions||(r.extensions=[]));break;case"reserved":W(r.reserved||(r.reserved=[]),!0);break;default:if(!x||!I.test(e))throw H(e);w(e),X(r,"optional")}})),e.add(r)}(e,t),!0;case"enum":return function(e,t){if(!T.test(t=U()))throw H(t,"name");var r=new u(t);Y(r,(function(e){switch(e){case"option":Q(r,e),P(";");break;case"reserved":W(r.reserved||(r.reserved=[]),!0);break;default:!function(e,t){if(!T.test(t))throw H(t,"name");P("=");var r=K(U(),!0),o={};Y(o,(function(e){if("option"!==e)throw H(e);Q(o,e),P(";")}),(function(){ee(o)})),e.add(t,r,o.comment)}(r,e)}})),e.add(r)}(e,t),!0;case"service":return function(e,t){if(!T.test(t=U()))throw H(t,"service name");var r=new l(t);Y(r,(function(e){if(!J(r,e)){if("rpc"!==e)throw H(e);!function(e,t){var r=t;if(!T.test(t=U()))throw H(t,"name");var o,n,i,a,s=t;if(P("("),P("stream",!0)&&(n=!0),!I.test(t=U()))throw H(t);if(o=t,P(")"),P("returns"),P("("),P("stream",!0)&&(a=!0),!I.test(t=U()))throw H(t);i=t,P(")");var c=new d(s,r,o,i,n,a);Y(c,(function(e){if("option"!==e)throw H(e);Q(c,e),P(";")})),e.add(c)}(r,e)}})),e.add(r)}(e,t),!0;case"extend":return function(e,t){if(!I.test(t=U()))throw H(t,"reference");var r=t;Y(null,(function(t){switch(t){case"required":case"repeated":case"optional":X(e,t,r);break;default:if(!x||!I.test(t))throw H(t);w(t),X(e,"optional",r)}}))}(e,t),!0}return!1}function Y(e,t,r){var o=M.line;if(e&&(e.comment=D(),e.filename=R.filename),P("{",!0)){for(var n;"}"!==(n=U());)t(n);P(";",!0)}else r&&r(),P(";"),e&&"string"!=typeof e.comment&&(e.comment=D(o))}function X(e,t,r){var o=U();if("group"!==o){if(!I.test(o))throw H(o,"type");var n=U();if(!T.test(n))throw H(n,"name");n=B(n),P("=");var s=new a(n,K(U()),o,t,r);Y(s,(function(e){if("option"!==e)throw H(e);Q(s,e),P(";")}),(function(){ee(s)})),e.add(s),x||!s.repeated||void 0===p.packed[o]&&void 0!==p.basic[o]||s.setOption("packed",!1,!0)}else!function(e,t){var r=U();if(!T.test(r))throw H(r,"name");var o=h.lcFirst(r);r===o&&(r=h.ucFirst(r)),P("=");var n=K(U()),s=new i(r);s.group=!0;var c=new a(o,n,r,t);c.filename=R.filename,Y(s,(function(e){switch(e){case"option":Q(s,e),P(";");break;case"required":case"optional":case"repeated":X(s,e);break;default:throw H(e)}})),e.add(s).add(c)}(e,t)}function Q(e,t){var r=P("(",!0);if(!I.test(t=U()))throw H(t,"name");var o=t;r&&(P(")"),o="("+o+")",t=L(),O.test(t)&&(o+=t,U())),P("="),Z(e,o)}function Z(e,t){if(P("{",!0))do{if(!T.test(b=U()))throw H(b,"name");"{"===L()?Z(e,t+"."+b):(P(":"),"{"===L()?Z(e,t+"."+b):$(e,t+"."+b,F(!0)))}while(!P("}",!0));else $(e,t,F(!0))}function $(e,t,r){e.setOption&&e.setOption(t,r)}function ee(e){if(P("[",!0)){do{Q(e,"option")}while(P(",",!0));P("]")}return e}for(;null!==(b=U());)switch(b){case"package":if(!k)throw H(b);q();break;case"import":if(!k)throw H(b);V();break;case"syntax":if(!k)throw H(b);z();break;case"option":if(!k)throw H(b);Q(G,b),P(";");break;default:if(J(G,b)){k=!1;continue}throw H(b)}return R.filename=null,{package:C,imports:N,weakImports:S,syntax:A,root:t}}R._configure=function(){o=r(496),n=r(773),i=r(465),a=r(101),s=r(83),c=r(220),u=r(660),l=r(177),d=r(512),p=r(452),h=r(629)}},940:function(e){e.exports=o;var t,r=/\/|\./;function o(e,t){r.test(e)||(e="google/protobuf/"+e+".proto",t={nested:{google:{nested:{protobuf:{nested:t}}}}}),o[e]=t}o("any",{Any:{fields:{type_url:{type:"string",id:1},value:{type:"bytes",id:2}}}}),o("duration",{Duration:t={fields:{seconds:{type:"int64",id:1},nanos:{type:"int32",id:2}}}}),o("timestamp",{Timestamp:t}),o("empty",{Empty:{fields:{}}}),o("struct",{Struct:{fields:{fields:{keyType:"string",type:"Value",id:1}}},Value:{oneofs:{kind:{oneof:["nullValue","numberValue","stringValue","boolValue","structValue","listValue"]}},fields:{nullValue:{type:"NullValue",id:1},numberValue:{type:"double",id:2},stringValue:{type:"string",id:3},boolValue:{type:"bool",id:4},structValue:{type:"Struct",id:5},listValue:{type:"ListValue",id:6}}},NullValue:{values:{NULL_VALUE:0}},ListValue:{fields:{values:{rule:"repeated",type:"Value",id:1}}}}),o("wrappers",{DoubleValue:{fields:{value:{type:"double",id:1}}},FloatValue:{fields:{value:{type:"float",id:1}}},Int64Value:{fields:{value:{type:"int64",id:1}}},UInt64Value:{fields:{value:{type:"uint64",id:1}}},Int32Value:{fields:{value:{type:"int32",id:1}}},UInt32Value:{fields:{value:{type:"uint32",id:1}}},BoolValue:{fields:{value:{type:"bool",id:1}}},StringValue:{fields:{value:{type:"string",id:1}}},BytesValue:{fields:{value:{type:"bytes",id:1}}}}),o("field_mask",{FieldMask:{fields:{paths:{rule:"repeated",type:"string",id:1}}}}),o.get=function(e){return o[e]||null}},970:function(e,t,r){e.exports=s;var o,n,i=r(629);function a(e,t){return RangeError("index out of range: "+e.pos+" + "+(t||1)+" > "+e.len)}function s(e){this.buf=e,this.pos=0,this.len=e.length}var c,u="undefined"!=typeof Uint8Array?function(e){return e instanceof Uint8Array||Array.isArray(e)?new s(e):("undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer&&console.warn(""),new s(new Uint8Array(e)))}:function(e){if(Array.isArray(e))return new s(e)};function l(){var e=new o(0,0),t=0;if(!(this.len-this.pos>4)){for(;t<3;++t){if(this.pos>=this.len)throw a(this);if(e.lo=(e.lo|(127&this.buf[this.pos])<<7*t)>>>0,this.buf[this.pos++]<128)return e}return e.lo=(e.lo|(127&this.buf[this.pos++])<<7*t)>>>0,e}for(;t<4;++t)if(e.lo=(e.lo|(127&this.buf[this.pos])<<7*t)>>>0,this.buf[this.pos++]<128)return e;if(e.lo=(e.lo|(127&this.buf[this.pos])<<28)>>>0,e.hi=(e.hi|(127&this.buf[this.pos])>>4)>>>0,this.buf[this.pos++]<128)return e;if(t=0,this.len-this.pos>4){for(;t<5;++t)if(e.hi=(e.hi|(127&this.buf[this.pos])<<7*t+3)>>>0,this.buf[this.pos++]<128)return e}else for(;t<5;++t){if(this.pos>=this.len)throw a(this);if(e.hi=(e.hi|(127&this.buf[this.pos])<<7*t+3)>>>0,this.buf[this.pos++]<128)return e}throw Error("invalid varint encoding")}function d(e,t){return(e[t-4]|e[t-3]<<8|e[t-2]<<16|e[t-1]<<24)>>>0}function p(){if(this.pos+8>this.len)throw a(this,8);return new o(d(this.buf,this.pos+=4),d(this.buf,this.pos+=4))}s.create=i.Buffer?function(e){return(s.create=function(e){return i.Buffer.isBuffer(e)?new(void 0)(e):u(e)})(e)}:u,s.prototype._slice=i.Array.prototype.subarray||i.Array.prototype.slice,s.prototype.uint32=(c=4294967295,function(){if(c=(127&this.buf[this.pos])>>>0,this.buf[this.pos++]<128)return c;if(c=(c|(127&this.buf[this.pos])<<7)>>>0,this.buf[this.pos++]<128)return c;if(c=(c|(127&this.buf[this.pos])<<14)>>>0,this.buf[this.pos++]<128)return c;if(c=(c|(127&this.buf[this.pos])<<21)>>>0,this.buf[this.pos++]<128)return c;if(c=(c|(15&this.buf[this.pos])<<28)>>>0,this.buf[this.pos++]<128)return c;if((this.pos+=5)>this.len)throw this.pos=this.len,a(this,10);return c}),s.prototype.int32=function(){return 0|this.uint32()},s.prototype.sint32=function(){var e=this.uint32();return e>>>1^-(1&e)},s.prototype.bool=function(){return 0!==this.uint32()},s.prototype.fixed32=function(){if(this.pos+4>this.len)throw a(this,4);return d(this.buf,this.pos+=4)},s.prototype.sfixed32=function(){if(this.pos+4>this.len)throw a(this,4);return 0|d(this.buf,this.pos+=4)},s.prototype.float=function(){if(this.pos+4>this.len)throw a(this,4);var e=i.float.readFloatLE(this.buf,this.pos);return this.pos+=4,e},s.prototype.double=function(){if(this.pos+8>this.len)throw a(this,4);var e=i.float.readDoubleLE(this.buf,this.pos);return this.pos+=8,e},s.prototype.bytes=function(){var e=this.uint32(),t=this.pos,r=this.pos+e;if(r>this.len)throw a(this,e);return this.pos+=e,Array.isArray(this.buf)?this.buf.slice(t,r):t===r?new this.buf.constructor(0):this._slice.call(this.buf,t,r)},s.prototype.string=function(){var e=this.bytes();return n.read(e,0,e.length)},s.prototype.skip=function(e){if("number"==typeof e){if(this.pos+e>this.len)throw a(this,e);this.pos+=e}else do{if(this.pos>=this.len)throw a(this)}while(128&this.buf[this.pos++]);return this},s.prototype.skipType=function(e){switch(e){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:for(;4!=(e=7&this.uint32());)this.skipType(e);break;case 5:this.skip(4);break;default:throw Error("invalid wire type "+e+" at offset "+this.pos)}return this},s._configure=function(){o=r(183),n=r(442);var e=i.Long?"toLong":"toNumber";i.merge(s.prototype,{int64:function(){return l.call(this)[e](!1)},uint64:function(){return l.call(this)[e](!0)},sint64:function(){return l.call(this).zzDecode()[e](!1)},fixed64:function(){return p.call(this)[e](!0)},sfixed64:function(){return p.call(this)[e](!1)}})}},986:function(e){"use strict";var t=e.exports;t.length=function(e){var t=e.length;if(!t)return 0;for(var r=0;--t%4>1&&"="===e.charAt(t);)++r;return Math.ceil(3*e.length)/4-r};for(var r=new Array(64),o=new Array(123),n=0;n<64;)o[r[n]=n<26?n+65:n<52?n+71:n<62?n-4:n-59|43]=n++;t.encode=function(e,t,o){for(var n,i=null,a=[],s=0,c=0;t<o;){var u=e[t++];switch(c){case 0:a[s++]=r[u>>2],n=(3&u)<<4,c=1;break;case 1:a[s++]=r[n|u>>4],n=(15&u)<<2,c=2;break;case 2:a[s++]=r[n|u>>6],a[s++]=r[63&u],c=0}s>8191&&((i||(i=[])).push(String.fromCharCode.apply(String,a)),s=0)}return c&&(a[s++]=r[n],a[s++]=61,1===c&&(a[s++]=61)),i?(s&&i.push(String.fromCharCode.apply(String,a.slice(0,s))),i.join("")):String.fromCharCode.apply(String,a.slice(0,s))};var i="invalid encoding";t.decode=function(e,t,r){for(var n,a=r,s=0,c=0;c<e.length;){var u=e.charCodeAt(c++);if(61===u&&s>1)break;if(void 0===(u=o[u]))throw Error(i);switch(s){case 0:n=u,s=1;break;case 1:t[r++]=n<<2|(48&u)>>4,n=u,s=2;break;case 2:t[r++]=(15&n)<<4|(60&u)>>2,n=u,s=3;break;case 3:t[r++]=(3&n)<<6|u,s=0}}if(1===s)throw Error(i);return r-a},t.test=function(e){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(e)}}},t={};function r(o){var n=t[o];if(void 0!==n)return n.exports;var i=t[o]={exports:{}};return e[o](i,i.exports,r),i.exports}r.amdO={},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,{a:t}),t},r.d=function(e,t){for(var o in t)r.o(t,o)&&!r.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};return function(){"use strict";r.r(o),r.d(o,{default:function(){return Ls}});var e={};r.r(e),r.d(e,{acceptContactInvite:function(){return so},acceptInvitation:function(){return ao},addContact:function(){return oo},addConversationMark:function(){return Po},addReaction:function(){return yo},addToBlackList:function(){return lo},addUsersToBlacklist:function(){return po},addUsersToBlocklist:function(){return ho},declineContactInvite:function(){return uo},declineInvitation:function(){return co},deleteAllMessagesAndConversations:function(){return Go},deleteContact:function(){return io},deleteConversation:function(){return Jr},deleteReaction:function(){return _o},deleteSession:function(){return zr},fetchHistoryMessages:function(){return eo},fetchUserInfoById:function(){return Qr},getAllContacts:function(){return Uo},getBlacklist:function(){return Gr},getBlocklist:function(){return Br},getContacts:function(){return jr},getContactsWithCursor:function(){return wo},getConversationlist:function(){return qr},getHistoryMessages:function(){return ro},getReactionDetail:function(){return Oo},getReactionList:function(){return To},getReactionlist:function(){return Io},getRoster:function(){return Hr},getSelfIdsOnOtherPlatform:function(){return xo},getServerConversations:function(){return No},getServerConversationsByFilter:function(){return ko},getServerPinnedConversations:function(){return Ao},getServerPinnedMessages:function(){return Fo},getSessionList:function(){return Kr},getTokenExpireTimestamp:function(){return $r},modifyMessage:function(){return vo},pinConversation:function(){return bo},pinMessage:function(){return Ho},recallMessage:function(){return Eo},removeConversationMark:function(){return Do},removeFromBlackList:function(){return fo},removeHistoryMessages:function(){return Co},removeRoster:function(){return no},removeUserFromBlackList:function(){return mo},removeUserFromBlocklist:function(){return go},reportMessage:function(){return Ro},setContactRemark:function(){return Mo},unbindPushToken:function(){return Wo},unpinMessage:function(){return jo},updateCurrentUserNick:function(){return Zr},updateOwnUserInfo:function(){return Yr},updateUserInfo:function(){return Xr},uploadPushToken:function(){return Wr},uploadToken:function(){return Fr}});var t={};r.r(t),r.d(t,{acceptGroupInvite:function(){return Nn},acceptGroupJoinRequest:function(){return In},addUsersToGroupAllowlist:function(){return ei},addUsersToGroupWhitelist:function(){return $n},agreeInviteIntoGroup:function(){return Cn},agreeJoinGroup:function(){return Tn},blockGroup:function(){return Yo},blockGroupMember:function(){return jn},blockGroupMembers:function(){return Wn},blockGroupMessage:function(){return Ii},blockGroupMessages:function(){return Xo},changeGroupOwner:function(){return rn},changeOwner:function(){return tn},createGroup:function(){return zo},createGroupNew:function(){return Vo},createGroupVNext:function(){return Jo},deleteGroupSharedFile:function(){return fi},destroyGroup:function(){return mn},disableSendGroupMsg:function(){return Qn},dissolveGroup:function(){return fn},downloadGroupSharedFile:function(){return Ei},enableSendGroupMsg:function(){return Zn},fetchGroupAnnouncement:function(){return di},fetchGroupSharedFileList:function(){return mi},getGroup:function(){return $o},getGroupAdmin:function(){return un},getGroupAllowlist:function(){return ii},getGroupBlacklist:function(){return Yn},getGroupBlacklistNew:function(){return Jn},getGroupBlocklist:function(){return Xn},getGroupInfo:function(){return on},getGroupMemberAttributes:function(){return yi},getGroupMembers:function(){return cn},getGroupMembersAttributes:function(){return _i},getGroupMsgReadUser:function(){return li},getGroupMuteList:function(){return Gn},getGroupMutelist:function(){return Bn},getGroupSharedFilelist:function(){return gi},getGroupWhitelist:function(){return ni},getJoinedGroups:function(){return en},getJoinedGroupsCount:function(){return Ti},getMuted:function(){return xn},getPublicGroups:function(){return Zo},groupBlockMulti:function(){return Fn},groupBlockSingle:function(){return Hn},inviteToGroup:function(){return vn},inviteUsersToGroup:function(){return yn},isGroupWhiteUser:function(){return ai},isInGroupAllowlist:function(){return ci},isInGroupMutelist:function(){return ui},isInGroupWhiteList:function(){return si},joinGroup:function(){return _n},leaveGroup:function(){return En},listGroupMember:function(){return an},listGroupMembers:function(){return sn},listGroups:function(){return Qo},modifyGroup:function(){return nn},mute:function(){return Ln},muteGroupMember:function(){return Pn},quitGroup:function(){return gn},rejectGroupInvite:function(){return An},rejectGroupJoinRequest:function(){return Rn},rejectInviteIntoGroup:function(){return Sn},rejectJoinGroup:function(){return On},removeAdmin:function(){return pn},removeGroupAdmin:function(){return hn},removeGroupAllowlistMember:function(){return oi},removeGroupBlockMulti:function(){return Vn},removeGroupBlockSingle:function(){return Kn},removeGroupMember:function(){return Mn},removeGroupMembers:function(){return wn},removeGroupWhitelistMember:function(){return ri},removeMultiGroupMember:function(){return Un},removeMute:function(){return Dn},removeSingleGroupMember:function(){return bn},rmUsersFromGroupWhitelist:function(){return ti},setAdmin:function(){return ln},setGroupAdmin:function(){return dn},setGroupMemberAttributes:function(){return vi},unblockGroupMember:function(){return qn},unblockGroupMembers:function(){return zn},unblockGroupMessage:function(){return Oi},unmuteGroupMember:function(){return kn},updateGroupAnnouncement:function(){return pi},uploadGroupSharedFile:function(){return hi}});var n={};r.r(n),r.d(n,{addUsersToChatRoom:function(){return Li},addUsersToChatRoomAllowlist:function(){return ua},addUsersToChatRoomWhitelist:function(){return ca},blockChatRoomMember:function(){return Xi},blockChatRoomMembers:function(){return Zi},chatRoomBlockMulti:function(){return Qi},chatRoomBlockSingle:function(){return Yi},createChatRoom:function(){return Ci},deleteChatRoomSharedFile:function(){return Ta},destroyChatRoom:function(){return Ni},disableSendChatRoomMsg:function(){return aa},enableSendChatRoomMsg:function(){return sa},fetchChatRoomAnnouncement:function(){return va},fetchChatRoomSharedFileList:function(){return Ia},getChatRoomAdmin:function(){return Hi},getChatRoomAllowlist:function(){return fa},getChatRoomAttributes:function(){return Ra},getChatRoomBlacklist:function(){return na},getChatRoomBlacklistNew:function(){return oa},getChatRoomBlocklist:function(){return ia},getChatRoomDetails:function(){return Si},getChatRoomMembers:function(){return Bi},getChatRoomMuteList:function(){return zi},getChatRoomMuted:function(){return Vi},getChatRoomMutelist:function(){return Ji},getChatRoomSharedFilelist:function(){return Oa},getChatRoomWhitelist:function(){return ha},getChatRooms:function(){return Ri},getJoinedChatRooms:function(){return ba},isChatRoomWhiteUser:function(){return ma},isInChatRoomAllowlist:function(){return ga},isInChatRoomMutelist:function(){return Ea},joinChatRoom:function(){return Pi},leaveChatRoom:function(){return ki},listChatRoomMember:function(){return xi},listChatRoomMembers:function(){return Gi},modifyChatRoom:function(){return Ai},muteChatRoomMember:function(){return Wi},quitChatRoom:function(){return Di},removeChatRoomAdmin:function(){return Fi},removeChatRoomAllowlistMember:function(){return pa},removeChatRoomAttribute:function(){return Aa},removeChatRoomAttributes:function(){return Sa},removeChatRoomBlockMulti:function(){return ta},removeChatRoomBlockSingle:function(){return $i},removeChatRoomMember:function(){return Mi},removeChatRoomMembers:function(){return wi},removeChatRoomWhitelistMember:function(){return da},removeMultiChatRoomMember:function(){return Ui},removeMuteChatRoomMember:function(){return Ki},removeSingleChatRoomMember:function(){return bi},rmUsersFromChatRoomWhitelist:function(){return la},setChatRoomAdmin:function(){return ji},setChatRoomAttribute:function(){return Na},setChatRoomAttributes:function(){return Ca},unblockChatRoomMember:function(){return ea},unblockChatRoomMembers:function(){return ra},unmuteChatRoomMember:function(){return qi},updateChatRoomAnnouncement:function(){return ya},uploadChatRoomSharedFile:function(){return _a}});var i={};r.r(i),r.d(i,{getPresenceStatus:function(){return xa},getSubscribedPresenceList:function(){return Da},getSubscribedPresencelist:function(){return ka},publishPresence:function(){return wa},subscribePresence:function(){return La},unsubscribePresence:function(){return Pa}});var a={};r.r(a),r.d(a,{clearRemindTypeForConversation:function(){return ja},getPushPerformLanguage:function(){return qa},getSilentModeForAll:function(){return Ba},getSilentModeForConversation:function(){return Fa},getSilentModeForConversations:function(){return Wa},getSilentModeRemindTypeConversations:function(){return Va},setPushPerformLanguage:function(){return Ka},setSilentModeForAll:function(){return Ga},setSilentModeForConversation:function(){return Ha}});var s={};r.r(s),r.d(s,{changeChatThreadName:function(){return $a},createChatThread:function(){return Ya},destroyChatThread:function(){return Za},getChatThreadDetail:function(){return as},getChatThreadLastMessage:function(){return ns},getChatThreadMembers:function(){return es},getChatThreads:function(){return os},getJoinedChatThreads:function(){return rs},joinChatThread:function(){return Xa},leaveChatThread:function(){return Qa},removeChatThreadMember:function(){return ts}});var c={};r.r(c),r.d(c,{getSupportedLanguages:function(){return ss},translateMessage:function(){return cs}});var u={};function l(){u.converter._configure(),u.decoder._configure(),u.encoder._configure(),u.Field._configure(),u.MapField._configure(),u.Message._configure(),u.Namespace._configure(),u.Method._configure(),u.ReflectionObject._configure(),u.OneOf._configure(),u.parse._configure(),u.Reader._configure(),u.Root._configure(),u.Service._configure(),u.verifier._configure(),u.Type._configure(),u.types._configure(),u.wrappers._configure(),u.Writer._configure()}u.build="minimal",u.Writer=r(322),u.encoder=r(229),u.Reader=r(970),u.util=r(629),u.rpc=r(470),u.roots=r(896),u.verifier=r(811),u.tokenize=r(496),u.parse=r(922),u.common=r(940),u.ReflectionObject=r(558),u.Namespace=r(694),u.Root=r(773),u.Enum=r(660),u.Type=r(465),u.Field=r(101),u.OneOf=r(220),u.MapField=r(83),u.Service=r(177),u.Method=r(512),u.converter=r(921),u.decoder=r(801),u.Message=r(278),u.wrappers=r(793),u.types=r(452),u.util=r(629),u.configure=l,u.load=function(e,t,r){return"function"==typeof t?(r=t,t=new u.Root):t||(t=new u.Root),t.load(e,r)},u.loadSync=function(e,t){return t||(t=new u.Root),t.loadSync(e)},u.parseFromPbString=function(e,t,r){return"function"==typeof t?(r=t,t=new u.Root):t||(t=new u.Root),t.parseFromPbString(e,r)},l();var d,p=u,h=r(570),f=r.n(h),m=function(){function e(e){this.type=e.type,this.message=e.message,this.data=e.data}return e.create=function(t){return new e(t)},e}();!function(e){e[e.REQUEST_SUCCESS=0]="REQUEST_SUCCESS",e[e.REQUEST_TIMEOUT=-1]="REQUEST_TIMEOUT",e[e.REQUEST_UNKNOWN=-2]="REQUEST_UNKNOWN",e[e.REQUEST_PARAMETER_ERROR=-3]="REQUEST_PARAMETER_ERROR",e[e.REQUEST_ABORT=-4]="REQUEST_ABORT",e[e.WEBIM_CONNCTION_USER_NOT_ASSIGN_ERROR=0]="WEBIM_CONNCTION_USER_NOT_ASSIGN_ERROR",e[e.WEBIM_CONNCTION_OPEN_ERROR=1]="WEBIM_CONNCTION_OPEN_ERROR",e[e.WEBIM_CONNCTION_AUTH_ERROR=2]="WEBIM_CONNCTION_AUTH_ERROR",e[e.WEBIM_CONNCTION_OPEN_USERGRID_ERROR=3]="WEBIM_CONNCTION_OPEN_USERGRID_ERROR",e[e.WEBIM_CONNCTION_ATTACH_ERROR=4]="WEBIM_CONNCTION_ATTACH_ERROR",e[e.WEBIM_CONNCTION_ATTACH_USERGRID_ERROR=5]="WEBIM_CONNCTION_ATTACH_USERGRID_ERROR",e[e.WEBIM_CONNCTION_REOPEN_ERROR=6]="WEBIM_CONNCTION_REOPEN_ERROR",e[e.WEBIM_CONNCTION_SERVER_CLOSE_ERROR=7]="WEBIM_CONNCTION_SERVER_CLOSE_ERROR",e[e.WEBIM_CONNCTION_SERVER_ERROR=8]="WEBIM_CONNCTION_SERVER_ERROR",e[e.WEBIM_CONNCTION_IQ_ERROR=9]="WEBIM_CONNCTION_IQ_ERROR",e[e.WEBIM_CONNCTION_PING_ERROR=10]="WEBIM_CONNCTION_PING_ERROR",e[e.WEBIM_CONNCTION_NOTIFYVERSION_ERROR=11]="WEBIM_CONNCTION_NOTIFYVERSION_ERROR",e[e.WEBIM_CONNCTION_GETROSTER_ERROR=12]="WEBIM_CONNCTION_GETROSTER_ERROR",e[e.WEBIM_CONNCTION_CROSSDOMAIN_ERROR=13]="WEBIM_CONNCTION_CROSSDOMAIN_ERROR",e[e.WEBIM_CONNCTION_LISTENING_OUTOF_MAXRETRIES=14]="WEBIM_CONNCTION_LISTENING_OUTOF_MAXRETRIES",e[e.WEBIM_CONNCTION_RECEIVEMSG_CONTENTERROR=15]="WEBIM_CONNCTION_RECEIVEMSG_CONTENTERROR",e[e.WEBIM_CONNCTION_DISCONNECTED=16]="WEBIM_CONNCTION_DISCONNECTED",e[e.WEBIM_CONNCTION_AJAX_ERROR=17]="WEBIM_CONNCTION_AJAX_ERROR",e[e.WEBIM_CONNCTION_JOINROOM_ERROR=18]="WEBIM_CONNCTION_JOINROOM_ERROR",e[e.WEBIM_CONNCTION_GETROOM_ERROR=19]="WEBIM_CONNCTION_GETROOM_ERROR",e[e.WEBIM_CONNCTION_GETROOMINFO_ERROR=20]="WEBIM_CONNCTION_GETROOMINFO_ERROR",e[e.WEBIM_CONNCTION_GETROOMMEMBER_ERROR=21]="WEBIM_CONNCTION_GETROOMMEMBER_ERROR",e[e.WEBIM_CONNCTION_GETROOMOCCUPANTS_ERROR=22]="WEBIM_CONNCTION_GETROOMOCCUPANTS_ERROR",e[e.WEBIM_CONNCTION_LOAD_CHATROOM_ERROR=23]="WEBIM_CONNCTION_LOAD_CHATROOM_ERROR",e[e.WEBIM_CONNCTION_NOT_SUPPORT_CHATROOM_ERROR=24]="WEBIM_CONNCTION_NOT_SUPPORT_CHATROOM_ERROR",e[e.WEBIM_CONNCTION_JOINCHATROOM_ERROR=25]="WEBIM_CONNCTION_JOINCHATROOM_ERROR",e[e.WEBIM_CONNCTION_QUITCHATROOM_ERROR=26]="WEBIM_CONNCTION_QUITCHATROOM_ERROR",e[e.WEBIM_CONNCTION_APPKEY_NOT_ASSIGN_ERROR=27]="WEBIM_CONNCTION_APPKEY_NOT_ASSIGN_ERROR",e[e.WEBIM_CONNCTION_TOKEN_NOT_ASSIGN_ERROR=28]="WEBIM_CONNCTION_TOKEN_NOT_ASSIGN_ERROR",e[e.WEBIM_CONNCTION_SESSIONID_NOT_ASSIGN_ERROR=29]="WEBIM_CONNCTION_SESSIONID_NOT_ASSIGN_ERROR",e[e.WEBIM_CONNCTION_RID_NOT_ASSIGN_ERROR=30]="WEBIM_CONNCTION_RID_NOT_ASSIGN_ERROR",e[e.WEBIM_CONNCTION_CALLBACK_INNER_ERROR=31]="WEBIM_CONNCTION_CALLBACK_INNER_ERROR",e[e.WEBIM_CONNCTION_CLIENT_OFFLINE=32]="WEBIM_CONNCTION_CLIENT_OFFLINE",e[e.WEBIM_CONNCTION_CLIENT_LOGOUT=33]="WEBIM_CONNCTION_CLIENT_LOGOUT",e[e.WEBIM_CONNCTION_CLIENT_TOO_MUCH_ERROR=34]="WEBIM_CONNCTION_CLIENT_TOO_MUCH_ERROR",e[e.WEBIM_CONNECTION_ACCEPT_INVITATION_FROM_GROUP=35]="WEBIM_CONNECTION_ACCEPT_INVITATION_FROM_GROUP",e[e.WEBIM_CONNECTION_DECLINE_INVITATION_FROM_GROUP=36]="WEBIM_CONNECTION_DECLINE_INVITATION_FROM_GROUP",e[e.WEBIM_CONNECTION_ACCEPT_JOIN_GROUP=37]="WEBIM_CONNECTION_ACCEPT_JOIN_GROUP",e[e.WEBIM_CONNECTION_DECLINE_JOIN_GROUP=38]="WEBIM_CONNECTION_DECLINE_JOIN_GROUP",e[e.WEBIM_CONNECTION_CLOSED=39]="WEBIM_CONNECTION_CLOSED",e[e.WEBIM_CONNECTION_ERROR=40]="WEBIM_CONNECTION_ERROR",e[e.MAX_LIMIT=50]="MAX_LIMIT",e[e.MESSAGE_NOT_FOUND=51]="MESSAGE_NOT_FOUND",e[e.NO_PERMISSION=52]="NO_PERMISSION",e[e.OPERATION_UNSUPPORTED=53]="OPERATION_UNSUPPORTED",e[e.OPERATION_NOT_ALLOWED=54]="OPERATION_NOT_ALLOWED",e[e.WEBIM_TOKEN_EXPIRED=56]="WEBIM_TOKEN_EXPIRED",e[e.WEBIM_SERVER_SERVING_DISABLED=57]="WEBIM_SERVER_SERVING_DISABLED",e[e.WEBIM_UPLOADFILE_BROWSER_ERROR=100]="WEBIM_UPLOADFILE_BROWSER_ERROR",e[e.WEBIM_UPLOADFILE_ERROR=101]="WEBIM_UPLOADFILE_ERROR",e[e.WEBIM_UPLOADFILE_NO_LOGIN=102]="WEBIM_UPLOADFILE_NO_LOGIN",e[e.WEBIM_UPLOADFILE_NO_FILE=103]="WEBIM_UPLOADFILE_NO_FILE",e[e.WEBIM_DOWNLOADFILE_ERROR=200]="WEBIM_DOWNLOADFILE_ERROR",e[e.WEBIM_DOWNLOADFILE_NO_LOGIN=201]="WEBIM_DOWNLOADFILE_NO_LOGIN",e[e.WEBIM_DOWNLOADFILE_BROWSER_ERROR=202]="WEBIM_DOWNLOADFILE_BROWSER_ERROR",e[e.PARSE_FILE_ERROR=203]="PARSE_FILE_ERROR",e[e.USER_NOT_FOUND=204]="USER_NOT_FOUND",e[e.MESSAGE_PARAMETER_ERROR=205]="MESSAGE_PARAMETER_ERROR",e[e.WEBIM_CONNCTION_USER_LOGIN_ANOTHER_DEVICE=206]="WEBIM_CONNCTION_USER_LOGIN_ANOTHER_DEVICE",e[e.WEBIM_CONNCTION_USER_REMOVED=207]="WEBIM_CONNCTION_USER_REMOVED",e[e.WEBIM_USER_ALREADY_LOGIN=208]="WEBIM_USER_ALREADY_LOGIN",e[e.WEBIM_CONNCTION_USER_KICKED_BY_CHANGE_PASSWORD=216]="WEBIM_CONNCTION_USER_KICKED_BY_CHANGE_PASSWORD",e[e.WEBIM_CONNCTION_USER_KICKED_BY_OTHER_DEVICE=217]="WEBIM_CONNCTION_USER_KICKED_BY_OTHER_DEVICE",e[e.USER_MUTED_BY_ADMIN=219]="USER_MUTED_BY_ADMIN",e[e.USER_NOT_FRIEND=221]="USER_NOT_FRIEND",e[e.WEBIM_DOWNLOADFILE_ERROR_NO_PERMISSION=222]="WEBIM_DOWNLOADFILE_ERROR_NO_PERMISSION",e[e.WEBIM_DOWNLOADFILE_ERROR_EXPIRED=223]="WEBIM_DOWNLOADFILE_ERROR_EXPIRED",e[e.WEBIM_MESSAGE_REC_TEXT=300]="WEBIM_MESSAGE_REC_TEXT",e[e.WEBIM_MESSAGE_REC_TEXT_ERROR=301]="WEBIM_MESSAGE_REC_TEXT_ERROR",e[e.WEBIM_MESSAGE_REC_EMOTION=302]="WEBIM_MESSAGE_REC_EMOTION",e[e.WEBIM_MESSAGE_REC_PHOTO=303]="WEBIM_MESSAGE_REC_PHOTO",e[e.SERVER_GET_DNSLIST_FAILED=304]="SERVER_GET_DNSLIST_FAILED",e[e.WEBIM_MESSAGE_REC_AUDIO_FILE=305]="WEBIM_MESSAGE_REC_AUDIO_FILE",e[e.WEBIM_MESSAGE_REC_VEDIO=306]="WEBIM_MESSAGE_REC_VEDIO",e[e.WEBIM_MESSAGE_REC_VEDIO_FILE=307]="WEBIM_MESSAGE_REC_VEDIO_FILE",e[e.WEBIM_MESSAGE_REC_FILE=308]="WEBIM_MESSAGE_REC_FILE",e[e.WEBIM_MESSAGE_SED_TEXT=309]="WEBIM_MESSAGE_SED_TEXT",e[e.WEBIM_MESSAGE_SED_EMOTION=310]="WEBIM_MESSAGE_SED_EMOTION",e[e.WEBIM_MESSAGE_SED_PHOTO=311]="WEBIM_MESSAGE_SED_PHOTO",e[e.WEBIM_MESSAGE_SED_AUDIO=312]="WEBIM_MESSAGE_SED_AUDIO",e[e.WEBIM_MESSAGE_SED_AUDIO_FILE=313]="WEBIM_MESSAGE_SED_AUDIO_FILE",e[e.WEBIM_MESSAGE_SED_VEDIO=314]="WEBIM_MESSAGE_SED_VEDIO",e[e.WEBIM_MESSAGE_SED_VEDIO_FILE=315]="WEBIM_MESSAGE_SED_VEDIO_FILE",e[e.WEBIM_MESSAGE_SED_FILE=316]="WEBIM_MESSAGE_SED_FILE",e[e.WEBIM_MESSAGE_SED_ERROR=317]="WEBIM_MESSAGE_SED_ERROR",e[e.STATUS_INIT=400]="STATUS_INIT",e[e.STATUS_DOLOGIN_USERGRID=401]="STATUS_DOLOGIN_USERGRID",e[e.STATUS_DOLOGIN_IM=402]="STATUS_DOLOGIN_IM",e[e.STATUS_OPENED=403]="STATUS_OPENED",e[e.STATUS_CLOSING=404]="STATUS_CLOSING",e[e.STATUS_CLOSED=405]="STATUS_CLOSED",e[e.STATUS_ERROR=406]="STATUS_ERROR",e[e.SERVER_BUSY=500]="SERVER_BUSY",e[e.MESSAGE_INCLUDE_ILLEGAL_CONTENT=501]="MESSAGE_INCLUDE_ILLEGAL_CONTENT",e[e.MESSAGE_EXTERNAL_LOGIC_BLOCKED=502]="MESSAGE_EXTERNAL_LOGIC_BLOCKED",e[e.SERVER_UNKNOWN_ERROR=503]="SERVER_UNKNOWN_ERROR",e[e.MESSAGE_RECALL_TIME_LIMIT=504]="MESSAGE_RECALL_TIME_LIMIT",e[e.SERVICE_NOT_ENABLED=505]="SERVICE_NOT_ENABLED",e[e.SERVICE_NOT_ALLOW_MESSAGING=506]="SERVICE_NOT_ALLOW_MESSAGING",e[e.SERVICE_NOT_ALLOW_MESSAGING_MUTE=507]="SERVICE_NOT_ALLOW_MESSAGING_MUTE",e[e.MESSAGE_MODERATION_BLOCKED=508]="MESSAGE_MODERATION_BLOCKED",e[e.MESSAGE_CURRENT_LIMITING=509]="MESSAGE_CURRENT_LIMITING",e[e.MESSAGE_WEBSOCKET_DISCONNECTED=510]="MESSAGE_WEBSOCKET_DISCONNECTED",e[e.MESSAGE_SIZE_LIMIT=511]="MESSAGE_SIZE_LIMIT",e[e.MESSAGE_SEND_TIMEOUT=512]="MESSAGE_SEND_TIMEOUT",e[e.GROUP_NOT_EXIST=605]="GROUP_NOT_EXIST",e[e.GROUP_NOT_JOINED=602]="GROUP_NOT_JOINED",e[e.GROUP_MEMBERS_FULL=606]="GROUP_MEMBERS_FULL",e[e.PERMISSION_DENIED=603]="PERMISSION_DENIED",e[e.WEBIM_LOAD_MSG_ERROR=604]="WEBIM_LOAD_MSG_ERROR",e[e.GROUP_ALREADY_JOINED=601]="GROUP_ALREADY_JOINED",e[e.GROUP_MEMBERS_LIMIT=607]="GROUP_MEMBERS_LIMIT",e[e.GROUP_IS_DISABLED=608]="GROUP_IS_DISABLED",e[e.GROUP_MEMBER_ATTRIBUTES_SET_FAILED=609]="GROUP_MEMBER_ATTRIBUTES_SET_FAILED",e[e.REST_PARAMS_STATUS=700]="REST_PARAMS_STATUS",e[e.CHATROOM_NOT_JOINED=702]="CHATROOM_NOT_JOINED",e[e.CHATROOM_MEMBERS_FULL=704]="CHATROOM_MEMBERS_FULL",e[e.CHATROOM_NOT_EXIST=705]="CHATROOM_NOT_EXIST",e[e.LOCAL_DB_OPERATION_FAILED=800]="LOCAL_DB_OPERATION_FAILED",e[e.SDK_RUNTIME_ERROR=999]="SDK_RUNTIME_ERROR",e[e.PRESENCE_PARAM_EXCEED=1100]="PRESENCE_PARAM_EXCEED",e[e.REACTION_ALREADY_ADDED=1101]="REACTION_ALREADY_ADDED",e[e.REACTION_CREATING=1102]="REACTION_CREATING",e[e.REACTION_OPERATION_IS_ILLEGAL=1103]="REACTION_OPERATION_IS_ILLEGAL",e[e.TRANSLATION_NOT_VALID=1200]="TRANSLATION_NOT_VALID",e[e.TRANSLATION_TEXT_TOO_LONG=1201]="TRANSLATION_TEXT_TOO_LONG",e[e.TRANSLATION_FAILED=1204]="TRANSLATION_FAILED",e[e.THREAD_NOT_EXIST=1300]="THREAD_NOT_EXIST",e[e.THREAD_ALREADY_EXIST=1301]="THREAD_ALREADY_EXIST",e[e.MODIFY_MESSAGE_NOT_EXIST=1302]="MODIFY_MESSAGE_NOT_EXIST",e[e.MODIFY_MESSAGE_FORMAT_ERROR=1303]="MODIFY_MESSAGE_FORMAT_ERROR",e[e.MODIFY_MESSAGE_FAILED=1304]="MODIFY_MESSAGE_FAILED",e[e.CONVERSATION_NOT_EXIST=1400]="CONVERSATION_NOT_EXIST"}(d||(d={}));var g,E,v,y,_,T,I,O=function(){return O=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},O.apply(this,arguments)},R=function(e,t,r,o){var n,i,a,s,c,u,l,p,h,f,m,g,E,v,y,_,T,I,R,C,N,S,A,b,M,U,w,L,P=e.response;P&&"string"==typeof P&&(P=JSON.parse(P));var D=e.status,k={elapse:o,httpCode:D,errDesc:null==P?void 0:P.error_description};if(400===D){if(40002===P.error_code)return void t({type:d.THREAD_ALREADY_EXIST,message:null==P?void 0:P.error_description,extraInfo:k});if(40009===P.error_code)return void t({type:d.OPERATION_UNSUPPORTED,message:null==P?void 0:P.error_description,extraInfo:k});if(60005===P.error_code)return void t({type:d.GROUP_MEMBER_ATTRIBUTES_SET_FAILED,message:(null==P?void 0:P.desc)||(null==P?void 0:P.error_description),extraInfo:k});if(60010===P.error_code)return void((null===(n=null==P?void 0:P.error_description)||void 0===n?void 0:n.includes("exceeds chatgroup user metadata single value limit"))?t({type:d.MAX_LIMIT,message:(null==P?void 0:P.desc)||P.error_description,extraInfo:k}):t({type:d.NO_PERMISSION,message:(null==P?void 0:P.desc)||P.error_description,extraInfo:k}));if(60011===P.error_code)return void t({type:d.CHATROOM_NOT_JOINED,message:null==P?void 0:P.desc,extraInfo:k});if(14403===P.error_code)return void t({type:d.WEBIM_UPLOADFILE_ERROR,message:null==P?void 0:P.error_description,data:P,extraInfo:k});if(60006===P.error_code||60007===P.error_code||60009===P.error_code||60012===P.error_code)return void t({type:d.MAX_LIMIT,message:(null==P?void 0:P.desc)||(null==P?void 0:P.error_description),extraInfo:k});if(91104===P.error_code)return void t({type:d.NO_PERMISSION,message:null==P?void 0:P.error_description,extraInfo:k});if(null===(i=P.error_description)||void 0===i?void 0:i.includes("are not members of this group"))return(null===(a=e.responseURL)||void 0===a?void 0:a.includes("chatgroups"))?t({type:d.GROUP_NOT_JOINED,data:e.response||e.responseText,message:P.error_description,extraInfo:k}):t({type:d.CHATROOM_NOT_JOINED,data:e.response||e.responseText,message:P.error_description,extraInfo:k}),void r({type:d.WEBIM_CONNCTION_AJAX_ERROR,message:null==P?void 0:P.error_description,data:e.responseText,extraInfo:k});if("the app not open presence"===(null==P?void 0:P.result))return void t({type:d.SERVICE_NOT_ENABLED,message:null==P?void 0:P.result,extraInfo:k});if(null===(s=null==P?void 0:P.error_description)||void 0===s?void 0:s.includes("remark length must less"))return void t({type:d.MAX_LIMIT,message:null==P?void 0:P.error_description,extraInfo:k});switch(null==P?void 0:P.error_description){case"the user is already operation this message":t({type:d.REACTION_ALREADY_ADDED,message:null==P?void 0:P.error_description,extraInfo:k});break;case"The quantity has exceeded the limit!":t({type:d.MAX_LIMIT,message:null==P?void 0:P.error_description,extraInfo:k});break;case"The user not in this group!":t({type:d.GROUP_NOT_JOINED,message:null==P?void 0:P.error_description,extraInfo:k});break;case"the user operation is illegal!":t({type:d.REACTION_OPERATION_IS_ILLEGAL,message:null==P?void 0:P.error_description,extraInfo:k});break;case"this appKey is not open reaction service!":case"this appKey not open message roaming":t({type:d.SERVICE_NOT_ENABLED,message:null==P?void 0:P.error_description,extraInfo:k});break;case"this message is creating reaction, please try again.":t({type:d.REACTION_CREATING,message:null==P?void 0:P.error_description,extraInfo:k});break;case"groupId can not be null!":t({type:d.GROUP_NOT_EXIST,message:null==P?void 0:P.error_description,extraInfo:k});break;case"The input text is too long.":t({type:d.TRANSLATION_TEXT_TOO_LONG,message:null==P?void 0:P.error_description,extraInfo:k});break;case"The target language is not valid.":t({type:d.TRANSLATION_NOT_VALID,message:null==P?void 0:P.error_description,extraInfo:k});break;case"report failed, get message by id failed":t({type:d.MESSAGE_NOT_FOUND,message:null==P?void 0:P.error_description,extraInfo:k});break;case"ext is too big ":t({type:d.PRESENCE_PARAM_EXCEED,message:null==P?void 0:P.error_description,extraInfo:k});break;case"Request body not readable.Please check content type is correct!":case"param mark must be not empty, please check!":case"param mark illegal, please check it!":case"param pin_msg_id illegal, please check it!":t({type:d.REQUEST_PARAMETER_ERROR,message:null==P?void 0:P.error_description,extraInfo:k});break;case"updateRemark | they are not friends, please add as a friend first.":t({type:d.USER_NOT_FRIEND,message:null==P?void 0:P.error_description,extraInfo:k});break;default:t({type:d.WEBIM_CONNCTION_AJAX_ERROR,message:null==P?void 0:P.error_description,data:e.responseText,extraInfo:k}),r({type:d.WEBIM_CONNCTION_AJAX_ERROR,message:null==P?void 0:P.error_description,data:e.responseText,extraInfo:k})}}else if(401===D)40001===P.error_code||60001===P.error_code||"Unable to authenticate (OAuth)"===P.error_description?t({type:d.NO_PERMISSION,message:null==P?void 0:P.error_description,extraInfo:k}):(r({type:d.WEBIM_CONNCTION_AJAX_ERROR,data:e.responseText,message:e.responseText,extraInfo:k}),t({type:d.WEBIM_CONNCTION_AJAX_ERROR,data:e.responseText,message:e.responseText,extraInfo:k}));else if(403===D)4e4===P.error_code||60004===P.error_code||15002===P.error_code?t({type:d.SERVICE_NOT_ENABLED,message:null==P?void 0:P.error_description,extraInfo:k}):40003===P.error_code||40004===P.error_code?t({type:d.THREAD_ALREADY_EXIST,message:null==P?void 0:P.error_description,extraInfo:k}):40005===P.error_code||40007===P.error_code||91002===P.error_code?t({type:d.MAX_LIMIT,message:null==P?void 0:P.error_description,extraInfo:k}):60002===P.error_code?t({type:d.PERMISSION_DENIED,message:null==P?void 0:P.error_description,extraInfo:k}):91101===P.error_code?t({type:d.MAX_LIMIT,message:null==P?void 0:P.error_description,extraInfo:k}):91102===P.error_code&&t({type:d.REQUEST_PARAMETER_ERROR,message:P.error_description,extraInfo:k}),"group member list is full!"===P.error_description?(null===(c=e.responseURL)||void 0===c?void 0:c.includes("chatgroups"))?t({type:d.GROUP_MEMBERS_FULL,data:e.response||e.responseText,message:P.error_description,extraInfo:k}):t({type:d.CHATROOM_MEMBERS_FULL,data:e.response||e.responseText,message:P.error_description,extraInfo:k}):(null===(u=P.error_description)||void 0===u?void 0:u.includes("invite users to join group failed"))&&(null===(l=P.error_description)||void 0===l?void 0:l.includes("already in group"))?(null===(p=e.responseURL)||void 0===p?void 0:p.includes("chatgroups"))&&t({type:d.GROUP_ALREADY_JOINED,data:e.response||e.responseText,message:P.error_description,extraInfo:k}):(null===(h=P.error_description)||void 0===h?void 0:h.includes("are not members of this group"))?(null===(f=e.responseURL)||void 0===f?void 0:f.includes("chatgroups"))?t({type:d.GROUP_NOT_JOINED,data:e.response||e.responseText,message:P.error_description,extraInfo:k}):t({type:d.CHATROOM_NOT_JOINED,data:e.response||e.responseText,message:P.error_description,extraInfo:k}):(null===(m=P.error_description)||void 0===m?void 0:m.includes("service not open!"))||(null===(g=P.error_description)||void 0===g?void 0:g.includes("message report not open"))||(null===(E=P.error_description)||void 0===E?void 0:E.includes("messageroaming function not open"))?t({type:d.SERVICE_NOT_ENABLED,data:e.response||e.responseText,message:P.error_description,extraInfo:k}):(null===(v=P.error_description)||void 0===v?void 0:v.includes("members size is greater than max user size !"))?t({type:d.GROUP_MEMBERS_LIMIT,data:e.response||e.responseText,message:P.error_description,extraInfo:k}):(null===(y=P.error_description)||void 0===y?void 0:y.includes("can not operate this group, reason: group is disabled"))?t({type:d.GROUP_IS_DISABLED,data:e.response||e.responseText,message:P.error_description,extraInfo:k}):(null===(_=P.error_description)||void 0===_?void 0:_.includes("Invitee's contact max count"))||(null===(T=P.error_description)||void 0===T?void 0:T.includes("Inviter's contact max count"))?t({type:d.MAX_LIMIT,data:e.response||e.responseText,message:P.error_description,extraInfo:k}):t({type:d.PERMISSION_DENIED,data:e.response||e.responseText,message:"permission denied",extraInfo:O(O({},k),{errDesc:"permission denied"})}),r({type:d.WEBIM_CONNCTION_AJAX_ERROR,data:e.responseText,message:e.responseText,extraInfo:k});else if(404===D)40011===P.error_code?t({type:d.THREAD_NOT_EXIST,message:null==P?void 0:P.error_description,extraInfo:k}):40012===P.error_code?t({type:d.NO_PERMISSION,message:null==P?void 0:P.error_description,extraInfo:k}):60003===P.error_code||20004===P.error_code?t({type:d.GROUP_NOT_JOINED,message:null==P?void 0:P.error_description,extraInfo:k}):91001===P.error_code&&t({type:d.CONVERSATION_NOT_EXIST,message:null==P?void 0:P.error_description,extraInfo:k}),(null===(I=P.error_description)||void 0===I?void 0:I.includes("do not find this group"))||(null===(R=P.error_description)||void 0===R?void 0:R.includes("does not exist"))?(null===(C=e.responseURL)||void 0===C?void 0:C.includes("chatgroups"))?t({type:d.GROUP_NOT_EXIST,data:e.response||e.responseText,message:"The chat room dose not exist.",extraInfo:O(O({},k),{errDesc:"The chat room dose not exist."})}):t({type:d.CHATROOM_NOT_EXIST,data:e.response||e.responseText,message:"The chat room dose not exist.",extraInfo:O(O({},k),{errDesc:"The chat room dose not exist."})}):(null===(N=P.error_description)||void 0===N?void 0:N.includes("username"))&&(null===(S=P.error_description)||void 0===S?void 0:S.includes("doesn't exist!'"))||(null===(A=P.error_description)||void 0===A?void 0:A.includes("user not found"))||(null===(b=P.error_description)||void 0===b?void 0:b.includes("Service resource not found"))&&"UserNotFoundException"===(null==P?void 0:P.exception)?t({type:d.USER_NOT_FOUND,data:e.response||e.responseText,message:P.error_description,extraInfo:k}):(null===(M=P.error_description)||void 0===M?void 0:M.includes("user session pin message not exist"))?t({type:d.MESSAGE_NOT_FOUND,message:P.error_description,extraInfo:k}):t({type:d.WEBIM_CONNCTION_AJAX_ERROR,data:e.response||e.responseText,message:e.responseText,extraInfo:k}),r({type:d.WEBIM_CONNCTION_AJAX_ERROR,data:e.response||e.responseText,message:e.responseText,extraInfo:k});else if(406===D)90004===P.error_code&&t({type:d.OPERATION_NOT_ALLOWED,message:null==P?void 0:P.error_description,extraInfo:k});else if(429===D||503===D){if(null===(U=P.error_description)||void 0===U?void 0:U.includes("The request has reached the maximum limit"))return void t({type:d.MAX_LIMIT,message:e.responseText,extraInfo:k});if(null===(w=P.error_description)||void 0===w?void 0:w.includes("upload client logs reached limit"))return void t({type:d.MAX_LIMIT,message:e.responseText});t({type:d.SERVER_BUSY,data:e.response||e.responseText,message:"Server is busy.",extraInfo:O(O({},k),{errDesc:"Server is busy."})}),r({type:d.WEBIM_CONNCTION_AJAX_ERROR,data:e.responseText,message:"Server is busy.",extraInfo:O(O({},k),{errDesc:"Server is busy."})})}else if(500===D){if(40006===P.error_code||40008===P.error_code||40010===P.error_code)return void t({type:d.SERVER_UNKNOWN_ERROR,message:null==P?void 0:P.error_description,extraInfo:k});if(90005===P.error_code||99999===P.error_code)return void t({type:d.REQUEST_UNKNOWN,message:null==P?void 0:P.error_description,extraInfo:k});if(null===(L=P.error_description)||void 0===L?void 0:L.includes("translte failed!"))return void t({type:d.TRANSLATION_FAILED,message:e.responseText,extraInfo:k});t({type:d.WEBIM_CONNCTION_AJAX_ERROR,data:e.responseText,message:"",extraInfo:k}),r({type:d.WEBIM_CONNCTION_AJAX_ERROR,data:e.responseText,message:"",extraInfo:k})}else t({type:d.WEBIM_CONNCTION_AJAX_ERROR,data:e.responseText,message:e.responseText,extraInfo:O(O({},k),{errDesc:"ajax error"})}),r({type:d.WEBIM_CONNCTION_AJAX_ERROR,data:e.responseText,message:e.responseText,extraInfo:O(O({},k),{errDesc:"ajax error"})})};!function(e){e[e.UNKNOWOPERATION=-1]="UNKNOWOPERATION",e[e.REST_GET_SESSION_LIST=1]="REST_GET_SESSION_LIST",e[e.REST_DEL_SESSION=2]="REST_DEL_SESSION",e[e.REST_GET_HISTORY_MESSAGE=3]="REST_GET_HISTORY_MESSAGE",e[e.REST_PIN_CONVERSATION=4]="REST_PIN_CONVERSATION",e[e.REST_MARK_CONVERSATION=5]="REST_MARK_CONVERSATION",e[e.REST_UPLOAD_FILE_IN_PARTS=6]="REST_UPLOAD_FILE_IN_PARTS",e[e.REST_DELETE_MESSAGES_CONVERSATIONS=7]="REST_DELETE_MESSAGES_CONVERSATIONS",e[e.REST_PIN_MESSAGE=8]="REST_PIN_MESSAGE",e[e.REST_FETCH_PINMESSAGES=9]="REST_FETCH_PINMESSAGES",e[e.REST_FETCH_CONVERSATIONS=10]="REST_FETCH_CONVERSATIONS",e[e.REST_OPERATE=100]="REST_OPERATE",e[e.MSYNC_SENDMESSAGE=101]="MSYNC_SENDMESSAGE",e[e.MSYNC_RECALLMESSAGE=102]="MSYNC_RECALLMESSAGE",e[e.MSYNC_MODIFYMESSAGE=103]="MSYNC_MODIFYMESSAGE",e[e.MSYNC_OPERATE=200]="MSYNC_OPERATE",e[e.ROSTER_ADD=201]="ROSTER_ADD",e[e.ROSTER_REMOVE=202]="ROSTER_REMOVE",e[e.ROSTER_ACCEPT=203]="ROSTER_ACCEPT",e[e.ROSTER_DECLINE=204]="ROSTER_DECLINE",e[e.ROSTER_BAN=205]="ROSTER_BAN",e[e.ROSTER_ALLOW=206]="ROSTER_ALLOW",e[e.ROSTER_BLACKLIST=207]="ROSTER_BLACKLIST",e[e.ROSTER_CONTACTS=208]="ROSTER_CONTACTS",e[e.ROSTER_GET_ALL_CONTACTS_REMARKS=209]="ROSTER_GET_ALL_CONTACTS_REMARKS",e[e.ROSTER_GET_ALL_CONTACTS_REMARKS_FROM_SERVER_BY_PAGE=210]="ROSTER_GET_ALL_CONTACTS_REMARKS_FROM_SERVER_BY_PAGE",e[e.ROSTER_SET_CONTACT_REMARK=211]="ROSTER_SET_CONTACT_REMARK",e[e.ROSTER_OPERATE=300]="ROSTER_OPERATE",e[e.USER_LOGIN=301]="USER_LOGIN",e[e.USER_CREATE=302]="USER_CREATE",e[e.USER_UPDATE_USERINFO=303]="USER_UPDATE_USERINFO",e[e.USER_FETCH_USERINFO=304]="USER_FETCH_USERINFO",e[e.USER_UPDATE_NICK=305]="USER_UPDATE_NICK",e[e.USER_UPLOAD_PUSH_TOKEN=306]="USER_UPLOAD_PUSH_TOKEN",e[e.USER_LOGGEDIN_OTHER_PLATFORM=307]="USER_LOGGEDIN_OTHER_PLATFORM",e[e.USER_OPERATE=400]="USER_OPERATE",e[e.GROUP_CREATEGROUP=401]="GROUP_CREATEGROUP",e[e.GROUP_BLOCK_MESSAGE=402]="GROUP_BLOCK_MESSAGE",e[e.GROUP_FETCH_PUBLICGROUPS_WITHCURSOR=403]="GROUP_FETCH_PUBLICGROUPS_WITHCURSOR",e[e.GROUP_FETCH_USERS_GROUP=404]="GROUP_FETCH_USERS_GROUP",e[e.GROUP_CHANGE_OWNER=405]="GROUP_CHANGE_OWNER",e[e.GROUP_FETCH_SPECIFICATION=406]="GROUP_FETCH_SPECIFICATION",e[e.GROUP_CHANGE_GROUPATTRIBUTE=407]="GROUP_CHANGE_GROUPATTRIBUTE",e[e.GROUP_FETCH_MEMEBERS=408]="GROUP_FETCH_MEMEBERS",e[e.GROUP_GET_ADMIN=409]="GROUP_GET_ADMIN",e[e.GROUP_SET_ADMIN=410]="GROUP_SET_ADMIN",e[e.GROUP_REMOVE_ADMIN=411]="GROUP_REMOVE_ADMIN",e[e.GROUP_DESTOTYGROUP=412]="GROUP_DESTOTYGROUP",e[e.GROUP_LEAVEGROUP=413]="GROUP_LEAVEGROUP",e[e.GROUP_INVITE_TO_GROUP=414]="GROUP_INVITE_TO_GROUP",e[e.GROUP_JOIN_PUBLICGROUP=415]="GROUP_JOIN_PUBLICGROUP",e[e.GROUP_ACCEPT_JOINPUBLICGROUPAPPL=416]="GROUP_ACCEPT_JOINPUBLICGROUPAPPL",e[e.GROUP_DECLINE_JOINPUBLICGROUPAPPL=417]="GROUP_DECLINE_JOINPUBLICGROUPAPPL",e[e.GROUP_ACCEPT_INVITATION=418]="GROUP_ACCEPT_INVITATION",e[e.GROUP_DECLINE_INVITATION=419]="GROUP_DECLINE_INVITATION",e[e.GROUP_REMOVE_MEMBER=420]="GROUP_REMOVE_MEMBER",e[e.GROUP_REMOVE_MEMBERS=421]="GROUP_REMOVE_MEMBERS",e[e.GROUP_MUTE_MEMBERS=422]="GROUP_MUTE_MEMBERS",e[e.GROUP_UNMUTE_MEMBERS=423]="GROUP_UNMUTE_MEMBERS",e[e.GROUP_FETCH_MUTES=424]="GROUP_FETCH_MUTES",e[e.GROUP_BLOCK_MEMBER=425]="GROUP_BLOCK_MEMBER",e[e.GROUP_BLOCK_MEMBERS=426]="GROUP_BLOCK_MEMBERS",e[e.GROUP_UNBLOCK_MEMBER=427]="GROUP_UNBLOCK_MEMBER",e[e.GROUP_UNBLOCK_MEMBERS=428]="GROUP_UNBLOCK_MEMBERS",e[e.GROUP_GET_BLOCK_LIST=429]="GROUP_GET_BLOCK_LIST",e[e.GROUP_MUTE_ALLMEMBERS=430]="GROUP_MUTE_ALLMEMBERS",e[e.GROUP_UNMUTE_ALLMEMBERS=431]="GROUP_UNMUTE_ALLMEMBERS",e[e.GROUP_ADD_WHITELIST=432]="GROUP_ADD_WHITELIST",e[e.GROUP_REMOVE_WHITELIST=433]="GROUP_REMOVE_WHITELIST",e[e.GROUP_FETCH_WHITELIST=434]="GROUP_FETCH_WHITELIST",e[e.GROUP_IS_IN_WHITELIST=435]="GROUP_IS_IN_WHITELIST",e[e.GROUP_GET_READ_USERS=436]="GROUP_GET_READ_USERS",e[e.GROUP_FETCH_ANNOUNCEMENT=437]="GROUP_FETCH_ANNOUNCEMENT",e[e.GROUP_UPDATE_ANNOUNCEMENT=438]="GROUP_UPDATE_ANNOUNCEMENT",e[e.GROUP_UPLOAD_SHAREDFILE=439]="GROUP_UPLOAD_SHAREDFILE",e[e.GROUP_DELETE_SHAREDFILE=440]="GROUP_DELETE_SHAREDFILE",e[e.GROUP_FETCH_SHAREDFILE=441]="GROUP_FETCH_SHAREDFILE",e[e.GROUP_DOWNLOAD_SHAREDFILE=442]="GROUP_DOWNLOAD_SHAREDFILE",e[e.GROUP_MEMBER_SET_META_DATA=443]="GROUP_MEMBER_SET_META_DATA",e[e.GROUP_MEMBER_FETCH_META_DATA=444]="GROUP_MEMBER_FETCH_META_DATA",e[e.GROUP_OPERATE=500]="GROUP_OPERATE",e[e.CHATROOM_FETCH_CHATROOMSWITHPAGE=501]="CHATROOM_FETCH_CHATROOMSWITHPAGE",e[e.CHATROOM_CREATECHATROOM=502]="CHATROOM_CREATECHATROOM",e[e.CHATROOM_DESTORYCHATROOM=503]="CHATROOM_DESTORYCHATROOM",e[e.CHATROOM_FETCH_SPECIFICATION=504]="CHATROOM_FETCH_SPECIFICATION",e[e.CHATROOM_CHANGE_ATTRIBUTE=505]="CHATROOM_CHANGE_ATTRIBUTE",e[e.CHATROOM_REMOVE_MEMBER=506]="CHATROOM_REMOVE_MEMBER",e[e.CHATROOM_REMOVE_MEMBERS=507]="CHATROOM_REMOVE_MEMBERS",e[e.CHATROOM_ADD_MEMBERS=508]="CHATROOM_ADD_MEMBERS",e[e.CHATROOM_JOINCAHTROOM=509]="CHATROOM_JOINCAHTROOM",e[e.CHATROOM_LEAVECAHTROOM=510]="CHATROOM_LEAVECAHTROOM",e[e.CHATROOM_FETCH_MEMBERS=511]="CHATROOM_FETCH_MEMBERS",e[e.CHATROOM_GET_ADMIN=512]="CHATROOM_GET_ADMIN",e[e.CHATROOM_SET_ADMIN=513]="CHATROOM_SET_ADMIN",e[e.CHATROOM_REMOVE_ADMIN=514]="CHATROOM_REMOVE_ADMIN",e[e.CHATROOM_MUTE_USER=515]="CHATROOM_MUTE_USER",e[e.CHATROOM_UNMUTE_USER=516]="CHATROOM_UNMUTE_USER",e[e.CHATROOM_FETCH_MUTES=517]="CHATROOM_FETCH_MUTES",e[e.CHATROOM_BLOCK_USER=518]="CHATROOM_BLOCK_USER",e[e.CHATROOM_BLOCK_USERS=519]="CHATROOM_BLOCK_USERS",e[e.CHATROOM_UNBLOCK_USER=520]="CHATROOM_UNBLOCK_USER",e[e.CHATROOM_UNBLOCK_USERS=521]="CHATROOM_UNBLOCK_USERS",e[e.CHATROOM_FETCH_BANS=522]="CHATROOM_FETCH_BANS",e[e.CHATROOM_MUTE_ALLMEMEBERS=523]="CHATROOM_MUTE_ALLMEMEBERS",e[e.CHATROOM_UNMUTE_ALLMEMEBERS=524]="CHATROOM_UNMUTE_ALLMEMEBERS",e[e.CHATROOM_ADD_WHITELIST=525]="CHATROOM_ADD_WHITELIST",e[e.CHATROOM_REMOVE_WHITELIST=526]="CHATROOM_REMOVE_WHITELIST",e[e.CHATROOM_FETCH_WHITELIST=527]="CHATROOM_FETCH_WHITELIST",e[e.CHATROOM_FETCH_MEMBERIN_WHITELIST=528]="CHATROOM_FETCH_MEMBERIN_WHITELIST",e[e.CHATROOM_FETCH_ANNOUNCEMENT=529]="CHATROOM_FETCH_ANNOUNCEMENT",e[e.CHATROOM_UPDATE_ANNOUNCEMENT=530]="CHATROOM_UPDATE_ANNOUNCEMENT",e[e.CHATROOM_REMOVE_SHARE_FILE=531]="CHATROOM_REMOVE_SHARE_FILE",e[e.CHATROOM_GET_SHARE_FILE_LIST=532]="CHATROOM_GET_SHARE_FILE_LIST",e[e.CHATROOM_UPLOAD_FILE=533]="CHATROOM_UPLOAD_FILE",e[e.CHATROOM_SET_META_DATA=534]="CHATROOM_SET_META_DATA",e[e.CHATROOM_DELETE_META_DATA=535]="CHATROOM_DELETE_META_DATA",e[e.CHATROOM_FETCH_META_DATA=536]="CHATROOM_FETCH_META_DATA",e[e.CHATROOM_FETCH_USER_JOINED_CHATROOM=537]="CHATROOM_FETCH_USER_JOINED_CHATROOM",e[e.CHATROOM_OPERATE=600]="CHATROOM_OPERATE"}(g||(g={})),function(e){e.SDK_INTERNAL="SDK_INTERNAL",e.LOGIN="USER_LOGIN",e.REGISTER="USER_CREATE",e.GET_CHATROOM_LIST="CHATROOM_FETCH_CHATROOMSWITHPAGE",e.CREATE_CHATROOM="CHATROOM_CREATECHATROOM",e.DESTROY_CHATROOM="CHATROOM_DESTORYCHATROOM",e.GET_CHATROOM_DETAIL="CHATROOM_FETCH_SPECIFICATION",e.MODIFY_CHATROOM="CHATROOM_CHANGE_ATTRIBUTE",e.REMOVE_CHATROOM_MEMBER="CHATROOM_REMOVE_MEMBER",e.MULTI_REMOVE_CHATROOM_MEMBER="CHATROOM_REMOVE_MEMBERS",e.ADD_USERS_TO_CHATROOM="CHATROOM_ADD_MEMBERS",e.JOIN_CHATROOM="CHATROOM_JOINCAHTROOM",e.QUIT_CHATROOM="CHATROOM_LEAVECAHTROOM",e.LIST_CHATROOM_MEMBERS="CHATROOM_FETCH_MEMBERS",e.GET_CHATROOM_ADMIN="CHATROOM_GET_ADMIN",e.SET_CHATROOM_ADMIN="CHATROOM_SET_ADMIN",e.REMOVE_CHATROOM_ADMIN="CHATROOM_REMOVE_ADMIN",e.MUTE_CHATROOM_MEMBER="CHATROOM_MUTE_USER",e.REMOVE_MUTE_CHATROOM_MEMBER="CHATROOM_UNMUTE_USER",e.GET_MUTE_CHATROOM_MEMBERS="CHATROOM_FETCH_MUTES",e.SET_CHATROOM_MEMBER_TO_BLACK="CHATROOM_BLOCK_USER",e.MULTI_SET_CHATROOM_MEMBER_TO_BLACK="CHATROOM_BLOCK_USERS",e.REMOVE_CHATROOM_MEMBER_BLACK="CHATROOM_UNBLOCK_USER",e.MULTI_REMOVE_CHATROOM_MEMBER_BLACK="CHATROOM_UNBLOCK_USERS",e.GET_CHATROOM_BLOCK_MEMBERS="CHATROOM_FETCH_BANS",e.DISABLED_CHATROOM_SEND_MSG="CHATROOM_MUTE_ALLMEMEBERS",e.ENABLE_CHATROOM_SEND_MSG="CHATROOM_UNMUTE_ALLMEMEBERS",e.ADD_CHATROOM_WHITE_USERS="CHATROOM_ADD_WHITELIST",e.REMOVE_CHATROOM_WHITE_USERS="CHATROOM_REMOVE_WHITELIST",e.GET_CHATROOM_WHITE_USERS="CHATROOM_FETCH_WHITELIST",e.CHECK_CHATROOM_WHITE_USER="CHATROOM_FETCH_MEMBERIN_WHITELIST",e.GET_CHATROOM_ANN="CHATROOM_FETCH_ANNOUNCEMENT",e.UPDATE_CHATROOM_ANN="CHATROOM_UPDATE_ANNOUNCEMENT",e.DELETE_CHATROOM_FILE="CHATROOM_REMOVE_SHARE_FILE",e.GET_CHATROOM_FILES="CHATROOM_GET_SHARE_FILE_LIST",e.UPLOAD_CHATROOM_FILE="CHATROOM_UPLOAD_FILE",e.SET_CHATROOM_ATTR="CHATROOM_SET_META_DATA",e.DELETE_CHATROOM_ATTR="CHATROOM_DELETE_META_DATA",e.GET_CHATROOM_ATTR="CHATROOM_FETCH_META_DATA",e.GET_USER_JOINED_CHATROOM="CHATROOM_FETCH_USER_JOINED_CHATROOM",e.CREATE_GROUP="GROUP_CREATEGROUP",e.BLOCK_GROUP="GROUP_BLOCK_MESSAGE",e.LIST_GROUP="GROUP_FETCH_PUBLICGROUPS_WITHCURSOR",e.GET_USER_GROUP="GROUP_FETCH_USERS_GROUP",e.CHANGE_OWNER="GROUP_CHANGE_OWNER",e.GET_GROUP_INFO="GROUP_FETCH_SPECIFICATION",e.MODIFY_GROUP="GROUP_CHANGE_GROUPATTRIBUTE",e.LIST_GROUP_MEMBER="GROUP_FETCH_MEMEBERS",e.GET_GROUP_ADMIN="GROUP_GET_ADMIN",e.SET_GROUP_ADMIN="GROUP_SET_ADMIN",e.REMOVE_GROUP_ADMIN="GROUP_REMOVE_ADMIN",e.DISSOLVE_GROUP="GROUP_DESTOTYGROUP",e.QUIT_GROUP="GROUP_LEAVEGROUP",e.INVITE_TO_GROUP="GROUP_INVITE_TO_GROUP",e.JOIN_GROUP="GROUP_JOIN_PUBLICGROUP",e.AGREE_JOIN_GROUP="GROUP_ACCEPT_JOINPUBLICGROUPAPPL",e.REJECT_JOIN_GROUP="GROUP_DECLINE_JOINPUBLICGROUPAPPL",e.AGREE_INVITE_GROUP="GROUP_ACCEPT_INVITATION",e.REJECT_INVITE_GROUP="GROUP_DECLINE_INVITATION",e.REMOVE_GROUP_MEMBER="GROUP_REMOVE_MEMBER",e.MULTI_REMOVE_GROUP_MEMBER="GROUP_REMOVE_MEMBERS",e.MUTE_GROUP_MEMBER="GROUP_MUTE_MEMBERS",e.UNMUTE_GROUP_MEMBER="GROUP_UNMUTE_MEMBERS",e.GET_GROUP_MUTE_LIST="GROUP_FETCH_MUTES",e.BLOCK_GROUP_MEMBER="GROUP_BLOCK_MEMBER",e.BLOCK_GROUP_MEMBERS="GROUP_BLOCK_MEMBERS",e.UNBLOCK_GROUP_MEMBER="GROUP_UNBLOCK_MEMBER",e.UNBLOCK_GROUP_MEMBERS="GROUP_UNBLOCK_MEMBERS",e.GET_GROUP_BLACK_LIST="GROUP_GET_BLOCK_LIST",e.DISABLED_SEND_GROUP_MSG="GROUP_MUTE_ALLMEMBERS",e.ENABLE_SEND_GROUP_MSG="GROUP_UNMUTE_ALLMEMBERS",e.ADD_USERS_TO_GROUP_WHITE="GROUP_ADD_WHITELIST",e.REMOVE_GROUP_WHITE_MEMBER="GROUP_REMOVE_WHITELIST",e.GET_GROUP_WHITE_LIST="GROUP_FETCH_WHITELIST",e.IS_IN_GROUP_WHITE_LIST="GROUP_IS_IN_WHITELIST",e.GET_GROUP_MSG_READ_USER="GROUP_GET_READ_USERS",e.GET_GROUP_ANN="GROUP_FETCH_ANNOUNCEMENT",e.UPDATE_GROUP_ANN="GROUP_UPDATE_ANNOUNCEMENT",e.UPLOAD_GROUP_FILE="GROUP_UPLOAD_SHAREDFILE",e.DELETE_GROUP_FILE="GROUP_DELETE_SHAREDFILE",e.GET_GROUP_FILE_LIST="GROUP_FETCH_SHAREDFILE",e.DOWN_GROUP_FILE="GROUP_DOWNLOAD_SHAREDFILE",e.SET_GROUP_MEMBER_ATTRS="GROUP_MEMBER_SET_META_DATA",e.GET_GROUP_MEMBER_ATTR="GROUP_MEMBER_FETCH_META_DATA",e.GET_SESSION_LIST="REST_GET_SESSION_LIST",e.REST_FETCH_CONVERSATIONS="REST_FETCH_CONVERSATIONS",e.DELETE_SESSION="REST_DEL_SESSION",e.GET_HISTORY_MSG="REST_GET_HISTORY_MESSAGE",e.PIN_CONVERSATION="REST_PIN_CONVERSATION",e.REST_UPLOAD_FILE_IN_PARTS="REST_UPLOAD_FILE_IN_PARTS",e.REST_DELETE_MESSAGES_CONVERSATIONS="REST_DELETE_MESSAGES_CONVERSATIONS",e.MARK_CONVERSATION="REST_MARK_CONVERSATION",e.REST_FETCH_PINMESSAGES="REST_FETCH_PINMESSAGES",e.REST_PIN_MESSAGE="REST_PIN_MESSAGE",e.UPDATE_USER_INFO="USER_UPDATE_USERINFO",e.GET_USER_INFO="USER_FETCH_USERINFO",e.UPDATE_USER_NICK="USER_UPDATE_NICK",e.UPLOAD_PUSH_TOKEN="USER_UPLOAD_PUSH_TOKEN",e.USER_LOGGEDIN_OTHER_PLATFORM="USER_LOGGEDIN_OTHER_PLATFORM",e.GET_BLACK_LIST="ROSTER_BLACKLIST",e.GET_CONTACTS="ROSTER_CONTACTS",e.ROSTER_GET_ALL_CONTACTS_REMARKS="ROSTER_GET_ALL_CONTACTS_REMARKS",e.ROSTER_GET_ALL_CONTACTS_REMARKS_FROM_SERVER_BY_PAGE="ROSTER_GET_ALL_CONTACTS_REMARKS_FROM_SERVER_BY_PAGE",e.ROSTER_SET_CONTACT_REMARK="ROSTER_SET_CONTACT_REMARK",e.ROSTER_ADD="ROSTER_ADD",e.ROSTER_REMOVE="ROSTER_REMOVE",e.ROSTER_ACCEPT="ROSTER_ACCEPT",e.ROSTER_DECLINE="ROSTER_DECLINE",e.ROSTER_BAN="ROSTER_BAN",e.ROSTER_ALLOW="ROSTER_ALLOW",e.SEND_MSG="MSYNC_SENDMESSAGE",e.UPLOAD_MSG_ATTACH="UPLOAD_MSG_ATTACH",e.SEND_RECALL_MSG="MSYNC_RECALLMESSAGE",e.MODIFY_MESSAGE="MSYNC_MODIFYMESSAGE",e.REST_FETCHHISTORYMESSAGE="REST_FETCHHISTORYMESSAGE",e.MSYNC_SYNCOFFLINEMESSAGE="MSYNC_SYNCOFFLINEMESSAGE"}(E||(E={})),function(e){e.GET_DNS="REST_DNSLIST",e.LOGIN_BY_AGORA_TOKEN="LOGIN_BY_AGORA_TOKEN",e.LOGIN_BY_PWD="LOGIN_BY_PWD",e.RESISTER="REGISTER",e.REST_INIT_UPLOAD_TASK_IN_PARTS="REST_INIT_UPLOAD_TASK_IN_PARTS",e.REST_UPLOAD_PART="REST_UPLOAD_PART",e.REST_COMPLETE_UPLOAD_PART="REST_COMPLETE_UPLOAD_PART",e.REST_ABORT_UPLOAD_PART="REST_ABORT_UPLOAD_PART",e.CONNECT_WEBSOCKET="CONNECT_WEBSOCKET"}(v||(v={})),function(e){e[e["5G"]=7]="5G",e[e["4G"]=7]="4G",e[e["3G"]=7]="3G",e[e["2G"]=7]="2G",e[e["SLOW-2G"]=7]="SLOW-2G",e[e.WIFI=2]="WIFI",e[e.LAN=1]="LAN",e[e.DISCONNECTED=0]="DISCONNECTED",e[e.NONE=0]="NONE",e[e.UNKNOWN=-1]="UNKNOWN",e[e["WEBIM UNABLE TO GET"]=-2]="WEBIM UNABLE TO GET"}(y||(y={})),function(e){e[e.success=200]="success",e[e.failed=500]="failed",e[e.disconnect=-1]="disconnect",e[e.closed=401]="closed",e[e.notFound=404]="notFound",e[e.reachLimit=429]="reachLimit"}(_||(_={})),function(e){e[e.web=0]="web",e[e.native=1]="native"}(T||(T={})),function(e){e[e.singleChat=0]="singleChat",e[e.groupChat=1]="groupChat",e[e.chatRoom=2]="chatRoom"}(I||(I={}));var C=new Map,N=new Map,S=1e3,A=9675,b=-1;function M(){console.log&&(console.log.apply?console.log.apply(console,Array.prototype.slice.call(arguments)):Function.prototype.apply.apply(console.log,[console,arguments])),console.trace&&console.trace()}var U,w="undefined"!=typeof window&&void 0!==window.navigator&&/Trident\/|MSIE /.test(window.navigator.userAgent),L=function(e,t,r,o){return new(r||(r=Promise))((function(n,i){function a(e){try{c(o.next(e))}catch(e){i(e)}}function s(e){try{c(o.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}c((o=o.apply(e,t||[])).next())}))},P=function(e,t){var r,o,n,i,a={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,o&&(n=2&i[0]?o.return:i[0]?o.throw||((n=o.return)&&n.call(o),0):o.next)&&!(n=n.call(o,i[1])).done)return n;switch(o=0,n&&(i=[2&i[0],n.value]),i[0]){case 0:case 1:n=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,o=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!((n=(n=a.trys).length>0&&n[n.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!n||i[1]>n[0]&&i[1]<n[3])){a.label=i[1];break}if(6===i[0]&&a.label<n[1]){a.label=n[1],n=i;break}if(n&&a.label<n[2]){a.label=n[2],a.ops.push(i);break}n[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],o=0}finally{r=n=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}},D=function(){},k={};!function(e){e[e.TRACE=0]="TRACE",e[e.DEBUG=1]="DEBUG",e[e.INFO=2]="INFO",e[e.WARN=3]="WARN",e[e.ERROR=4]="ERROR",e[e.SILENT=5]="SILENT"}(U||(U={}));var x=function(){function e(e,t,r){this.name=e||"defaultLogger",this.currentLevel=0,this.useCookiePersist=!1,this.storageLogLevelKey="loglevel",this.levels=U,this.consoleLogVisibility=!0,this.logMethods=["trace","debug","info","warn","error"],this.methodFactory=r||this.defaultMethodFactory,this.report=!1;var o=this._getPersistedLevel();null==o&&(o=null===t?"WARN":t),this.logs=[],this.reportLogs=[],this.reportInterval=3e5,this.timer=null,this.config={useCache:!1,maxCache:3145728,color:"",background:""},this.logBytes=0,this.setLevel(o,!1,"")}return e.prototype.reportLog=function(){var e,t,r;return L(this,void 0,void 0,(function(){var o,n,i,a,s,c,u,l;return P(this,(function(d){switch(d.label){case 0:if(0===(null===(e=this.reportLogs)||void 0===e?void 0:e.length))return[2];for(o=2097152,n=this.reportLogs.join("\n")+"\n",i=n.length,a=[];i>o;)a.push(n.substring(0,o)),i-=o,n=n.substring(o);a.push(n),s=0,c=a,d.label=1;case 1:if(!(s<c.length))return[3,8];if(u=c[s],this.reportLogs=[],!(null===(r=null===(t=this.connection)||void 0===t?void 0:t.context)||void 0===r?void 0:r.accessToken))return[2];d.label=2;case 2:return d.trys.push([2,4,,5]),[4,this.reportData(u)];case 3:return"ok"!==(null==(l=d.sent())?void 0:l.status)&&this.reportLogs.unshift(u),[3,5];case 4:return d.sent(),this.reportLogs.unshift(u),[3,5];case 5:return[4,_e.delay(3e3)];case 6:d.sent(),d.label=7;case 7:return s++,[3,1];case 8:return[2]}}))}))},e.prototype.reportData=function(e){var t,r=this;if(this.connection){var o=this.connection.context||{},n=o.orgName,i=o.appName,a=o.accessToken,s=o.userId;if(n&&i){var c={url:"".concat(null===(t=this.connection)||void 0===t?void 0:t.apiUrl,"/").concat(n,"/").concat(i,"/sdk/users/").concat(s,"/client/logs"),type:"POST",data:JSON.stringify({resource:this.connection.clientResource||"random_".concat(Date.now()),logContent:e}),dataType:"json",headers:{Authorization:"Bearer "+a,"Content-Type":"application/json"}};return ae.call(this.connection,c).then((function(e){return r.log("report log success",e),e})).catch((function(e){r.error("report log error",e)}))}}else this.error("report log error","connection is null")},e.prototype._regularlyReportLogs=function(){var e,t=this;this.timer&&clearInterval(this.timer),(null!==(e=this.reportInterval)&&void 0!==e?e:0)<6e4&&(this.reportInterval=6e4),this.timer=setInterval((function(){t.reportLog()}),this.reportInterval||3e5)},e.prototype._stopReportLogs=function(){return L(this,void 0,void 0,(function(){return P(this,(function(e){switch(e.label){case 0:return e.trys.push([0,2,,3]),[4,this.reportLog()];case 1:return e.sent(),[3,3];case 2:return e.sent(),this.error("report log error when stopping upload"),[3,3];case 3:return this.reportLogs=[],clearInterval(this.timer),[2]}}))}))},e.prototype.setConfig=function(e){this.config=e},e.prototype.getLevel=function(){return this.currentLevel},e.prototype.setLevel=function(e,t,r){if("string"==typeof e&&(e=U[e]),void 0===e&&(e=0),!("number"==typeof e&&e>=0&&e<=this.levels.SILENT))throw Error("log.setLevel() called with invalid level: "+e);if(this.currentLevel=e,!1!==t&&this._persistLevel(e),this.replaceLoggingMethods(e,r||""),"undefined"==typeof console&&e<U.SILENT)throw Error("No console available for logging")},e.prototype.setDefaultLevel=function(e){this._getPersistedLevel()||this.setLevel(e,!1,"")},e.prototype.enableAll=function(e){this.setLevel(this.levels.TRACE,!0,"")},e.prototype.disableAll=function(e){this.setLevel(this.levels.SILENT,!0,"")},e.prototype.getLogs=function(){return this.logs},e.prototype.download=function(){if("undefined"!=typeof window&&"undefined"!=typeof document){var e=this.getLogs().join("\n"),t=new Blob([e],{type:"text/plain;charset=UTF-8"}),r=window.URL.createObjectURL(t),o=document.createElement("a");o.style.display="none",o.href=r,o.setAttribute("download","sdklog"),document.body.appendChild(o),o.click()}},e.prototype.setConsoleLogVisibility=function(e){this.consoleLogVisibility=e},e.prototype._bindMethod=function(e,t,r){var o=this,n=e[t],i=this.getTime();if(r)return this._cacheLog;if("function"==typeof n.bind)return function(){for(var r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];var a=o.getTime();o.consoleLogVisibility&&n.call.apply(n,function(e,t,r){if(r||2===arguments.length)for(var o,n=0,i=t.length;n<i;n++)!o&&n in t||(o||(o=Array.prototype.slice.call(t,0,n)),o[n]=t[n]);return e.concat(o||Array.prototype.slice.call(t))}([e,"".concat(a," IM SDK [").concat("log"===t?"debug":t,"]: ")],r,!1)),o.onLog&&o.onLog({time:a,level:"log"===t?"debug":t,logs:r}),o._cacheReportLogs.apply(o,r)};try{return Function.prototype.bind.call(n,e,"".concat(i," IM SDK [").concat("log"===t?"debug":t,"]: "))}catch(t){return function(){return Function.prototype.apply.apply(n,[e,arguments])}}},e.prototype.getTime=function(){var e=new Date;return e.toTimeString().split(" ")[0]+":"+e.getMilliseconds()},e.prototype._cacheLog=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=(new Date).toLocaleString()+": ",o="";e.forEach((function(e){o+="object"==typeof e?JSON.stringify(e)+" ":e+" "})),this._cacheLogCall(r+o),this._cacheReportLogs.apply(this,e)},e.prototype._cacheLogCall=function(e){var t=e.length,r=this.logBytes+t,o=this.config.maxCache;if(!(t>=o)){if(r<o)this.logBytes+=t;else for(var n=r-o,i=0;i<n;){var a=this.logs.shift();void 0!==a&&(i+=a.length)}this.logs.push(e)}},e.prototype._cacheReportLogs=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];if(this.report){var r=(new Date).toLocaleString()+": ",o="";e.forEach((function(e){o+="object"==typeof e?JSON.stringify(e)+" ":e+" "})),this.reportLogs.push(r+o)}},e.prototype._getPersistedLevel=function(){var e;if("undefined"==typeof window)return 5;if("undefined"===(e=window&&window.localStorage&&window.localStorage[this.storageLogLevelKey])){var t=window.document.cookie,r=t.indexOf(encodeURIComponent(this.storageLogLevelKey));-1!==r&&(e=/^([^;]+)/.exec(t.slice(r))[1])}return e||5},e.prototype._persistLevel=function(e){var t=this.logMethods[e]||"SILENT";if("undefined"!=typeof window){if(window.localStorage)try{window.localStorage[this.storageLogLevelKey]=t}catch(e){console.log(e)}this.useCookiePersist&&(window.document.cookie=encodeURIComponent(this.storageLogLevelKey)+"="+t+";")}},e.prototype.replaceLoggingMethods=function(e,t){for(var r=this,o=0;o<this.logMethods.length;o++){var n=this.logMethods[o];this[n]=o<e?function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];r.report&&r._cacheReportLogs.apply(r,e)}:this.methodFactory(n,e,t)}this.log=this.debug},e.prototype.defaultMethodFactory=function(e,t,r){return this.realMethod(e)||this.enableLoggingWhenConsoleArrives.apply(this,[e,t,r])},e.prototype.realMethod=function(e){return"debug"===e&&(e="log"),"undefined"!=typeof console&&("trace"===e&&w?M:void 0!==console[e]?this._bindMethod(console,e,this.config.useCache):void 0!==console.log?this._bindMethod(console,"log",this.config.useCache):D)},e.prototype.enableLoggingWhenConsoleArrives=function(e,t,r){return function(){"undefined"!=typeof console&&(this.replaceLoggingMethods.call(this,t,r),this[e].apply(this,arguments))}.bind(this)},e}(),G=new x;G.getLogger=function(e){if("string"!=typeof e||""===e)throw new TypeError("You must supply a name when creating a logger.");return this};var B="undefined"!=typeof window?window.log:void 0;G.noConflict=function(){return"undefined"!=typeof window&&window.log===G&&(window.log=B),G},G.getLoggers=function(){return k},G.initReport=function(e){var t=e.report,r=e.reportInterval,o=e.connection;G.report=t,G.reportInterval=r,G.connection=o,t&&G._regularlyReportLogs()};var H=G,j=5242880,F=5242880,W={size:0},K={singleChat:"CHAT",groupChat:"GROUP",chatRoom:"ROOM"},q=15e3,V=3e5,z=3e5,J=[d.MAX_LIMIT,d.WEBIM_TOKEN_EXPIRED,d.WEBIM_CONNCTION_TOKEN_NOT_ASSIGN_ERROR,d.USER_NOT_FOUND,d.WEBIM_CONNCTION_AUTH_ERROR,d.REQUEST_PARAMETER_ERROR,d.WEBIM_CONNCTION_AUTH_ERROR,d.WEBIM_SERVER_SERVING_DISABLED],Y=function(){return Y=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},Y.apply(this,arguments)};function X(){var e=this.context,t=e.orgName,r=e.appName,o=e.accessToken,n={url:"".concat(this.apiUrl,"/").concat(t,"/").concat(r,"/sdk/chatfiles/part-upload"),dataType:"json",type:"POST",headers:{Authorization:"Bearer "+o}};return H.debug("Call multipartInit"),ae.call(this,n,E.SDK_INTERNAL).then((function(e){var t=e.entities[0];return{data:{fileMaxSize:t.file_upper_limit,partMinSize:t.part_lower_limit,uuid:t.uuid},extraInfo:e.extraInfo,type:e.type}}))}function Q(e){var t=this;return new Promise((function(r,o){var n,i,a=(new Date).getTime(),s=t.context,c=s.orgName,u=s.appName,l=s.accessToken,p=e.uuid,h=e.partNumber,f=e.part,m=e.onProgress,g="".concat(t.apiUrl,"/").concat(c,"/").concat(u,"/sdk/chatfiles/part-upload/").concat(p),E=new XMLHttpRequest;H.debug("Call multipartUpload"),E.upload&&(null===(i=(n=E.upload).addEventListener)||void 0===i||i.call(n,"progress",(function(e){null==m||m(e)}),!1)),E.addEventListener("abort",(function(){o({type:d.REQUEST_ABORT,message:"Request Abort",errorType:"onabort",xhr:E,extraInfo:{elapse:(new Date).getTime()-a,httpCode:E.status||-1,errDesc:"Request Abort",url:g}})}),!1),E.addEventListener("error",(function(){o({type:d.WEBIM_UPLOADFILE_ERROR,data:E,extraInfo:{elapse:(new Date).getTime()-a,httpCode:E.status||-1,errDesc:"request error",url:g}})}),!1),E.addEventListener("load",(function(){try{var e=JSON.parse(E.responseText);if(200!==E.status)return o({type:d.WEBIM_UPLOADFILE_ERROR,data:e,extraInfo:{elapse:(new Date).getTime()-a,httpCode:E.status||-1,errDesc:"part upload failed",url:g}}),!1;try{r(Y(Y({},e),{extraInfo:{elapse:(new Date).getTime()-a,httpCode:E.status||-1,url:g}}))}catch(e){o({type:d.WEBIM_CONNCTION_CALLBACK_INNER_ERROR,data:e,extraInfo:{elapse:(new Date).getTime()-a,httpCode:E.status||-1,errDesc:"part upload failed",url:g}})}}catch(e){o({type:d.WEBIM_UPLOADFILE_ERROR,data:E.responseText,extraInfo:{elapse:(new Date).getTime()-a,httpCode:E.status||-1,errDesc:"part upload failed",url:g}})}}),!1),E.addEventListener("timeout",(function(){o({type:d.REQUEST_TIMEOUT,message:"request timeout",extraInfo:{elapse:(new Date).getTime()-a,httpCode:E.status||-1,errDesc:"request timeout",url:g}})}),!1);var v=new FormData;v.append("part_file",f),v.append("part_number",h),E.timeout=V,E.open("PUT",g),E.setRequestHeader("restrict-access","true"),E.setRequestHeader("Accept","*/*"),E.setRequestHeader("Authorization","Bearer "+l),E.send(v)}))}function Z(e){var t=e.uuid,r=e.thumbnailHeight,o=e.thumbnailWidth,n=e.conversationInfo,i=n.conversationId,a=n.conversationType,s=K[a],c=this.context,u=c.orgName,l=c.appName,d=c.accessToken,p="".concat(this.apiUrl,"/").concat(u,"/").concat(l,"/sdk/chatfiles/part-upload/").concat(t,"?restrict-access=true&chat-type=").concat(s,"&chat-target=").concat(i);o&&(p+="&thumbnail-width=".concat(o)),r&&(p+="&thumbnail-height=".concat(r));var h={url:p,dataType:"json",type:"POST",headers:{Authorization:"Bearer "+d}};return H.debug("Call multipartComplete"),ae.call(this,h,E.SDK_INTERNAL)}function $(e){var t=e.uuid,r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/sdk/chatfiles/part-upload/").concat(t),dataType:"json",type:"DELETE",headers:{Authorization:"Bearer "+i}};return H.debug("Call multipartAbort"),ae.call(this,a,E.SDK_INTERNAL)}var ee=function(e,t,r,o){return new(r||(r=Promise))((function(n,i){function a(e){try{c(o.next(e))}catch(e){i(e)}}function s(e){try{c(o.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}c((o=o.apply(e,t||[])).next())}))},te=function(e,t){var r,o,n,i,a={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,o&&(n=2&i[0]?o.return:i[0]?o.throw||((n=o.return)&&n.call(o),0):o.next)&&!(n=n.call(o,i[1])).done)return n;switch(o=0,n&&(i=[2&i[0],n.value]),i[0]){case 0:case 1:n=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,o=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!((n=(n=a.trys).length>0&&n[n.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!n||i[1]>n[0]&&i[1]<n[3])){a.label=i[1];break}if(6===i[0]&&a.label<n[1]){a.label=n[1],n=i;break}if(n&&a.label<n[2]){a.label=n[2],a.ops.push(i);break}n[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],o=0}finally{r=n=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}},re=function(){function e(e,t){var r=this;this.handleUploadProgress=function(e,t){var o,n;if(e.total){r.progressArr[t]=e.loaded;var i=r.progressArr.reduce((function(e,t){return e+t}),0);null===(n=(o=r.options).onFileUploadProgress)||void 0===n||n.call(o,{isTrusted:e.isTrusted,type:e.type,loaded:i>r.file.size?r.file.size:i,total:r.file.size,lengthComputable:e.lengthComputable})}},this.uuid="",this.pool=[],this.progressArr=[],this.connection=e,this.options=t,this.partSize=j,this.file=t.file,this.init(),this.rpt=this.connection.dataReport.geOperateFun({operationName:E.REST_UPLOAD_FILE_IN_PARTS})}return e.prototype.init=function(){var e,t,r,o;return ee(this,void 0,void 0,(function(){var n,i,a,s,c,u,l,p,h,f,m,g,E,y,_;return te(this,(function(T){switch(T.label){case 0:return T.trys.push([0,4,,6]),[4,X.call(this.connection)];case 1:return n=T.sent(),i=n.data||{},a=i.fileMaxSize,s=void 0===a?0:a,c=i.partMinSize,u=void 0===c?j:c,l=i.uuid,p=void 0===l?"":l,h=n.extraInfo,g=h.elapse,E=h.httpCode,_=h.url,this.partSize=u,this.uuid=p,W.size=u,H.debug("multipartInit success","uuid: ".concat(p),"fileMaxSize: ".concat(s),"partMinSize: ".concat(u)),this.file.size>s?[4,this.multipartAbort()]:[3,3];case 2:return T.sent(),null===(t=(e=this.options).onFileUploadError)||void 0===t||t.call(e,{code:d.WEBIM_UPLOADFILE_ERROR,message:"The file size exceeds the maximum limit"}),[2];case 3:return this.rpt({data:{requestUrl:_,requestName:v.REST_INIT_UPLOAD_TASK_IN_PARTS,requestElapse:g,requestMethod:"POST",isSuccess:1,code:E}}),this.upload(),[3,6];case 4:return f=T.sent(),m=(null==f?void 0:f.extraInfo)||{},g=m.elapse,E=m.httpCode,y=m.errDesc,_=m.url,this.rpt({data:{requestUrl:_,requestName:v.REST_INIT_UPLOAD_TASK_IN_PARTS,requestElapse:g,requestMethod:"POST",isSuccess:0,codeDesc:y,code:E}}),[4,this.multipartAbort()];case 5:return T.sent(),null===(o=null===(r=this.options)||void 0===r?void 0:r.onInitFail)||void 0===o||o.call(r),[3,6];case 6:return[2]}}))}))},e.prototype.upload=function(){var e,t,r;return ee(this,void 0,void 0,(function(){var o,n,i,a,s,c,u=this;return te(this,(function(l){switch(l.label){case 0:(new FileReader).readAsArrayBuffer(this.file),o=this.file.size,n=Math.ceil(o/this.partSize),l.label=1;case 1:l.trys.push([1,7,,9]),i=function(t){var r,n,i,s;return te(this,(function(c){switch(c.label){case 0:return r=t*a.partSize,n=Math.min(o,r+a.partSize),i=null===(e=a.file)||void 0===e?void 0:e.slice(r,n),(s=Q.call(a.connection,{uuid:a.uuid,partNumber:"".concat(t+1),part:i,onProgress:function(e){u.handleUploadProgress(e,t)}})).then((function(e){var t=(null==e?void 0:e.extraInfo)||{},r=t.elapse,o=t.httpCode,n=t.url;u.rpt({data:{requestUrl:n,requestName:v.REST_UPLOAD_PART,requestElapse:r,isSuccess:1,requestMethod:"PUT",code:o}}),u.handleTask(s)})),s.catch((function(e){var t=(null==e?void 0:e.extraInfo)||{},r=t.elapse,o=t.httpCode,n=t.url,i=t.errDesc;u.rpt({data:{requestUrl:n,requestName:v.REST_UPLOAD_PART,requestElapse:r,isSuccess:0,requestMethod:"PUT",code:o,codeDesc:i}}),delete e.extraInfo})),a.pool.push(s),4!==a.pool.length?[3,2]:[4,Promise.race(a.pool)];case 1:c.sent(),c.label=2;case 2:return[2]}}))},a=this,s=0,l.label=2;case 2:return s<n?[5,i(s)]:[3,5];case 3:l.sent(),l.label=4;case 4:return s++,[3,2];case 5:return[4,Promise.all(this.pool)];case 6:return l.sent(),this.multipartComplete(),[3,9];case 7:return c=l.sent(),[4,this.multipartAbort()];case 8:return l.sent(),this.rpt({data:{isLastApi:1,isSuccess:0}}),null===(r=(t=this.options).onFileUploadError)||void 0===r||r.call(t,c),[3,9];case 9:return[2]}}))}))},e.prototype.multipartComplete=function(){var e,t,r,o;return ee(this,void 0,void 0,(function(){var n,i,a,s,c,u,l,d,p,h,f;return te(this,(function(m){switch(m.label){case 0:return m.trys.push([0,2,,3]),n=this.options.thumbnailInfo||{},i=n.width,a=n.height,[4,Z.call(this.connection,{uuid:this.uuid,thumbnailHeight:a,thumbnailWidth:i,conversationInfo:this.options.conversationInfo})];case 1:return s=m.sent(),c=(null==s?void 0:s.extraInfo)||{},d=c.elapse,p=c.httpCode,f=c.url,this.rpt({data:{requestUrl:f,requestName:v.REST_COMPLETE_UPLOAD_PART,requestElapse:d,requestMethod:"POST",isSuccess:1,code:p}}),this.rpt({data:{isLastApi:1,isSuccess:1}}),null===(t=(e=this.options).onFileUploadComplete)||void 0===t||t.call(e,s),[3,3];case 2:return u=m.sent(),l=(null==u?void 0:u.extraInfo)||{},d=l.elapse,p=l.httpCode,h=l.errDesc,f=l.url,this.rpt({data:{requestUrl:f,requestName:v.REST_COMPLETE_UPLOAD_PART,requestElapse:d,requestMethod:"POST",isSuccess:0,codeDesc:h,code:p}}),this.rpt({data:{isLastApi:1,isSuccess:0}}),null===(o=(r=this.options).onFileUploadError)||void 0===o||o.call(r,u),[3,3];case 3:return[2]}}))}))},e.prototype.multipartAbort=function(){return ee(this,void 0,void 0,(function(){var e,t,r,o,n,i,a,s;return te(this,(function(c){switch(c.label){case 0:if(!this.uuid)return[2];c.label=1;case 1:return c.trys.push([1,3,,4]),[4,$.call(this.connection,{uuid:this.uuid})];case 2:return e=c.sent(),t=(null==e?void 0:e.extraInfo)||{},n=t.elapse,i=t.httpCode,s=t.url,this.rpt({data:{requestUrl:s,requestName:v.REST_ABORT_UPLOAD_PART,requestElapse:n,requestMethod:"DELETE",isSuccess:1,code:i}}),[3,4];case 3:return r=c.sent(),o=(null==r?void 0:r.extraInfo)||{},n=o.elapse,i=o.httpCode,a=o.errDesc,s=o.url,this.rpt({data:{requestUrl:s,requestName:v.REST_ABORT_UPLOAD_PART,requestElapse:n,requestMethod:"DELETE",isSuccess:0,codeDesc:a,code:i}}),[3,4];case 4:return[2]}}))}))},e.prototype.handleTask=function(e){var t=this.pool.findIndex((function(t){return t===e}));this.pool.splice(t,1)},e}(),oe=function(e){var t,r,o=this,n=(new Date).getTime(),i=e.apiUrl,a=e.orgName,s=e.appName,c=e.operateName,u=e.accessToken,l=e.conversationInfo,p=l.conversationType,h=l.conversationId,f=K[p],m=e.uploadUrl||"".concat(i,"/").concat(a,"/").concat(s,"/chatfiles?chat-type=").concat(f,"&chat-target=").concat(h),g=function(t){var r=(new Date).getTime()-n;o.dataReport&&c&&[E.UPLOAD_MSG_ATTACH,E.UPLOAD_CHATROOM_FILE,E.UPLOAD_GROUP_FILE].includes(c)&&o.dataReport.geOperateFun({operationName:c})({isEndApi:!0,data:{isSuccess:0,requestMethod:"POST",requestName:c,requestElapse:r,requestUrl:m,code:(null==y?void 0:y.status)||0,codeDesc:"upload file error"}}),e.onFileUploadError&&e.onFileUploadError(t)};function v(e){g({type:d.WEBIM_UPLOADFILE_ERROR,data:y})}var y=new XMLHttpRequest;y.upload&&(null===(r=(t=y.upload).addEventListener)||void 0===r||r.call(t,"progress",e.onFileUploadProgress||ie,!1)),y.addEventListener("abort",e.onFileUploadCanceled||ie,!1),y.addEventListener("error",v,!1),y.addEventListener("load",(function(t){try{var r=JSON.parse(y.responseText);if(400===y.status)return g({type:d.WEBIM_UPLOADFILE_ERROR,data:r}),!1;try{!function(t){var r=(new Date).getTime()-n;o.dataReport&&c&&[E.UPLOAD_MSG_ATTACH,E.UPLOAD_CHATROOM_FILE,E.UPLOAD_GROUP_FILE].includes(c)&&o.dataReport.geOperateFun({operationName:c})({isEndApi:!0,data:{isSuccess:(null==t?void 0:t.error)?0:1,requestMethod:"POST",requestName:c,requestElapse:r,requestUrl:m,code:y.status,codeDesc:(null==t?void 0:t.error_description)||""}}),e.onFileUploadComplete&&e.onFileUploadComplete(t)}(r)}catch(t){g({type:d.WEBIM_CONNCTION_CALLBACK_INNER_ERROR,data:t})}}catch(t){g({type:d.WEBIM_UPLOADFILE_ERROR,data:y.responseText})}}),!1),y.addEventListener("timeout",v,!1),y.timeout=V,y.open("POST",m),y.setRequestHeader("restrict-access","true"),y.setRequestHeader("Accept","*/*"),y.setRequestHeader("Authorization","Bearer "+u);var _=new FormData;_.append("file",e.file.data),e.thumbnailWidth&&_.append("thumbnail-width",e.thumbnailWidth+""),e.thumbnailHeight&&_.append("thumbnail-height",e.thumbnailHeight+""),y.send(_)},ne=function(){return ne=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},ne.apply(this,arguments)},ie=function(){};function ae(e,t){var r,o=this;return he().platform===le.WEB?new Promise((function(t,o){var n=e.dataType||"text",i=e.success||ie,a=e.error||ie,s=new XMLHttpRequest;s.timeout=q;var c=!1;s.ontimeout=function(){H.warn("request timeout"),c=!0;var e={type:d.REQUEST_TIMEOUT,message:"Request Timeout",errorType:"timeout_error",xhr:s};a(e),o(e)},s.onerror=function(){o({type:d.REQUEST_UNKNOWN,message:"Request Unknow Error",errorType:"onerror",xhr:s})},s.onabort=function(){o({type:d.REQUEST_ABORT,message:"Request Abort",errorType:"onabort",xhr:s})},s.onreadystatechange=function(){if(4===s.readyState){var e=(new Date).getTime()-r,u=s.status||0,l={elapse:e,httpCode:u};if(200===u){_e.ajaxUnconventionalErrorTimes=0;try{switch(n){case"text":return i(s.responseText),void t(s.responseText);case"json":var p=JSON.parse(s.responseText);return p.extraInfo=l,i(p),void t(p);case"xml":return s.responseXML&&s.responseXML.documentElement?(i(s.responseXML.documentElement),void t(s.responseXML.documentElement)):(a({type:d.WEBIM_CONNCTION_AJAX_ERROR,data:s.responseText,message:"XHR.responseXML is null or XHR.responseXML.documentElement is null"}),void o({type:d.WEBIM_CONNCTION_AJAX_ERROR,data:s.responseText,message:"XHR.responseXML is null or XHR.responseXML.documentElement is null"}));default:a({type:d.WEBIM_CONNCTION_AJAX_ERROR,data:s.responseText,message:"Invalid dataType"}),o({type:d.WEBIM_CONNCTION_AJAX_ERROR,data:s.responseText,message:"Invalid dataType"})}return t(s.response||s.responseText),void i(s.response||s.responseText,s)}catch(e){o(e)}return}0===u?setTimeout((function(){H.debug("request timeout:",c),!c&&R(s,o,a,e)}),0):([400,401,403,404,429,500,503].includes(u)||(H.debug("rest api request fail status:",u),_e.ajaxUnconventionalErrorTimes++),R(s,o,a,e))}0===s.readyState&&(a({type:d.WEBIM_CONNCTION_AJAX_ERROR,data:s.responseText,message:"Request not initialized"}),o({type:d.WEBIM_CONNCTION_AJAX_ERROR,data:s.responseText,message:"Request not initialized"}))},e.responseType&&s.responseType&&(s.responseType=e.responseType),e.mimeType&&s.overrideMimeType(e.mimeType);var u=e.type||"POST",l=e.data||null,p="";if("get"===u.toLowerCase()&&l){for(var h in l)l.hasOwnProperty(h)&&(p+=h+"="+l[h]+"&");p=p?p.slice(0,-1):p,e.url+=(e.url.indexOf("?")>0?"&":"?")+(p?p+"&":p)+"_v="+(new Date).getTime(),l=null,p=""}r=(new Date).getTime(),s.open(u,e.url);var f=e.headers||{};for(var m in f["Content-Type"]||(f["Content-Type"]="application/json"),f)f.hasOwnProperty(m)&&s.setRequestHeader(m,f[m]);s.send(l)})).then((function(r){var n,i;if(o.dataReport&&t&&t!==E.SDK_INTERNAL){var a=o.dataReport.geOperateFun({operationName:t}),s=ne({isSuccess:1,requestUrl:e.url,requestName:t,requestMethod:e.type},Ee(r.extraInfo));t===E.REST_FETCH_CONVERSATIONS?s.resultCount=null===(n=r.data.channel_infos)||void 0===n?void 0:n.length:t===E.REST_FETCHHISTORYMESSAGE&&(s.resultCount=null===(i=r.data.msgs)||void 0===i?void 0:i.length),a({isEndApi:!0,data:s})}return t===E.SDK_INTERNAL&&(r.extraInfo.url=e.url),t!==E.SDK_INTERNAL&&delete r.extraInfo,"Object"===se(r)?ne(ne({},r),{type:d.REQUEST_SUCCESS}):{data:r,type:d.REQUEST_SUCCESS}})).catch((function(n){var i,a;if(o.dataReport&&t&&t!==E.SDK_INTERNAL&&o.dataReport.geOperateFun({operationName:t})({isEndApi:!0,data:ne({isSuccess:0,requestUrl:e.url,requestName:t,requestMethod:e.type},Ee(n.extraInfo))}),t===E.SDK_INTERNAL)if(n.extraInfo)n.extraInfo.url=e.url;else{var s={elapse:(new Date).getTime()-r,httpCode:null!==(a=null===(i=n.xhr)||void 0===i?void 0:i.status)&&void 0!==a?a:0,url:e.url};n.extraInfo=s}throw t!==E.SDK_INTERNAL&&delete n.extraInfo,n})):ce.call(this,e,t)}function se(e){return Object.prototype.toString.call(e).slice(8,-1)}function ce(e,t){var r=this;return new Promise((function(r,o){var n=e.success||ie,i=e.error||ie,a=e.type||"POST",s=e.data||null,c="",u=(new Date).getTime(),l=_e.getEnvInfo();if("get"===a.toLowerCase()&&s){for(var p in s)s.hasOwnProperty(p)&&(c+=p+"="+s[p]+"&");c=c?c.slice(0,-1):c,e.url+=(e.url.indexOf("?")>0?"&":"?")+(c?c+"&":c)+"_v="+(new Date).getTime(),s=null,c=""}var h={url:e.url,data:e.data,method:a,headers:{},timeout:q,success:function(e){var a,s,c,l,d,p={elapse:(new Date).getTime()-u,httpCode:Number((null===(a=e.statusCode)||void 0===a?void 0:a.toString())||(null===(s=e.status)||void 0===s?void 0:s.toString())),errDesc:(null===(c=null==e?void 0:e.data)||void 0===c?void 0:c.error_description)||""};if("200"===(null===(l=e.statusCode)||void 0===l?void 0:l.toString())||"200"===(null===(d=e.status)||void 0===d?void 0:d.toString())){e.data.extraInfo=p;var h=e.data;n(h),r(h)}else e.extraInfo=p,i(h=e),o(h),H.debug(t,"reject reason: ",h)},complete:function(){},fail:function(e){var r={elapse:(new Date).getTime()-u,httpCode:b,errDesc:"request:fail"};if(e.extraInfo=r,e.data={error:"request:fail",error_description:"request:fail"},"request:fail timeout"===e.errMsg)return o({type:d.REQUEST_TIMEOUT,message:"Request Timeout",extraInfo:r}),void i({type:d.REQUEST_TIMEOUT,message:"Request Timeout",extraInfo:r});i(e),o(e),H.error(t,"fail reason:",e)}};if("zfb"===l.platform||"dd"===l.platform?h.headers=e.headers:h.header=e.headers,"dd"===l.platform)return dd.httpRequest(h);l.global.request(h)})).then((function(o){var n,i;if(r.dataReport&&t&&t!==E.SDK_INTERNAL){var a=r.dataReport.geOperateFun({operationName:t}),s=ne({isSuccess:1,requestUrl:e.url,requestName:t,requestMethod:e.type},Ee(o.extraInfo));t===E.REST_FETCH_CONVERSATIONS?s.resultCount=null===(n=o.data.channel_infos)||void 0===n?void 0:n.length:t===E.REST_FETCHHISTORYMESSAGE&&(s.resultCount=null===(i=o.data.msgs)||void 0===i?void 0:i.length),a({isEndApi:!0,data:s})}return t!==E.SDK_INTERNAL&&delete o.extraInfo,"Object"===se(o)?ne(ne({},o),{type:d.REQUEST_SUCCESS}):{data:o,type:d.REQUEST_SUCCESS}})).catch((function(o){r.dataReport&&t&&t!==E.SDK_INTERNAL&&r.dataReport.geOperateFun({operationName:t})({isEndApi:!0,data:ne({isSuccess:0,requestUrl:e.url,requestName:t,requestMethod:e.type},Ee(o.extraInfo))}),t!==E.SDK_INTERNAL&&delete o.extraInfo;var n=JSON.stringify(o.data||{}),i=ne(ne({},o),{status:o.statusCode||o.status||0,response:n,responseText:n});R(i,(function(e){throw ne(ne({},o),{message:e.message,type:e.type})}),ie,0)}))}function ue(e,t){var r;return function(){for(var o=[],n=0;n<arguments.length;n++)o[n]=arguments[n];if(!e)return r;r=t?e.apply(t,o):e.apply(void 0,o),e=null}}var le,de=ue((function(e,t){var r=he();if(r.platform!==le.WEB){var o=r.global,n=function(r){r.isConnected?e():t()};o.offNetworkStatusChange&&o.offNetworkStatusChange(n),o.onNetworkStatusChange&&o.onNetworkStatusChange(n)}else"undefined"!=typeof addEventListener&&(window.addEventListener("online",e),window.addEventListener("offline",t))})),pe=ue((function(e,t){var r,o,n=he();if(n.platform===le.WEB)document&&document.addEventListener("visibilitychange",(function(){document.hidden?null==t||t():null==e||e()}),!1);else{var i=n.global;i.onAppShow&&(null===(r=i.onAppShow)||void 0===r||r.call(i,e)),i.onAppHide&&(null===(o=i.onAppHide)||void 0===o||o.call(i,t))}}));function he(){return"undefined"!=typeof swan&&fe(swan)?{platform:le.BAIDU,global:swan}:"undefined"!=typeof tt&&fe(tt)?{platform:le.TT,global:tt}:"undefined"!=typeof dd&&fe(dd)?{platform:le.DD,global:dd}:"undefined"!=typeof my&&fe(my)?{platform:le.ZFB,global:my}:"undefined"!=typeof wx&&fe(wx)||"undefined"!=typeof wx&&"function"==typeof wx.createCanvas?{platform:le.WX,global:wx}:"undefined"!=typeof qq&&fe(qq)?{platform:le.QQ,global:qq}:"undefined"!=typeof uni&&fe(uni)?{platform:le.UNI,global:uni}:"undefined"!=typeof window&&window.WebSocket?{platform:le.WEB,global:window}:{platform:le.NODE,global:r.g||{}}}function fe(e){for(var t=["canIUse","getSystemInfo"],r=0,o=t.length;r<o;r++)if(!e[t[r]])return!1;return!0}function me(e,t){var r,o,n,i=this,a=e.accessToken,s=e.appKey,c=null===(o=null===(r=null==e?void 0:e.file)||void 0===r?void 0:r.data)||void 0===o?void 0:o.size,u=[],l="",p="";if(a)if(s&&(u=s.split("#"),l=u[0],p=u[1]),l||p)if(c<=0)e.onFileUploadError&&e.onFileUploadError({type:d.WEBIM_UPLOADFILE_ERROR,message:"fileSize must be greater than 0"});else if(e.uploadUrl)oe.call(this,ne(ne({},e),{orgName:l,appName:p,operateName:t,conversationInfo:{conversationId:e.to,conversationType:e.chatType}}));else{var h=W.size||F;this.uploadPartEnable&&c>1.5*h?new re(this,{file:null===(n=null==e?void 0:e.file)||void 0===n?void 0:n.data,onFileUploadProgress:e.onFileUploadProgress||ie,onFileUploadComplete:e.onFileUploadComplete||ie,onFileUploadError:e.onFileUploadError||ie,onFileUploadCanceled:e.onFileUploadCanceled||ie,conversationInfo:{conversationId:e.to,conversationType:e.chatType},onInitFail:function(){oe.call(i,ne(ne({},e),{orgName:l,appName:p,operateName:t,conversationInfo:{conversationId:e.to,conversationType:e.chatType}}))},thumbnailInfo:{width:e.thumbnailWidth,height:e.thumbnailHeight}}):oe.call(this,ne(ne({},e),{orgName:l,appName:p,operateName:t,conversationInfo:{conversationId:e.to,conversationType:e.chatType}}))}else e.onFileUploadError&&e.onFileUploadError({type:d.WEBIM_UPLOADFILE_ERROR,message:this._initWithAppId?"appId illegal":"AppKey illegal"});else e.onFileUploadError&&e.onFileUploadError({type:d.WEBIM_UPLOADFILE_NO_LOGIN,message:"AccessToken cannot be empty"})}function ge(e,t){var r;e.onFileDownloadComplete=e.onFileDownloadComplete||ie,e.onFileDownloadError=e.onFileDownloadError||ie;var o=(new Date).getTime(),n=new XMLHttpRequest,i=this;n.addEventListener("load",(function(){var r,a=(new Date).getTime()-o;i.dataReport&&t&&t===E.DOWN_GROUP_FILE&&i.dataReport.geOperateFun({operationName:t})({isEndApi:!0,data:{isSuccess:200===n.status?1:0,requestMethod:e.method||"GET",requestName:t,requestElapse:a,requestUrl:null==e?void 0:e.url,code:n.status,codeDesc:200===n.status?"":"download file error"}}),200===n.status?e.onFileDownloadComplete&&e.onFileDownloadComplete(n.response):403===n.status||404===n.status?((r=new FileReader).addEventListener("loadend",(function(t){var r;try{var o=JSON.parse(null===(r=t.target)||void 0===r?void 0:r.result);H.error("download file failed","status:".concat(n.status),o),403===n.status?"chatfile no permission"===o.error_description?e.onFileDownloadError&&e.onFileDownloadError({type:d.WEBIM_DOWNLOADFILE_ERROR_NO_PERMISSION,id:e.id,xhr:n}):e.onFileDownloadError&&e.onFileDownloadError({type:d.PERMISSION_DENIED,id:e.id,xhr:n}):404===n.status&&("file_expired"===o.error?e.onFileDownloadError&&e.onFileDownloadError({type:d.WEBIM_DOWNLOADFILE_ERROR_EXPIRED,id:e.id,xhr:n}):e.onFileDownloadError&&e.onFileDownloadError({type:d.WEBIM_DOWNLOADFILE_ERROR,id:e.id,xhr:n}))}catch(t){H.error("Error parsing download error response:",t),e.onFileDownloadError&&e.onFileDownloadError({type:d.WEBIM_DOWNLOADFILE_ERROR,id:e.id,xhr:n})}})),r.readAsText(n.response)):500===n.status?(H.error("download file failed","status:".concat(n.status),"type:".concat(d.SERVER_UNKNOWN_ERROR)),e.onFileDownloadError&&e.onFileDownloadError({type:d.SERVER_UNKNOWN_ERROR,id:e.id,xhr:n})):(H.error("download file failed","status:".concat(n.status),"type:".concat(d.WEBIM_DOWNLOADFILE_ERROR)),e.onFileDownloadError&&e.onFileDownloadError({type:d.WEBIM_DOWNLOADFILE_ERROR,id:e.id,xhr:n}))}),!1),n.addEventListener("error",(function(r){var a=(new Date).getTime()-o;i.dataReport&&t&&t===E.DOWN_GROUP_FILE&&i.dataReport.geOperateFun({operationName:t})({isEndApi:!0,data:{isSuccess:0,requestMethod:e.method||"GET",requestName:t,requestElapse:a,requestUrl:null==e?void 0:e.url,code:(null==n?void 0:n.status)||0,codeDesc:"download file error"}}),e.onFileDownloadError&&e.onFileDownloadError({type:d.WEBIM_DOWNLOADFILE_ERROR,id:e.id,xhr:n})}),!1);var a=e.method||"GET",s=e.responseType||"blob",c=e.mimeType||"text/plain; charset=x-user-defined";n.open(a,e.url),"undefined"!=typeof Blob?n.responseType=s:n.overrideMimeType(c);var u={"X-Requested-With":"XMLHttpRequest","share-secret":e.secret,Authorization:"Bearer "+(null===(r=null==this?void 0:this.context)||void 0===r?void 0:r.accessToken)},l=e.headers||{};for(var p in l)u[p]=l[p];for(var p in u)u[p]&&n.setRequestHeader(p,u[p]);n.send(null)}function Ee(e){void 0===e&&(e={});var t=e.elapse,r=void 0===t?0:t,o=e.httpCode,n=void 0===o?0:o,i=e.errDesc;return{requestElapse:r,code:n,codeDesc:void 0===i?"":i}}!function(e){e.WEB="web",e.WX="wx",e.QQ="qq",e.ZFB="zfb",e.DD="dd",e.TT="tt",e.BAIDU="baidu",e.QUICK_APP="quick_app",e.UNI="uni",e.NODE="node"}(le||(le={}));var ve,ye="localDeviceInfo",_e={autoIncrement:0,ajaxUnconventionalErrorTimes:0,isUseHttps:function(e){var t;return"boolean"==typeof e?e:!("undefined"!=typeof window)||"https:"===(null===(t=window.location)||void 0===t?void 0:t.protocol)},getRetryDelay:function(e){return e<=5?Number((Math.random()+1).toFixed(2)):e>5&&e<20?Math.floor(4*Math.random())+3:Math.floor(6*Math.random())+5},ajax:ae,getUniqueId:function(){this.autoIncrement?this.autoIncrement++:this.autoIncrement=1;var e=new Date,t=new Date(2010,1,1);return(e.getTime()-t.getTime()+this.autoIncrement).toString()},getFileUrl:function(e){var t={url:"",filename:"",filetype:"",data:{}},r="string"==typeof e?document.getElementById(e):e;if(window.URL.createObjectURL){if(!r.files)throw Error("this is not HTMLInputElement");var o=r.files;if(o.length>0){var n=o.item(0);t.data=n,t.url=window.URL.createObjectURL(n),t.filename=(null==n?void 0:n.name)||""}}else{if("string"!=typeof e)throw Error("in IE fileInputId must be string");n=document.getElementById(e).value,t.url=n;var i=n.lastIndexOf("/"),a=n.lastIndexOf("\\"),s=Math.max(i,a);t.filename=s<0?n:n.substring(s+1)}var c=t.filename.lastIndexOf(".");return-1!==c&&(t.filetype=t.filename.substring(c+1).toLowerCase()),t},uploadFile:me,flow:function(e){for(var t=e.length,r=t;r--;)if("function"!=typeof e[r])throw new TypeError("Expected a function");return function(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];for(var n=0,i=t?e[n].apply(this,r):r[0];++n<t;)i=e[n].call(this,i);return i}},listenNetwork:de,listenBrowserVisibility:pe,getEnvInfo:he,wxRequest:ce,parseDownloadResponse:function(e){if(!window||!window.URL)throw Error("parseDownloadResponse can be used in broswer only");return e&&e.type&&"application/json"===e.type||0>Object.prototype.toString.call(e).indexOf("Blob")?this.url+"?token=":window.URL.createObjectURL(e)},download:ge,parseNotify:function(e){for(var t="",r=0;r<e.length;r++)t+="%"+e[r].toString(16);return JSON.parse(decodeURIComponent(t))},getExtraData:Ee,retryPromise:function(e,t,r){return new Promise((function(o,n){var i=function(t){e().then(o).catch((function(e){t>0?setTimeout((function(){i(t-1)}),r||1e3):n(e)}))};i(t||3)}))},formatAttachUrl:function(e){return e&&"string"==typeof e?"".concat(this.apiUrl).concat(e.slice(e.indexOf("/",9))):""},Uint8ArrayToString:function(e){for(var t="",r=0;r<e.length;r++)t+=String.fromCharCode(e[r]);return t},getLocalDeviceInfo:function(){return function(e){var t,r=_e.getEnvInfo(),o=r.platform,n="";o!==le.NODE&&o!==le.QUICK_APP||(n="");try{o===le.WEB?n=localStorage.getItem(e)||"":o===le.WX||o===le.QQ||o===le.TT||o===le.BAIDU||o===le.UNI?n=r.global.getStorageSync(e)||"":o!==le.ZFB&&o!==le.DD||(n=(null===(t=r.global.getStorageSync({key:e}))||void 0===t?void 0:t.data)||"")}catch(t){H.debug("get local ".concat(e," failed: "),t)}return H.debug("".concat(e," "),n),n}(ye)},setLocalDeviceInfo:function(e){!function(e,t){var r=_e.getEnvInfo(),o=r.platform;if(o!==le.NODE&&o!==le.QUICK_APP)if(o===le.WEB)try{localStorage.setItem(e,t)}catch(t){H.error("set local ".concat(e," failed: "),t)}else r.global.setStorage({key:e,data:t,success:function(t){H.debug("set local ".concat(e," success: "),t)},fail:function(t){H.error("set local ".concat(e," failed: "),t)}})}(ye,e)},detectBrowser:function(){if("undefined"==typeof navigator)return"unknown";var e=navigator.userAgent;return/MicroMessenger/i.test(e)?"WeChat":/QQBrowser/i.test(e)?"QQ":!/Chrome/i.test(e)||/Edg/i.test(e)||/OPR/i.test(e)?!/Safari/i.test(e)||/Chrome/i.test(e)||/CriOS/i.test(e)?/Firefox/i.test(e)?"Firefox":/MSIE/i.test(e)||/Trident/i.test(e)?"IE":/Edg/i.test(e)?"Edge":"unknown":"Safari":"Chrome"},getDevicePlatform:function(e){return e.platform===le.WX&&"undefined"!=typeof uni&&fe(uni)?le.UNI:e.platform},delay:function(e){return new Promise((function(t){return setTimeout(t,e)}))}};!function(e){e[e.SYNC_INIT=0]="SYNC_INIT",e[e.SYNC_START=1]="SYNC_START",e[e.SYNC_FINISH=2]="SYNC_FINISH"}(ve||(ve={}));var Te=r(887),Ie=r.n(Te),Oe=function(){return Oe=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},Oe.apply(this,arguments)},Re={chat:"singleChat",chatroom:"chatRoom",groupchat:"groupChat",singleChat:"singleChat",chatRoom:"chatRoom",groupChat:"groupChat"};function Ce(e,t){var r,o,n,i=t||{},a=i.formatCustomExts,s=void 0===a||a,c=i.formatChatType,u=void 0!==c&&c,l=e.id,d=e.payload,p=e.timestamp,h=e.to,f=d.bodies&&d.bodies.length>0?d.bodies[0]:{},m={},g={},E=d.type?d.type:h.indexOf("@conference.easemob.com")>-1?"groupChat":"singleChat";E="chat"===E?"singleChat":E,u&&d.type&&(E=Re[d.type]);var v="";switch(f.type){case"txt":Object.prototype.hasOwnProperty.call(f,"subType")&&"sub_combine"===f.subType?(v=Ye.call(this,{remotePath:null==f?void 0:f.url,secret:null==f?void 0:f.secret}),m={id:l,type:"combine",chatType:E,to:d.to,from:d.from,ext:d.ext,time:p,title:f.title||"",summary:f.summary||"",url:v||"",secret:f.secret||"",file_length:f.file_length||0,filename:f.filename||"",compatibleText:f.msg,combineLevel:f.combineLevel||0}):m={id:l,type:"txt",chatType:E,msg:f.msg||"",to:d.to||"",from:d.from,time:p,ext:d.ext};break;case"img":v=this.useOwnUploadFun?null==f?void 0:f.url:Ye.call(this,{remotePath:null==f?void 0:f.url,secret:null==f?void 0:f.secret}),m={id:l,type:"img",chatType:E,to:d.to,from:d.from,time:p,ext:d.ext,width:(null===(r=f.size)||void 0===r?void 0:r.width)||0,height:(null===(o=f.size)||void 0===o?void 0:o.height)||0,thumb:this.useOwnUploadFun?"":"".concat(v,"&thumbnail=true"),thumb_secret:f.secret,secret:f.secret||"",url:v||"",file_length:f.file_length||0,file:{},isGif:"sub_gif"===f.subType};break;case"video":v=this.useOwnUploadFun?null==f?void 0:f.url:Ye.call(this,{remotePath:null==f?void 0:f.url,secret:null==f?void 0:f.secret}),m={id:l,type:"video",chatType:E,from:d.from,to:d.to,thumb:_e.formatAttachUrl.call(this,f.thumb),thumb_secret:f.thumb_secret||"",url:v||"",secret:f.secret||"",filename:f.filename,length:f.length||0,file:{},file_length:f.file_length||0,filetype:d.ext.file_type||"",ext:d.ext,time:p};break;case"loc":m={id:l,type:"loc",chatType:E,from:d.from,to:d.to,buildingName:f.buildingName||"",addr:f.addr,lat:f.lat,lng:f.lng,ext:d.ext,time:p};break;case"audio":v=this.useOwnUploadFun?null==f?void 0:f.url:Ye.call(this,{remotePath:null==f?void 0:f.url,secret:null==f?void 0:f.secret}),m={id:l,type:"audio",chatType:E,from:d.from,to:d.to,secret:f.secret||"",ext:d.ext,time:p,url:v||"",file:{},filename:f.filename,length:f.length||0,file_length:f.file_length||0,filetype:d.ext.file_type||""};break;case"file":v=this.useOwnUploadFun?null==f?void 0:f.url:Ye.call(this,{remotePath:null==f?void 0:f.url,secret:null==f?void 0:f.secret}),m={id:l,type:"file",chatType:E,from:d.from,to:d.to,ext:d.ext,time:p,url:v||"",secret:f.secret||"",file:{},filename:f.filename||"",file_length:f.file_length||0,filetype:d.ext.file_type||""};break;case"cmd":m={id:l,type:"cmd",chatType:E,from:d.from,to:d.to,ext:d.ext,time:p,action:f.action||""};break;case"custom":var y=f.customExts||{};s&&f.customExts&&(y={},f.customExts.map((function(e){y=Oe(Oe({},y),e)}))),m={id:l,type:"custom",chatType:E,from:d.from,to:d.to,ext:d.ext,time:p,customEvent:f.customEvent||"",customExts:y};break;case"combine":v=Ye.call(this,{remotePath:null==f?void 0:f.url,secret:null==f?void 0:f.secret}),m={id:l,type:"combine",chatType:E,msg:f.msg||"",to:d.to||"",from:d.from,time:p,ext:d.ext,title:f.title||"",summary:f.summary||"",url:v||"",compatibleText:f.text,combineLevel:f.combineLevel||0,secret:f.secret||"",filename:f.filename||"",file_length:f.file_length||0};break;default:H.error("unexpected message: ".concat(e))}if(d.msgConfig&&(g.msgConfig=d.msgConfig),null==d?void 0:d.meta){var _=d.meta;_.thread&&(g.chatThread={messageId:_.thread.msg_parent_id,parentId:_.thread.muc_parent_id,chatThreadName:_.thread.thread_name}),_.reaction&&(g.reactions=_.reaction),_.translations&&(g.translations=_.translations)}var T=null===(n=null==d?void 0:d.meta)||void 0===n?void 0:n.edit_msg;if(T){var I=T.edit_time,O=T.operator,R=T.count;m.modifiedInfo={operationTime:I,operatorId:O,operationCount:R}}return Oe(Oe({},m),g)}var Ne,Se=function(e){return"".concat(e.conversationType,"-").concat(e.conversationId)},Ae=function(e){var t=e.isRecallSelfMsg,r=e.conversation,o=e.recalledMsgTime,n=r.unReadCount,i=void 0===n?0:n,a=r.unreadCountClearTimestamp;return t||(void 0===a?0:a)>o?i:i&&i>0?i-1:0},be=function(e){var t,r=0;if(0===e.length)return r;for(t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t),r|=0;return r},Me=function(){function e(e){var t=e.id;this.id=t,this.type=e.type}return e.prototype.set=function(e){this.body={id:this.id,ackId:e.id,type:"read",to:e.to,from:e.from||"",chatType:e.chatType}},e.create=function(e){return{id:_e.getUniqueId(),chatType:e.chatType,ackId:e.id,type:"read",to:e.to,from:e.from||"",ackContent:e.ackContent,isChatThread:e.isChatThread,priority:e.priority,deliverOnlineOnly:e.deliverOnlineOnly}},e}(),Ue=function(){function e(e){this.id=e.id,this.type=e.type}return e.prototype.set=function(e){this.body={id:this.id,ackId:e.ackId,type:"delivery",to:e.to,from:e.from||""}},e.create=function(e){return{id:_e.getUniqueId(),ackId:e.ackId,type:"delivery",to:e.to,from:e.from||"",isChatThread:e.isChatThread,priority:e.priority,deliverOnlineOnly:e.deliverOnlineOnly}},e}(),we=function(){function e(e){var t=e.type,r=e.id;this.id=r,this.type=t}return e.prototype.set=function(e){this.body={id:this.id,chatType:e.chatType||"singleChat",type:"channel",to:e.to,from:e.from||"",time:Date.now()}},e.prototype.setChatType=function(e){return!!this.body&&(this.body.chatType=e,!0)},e.prototype.setGroup=function(e){return!!this.body&&(this.body.group=e,!0)},e.create=function(e){return{id:_e.getUniqueId(),type:"channel",chatType:e.chatType||"singleChat",to:e.to,from:e.from||"",time:Date.now(),isChatThread:e.isChatThread,priority:e.priority,deliverOnlineOnly:e.deliverOnlineOnly}},e}(),Le=function(){function e(e){var t=e.type,r=e.id||_e.getUniqueId();this.id=r,this.type=t,this.value=""}return e.prototype.set=function(e){this.body={id:this.id,chatType:e.chatType||"singleChat",type:"txt",to:e.to,msg:e.msg,from:e.from,roomType:e.roomType,success:e.success,fail:e.fail,ext:e.ext,time:Date.now(),isChatThread:e.isChatThread},this.value=e.msg},e.prototype.setChatType=function(e){return!!this.body&&(this.body.chatType=e,!0)},e.prototype.setGroup=function(e){return!!this.body&&(this.body.group=e,!0)},e.create=function(e){var t,r;return(null===(t=e.msgConfig)||void 0===t?void 0:t.languages)&&Array.isArray(null===(r=e.msgConfig)||void 0===r?void 0:r.languages),{type:"txt",id:_e.getUniqueId(),msg:e.msg,to:e.to,from:e.from||"",chatType:e.chatType,ext:e.ext,time:Date.now(),msgConfig:e.msgConfig,isChatThread:e.isChatThread,priority:e.priority,deliverOnlineOnly:e.deliverOnlineOnly,receiverList:e.receiverList}},e}(),Pe=function(){function e(e){var t=e.type,r=e.id;this.id=r||_e.getUniqueId(),this.type=t}return e.prototype.set=function(e){this.body={id:this.id,chatType:e.chatType||"singleChat",type:"cmd",to:e.to,action:e.action,from:e.from,roomType:e.roomType,success:e.success,fail:e.fail,ext:e.ext,time:Date.now()}},e.prototype.setChatType=function(e){return!!this.body&&(this.body.chatType=e,!0)},e.prototype.setGroup=function(e){return!!this.body&&(this.body.group=e,!0)},e.create=function(e){return{id:_e.getUniqueId(),type:"cmd",to:e.to,from:e.from||"",chatType:e.chatType||"singleChat",action:e.action,time:Date.now(),ext:e.ext,msgConfig:e.msgConfig,isChatThread:e.isChatThread,priority:e.priority,deliverOnlineOnly:e.deliverOnlineOnly,receiverList:e.receiverList}},e}(),De=function(){function e(e){var t=e.type,r=e.id||_e.getUniqueId();this.id=r,this.type=t}return e.prototype.set=function(e){this.body={id:this.id,chatType:e.chatType||"singleChat",type:"custom",to:e.to,customEvent:e.customEvent,customExts:e.customExts,from:e.from,roomType:e.roomType,success:e.success,fail:e.fail,ext:e.ext,time:Date.now()}},e.prototype.setChatType=function(e){return!!this.body&&(this.body.chatType=e,!0)},e.prototype.setGroup=function(e){return!!this.body&&(this.body.group=e,!0)},e.create=function(e){return{id:_e.getUniqueId(),chatType:e.chatType||"singleChat",type:"custom",to:e.to,customEvent:e.customEvent,customExts:e.customExts,from:e.from||"",ext:e.ext,time:Date.now(),msgConfig:e.msgConfig,isChatThread:e.isChatThread,priority:e.priority,deliverOnlineOnly:e.deliverOnlineOnly,receiverList:e.receiverList}},e}(),ke=function(){function e(e){var t=e.type,r=e.id;this.id=r||_e.getUniqueId(),this.type=t}return e.prototype.set=function(e){this.body={id:this.id,chatType:e.chatType||"singleChat",type:"loc",to:e.to,addr:e.addr,buildingName:e.buildingName,lat:e.lat,lng:e.lng,from:e.from,roomType:e.roomType,success:e.success,fail:e.fail,ext:e.ext,time:Date.now()}},e.prototype.setChatType=function(e){return!!this.body&&(this.body.chatType=e,!0)},e.prototype.setGroup=function(e){return!!this.body&&(this.body.group=e,!0)},e.create=function(e){return{id:_e.getUniqueId(),chatType:e.chatType||"singleChat",type:"loc",to:e.to,addr:e.addr,buildingName:e.buildingName,lat:e.lat,lng:e.lng,from:e.from||"",ext:e.ext,time:Date.now(),msgConfig:e.msgConfig,isChatThread:e.isChatThread,priority:e.priority,deliverOnlineOnly:e.deliverOnlineOnly,receiverList:e.receiverList}},e}(),xe=function(){function e(e){var t=e.type,r=e.id||_e.getUniqueId();this.id=r,this.type=t}return e.prototype.set=function(e){e.file=e.file||e.fileInputId&&_e.getFileUrl(e.fileInputId),this.body={id:this.id,chatType:e.chatType||"singleChat",type:"img",file:e.file,width:e.width,height:e.height,to:e.to,from:e.from||"",roomType:e.roomType,success:e.success,fail:e.fail,ext:e.ext,time:Date.now(),onFileUploadError:e.onFileUploadError,onFileUploadComplete:e.onFileUploadComplete,onFileUploadProgress:e.onFileUploadProgress,body:e.body,isChatThread:e.isChatThread}},e.prototype.setChatType=function(e){return!!this.body&&(this.body.chatType=e,!0)},e.prototype.setGroup=function(e){return!!this.body&&(this.body.group=e,!0)},e.create=function(e){return{id:_e.getUniqueId(),chatType:e.chatType,type:"img",url:e.url,width:e.width,height:e.height,file:e.file,to:e.to,from:e.from||"",ext:e.ext,time:Date.now(),msgConfig:e.msgConfig,file_length:e.file_length,onFileUploadError:e.onFileUploadError,onFileUploadComplete:e.onFileUploadComplete,onFileUploadProgress:e.onFileUploadProgress,isChatThread:e.isChatThread,priority:e.priority,deliverOnlineOnly:e.deliverOnlineOnly,receiverList:e.receiverList,thumbnailWidth:e.thumbnailWidth,thumbnailHeight:e.thumbnailHeight,isGif:e.isGif}},e}(),Ge=function(){function e(e){var t=e.type,r=e.id||_e.getUniqueId();this.id=r,this.type=t}return e.prototype.set=function(e){e.file=e.file||e.fileInputId&&_e.getFileUrl(e.fileInputId),this.body={id:this.id,chatType:e.chatType||"singleChat",type:"audio",file:e.file,filename:e.filename,length:e.length,file_length:e.file_length,fileInputId:e.fileInputId,to:e.to,from:e.from,roomType:e.roomType,success:e.success,fail:e.fail,ext:e.ext,time:Date.now(),onFileUploadError:e.onFileUploadError,onFileUploadComplete:e.onFileUploadComplete,onFileUploadProgress:e.onFileUploadProgress,body:e.body,isChatThread:e.isChatThread}},e.prototype.setChatType=function(e){return!!this.body&&(this.body.chatType=e,!0)},e.prototype.setGroup=function(e){return!!this.body&&(this.body.group=e,!0)},e.create=function(e){return{id:_e.getUniqueId(),chatType:e.chatType,type:"audio",filename:e.filename,length:e.length,file:e.file,to:e.to,from:e.from||"",ext:e.ext,time:Date.now(),onFileUploadError:e.onFileUploadError,onFileUploadComplete:e.onFileUploadComplete,onFileUploadProgress:e.onFileUploadProgress,body:e.body,file_length:e.file_length,msgConfig:e.msgConfig,isChatThread:e.isChatThread,priority:e.priority,deliverOnlineOnly:e.deliverOnlineOnly,receiverList:e.receiverList}},e}(),Be=function(){function e(e){var t=e.type,r=e.id;this.id=r,this.type=t}return e.prototype.set=function(e){e.file=e.file||e.fileInputId&&_e.getFileUrl(e.fileInputId),this.body={id:this.id,chatType:e.chatType||"singleChat",type:"video",file:e.file,filename:e.filename,length:e.length,file_length:e.file_length,fileInputId:e.fileInputId,to:e.to,from:e.from,roomType:e.roomType,success:e.success,fail:e.fail,ext:e.ext,time:Date.now(),apiUrl:e.apiUrl,onFileUploadError:e.onFileUploadError,onFileUploadComplete:e.onFileUploadComplete,onFileUploadProgress:e.onFileUploadProgress,body:e.body,isChatThread:e.isChatThread}},e.prototype.setChatType=function(e){return!!this.body&&(this.body.chatType=e,!0)},e.prototype.setGroup=function(e){return!!this.body&&(this.body.group=e,!0)},e.create=function(e){return{id:_e.getUniqueId(),chatType:e.chatType||"singleChat",type:"video",file:e.file,filename:e.filename,length:e.length,file_length:e.file_length,fileInputId:e.fileInputId,to:e.to,from:e.from||"",ext:e.ext,time:Date.now(),onFileUploadError:e.onFileUploadError,onFileUploadComplete:e.onFileUploadComplete,onFileUploadProgress:e.onFileUploadProgress,body:e.body,msgConfig:e.msgConfig,isChatThread:e.isChatThread,priority:e.priority,deliverOnlineOnly:e.deliverOnlineOnly,receiverList:e.receiverList}},e}(),He=function(){function e(e){var t=e.type,r=e.id;this.id=r,this.type=t}return e.prototype.set=function(e){e.file=e.file||e.fileInputId&&_e.getFileUrl(e.fileInputId),this.body={id:this.id,chatType:e.chatType||"singleChat",type:"file",file:e.file,filename:e.filename,fileInputId:e.fileInputId,to:e.to,from:e.from,roomType:e.roomType,success:e.success,fail:e.fail,ext:e.ext,time:Date.now(),onFileUploadError:e.onFileUploadError,onFileUploadComplete:e.onFileUploadComplete,onFileUploadProgress:e.onFileUploadProgress,body:e.body,isChatThread:e.isChatThread}},e.prototype.setChatType=function(e){return!!this.body&&(this.body.chatType=e,!0)},e.prototype.setGroup=function(e){return!!this.body&&(this.body.group=e,!0)},e.create=function(e){return{id:_e.getUniqueId(),chatType:e.chatType||"singleChat",type:"file",file:e.file,filename:e.filename,fileInputId:e.fileInputId,file_length:e.file_length,to:e.to,from:e.from||"",ext:e.ext,onFileUploadError:e.onFileUploadError,onFileUploadComplete:e.onFileUploadComplete,onFileUploadProgress:e.onFileUploadProgress,body:e.body,time:Date.now(),msgConfig:e.msgConfig,isChatThread:e.isChatThread,priority:e.priority,deliverOnlineOnly:e.deliverOnlineOnly,receiverList:e.receiverList}},e}(),je=function(){function e(e){var t=e.type,r=e.id||_e.getUniqueId();this.id=r,this.type=t,this.value=""}return e.create=function(e){return{type:"combine",id:_e.getUniqueId(),to:e.to,from:e.from||"",chatType:e.chatType,ext:e.ext,time:Date.now(),msgConfig:e.msgConfig,isChatThread:e.isChatThread,priority:e.priority,deliverOnlineOnly:e.deliverOnlineOnly,receiverList:e.receiverList,compatibleText:e.compatibleText,title:e.title,summary:e.summary,messageList:e.messageList,onFileUploadError:e.onFileUploadError,onFileUploadComplete:e.onFileUploadComplete}},e}(),Fe=function(){function e(t,r){return this.type=t,this.id=r||_e.getUniqueId(),e.createOldMsg({type:t,id:this.id})}return e.createOldMsg=function(e){switch(e.type){case"read":return new Me({type:"read",id:e.id});case"delivery":return new Ue({type:"delivery",id:e.id});case"channel":return new we({type:"channel",id:e.id});case"txt":return new Le({type:"txt",id:e.id});case"cmd":return new Pe({type:"cmd",id:e.id});case"custom":return new De({type:"custom",id:e.id});case"loc":return new ke({type:"loc",id:e.id});case"img":return new xe({type:"img",id:e.id});case"audio":return new Ge({type:"audio",id:e.id});case"video":return new Be({type:"video",id:e.id});case"file":return new He({type:"file",id:e.id})}},e.create=function(e){return"txt"!==(t=e).type||"version"in t?function(e){return"img"===e.type&&!("version"in e)}(e)?xe.create(e):function(e){return"cmd"===e.type&&!("version"in e)}(e)?Pe.create(e):function(e){return"file"===e.type&&!("version"in e)}(e)?He.create(e):function(e){return"audio"===e.type&&!("version"in e)}(e)?Ge.create(e):function(e){return"video"===e.type&&!("version"in e)}(e)?Be.create(e):function(e){return"custom"===e.type&&!("version"in e)}(e)?De.create(e):function(e){return"loc"===e.type&&!("version"in e)}(e)?ke.create(e):function(e){return"channel"===e.type&&!("version"in e)}(e)?we.create(e):function(e){return"delivery"===e.type&&!("version"in e)}(e)?Ue.create(e):function(e){return"read"===e.type&&!("version"in e)}(e)?Me.create(e):function(e){return"combine"===e.type&&!("version"in e)}(e)?je.create(e):{}:Le.create(e);var t},e.prototype.set=function(e){},e.getFileUrl=_e.getFileUrl,e.download=_e.download,e.parseDownloadResponse=_e.parseDownloadResponse,e}();!function(e){e[e.CREATE=0]="CREATE",e[e.FAIL=1]="FAIL",e[e.INPROGRESS=2]="INPROGRESS",e[e.SUCCESS=3]="SUCCESS"}(Ne||(Ne={}));var We,Ke=function(){return Ke=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},Ke.apply(this,arguments)},qe=function(e,t,r,o){return new(r||(r=Promise))((function(n,i){function a(e){try{c(o.next(e))}catch(e){i(e)}}function s(e){try{c(o.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}c((o=o.apply(e,t||[])).next())}))},Ve=function(e,t){var r,o,n,i,a={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,o&&(n=2&i[0]?o.return:i[0]?o.throw||((n=o.return)&&n.call(o),0):o.next)&&!(n=n.call(o,i[1])).done)return n;switch(o=0,n&&(i=[2&i[0],n.value]),i[0]){case 0:case 1:n=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,o=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!((n=(n=a.trys).length>0&&n[n.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!n||i[1]>n[0]&&i[1]<n[3])){a.label=i[1];break}if(6===i[0]&&a.label<n[1]){a.label=n[1],n=i;break}if(n&&a.label<n[2]){a.label=n[2],a.ops.push(i);break}n[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],o=0}finally{r=n=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}};!function(e){e["chat:user"]="singleChat",e["chat:group"]="groupChat",e["chat:room"]="chatRoom"}(We||(We={}));var ze={0:"TEXT",1:"IMAGE",2:"VIDEO",3:"LOCATION",4:"VOICE",5:"FILE",6:"COMMAND",7:"CUSTOM",8:"COMBINE"};function Je(e){for(var t={},r=0;r<e.length;r++)if(8===e[r].type)t[e[r].key]=JSON.parse(e[r].stringValue);else if(7===e[r].type)t[e[r].key]=e[r].stringValue;else if(6===e[r].type)t[e[r].key]=e[r].doubleValue;else if(5===e[r].type)t[e[r].key]=e[r].floatValue;else if(1===e[r].type){var o=e[r].varintValue,n=new(f())(o.low,o.high,o.unsigned).toString();t[e[r].key]=0!==Number(n)}else 2!==e[r].type&&3!==e[r].type&&4!==e[r].type||(o=e[r].varintValue,n=new(f())(o.low,o.high,o.unsigned).toString(),t[e[r].key]=Number(n));return t}function Ye(e){var t="";return e.remotePath&&(t=_e.formatAttachUrl.call(this,e.remotePath),e.remotePath.includes("?em-redirect")||(t="".concat(t,"?em-redirect=true"),e.secretKey&&(t="".concat(t,"&share-secret=").concat(e.secretKey)))),t}function Xe(e,t){var r,o,n=t.from&&t.from.name;if(n===this.context.userId){var i=null===(r=null==e?void 0:e.from)||void 0===r?void 0:r.clientResource;n===(null===(o=null==t?void 0:t.to)||void 0===o?void 0:o.name)&&i&&i!==this.clientResource&&(n="".concat(n,"/").concat(i))}return n}function Qe(e,t){var r,o=t.to&&t.to.name;return(null===(r=null==e?void 0:e.to)||void 0===r?void 0:r.clientResource)&&(o="".concat(o,"/").concat(e.to.clientResource)),o}function Ze(e){var t,r,o,n,i,a,s,c,u,l,d,p,h,f,m,g,E,v,y,_,T,I,O,R,C,N,S;return qe(this,void 0,void 0,(function(){var A,b,M,U,w,L,P,D,k,x,G,B,j,F,W,K,q,V,z,J,Y,X,Q,Z,$,ee,te,re,oe,ne,ie,ae,se,ce,ue,le,de,pe,he,fe,me,ge,Ee,ve,ye,Te,Ie,Oe,Re,Se;return Ve(this,(function(Ae){switch(Ae.label){case 0:switch(A=e.status,b=e.thirdMessage,M=e.msgBody,U=e.msgId,w=e.type,L=e.from,P=e.to,D=e.time,k=e.onlineState,x=e.chatType,G=e.ignoreCallback,B=e.priority,j=e.format,F=e.broadcast,W=void 0!==F&&F,K=e.isContentReplaced,q=void 0!==K&&K,V={},z={},J=A.errorCode>0,Y=A.errorCode,X=A.reason,Q={},Z=[],$=[],ee={},te=null,re=null,ne=!1,ie=!1,b.ext&&(Q=Je(b.ext)),b.meta&&"string"==typeof b.meta&&((ae=JSON.parse(b.meta)).reaction&&(Z=ae.reaction).forEach((function(e){e.isAddedBySelf=e.state,delete e.state})),ae.translations&&($=ae.translations),ae.edit_msg&&(se=ae.edit_msg,ce=se.count,ue=se.operator,le=se.edit_time,ee={operationTime:le,operatorId:ue,operationCount:ce}),ae.thread&&"{}"!==JSON.stringify(ae.thread)&&(te={messageId:ae.thread.msg_parent_id,parentId:ae.thread.muc_parent_id,chatThreadName:ae.thread.thread_name}),ae.thread_overview&&"{}"!==JSON.stringify(ae.thread_overview)&&(re={id:ae.thread_overview.id,parentId:ae.thread_overview.muc_parent_id,name:ae.thread_overview.name,lastMessage:ae.thread_overview.last_message&&"{}"!==JSON.stringify(ae.thread_overview.last_message)?Ce.call(this,ae.thread_overview.last_message):null,createTimestamp:ae.thread_overview.create_timestamp,updateTimestamp:ae.thread_overview.update_timestamp,messageCount:ae.thread_overview.message_count||0}),ae.isDelivered&&(ne=!0),ae.isRead&&(ie=!0)),M.type){case 0:return[3,1];case 1:return[3,7];case 2:return[3,10];case 3:return[3,13];case 4:return[3,16];case 5:return[3,19];case 6:return[3,22];case 7:return[3,23];case 8:return[3,26]}return[3,27];case 1:return Object.prototype.hasOwnProperty.call(M,"subType")&&0===M.subType?(oe=Ye.call(this,M),de={id:U,type:"combine",chatType:x,to:P,from:L,ext:Q,time:Number(D),onlineState:k,title:M.title||"",summary:M.summary||"",url:oe||"",secret:M.secretKey||"",file_length:M.fileLength||0,filename:M.displayName||"",compatibleText:M.text,combineLevel:M.combineLevel||0},V.msgConfig&&(de.msgConfig=V.msgConfig),Z.length>0&&(de.reactions=Z),te&&(de.chatThread=te),re&&(de.chatThreadOverview=re),"chatRoom"===x&&(de.priority=B,de.broadcast=W),ee.operationCount>0&&(de.modifiedInfo=ee),q&&(de.isContentReplaced=q),ie&&(de.isRead=!0),ne&&(de.isDelivered=!0),z=de,G?[3,3]:[4,null===(r=null===(t=this._localCache)||void 0===t?void 0:t.getInstance())||void 0===r?void 0:r.storeMessage(de,Ne.SUCCESS)]):[3,4];case 2:Ae.sent(),null===(o=this.eventHandler)||void 0===o||o.dispatch("onCombineMessage",de),Ae.label=3;case 3:return[3,28];case 4:return!(V={id:U,type:w,contentsType:ze[M.type],from:L,to:P,data:M.text,ext:Q,sourceMsg:M.text,time:D,msgConfig:b.msgConfig,onlineState:k}).msgConfig&&delete b.msgConfig,V.error=J,V.errorText=X,V.errorCode=Y,!G&&this.onTextMessage&&this.onTextMessage(V),pe={id:U,type:"txt",chatType:x,msg:M.text,to:P,from:L,ext:Q,time:Number(D),onlineState:k},V.msgConfig&&(pe.msgConfig=V.msgConfig),Z.length>0&&(pe.reactions=Z),te&&(pe.chatThread=te),re&&(pe.chatThreadOverview=re),$.length>0&&(pe.translations=$),ee.operationCount>0&&(pe.modifiedInfo=ee),"chatRoom"===x&&(pe.priority=B,pe.broadcast=W),q&&(pe.isContentReplaced=q),ie&&(pe.isRead=!0),ne&&(pe.isDelivered=!0),z=pe,G?[3,6]:[4,null===(i=null===(n=this._localCache)||void 0===n?void 0:n.getInstance())||void 0===i?void 0:i.storeMessage(pe,Ne.SUCCESS)];case 5:Ae.sent(),null===(a=this.eventHandler)||void 0===a||a.dispatch("onTextMessage",pe),Ae.label=6;case 6:return[3,28];case 7:return he=(null===(s=null==M?void 0:M.size)||void 0===s?void 0:s.width)||0,fe=(null===(c=null==M?void 0:M.size)||void 0===c?void 0:c.height)||0,oe=this.useOwnUploadFun?M.remotePath:Ye.call(this,M),!(V={id:U,type:w,contentsType:ze[M.type],from:L,to:P,url:oe,secret:M.secretKey,filename:M.displayName,thumb:this.useOwnUploadFun?"":"".concat(oe,"&thumbnail=true"),thumb_secret:M.secretKey,file_length:M.fileLength||"",width:he,height:fe,filetype:M.filetype||"",accessToken:this.token,ext:Q,time:D,msgConfig:b.msgConfig,onlineState:k}).delay&&delete V.delay,!V.msgConfig&&delete b.msgConfig,V.error=J,V.errorText=X,V.errorCode=Y,!G&&this.onPictureMessage&&this.onPictureMessage(V),me={id:U,type:"img",chatType:x,from:L,to:P,url:oe||"",width:he,height:fe,secret:M.secretKey||"",thumb:this.useOwnUploadFun?"":"".concat(oe,"&thumbnail=true"),thumb_secret:M.secretKey,file_length:M.fileLength||0,ext:Q,time:Number(D),onlineState:k},V.msgConfig&&(me.msgConfig=V.msgConfig),Z.length>0&&(me.reactions=Z),te&&(me.chatThread=te),re&&(me.chatThreadOverview=re),"chatRoom"===x&&(me.priority=B,me.broadcast=W),ee.operationCount>0&&(me.modifiedInfo=ee),q&&(me.isContentReplaced=q),ie&&(me.isRead=!0),ne&&(me.isDelivered=!0),Object.prototype.hasOwnProperty.call(M,"subType")&&1===M.subType&&(me.isGif=!0),z=me,G?[3,9]:[4,null===(l=null===(u=this._localCache)||void 0===u?void 0:u.getInstance())||void 0===l?void 0:l.storeMessage(me,Ne.SUCCESS)];case 8:Ae.sent(),null===(d=this.eventHandler)||void 0===d||d.dispatch("onImageMessage",me),Ae.label=9;case 9:return[3,28];case 10:return oe=this.useOwnUploadFun?M.remotePath:Ye.call(this,M),!(V={id:U,type:w,contentsType:ze[M.type],from:L,to:P,url:oe,secret:M.secretKey,filename:M.displayName,length:M.duration||"",file_length:M.fileLength||"",filetype:M.filetype||"",accessToken:this.token||"",ext:Q,time:D,msgConfig:b.msgConfig,onlineState:k}).delay&&delete V.delay,!V.msgConfig&&delete b.msgConfig,V.error=J,V.errorText=X,V.errorCode=Y,!G&&this.onVideoMessage&&this.onVideoMessage(V),ge={id:U,type:"video",chatType:x,from:L,to:P,url:oe,secret:M.secretKey,thumb:_e.formatAttachUrl.call(this,M.thumbnailRemotePath),thumb_secret:M.thumbnailSecretKey,filename:M.displayName,length:M.duration||0,file:{},file_length:M.fileLength||0,filetype:M.filetype||"",accessToken:this.token||"",ext:Q,time:Number(D),onlineState:k},V.msgConfig&&(ge.msgConfig=V.msgConfig),Z.length>0&&(ge.reactions=Z),te&&(ge.chatThread=te),re&&(ge.chatThreadOverview=re),"chatRoom"===x&&(ge.priority=B,ge.broadcast=W),ee.operationCount>0&&(ge.modifiedInfo=ee),q&&(ge.isContentReplaced=q),ie&&(ge.isRead=!0),ne&&(ge.isDelivered=!0),z=ge,G?[3,12]:[4,null===(h=null===(p=this._localCache)||void 0===p?void 0:p.getInstance())||void 0===h?void 0:h.storeMessage(ge,Ne.SUCCESS)];case 11:Ae.sent(),null===(f=this.eventHandler)||void 0===f||f.dispatch("onVideoMessage",ge),Ae.label=12;case 12:return[3,28];case 13:return!(V={id:U,type:w,contentsType:ze[M.type],from:L,to:P,addr:M.address,buildingName:M.buildingName,lat:M.latitude,lng:M.longitude,ext:Q,time:D,msgConfig:b.msgConfig,onlineState:k}).delay&&delete V.delay,!V.msgConfig&&delete b.msgConfig,V.error=J,V.errorText=X,V.errorCode=Y,!G&&this.onLocationMessage&&this.onLocationMessage(V),Ee={id:U,type:"loc",chatType:x,from:L,to:P,buildingName:M.buildingName,addr:M.address,lat:M.latitude,lng:M.longitude,ext:Q,time:Number(D),onlineState:k},V.msgConfig&&(Ee.msgConfig=V.msgConfig),Z.length>0&&(Ee.reactions=Z),te&&(Ee.chatThread=te),re&&(Ee.chatThreadOverview=re),"chatRoom"===x&&(Ee.priority=B,Ee.broadcast=W),ee.operationCount>0&&(Ee.modifiedInfo=ee),q&&(Ee.isContentReplaced=q),ie&&(Ee.isRead=!0),ne&&(Ee.isDelivered=!0),z=Ee,G?[3,15]:[4,null===(g=null===(m=this._localCache)||void 0===m?void 0:m.getInstance())||void 0===g?void 0:g.storeMessage(Ee,Ne.SUCCESS)];case 14:Ae.sent(),null===(E=this.eventHandler)||void 0===E||E.dispatch("onLocationMessage",Ee),Ae.label=15;case 15:return[3,28];case 16:return oe=this.useOwnUploadFun?M.remotePath:Ye.call(this,M),!(V={id:U,type:w,contentsType:ze[M.type],from:L,to:P,url:oe,secret:M.secretKey,filename:M.displayName,file_length:M.fileLength||"",accessToken:this.token||"",ext:Q,length:M.duration,time:D,msgConfig:b.msgConfig,onlineState:k}).delay&&delete V.delay,!V.msgConfig&&delete b.msgConfig,V.error=J,V.errorText=X,V.errorCode=Y,!G&&this.onAudioMessage&&this.onAudioMessage(V),ve={id:U,type:"audio",chatType:x,from:L,to:P,url:oe,secret:M.secretKey,file:{},filename:M.displayName,length:M.duration||0,file_length:M.fileLength||0,filetype:M.filetype||"",accessToken:this.token||"",ext:Q,time:Number(D),onlineState:k},V.msgConfig&&(ve.msgConfig=V.msgConfig),Z.length>0&&(ve.reactions=Z),te&&(ve.chatThread=te),re&&(ve.chatThreadOverview=re),"chatRoom"===x&&(ve.priority=B,ve.broadcast=W),ee.operationCount>0&&(ve.modifiedInfo=ee),q&&(ve.isContentReplaced=q),ie&&(ve.isRead=!0),ne&&(ve.isDelivered=!0),z=ve,G?[3,18]:[4,null===(y=null===(v=this._localCache)||void 0===v?void 0:v.getInstance())||void 0===y?void 0:y.storeMessage(ve,Ne.SUCCESS)];case 17:Ae.sent(),null===(_=this.eventHandler)||void 0===_||_.dispatch("onAudioMessage",ve),Ae.label=18;case 18:return[3,28];case 19:return oe=this.useOwnUploadFun?M.remotePath:Ye.call(this,M),!(V={id:U,type:w,contentsType:ze[M.type],from:L,to:P,url:oe,secret:M.secretKey,filename:M.displayName,file_length:M.fileLength,accessToken:this.token||"",ext:Q,time:D,msgConfig:b.msgConfig,onlineState:k}).delay&&delete V.delay,!V.msgConfig&&delete b.msgConfig,V.error=J,V.errorText=X,V.errorCode=Y,!G&&this.onFileMessage&&this.onFileMessage(V),ye={id:U,type:"file",chatType:x,from:L,to:P,url:oe,secret:M.secretKey,file:{},filename:M.displayName,length:M.duration||0,file_length:M.fileLength||0,filetype:M.filetype||"",accessToken:this.token||"",ext:Q,time:Number(D),onlineState:k},V.msgConfig&&(ye.msgConfig=V.msgConfig),Z.length>0&&(ye.reactions=Z),te&&(ye.chatThread=te),re&&(ye.chatThreadOverview=re),"chatRoom"===x&&(ye.priority=B,ye.broadcast=W),ee.operationCount>0&&(ye.modifiedInfo=ee),q&&(ye.isContentReplaced=q),ie&&(ye.isRead=!0),ne&&(ye.isDelivered=!0),z=ye,G?[3,21]:[4,null===(I=null===(T=this._localCache)||void 0===T?void 0:T.getInstance())||void 0===I?void 0:I.storeMessage(ye,Ne.SUCCESS)];case 20:Ae.sent(),null===(O=this.eventHandler)||void 0===O||O.dispatch("onFileMessage",ye),Ae.label=21;case 21:return[3,28];case 22:return!(V={id:U,type:w,contentsType:ze[M.type],from:L,to:P,action:M.action,ext:Q,time:D,msgConfig:b.msgConfig,onlineState:k}).msgConfig&&delete b.msgConfig,V.error=J,V.errorText=X,V.errorCode=Y,!G&&this.onCmdMessage&&this.onCmdMessage(V),Te={id:U,type:"cmd",chatType:x,from:L,to:P,action:M.action,ext:Q,time:Number(D),onlineState:k},V.msgConfig&&(Te.msgConfig=V.msgConfig),Z.length>0&&(Te.reactions=Z),te&&(Te.chatThread=te),re&&(Te.chatThreadOverview=re),"chatRoom"===x&&(Te.priority=B,Te.broadcast=W),ee.operationCount>0&&(Te.modifiedInfo=ee),q&&(Te.isContentReplaced=q),ie&&(Te.isRead=!0),ne&&(Te.isDelivered=!0),z=Te,G||null===(R=this.eventHandler)||void 0===R||R.dispatch("onCmdMessage",Te),[3,28];case 23:return Ie={},Oe={},b.contents[0].customExts&&(Ie=Je(b.contents[0].customExts)),b.contents[0].params&&(Oe=Je(b.contents[0].params)),V={id:U,type:w,contentsType:ze[M.type],from:L,to:P,customEvent:M.customEvent,params:Oe,customExts:Ie,ext:Q,time:D,onlineState:k},!G&&this.onCustomMessage&&this.onCustomMessage(V),Re={id:U,type:"custom",chatType:x,from:L,to:P,customEvent:M.customEvent,params:Oe,customExts:Ie,ext:Q,time:Number(D),onlineState:k},V.msgConfig&&(Re.msgConfig=V.msgConfig),Z.length>0&&(Re.reactions=Z),te&&(Re.chatThread=te),re&&(Re.chatThreadOverview=re),ee.operationCount>0&&(Re.modifiedInfo=ee),"chatRoom"===x&&(Re.priority=B,Re.broadcast=W),q&&(Re.isContentReplaced=q),ie&&(Re.isRead=!0),ne&&(Re.isDelivered=!0),z=Re,G?[3,25]:[4,null===(N=null===(C=this._localCache)||void 0===C?void 0:C.getInstance())||void 0===N?void 0:N.storeMessage(Re,Ne.SUCCESS)];case 24:Ae.sent(),null===(S=this.eventHandler)||void 0===S||S.dispatch("onCustomMessage",Re),Ae.label=25;case 25:return[3,28];case 26:return oe=Ye.call(this,M),Se={id:U,type:"combine",chatType:x,to:P,from:L,ext:Q,time:Number(D),onlineState:k,title:M.title||"",summary:M.summary||"",url:oe||"",secret:M.secretKey||"",file_length:M.fileLength||0,filename:M.displayName||"",compatibleText:M.text,combineLevel:M.combineLevel||0},V.msgConfig&&(Se.msgConfig=V.msgConfig),Z.length>0&&(Se.reactions=Z),te&&(Se.chatThread=te),re&&(Se.chatThreadOverview=re),"chatRoom"===x&&(Se.priority=B,Se.broadcast=W),ee.operationCount>0&&(Se.modifiedInfo=ee),q&&(Se.isContentReplaced=q),ie&&(Se.isRead=!0),ne&&(Se.isDelivered=!0),z=Se,!G&&this.eventHandler&&this.eventHandler.dispatch("onCombineMessage",Se),[3,28];case 27:return H.error("Unknow message type, message:",M),[3,28];case 28:return j?[2,z]:[2,V]}}))}))}function $e(e,t,r){if(this.delivery&&e!==t){var o=this.getUniqueId(),n=new Fe("delivery",o);n.set({ackId:r,to:e}),H.debug("send delivery ack"),this.send(n.body)}}var et=function(e,t,r,o,n){var i,a,s,c,u,l,d,p,h,m,g,E,v,y,_,T,I,O,R,C,N,S,A,b,M,U,w,L;return qe(this,void 0,void 0,(function(){var P,D,k,x,G,B,j,F,W,K,q,V,z,J,Y,X,Q,Z,$,ee,te,re,oe,ne,ie,ae,se,ce,ue,le,de,pe,he,fe,me,ge,Ee,ve,ye,Te,Ie,Oe;return Ve(this,(function(Re){switch(Re.label){case 0:if(P=new(f())(e.timestamp.low,e.timestamp.high,e.timestamp.unsigned).toString(),D=this.root.lookup("easemob.pb.MessageBody"),k=D.decode(e.payload),x=3,G=!1,B=new(f())(e.id.low,e.id.high,e.id.unsigned).toString(),n&&e.from&&e.from.name===this.context.userId&&e.from.clientResource===this.clientResource&&k.type===Ot.CHATROOM)return[2,H.debug("Discard your own chat room message:",B)];if(e.meta&&e.meta.length){if(j=_e.parseNotify(e.meta),F=j.is_online,W=j.callback_replace,this.useReplacedMessageContents&&W&&(G=!0),F||0===F)switch(F){case 0:x=0;break;case 1:x=1;break;default:x=2}}else x=3;switch(K=k.ackMessageId?new(f())(k.ackMessageId.low,k.ackMessageId.high,k.ackMessageId.unsigned).toString():"",q="",V=Xe.call(this,e,k),z=Qe.call(this,e,k),H.debug("meta thirdMessage:",{metaId:B,metaNs:e.ns,type:k.type,from:V,to:z,contentType:null===(a=null===(i=k.contents)||void 0===i?void 0:i[0])||void 0===a?void 0:a.type,contentLen:null===(s=k.contents)||void 0===s?void 0:s.length}),k.type){case Ot.SINGLECHAT:return[3,1];case Ot.GROUPCHAT:return[3,2];case Ot.CHATROOM:return[3,3];case Ot.READ_ACK:return[3,4];case Ot.DELIVER_ACK:return[3,5];case Ot.RECALL:return[3,6];case Ot.CHANNEL_ACK:return[3,15];case Ot.EDIT:return[3,16]}return[3,20];case 1:return q="chat","agoraToken"===this.grantType&&(q="singleChat"),this.delivery&&!r&&V!==this.context.userId&&$e.call(this,V,z,B),[3,21];case 2:return q="groupchat","agoraToken"===this.grantType&&(q="groupChat"),[3,21];case 3:return q="chatroom","agoraToken"===this.grantType&&(q="chatRoom"),x=1,[3,21];case 4:return q="read_ack",J=void 0,k.ext[0]&&JSON.parse(k.ext[0].stringValue)?(J={id:B,type:"read",from:V,to:z,mid:K,groupReadCount:k.ext[0]&&JSON.parse(k.ext[0].stringValue),ackContent:k.ackContent,onlineState:x},this.onReadMessage&&this.onReadMessage(J),null===(c=this.eventHandler)||void 0===c||c.dispatch("onReadMessage",J),[2]):(J={id:B,type:"read",from:V,to:z,mid:K,onlineState:x},this.onReadMessage&&this.onReadMessage(J),null===(u=this.eventHandler)||void 0===u||u.dispatch("onReadMessage",J),[2]);case 5:return q="deliver_ack",this.onDeliveredMessage&&this.onDeliveredMessage({id:B,type:"delivery",from:V,to:z,mid:K,onlineState:x}),Y={id:B,type:"delivery",from:V,to:z,mid:K,onlineState:x},null===(l=this.eventHandler)||void 0===l||l.dispatch("onDeliveredMessage",Y),[2];case 6:return q="recall",X="",e.ext&&(X=(null===(d=Je(e.ext))||void 0===d?void 0:d.recallMessageExtensionInfo)||""),Q={id:B,from:V||"admin",to:z,mid:K,ext:X,onlineState:x},Z=z===this.user?V:z,[4,null===(h=null===(p=this._localCache)||void 0===p?void 0:p.getInstance())||void 0===h?void 0:h.getMessageByServerMsgId(Q.mid)];case 7:return($=Re.sent())?[4,null===(g=null===(m=this._localCache)||void 0===m?void 0:m.getInstance())||void 0===g?void 0:g.removeMsgByServerMsgId(Q.mid)]:[3,9];case 8:Re.sent(),Re.label=9;case 9:return"singleChat"!==(null==$?void 0:$.chatType)&&"groupChat"!==(null==$?void 0:$.chatType)?[3,14]:(ee=(null==$?void 0:$.from)===this.user||""===(null==$?void 0:$.from),[4,null===(v=null===(E=this._localCache)||void 0===E?void 0:E.getInstance())||void 0===v?void 0:v.getConversationLastMessage(Z,$.chatType)]);case 10:return te=Re.sent(),[4,null===(_=null===(y=this._localCache)||void 0===y?void 0:y.getInstance())||void 0===_?void 0:_.getConversationBySessionId(Se({conversationId:Z,conversationType:$.chatType}))];case 11:return(re=Re.sent())?[4,null===(I=null===(T=this._localCache)||void 0===T?void 0:T.getInstance())||void 0===I?void 0:I.updateLocalConversation(Se({conversationId:Z,conversationType:$.chatType}),{lastMessageId:null==te?void 0:te.serverMsgId,unReadCount:Ae({conversation:re,isRecallSelfMsg:ee,recalledMsgTime:$.time})})]:[3,13];case 12:Re.sent(),Re.label=13;case 13:Re.label=14;case 14:return this.onRecallMessage&&this.onRecallMessage(Q),null===(O=this.eventHandler)||void 0===O||O.dispatch("onRecallMessage",Q),[2];case 15:return this.onChannelMessage&&this.onChannelMessage({id:B,type:"channel",chatType:"singleChat",from:V,to:z,time:Number(P),onlineState:x}),oe={id:B,type:"channel",chatType:"singleChat",from:V,to:z,time:Number(P),onlineState:x},null===(R=this.eventHandler)||void 0===R||R.dispatch("onChannelMessage",oe),[2];case 16:return ne={errorCode:0,reason:""},ie="",ae=0,se="",k.meta&&"string"==typeof k.meta&&(ce=JSON.parse(k.meta)).edit_msg&&(ue=ce.edit_msg,le=ue.sender,de=ue.send_time,pe=ue.chat_type,ie=le,ae=de,se=We[pe]),he=k.editMessageId&&new(f())(k.editMessageId.low,k.editMessageId.high,k.editMessageId.unsigned).toString(),fe=k.editScope,[4,Ze.call(this,{status:ne,thirdMessage:k,msgBody:k.contents[0],msgId:he,type:q,from:ie,to:z,time:ae,onlineState:x,ignoreCallback:!0,format:!0,isContentReplaced:G})];case 17:return(me=Re.sent()).chatType=se,e.ignoreCallback?[2,me]:me?[4,null===(N=null===(C=this._localCache)||void 0===C?void 0:C.getInstance())||void 0===N?void 0:N.getMessageByServerMsgId(me.id)]:[3,19];case 18:(ge=Re.sent())&&(Ee=ge.ext||{},(fe===Rt.BODY_AND_EXT||Object.keys(me.ext||{}).length>0)&&(Ee=me.ext),"txt"===me.type&&"txt"===ge.type?null===(A=null===(S=this._localCache)||void 0===S?void 0:S.getInstance())||void 0===A||A.putMessageToDB(Ke(Ke({},ge),{msg:me.msg,modifiedInfo:me.modifiedInfo,translations:me.translations,ext:Ee})):"custom"===me.type&&"custom"===ge.type?null===(M=null===(b=this._localCache)||void 0===b?void 0:b.getInstance())||void 0===M||M.putMessageToDB(Ke(Ke({},ge),{ext:Ee,customExts:me.customExts,customEvent:me.customEvent,modifiedInfo:me.modifiedInfo})):null===(w=null===(U=this._localCache)||void 0===U?void 0:U.getInstance())||void 0===w||w.putMessageToDB(Ke(Ke({},ge),{ext:Ee,modifiedInfo:me.modifiedInfo}))),Re.label=19;case 19:return null===(L=this.eventHandler)||void 0===L||L.dispatch("onModifiedMessage",me),[2];case 20:return H.error("unexpected message type: ".concat(k.type)),[2];case 21:ve="normal",Te=!1,"chat"===q.toLowerCase()||"singleChat"===q?ye="singleChat":"groupchat"===q.toLowerCase()||"groupChat"===q?ye="groupChat":(ye="chatRoom",x=1,e.ext&&(Ie=Je(e.ext),Te=!!(null==Ie?void 0:Ie.is_broadcast),ve=0===Ie.chatroom_msg_tag?"high":2===Ie.chatroom_msg_tag?"low":"normal")),Oe=0,Re.label=22;case 22:return Oe<k.contents.length?[4,Ze.call(this,{status:t,thirdMessage:k,msgBody:k.contents[Oe],msgId:B,type:q,from:V,to:z,time:P,onlineState:x,chatType:ye,ignoreCallback:r,priority:ve,format:o,broadcast:Te,isContentReplaced:G})]:[3,25];case 23:return[2,Re.sent()];case 24:return Oe++,[3,22];case 25:return[2]}}))}))},rt=function(){return rt=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},rt.apply(this,arguments)},ot=["public","members_only","allow_user_invites","invite_need_confirm"],nt={name:"name",title:"name",description:"description",public:"public",members_only:"approval",allow_user_invites:"allowInvites",max_users:"maxUsers",invite_need_confirm:"inviteNeedConfirm",custom:"ext",last_modified:"lastModified",avatar:"avatar"};function it(e,t){var r,o,n,i,a,s,c=this,u=this.context,l=u.userId,d=u.jid,p=t.from.name===l&&d.clientResource!==t.from.clientResource;return t.isThread?(n={id:t.mucId.name,name:t.mucName,operation:"",parentId:t.mucParentId.name,operator:t.from.name,userName:t.to.length?t.to[0].name:""},i={chatThreadId:t.mucId.name,chatThreadName:t.mucName,operation:"",parentId:t.mucParentId.name}):(o={type:"",owner:t.from.name,gid:t.mucId.name,from:t.from.name,fromJid:t.from,to:t.to.length?t.to[0].name:"",toJid:t.to,chatroom:t.isChatroom,status:t.status},a={operation:"",id:t.mucId.name,from:t.from.name},t.isChatroom&&(null===(r=null==t?void 0:t.eventInfo)||void 0===r?void 0:r.ext)&&(s=JSON.parse(t.eventInfo.ext))),({45:function(){var e,r,o;a.operation="memberAttributesUpdate";var n=JSON.parse(null===(e=null==t?void 0:t.eventInfo)||void 0===e?void 0:e.ext)||{};a.attributes=n.properties||{},a.userId=n.username||"",p?null===(r=c.eventHandler)||void 0===r||r.dispatch("onMultiDeviceEvent",a):null===(o=c.eventHandler)||void 0===o||o.dispatch("onGroupEvent",a)},44:function(){var e;a.operation="removeChatRoomAttributes",a.attributes=s.result.successKeys,s.result.successKeys.length>0&&(null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomEvent",a))},43:function(){var e;a.operation="updateChatRoomAttributes";var t={};s.result.successKeys.forEach((function(e){t[e]=s.properties[e]})),a.attributes=t,s.result.successKeys.length>0&&(null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomEvent",a))},42:function(){},41:function(){},40:function(){},39:function(){},38:function(){var e;i.operation="chatThreadNameUpdate",null===(e=c.eventHandler)||void 0===e||e.dispatch("onMultiDeviceEvent",i)},37:function(){var e;n.operation="userRemove",null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatThreadChange",n)},36:function(){var e;i.operation="chatThreadLeave",null===(e=c.eventHandler)||void 0===e||e.dispatch("onMultiDeviceEvent",i)},35:function(){var e;i.operation="chatThreadJoin",null===(e=c.eventHandler)||void 0===e||e.dispatch("onMultiDeviceEvent",i)},34:function(){var e;i.operation="chatThreadDestroy",null===(e=c.eventHandler)||void 0===e||e.dispatch("onMultiDeviceEvent",i)},33:function(){var e;i.operation="chatThreadCreate",null===(e=c.eventHandler)||void 0===e||e.dispatch("onMultiDeviceEvent",i)},32:function(){var e,r,n,i;o.type=t.isChatroom?"rmChatRoomMute":"rmGroupMute",c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),a.operation="unmuteAllMembers",t.isChatroom?null===(n=c.eventHandler)||void 0===n||n.dispatch("onChatroomEvent",a):null===(i=c.eventHandler)||void 0===i||i.dispatch("onGroupEvent",a)},31:function(){var e,r,n,i;o.type=t.isChatroom?"muteChatRoom":"muteGroup",c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),a.operation="muteAllMembers",t.isChatroom?null===(n=c.eventHandler)||void 0===n||n.dispatch("onChatroomEvent",a):null===(i=c.eventHandler)||void 0===i||i.dispatch("onGroupEvent",a)},30:function(){var e,r,n,i;o.type=t.isChatroom?"rmUserFromChatRoomWhiteList":"rmUserFromGroupWhiteList",c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),a.operation="removeAllowlistMember",a.userId=o.to,t.isChatroom?null===(n=c.eventHandler)||void 0===n||n.dispatch("onChatroomEvent",a):null===(i=c.eventHandler)||void 0===i||i.dispatch("onGroupEvent",a)},29:function(){var e,r,n,i;o.type=t.isChatroom?"addUserToChatRoomWhiteList":"addUserToGroupWhiteList",c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),a.operation="addUserToAllowlist",a.userId=o.to,t.isChatroom?null===(n=c.eventHandler)||void 0===n||n.dispatch("onChatroomEvent",a):null===(i=c.eventHandler)||void 0===i||i.dispatch("onGroupEvent",a)},28:function(){var e,r,n,i;o.type="deleteFile",c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),a.operation="deleteFile",t.isChatroom?null===(n=c.eventHandler)||void 0===n||n.dispatch("onChatroomEvent",a):null===(i=c.eventHandler)||void 0===i||i.dispatch("onGroupEvent",a)},27:function(){var e,r,n,i;o.type="uploadFile",c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),a.operation="uploadFile",t.isChatroom?null===(n=c.eventHandler)||void 0===n||n.dispatch("onChatroomEvent",a):null===(i=c.eventHandler)||void 0===i||i.dispatch("onGroupEvent",a)},26:function(){var e,r,n,i;o.type="deleteAnnouncement",c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),a.operation="deleteAnnouncement",t.isChatroom?null===(n=c.eventHandler)||void 0===n||n.dispatch("onChatroomEvent",a):null===(i=c.eventHandler)||void 0===i||i.dispatch("onGroupEvent",a)},25:function(){var e,r,n,i;o.type="updateAnnouncement",o.announcement=t.reason,c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),a.operation="updateAnnouncement",a.announcement=t.reason,t.isChatroom?null===(n=c.eventHandler)||void 0===n||n.dispatch("onChatroomEvent",a):null===(i=c.eventHandler)||void 0===i||i.dispatch("onGroupEvent",a)},24:function(){var e,r,n,i;o.type="removeMute",c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),a.operation="unmuteMember",a.userId=o.to,t.isChatroom?null===(n=c.eventHandler)||void 0===n||n.dispatch("onChatroomEvent",a):null===(i=c.eventHandler)||void 0===i||i.dispatch("onGroupEvent",a)},23:function(){var e,r,n,i;if(o.type="addMute",c.onPresence&&c.onPresence(o),t.ext&&"string"==typeof t.ext){var s=JSON.parse(t.ext);s.user_mute_time&&(o.muteTimestamp=s.user_mute_time,a.muteTimestamp=s.user_mute_time)}t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),a.operation="muteMember",a.userId=o.to,t.isChatroom?null===(n=c.eventHandler)||void 0===n||n.dispatch("onChatroomEvent",a):null===(i=c.eventHandler)||void 0===i||i.dispatch("onGroupEvent",a)},22:function(){var e,r,n,i;o.type="removeAdmin",c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),a.operation="removeAdmin",t.isChatroom?null===(n=c.eventHandler)||void 0===n||n.dispatch("onChatroomEvent",a):null===(i=c.eventHandler)||void 0===i||i.dispatch("onGroupEvent",a)},21:function(){var e,r,n,i;o.type="addAdmin",c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),a.operation="setAdmin",a.userId=o.to,t.isChatroom?null===(n=c.eventHandler)||void 0===n||n.dispatch("onChatroomEvent",a):null===(i=c.eventHandler)||void 0===i||i.dispatch("onGroupEvent",a)},20:function(){var e,r,n,i;o.type="changeOwner",c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),a.operation="changeOwner",t.isChatroom?null===(n=c.eventHandler)||void 0===n||n.dispatch("onChatroomEvent",a):null===(i=c.eventHandler)||void 0===i||i.dispatch("onGroupEvent",a)},19:function(){var e,r,n,i;o.type="direct_joined",o.groupName=t.mucName,c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),a.operation="directJoined",a.name=t.mucName,t.isChatroom?null===(n=c.eventHandler)||void 0===n||n.dispatch("onChatroomEvent",a):null===(i=c.eventHandler)||void 0===i||i.dispatch("onGroupEvent",a)},18:function(){var e,r,n,i,s;o.type=t.isChatroom?"leaveChatRoom":"leaveGroup",c.onPresence&&c.onPresence(o);var u=t.mucMemberCount;if(u&&(a.memberCount=u),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),a.operation="memberAbsence",t.members&&t.members.length>0){var l=rt({},a);l.members=t.members,l.operation="membersAbsence",delete l.from,t.isChatroom?t.members.forEach((function(e){var t;null===(t=c.eventHandler)||void 0===t||t.dispatch("onChatroomEvent",rt(rt({},a),{from:e}))})):null===(n=c.eventHandler)||void 0===n||n.dispatch("onGroupEvent",l)}else t.isChatroom?null===(i=c.eventHandler)||void 0===i||i.dispatch("onChatroomEvent",a):null===(s=c.eventHandler)||void 0===s||s.dispatch("onGroupEvent",a)},17:function(){var e,r,n,i,s;t.isChatroom&&t.ext&&(a.ext=t.ext,o.ext=t.ext),o.type=t.isChatroom?"memberJoinChatRoomSuccess":"memberJoinPublicGroupSuccess",c.onPresence&&c.onPresence(o);var u=t.mucMemberCount;if(t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),a.operation="memberPresence",u&&(a.memberCount=u),t.members&&t.members.length>0){var l=rt({},a);l.members=t.members,l.operation="membersPresence",delete l.from,t.isChatroom?t.members.forEach((function(e){var t;null===(t=c.eventHandler)||void 0===t||t.dispatch("onChatroomEvent",rt(rt({},a),{from:e}))})):null===(n=c.eventHandler)||void 0===n||n.dispatch("onGroupEvent",l)}else t.isChatroom?null===(i=c.eventHandler)||void 0===i||i.dispatch("onChatroomEvent",a):null===(s=c.eventHandler)||void 0===s||s.dispatch("onGroupEvent",a)},16:function(){var e,r;o.type="unblock",c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o)},15:function(){var e,r;o.type="block",c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o)},14:function(){var e,r,n,i,s,u=t.isChatroom;if(!u){var l=JSON.parse((null===(e=null==t?void 0:t.eventInfo)||void 0===e?void 0:e.ext)||"{}",(function(e,t){return"last_modified"===e?Number(t):ot.includes(e)?"true"===t||!0===t:t}));a.detail=o.detail={},Object.keys(l).forEach((function(e){var t=nt[e];if(t){var r=l[e];a.detail&&(a.detail[t]=r),o.detail&&(o.detail[t]=r)}}))}o.type="update",c.onPresence&&c.onPresence(o),u?null===(r=c.eventHandler)||void 0===r||r.dispatch("onChatroomChange",o):null===(n=c.eventHandler)||void 0===n||n.dispatch("onGroupChange",o),a.operation="updateInfo",u?null===(i=c.eventHandler)||void 0===i||i.dispatch("onChatroomEvent",a):null===(s=c.eventHandler)||void 0===s||s.dispatch("onGroupEvent",a)},13:function(){var e,r,n,i;o.type="allow",o.reason=t.reason,c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),t.reason&&(a.reason=t.reason),a.operation="unblockMember",t.isChatroom?null===(n=c.eventHandler)||void 0===n||n.dispatch("onChatroomEvent",a):null===(i=c.eventHandler)||void 0===i||i.dispatch("onGroupEvent",a)},12:function(){var e,r;o.type="ban",c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o)},11:function(){var e,r;o.type="getBlackList",c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o)},10:function(){var e,r,n,i;o.type="removedFromGroup",o.kicked=o.to,c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),a.operation="removeMember",t.isChatroom?null===(n=c.eventHandler)||void 0===n||n.dispatch("onChatroomEvent",a):null===(i=c.eventHandler)||void 0===i||i.dispatch("onGroupEvent",a)},9:function(){var e,r,n,i;o.type="invite_decline",o.kicked=o.to,c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),a.operation="rejectInvite",t.isChatroom?null===(n=c.eventHandler)||void 0===n||n.dispatch("onChatroomEvent",a):null===(i=c.eventHandler)||void 0===i||i.dispatch("onGroupEvent",a)},8:function(){var e,r,n,i;o.type="invite_accept",o.kicked=o.to,c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),a.operation="acceptInvite",t.isChatroom?null===(n=c.eventHandler)||void 0===n||n.dispatch("onChatroomEvent",a):null===(i=c.eventHandler)||void 0===i||i.dispatch("onGroupEvent",a)},7:function(){var e,r,n,i;o.type="invite",o.kicked=o.to,o.groupName=t.mucName,c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),a.operation="inviteToJoin",a.name=t.mucName,t.isChatroom?null===(n=c.eventHandler)||void 0===n||n.dispatch("onChatroomEvent",a):null===(i=c.eventHandler)||void 0===i||i.dispatch("onGroupEvent",a)},6:function(){var e,r,n,i;o.type="joinPublicGroupDeclined",c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),a.operation="joinPublicGroupDeclined",t.isChatroom?null===(n=c.eventHandler)||void 0===n||n.dispatch("onChatroomEvent",a):(a.userId=t.to.length?t.to[0].name:"",null===(i=c.eventHandler)||void 0===i||i.dispatch("onGroupEvent",a))},5:function(){var e,r,n,i;o.type="joinPublicGroupSuccess",c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),a.operation="acceptRequest",t.isChatroom?null===(n=c.eventHandler)||void 0===n||n.dispatch("onChatroomEvent",a):null===(i=c.eventHandler)||void 0===i||i.dispatch("onGroupEvent",a)},4:function(){var e,r,n,i;o.type="joinGroupNotifications",o.reason=t.reason,c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),t.reason&&(a.reason=t.reason),a.operation="requestToJoin",t.isChatroom?null===(n=c.eventHandler)||void 0===n||n.dispatch("onChatroomEvent",a):null===(i=c.eventHandler)||void 0===i||i.dispatch("onGroupEvent",a)},3:function(){var e,r;o.type="leave",c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o)},2:function(){var e,r;o.type="join",c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o)},1:function(){var e,r,n,i;o.type="deleteGroupChat",c.onPresence&&c.onPresence(o),t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomChange",o):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupChange",o),a.operation="destroy",t.isChatroom?null===(n=c.eventHandler)||void 0===n||n.dispatch("onChatroomEvent",a):null===(i=c.eventHandler)||void 0===i||i.dispatch("onGroupEvent",a)},0:function(){var e,r;a.operation="create",t.isChatroom?null===(e=c.eventHandler)||void 0===e||e.dispatch("onChatroomEvent",a):null===(r=c.eventHandler)||void 0===r||r.dispatch("onGroupEvent",a)}}[e]||function(){console.error("No match operation ".concat(e))})()}var at,st=function(e){var t=this.root.lookup("easemob.pb.MUCBody").decode(e.payload),r=t.operation;H.debug("onMucMessage",t),it.call(this,r,t)},ct={handleRosterMsg:function(e){var t,r,o,n,i,a,s=this.root.lookup("easemob.pb.RosterBody").decode(e.payload),c={type:"",to:s.to[0].name,from:s.from.name,status:s.reason};switch(s.operation){case 2:c.type="subscribe",this.onContactInvited&&this.onContactInvited(c),null===(t=this.eventHandler)||void 0===t||t.dispatch("onContactInvited",c);break;case 3:c.type="unsubscribed",this.onContactDeleted&&this.onContactDeleted(c),null===(r=this.eventHandler)||void 0===r||r.dispatch("onContactDeleted",c);break;case 4:c.type="subscribed",this.onContactAdded&&this.onContactAdded(c),null===(o=this.eventHandler)||void 0===o||o.dispatch("onContactAdded",c);break;case 5:c.type="unsubscribed",this.onContactRefuse&&this.onContactRefuse(c),null===(n=this.eventHandler)||void 0===n||n.dispatch("onContactRefuse",c);break;case 6:case 7:break;case 8:c.type="subscribed",this.onContactAgreed&&this.onContactAgreed(c),null===(i=this.eventHandler)||void 0===i||i.dispatch("onContactAgreed",c);break;case 9:c.type="unsubscribed",this.onContactRefuse&&this.onContactRefuse(c),null===(a=this.eventHandler)||void 0===a||a.dispatch("onContactRefuse",c);break;default:H.error("handleRosterMsg:",s)}this.onPresence&&c.type&&this.onPresence(c)}},ut=function(e){var t,r,o,n,i,a,s=this.root.lookup("easemob.pb.StatisticsBody").decode(e.payload),c=s.operation,u=s.reason;switch(c){case 0:this.onStatisticMessage&&this.onStatisticMessage(s),null===(t=this.eventHandler)||void 0===t||t.dispatch("onStatisticMessage",s);break;case 1:a=m.create({type:d.WEBIM_CONNCTION_USER_REMOVED,message:"user has been removed"}),this.disconnectedReason=a,this.logOut=!0,this.onError&&this.onError(a),null===(r=this.eventHandler)||void 0===r||r.dispatch("onError",a);break;case 2:a=m.create({type:d.WEBIM_CONNCTION_USER_LOGIN_ANOTHER_DEVICE,message:"the user is already logged on another device"}),u&&(a.data={loginInfoCustomExt:s.reason}),this.disconnectedReason=a,this.logOut=!0,this.onError&&this.onError(a),null===(o=this.eventHandler)||void 0===o||o.dispatch("onError",a);break;case 3:a=m.create({type:d.WEBIM_CONNCTION_USER_KICKED_BY_CHANGE_PASSWORD,message:"the user was kicked by changing password"}),this.disconnectedReason=a,this.logOut=!0,this.onError&&this.onError(a),null===(n=this.eventHandler)||void 0===n||n.dispatch("onError",a);break;case 4:a=m.create({type:d.WEBIM_CONNCTION_USER_KICKED_BY_OTHER_DEVICE,message:"the user was kicked by other device"}),this.disconnectedReason=a,this.logOut=!0,this.onError&&this.onError(a),null===(i=this.eventHandler)||void 0===i||i.dispatch("onError",a);break;default:H.error("handleStatisticsMsg:",s)}};function lt(e){var t,r=[],o=[],n=e.data;n&&n.values&&n.values.forEach((function(e){Object.entries(e.status).forEach((function(e){o.push({device:e[0],status:Number(e[1])})})),r.push({userId:e.uid,lastTime:Number(e.last_time),expire:Number(e.expiry),ext:e.ext,statusDetails:o})})),this.onPresenceStatusChange&&this.onPresenceStatusChange(r),null===(t=this.eventHandler)||void 0===t||t.dispatch("onPresenceStatusChange",r)}function dt(e){var t=this;e.data.forEach((function(e){var r,o={from:e.from,to:e.to,chatType:"chat"===e.channel_type?"singleChat":"groupChat",messageId:e.messageId,reactions:e.reactions,ts:e.ts};null===(r=t.eventHandler)||void 0===r||r.dispatch("onReactionChange",o)}))}function pt(e){var t,r,o,n;if(e.data){var i=e.data,a={id:i.id||"",name:i.name||"",parentId:i.muc_parent_id||"",messageId:i.msg_parent_id||"",timestamp:i.timestamp||0,operator:i.from||"",operation:""};switch(i.operation){case"create":a.operation="create",a.createTimestamp=a.timestamp,a.messageCount=0,null===(t=this.eventHandler)||void 0===t||t.dispatch("onChatThreadChange",a);break;case"update_msg":a.operation="update",a.messageCount=i.message_count,i.last_message&&"{}"!==JSON.stringify(i.last_message)?a.lastMessage=Ce.call(this,i.last_message):"{}"===JSON.stringify(i.last_message)&&(a.lastMessage={}),null===(r=this.eventHandler)||void 0===r||r.dispatch("onChatThreadChange",a);break;case"update":a.operation="update",a.messageCount=i.message_count,null===(o=this.eventHandler)||void 0===o||o.dispatch("onChatThreadChange",a);break;case"delete":a.operation="destroy",null===(n=this.eventHandler)||void 0===n||n.dispatch("onChatThreadChange",a)}}}function ht(e){var t,r=e.data;if(r.resource!==this.clientResource){var o={operation:"deleteRoaming",conversationId:r.to,chatType:"chat"===r.chatType?"singleChat":"groupChat",resource:r.resource};null===(t=this.eventHandler)||void 0===t||t.dispatch("onMultiDeviceEvent",o)}}function ft(e){var t,r,o=e.data,n="";if(this.clientResource!==o.res||o.from!==this.user){switch(o.op){case"del":n="deleteConversation";break;case"top":n="pinnedConversation";break;case"not_top":n="unpinnedConversation";break;case"mark":n="markConversation";break;case"mark_delete":n="unMarkConversation";break;case"pin":n="pin";break;case"pin_delete":n="unpin";break;default:return void H.error("unexpected conversation op:",o.op)}if("pin"!==n&&"unpin"!==n){var i={operation:n,conversationId:o.id,conversationType:"chat"===o.type?"singleChat":"groupChat",timestamp:o.ts};"markConversation"!==i.operation&&"unMarkConversation"!==i.operation||o.ext&&(i.conversationMark=at[o.ext]),null===(r=this.eventHandler)||void 0===r||r.dispatch("onMultiDeviceEvent",i)}else{var a=o.ext,s=o.from,c=o.id,u=o.type,l=o.ts,d={messageId:a||"",conversationId:"chat"===u?s:c,conversationType:Re[u],operation:n,operatorId:s,time:l};null===(t=this.eventHandler)||void 0===t||t.dispatch("onMessagePinEvent",d)}}}function mt(e){var t,r=this;null===(t=e.values)||void 0===t||t.forEach((function(e){var t,o,n,i,a;if(e.operator_resource!==r.clientResource)if("ignoreInterval"in e.data){var s={operation:"setSilentModeForUser",resource:e.operator_resource,data:e.data};null===(t=r.eventHandler)||void 0===t||t.dispatch("onMultiDeviceEvent",s)}else"group"in e?(s={operation:0===Object.keys(null!==(o=e.data)&&void 0!==o?o:{}).length?"removeSilentModeForConversation":"setSilentModeForConversation",resource:e.operator_resource,conversationId:e.group,type:"groupChat",data:e.data},null===(n=r.eventHandler)||void 0===n||n.dispatch("onMultiDeviceEvent",s)):(s={operation:0===Object.keys(null!==(i=e.data)&&void 0!==i?i:{}).length?"removeSilentModeForConversation":"setSilentModeForConversation",resource:e.operator_resource,conversationId:e.user,type:"singleChat",data:e.data},null===(a=r.eventHandler)||void 0===a||a.dispatch("onMultiDeviceEvent",s))}))}!function(e){e[e.mark_0=0]="mark_0",e[e.mark_1=1]="mark_1",e[e.mark_2=2]="mark_2",e[e.mark_3=3]="mark_3",e[e.mark_4=4]="mark_4",e[e.mark_5=5]="mark_5",e[e.mark_6=6]="mark_6",e[e.mark_7=7]="mark_7",e[e.mark_8=8]="mark_8",e[e.mark_9=9]="mark_9",e[e.mark_10=10]="mark_10",e[e.mark_11=11]="mark_11",e[e.mark_12=12]="mark_12",e[e.mark_13=13]="mark_13",e[e.mark_14=14]="mark_14",e[e.mark_15=15]="mark_15",e[e.mark_16=16]="mark_16",e[e.mark_17=17]="mark_17",e[e.mark_18=18]="mark_18",e[e.mark_19=19]="mark_19"}(at||(at={}));var gt=function(e){var t=_e.parseNotify(e.payload);switch(t.type){case"presence":lt.call(this,t);break;case"reaction":dt.call(this,t);break;case"thread":pt.call(this,t);break;case"roaming_delete":ht.call(this,t);break;case"conv":ft.call(this,t);break;case"user_notification_mute":mt.call(this,t);break;default:H.error("unexpected notify type: ".concat(t.type))}};function Et(){var e=this.context.appName,t=this.context.orgName;return!(!e||!t)||(this.onError&&this.onError({type:d.WEBIM_CONNCTION_AUTH_ERROR,message:"appName or orgName is illegal"}),!1)}function vt(){var e,t=this.context.accessToken;if(!t){var r=m.create({type:d.WEBIM_CONNCTION_TOKEN_NOT_ASSIGN_ERROR,message:"token not assign error"});return H.debug("token not assign error",t),this.onError&&this.onError(r),null===(e=this.eventHandler)||void 0===e||e.dispatch("onError",r),!1}return!0}function yt(){if(this._initWithAppId){if(!Et.call(this))return{error:m.create({type:d.WEBIM_CONNECTION_CLOSED,message:"not login"})};if(!vt.call(this))return{error:m.create({type:d.WEBIM_CONNCTION_TOKEN_NOT_ASSIGN_ERROR,message:"token not assign error"})}}else if(!Et.call(this)||!vt.call(this))return{error:m.create({type:d.REST_PARAMS_STATUS,message:"appkey or token error"})};return{}}function _t(e){var t=e.data,r=e.type;return{data:{status:Object.keys(t.errorKeys).length>0?"fail":"success",errorKeys:t.errorKeys,successKeys:t.successKeys},type:r}}function Tt(e){var t=e.data,r=void 0;return Object.keys(t.errorKeys).length>0&&Object.keys(t.errorKeys).forEach((function(e){var o=t.errorKeys[e];r=o.includes("is not part of you")?m.create({type:d.NO_PERMISSION,message:o}):o.includes("size of metadata for this single chatroom exceeds the user defined limit")||o.includes("total size of chatroom metadata for this app exceeds the user defined limit")||o.includes("is exceeding maximum limit")?m.create({type:d.MAX_LIMIT,message:o}):o.includes("is not Legal")?m.create({type:d.REQUEST_PARAMETER_ERROR,message:o}):o.includes("Failed to update userMetadata. Concurrent updates not allowed")?m.create({type:d.OPERATION_NOT_ALLOWED,message:o}):m.create({type:d.WEBIM_CONNCTION_AJAX_ERROR,message:o})})),r}function It(e){if("string"!=typeof e.deviceId||""===e.deviceId)throw Error('Invalid parameter: "deviceId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/users/").concat(this.user),type:"PUT",data:JSON.stringify({device_id:e.deviceId,device_token:e.deviceToken,notifier_name:e.notifierName}),dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};H.debug("Call uploadPushTokenToServer",e);var s=ae.bind(this,a,E.UPLOAD_PUSH_TOKEN);return _e.retryPromise(s).then((function(e){var t=e.entities[0]||{};return{type:e.type,data:t}}))}var Ot,Rt,Ct,Nt,St=function(){return St=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},St.apply(this,arguments)},At=function(e,t,r,o){return new(r||(r=Promise))((function(n,i){function a(e){try{c(o.next(e))}catch(e){i(e)}}function s(e){try{c(o.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}c((o=o.apply(e,t||[])).next())}))},bt=function(e,t){var r,o,n,i,a={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,o&&(n=2&i[0]?o.return:i[0]?o.throw||((n=o.return)&&n.call(o),0):o.next)&&!(n=n.call(o,i[1])).done)return n;switch(o=0,n&&(i=[2&i[0],n.value]),i[0]){case 0:case 1:n=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,o=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!((n=(n=a.trys).length>0&&n[n.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!n||i[1]>n[0]&&i[1]<n[3])){a.label=i[1];break}if(6===i[0]&&a.label<n[1]){a.label=n[1],n=i;break}if(n&&a.label<n[2]){a.label=n[2],a.ops.push(i);break}n[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],o=0}finally{r=n=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}};!function(e){e[e.NORMAL=0]="NORMAL",e[e.SINGLECHAT=1]="SINGLECHAT",e[e.GROUPCHAT=2]="GROUPCHAT",e[e.CHATROOM=3]="CHATROOM",e[e.READ_ACK=4]="READ_ACK",e[e.DELIVER_ACK=5]="DELIVER_ACK",e[e.RECALL=6]="RECALL",e[e.CHANNEL_ACK=7]="CHANNEL_ACK",e[e.EDIT=8]="EDIT"}(Ot||(Ot={})),function(e){e[e.BODY=1]="BODY",e[e.BODY_AND_EXT=2]="BODY_AND_EXT"}(Rt||(Rt={})),function(e){e[e.APNs=0]="APNs",e[e.FCM=1]="FCM",e[e.HMSPUSH=2]="HMSPUSH",e[e.MIPUSH=3]="MIPUSH",e[e.MEIZUPUSH=4]="MEIZUPUSH",e[e.VIVOPUSH=5]="VIVOPUSH",e[e.OPPOPUSH=6]="OPPOPUSH",e[e.HONORPUSH=7]="HONORPUSH"}(Ct||(Ct={})),function(e){e[e.OK=0]="OK",e[e.FAIL=1]="FAIL",e[e.UNAUTHORIZED=2]="UNAUTHORIZED",e[e.MISSING_PARAMETER=3]="MISSING_PARAMETER",e[e.WRONG_PARAMETER=4]="WRONG_PARAMETER",e[e.REDIRECT=5]="REDIRECT",e[e.TOKEN_EXPIRED=6]="TOKEN_EXPIRED",e[e.PERMISSION_DENIED=7]="PERMISSION_DENIED",e[e.NO_ROUTE=8]="NO_ROUTE",e[e.UNKNOWN_COMMAND=9]="UNKNOWN_COMMAND",e[e.PB_PARSER_ERROR=10]="PB_PARSER_ERROR",e[e.BIND_ANOTHER_DEVICE=11]="BIND_ANOTHER_DEVICE",e[e.IM_FORBIDDEN=12]="IM_FORBIDDEN",e[e.TOO_MANY_DEVICES=13]="TOO_MANY_DEVICES",e[e.PLATFORM_LIMIT=14]="PLATFORM_LIMIT",e[e.USER_MUTED=15]="USER_MUTED",e[e.ENCRYPT_DISABLE=16]="ENCRYPT_DISABLE",e[e.ENCRYPT_ENABLE=17]="ENCRYPT_ENABLE",e[e.DECRYPT_FAILURE=18]="DECRYPT_FAILURE",e[e.PERMISSION_DENIED_EXTERNAL=19]="PERMISSION_DENIED_EXTERNAL"}(Nt||(Nt={}));var Mt=["txt","img","video","audio","file","loc","custom","cmd","combine"],Ut=_e.getEnvInfo();function wt(){var e,t,r,o,n="webim",i="",a="",s=[],c=(new Date).valueOf(),u=_e.getDevicePlatform(Ut);if(this.isFixedDeviceId){var l=_e.getLocalDeviceInfo();if(l){var d=JSON.parse(l);n=d.deviceId,i=d.deviceName,a=d.deviceUuid,void 0!==this.customOSPlatform&&(i=this.deviceId,n="custom".concat(this.customOSPlatform,"_").concat(a))}else"webim"===this.deviceId?(a="".concat(u,"_").concat(c.toString()),n="".concat(this.deviceId,"_").concat(a),i=this.deviceId,void 0!==this.customOSPlatform&&(n="custom".concat(this.customOSPlatform,"_").concat(a))):(n=i=a="webim_".concat(u,"_").concat(this.deviceId),void 0!==this.customOSPlatform&&(a="".concat(u,"_").concat(c.toString()),i=this.deviceId,n="custom".concat(this.customOSPlatform,"_").concat(a))),_e.setLocalDeviceInfo(JSON.stringify({deviceId:n,deviceName:i,deviceUuid:a}))}else"webim"===this.deviceId?(a="random_".concat(u,"_").concat(c.toString()),n="".concat(this.deviceId,"_").concat(a),i=this.deviceId):n=i=a="webim_".concat(u,"_").concat(this.deviceId),void 0!==this.customOSPlatform&&(a="random_".concat(u,"_").concat(c.toString()),i=this.deviceId,n="custom".concat(this.customOSPlatform,"_").concat(a));this.context.jid&&(this.context.jid.clientResource=n);var p=this.root.lookup("easemob.pb.Provision"),h=p.decode(s);void 0!==this.customOSPlatform&&(h.osCustomValue=this.customOSPlatform,this.osType=5),h.compressType=this.compressType,h.encryptType=this.encryptType,h.osType=this.osType,h.version=this.version,h.deviceName=i,h.resource=n,h.deviceUuid=a,h.authToken='{"token":"$t$'+this.token+'"}',h.sessionId=Date.now().toString()+":",this.loginInfoCustomExt&&(h.reason=this.loginInfoCustomExt),H.debug("provision info",{version:this.version,resource:n,isFixedDeviceId:this.isFixedDeviceId,loginInfoCustomExt:!!this.loginInfoCustomExt,token:(null===(t=null===(e=this.token)||void 0===e?void 0:e.slice)||void 0===t?void 0:t.call(e,0,10))+"...",userId:null===(o=null===(r=this.context)||void 0===r?void 0:r.jid)||void 0===o?void 0:o.name}),h.actionVersion="v5.1",h=p.encode(h).finish();var f=this.root.lookup("easemob.pb.MSync"),m=f.decode(s);return m.version=this.version,m.guid=this.context.jid,m.auth="$t$"+this.token,m.command=3,m.deviceId=i,m.serviceId=this.dataReport.getServiceId(),m.encryptType=this.encryptType,m.payload=h,f.encode(m).finish()}function Lt(e,t){var r=this,o=_e.getEnvInfo();if("zfb"===o.platform||"dd"===o.platform){for(var n="",i=0;i<e.length;i++)n+=String.fromCharCode(e[i]);return{data:n=Ie().btoa(n),isBuffer:!1,complete:function(){},fail:function(e){"sendSocketMessage:fail taskID not exist"!==e.errMsg&&"SocketTast.send:fail SocketTask.readyState is not OPEN"!==e.errMsg||(H.debug("send message fail and reconnect"),r.reconnecting||r.reconnect()),t&&r._msgHash&&r._msgHash[t]&&r._msgHash[t].fail({id:t})}}}var a=e;return{data:a.buffer.slice(a.byteOffset,a.byteOffset+a.byteLength),fail:function(e){"sendSocketMessage:fail taskID not exist"!==e.errMsg&&"SocketTast.send:fail SocketTask.readyState is not OPEN"!==e.errMsg||r.reconnecting||r.reconnect(),t&&r._msgHash&&r._msgHash[t]&&r._msgHash[t].fail({id:t})}}}function Pt(e,t){var r;return At(this,void 0,void 0,(function(){var o,n,i,a,s,c;return bt(this,(function(u){switch(u.label){case 0:for(H.debug("distributeMeta, metas length: ",e.length),o=[],n=function(r){var n=e[r],a=new(f())(n.id.low,n.id.high,n.id.unsigned).toString(),s=n.from.name,c=n.to?n.to.name:"",u=!!n.to&&-1!==n.to.domain.indexOf("conference");if(i._load_msg_cache.some((function(e){return e.msgId===a})))return"continue";switch(i._load_msg_cache.length<=i.max_cache_length||i._load_msg_cache.shift(),i._load_msg_cache.push({msgId:a,from:s,to:c,isGroup:u}),n.ns){case 0:ut.call(i,n);break;case 1:o.push(et.call(i,n,t,!1,!0,!0));break;case 2:st.call(i,n);break;case 3:ct.handleRosterMsg.call(i,n);break;case 4:i.registerConfrIQHandler&&i.registerConfrIQHandler(n,t,i);break;case 5:gt.call(i,n);break;default:H.error("distributeMeta",n)}},i=this,a=0;a<e.length;a++)n(a);return[4,Promise.all(o)];case 1:return s=u.sent(),(c=s.filter((function(e){return e&&"cmd"!==e.type}))).length>0&&(null===(r=this.eventHandler)||void 0===r||r.dispatch("onMessage",c)),[2]}}))}))}function Dt(e,t){Pt.call(this,e,t)}function kt(e){var t,r=this;or.clear(),this._offlineMessagePullState===ve.SYNC_INIT&&(this._offlineMessagePullState=ve.SYNC_START,this._offlineMessagePullQueue=e.unread.reduce((function(e,t,o){return e.find((function(e){return e.name===t.queue.name}))||e.push(t),r.pullOfflineMessageInfo.pullCount+=t.n,e}),[]),this.pullOfflineMessageInfo.startTime=Date.now(),this._offlineMessageRpt=this.dataReport.geOperateFun({operationName:E.MSYNC_SYNCOFFLINEMESSAGE}),null===(t=this.eventHandler)||void 0===t||t.dispatch("onOfflineMessageSyncStart"))}function xt(e){var t;if(this._offlineMessagePullState===ve.SYNC_START){var r=this._offlineMessagePullQueue.findIndex((function(t){return t.queue.name===e.queue.name}));if(r>-1){this._offlineMessagePullQueue.splice(r,1);var o=or.get(e.queue.name)||0;o>0&&this._offlineMessageRpt({isEndApi:!1,data:{isSuccess:1,requestName:E.MSYNC_SYNCOFFLINEMESSAGE,requestMethod:"WEBSOCKET",requestUrl:this.url,code:_.success,resultCount:o,requestElapse:Date.now()-this.pullOfflineMessageInfo.startTime}})}0===this._offlineMessagePullQueue.length&&(this._offlineMessagePullState=ve.SYNC_FINISH,null===(t=this.eventHandler)||void 0===t||t.dispatch("onOfflineMessageSyncFinish"),this._offlineMessageRpt({data:{isLastApi:1,isSuccess:1,requestName:E.MSYNC_SYNCOFFLINEMESSAGE,requestMethod:"WEBSOCKET",requestUrl:this.url,code:_.success,resultCount:this.pullOfflineMessageInfo.pullCount}}),or.clear())}}function Gt(e){var t=this.root.lookup("easemob.pb.CommUnreadDL");t=t.decode(e.payload);var r=new(f())(t.timestamp.low,t.timestamp.high,t.timestamp.unsigned).toString();if(this.expirationTime&&this.compareTokenExpireTime(Number(r),this.expirationTime),0===t.unread.length)Jt.call(this);else{kt.call(this,t),H.debug("pull unread message",t.unread);for(var o=0;o<t.unread.length;o++)jt.call(this,t.unread[o].queue)}Jt.call(this)}function Bt(){var e=[],t=this.root.lookup("easemob.pb.StatisticsBody"),r=t.decode(e);r.operation=0,r=t.encode(r).finish();var o=this.root.lookup("easemob.pb.Meta").decode(e);o.id=(new Date).valueOf(),o.ns=0,o.payload=r;var n=this.root.lookup("easemob.pb.CommSyncUL"),i=n.decode(e);i.meta=o,i=n.encode(i).finish();var a=this.root.lookup("easemob.pb.MSync"),s=a.decode(e);return s.version=this.version,s.encryptType=[0],s.command=0,s.payload=i,a.encode(s).finish()}function Ht(e){var t=[],r=this.root.lookup("easemob.pb.CommSyncUL"),o=r.decode(t);o.queue=e,o=r.encode(o).finish();var n=this.root.lookup("easemob.pb.MSync"),i=n.decode(t);return i.version=this.version,i.encryptType=this.encryptType,i.command=0,i.payload=o,n.encode(i).finish()}function jt(e){H.debug("sendBackqueue",e);var t=Ht.call(this,e);hr.call(this,t)}function Ft(e,t){var r=[],o=this.root.lookup("easemob.pb.CommSyncUL"),n=o.decode(r);n.queue=t,n.key=new(f())(e.low,e.high,e.unsigned).toString(),n=o.encode(n).finish();var i=this.root.lookup("easemob.pb.MSync"),a=i.decode(r);return a.version=this.version,a.encryptType=this.encryptType,a.command=0,a.payload=n,i.encode(a).finish()}function Wt(){var e=this;this.uniPush&&!1===this.isRegisterPush&&(this.isRegisterPush=!0,this.uniPush.onRegister((function(t){H.debug("push token onRegister",t);var r=e.uniPushConfig||{},o=r.APNsCertificateName,n=void 0===o?"":o,i=r.HMSCertificateName,a=void 0===i?"":i,s=r.HONORCertificateName,c=void 0===s?"":s,u=r.MEIZUCertificateName,l=void 0===u?"":u,d=r.MICertificateName,p=void 0===d?"":d,h=r.OPPOCertificateName,f=void 0===h?"":h,m=r.VIVOCertificateName,g=void 0===m?"":m,E=r.FCMCertificateName,v=void 0===E?"":E;switch(t.push_type){case Ct.APNs:e.pushCertificateName=n;break;case Ct.FCM:e.pushCertificateName=v;break;case Ct.HMSPUSH:e.pushCertificateName=a;break;case Ct.HONORPUSH:e.pushCertificateName=c;break;case Ct.MEIZUPUSH:e.pushCertificateName=l;break;case Ct.MIPUSH:e.pushCertificateName=p;break;case Ct.OPPOPUSH:e.pushCertificateName=f;break;case Ct.VIVOPUSH:e.pushCertificateName=g;break;default:H.error("unexpected push type",t.push_type)}e.pushCertificateName&&t.push_token&&It.call(e,{deviceId:e.clientResource,deviceToken:t.push_token,notifierName:e.pushCertificateName}).then((function(){H.debug("uploadPushToken success")})).catch((function(e){H.debug("uploadPushToken failed",e)}))})))}function Kt(){var e;this._offlineMessagePullState=ve.SYNC_INIT,this._offlineMessagePullQueue=[],this.times=1,this.autoReconnectNumTotal=0,this.onOpened&&this.onOpened(),Wt.call(this),null===(e=this.eventHandler)||void 0===e||e.dispatch("onConnected"),zt.call(this),Xt.call(this),Yt.call(this),Jt.call(this),this.pullOfflineMessageInfo={pullCount:0,startTime:0}}function qt(e,t){H.debug("sendLastSession",e);var r=Ft.call(this,e,t);hr.call(this,r)}function Vt(e){var t,r,o,n,i,a,s,c,u,l,p,h,f,g,E=this,v=this.root.lookup("easemob.pb.Provision").decode(e.payload);if(this.context.jid&&(this.context.jid.clientResource=v.resource),this.clientResource=v.resource,this.provisionTimer&&clearTimeout(this.provisionTimer),H.debug("receiveProvisionResult",v.status),this._isLogging=!1,v.status.errorCode===Nt.OK){if(this.disconnectedReason=void 0,v.authToken){var y=JSON.parse(v.authToken).expires_in;if(!this.tokenExpiredTimer&&!this.tokenWillExpireTimer){var _=Date.now();this.expirationTime=y;var T=this.expirationTime-_;this.expiresIn=T<0?0:T,this.tokenExpireTimeCountDown(this.expiresIn)}}this.reconnecting=!1,this.logOut=!1,this.needSetReadyState&&(this.sock=St(St({},this.sock),{close:this.sock.close,send:this.sock.send,readyState:1})),this._localCache?(null===(t=this._localCache)||void 0===t?void 0:t.getInstance())?Kt.call(this):this._localCache&&new this._localCache({user:this.user,dbName:"cache_".concat(Math.abs(be(this.appKey||this.appId)),"_").concat(this.user),version:2,onInit:function(){return At(E,void 0,void 0,(function(){return bt(this,(function(e){return H.debug("localCache init success"),Kt.call(this),[2]}))}))}}):Kt.call(this),null===(r=this.connectionResolve)||void 0===r||r.call(this)}else{var I=void 0,O=v.status,R=O.reason,C=O.errorCode;switch(H.debug("provision errorCode: ",C),H.debug("provision reason: ",R),C){case Nt.FAIL:I="Sorry, user register limit"===R?m.create({type:d.MAX_LIMIT,message:"Sorry, the number of user registrations limit has been reached"}):"Sorry, user register rate limit"===R?m.create({type:d.MAX_LIMIT,message:"Sorry, user register rate limit"}):"Sorry, token expired"===R?m.create({type:d.WEBIM_TOKEN_EXPIRED,message:"Sorry, token expired"}):"Sorry, token or password does not match login info"===R?m.create({type:d.WEBIM_CONNCTION_TOKEN_NOT_ASSIGN_ERROR,message:"INVALID_TOKEN"}):"Sorry, user not found"===R?m.create({type:d.USER_NOT_FOUND,message:"User not found"}):m.create({type:d.WEBIM_CONNCTION_AUTH_ERROR,message:"Auth failed"}),null===(o=this.connectionReject)||void 0===o||o.call(this,I),null===(n=this.eventHandler)||void 0===n||n.dispatch("onError",I);break;case Nt.WRONG_PARAMETER:I=m.create({type:d.REQUEST_PARAMETER_ERROR,message:"Invalid parameter"}),null===(i=this.connectionReject)||void 0===i||i.call(this,I),null===(a=this.eventHandler)||void 0===a||a.dispatch("onError",I);break;case Nt.UNAUTHORIZED:I=m.create({type:d.WEBIM_CONNCTION_AUTH_ERROR,message:"Auth failed"}),null===(s=this.connectionReject)||void 0===s||s.call(this,I),null===(c=this.eventHandler)||void 0===c||c.dispatch("onError",I);break;case Nt.IM_FORBIDDEN:I=m.create({type:d.WEBIM_SERVER_SERVING_DISABLED,message:"Server serving disabled."}),null===(u=this.connectionReject)||void 0===u||u.call(this,I),null===(l=this.eventHandler)||void 0===l||l.dispatch("onError",I);break;case Nt.PERMISSION_DENIED:I="Sorry, the app month live count limit"===R?m.create({type:d.MAX_LIMIT,message:"Sorry, the monthly active user limit for this app has been reached"}):"Sorry, the app day live count limit"===R?m.create({type:d.MAX_LIMIT,message:"Sorry, the daily active user limit for this app has been reached"}):"Sorry, the app online count limit"===R?m.create({type:d.MAX_LIMIT,message:"Sorry, the maximum number limit of online users for this app has been reached"}):m.create({type:d.WEBIM_CONNCTION_AUTH_ERROR,message:"Auth failed"}),null===(p=this.connectionReject)||void 0===p||p.call(this,I),null===(h=this.eventHandler)||void 0===h||h.dispatch("onError",I);break;default:I=m.create({type:d.WEBIM_CONNCTION_AUTH_ERROR,message:R}),null===(f=this.connectionReject)||void 0===f||f.call(this,I),null===(g=this.eventHandler)||void 0===g||g.dispatch("onError",I)}this.disconnectedReason=I}}function zt(){var e,t;if((null===(e=this.unMSyncSendMsgMap)||void 0===e?void 0:e.size)>0){for(var r=Array.from(this.unMSyncSendMsgMap.keys()),o=0;o<r.length;o++){var n=this.unMSyncSendMsgMap.get(r[o]);hr.call(this,n,r[o])}null===(t=this.unMSyncSendMsgMap)||void 0===t||t.clear()}}function Jt(){var e=Bt.call(this);hr.call(this,e)}function Yt(){H.debug("sendUnreadDeal");var e=Zt.call(this);hr.call(this,e)}function Xt(){var e=this;Qt.call(this),this.heartBeatID=setInterval((function(){(Date.now()-e.lastHeartbeat)/1e3>=30&&(H.debug("send heart beat"),Yt.call(e),e.lastHeartbeat=Date.now())}),this.heartBeatWait)}function Qt(){clearInterval(this.heartBeatID)}function Zt(){var e=this.root.lookup("easemob.pb.MSync"),t=e.decode([]);return t.version=this.version,t.encryptType=this.encryptType,t.command=1,e.encode(t).finish()}function $t(e,t){return e.some((function(e){return e.name===t.name}))}function er(e){var t=this.root.lookup("easemob.pb.CommNotice").decode(e.payload),r=$t(this._queues,t.queue);H.debug("receive notice",t.queue,this._queues),r||this.clientResource===t.queue.clientResource&&t.queue.name===this.context.userId||(this._queues.push(t.queue),1===this._queues.length&&jt.call(this,t.queue))}function tr(e){return At(this,void 0,void 0,(function(){var t,r,o,n,i,a,s,c;return bt(this,(function(u){if(t=_e.getEnvInfo(),r=this.root.lookup("easemob.pb.MSync"),"miniCore"===this.name||"web"===t.platform){try{n=new Uint8Array(e.data),o=r.decode(n)}catch(e){throw new Error("decode message fail.")}return[2,o]}if("zfb"===t.platform||"dd"===t.platform){for(i=Ie().atob(e.data),a=[],s=0,c=i.length;s<c;++s)a.push(i.charCodeAt(s));return[2,r.decode(a)]}try{o=r.decode(e.data)}catch(e){throw new Error("decode message fail.")}return[2,o]}))}))}function rr(e){var t=this;this.lastHeartbeat=Date.now(),this.probingTimer&&clearTimeout(this.probingTimer),e.then((function(e){if(e)switch(e.command){case 0:nr.call(t,e);break;case 1:Gt.call(t,e);break;case 2:er.call(t,e);break;case 3:Vt.call(t,e);break;default:H.error("unexpected msync command: ".concat(e.command))}else H.error("unexpected msync result: ".concat(e))}))}var or=new Map;function nr(e){var t,r,o,n,i,a,s,c,u,l,p,h,g,E,v,y,T,O,R,S,A,b,M,U,w,L,P,D,k,x,G,B,j,F,W,K,q,V,z,J,Y,X,Q,Z,$,ee,te,re,oe,ne,ie,ae,se,ce,ue,le,de;return At(this,void 0,void 0,(function(){var pe,he,fe,me,ge,Ee,ye,Te,Ie,Oe,Re,Ce,Se,Ae,be,Me,Ue,we,Le,Pe,De,ke,xe,Ge,Be,He,je,Fe,Ke,qe,Ve,ze,Je,Ye,Xe,Qe,Ze,$e,tt,rt,ot,nt,it,at,st,ct,ut,lt=this;return bt(this,(function(dt){switch(dt.label){case 0:if(pe=(pe=this.root.lookup("easemob.pb.CommSyncDL")).decode(e.payload),he=new(f())(pe.serverId.low,pe.serverId.high,pe.serverId.unsigned).toString(),fe=new(f())(pe.metaId.low,pe.metaId.high,pe.metaId.unsigned).toString(),!(Number(fe)>0))return[3,23];if(clearTimeout(null===(t=this._msgHash[fe])||void 0===t?void 0:t.messageTimer),!pe.status)return[3,22];if(0!==pe.status.errorCode)return[3,21];if(null==(me=this._msgHash[fe])?void 0:me.isHandleChatroom){try{if(ge="join"===(null===(r=this._msgHash[fe])||void 0===r?void 0:r.operation)){Ee={};try{pe.metas.length>0&&(ye=this.root.lookup("easemob.pb.MUCBody"),Te=ye.decode(pe.metas[0].payload),(Ie=JSON.parse(Te.status.chatroomInfo||"{}")[this.user])&&(Ee={isAllMembersMuted:Ie.is_all_mute,createTimestamp:Ie.create_timestamp,isInAllowlist:Ie.is_in_white_list,memberCount:Ie.member_count,muteExpireTimestamp:Ie.mute_duration?Ie.mute_duration:-1}))}catch(e){H.error("decode chat room info error",e)}(null===(o=this._msgHash[fe])||void 0===o?void 0:o.resolve)instanceof Function&&this._msgHash[fe].resolve({type:0,data:{action:"apply",id:this._msgHash[fe].roomId,result:!0,user:this.context.userId,info:Ee}}),(null===(n=this._msgHash[fe])||void 0===n?void 0:n.success)instanceof Function&&this._msgHash[fe].success({type:0,data:{action:"apply",id:this._msgHash[fe].roomId,result:!0,user:this.context.userId,info:Ee}})}(null===(i=this._msgHash[fe])||void 0===i?void 0:i.resolve)instanceof Function&&!ge&&this._msgHash[fe].resolve({type:0,data:{result:!0}}),(null===(a=this._msgHash[fe])||void 0===a?void 0:a.success)instanceof Function&&!ge&&this._msgHash[fe].success({type:0,data:{result:!0}}),N.has(fe)&&(Oe=N.get(fe),tt=Oe.rpt,rt=Oe.requestName,tt({isEndApi:!0,data:{isSuccess:1,requestName:rt,requestMethod:"WEBSOCKET",requestUrl:this.url,code:_.success}}),N.delete(fe))}catch(e){N.has(fe)&&(Re=N.get(fe),tt=Re.rpt,rt=Re.requestName,tt({isEndApi:!0,data:{isSuccess:0,requestName:rt,requestMethod:"WEBSOCKET",requestUrl:this.url,code:_.failed,codeDesc:"when executing success function error"}}),N.delete(fe)),nt=m.create({type:d.WEBIM_CONNCTION_CALLBACK_INNER_ERROR,message:"when executing success function error",data:e}),this.onError&&this.onError(nt),null===(s=this.eventHandler)||void 0===s||s.dispatch("onError",nt)}delete this._msgHash[fe]}return!me||me.isHandleChatroom?[3,20]:(Ce=null,Se="",Ae=0,be="",Me=null,this._msgHash[fe].thirdMessage?(this._msgHash[fe].thirdMessage.id=pe.serverId,this._msgHash[fe].thirdMessage.timestamp=pe.timestamp,[4,et.call(this,this._msgHash[fe].thirdMessage,pe.status,!0,!0)]):[3,2]);case 1:Me=dt.sent(),dt.label=2;case 2:if(0===pe.metas.length)return[3,13];dt.label=3;case 3:switch(dt.trys.push([3,12,,13]),Ue=pe.metas[0],we=pe.status,Ue.ns){case 1:return[3,4];case 5:return[3,9]}return[3,10];case 4:return this.useReplacedMessageContents?[4,et.call(this,Ue,we,!0,!0)]:[3,8];case 5:return Me=dt.sent(),[4,null===(u=null===(c=this._localCache)||void 0===c?void 0:c.getInstance())||void 0===u?void 0:u.getMessageByServerMsgId(fe)];case 6:return(Le=dt.sent())?[4,null===(p=null===(l=this._localCache)||void 0===l?void 0:l.getInstance())||void 0===p?void 0:p.putMessageToDB(St(St(St({},Le),Me),{id:fe}))]:[3,8];case 7:dt.sent(),dt.label=8;case 8:return[3,11];case 9:return(Pe=_e.parseNotify(pe.metas[0].payload)).edit_msg&&(De=Pe.edit_msg,ke=De.count,xe=De.operator,Ge=De.edit_time,Be=De.sender,He=De.send_time,je=De.chat_type,Ce={operationTime:Ge,operatorId:xe,operationCount:ke},Se=Be,Ae=Number(He),be=We[je]),[3,11];case 10:return H.error("decode local meta error",Ue),[3,11];case 11:return[3,13];case 12:return Fe=dt.sent(),nt=m.create({type:d.WEBIM_LOAD_MSG_ERROR,message:"decode local meta message error",data:Fe}),this.onError&&this.onError(nt),null===(h=this.eventHandler)||void 0===h||h.dispatch("onError",nt),[3,13];case 13:C.has(fe)&&(C.get(fe).rpt({isEndApi:!0,data:{isSuccess:1,requestMethod:"WEBSOCKET",requestUrl:this.url,code:_.success,msgId:he,messageType:null!==(g=I[null==Me?void 0:Me.chatType])&&void 0!==g?g:I.singleChat}}),C.delete(fe)),dt.label=14;case 14:return dt.trys.push([14,18,,19]),Ke={localMsgId:fe,serverMsgId:he},Ce&&(qe=this._msgHash[fe],Ve=qe.editMessageId,ze=qe.ext,Je=qe.customExts,Ye=qe.customEvent,Me.modifiedInfo=Ce,Me.from=Se,Me.time=Number(Ae),Me.chatType=be,Me.id=Ve,null===(y=null===(v=null===(E=this._localCache)||void 0===E?void 0:E.getInstance())||void 0===v?void 0:v.getMessageByServerMsgId(Ve))||void 0===y||y.then((function(e){var t,r,o,n,i,a;e&&("txt"===e.type?null===(r=null===(t=lt._localCache)||void 0===t?void 0:t.getInstance())||void 0===r||r.putMessageToDB(St(St({},e),{msg:Me.msg,modifiedInfo:Me.modifiedInfo,translations:Me.translations,ext:ze?Me.ext:e.ext})):"custom"===e.type?null===(n=null===(o=lt._localCache)||void 0===o?void 0:o.getInstance())||void 0===n||n.putMessageToDB(St(St({},e),{ext:ze?Me.ext:e.ext,customExts:Je,customEvent:Ye,modifiedInfo:Me.modifiedInfo})):null===(a=null===(i=lt._localCache)||void 0===i?void 0:i.getInstance())||void 0===a||a.putMessageToDB(St(St({},e),{ext:ze?Me.ext:e.ext,modifiedInfo:Me.modifiedInfo})))}))),Me&&(Ce?("txt"===Me.type&&(Ke.message=Me),Ke.modifiedInfo=Ce):Ke.message=Me),[4,null===(O=null===(T=this._localCache)||void 0===T?void 0:T.getInstance())||void 0===O?void 0:O.updateLocalMessage(fe,{serverMsgId:he,status:Ne.SUCCESS})];case 15:return dt.sent(),(null===(R=this._msgHash[fe])||void 0===R?void 0:R.success)instanceof Function?[4,this._msgHash[fe].success(fe,he)]:[3,17];case 16:dt.sent(),dt.label=17;case 17:return(null===(S=this._msgHash[fe])||void 0===S?void 0:S.resolve)instanceof Function&&this._msgHash[fe].resolve(Ke),[3,19];case 18:return Xe=dt.sent(),nt=m.create({type:d.WEBIM_CONNCTION_CALLBACK_INNER_ERROR,message:"when executing success function error",data:Xe}),this.onError&&this.onError(nt),null===(A=this.eventHandler)||void 0===A||A.dispatch("onError",nt),[3,19];case 19:this.onReceivedMessage&&this.onReceivedMessage({id:fe,mid:he,to:this._msgHash[fe].to,time:0}),null===(b=this.eventHandler)||void 0===b||b.dispatch("onReceivedMessage",{id:fe,mid:he,to:this._msgHash[fe].to}),delete this._msgHash[fe],dt.label=20;case 20:return[3,22];case 21:if(15===pe.status.errorCode)(null===(M=this._msgHash[fe])||void 0===M?void 0:M.fail)instanceof Function&&this._msgHash[fe].fail({type:d.SERVICE_NOT_ALLOW_MESSAGING_MUTE,reason:"you were muted"}),this._msgHash[fe].reject instanceof Function&&this._msgHash[fe].reject({type:d.SERVICE_NOT_ALLOW_MESSAGING_MUTE,reason:"you were muted"}),null===(w=null===(U=this._localCache)||void 0===U?void 0:U.getInstance())||void 0===w||w.updateLocalMessage(fe,{serverMsgId:he,status:Ne.FAIL});else if(1===pe.status.errorCode){switch(Qe=void 0,pe.status.reason){case"blocked":Qe=d.PERMISSION_DENIED;break;case"group not found":Qe=d.GROUP_NOT_EXIST;break;case"not in group or chatroom":Qe=d.GROUP_NOT_JOINED;break;case"exceed recall time limit":Qe=d.MESSAGE_RECALL_TIME_LIMIT;break;case"message recall disabled":Qe=d.SERVICE_NOT_ENABLED;break;case"not in group or chatroom white list":Qe=d.SERVICE_NOT_ALLOW_MESSAGING;break;case"nonroster":Qe=d.USER_NOT_FRIEND,pe.status.reason="not contact";break;case"group is disabled":Qe=d.GROUP_IS_DISABLED,pe.status.reason="group is disabled";break;case"limit directed users":Qe=d.MAX_LIMIT;break;case"Sorry, edit limit reached":Qe=d.MAX_LIMIT,pe.status.reason="Modify message limit reached";break;case"Sorry, message does not exist":Qe=d.MODIFY_MESSAGE_NOT_EXIST,pe.status.reason="The message does not exist.";break;case"Sorry, You do not have permission":Qe=d.PERMISSION_DENIED,pe.status.reason="You do not have the modified permission.";break;case"Sorry, format is incorrect":Qe=d.MODIFY_MESSAGE_FORMAT_ERROR,pe.status.reason="The modify messaged format error.";break;case"Sorry, edit is not available":Qe=d.SERVICE_NOT_ENABLED,pe.status.reason="The message modify function is not activated.";break;case"Sorry, edit fail":Qe=d.MODIFY_MESSAGE_FAILED,pe.status.reason="Modify message failed.";break;default:pe.status.reason.includes("grpID")&&pe.status.reason.includes("does not exist!")?(Qe=d.CHATROOM_NOT_EXIST,pe.status.reason="The chat room dose not exist."):pe.status.reason.includes("username")&&pe.status.reason.includes("doesn't exist!")?Qe=d.USER_NOT_FOUND:"group member list is full!"===pe.status.reason?Qe=d.CHATROOM_MEMBERS_FULL:pe.status.reason.includes("can not join this group")&&pe.status.reason.includes("is in the blacklist")?(Qe=d.PERMISSION_DENIED,pe.status.reason="permission denied"):"can not operate this group, reason: group is disabled"===pe.status.reason?Qe=d.GROUP_IS_DISABLED:pe.status.reason.includes("moderation")?Qe=d.MESSAGE_MODERATION_BLOCKED:pe.status.reason.includes("group ID illegal, please check it")?(Qe=d.REQUEST_PARAMETER_ERROR,pe.status.reason="Invalid parameter"):Qe=pe.status.reason.includes("has create too many chatrooms")||pe.status.reason.includes("has joined too many chatrooms")?d.MAX_LIMIT:pe.status.reason.includes("auto create failed")?d.SERVER_BUSY:d.SERVER_UNKNOWN_ERROR}this._msgHash[fe]&&((null===(L=this._msgHash[fe])||void 0===L?void 0:L.isHandleChatroom)?(ot=m.create({type:Qe,message:pe.status.reason||"",data:""}),this._msgHash[fe].reject instanceof Function&&this._msgHash[fe].reject(ot),this._msgHash[fe].error instanceof Function&&this._msgHash[fe].error(ot),N.has(fe)&&(Ze=_.failed,Qe===d.CHATROOM_NOT_EXIST?Ze=_.notFound:Qe===d.CHATROOM_MEMBERS_FULL&&(Ze=_.reachLimit),$e=N.get(fe),tt=$e.rpt,rt=$e.requestName,tt({isEndApi:!0,data:{isSuccess:0,requestName:rt,requestMethod:"WEBSOCKET",requestUrl:this.url,code:Ze,codeDesc:pe.status.reason}}),N.delete(fe))):(ot=m.create({type:Qe,message:pe.status.reason||"",data:{id:fe,mid:he}}),this._msgHash[fe].reject instanceof Function&&this._msgHash[fe].reject(ot),this._msgHash[fe].fail instanceof Function&&this._msgHash[fe].fail({type:Qe,reason:pe.status.reason?pe.status.reason:"",data:{id:fe,mid:he}}),null===(D=null===(P=this._localCache)||void 0===P?void 0:P.getInstance())||void 0===D||D.updateLocalMessage(fe,{serverMsgId:he,status:Ne.FAIL})),delete this._msgHash[fe])}else if(7===pe.status.errorCode)"sensitive words"===pe.status.reason&&this._msgHash[fe]?(ot=m.create({type:d.MESSAGE_INCLUDE_ILLEGAL_CONTENT,message:"sensitive words",data:{id:fe,mid:he}}),this._msgHash[fe].reject instanceof Function&&this._msgHash[fe].reject(ot),this._msgHash[fe].fail instanceof Function&&this._msgHash[fe].fail({type:d.MESSAGE_INCLUDE_ILLEGAL_CONTENT,data:{id:fe,mid:he,reason:"sensitive words"}}),null===(x=null===(k=this._localCache)||void 0===k?void 0:k.getInstance())||void 0===x||x.updateLocalMessage(fe,{serverMsgId:he,status:Ne.FAIL})):"blocked by mod_antispam"===pe.status.reason&&this._msgHash[fe]?(ot=m.create({type:d.MESSAGE_INCLUDE_ILLEGAL_CONTENT,message:"blocked by mod_antispam",data:{id:fe,mid:he}}),this._msgHash[fe].reject instanceof Function&&this._msgHash[fe].reject(ot),this._msgHash[fe].fail instanceof Function&&this._msgHash[fe].fail({type:d.MESSAGE_INCLUDE_ILLEGAL_CONTENT,data:{id:fe,mid:he,reason:"blocked by mod_antispam"}}),null===(B=null===(G=this._localCache)||void 0===G?void 0:G.getInstance())||void 0===B||B.updateLocalMessage(fe,{serverMsgId:he,status:Ne.FAIL})):"user is mute"===pe.status.reason&&this._msgHash[fe]?(ot=m.create({type:d.USER_MUTED_BY_ADMIN,message:"user is mute",data:{id:fe,mid:he}}),this._msgHash[fe].reject instanceof Function&&this._msgHash[fe].reject(ot),this._msgHash[fe].fail instanceof Function&&this._msgHash[fe].fail(ot),null===(F=null===(j=this._localCache)||void 0===j?void 0:j.getInstance())||void 0===F||F.updateLocalMessage(fe,{serverMsgId:he,status:Ne.FAIL})):"traffic limit"===pe.status.reason&&this._msgHash[fe]?(ot=m.create({type:d.MESSAGE_CURRENT_LIMITING,message:"traffic limit",data:{id:fe,mid:he}}),this._msgHash[fe].reject instanceof Function&&this._msgHash[fe].reject(ot),this._msgHash[fe].fail instanceof Function&&this._msgHash[fe].fail(ot),null===(K=null===(W=this._localCache)||void 0===W?void 0:W.getInstance())||void 0===K||K.updateLocalMessage(fe,{serverMsgId:he,status:Ne.FAIL})):"Sorry, data is too large"===pe.status.reason&&this._msgHash[fe]&&(ot=m.create({type:d.MESSAGE_SIZE_LIMIT,message:"Sorry, data is too large",data:{id:fe,mid:he}}),this._msgHash[fe].reject instanceof Function&&this._msgHash[fe].reject(ot),this._msgHash[fe].fail instanceof Function&&this._msgHash[fe].fail(ot),null===(V=null===(q=this._localCache)||void 0===q?void 0:q.getInstance())||void 0===V||V.updateLocalMessage(fe,{serverMsgId:he,status:Ne.FAIL}));else if(19===pe.status.errorCode)this._msgHash[fe]&&(C.has(fe)&&(C.get(fe).rpt({isEndApi:!0,data:{isSuccess:0,requestMethod:"WEBSOCKET",requestUrl:this.url,code:d.MESSAGE_EXTERNAL_LOGIC_BLOCKED,codeDesc:pe.status.reason||"",msgId:he,messageType:null!==(J=I[null===(z=this._msgHash[fe])||void 0===z?void 0:z.chatType])&&void 0!==J?J:I.singleChat}}),C.delete(fe)),ot=m.create({type:d.MESSAGE_EXTERNAL_LOGIC_BLOCKED,message:pe.status.reason||"",data:{id:fe,mid:he}}),this._msgHash[fe].reject instanceof Function&&this._msgHash[fe].reject(ot),this._msgHash[fe].fail instanceof Function&&this._msgHash[fe].fail({type:d.MESSAGE_EXTERNAL_LOGIC_BLOCKED,data:{id:fe,mid:he,reason:pe.status.reason}}),null===(X=null===(Y=this._localCache)||void 0===Y?void 0:Y.getInstance())||void 0===X||X.updateLocalMessage(fe,{serverMsgId:he,status:Ne.FAIL}));else if(this._msgHash[fe]){C.has(fe)&&(C.get(fe).rpt({isEndApi:!0,data:{isSuccess:0,requestMethod:"WEBSOCKET",requestUrl:this.url,code:d.WEBIM_LOAD_MSG_ERROR,codeDesc:(null===(Q=pe.status)||void 0===Q?void 0:Q.reason)||"",msgId:he,messageType:null!==($=I[null===(Z=this._msgHash[fe])||void 0===Z?void 0:Z.chatType])&&void 0!==$?$:I.singleChat}}),C.delete(fe));try{ot=m.create({type:d.WEBIM_LOAD_MSG_ERROR,message:(null===(ee=pe.status)||void 0===ee?void 0:ee.reason)||"",data:{id:fe,mid:he,reason:pe.status&&pe.status.reason}}),this._msgHash[fe].reject instanceof Function&&this._msgHash[fe].reject(ot),null===(re=null===(te=this._localCache)||void 0===te?void 0:te.getInstance())||void 0===re||re.updateLocalMessage(fe,{serverMsgId:he,status:Ne.FAIL}),this._msgHash[fe].fail instanceof Function&&this._msgHash[fe].fail({type:d.WEBIM_LOAD_MSG_ERROR,data:{errorCode:pe.status&&pe.status.errorCode,reason:pe.status&&pe.status.reason}})}catch(e){nt=m.create({type:d.WEBIM_CONNCTION_CALLBACK_INNER_ERROR,message:"when executing fail function error",data:e}),this.onError&&this.onError(nt),null===(oe=this.eventHandler)||void 0===oe||oe.dispatch("onError",nt)}delete this._msgHash[fe]}else C.has(fe)&&(C.get(fe).rpt({isEndApi:!0,data:{isSuccess:0,requestMethod:"WEBSOCKET",requestUrl:this.url,code:d.WEBIM_CONNCTION_CALLBACK_INNER_ERROR,codeDesc:"on message error",msgId:he,messageType:null!==(ie=I[null===(ne=this._msgHash[fe])||void 0===ne?void 0:ne.chatType])&&void 0!==ie?ie:I.singleChat}}),C.delete(fe)),nt=m.create({type:d.WEBIM_CONNCTION_CALLBACK_INNER_ERROR,message:"on message error"}),this.onError&&this.onError(nt),null===(ae=this.eventHandler)||void 0===ae||ae.dispatch("onError",nt);dt.label=22;case 22:return[2];case 23:if(pe.metas&&0!==pe.metas.length){this._offlineMessagePullState!==ve.SYNC_FINISH&&or.set(pe.queue.name,pe.metas.length);try{Dt.call(this,pe.metas,pe.status)}catch(e){nt=m.create({type:d.WEBIM_LOAD_MSG_ERROR,message:"decode message error",data:e}),H.error("decode message error",e),this.onError&&this.onError(nt),null===(se=this.eventHandler)||void 0===se||se.dispatch("onError",nt)}finally{pe.isLast?(H.debug("metas & CommSyncDLMessage.isLast",pe.isLast),it=-1,this._queues.some((function(e,t){return e.name===pe.name&&(it=t,!0)}))&&it>0&&this._queues.splice(it,1),this._queues.length>0&&jt.call(this,this._queues[0]),xt.call(this,pe)):pe.nextKey&&(ct=new(f())(pe.nextKey.low,pe.nextKey.high,pe.nextKey.unsigned).toString(),H.debug("nexKey:",ct,"this.nextKey:",this.nexKey),ct!==this.nexKey||(null===(ce=pe.queue)||void 0===ce?void 0:ce.name)!==this.currentQueue?(this.nexKey=ct,this.currentQueue=null===(ue=pe.queue)||void 0===ue?void 0:ue.name,qt.call(this,pe.nextKey,pe.queue)):(H.debug("nexKey and queue are same, delete it"),at=-1,this._queues.some((function(e,t){return e.name===pe.queue.name&&(at=t,!0)}))&&this._queues.splice(at,1),this._queues.length>0&&jt.call(this,this._queues[0])))}}else pe.isLast?(H.debug("CommSyncDLMessage.isLast",pe.isLast),st=-1,this._queues.some((function(e,t){return e.name===pe.queue.name&&(st=t,!0)}))&&this._queues.splice(st,1),this._queues.length>0&&jt.call(this,this._queues[0]),xt.call(this,pe)):pe.nextKey&&(ct=new(f())(pe.nextKey.low,pe.nextKey.high,pe.nextKey.unsigned).toString(),H.debug("nexKey:",ct,"this.nextKey:",this.nexKey),ct!==this.nexKey||(null===(le=pe.queue)||void 0===le?void 0:le.name)!==this.currentQueue?(this.nexKey=ct,this.currentQueue=null===(de=pe.queue)||void 0===de?void 0:de.name,qt.call(this,pe.nextKey,pe.queue)):(H.debug("nexKey and queue are same, delete it"),ut=-1,this._queues.some((function(e,t){return e.name===pe.queue.name&&(ut=t,!0)}))&&this._queues.splice(ut,1),this._queues.length>0&&jt.call(this,this._queues[0])));return[2]}}))}))}function ir(e){var t=[],r=this.root.lookup("easemob.pb.KeyValue"),o=[];for(var n in e){var i=r.decode(t);if(i.key=n,void 0!==e[n]){if("object"==typeof e[n])i.type=8,i.stringValue=JSON.stringify(e[n]);else if("string"==typeof e[n])i.type=7,i.stringValue=e[n];else if("boolean"==typeof e[n])i.type=1,i.varintValue=!0===e[n]?1:0;else if(Number.isInteger(e[n]))i.type=2,i.varintValue=e[n];else if("bigint"==typeof e[n]||"symbol"==typeof e[n]||"function"==typeof e[n]||Number.isNaN(e[n]))i.type=7,i.stringValue=e[n].toString();else{if("number"!=typeof e[n]||Number.isInteger(e[n]))continue;i.type=6,i.doubleValue=e[n]}o.push(i)}}return o}function ar(e){var t,r,o,n=[];if(this.root){var i,a=this.root.lookup("easemob.pb.MessageBody.Content").decode(n);switch(i=!e.group&&"groupchat"!==(null===(t=null==e?void 0:e.chatType)||void 0===t?void 0:t.toLowerCase())||e.roomType?e.group&&e.roomType||"chatroom"===(null===(r=null==e?void 0:e.chatType)||void 0===r?void 0:r.toLowerCase())?"chatRoom":"singleChat":"groupChat",e.type){case"txt":a.type=0,a.text=e.msg;break;case"img":a.type=1,e.body?(a.displayName=e.body.filename,a.remotePath=e.body.url,a.secretKey=e.body.secret,a.fileLength=e.body.file_length,a.size=e.body.size,a.thumbnailDisplayName=e.body.filename):e.file?(a.displayName=e.file.filename,a.remotePath=e.file.url,a.secretKey=e.file.secret,a.fileLength=e.file.file_length,a.size=e.file.size,a.thumbnailDisplayName=e.file.filename):(a.displayName=e.filename,a.remotePath=e.url,a.secretKey=e.secret,a.fileLength=e.file_length,a.size=e.size,a.thumbnailDisplayName=e.filename),e.isBuildCombinedMsg&&(a.remotePath=e.url,a.secretKey=e.secret,a.size={height:e.height,width:e.width}),e.isGif&&(a.subType=1);break;case"video":a.type=2,e.body?(a.displayName=e.body.filename,a.remotePath=e.body.url,a.secretKey=e.body.secret,a.fileLength=e.body.file_length,a.duration=e.body.length,a.thumbnailDisplayName=e.body.filename,a.thumbnailRemotePath=this.useOwnUploadFun?"":"".concat(e.body.url,"?vframe=true"),a.thumbnailSecretKey=this.useOwnUploadFun?"":e.body.secret):e.isBuildCombinedMsg&&(a.displayName=e.filename,a.remotePath=e.url,a.secretKey=e.secret,a.fileLength=e.file_length,a.duration=e.length,a.thumbnailDisplayName=e.filename,a.thumbnailRemotePath=e.thumb,a.thumbnailSecretKey=e.thumb_secret);break;case"loc":a.type=3,a.latitude=e.lat,a.longitude=e.lng,a.address=e.addr,a.buildingName=e.buildingName,a.latitude=e.lat;break;case"audio":a.type=4,e.body?(a.displayName=e.body.filename,a.remotePath=e.body.url,a.secretKey=e.body.secret,a.fileLength=e.body.file_length,a.duration=e.body.length,a.thumbnailDisplayName=e.body.filename):e.isBuildCombinedMsg&&(a.displayName=e.filename,a.remotePath=e.url,a.secretKey=e.secret,a.fileLength=e.file_length,a.duration=e.length,a.thumbnailDisplayName=e.filename);break;case"file":a.type=5,e.body?(a.displayName=e.body.filename,a.remotePath=e.body.url,a.secretKey=e.body.secret,a.fileLength=e.body.file_length,a.thumbnailDisplayName=e.body.filename):e.isBuildCombinedMsg&&(a.displayName=e.filename,a.remotePath=e.url,a.secretKey=e.secret,a.fileLength=e.file_length,a.thumbnailDisplayName=e.filename);break;case"cmd":a.type=6,a.action=e.action;break;case"custom":a.type=7,a.customEvent=e.customEvent,a.customExts=ir.call(this,e.customExts);break;case"combine":a.type=0,a.subType=0,a.text=e.compatibleText,a.displayName=e.filename,a.remotePath=e.url,a.secretKey=e.secret,a.fileLength=e.file_length,a.title=e.title,a.summary=e.summary,a.combineLevel=e.combineLevel}var s=[];e.ext&&(s=ir.call(this,e.ext));var c=this.root.lookup("easemob.pb.MessageBody"),u=c.decode(n),l=e.from||this.context.jid.name;u.from={name:e.isBuildCombinedMsg?l:this.context.jid.name};var p=e.to.indexOf("/"),h=p>-1?e.to.substring(0,p):e.to;if(u.to={name:h},"channel"===e.type)u.type=Ot.CHANNEL_ACK;else if("recall"===e.type)u.type=Ot.RECALL,u.ackMessageId=e.ackId;else if("delivery"===e.type)u.type=Ot.DELIVER_ACK,u.ackMessageId=e.ackId;else if("read"===e.type)u.type=Ot.READ_ACK,u.ackMessageId=e.ackId,"groupChat"===i&&(u.msgConfig={allowGroupAck:!0},u.ackContent=e.ackContent);else if("chatRoom"===i)u.type=Ot.CHATROOM;else if("groupChat"===i){if(u.type=Ot.GROUPCHAT,e.msgConfig){var m=e.msgConfig.allowGroupAck;u.msgConfig={allowGroupAck:!!m}}}else"singleChat"===i&&(u.type=Ot.SINGLECHAT);e.editMessageId&&(u.type=Ot.EDIT,u.editMessageId=e.editMessageId,u.editScope=Rt.BODY,e.ext&&(u.editScope=Rt.BODY_AND_EXT)),u.contents=[a],u.ext=s;var g=function(e){var t={};return"translations"in e&&(t.translations=e.translations),"isChatThread"in e&&e.isChatThread&&(t.thread={}),Object.keys(t).length>0?JSON.stringify(t):""}(e);g&&(u.meta=g),u=c.encode(u).finish();var E=this.root.lookup("easemob.pb.Meta"),v=E.decode(n);v.id=e.id;var y="easemob.com";if("chatRoom"!==i&&"groupChat"!==i||(y="conference.easemob.com"),v.to={appKey:this.appKey,name:h,domain:y},p>-1&&(v.to.clientResource=e.to.substring(p+1,e.to.length)),"chatRoom"===i&&(v.ext=ir.call(this,function(e){return{chatroom_msg_tag:"high"===e.priority?0:"low"===e.priority?2:1}}(e))),"recall"===e.type&&e.metaExt&&(v.ext=ir.call(this,{recallMessageExtensionInfo:e.metaExt})),v.ns=1,v.payload=u,v.routetype=e.deliverOnlineOnly?1:0,"singleChat"!==i&&Array.isArray(e.receiverList)&&(null===(o=e.receiverList)||void 0===o?void 0:o.length)>0&&(v.directedUsers=e.receiverList,v.routetype=2),e.isBuildCombinedMsg)return v.timestamp=f().fromValue(e.time),E.encode(v).finish();var _=this.root.lookup("easemob.pb.CommSyncUL"),T=_.decode(n);T.meta=v,!e.isPing&&cr.call(this,e,v),T=_.encode(T).finish();var I=this.root.lookup("easemob.pb.MSync"),O=I.decode(n);return O.version=this.version,O.encryptType=this.encryptType,O.command=0,O.payload=T,I.encode(O).finish()}e.fail&&e.fail({type:d.WEBIM_CONNCTION_CLIENT_OFFLINE,message:"Not logged in"})}function sr(e){var t,r,o=this,n=St({},e);if(n.editMessageId){var i=ar.call(this,n);hr.call(this,i)}else{if(e.file)return n.accessToken=this.token,n.appKey=this.appKey,n.apiUrl=this.apiUrl,n.body&&n.body.url?ar.call(this,n):n.isGif&&"image/gif"!==n.file.data.type?void this._msgHash[n.id].reject({type:d.MESSAGE_INCLUDE_ILLEGAL_CONTENT,message:"The file type is not gif."}):new Promise((function(t,r){var i=n.onFileUploadComplete;n.onFileUploadComplete=function(t){var r,a,s,c,u,l,p;if(t.entities){if(t.entities[0]["file-metadata"]){var h=t.entities[0]["file-metadata"]["content-length"];n.file_length=h,n.filetype=t.entities[0]["file-metadata"]["content-type"],h>204800&&(n.thumbnail=!0)}var f="".concat(o.apiUrl,"/").concat(o.orgName,"/").concat(o.appName,"/chatfiles/").concat(t.entities[0].uuid);n.body={type:n.type||"file",secret:t.entities[0]["share-secret"],filename:n.file.filename||n.filename,url:f,length:n.length||0,filetype:n.filetype||n.file.filetype,file_length:(null===(s=null===(a=n.file)||void 0===a?void 0:a.data)||void 0===s?void 0:s.size)||0,size:{width:n.width||0,height:n.height||0}},n.file.url=t.uri,e.secret=t.entities[0]["share-secret"],t.url=e.url="".concat(f,"?em-redirect=true&share-secret=").concat(t.entities[0]["share-secret"]),e.file_length=n.file_length=(null===(u=null===(c=n.file)||void 0===c?void 0:c.data)||void 0===u?void 0:u.size)||0,"img"===n.type&&(e.thumb="".concat(e.url,"&thumbnail=true"),t.thumb="".concat(t.url,"&thumbnail=true")),"video"===n.type&&(e.thumb="".concat(e.url,"&vframe=true"),e.thumb_secret=t.entities[0]["share-secret"],t.thumb="".concat(t.url,"&vframe=true")),i instanceof Function&&i(t,n.id);var m=ar.call(o,n);C.size<=S&&C.set(n.id,{rpt:o.dataReport.geOperateFun({operationName:E.SEND_MSG})}),null===(p=null===(l=o._localCache)||void 0===l?void 0:l.getInstance())||void 0===p||p.storeMessage(e,Ne.INPROGRESS),hr.call(o,m)}else null===(r=o._msgHash[n.id])||void 0===r||r.reject({type:d.WEBIM_UPLOADFILE_ERROR,message:"Failed to upload the file",data:t})},n.onFileUploadError=function(e){var t;n.onFileUploadError instanceof Function&&n.onFileUploadError(e),null===(t=o._msgHash[n.id])||void 0===t||t.reject({type:d.WEBIM_UPLOADFILE_ERROR,message:e.message||"Failed to upload the file",data:e})},_e.uploadFile.call(o,n,E.UPLOAD_MSG_ATTACH)}));if("combine"===e.type){n.accessToken=this.token,n.appKey=this.appKey,n.apiUrl=this.apiUrl;var a=n.onFileUploadComplete,s=n.onFileUploadError;return new Promise((function(t,r){var i,c,u,l;if((null===(i=e.messageList)||void 0===i?void 0:i.length)>300||0===(null===(c=e.messageList)||void 0===c?void 0:c.length))return o._msgHash[n.id].reject({type:d.MAX_LIMIT,message:"The number of combined messages exceeded the limit."});var p=function(e,t,r){if(r||2===arguments.length)for(var o,n=0,i=t.length;n<i;n++)!o&&n in t||(o||(o=Array.prototype.slice.call(t,0,n)),o[n]=t[n]);return e.concat(o||Array.prototype.slice.call(t))}([],e.messageList,!0),h=0;if(p.forEach((function(e){(null==e?void 0:e.combineLevel)>h&&(h=null==e?void 0:e.combineLevel)})),n.combineLevel=h+1,n.combineLevel>10)return o._msgHash[n.id].reject({type:d.MAX_LIMIT,message:"The level of the merged message exceeded the limit."});var f=ur.call(o,p),m=function(t,r){var i,s,c,u,l,d,p="".concat(o.apiUrl,"/").concat(o.orgName,"/").concat(o.appName,"/chatfiles/").concat(t.entities[0].uuid),h=null===(i=t.entities[0])||void 0===i?void 0:i["share-secret"],f="".concat(p,"?em-redirect=true");h&&(f="".concat(f,"&share-secret=").concat(h)),a instanceof Function&&a({url:f,secret:h}),e.url=f,e.secret=h,n.url=f,n.secret=t.entities[0]["share-secret"],n.filename=(null===(s=n.file)||void 0===s?void 0:s.filename)||(null==r?void 0:r.fileName),n.file_length=(null===(u=null===(c=n.file)||void 0===c?void 0:c.data)||void 0===u?void 0:u.size)||(null==r?void 0:r.fileLength)||0,null===(d=null===(l=o._localCache)||void 0===l?void 0:l.getInstance())||void 0===d||d.storeMessage(e,Ne.INPROGRESS);var m=ar.call(o,n);hr.call(o,m)},g=_e.getEnvInfo();if("web"!==g.platform&&"node"!==g.platform&&"quick_app"!==g.platform&&(null===(l=null===(u=g.global)||void 0===u?void 0:u.canIUse)||void 0===l?void 0:l.call(u,"getFileSystemManager"))){var v=g.global.getFileSystemManager(),y="".concat(o.apiUrl,"/").concat(o.orgName,"/").concat(o.appName,"/chatfiles");v.writeFile({filePath:"".concat(g.global.env.USER_DATA_PATH,"/combine"),data:f.buffer,encoding:"binary",success:function(e){g.global.uploadFile({url:y,filePath:"".concat(g.global.env.USER_DATA_PATH,"/combine"),name:"file",header:{"Content-Type":"multipart/form-data",Authorization:"Bearer "+n.accessToken},success:function(e){if(200===e.statusCode){H.debug("upload success",e);var t=JSON.parse(e.data);m(t,{fileName:"combine",fileLength:f.length})}else H.debug("upload fail"),n.onFileUploadError instanceof Function&&n.onFileUploadError(e),this._msgHash[n.id].reject({type:d.WEBIM_UPLOADFILE_ERROR,message:"Failed to upload the merge message.Please try again",data:e})},fail:function(t){H.debug("upload fail"),n.onFileUploadError instanceof Function&&n.onFileUploadError(e),this._msgHash[n.id].reject({type:d.WEBIM_UPLOADFILE_ERROR,message:"Failed to upload the merge message.Please try again",data:t})}})},fail:function(e){H.debug("write file fail",e),this._msgHash[n.id].reject({type:d.WEBIM_UPLOADFILE_ERROR,message:"Failed to upload the merge message.Please try again",data:e})}})}else{var _=new File([f],"combine",{type:"application/octet-stream"}),T={url:URL.createObjectURL(_),filename:n.id,data:_};n.file=T,n.onFileUploadComplete=function(e){m(e)},n.onFileUploadError=function(e){s instanceof Function&&s(e),o._msgHash[n.id].reject({type:d.WEBIM_UPLOADFILE_ERROR,message:"Failed to upload the merge message.Please try again",data:e})},_e.uploadFile.call(o,n,E.UPLOAD_MSG_ATTACH)}}))}"img"===e.type&&(n.body||(n.body=St(St({},n),{size:{width:n.width||0,height:n.height||0}}))),null===(r=null===(t=this._localCache)||void 0===t?void 0:t.getInstance())||void 0===r||r.storeMessage(e,Ne.INPROGRESS),i=ar.call(this,n),hr.call(this,i)}}function cr(e,t){if(e.editMessageId)return t.ignoreCallback=!0,void(this._msgHash[e.id].thirdMessage=t);Mt.includes(e.type)&&(this._msgHash[e.id].thirdMessage=t)}function ur(e){for(var t=Uint8Array.from("cm",(function(e){return e.charCodeAt(0)})),r=0;r<e.length;r++){for(var o=e[r],n=St(St({},o),{isBuildCombinedMsg:!0}),i=this.mSync.encodeChatMsg.call(this,n),a=i.length,s=new Uint8Array(4),c=0;c<4;c++)s[c]=a>>8*(3-c)&255;H.debug("message length:",s);var u=new Uint8Array(t.length+s.length+i.length);u.set(t),u.set(s,t.length),u.set(i,t.length+s.length),t=u}var l=new Uint8Array(t.length+1),d=0;for(r=2;r<t.length;r++)r%2==1&&(d^=t[r]);return l.set(t),H.debug("checkResult:",d),l[t.length]=d,l}function lr(e,t,r){var o=[],n=this.root.lookup("easemob.pb.MUCBody"),i=e.roomId,a=e.ext,s=void 0===a?"":a,c=e.leaveOtherRooms,u=void 0!==c&&c,l=n.decode(o);l.mucId={appKey:this.appKey,name:i,domain:"conference.easemob.com"},l.operation="join"===r?2:3,l.from={name:this.context.userId},l.isChatroom=!0,"join"===r&&(l.ext=s,l.leaveOtherRooms=u),l=n.encode(l).finish();var d=this.root.lookup("easemob.pb.Meta").decode(o);d.id=t,d.from={appKey:this.appKey,name:this.context.userId,domain:"easemob.com",client_resource:this.context.jid.clientResource},d.to={domain:"easemob.com"},d.ns=2,d.payload=l;var p=this.root.lookup("easemob.pb.CommSyncUL"),h=p.decode(o);h.meta=d,h=p.encode(h).finish();var f=this.root.lookup("easemob.pb.MSync"),m=f.decode(o);return m.version=this.version,m.encryptType=this.encryptType,m.command=0,m.payload=h,f.encode(m).finish()}function dr(e,t){var r=_e.getUniqueId(),o=lr.call(this,e,r,t),n="join"===t?E.JOIN_CHATROOM:E.QUIT_CHATROOM,i=this.dataReport.geOperateFun({operationName:n});return N.size<=S&&N.set(r,{rpt:i,requestName:n}),hr.call(this,St(St({},e),{isHandleChatroom:!0,joinMsg:o,id:r,operation:t}),r)}function pr(e){var t=this;return new Promise((function(r,o){var n,i,a,s,c,u;if(H.debug("call send"),t.logOut)return H.debug("send message failed",d.WEBIM_CONNECTION_CLOSED),o({type:d.WEBIM_CONNECTION_CLOSED,message:"not login"});if(!e.id||"string"!=typeof e.id||""===e.id)return o({type:d.MESSAGE_PARAMETER_ERROR,message:'Missing required parameter: "id"'});if(!e.to||"string"!=typeof e.to||""===e.to)return o({type:d.MESSAGE_PARAMETER_ERROR,message:'Missing required parameter: "to"'});var l="file"===e.type||"img"===e.type||"audio"===e.type||"video"===e.type,p="delivery"===e.type||"read"===e.type||"channel"===e.type,h="cmd"===e.type,f="recall"===e.type,m=null==e?void 0:e.editMessageId,g=!(p||f||h||m);if(e.id){if(!l&&!p||l&&t.useOwnUploadFun){var v="recall"===e.type?E.SEND_RECALL_MSG:E.SEND_MSG;m&&(v=E.MODIFY_MESSAGE),C.size<=S&&C.set(e.id,{rpt:t.dataReport.geOperateFun({operationName:v})})}g&&(null===(i=null===(n=t._localCache)||void 0===n?void 0:n.getInstance())||void 0===i||i.storeMessage(e,Ne.CREATE)),t._msgHash[e.id]=St(St({},e),{resolve:r,reject:o})}if(l||"combine"===e.type)return sr.call(t,e);if("txt"===e.type&&(null===(a=e.msgConfig)||void 0===a?void 0:a.languages)&&Array.isArray(null===(s=e.msgConfig)||void 0===s?void 0:s.languages)&&e.msgConfig.languages.length>0){var y=t.translateMessage||t.translation.translateMessage;if(!y)throw new Error("there is no method to translate message");y.call(t,{text:e.msg,languages:e.msgConfig.languages}).then((function(r){var o,n,i,a=null===(o=r.data[0])||void 0===o?void 0:o.translations;a=a.map((function(e){return{code:e.to,text:e.text}})),e.translations=a,t._msgHash[e.id].translations=a,null===(i=null===(n=t._localCache)||void 0===n?void 0:n.getInstance())||void 0===i||i.storeMessage(e,Ne.INPROGRESS);var s=ar.call(t,e);hr.call(t,s,e.id)})).catch((function(e){o(e)}))}else{g&&(null===(u=null===(c=t._localCache)||void 0===c?void 0:c.getInstance())||void 0===u||u.storeMessage(e,Ne.INPROGRESS));var _=ar.call(t,e);hr.call(t,_,e.id)}}))}function hr(e,t){var r,o=this;if(e.isHandleChatroom){if(!this.isOpened()){var n={data:"",type:d.WEBIM_CONNCTION_DISCONNECTED,message:"websocket has been disconnected"};if(N.has(e.id)){var i=N.get(e.id);(0,i.rpt)({isEndApi:!0,data:{isSuccess:0,requestName:i.requestName,requestMethod:"WEBSOCKET",requestUrl:this.url,code:_.disconnect,codeDesc:"websocket has been disconnected"}}),N.delete(e.id)}return Promise.reject(n)}return new Promise((function(r,n){var i;o._msgHash[e.id]=St(St({},e),{resolve:r,reject:n});var a=_e.getEnvInfo();i="miniCore"===o.name||"web"===a.platform?e.joinMsg:Lt.call(o,e.joinMsg,t);var s=t&&setTimeout((function(){var t,r,n;if(o._msgHash[e.id]){var i={type:d.REQUEST_TIMEOUT,message:"request timeout"};null===(r=(t=o._msgHash[e.id]).reject)||void 0===r||r.call(t,i),clearTimeout(null===(n=o._msgHash[e.id])||void 0===n?void 0:n.messageTimer),delete o._msgHash[e.id],o.reconnecting||o.reconnect(!0)}}),15e3);o._msgHash[e.id].messageTimer=s,o.sock.send(i)}))}if(!this.isOpened())return null===(r=this.unMSyncSendMsgMap)||void 0===r||r.set(t,e),!this.logOut&&this.autoReconnectNumTotal<this.autoReconnectNumMax&&(H.debug("need to reconnect",this.autoReconnectNumTotal,this.autoReconnectNumMax),this.offLineSendConnecting=!0,this.reconnecting||this.reconnect()),void(this.onError&&this.onError({type:d.WEBIM_CONNCTION_DISCONNECTED,message:"websocket has been disconnected"}));var a,s=t&&setTimeout((function(){var e,r,n;if(o._msgHash[null!=t?t:""]){var i={type:d.MESSAGE_SEND_TIMEOUT,message:"send message timeout"};null===(r=(e=o._msgHash[null!=t?t:""]).reject)||void 0===r||r.call(e,i),clearTimeout(null===(n=o._msgHash[null!=t?t:""])||void 0===n?void 0:n.messageTimer),delete o._msgHash[null!=t?t:""],o.reconnecting||o.reconnect(!0)}}),15e3);this._msgHash[null!=t?t:""]&&(this._msgHash[null!=t?t:""].messageTimer=s);var c=_e.getEnvInfo();a="miniCore"===this.name||"web"===c.platform?e:Lt.call(this,e,t),this.sock.send(a)}var fr=function(e,t){return e.send=pr,e.sendMsg=pr,H.debug("init Msync by ".concat(e.name)),{generateProvision:wt.bind(e),base64transform:Lt.bind(e),distributeMeta:Pt.bind(e),decodeMeta:Dt.bind(e),decodeUnreadDL:Gt.bind(e),_rebuild:Bt.bind(e),_lastsession:Ft.bind(e),receiveProvision:Vt.bind(e),isInQueue:$t.bind(e),decodeNotice:er.bind(e),decodeMSync:tr.bind(e),distributeMSync:rr.bind(e),encodeChatMsg:ar.bind(e),upLoadFile:sr.bind(e),send:pr.bind(e),stopHeartBeat:Qt.bind(e),handleChatRoom:dr.bind(e),sendUnreadDeal:Yt.bind(e)}},mr=["onTextMessage","onFileMessage","onAudioMessage","onVideoMessage","onImageMessage","onLocationMessage","onCustomMessage","onCMDMessage"],gr=function(){function e(e,t,r){this.handlerData={},this.handlerData={},e.addEventHandler=this.addEventHandler.bind(this),e.removeEventHandler=this.removeEventHandler.bind(this)}return e.prototype.addEventHandler=function(e,t){this.handlerData[e]=t},e.prototype.removeEventHandler=function(e){delete this.handlerData[e]},e.prototype.dispatch=function(e,t){for(var r in mr.includes(e)&&t?H.debug("dispatch event: "+e,{id:t.id,type:t.type,time:t.time,from:t.from,to:t.to,chatType:t.chatType}):"onMessage"===e?H.debug("dispatch event: "+e,null==t?void 0:t.length):H.debug("dispatch event: "+e,t||""),this.handlerData){var o=this.handlerData[r][e];o&&o(t)}},e}(),Er=function(){};function vr(e){this.onOpened=e.onOpened||Er,this.onPresence=e.onPresence||Er,this.onTextMessage=e.onTextMessage||Er,this.onPictureMessage=e.onPictureMessage||Er,this.onAudioMessage=e.onAudioMessage||Er,this.onVideoMessage=e.onVideoMessage||Er,this.onFileMessage=e.onFileMessage||Er,this.onLocationMessage=e.onLocationMessage||Er,this.onCmdMessage=e.onCmdMessage||Er,this.onCustomMessage=e.onCustomMessage||Er,this.onReceivedMessage=e.onReceivedMessage||Er,this.onDeliveredMessage=e.onDeliveredMessage||Er,this.onReadMessage=e.onReadMessage||Er,this.onRecallMessage=e.onRecallMessage||Er,this.onChannelMessage=e.onChannelMessage||Er,this.onError=e.onError||Er,this.onOffline=e.onOffline||Er,this.onOnline=e.onOnline||Er,this.onStatisticMessage=e.onStatisticMessage||Er,this.onContactInvited=e.onContactInvited||Er,this.onContactAgreed=e.onContactAgreed||Er,this.onContactRefuse=e.onContactRefuse||Er,this.onContactDeleted=e.onContactDeleted||Er,this.onContactAdded=e.onContactAdded||Er,this.onTokenWillExpire=e.onTokenWillExpire||Er,this.onTokenExpired=e.onTokenExpired||Er,this.onClosed=e.onClosed||Er,this.onPresenceStatusChange=e.onPresenceStatusChange||Er}var yr={biz:"",debug:!1,token:""},_r="https://data-reporting.agora.io/report",Tr="https://data-reporting.sh.agoralab.co/report",Ir=function(e){var t=Number(g[e]);return t===g.USER_LOGIN?"MANUALLOGIN":t===g.MSYNC_SENDMESSAGE?"SENDMESSAGE":t>g.UNKNOWOPERATION&&t<g.REST_OPERATE?"REST":t>g.REST_OPERATE&&t<g.MSYNC_OPERATE?"MESSAGE":t>g.MSYNC_OPERATE&&t<g.ROSTER_OPERATE?"ROSTER":t>g.ROSTER_OPERATE&&t<g.USER_OPERATE?"USER":t>g.USER_OPERATE&&t<g.GROUP_OPERATE?"GROUP":t>g.GROUP_OPERATE&&t<g.CHATROOM_OPERATE?"CHATROOM":"OPERATION"},Or=function(){return(new Date).getTime()},Rr=function(e){return[le.BAIDU,le.WX,le.DD,le.ZFB,le.TT,le.QUICK_APP,le.UNI].includes(e.platform)},Cr=function(){return e=1,t=99999,e=Math.ceil(e),t=Math.floor(t),Math.floor(Math.random()*(t-e))+e;var e,t},Nr={},Sr=1e3,Ar=function(){function e(e){this.eventQueue=[],this.stock=Sr,this.config=e,this.governor()}return e.prototype.add=function(e){this.stock<=0?console.warn("Event Report limit ".concat(Sr," per minute")):(this.eventQueue.push(e),this.consume(),this.stock-=1)},e.prototype.consume=function(){var e=this;0!==this.eventQueue.length&&(this.timer&&this.eventQueue.length<=10&&clearTimeout(this.timer),this.timer=setTimeout((function(){var t,r=e.eventQueue.splice(0,10),o=r.filter((function(e){return e.appId===r[0].appId})),n=r.filter((function(e){return e.appId!==r[0].appId}));(t=e.eventQueue).unshift.apply(t,n),e.batchSend(o)}),1e3))},e.prototype.governor=function(){var e=this,t=setInterval((function(){e.stock=Sr}),6e4);"undefined"!=typeof addEventListener&&addEventListener("beforeunload",(function(){clearInterval(t)}))},e.prototype.batchSend=function(e){var t,r;if(void 0===e&&(e=[]),0!==e.length)try{var o=e.map((function(e){e.biz,e.appId;var t=e.eventId,r=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(o=Object.getOwnPropertySymbols(e);n<o.length;n++)t.indexOf(o[n])<0&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]])}return r}(e,["biz","appId","eventId"]);return{eventId:Number(t),body:r}}));!function(e,t){try{var r=t.biz,o=t.appId,n=t.data,i=t.debug,a=t.onSuccess;if(!r)throw new Error("biz is not defined");Nr.global||(Nr=_e.getEnvInfo());var s=Nr;if(Rr(s)){var c={url:i?Tr:_r,data:n,method:"POST",timeout:V,success:function(){null==a||a()},fail:function(){},complete:function(){}},u={token:e,appid:null!=o?o:"",sendts:"".concat(Math.floor((new Date).getTime()/1e3)),biz:r,debug:"".concat(i)};if("zfb"===s.platform||"dd"===s.platform?c.headers=u:c.header=u,"dd"===s.platform)return s.global.httpRequest(c);s.global.request(c)}else{var l=new XMLHttpRequest;l.onreadystatechange=function(){2===l.readyState&&(null==a||a())},l.open("POST",i?Tr:_r),l.setRequestHeader("Content-Type","application/json"),l.setRequestHeader("token",e),l.setRequestHeader("appid",null!=o?o:""),l.setRequestHeader("sendts","".concat(Math.floor((new Date).getTime()/1e3))),l.setRequestHeader("biz",r),l.setRequestHeader("debug","".concat(i)),l.send(JSON.stringify(n))}}catch(e){console.error(e)}}(this.config.token,{biz:null===(t=e[0])||void 0===t?void 0:t.biz,appId:null===(r=e[0])||void 0===r?void 0:r.appId,data:o,debug:this.config.debug,onSuccess:this.consume.bind(this)})}catch(e){console.error(e)}},e}(),br=function(){return br=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},br.apply(this,arguments)},Mr=new(function(){function e(e){this.inited=!1,this.appId="",this.biz="",this.eventQueue={},this.config=yr,e&&this.init(e)}return e.prototype.init=function(e){var t;if(void 0===e&&(e={}),!e.biz||!e.token)throw new Error("Event Report: biz or token is not defined");try{this.appId=null!==(t=e.appId)&&void 0!==t?t:"",this.biz=e.biz,this.config=br(br({},yr),e),this.eventQueue=new Ar(this.config),this.log(e),this.inited=!0}catch(e){console.error(e)}},e.prototype.updateAppId=function(e){this.appId=e},e.prototype.send=function(e,t,r){var o;if(void 0===t&&(t={}),this.inited){var n=br(br({},t),{eventId:Number(e),biz:this.biz,appId:null!==(o=null==r?void 0:r.appId)&&void 0!==o?o:this.appId});this.eventQueue.add(n),this.log(n)}else console.error("Event Report: init is not called")},e.prototype.log=function(e){try{if(this.config.debug){var t=e.payload,r=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(o=Object.getOwnPropertySymbols(e);n<o.length;n++)t.indexOf(o[n])<0&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]])}return r}(e,["payload"]);console.log("%c Event Report: ".concat(this.config.biz," "),"background: #8A97FC; color: #fff"),console.table(r),t&&(console.info("payload:"),console.table(t))}}catch(e){console.error(e)}},e}()),Ur=function(){return Ur=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},Ur.apply(this,arguments)},wr={requestName:"",subrequestid:"1",requestMethod:"GET",requestUrl:"",requestElapse:0,code:0,codeDesc:"",isLastApi:0,isSuccess:1},Lr=function(){function e(e){this.platform=_e.getEnvInfo(),this.isReportDt=e.isReport||!1,this.isCollectDt=!0,Rr(this.platform)&&!this.isReportDt&&(this.isCollectDt=!1),this.eventQueue=[],this.accessChannel="direct",this.options=function(e,t){t.platform||(t=_e.getEnvInfo());var r=e.org,o=e.appkey,n=e.deviceId,i=e.sdkVersion,a=e.uikitVersion,s="undefined"!=typeof navigator?null===navigator||void 0===navigator?void 0:navigator.userAgent:"".concat(t.platform,"_mini_program");return{org:r,appkey:o,deviceId:n,sdkServiceId:"sid_".concat(_e.getUniqueId(),"_").concat(Cr()),did:s,sdkVersion:i,os:7,sdkProduct:T.web,uikitVersion:a}}(e,this.platform),this.sid=this.options.sdkServiceId,this.init(e)}return e.getInstance=function(t){return e.instance||(e.instance=new e(t)),e.instance},e.prototype.getServiceId=function(){return this.sid||"sid_0"},e.prototype.setIsReportDt=function(e){this.isReportDt=e,e&&this.rptEventQueue()},e.prototype.setIsCollectDt=function(e){this.isCollectDt=e,e||(this.eventQueue=[])},e.prototype.rptEventQueue=function(){var e=this;this.eventQueue.length&&this.eventQueue.forEach((function(t,r){Mr.send(t.eventId,t.dt),r>=e.eventQueue.length-1&&(e.eventQueue=[])}))},e.prototype.init=function(t){e.instance||(e.instance=this,Mr.init({biz:"im",token:"32f24ab2ddb74f508aa9286c356cec84",appId:t.appkey,debug:!1}),this.reportInit())},e.prototype.updateAppKey=function(e){this.options.appkey=e,this.eventQueue.forEach((function(t){"default#appkey"===t.dt.appkey&&(t.dt.appkey=e)}))},e.prototype.reportInit=function(){if(this.isCollectDt){var e=this.options,t=e.did,r=e.os,o=e.sdkVersion,n=e.deviceId;this.reportData(9674,{did:t,os:r,sdkVersion:o,deviceId:n})}},e.prototype.geOperateFun=function(e){var t=this;if(!this.isCollectDt)return function(){};var r=1,o=0,n="",i=Or(),a=e.uid,s=e.operationName;a&&(this.uid=a);var c={uid:this.uid,operationId:"opr_".concat(_e.getUniqueId(),"_").concat(Cr()),requestid:"req_".concat(_e.getUniqueId(),"_").concat(Cr()),operationName:s};return function(e){var a,u,l,d;if(e.data.isSuccess?(o=0,n=""):(0===e.data.code&&(e.data.code=b),o=null!==(a=e.data.code)&&void 0!==a?a:o,n=null!==(u=e.data.codeDesc)&&void 0!==u?u:n),(null===(l=e.data)||void 0===l?void 0:l.accessChannel)&&(t.accessChannel=null===(d=e.data)||void 0===d?void 0:d.accessChannel),e.isRetry?(r++,e.data.subrequestid="".concat(r)):(c.requestid="req_".concat(_e.getUniqueId(),"_").concat(Cr()),r=1),e.data.isLastApi){var p=Or();e.data.requestElapse=p-i,e.data.requestMethod="",e.data.subrequestid="0",e.data.code=200===o?0:o,e.data.codeDesc=n}else e.data.requestName||(e.data.requestName=s);e.data.requestElapse||(e.data.requestElapse=Or()-i),t.reportData.call(t,A,Ur(Ur(Ur(Ur({},wr),c),e.data),{accessChannel:t.accessChannel,operationType:Ir(s)})),e.isEndApi&&t.reportData.call(t,A,Ur(Ur(Ur({},c),wr),{isSuccess:e.data.isSuccess,isLastApi:1,subrequestid:"0",requestMethod:"",code:200===o?0:o,codeDesc:n,requestElapse:e.data.requestElapse,accessChannel:t.accessChannel,operationType:Ir(s)}))}},e.prototype.reportData=function(e,t){return r=this,o=void 0,i=function(){var r,o,n,i,a,s,c,u,l;return function(e,t){var r,o,n,i,a={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,o&&(n=2&i[0]?o.return:i[0]?o.throw||((n=o.return)&&n.call(o),0):o.next)&&!(n=n.call(o,i[1])).done)return n;switch(o=0,n&&(i=[2&i[0],n.value]),i[0]){case 0:case 1:n=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,o=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!((n=(n=a.trys).length>0&&n[n.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!n||i[1]>n[0]&&i[1]<n[3])){a.label=i[1];break}if(6===i[0]&&a.label<n[1]){a.label=n[1],n=i;break}if(n&&a.label<n[2]){a.label=n[2],a.ops.push(i);break}n[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],o=0}finally{r=n=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}}(this,(function(d){switch(d.label){case 0:return d.trys.push([0,3,,4]),r=Or(),o=0,e!==A?[3,2]:[4,(p=this.platform,new Promise((function(e){var t;p.platform||(p=_e.getEnvInfo());var r="";if(p.platform===le.WEB){var o=navigator.connection;(null==o?void 0:o.type)?(null==o||o.type,r=y.WIFI):(null==o?void 0:o.effectiveType)&&(r=y[o.effectiveType.toLocaleUpperCase()]),e(r)}else p.platform===le.NODE?(r=y.UNKNOWN,e(r)):null===(t=p.global)||void 0===t||t.getNetworkType({success:function(t){r=y[t.networkType.toLocaleUpperCase()],e(r)}})})))];case 1:o=d.sent(),d.label=2;case 2:return n=this.options,i=n.appkey,a=n.sdkServiceId,s=n.sdkProduct,c=n.uikitVersion,u=Ur({lts:r,net:o,appkey:i,sdkServiceId:a,sdkProduct:s,uikitVersion:c},t),this.isReportDt?Mr.send(e,u):this.isCollectDt&&this.eventQueue.push({eventId:e,dt:u}),[3,4];case 3:return l=d.sent(),console.warn(l),[3,4];case 4:return[2]}var p}))},new((n=void 0)||(n=Promise))((function(e,t){function a(e){try{c(i.next(e))}catch(e){t(e)}}function s(e){try{c(i.throw(e))}catch(e){t(e)}}function c(t){var r;t.done?e(t.value):(r=t.value,r instanceof n?r:new n((function(e){e(r)}))).then(a,s)}c((i=i.apply(r,o||[])).next())}));var r,o,n,i},e}(),Pr=function(){return Pr=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},Pr.apply(this,arguments)},Dr=function(e,t,r,o){return new(r||(r=Promise))((function(n,i){function a(e){try{c(o.next(e))}catch(e){i(e)}}function s(e){try{c(o.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}c((o=o.apply(e,t||[])).next())}))},kr=function(e,t){var r,o,n,i,a={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,o&&(n=2&i[0]?o.return:i[0]?o.throw||((n=o.return)&&n.call(o),0):o.next)&&!(n=n.call(o,i[1])).done)return n;switch(o=0,n&&(i=[2&i[0],n.value]),i[0]){case 0:case 1:n=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,o=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!((n=(n=a.trys).length>0&&n[n.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!n||i[1]>n[0]&&i[1]<n[3])){a.label=i[1];break}if(6===i[0]&&a.label<n[1]){a.label=n[1],n=i;break}if(n&&a.label<n[2]){a.label=n[2],a.ops.push(i);break}n[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],o=0}finally{r=n=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}},xr={singleChat:"chat",chatRoom:"chatroom",groupChat:"groupchat"};function Gr(e){var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a={url:this.apiUrl+"/"+o+"/"+n+"/users/"+this.user+"/blocks/users",dataType:"json",type:"GET",headers:{Authorization:"Bearer "+i},success:function(t){var r={};t.data.forEach((function(e){r[e]={name:e}})),"function"==typeof(null==e?void 0:e.success)&&e.success(t)},error:function(t){"function"==typeof(null==e?void 0:e.error)&&e.error(t)}};return H.debug("Call getBlocklist"),ae.call(this,a,E.GET_BLACK_LIST)}var Br=Gr,Hr=jr;function jr(e){var t=this,r=yt.call(this).error;if(r)return Promise.reject(r);var o=this.context,n=o.orgName,i=o.appName,a=o.accessToken,s={url:this.apiUrl+"/"+n+"/"+i+"/users/"+this.user+"/contacts/users",dataType:"json",type:"GET",headers:{Authorization:"Bearer "+a},success:function(r){var o=[];r.data.forEach((function(e){o.push({name:e,subscription:"both",jid:t.context.jid})})),"function"==typeof(null==e?void 0:e.success)&&e.success(o)},error:function(t){"function"==typeof(null==e?void 0:e.error)&&e.error(t)}};return H.debug("Call getContacts"),ae.call(this,s,E.GET_CONTACTS)}function Fr(e){return H.debug("Call uploadPushToken"),It.call(this,e)}var Wr=Fr;function Kr(e){var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/users/").concat(this.user,"/user_channels"),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e&&(null==e?void 0:e.success),error:e&&(null==e?void 0:e.error)};return H.debug("Call getSessionList"),ae.call(this,a)}function qr(e){var t=this,r=yt.call(this).error;if(r)return Promise.reject(r);var o=this.context,n=o.orgName,i=o.appName,a=o.accessToken,s=!!(e&&"number"==typeof e.pageNum&&"number"==typeof e.pageSize&&e.pageNum>0&&e.pageSize>0),c={url:"".concat(this.apiUrl,"/").concat(n,"/").concat(i,"/users/").concat(this.user,"/user_channels").concat(s?"/page":""),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+a,"Content-Type":"application/json"},success:e&&(null==e?void 0:e.success),error:e&&(null==e?void 0:e.error)};return s&&(c.data={pageNum:e.pageNum,pageSize:e.pageSize}),H.debug("Call getConversationlist"),ae.call(this,c,E.GET_SESSION_LIST).then((function(e){return Vr.call(t,e)}))}function Vr(e){var t=this,r=e.data.channel_infos;return null==r||r.forEach((function(e){e.meta&&"{}"!==JSON.stringify(e.meta)?(e.meta.payload=JSON.parse(e.meta.payload),e.lastMessage=Ce.call(t,e.meta,{formatCustomExts:!1})):e.lastMessage=e.meta,delete e.meta})),e}function zr(e){if(e&&"string"!=typeof e.channel)throw Error("Invalid parameter channel: ".concat(e.channel));if(e&&"singleChat"!==e.chatType&&"groupChat"!==e.chatType)throw Error("Invalid parameter chatType: ".concat(e.chatType));if(e&&"boolean"!=typeof e.deleteRoam)throw Error("Invalid parameter deleteRoam: ".concat(e.deleteRoam));var t=yt.call(this).error;if(t)return Promise.reject(t);var r,o=this.context,n=o.orgName,i=o.appName,a=o.accessToken,s=o.jid;r="singleChat"===e.chatType?"chat":"groupChat";var c={url:"".concat(this.apiUrl,"/").concat(n,"/").concat(i,"/user/").concat(this.user,"/user_channel?resource=").concat(s.clientResource),dataType:"json",type:"DELETE",data:JSON.stringify({channel:e.channel,type:r,delete_roam:e.deleteRoam}),headers:{Authorization:"Bearer "+a,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call deleteSession",e),ae.call(this,c,E.DELETE_SESSION)}var Jr=zr;function Yr(e,t){var r=yt.call(this).error;if(r)return Promise.reject(r);var o=this.context,n=o.orgName,i=o.appName,a=o.accessToken,s=["nickname","avatarurl","mail","phone","gender","sign","birth","ext"],c={},u=_e.getEnvInfo();if("wx"===u.platform||"qq"===u.platform)if("string"==typeof e&&void 0!==t){if(!s.includes(e))throw new Error("illegal key, only these keys: nickname, avatarurl, mail, phone, gender, sign, birth, ext are allowed");c[e]=t}else{if("[object Object]"!==Object.prototype.toString.call(e))throw new Error("illegal params");s.forEach((function(t){s.includes(t)&&void 0!==e[t]&&(c[t]=e[t])}))}else if("string"==typeof e){if(!s.includes(e))throw new Error("illegal key, only these keys: nickname, avatarurl, mail, phone, gender, sign, birth, ext are allowed");c=e+"="+t}else{if("[object Object]"!==Object.prototype.toString.call(e))throw new Error("illegal params");var l=[];s.forEach((function(t){if(s.includes(t)&&void 0!==e[t]){var r=encodeURIComponent(t),o=encodeURIComponent(e[t]);l.push(r+"="+o)}})),c=l.join("&")}var d={url:"".concat(this.apiUrl,"/").concat(n,"/").concat(i,"/metadata/user/").concat(this.user),type:"PUT",data:c,dataType:"json",headers:{Authorization:"Bearer "+a,"Content-Type":"application/x-www-form-urlencoded"}};return H.debug("Call updateOwnUserInfo",e),ae.call(this,d,E.UPDATE_USER_INFO)}var Xr=Yr;function Qr(e,t){var r=yt.call(this).error;if(r)return Promise.reject(r);var o,n=this.context,i=n.orgName,a=n.appName,s=n.accessToken,c=[];if("string"==typeof e)c=[e];else{if("[object Array]"!==Object.prototype.toString.call(e))throw new Error("illegal params");c=e}o="string"==typeof t?[t]:t&&"[object Array]"===Object.prototype.toString.call(t)?t:["nickname","avatarurl","mail","phone","gender","sign","birth","ext"];var u={url:"".concat(this.apiUrl,"/").concat(i,"/").concat(a,"/metadata/user/get"),type:"POST",data:JSON.stringify({targets:c,properties:o}),dataType:"json",headers:{Authorization:"Bearer "+s,"Content-Type":"application/json"}};return H.debug("Call fetchUserInfoById",e),ae.call(this,u,E.GET_USER_INFO)}function Zr(e){var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/users/").concat(this.user),type:"PUT",dataType:"json",data:JSON.stringify({nickname:e}),headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return H.debug("Call updateCurrentUserNick",e),ae.call(this,a)}function $r(e){if("string"!=typeof e||""===e)throw Error('Invalid parameter: "token"');var t=this.context,r=t.orgName,o=t.appName,n={url:"".concat(this.apiUrl,"/").concat(r,"/").concat(o,"/sdk/users/").concat(this.user,"/token/expires"),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+e,"Content-Type":"application/json"}};return H.debug("Call getTokenExpireTimestamp",e),ae.call(this,n,E.SDK_INTERNAL)}function eo(e){var t=this;return new Promise((function(r,o){if(!e.queue)throw Error('Invalid parameter: "specified"');var n=yt.call(t).error;if(n)return Promise.reject(n);(function e(t){var n=this,i=t.count||20;to.call(this,{count:i,isGroup:!!t.isGroup,queue:t.queue,start:t.start,format:t.format,success:function(o){if(o.msgs.length>=i||o.is_last){var a=o.msgs.splice(0,i).reverse();t.success&&t.success(a),r(a)}else e.call(n,Pr(Pr({},t),{start:null}))},fail:function(e){o(e),t.fail&&t.fail(e)}})}).call(t,e),H.debug("Call fetchHistoryMessages",e)}))}function to(e){var t=this,r=e.queue,o=this.mr_cache[r]||(this.mr_cache[r]={msgs:[]}),n=this.context.userId,i=e.start||-1,a=e.count||20;if(o.msgs.length>=a||o.is_last)"function"==typeof e.success&&e.success(o);else{o&&o.next_key&&(i=o.next_key),e.start&&(i=e.start);var s={queue:r+(e.isGroup?"@conference.easemob.com":"@easemob.com"),start:i,end:-1},c=this.context,u=c.orgName,l=c.appName,p={url:"".concat(this.apiUrl,"/").concat(u,"/").concat(l,"/users/").concat(n,"/messageroaming"),dataType:"json",type:"POST",headers:{Authorization:"Bearer "+this.token,"Content-Type":"application/json"},data:JSON.stringify(s),success:function(r){return Dr(t,void 0,void 0,(function(){var t,n,i,a,s,c,u=this;return kr(this,(function(l){switch(l.label){case 0:if(t=null==r?void 0:r.data,!r.data.msgs)return"function"==typeof e.success&&e.success(o),o.is_last=!0,o.next_key="",[2];n=t.msgs,i=n.length,o.is_last=t.is_last,o.next_key=t.next_key,a=function(t){return Dr(u,void 0,void 0,(function(){var r,o,n,i,a;return kr(this,(function(s){switch(s.label){case 0:for(r=[],t=Ie().atob(t),o=0,n=t.length;o<n;++o)r.push(t.charCodeAt(o));return i=(i=this.context.root.lookup("easemob.pb.Meta")).decode(r),a={errorCode:0,reason:""},1!==i.ns?[3,2]:[4,et.call(this,i,a,!0,e.format)];case 1:return[2,s.sent()];case 2:return[2]}}))}))},s=0,l.label=1;case 1:return s<i?[4,a(n[s].msg)]:[3,4];case 2:(c=l.sent())&&o.msgs.push(c),l.label=3;case 3:return s++,[3,1];case 4:return"function"==typeof e.success&&e.success(o),[2]}}))}))},error:function(e){if(e.error&&e.error_description){var r=m.create({type:d.WEBIM_CONNCTION_AJAX_ERROR,message:"fetch history messages error",data:e});t.onError&&t.onError(r)}}};ae.call(this,p,E.GET_HISTORY_MSG).catch((function(t){"function"==typeof e.fail&&e.fail(t)}))}}function ro(e){var t=this;return new Promise((function(r,o){var n=e.targetId,i=e.cursor,a=void 0===i?-1:i,s=e.pageSize,c=void 0===s?20:s,u=e.chatType,l=e.searchDirection,d=e.searchOptions,p=void 0===d?{}:d,h=p.msgTypes,f=void 0===h?[]:h,m=p.startTime,g=void 0===m?null:m,v=p.endTime,y=void 0===v?null:v,_=p.from,T=void 0===_?null:_;if("string"!=typeof n||""===n)throw Error('"Invalid parameter": "targetId"');if(T&&"string"!=typeof T)throw Error('"Invalid parameter": "searchOptions.from"');if(f&&!Array.isArray(f))throw Error('"Invalid parameter": "searchOptions.msgTypes"');if(g&&"number"!=typeof g)throw Error('"Invalid parameter": "searchOptions.startTime"');if(y&&"number"!=typeof g)throw Error('"Invalid parameter": "searchOptions.endTime"');var I=yt.call(t).error;if(I)return Promise.reject(I);var O=t.context,R=O.orgName,C=O.appName,N=O.userId,S="singleChat"===e.chatType?"@easemob.com":"@conference.easemob.com",A={queue:"".concat(n).concat(S),start:a,pull_number:c,is_positive:"down"===l,msgType:f.join(",")||"",end:-1,startTime:g,endTime:y,userId:"singleChat"===u?null:T},b={url:"".concat(t.apiUrl,"/").concat(R,"/").concat(C,"/users/").concat(N,"/messageroaming"),dataType:"json",type:"POST",headers:{Authorization:"Bearer "+t.token,"Content-Type":"application/json"},data:JSON.stringify(A),success:function(o){return Dr(t,void 0,void 0,(function(){var t,n,i,a,s,c,u,l,d=this;return kr(this,(function(p){switch(p.label){case 0:t=null==o?void 0:o.data,n=t.msgs||[],i=function(e){return Dr(d,void 0,void 0,(function(){var t,r,o,n,i;return kr(this,(function(a){switch(a.label){case 0:for(t=[],e=Ie().atob(e),r=0,o=e.length;r<o;++r)t.push(e.charCodeAt(r));return n=(n=this.context.root.lookup("easemob.pb.Meta")).decode(t),i={errorCode:0,reason:""},1!==n.ns?[3,2]:[4,et.call(this,n,i,!0,!0)];case 1:return[2,a.sent()];case 2:return[2]}}))}))},a=[],s=0,p.label=1;case 1:return s<n.length?[4,i(n[s].msg)]:[3,4];case 2:(c=p.sent())&&a.push(c),p.label=3;case 3:return s++,[3,1];case 4:return t.msgs=a,u={cursor:t.next_key,messages:a,isLast:t.is_last},null===(l=e.success)||void 0===l||l.call(e,u),r(u),[2]}}))}))},error:e.fail};ae.call(t,b,E.REST_FETCHHISTORYMESSAGE).catch((function(e){o(e)})),H.debug("Call getHistoryMessages",e)}))}function oo(e,t){return Dr(this,void 0,void 0,(function(){var r,o,n,i,a,s,c;return kr(this,(function(u){switch(u.label){case 0:if(r=yt.call(this).error)return[2,Promise.reject(r)];if("string"!=typeof e||""===e)throw Error('"Invalid parameter": "to"');return H.debug("Call addContact",e,t),o=this.context,n=o.orgName,i=o.appName,a=o.accessToken,s=o.jid,c={url:"".concat(this.apiUrl,"/").concat(n,"/").concat(i,"/users/").concat(this.user,"/contacts/apply?resource=").concat(s.clientResource),type:"POST",dataType:"json",data:JSON.stringify({usernames:[e],reason:t}),headers:{Authorization:"Bearer "+a,"Content-Type":"application/json"}},[4,ae.call(this,c,E.ROSTER_ADD)];case 1:return u.sent(),[2]}}))}))}var no=io;function io(e){return Dr(this,void 0,void 0,(function(){var t,r,o,n,i,a,s;return kr(this,(function(c){switch(c.label){case 0:if(t=yt.call(this).error)return[2,Promise.reject(t)];if("string"!=typeof e||""===e)throw Error('"Invalid parameter": "to"');return H.debug("Call deleteContact",e),r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/users/").concat(this.user,"/contacts/users/").concat(e,"?resource=").concat(a.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}},[4,ae.call(this,s,E.ROSTER_REMOVE)];case 1:return c.sent(),[2]}}))}))}function ao(e){return Dr(this,void 0,void 0,(function(){var t,r,o,n,i,a,s;return kr(this,(function(c){switch(c.label){case 0:if(t=yt.call(this).error)return[2,Promise.reject(t)];if("string"!=typeof e||""===e)throw Error('"Invalid parameter": "to"');return H.debug("Call acceptInvitation",e),r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/users/").concat(this.user,"/contacts/accept/users/").concat(e,"?resource=").concat(a.clientResource),type:"POST",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}},[4,ae.call(this,s,E.ROSTER_ACCEPT)];case 1:return c.sent(),[2]}}))}))}var so=ao;function co(e){return Dr(this,void 0,void 0,(function(){var t,r,o,n,i,a,s;return kr(this,(function(c){switch(c.label){case 0:if(t=yt.call(this).error)return[2,Promise.reject(t)];if("string"!=typeof e||""===e)throw Error('"Invalid parameter": "to"');return H.debug("Call declineInvitation",e),r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/users/").concat(this.user,"/contacts/decline/users/").concat(e,"?resource=").concat(a.clientResource),type:"POST",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}},[4,ae.call(this,s,E.ROSTER_DECLINE)];case 1:return c.sent(),[2]}}))}))}var uo=co;function lo(e){return Dr(this,void 0,void 0,(function(){var t,r,o,n,i,a,s,c,u;return kr(this,(function(l){switch(l.label){case 0:if(t=yt.call(this).error)return[2,Promise.reject(t)];if(r=e.name,o=[],"string"==typeof r){if(""===r)throw Error('"Invalid parameter": "name"');o=[r]}else{if(!Array.isArray(r))throw Error('"Invalid parameter": "name"');o=r}return H.debug("Call addToBlockList",e),n=this.context,i=n.orgName,a=n.appName,s=n.accessToken,c=n.jid,u={url:"".concat(this.apiUrl,"/").concat(i,"/").concat(a,"/sdk/user/").concat(this.user,"/blocks?resource=").concat(c.clientResource),type:"POST",dataType:"json",data:JSON.stringify({usernames:o}),headers:{Authorization:"Bearer "+s,"Content-Type":"application/json"}},[4,ae.call(this,u,E.ROSTER_BAN).then((function(e){return{type:e.type,data:{userIds:(null==e?void 0:e.data)||[]}}}))];case 1:return[2,l.sent()]}}))}))}var po=lo,ho=lo;function fo(e){return Dr(this,void 0,void 0,(function(){var t,r,o,n,i,a,s,c,u;return kr(this,(function(l){switch(l.label){case 0:if(t=yt.call(this).error)return[2,Promise.reject(t)];if(r=e.name,o=[],"string"==typeof r){if(""===r)throw Error('"Invalid parameter": "name"');o=[r]}else{if(!Array.isArray(r))throw Error('"Invalid parameter": "name"');o=r}return H.debug("Call removeFromBlockList",e),n=this.context,i=n.orgName,a=n.appName,s=n.accessToken,c=n.jid,u={url:"".concat(this.apiUrl,"/").concat(i,"/").concat(a,"/sdk/user/").concat(this.user,"/blocks?resource=").concat(c.clientResource),type:"DELETE",dataType:"json",data:JSON.stringify({usernames:o}),headers:{Authorization:"Bearer "+s,"Content-Type":"application/json"}},[4,ae.call(this,u,E.ROSTER_ALLOW)];case 1:return l.sent(),[2]}}))}))}var mo=fo,go=fo;function Eo(e){var t=this,r=(null==e?void 0:e.ext)||"";if("string"!=typeof r)throw Error('"Invalid parameter": "ext"',r);var o=this.getUniqueId(),n={id:o,to:e.to};this._msgHash[o]=Pr({},n);var i="";void 0!==e.chatType?i=e.chatType:void 0!==e.type&&(i="chat"===e.type?"singleChat":e.type);var a={id:o,type:"recall",chatType:i,ackId:e.mid,to:e.to,isChatThread:e.isChatThread||!1,metaExt:r,success:function(r){return Dr(t,void 0,void 0,(function(){var t,o,n,a,s,c,u,l,d;return kr(this,(function(p){switch(p.label){case 0:return p.trys.push([0,5,,6]),"singleChat"!==i&&"groupChat"!==i?[3,4]:[4,null===(n=null===(o=this._localCache)||void 0===o?void 0:o.getInstance())||void 0===n?void 0:n.removeMsgByServerMsgId(e.mid)];case 1:return p.sent(),[4,null===(s=null===(a=this._localCache)||void 0===a?void 0:a.getInstance())||void 0===s?void 0:s.getConversationLastMessage(e.to,i)];case 2:return t=p.sent(),[4,null===(u=null===(c=this._localCache)||void 0===c?void 0:c.getInstance())||void 0===u?void 0:u.updateLocalConversation(Se({conversationId:e.to,conversationType:i}),{lastMessageId:null==t?void 0:t.serverMsgId})];case 3:p.sent(),p.label=4;case 4:return null===(l=null==e?void 0:e.success)||void 0===l||l.call(e,r),[3,6];case 5:return p.sent(),null===(d=null==e?void 0:e.success)||void 0===d||d.call(e,r),[3,6];case 6:return[2]}}))}))},fail:e.fail};return H.debug("Call recallMessage",e),this.mSync.send(a,this)}function vo(e){var t=e||{},r=t.messageId,o=t.modifiedMessage;if(H.debug("Call modifyMessage",r,o),""===r)throw Error('Invalid parameter: "messageId"');return this.mSync.send(Pr({editMessageId:r},o))}function yo(e){return Dr(this,void 0,void 0,(function(){var t,r,o,n,i,a,s,c,u;return kr(this,(function(l){switch(l.label){case 0:if("string"!=typeof e.messageId||!e.messageId)throw Error("Invalid parameter messageId: ".concat(e.messageId));if("string"!=typeof e.reaction||!e.reaction)throw Error("Invalid parameter reaction: ".concat(e.reaction));return(t=yt.call(this).error)?[2,Promise.reject(t)]:(r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=e.reaction,s=e.messageId,c={msgId:s,message:a},u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/reaction/user/").concat(this.user),type:"POST",data:JSON.stringify(c),dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}},[4,ae.call(this,u)]);case 1:return l.sent(),[2]}}))}))}function _o(e){return Dr(this,void 0,void 0,(function(){var t,r,o,n,i,a,s,c;return kr(this,(function(u){switch(u.label){case 0:if("string"!=typeof e.reaction||!e.reaction)throw Error("Invalid parameter reactionId: ".concat(e.reaction));return(t=yt.call(this).error)?[2,Promise.reject(t)]:(r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=e.messageId,s=e.reaction,c={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/reaction/user/").concat(this.user,"?msgId=").concat(a,"&message=").concat(s),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}},[4,ae.call(this,c)]);case 1:return u.sent(),[2]}}))}))}function To(e){if("string"!=typeof e.chatType||!e.chatType)throw Error("Invalid parameter chatType: ".concat(e.chatType));if(!e.messageId)throw Error("Invalid parameter messageId: ".concat(e.messageId));var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=e.chatType,s=e.messageId,c={msgIdList:"string"==typeof s?[s]:s,msgType:"singleChat"===a?"chat":"groupchat",groupId:e.groupId||null},u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/reaction/user/").concat(this.user),type:"GET",data:c,dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return ae.call(this,u).then((function(e){var t=e.data;return null==t||t.forEach((function(e){null==e||e.reactionList.forEach((function(e){e.isAddedBySelf=e.state,delete e.state,delete e.reactionId}))})),e}))}var Io=To;function Oo(e){if("string"!=typeof e.reaction||!e.reaction)throw Error("Invalid parameter reaction: ".concat(e.reaction));if("string"!=typeof e.messageId||!e.messageId)throw Error("Invalid parameter messageId: ".concat(e.messageId));var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=e.cursor,s=e.pageSize,c={msgId:e.messageId,message:e.reaction,currentPage:a||null,pageSize:s||20},u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/reaction/user/").concat(this.user,"/detail"),type:"GET",data:c,dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return ae.call(this,u).then((function(e){return e.data.isAddedBySelf=e.data.state,delete e.data.state,delete e.data.reactionId,e}))}function Ro(e){return Dr(this,void 0,void 0,(function(){var t,r,o,n,i,a,s,c,u,l,d;return kr(this,(function(p){switch(p.label){case 0:if("string"!=typeof e.reportType||!e.reportType)throw Error("Invalid parameter reportType: ".concat(e.reportType));if("string"!=typeof e.messageId||!e.messageId)throw Error("Invalid parameter messageId: ".concat(e.messageId));if("string"!=typeof e.reportReason||!e.reportReason)throw Error("Invalid parameter messageId: ".concat(e.reportReason));return(t=yt.call(this).error)?[2,Promise.reject(t)]:(r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=e.reportType,s=e.reportReason,c=e.messageId,u={username:this.user,reportType:a,reportReason:s},l="".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/user/").concat(this.user,"/moderation/report/message/").concat(c),d={url:l,type:"POST",data:JSON.stringify(u),dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}},[4,ae.call(this,d)]);case 1:return p.sent(),[2]}}))}))}function Co(e){var t;return Dr(this,void 0,void 0,(function(){var r,o,n,i,a,s,c,u,l;return kr(this,(function(d){switch(d.label){case 0:if("string"!=typeof e.targetId||""===e.targetId)throw Error('"Invalid parameter targetId": '+e.targetId);if(!["singleChat","groupChat","chatRoom"].includes(e.chatType))throw Error('"Invalid parameter chatType": '+e.chatType);if(e.beforeTimeStamp&&("number"!=typeof e.beforeTimeStamp||e.beforeTimeStamp<0||(null===(t=e.beforeTimeStamp)||void 0===t?void 0:t.toString().length)>18))throw Error('"Invalid parameter beforeTimeStamp": '+e.beforeTimeStamp);if(e.messageIds&&!(Array.isArray(e.messageIds)&&e.messageIds.length>0&&e.messageIds.length<=20))throw Error('"Invalid parameter messageIds": '+e.messageIds);if(!e.messageIds&&!e.beforeTimeStamp)throw Error("messageIds or beforeTimeStamp field is required.");return(r=yt.call(this).error)?[2,Promise.reject(r)]:(o=this.context,n=o.orgName,i=o.appName,a=o.userId,s="singleChat"===e.chatType?"userId":"groupId",c="singleChat"===e.chatType?"chat":"group",u=e.messageIds?"".concat(this.apiUrl,"/").concat(n,"/").concat(i,"/sdk/message/roaming/").concat(c,"/user/").concat(a,"?").concat(s,"=").concat(e.targetId,"&msgIdList=").concat(e.messageIds,"&resource=").concat(this.clientResource):"".concat(this.apiUrl,"/").concat(n,"/").concat(i,"/sdk/message/roaming/").concat(c,"/user/").concat(a,"/time?").concat(s,"=").concat(e.targetId,"&delTime=").concat(e.beforeTimeStamp,"&&resource=").concat(this.clientResource),l={url:u,dataType:"json",type:"DELETE",headers:{Authorization:"Bearer "+this.token,"Content-Type":"application/json"}},H.debug("Call removeHistoryMessages",e),[4,ae.call(this,l)]);case 1:return d.sent(),[2]}}))}))}function No(e){var t=this,r=yt.call(this).error;if(r)return Promise.reject(r);if((null==e?void 0:e.pageSize)&&"number"!=typeof e.pageSize)throw Error("Invalid parameter pageSize: ".concat(e.pageSize));if((null==e?void 0:e.cursor)&&"string"!=typeof e.cursor)throw Error("Invalid parameter cursor: ".concat(e.cursor));var o=this.context,n=o.orgName,i=o.appName,a=o.accessToken,s={url:"".concat(this.apiUrl,"/").concat(n,"/").concat(i,"/sdk/user/").concat(this.user,"/user_channels/list?"),type:"GET",dataType:"json",data:{limit:(null==e?void 0:e.pageSize)||20,cursor:(null==e?void 0:e.cursor)||"",need_mark:!0},headers:{Authorization:"Bearer "+a,"Content-Type":"application/json"}};return H.debug("Call getServerConversations",e),new Promise((function(e,r){ae.call(t,s,E.REST_FETCH_CONVERSATIONS).then((function(r){return Dr(t,void 0,void 0,(function(){var t,o,n=this;return kr(this,(function(i){switch(i.label){case 0:return t=So.call(this,r),this._localCache?(o=t.data.conversations.map((function(e){return Dr(n,void 0,void 0,(function(){var t,r,o,n;return kr(this,(function(i){switch(i.label){case 0:return[4,null===(r=null===(t=this._localCache)||void 0===t?void 0:t.getInstance())||void 0===r?void 0:r.storeMessage(e.lastMessage,Ne.SUCCESS,!0)];case 1:return i.sent(),[4,null===(n=null===(o=this._localCache)||void 0===o?void 0:o.getInstance())||void 0===n?void 0:n.updateLocalConversation(Se({conversationId:e.conversationId,conversationType:e.conversationType}),{unReadCount:e.unReadCount})];case 2:return i.sent(),[2]}}))}))})),[4,Promise.all(o)]):[3,2];case 1:i.sent(),i.label=2;case 2:return e(t),[2]}}))}))})).catch((function(e){r(e)}))}))}function So(e){var t=this,r=e.data,o=r.cursor,n=r.channel_infos,i=[];null==n||n.forEach((function(e){var r=null;(null==e?void 0:e.meta)&&"{}"!==JSON.stringify(e.meta)&&(e.meta.payload=JSON.parse(e.meta.payload),"delivery"!==(r=Ce.call(t,e.meta)).type&&"read"!==r.type&&"channel"!==r.type&&(r.chatType=Re[r.chatType]));var o={conversationId:e.session_to,conversationType:"chat"===e.session_type?"singleChat":"groupChat",isPinned:e.is_top,pinnedTime:e.is_top?e.update_top_status_time:0,unReadCount:e.unread_num,lastMessage:r};e.marks&&(o.marks=e.marks.map((function(e){return at[e]}))),i.push(o)}));var a={conversations:i,cursor:o||""};return{type:e.type,data:a}}function Ao(e){var t=this,r=yt.call(this).error;if(r)return Promise.reject(r);if((null==e?void 0:e.pageSize)&&"number"!=typeof e.pageSize)throw Error("Invalid parameter pageSize: ".concat(e.pageSize));if((null==e?void 0:e.cursor)&&"string"!=typeof e.cursor)throw Error("Invalid parameter cursor: ".concat(e.cursor));var o=this.context,n=o.orgName,i=o.appName,a=o.accessToken,s={url:"".concat(this.apiUrl,"/").concat(n,"/").concat(i,"/sdk/user/").concat(this.user,"/user_channels/list?"),type:"GET",dataType:"json",data:{limit:(null==e?void 0:e.pageSize)||20,cursor:(null==e?void 0:e.cursor)||"",is_top:!0,need_mark:!0},headers:{Authorization:"Bearer "+a,"Content-Type":"application/json"}};return H.debug("Call getServerPinnedConversations",e),ae.call(this,s,E.GET_SESSION_LIST).then((function(e){return So.call(t,e)}))}function bo(e){return Dr(this,void 0,void 0,(function(){var t,r,o,n,i,a,s,c,u,l,d,p,h,f;return kr(this,(function(m){if(t=yt.call(this).error)return[2,Promise.reject(t)];if(r=e.conversationId,o=e.conversationType,n=e.isPinned,i="singleChat"===o?"chat":"groupChat","string"!=typeof r||""===r)throw Error("Invalid parameter conversationId: ".concat(r));if(!["singleChat","groupChat"].includes(o))throw Error("Invalid parameter conversationType: ".concat(o));if("boolean"!=typeof n)throw Error("Invalid parameter isPinned: ".concat(n));return a=this.context,s=a.orgName,c=a.appName,u=a.accessToken,l=a.jid,d={type:i,to:r},p=n?"":"type=".concat(i,"&to=").concat(r,"&"),h="".concat(this.apiUrl,"/").concat(s,"/").concat(c,"/sdk/user/").concat(this.user,"/user_channel/top?").concat(p,"resource=").concat(l.clientResource),f={url:h,type:n?"POST":"DELETE",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"}},n&&(f.data=JSON.stringify(d)),H.debug("Call pinConversation",e),[2,ae.call(this,f,E.PIN_CONVERSATION).then((function(e){var t=e.type,r=e.data;return{type:t,data:{isPinned:r.is_top||!1,pinnedTime:r.is_top?r.update_top_status_time:0}}}))]}))}))}function Mo(e){return Dr(this,void 0,void 0,(function(){var t,r,o,n,i,a,s,c,u,l;return kr(this,(function(d){switch(d.label){case 0:if(t=e.userId,r=e.remark,o=yt.call(this).error)return[2,Promise.reject(o)];if("string"!=typeof t||""===t)throw Error('Invalid parameter: "userId"');if("string"!=typeof r)throw Error('Invalid parameter: "remark"');return H.debug("Call setContactRemark",e),n=this.context,i=n.orgName,a=n.appName,s=n.accessToken,c=n.jid,u="".concat(this.apiUrl,"/").concat(i,"/").concat(a,"/users/").concat(this.context.userId,"/contacts/users/").concat(t,"?resource=").concat(c.clientResource),l={url:u,type:"PUT",data:JSON.stringify({remark:r}),dataType:"json",headers:{Authorization:"Bearer "+s,"Content-Type":"application/json"}},[4,ae.call(this,l,E.ROSTER_SET_CONTACT_REMARK)];case 1:return d.sent(),[2]}}))}))}function Uo(){var e=yt.call(this).error;if(e)return Promise.reject(e);var t=this.context,r=t.orgName,o=t.appName,n=t.accessToken,i={url:"".concat(this.apiUrl,"/").concat(r,"/").concat(o,"/users/").concat(this.user,"/contacts/users?needReturnRemark=true"),dataType:"json",type:"GET",headers:{Authorization:"Bearer "+n}};return H.debug("Call getAllContacts"),ae.call(this,i,E.ROSTER_GET_ALL_CONTACTS_REMARKS).then((function(e){var t=((null==e?void 0:e.entities)||[]).map((function(e){return{userId:e.username,remark:e.remark}}));return{type:e.type,data:t}}))}function wo(e){var t=yt.call(this).error;if(t)return Promise.reject(t);H.debug("Call getContactsWithCursor",e);var r=e||{},o=r.pageSize,n=void 0===o?20:o,i=r.cursor,a=void 0===i?"":i;if(n&&"number"!=typeof n)throw Error("Invalid parameter pageSize: ".concat(e.pageSize));var s=this.context,c=s.orgName,u=s.appName,l=s.accessToken,d={url:"".concat(this.apiUrl,"/").concat(c,"/").concat(u,"/users/").concat(this.user,"/contacts?needReturnRemark=true&limit=").concat(n,"&cursor=").concat(a),dataType:"json",type:"GET",headers:{Authorization:"Bearer "+l}};return ae.call(this,d,E.ROSTER_GET_ALL_CONTACTS_REMARKS_FROM_SERVER_BY_PAGE).then((function(e){var t,r,o=(null===(t=null==e?void 0:e.data)||void 0===t?void 0:t.contacts)||[],n=(null===(r=null==e?void 0:e.data)||void 0===r?void 0:r.cursor)||"",i=o.map((function(e){return{userId:e.username,remark:e.remark}}));return{type:e.type,data:{cursor:n,contacts:i}}}))}function Lo(e){return Dr(this,void 0,void 0,(function(){var t,r,o,n,i,a,s,c,u,l,d,p,h,f,m;return kr(this,(function(g){switch(g.label){case 0:if(t=yt.call(this).error)return[2,Promise.reject(t)];if(H.debug("Call markConversation",e),o=(r=e||{}).conversations,n=void 0===o?[]:o,i=r.mark,a=r.isMarked,!Array.isArray(n))throw Error("Invalid parameter conversations");if(s=n.map((function(e){if(!e.conversationId||!["singleChat","groupChat"].includes(e.conversationType))throw Error("Invalid parameter conversations");return{to:e.conversationId,type:xr[e.conversationType]}})),"boolean"!=typeof a)throw Error("Invalid parameter isMarked: ".concat(a));if("number"!=typeof i)throw Error("Invalid parameter mark: ".concat(i));return c=this.context,u=c.orgName,l=c.appName,d=c.accessToken,p=c.userId,h=c.jid,f={mark:at[i],targets:s},m={url:"".concat(this.apiUrl,"/").concat(u,"/").concat(l,"/sdk/user/").concat(p,"/user_channels/mark?resource=").concat(h.clientResource),dataType:"json",data:JSON.stringify(f),type:a?"Post":"Delete",headers:{Authorization:"Bearer "+d}},[4,ae.call(this,m,E.MARK_CONVERSATION).then((function(e){var t,r=(null===(t=e.data)||void 0===t?void 0:t.ignore)||[];r&&Array.isArray(r)&&r.length>0&&H.debug("markConversation has ignored conversations",r.map((function(e){return{conversationId:e.to,conversationType:Re[e.type]}})))}))];case 1:return g.sent(),[2]}}))}))}function Po(e){return Dr(this,void 0,void 0,(function(){var t,r;return kr(this,(function(o){return H.debug("Call addConversationMark",e),t=e.conversations,r=e.mark,[2,Lo.call(this,{conversations:t,mark:r,isMarked:!0})]}))}))}function Do(e){return Dr(this,void 0,void 0,(function(){var t,r;return kr(this,(function(o){return H.debug("Call removeConversationMark",e),t=e.conversations,r=e.mark,[2,Lo.call(this,{conversations:t,mark:r,isMarked:!1})]}))}))}function ko(e){var t,r,o=this,n=yt.call(this).error;if(n)return Promise.reject(n);if(H.debug("Call getServerConversationsByFilter",e),(null==e?void 0:e.pageSize)&&"number"!=typeof e.pageSize)throw Error("Invalid parameter pageSize: ".concat(e.pageSize));if((null==e?void 0:e.cursor)&&"string"!=typeof e.cursor)throw Error("Invalid parameter cursor: ".concat(e.cursor));if("number"!=typeof(null===(t=null==e?void 0:e.filter)||void 0===t?void 0:t.mark))throw Error("Invalid parameter mark: ".concat(null===(r=null==e?void 0:e.filter)||void 0===r?void 0:r.mark));var i=this.context,a=i.orgName,s=i.appName,c=i.accessToken,u="".concat(this.apiUrl,"/").concat(a,"/").concat(s,"/sdk/user/").concat(this.user,"/user_channels/mark/search?"),l=e.pageSize,d=e.cursor,p=((null==e?void 0:e.filter)||{}).mark,h={url:u,type:"GET",dataType:"json",data:{limit:l||10,cursor:d||"",need_mark:!0,mark:at[p]},headers:{Authorization:"Bearer "+c,"Content-Type":"application/json"}};return ae.call(this,h,E.GET_SESSION_LIST).then((function(e){return So.call(o,e)}))}function xo(){var e=yt.call(this).error;if(e)return Promise.reject(e);H.debug("Call getSelfIdsOnOtherPlatform");var t=this.context,r=t.orgName,o=t.appName,n=t.accessToken,i=t.userId,a=t.jid,s={url:"".concat(this.apiUrl,"/").concat(r,"/").concat(o,"/users/").concat(i,"/resources"),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+n,"Content-Type":"application/json"}};return ae.call(this,s,E.USER_LOGGEDIN_OTHER_PLATFORM).then((function(e){var t,r=null===(t=e.data)||void 0===t?void 0:t.filter((function(e){return e.res!==a.clientResource})),o=null==r?void 0:r.map((function(e){return"".concat(i,"/").concat(e.res)}));return{type:e.type,data:o}}))}function Go(){var e,t,r,o,n,i;return Dr(this,void 0,void 0,(function(){var a,s,c,u,l,d,p,h,f;return kr(this,(function(m){switch(m.label){case 0:return(a=yt.call(this).error)?[2,Promise.reject(a)]:(H.debug("Call deleteAllMessagesAndConversations"),s=this.context,c=s.orgName,u=s.appName,l=s.accessToken,d=s.userId,p=s.jid,h="".concat(this.apiUrl,"/").concat(c,"/").concat(u,"/sdk/message/roaming/user/").concat(d,"/delete/all?resource=").concat(p.clientResource),f={url:h,type:"POST",dataType:"json",headers:{Authorization:"Bearer "+l,"Content-Type":"application/json"}},[4,ae.call(this,f,E.REST_DELETE_MESSAGES_CONVERSATIONS)]);case 1:return m.sent(),null===(t=null===(e=this._localCache)||void 0===e?void 0:e.getInstance())||void 0===t||t.clearConversationMap(),[4,null===(o=null===(r=this._localCache)||void 0===r?void 0:r.getInstance())||void 0===o?void 0:o.clearStoreData("conversationList")];case 2:return m.sent(),[4,null===(i=null===(n=this._localCache)||void 0===n?void 0:n.getInstance())||void 0===i?void 0:i.clearStoreData("message")];case 3:return m.sent(),[2]}}))}))}function Bo(e){return Dr(this,void 0,void 0,(function(){var t,r,o,n,i,a,s,c,u,l,d,p,h,f,m;return kr(this,(function(g){switch(g.label){case 0:if(t=yt.call(this).error)return[2,Promise.reject(t)];if(H.debug("Call setMessagePinStatus",e),o=(r=e||{}).conversationId,n=void 0===o?"":o,i=r.conversationType,a=r.messageId,s=r.isPinned,"string"!=typeof n||""===n)throw Error("Invalid parameter conversationId");if("string"!=typeof a||""===a)throw Error("Invalid parameter messageId");if("boolean"!=typeof s)throw Error("Invalid parameter isPinned");return c=this.context,u=c.orgName,l=c.appName,d=c.accessToken,p=c.userId,h=c.jid,f={to:n,type:xr[i],pin_msg_id:a},m={url:"".concat(this.apiUrl,"/").concat(u,"/").concat(l,"/sdk/user/").concat(p,"/user_channel/pin?resource=").concat(h.clientResource),dataType:"json",data:JSON.stringify(f),type:s?"Post":"Delete",headers:{Authorization:"Bearer "+d}},[4,ae.call(this,m,E.REST_PIN_MESSAGE)];case 1:return g.sent(),[2]}}))}))}function Ho(e){return Dr(this,void 0,void 0,(function(){var t,r,o,n;return kr(this,(function(i){return H.debug("Call pinMessage",e),r=(t=e||{}).conversationType,o=t.conversationId,n=t.messageId,[2,Bo.call(this,{conversationId:o,conversationType:r,messageId:n,isPinned:!0})]}))}))}function jo(e){return Dr(this,void 0,void 0,(function(){var t,r,o,n;return kr(this,(function(i){return H.debug("Call unpinMessage",e),r=(t=e||{}).conversationType,o=t.conversationId,n=t.messageId,[2,Bo.call(this,{conversationId:o,conversationType:r,messageId:n,isPinned:!1})]}))}))}function Fo(e){var t=this,r=yt.call(this).error;if(r)return Promise.reject(r);H.debug("Call getServerPinnedMessages",e);var o=e.conversationId,n=e.conversationType,i=e.pageSize,a=e.cursor;if("string"!=typeof o||""===o)throw Error("Invalid parameter conversationId");if(i&&"number"!=typeof i)throw Error("Invalid parameter pageSize: ".concat(i));if(a&&"string"!=typeof a)throw Error("Invalid parameter cursor: ".concat(a));var s=this.context,c=s.orgName,u=s.appName,l=s.accessToken,d={url:"".concat(this.apiUrl,"/").concat(c,"/").concat(u,"/sdk/user/").concat(this.user,"/user_channel/pin"),type:"GET",dataType:"json",data:{to:o,type:xr[n],limit:i||10,cursor:a||""},headers:{Authorization:"Bearer "+l,"Content-Type":"application/json"}};return ae.call(this,d,E.GET_SESSION_LIST).then((function(e){return{type:0,data:{list:e.data.msg_infos.map((function(e){e.message.payload=JSON.parse(e.message.payload);var r=e.message,o=e.pin_opt_at,n=e.pin_operator;return{message:Ce.call(t,r,{formatChatType:!0}),pinTime:o,operatorId:n}})),cursor:e.data.cursor||""}}}))}function Wo(){var e=this;H.debug("Call unbindPushToken");var t={deviceId:this.clientResource,deviceToken:"",notifierName:this.pushCertificateName};return It.call(this,t).then((function(t){return e.isRegisterPush=!1,t}))}var Ko=function(e,t,r,o){return new(r||(r=Promise))((function(n,i){function a(e){try{c(o.next(e))}catch(e){i(e)}}function s(e){try{c(o.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}c((o=o.apply(e,t||[])).next())}))},qo=function(e,t){var r,o,n,i,a={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,o&&(n=2&i[0]?o.return:i[0]?o.throw||((n=o.return)&&n.call(o),0):o.next)&&!(n=n.call(o,i[1])).done)return n;switch(o=0,n&&(i=[2&i[0],n.value]),i[0]){case 0:case 1:n=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,o=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!((n=(n=a.trys).length>0&&n[n.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!n||i[1]>n[0]&&i[1]<n[3])){a.label=i[1];break}if(6===i[0]&&a.label<n[1]){a.label=n[1],n=i;break}if(n&&a.label<n[2]){a.label=n[2],a.ops.push(i);break}n[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],o=0}finally{r=n=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}};function Vo(e){if(!e||!e.data)throw Error("Invalid parameter");var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups?resource=").concat(a.clientResource),dataType:"json",type:"POST",data:JSON.stringify({owner:this.user,groupname:e.data.groupname,avatar:e.data.avatar,desc:e.data.desc,members:e.data.members,public:e.data.public,approval:e.data.approval,allowinvites:e.data.allowinvites,invite_need_confirm:e.data.inviteNeedConfirm,maxusers:e.data.maxusers,custom:e.data.ext}),headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:function(t){e.success&&e.success(t)},error:e.error};return H.debug("Call createGroup:",e),ae.call(this,s,E.CREATE_GROUP)}var zo=Vo;function Jo(e){var t,r,o,n,i,a,s,c,u;if(!e)throw Error("Invalid parameter");var l=yt.call(this).error;if(l)return Promise.reject(l);var p=this.context,h=p.orgName,f=p.appName,m=p.accessToken,g=p.jid,v={url:"".concat(this.apiUrl,"/").concat(h,"/").concat(f,"/chatgroups?resource=").concat(g.clientResource),dataType:"json",type:"POST",data:JSON.stringify({owner:this.user,groupname:null!==(t=e.groupName)&&void 0!==t?t:"",avatar:null!==(r=e.avatar)&&void 0!==r?r:"",desc:null!==(o=e.description)&&void 0!==o?o:"",members:e.members,public:null!==(n=e.isPublic)&&void 0!==n&&n,approval:null!==(i=e.needApprovalToJoin)&&void 0!==i&&i,allowinvites:null===(a=e.allowMemberToInvite)||void 0===a||a,invite_need_confirm:null!==(s=e.inviteNeedConfirm)&&void 0!==s&&s,maxusers:null!==(c=e.maxMemberCount)&&void 0!==c?c:200,custom:null!==(u=e.extension)&&void 0!==u?u:""}),headers:{Authorization:"Bearer "+m,"Content-Type":"application/json"}};return H.debug("Call createGroupVNext:",e),ae.call(this,v,E.CREATE_GROUP).then((function(e){return{type:d.REQUEST_SUCCESS,data:{groupId:e.data.groupid}}}))}function Yo(e){var t;if("string"!=typeof e.groupId||""===e.groupId)throw Error("Invalid parameter");var r=yt.call(this).error;if(r)return Promise.reject(r);var o=this.context,n=o.orgName,i=o.appName,a=o.accessToken,s=o.jid,c={entities:[(t={},t["notification_ignore_"+e.groupId]=!0,t)]},u={type:"PUT",url:"".concat(this.apiUrl,"/").concat(n,"/").concat(i,"/users/").concat(this.user,"?resource=").concat(s.clientResource),data:JSON.stringify(c),dataType:"json",headers:{Authorization:"Bearer "+a,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call blockGroupMessages",e),ae.call(this,u,E.BLOCK_GROUP)}var Xo=Yo;function Qo(e){if("number"!=typeof e.limit)throw Error("Invalid parameter");var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a={limit:e.limit,cursor:e.cursor};e.cursor||delete a.cursor;var s={url:this.apiUrl+"/"+o+"/"+n+"/publicchatgroups",type:"GET",dataType:"json",data:a,headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call listGroups",e),ae.call(this,s,E.LIST_GROUP)}var Zo=Qo;function $o(e){var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a={url:this.apiUrl+"/"+o+"/"+n+"/users/"+this.user+"/joined_chatgroups",dataType:"json",type:"GET",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e&&(null==e?void 0:e.success),error:e&&(null==e?void 0:e.error)};return H.debug("Call getJoinedGroups",e),ae.call(this,a,E.GET_USER_GROUP)}function en(e){if("number"!=typeof e.pageNum||"number"!=typeof e.pageSize)throw Error('Invalid parameter: "pageNum or pageSize"');if(e.pageNum<0||e.pageSize<0)throw Error('"pageNum" should >= 0 and "pageSize" should >= 0');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=e.needAffiliations||e.needRole?"/chatgroups/user/".concat(this.user,"?pagenum=").concat(e.pageNum,"&pagesize=").concat(e.pageSize,"&needAffiliations=").concat(e.needAffiliations,"&needRole=").concat(e.needRole):"/users/".concat(this.user,"/joined_chatgroups?pagenum=").concat(e.pageNum,"&pagesize=").concat(e.pageSize),s={url:this.apiUrl+"/"+o+"/"+n+a,dataType:"json",type:"GET",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e&&(null==e?void 0:e.success),error:e&&(null==e?void 0:e.error)};return H.debug("Call getGroup",e),ae.call(this,s,E.GET_USER_GROUP).then((function(e){var t=e.uri,r=e.entities,o=[];return t.includes("joined_chatgroups")||(r.forEach((function(e){var t={affiliationsCount:e.affiliations_count,groupName:e.name,groupId:e.groupId,role:e.permission,disabled:e.disabled,approval:e.membersonly,allowInvites:e.allowinvites,description:e.description,maxUsers:e.maxusers,public:e.public};o.push(t)})),e.entities=o),e}))}function tn(e){if("string"!=typeof e.groupId||"string"!=typeof e.newOwner)throw Error("Invalid parameter");var t=yt.call(this).error;if(t)return Promise.reject(t);var r={newowner:e.newOwner},o=this.context,n=o.orgName,i=o.appName,a=o.accessToken,s=o.jid,c={url:"".concat(this.apiUrl,"/").concat(n,"/").concat(i,"/chatgroups/").concat(e.groupId,"?resource=").concat(s.clientResource),type:"PUT",dataType:"json",headers:{Authorization:"Bearer "+a,"Content-Type":"application/json"},data:JSON.stringify(r),success:e.success,error:e.error};return H.debug("Call changeOwner",e),ae.call(this,c,E.CHANGE_OWNER)}var rn=tn;function on(e){if("string"!=typeof e.groupId&&!Array.isArray(e.groupId))throw Error('Invalid parameter: "groupId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a={url:this.apiUrl+"/"+o+"/"+n+"/chatgroups/"+e.groupId+"?joined_time=true",type:"GET",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call getGroupInfo",e),ae.call(this,a,E.GET_GROUP_INFO)}function nn(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error("Invalid parameter");var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.groupId,c={groupname:e.groupName,avatar:e.avatar,description:e.description,custom:e.ext},u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(s,"?resource=").concat(a.clientResource),type:"PUT",data:JSON.stringify(c),dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call modifyGroup",e),ae.call(this,u,E.MODIFY_GROUP)}function an(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');if(isNaN(e.pageNum)||e.pageNum<=0)throw Error('The parameter "pageNum" should be a positive number');if(isNaN(e.pageSize)||e.pageSize<=0)throw Error('The parameter "pageSize" should be a positive number');var t=yt.call(this).error;if(t)return Promise.reject(t);var r={pagenum:e.pageNum,pagesize:e.pageSize},o=this.context,n=o.orgName,i=o.appName,a=o.accessToken,s={url:this.apiUrl+"/"+n+"/"+i+"/chatgroups/"+e.groupId+"/users",dataType:"json",type:"GET",data:r,headers:{Authorization:"Bearer "+a,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call listGroupMember",e),ae.call(this,s,E.LIST_GROUP_MEMBER)}var sn=an;function cn(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');if(e.cursor&&"string"!=typeof e.cursor)throw Error('Invalid parameter: "cursor"');if(e.limit&&"number"!=typeof e.limit)throw Error('Invalid parameter: "limit"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r={cursor:e.cursor,limit:e.limit||50,joined_time:!0},o=this.context,n=o.orgName,i=o.appName,a=o.accessToken,s={url:this.apiUrl+"/"+n+"/"+i+"/chatgroups/"+e.groupId+"/users",dataType:"json",type:"GET",data:r,headers:{Authorization:"Bearer "+a,"Content-Type":"application/json"}};return H.debug("Call getGroupMembers",e),ae.call(this,s,E.LIST_GROUP_MEMBER).then((function(e){var t=e.data,r=e.cursor,o=t.map((function(e){for(var t in e)if(["member","admin","owner"].includes(t))return{role:t,userId:e[t],joinedTime:e.joined_time}}));return{type:d.REQUEST_SUCCESS,data:{cursor:r,members:o}}}))}function un(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=e.groupId,s={url:this.apiUrl+"/"+o+"/"+n+"/chatgroups/"+a+"/admin",dataType:"json",type:"GET",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call getGroupAdmin",e),ae.call(this,s,E.GET_GROUP_ADMIN)}function ln(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof e.username||""===e.username)throw Error('Invalid parameter: "username"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.groupId,c={newadmin:e.username},u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(s,"/admin?resource=").concat(a.clientResource),type:"POST",dataType:"json",data:JSON.stringify(c),headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call setGroupAdmin",e),ae.call(this,u,E.SET_GROUP_ADMIN)}var dn=ln;function pn(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof e.username||""===e.username)throw Error('Invalid parameter: "username"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.groupId,c=e.username,u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(s,"/admin/").concat(c,"?resource=").concat(a.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call removeAdmin",e),ae.call(this,u,E.REMOVE_GROUP_ADMIN)}var hn=pn;function fn(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.groupId,c={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(s,"?version=v3&resource=").concat(a.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call destroyGroup",e),ae.call(this,c,E.DISSOLVE_GROUP)}var mn=fn;function gn(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.groupId,c={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(s,"/quit?resource=").concat(a.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call quitGroup",e),ae.call(this,c,E.QUIT_GROUP)}var En=gn;function vn(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');if(!Array.isArray(e.users))throw Error('Invalid parameter: "users"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=e.groupId,o={usernames:e.users},n=this.context,i=n.orgName,a=n.appName,s=n.accessToken,c=n.jid,u={url:"".concat(this.apiUrl,"/").concat(i,"/").concat(a,"/chatgroups/").concat(r,"/invite?resource=").concat(c.clientResource),type:"POST",data:JSON.stringify(o),dataType:"json",headers:{Authorization:"Bearer "+s,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call inviteUsersToGroup",e),ae.call(this,u,E.INVITE_TO_GROUP)}var yn=vn;function _n(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(e.groupId,"/apply?resource=").concat(a.clientResource),type:"POST",dataType:"json",data:JSON.stringify({message:e.message||""}),headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call joinGroup",e),ae.call(this,s,E.JOIN_GROUP)}function Tn(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof e.applicant||""===e.applicant)throw Error('Invalid parameter: "applicant"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.groupId,c={applicant:e.applicant,verifyResult:!0,reason:"no clue"},u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(s,"/apply_verify?resource=").concat(a.clientResource),type:"POST",dataType:"json",data:JSON.stringify(c),headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call agreeJoinGroup",e),ae.call(this,u,E.AGREE_JOIN_GROUP)}var In=Tn;function On(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof e.applicant||""===e.applicant)throw Error('Invalid parameter: "applicant"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.groupId,c={applicant:e.applicant,verifyResult:!1,reason:e.reason||""},u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(s,"/apply_verify?resource=").concat(a.clientResource),type:"POST",dataType:"json",data:JSON.stringify(c),headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call rejectGroupJoinRequest",e),ae.call(this,u,E.REJECT_JOIN_GROUP)}var Rn=On;function Cn(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof e.invitee||""===e.invitee)throw Error('Invalid parameter: "invitee"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.groupId,c={invitee:e.invitee,verifyResult:!0},u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(s,"/invite_verify?resource=").concat(a.clientResource),type:"POST",dataType:"json",data:JSON.stringify(c),headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call acceptGroupInvite",e),ae.call(this,u,E.AGREE_INVITE_GROUP)}var Nn=Cn;function Sn(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof e.invitee||""===e.invitee)throw Error('Invalid parameter: "invitee"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.groupId,c={invitee:e.invitee,verifyResult:!1},u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(s,"/invite_verify?resource=").concat(a.clientResource),type:"POST",dataType:"json",data:JSON.stringify(c),headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call rejectGroupInvite",e),ae.call(this,u,E.REJECT_INVITE_GROUP)}var An=Sn;function bn(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof e.username||""===e.username)throw Error('Invalid parameter: "username"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=e.groupId,o=e.username,n=this.context,i=n.orgName,a=n.appName,s=n.accessToken,c=n.jid,u={url:"".concat(this.apiUrl,"/").concat(i,"/").concat(a,"/chatgroups/").concat(r,"/users/").concat(o,"?resource=").concat(c.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+s,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call removeGroupMember",e),ae.call(this,u,E.REMOVE_GROUP_MEMBER)}var Mn=bn;function Un(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');if(!Array.isArray(e.users))throw Error('Invalid parameter: "users"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.groupId,c=e.users.join(","),u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(s,"/users/").concat(c,"?resource=").concat(a.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call removeGroupMembers",e),ae.call(this,u,E.MULTI_REMOVE_GROUP_MEMBER)}var wn=Un;function Ln(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');if(!(Array.isArray(e.username)||"string"==typeof e.username&&""!==e.username))throw Error('Invalid parameter: "username"');if("number"!=typeof e.muteDuration)throw Error('Invalid parameter: "muteDuration"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.groupId,c={usernames:"string"==typeof e.username?[e.username]:e.username,mute_duration:e.muteDuration},u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(s,"/mute?resource=").concat(a.clientResource),dataType:"json",type:"POST",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},data:JSON.stringify(c),success:e.success,error:e.error};return H.debug("Call muteGroupMember",e),ae.call(this,u,E.MUTE_GROUP_MEMBER)}var Pn=Ln;function Dn(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');if(!(Array.isArray(e.username)||"string"==typeof e.username&&""!==e.username))throw Error('Invalid parameter: "username"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.groupId,c=e.username,u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(s,"/mute/").concat(c,"?resource=").concat(a.clientResource),dataType:"json",type:"DELETE",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call unmuteGroupMember",e),ae.call(this,u,E.UNMUTE_GROUP_MEMBER)}var kn=Dn;function xn(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=e.groupId,s={url:this.apiUrl+"/"+o+"/"+n+"/chatgroups/"+a+"/mute",dataType:"json",type:"GET",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call getGroupMuteList",e),ae.call(this,s,E.GET_GROUP_MUTE_LIST)}var Gn=xn,Bn=xn;function Hn(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof e.username||""===e.username)throw Error('Invalid parameter: "username"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.groupId,c=e.username,u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(s,"/blocks/users/").concat(c,"?resource=").concat(a.clientResource),type:"POST",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call blockGroupMember",e),ae.call(this,u,E.BLOCK_GROUP_MEMBER)}var jn=Hn;function Fn(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');if(!Array.isArray(e.usernames))throw Error('Invalid parameter: "usernames"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.groupId,c={usernames:e.usernames},u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(s,"/blocks/users?resource=").concat(a.clientResource),data:JSON.stringify(c),type:"POST",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call blockGroupMembers",e),ae.call(this,u,E.BLOCK_GROUP_MEMBERS)}var Wn=Fn;function Kn(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof e.username||""===e.username)throw Error('Invalid parameter: "username"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.groupId,c=e.username,u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(s,"/blocks/users/").concat(c,"?resource=").concat(a.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call unblockGroupMember",e),ae.call(this,u,E.UNBLOCK_GROUP_MEMBER)}var qn=Kn;function Vn(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');if(!Array.isArray(e.usernames))throw Error('Invalid parameter: "usernames"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.groupId,c=e.usernames.join(","),u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(s,"/blocks/users/").concat(c,"?resource=").concat(a.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call unblockGroupMembers",e),ae.call(this,u,E.UNBLOCK_GROUP_MEMBERS)}var zn=Vn;function Jn(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=e.groupId,s={url:this.apiUrl+"/"+o+"/"+n+"/chatgroups/"+a+"/blocks/users",type:"GET",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call getGroupBlacklist",e),ae.call(this,s,E.GET_GROUP_BLACK_LIST)}var Yn=Jn,Xn=Jn;function Qn(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.groupId,c={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(s,"/ban?resource=").concat(a.clientResource),type:"POST",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call disableSendGroupMsg",e),ae.call(this,c,E.DISABLED_SEND_GROUP_MSG)}function Zn(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.groupId,c={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(s,"/ban?resource=").concat(a.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call enableSendGroupMsg",e),ae.call(this,c,E.ENABLE_SEND_GROUP_MSG)}function $n(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');if(!Array.isArray(e.users))throw Error('Invalid parameter: "users"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.groupId,c={usernames:e.users},u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(s,"/white/users?resource=").concat(a.clientResource),type:"POST",data:JSON.stringify(c),dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call addUsersToGroupWhitelist",e),ae.call(this,u,E.ADD_USERS_TO_GROUP_WHITE)}var ei=$n;function ti(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof e.userName||""===e.userName)throw Error('Invalid parameter: "userName"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.groupId,c={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(s,"/white/users/").concat(e.userName,"?resource=").concat(a.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call removeGroupAllowlistMember",e),ae.call(this,c,E.REMOVE_GROUP_WHITE_MEMBER)}var ri=ti,oi=ti;function ni(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=e.groupId,s={url:this.apiUrl+"/"+o+"/"+n+"/chatgroups/"+a+"/white/users",type:"GET",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call getGroupAllowlist",e),ae.call(this,s,E.GET_GROUP_WHITE_LIST)}var ii=ni;function ai(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof e.userName||""===e.userName)throw Error('Invalid parameter: "userName"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=e.groupId,s={url:this.apiUrl+"/"+o+"/"+n+"/chatgroups/"+a+"/white/users/"+e.userName,type:"GET",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call isInGroupAllowlist",e),ae.call(this,s,E.IS_IN_GROUP_WHITE_LIST)}var si=ai,ci=ai;function ui(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.userId,s={url:this.apiUrl+"/"+o+"/"+n+"/sdk/chatgroups/"+e.groupId+"/mute/"+a,dataType:"json",type:"GET",headers:{Authorization:"Bearer "+i}};return H.debug("Call isInGroupMutelist",e),ae.call(this,s).then((function(e){return{type:e.type,data:e.data}}))}function li(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof e.msgId||""===e.msgId)throw Error('Invalid parameter: "msgId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a={url:this.apiUrl+"/"+o+"/"+n+"/chatgroups/"+e.groupId+"/acks/"+e.msgId,dataType:"json",type:"GET",data:{limit:500,key:void 0},headers:{Authorization:"Bearer "+i},success:e.success,error:e.error};return H.debug("Call getGroupMsgReadUser",e),ae.call(this,a,E.GET_GROUP_MSG_READ_USER)}function di(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=e.groupId,s={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(a,"/announcement"),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call fetchGroupAnnouncement",e),ae.call(this,s,E.GET_GROUP_ANN)}function pi(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof e.announcement)throw Error('Invalid parameter: "announcement"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.groupId,c={announcement:e.announcement},u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(s,"/announcement?resource=").concat(a.clientResource),type:"POST",dataType:"json",data:JSON.stringify(c),headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call updateGroupAnnouncement",e),ae.call(this,u,E.UPDATE_GROUP_ANN)}function hi(e){var t;if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');if("object"!=typeof e.file)throw Error('Invalid parameter: "file"');var r=yt.call(this).error;if(r)return null===(t=e.onFileUploadError)||void 0===t?void 0:t.call(e,r);var o=this.context,n=o.orgName,i=o.appName,a=o.accessToken,s=o.jid,c=e.groupId;me.call(this,{uploadUrl:"".concat(this.apiUrl,"/").concat(n,"/").concat(i,"/chatgroups/").concat(c,"/share_files?resource=").concat(s.clientResource),onFileUploadProgress:e.onFileUploadProgress,onFileUploadComplete:e.onFileUploadComplete,onFileUploadError:e.onFileUploadError,onFileUploadCanceled:e.onFileUploadCanceled,accessToken:a,apiUrl:this.apiUrl,file:e.file,appKey:this.context.appKey,to:c,chatType:"groupChat"},E.UPLOAD_GROUP_FILE),H.debug("Call uploadGroupSharedFile",e)}function fi(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof e.fileId||""===e.fileId)throw Error('Invalid parameter: "file"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.groupId,c=e.fileId,u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(s,"/share_files/").concat(c,"?resource=").concat(a.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call deleteGroupSharedFile",e),ae.call(this,u,E.DELETE_GROUP_FILE)}function mi(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=e.pageNum||1,s=e.pageSize||10,c=e.groupId,u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(c,"/share_files?pagenum=").concat(a,"&pagesize=").concat(s),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call getGroupSharedFilelist",e),ae.call(this,u,E.GET_GROUP_FILE_LIST)}var gi=mi;function Ei(e){var t=this.context,r=t.orgName,o=t.appName,n=t.accessToken,i=this.apiUrl,a=e.groupId,s=e.fileId;ge.call(this,{url:"".concat(i,"/").concat(r,"/").concat(o,"/chatgroups/").concat(a,"/share_files/").concat(s),onFileDownloadComplete:e.onFileDownloadComplete,onFileDownloadError:e.onFileDownloadError,accessToken:n,id:s,secret:e.secret},E.DOWN_GROUP_FILE),H.debug("Call downloadGroupSharedFile",e)}function vi(e){return Ko(this,void 0,void 0,(function(){var t,r,o,n,i,a,s,c,u,l,d;return qo(this,(function(p){switch(p.label){case 0:if(t=e.groupId,r=e.userId,o=e.memberAttributes,"string"!=typeof t||""===t)throw Error('Invalid parameter: "groupId"');if("string"!=typeof r||""===r)throw Error('Invalid parameter: "userId"');if("object"!=typeof o)throw Error('Invalid parameter: "memberAttributes"');return(n=yt.call(this).error)?[2,Promise.reject(n)]:(i=this.context,a=i.orgName,s=i.appName,c=i.accessToken,u=i.jid,l={metaData:o},d={url:"".concat(this.apiUrl,"/").concat(a,"/").concat(s,"/sdk/metadata/chatgroup/").concat(t,"/user/").concat(r,"?resource=").concat(u.clientResource),type:"PUT",dataType:"json",data:JSON.stringify(l),headers:{Authorization:"Bearer "+c,"Content-Type":"application/json"}},H.debug("Call setGroupMemberAttributes",e),[4,ae.call(this,d,E.SET_GROUP_MEMBER_ATTRS)]);case 1:return p.sent(),[2]}}))}))}function yi(e){var t=e.groupId,r=e.userId;return _i.call(this,{groupId:t,userIds:[r]}).then((function(e){var t;return{type:e.type,data:null===(t=e.data)||void 0===t?void 0:t[r]}}))}function _i(e){var t=e.groupId,r=e.userIds,o=e.keys,n=void 0===o?[]:o;if("string"!=typeof t||""===t)throw Error('Invalid parameter: "groupId"');if(!Array.isArray(r)||(null==r?void 0:r.length)<=0)throw Error('Invalid parameter: "userIds"');if(!Array.isArray(n))throw Error('Invalid parameter: "keys"');var i=yt.call(this).error;if(i)return Promise.reject(i);var a=this.context,s=a.orgName,c=a.appName,u=a.accessToken,l={targets:r,properties:n},d={url:"".concat(this.apiUrl,"/").concat(s,"/").concat(c,"/sdk/metadata/chatgroup/").concat(t,"/get"),type:"POST",dataType:"json",data:JSON.stringify(l),headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"}};return H.debug("Call getGroupMembersAttributes",e),ae.call(this,d,E.GET_GROUP_MEMBER_ATTR).then((function(e){return{type:e.type,data:(null==e?void 0:e.data)||{}}}))}function Ti(){var e=yt.call(this).error;if(e)return Promise.reject(e);var t=this.context,r=t.orgName,o=t.appName,n=t.accessToken,i={url:"".concat(this.apiUrl,"/").concat(r,"/").concat(o,"/chatgroups/user/").concat(this.context.userId,"/joined_count"),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+n,"Content-Type":"application/json"}};return H.debug("Call getJoinedGroupsCount"),ae.call(this,i).then((function(e){return{type:d.REQUEST_SUCCESS,data:e.data||0}}))}function Ii(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.groupId,c={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(s,"/shield?version=v3&resource=").concat(a.clientResource),type:"POST",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call blockGroupMessages",e),ae.call(this,c).then((function(e){return{type:d.REQUEST_SUCCESS,data:e.data||{}}}))}function Oi(e){if("string"!=typeof e.groupId||""===e.groupId)throw Error('Invalid parameter: "groupId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.groupId,c={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatgroups/").concat(s,"/shield?version=v3&resource=").concat(a.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call unblockGroupMessages",e),ae.call(this,c).then((function(e){return{type:d.REQUEST_SUCCESS,data:e.data||{}}}))}function Ri(e){var t=this;if("number"!=typeof e.pagenum||"number"!=typeof e.pagesize)throw Error("Invalid parameter");var r=yt.call(this).error;if(r)return Promise.reject(r);var o={pagenum:e.pagenum||1,pagesize:e.pagesize||20},n=this.context,i=n.orgName,a=n.appName,s=n.accessToken,c={url:this.apiUrl+"/"+i+"/"+a+"/chatrooms",dataType:"json",type:"GET",headers:{Authorization:"Bearer "+s},data:o,success:function(t){"function"==typeof e.success&&e.success(t)},error:function(r){r.error&&r.error_description&&t.onError&&t.onError({type:d.WEBIM_CONNCTION_LOAD_CHATROOM_ERROR,message:r.error_description,data:r}),"function"==typeof e.error&&e.error(r)}};return H.debug("Call getChatRooms",e),ae.call(this,c,E.GET_CHATROOM_LIST)}function Ci(e){if("string"!=typeof e.name)throw Error("Invalid parameter");var t=yt.call(this).error;if(t)return Promise.reject(t);var r={name:e.name,description:e.description,maxusers:e.maxusers,owner:this.user,members:e.members},o=this.context,n=o.orgName,i=o.appName,a=o.accessToken,s=o.jid,c={url:"".concat(this.apiUrl,"/").concat(n,"/").concat(i,"/chatrooms?resource=").concat(s.clientResource),dataType:"json",type:"POST",data:JSON.stringify(r),headers:{Authorization:"Bearer "+(e.token||a),"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call createChatRoom",e),ae.call(this,c,E.CREATE_CHATROOM)}function Ni(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error("Invalid parameter");var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatrooms/").concat(e.chatRoomId,"?resource=").concat(a.clientResource,"&version=v3"),dataType:"json",type:"DELETE",headers:{Authorization:"Bearer "+(e.token||i)},success:e.success,error:e.error};return H.debug("Call destroyChatRoom",e),ae.call(this,s,E.DESTROY_CHATROOM)}function Si(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error("Invalid parameter");var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a={url:this.apiUrl+"/"+o+"/"+n+"/chatrooms/"+e.chatRoomId,dataType:"json",type:"GET",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call getChatRoomDetails",e),ae.call(this,a,E.GET_CHATROOM_DETAIL)}function Ai(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error("Invalid parameter");var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.chatRoomId,c={groupname:e.chatRoomName,description:e.description,maxusers:e.maxusers},u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatrooms/").concat(s,"?resource=").concat(a.clientResource),type:"PUT",data:JSON.stringify(c),dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call modifyChatRoom",e),ae.call(this,u,E.MODIFY_CHATROOM)}function bi(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId||"string"!=typeof e.username)throw Error("Invalid parameter");var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.chatRoomId,c=e.username,u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatrooms/").concat(s,"/users/").concat(c,"?resource=").concat(a.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call removeChatRoomMember",e),ae.call(this,u,E.REMOVE_CHATROOM_MEMBER)}var Mi=bi;function Ui(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId||!Array.isArray(e.users))throw Error("Invalid parameter");var t=yt.call(this).error;if(t)return Promise.reject(t);var r=e.chatRoomId,o=e.users.join(","),n=this.context,i=n.orgName,a=n.appName,s=n.accessToken,c=n.jid,u={url:"".concat(this.apiUrl,"/").concat(i,"/").concat(a,"/chatrooms/").concat(r,"/users/").concat(o,"?resource=").concat(c.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+s,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call removeChatRoomMembers",e),ae.call(this,u,E.MULTI_REMOVE_CHATROOM_MEMBER)}var wi=Ui;function Li(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId||!Array.isArray(e.users))throw Error("Invalid parameter");var t=yt.call(this).error;if(t)return Promise.reject(t);var r=e.chatRoomId,o={usernames:e.users},n=this.context,i=n.orgName,a=n.appName,s=n.accessToken,c=n.jid,u={url:"".concat(this.apiUrl,"/").concat(i,"/").concat(a,"/chatrooms/").concat(r,"/users?resource=").concat(c.clientResource),type:"POST",data:JSON.stringify(o),dataType:"json",headers:{Authorization:"Bearer "+s,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call addUsersToChatRoom",e),ae.call(this,u,E.ADD_USERS_TO_CHATROOM)}function Pi(e){var t=e.roomId,r=e.message,o=void 0===r?"":r,n=e.ext,i=void 0===n?"":n,a=e.leaveOtherRooms,s=void 0!==a&&a,c=e.success,u=e.error;if("string"!=typeof t||""===t)throw Error("Invalid parameter roomId");if("string"!=typeof i)throw Error("Invalid parameter ext");if("boolean"!=typeof s)throw Error("Invalid parameter leaveOtherRooms");var l=yt.call(this).error;return l?Promise.reject(l):(H.debug("Call joinChatRoom",e),this.logOut?Promise.reject({type:d.WEBIM_CONNECTION_CLOSED,message:"not login"}):this.mSync.handleChatRoom({roomId:t,ext:i,leaveOtherRooms:s,message:o,success:c,errorCb:u},"join"))}function Di(e){if("string"!=typeof e.roomId||""===e.roomId)throw Error("Invalid parameter");var t=yt.call(this).error;return t?Promise.reject(t):(H.debug("Call leaveChatRoom",e),this.logOut?Promise.reject({type:d.WEBIM_CONNECTION_CLOSED,message:"not login"}):this.mSync.handleChatRoom(e,"leave"))}var ki=Di;function xi(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');if(isNaN(e.pageNum)||e.pageNum<=0)throw Error('The parameter "pageNum" should be a positive number');if(isNaN(e.pageSize)||e.pageSize<=0)throw Error('The parameter "pageSize" should be a positive number');var t=yt.call(this).error;if(t)return Promise.reject(t);var r={pagenum:e.pageNum,pagesize:e.pageSize},o=this.context,n=o.orgName,i=o.appName,a=o.accessToken,s={url:this.apiUrl+"/"+n+"/"+i+"/chatrooms/"+e.chatRoomId+"/users",dataType:"json",type:"GET",data:r,headers:{Authorization:"Bearer "+a,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call listChatRoomMembers",e),ae.call(this,s,E.LIST_CHATROOM_MEMBERS)}var Gi=xi;function Bi(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');if(e.cursor&&"string"!=typeof e.cursor)throw Error('Invalid parameter: "cursor"');if(e.limit&&"number"!=typeof e.limit)throw Error('Invalid parameter: "limit"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r={cursor:e.cursor,limit:e.limit||50,joined_time:!0},o=this.context,n=o.orgName,i=o.appName,a=o.accessToken,s={url:this.apiUrl+"/"+n+"/"+i+"/chatrooms/"+e.chatRoomId+"/users",dataType:"json",type:"GET",data:r,headers:{Authorization:"Bearer "+a,"Content-Type":"application/json"}};return H.debug("Call getChatRoomMembers",e),ae.call(this,s,E.LIST_GROUP_MEMBER).then((function(e){var t=e.data,r=e.cursor,o=t.map((function(e){for(var t in e)if(["member","admin","owner"].includes(t))return{role:t,userId:e[t],joinedTime:e.joined_time}}));return{type:d.REQUEST_SUCCESS,data:{cursor:r,members:o}}}))}function Hi(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=e.chatRoomId,s={url:this.apiUrl+"/"+o+"/"+n+"/chatrooms/"+a+"/admin",dataType:"json",type:"GET",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call getChatRoomAdmin",e),ae.call(this,s,E.GET_CHATROOM_ADMIN)}function ji(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');if("string"!=typeof e.username||""===e.username)throw Error('Invalid parameter: "username"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.chatRoomId,c={newadmin:e.username},u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatrooms/").concat(s,"/admin?resource=").concat(a.clientResource),type:"POST",dataType:"json",data:JSON.stringify(c),headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call setChatRoomAdmin",e),ae.call(this,u,E.SET_CHATROOM_ADMIN)}function Fi(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');if("string"!=typeof e.username||""===e.username)throw Error('Invalid parameter: "username"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.chatRoomId,c=e.username,u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatrooms/").concat(s,"/admin/").concat(c,"?resource=").concat(a.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call removeChatRoomAdmin",e),ae.call(this,u,E.REMOVE_CHATROOM_ADMIN)}function Wi(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error('Invalid parameter: "groupId"');if("string"!=typeof e.username||""===e.username)throw Error('Invalid parameter: "username"');if("number"!=typeof e.muteDuration)throw Error('Invalid parameter: "muteDuration"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.chatRoomId,c={usernames:[e.username],mute_duration:e.muteDuration},u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatrooms/").concat(s,"/mute?resource=").concat(a.clientResource),dataType:"json",type:"POST",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},data:JSON.stringify(c),success:e.success,error:e.error};return H.debug("Call muteChatRoomMember",e),ae.call(this,u,E.MUTE_CHATROOM_MEMBER)}function Ki(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');if("string"!=typeof e.username||""===e.username)throw Error('Invalid parameter: "username"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.chatRoomId,c=e.username,u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatrooms/").concat(s,"/mute/").concat(c,"?resource=").concat(a.clientResource),dataType:"json",type:"DELETE",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call unmuteChatRoomMember",e),ae.call(this,u,E.REMOVE_MUTE_CHATROOM_MEMBER)}var qi=Ki;function Vi(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=e.chatRoomId,s={url:this.apiUrl+"/"+o+"/"+n+"/chatrooms/"+a+"/mute",dataType:"json",type:"GET",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call getChatRoomMutelist",e),ae.call(this,s,E.GET_MUTE_CHATROOM_MEMBERS)}var zi=Vi,Ji=Vi;function Yi(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');if("string"!=typeof e.username||""===e.username)throw Error('Invalid parameter: "username"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.chatRoomId,c=e.username,u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatrooms/").concat(s,"/blocks/users/").concat(c,"?resource=").concat(a.clientResource),type:"POST",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call blockChatRoomMember",e),ae.call(this,u,E.SET_CHATROOM_MEMBER_TO_BLACK)}var Xi=Yi;function Qi(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');if(!Array.isArray(e.usernames))throw Error('Invalid parameter: "usernames"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.chatRoomId,c={usernames:e.usernames},u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatrooms/").concat(s,"/blocks/users?resource=").concat(a.clientResource),data:JSON.stringify(c),type:"POST",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Chat blockChatRoomMembers:",u),ae.call(this,u,E.MULTI_SET_CHATROOM_MEMBER_TO_BLACK)}var Zi=Qi;function $i(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');if("string"!=typeof e.username||""===e.username)throw Error('Invalid parameter: "username"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.chatRoomId,c=e.username,u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatrooms/").concat(s,"/blocks/users/").concat(c,"?resource=").concat(a.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call unblockChatRoomMember",e),ae.call(this,u,E.REMOVE_CHATROOM_MEMBER_BLACK)}var ea=$i;function ta(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');if(!Array.isArray(e.usernames))throw Error('Invalid parameter: "usernames"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.chatRoomId,c=e.usernames.join(","),u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatrooms/").concat(s,"/blocks/users/").concat(c,"?resource=").concat(a.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call unblockChatRoomMembers",e),ae.call(this,u,E.MULTI_REMOVE_CHATROOM_MEMBER_BLACK)}var ra=ta;function oa(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=e.chatRoomId,s={url:this.apiUrl+"/"+o+"/"+n+"/chatrooms/"+a+"/blocks/users",type:"GET",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call getChatRoomBlocklist",e),ae.call(this,s,E.GET_CHATROOM_BLOCK_MEMBERS)}var na=oa,ia=oa;function aa(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.chatRoomId,c={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatrooms/").concat(s,"/ban?resource=").concat(a.clientResource),type:"POST",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call disableSendChatRoomMsg",e),ae.call(this,c,E.DISABLED_CHATROOM_SEND_MSG)}function sa(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.chatRoomId,c={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatrooms/").concat(s,"/ban?resource=").concat(a.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call enableSendChatRoomMsg",e),ae.call(this,c,E.ENABLE_CHATROOM_SEND_MSG)}function ca(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');if(!Array.isArray(e.users))throw Error('Invalid parameter: "users"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.chatRoomId,c={usernames:e.users},u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatrooms/").concat(s,"/white/users?resource=").concat(a.clientResource),type:"POST",data:JSON.stringify(c),dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call addUsersToChatRoomWhitelist",e),ae.call(this,u,E.ADD_USERS_TO_CHATROOM)}var ua=ca;function la(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');if("string"!=typeof e.userName||""===e.userName)throw Error('Invalid parameter: "userName"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.chatRoomId,c={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatrooms/").concat(s,"/white/users/").concat(e.userName,"?resource=").concat(a.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call removeChatRoomAllowlistMember",e),ae.call(this,c,E.REMOVE_CHATROOM_WHITE_USERS)}var da=la,pa=la;function ha(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=e.chatRoomId,s={url:this.apiUrl+"/"+o+"/"+n+"/chatrooms/"+a+"/white/users",type:"GET",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call getChatRoomAllowlist",e),ae.call(this,s,E.GET_CHATROOM_WHITE_USERS)}var fa=ha;function ma(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');if("string"!=typeof e.userName||""===e.userName)throw Error('Invalid parameter: "userName"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=e.chatRoomId,s={url:this.apiUrl+"/"+o+"/"+n+"/chatrooms/"+a+"/white/users/"+e.userName,type:"GET",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call isInChatRoomAllowlist",e),ae.call(this,s,E.CHECK_CHATROOM_WHITE_USER)}var ga=ma;function Ea(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error('Invalid parameter: "chatRoomId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.userId,s={url:this.apiUrl+"/"+o+"/"+n+"/sdk/chatrooms/"+e.chatRoomId+"/mute/"+a,dataType:"json",type:"GET",headers:{Authorization:"Bearer "+i}};return H.debug("Call isInChatRoomMutelist",e),ae.call(this,s).then((function(e){return{type:e.type,data:e.data}}))}function va(e){if("string"!=typeof e.roomId||""===e.roomId)throw Error('Invalid parameter: "roomId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=e.roomId,s={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatrooms/").concat(a,"/announcement"),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call fetchChatRoomAnnouncement",e),ae.call(this,s,E.GET_CHATROOM_ANN)}function ya(e){if("string"!=typeof e.roomId||""===e.roomId)throw Error('Invalid parameter: "roomId"');if("string"!=typeof e.announcement)throw Error('Invalid parameter: "announcement"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.roomId,c={announcement:e.announcement},u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatrooms/").concat(s,"/announcement?resource=").concat(a.clientResource),type:"POST",dataType:"json",data:JSON.stringify(c),headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call updateChatRoomAnnouncement:",e),ae.call(this,u,E.UPDATE_CHATROOM_ANN)}function _a(e){var t;if("string"!=typeof e.roomId||""===e.roomId)throw Error('Invalid parameter: "roomId"');if("object"!=typeof e.file)throw Error('Invalid parameter: "file"');var r=yt.call(this).error;if(r)return null===(t=e.onFileUploadError)||void 0===t?void 0:t.call(e,r);var o=this.context,n=o.orgName,i=o.appName,a=o.accessToken,s=o.jid,c=e.roomId;me.call(this,{uploadUrl:"".concat(this.apiUrl,"/").concat(n,"/").concat(i,"/chatrooms/").concat(c,"/share_files?resource=").concat(s.clientResource),onFileUploadProgress:e.onFileUploadProgress,onFileUploadComplete:e.onFileUploadComplete,onFileUploadError:e.onFileUploadError,onFileUploadCanceled:e.onFileUploadCanceled,accessToken:a,apiUrl:this.apiUrl,file:e.file,appKey:this.context.appKey,to:c,chatType:"chatRoom"},E.UPLOAD_CHATROOM_FILE),H.debug("Call uploadChatRoomSharedFile",e)}function Ta(e){if("string"!=typeof e.roomId||""===e.roomId)throw Error('Invalid parameter: "roomId"');if("string"!=typeof e.fileId||""===e.fileId)throw Error('Invalid parameter: "fileId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s=e.roomId,c=e.fileId,u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatrooms/").concat(s,"/share_files/").concat(c,"?resource=").concat(a.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call deleteChatRoomSharedFile",e),ae.call(this,u,E.DELETE_CHATROOM_FILE)}function Ia(e){if("string"!=typeof e.roomId||""===e.roomId)throw Error('Invalid parameter: "roomId"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=e.roomId,s={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/chatrooms/").concat(a,"/share_files"),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json",accept:"application/json"},success:e.success,error:e.error};return H.debug("Call fetchChatRoomSharedFileList",e),ae.call(this,s,E.GET_CHATROOM_FILES)}var Oa=Ia;function Ra(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error("Invalid parameter chatRoomId: "+e.chatRoomId);if(e.attributeKeys&&!Array.isArray(e.attributeKeys))throw Error('"Invalid parameter attributeKeys": '+e.attributeKeys);var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=(r.jid,e.chatRoomId),s={keys:e.attributeKeys},c={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/metadata/chatroom/").concat(a),type:"POST",dataType:"json",data:JSON.stringify(s),headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return H.debug("Call getChatRoomAttributes:",e),ae.call(this,c,E.GET_CHATROOM_ATTR).then((function(e){return{data:e.data,type:e.type}}))}function Ca(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error("Invalid parameter chatRoomId: "+e.chatRoomId);if("object"!=typeof e.attributes)throw Error("Invalid parameter attributes: "+e.attributes);var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.userId,s=e.chatRoomId,c=e.attributes,u=e.autoDelete,l=void 0===u||u,d=e.isForced?"/forced":"",p={metaData:c,autoDelete:l?"DELETE":"NO_DELETE"},h={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/metadata/chatroom/").concat(s,"/user/").concat(a)+d,type:"PUT",dataType:"json",data:JSON.stringify(p),headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return H.debug("Call setChatRoomAttributes:",e),ae.call(this,h,E.SET_CHATROOM_ATTR).then((function(e){return _t(e)}))}function Na(e){var t;if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error("Invalid parameter chatRoomId: "+e.chatRoomId);if("string"!=typeof e.attributeKey||""===e.attributeKey)throw Error("Invalid parameter attributeKey: "+e.attributeKey);if("string"!=typeof e.attributeValue||""===e.attributeValue)throw Error("Invalid parameter attributeValue: "+e.attributeValue);var r=yt.call(this).error;if(r)return Promise.reject(r);var o=this.context,n=o.orgName,i=o.appName,a=o.accessToken,s=o.userId,c=e.chatRoomId,u=e.attributeKey,l=e.attributeValue,d=e.autoDelete,p=void 0===d||d,h=e.isForced?"/forced":"",f={metaData:(t={},t[u]=l,t),autoDelete:p?"DELETE":"NO_DELETE"},m={url:"".concat(this.apiUrl,"/").concat(n,"/").concat(i,"/metadata/chatroom/").concat(c,"/user/").concat(s)+h,type:"PUT",dataType:"json",data:JSON.stringify(f),headers:{Authorization:"Bearer "+a,"Content-Type":"application/json"}};return H.debug("Call setChatRoomAttribute:",e),ae.call(this,m,E.SET_CHATROOM_ATTR).then((function(e){var t=Tt(e);if(t)throw t}))}function Sa(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error("Invalid parameter chatRoomId: "+e.chatRoomId);if(!Array.isArray(e.attributeKeys))throw Error('"Invalid parameter attributes": '+e.attributeKeys);var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.userId,s=e.chatRoomId,c=e.attributeKeys,u=e.isForced?"/forced":"",l={keys:c},d={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/metadata/chatroom/").concat(s,"/user/").concat(a)+u,type:"DELETE",dataType:"json",data:JSON.stringify(l),headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return H.debug("Call removeChatRoomAttributes:",e),ae.call(this,d,E.DELETE_CHATROOM_ATTR).then((function(e){return _t(e)}))}function Aa(e){if("string"!=typeof e.chatRoomId||""===e.chatRoomId)throw Error("Invalid parameter chatRoomId: "+e.chatRoomId);if("string"!=typeof e.attributeKey||""===e.attributeKey)throw Error('"Invalid parameter attributeKey": '+e.attributeKey);var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.userId,s=e.chatRoomId,c=e.attributeKey,u=e.isForced?"/forced":"",l={keys:[c]},d={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/metadata/chatroom/").concat(s,"/user/").concat(a)+u,type:"DELETE",dataType:"json",data:JSON.stringify(l),headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return H.debug("Call removeChatRoomAttribute:",e),ae.call(this,d,E.DELETE_CHATROOM_ATTR).then((function(e){var t=Tt(e);if(t)throw t}))}function ba(e){var t=this,r=e||{},o=r.pageNum,n=r.pageSize;if(isNaN(o)||o<=0)throw Error("Invalid parameter pageNum:".concat(o));if(isNaN(n)||n<=0)throw Error("Invalid parameter pageSize:".concat(n));var i=yt.call(this).error;if(i)return Promise.reject(i);var a={pagenum:o,pagesize:n,detail:!0},s=this.context,c=s.orgName,u=s.appName,l=s.accessToken,d={url:"".concat(this.apiUrl,"/").concat(c,"/").concat(u,"/users/").concat(this.user,"/joined_chatrooms"),dataType:"json",type:"GET",data:a,headers:{Authorization:"Bearer "+l,"Content-Type":"application/json"}};return H.debug("Call getJoinedChatRooms",e),ae.call(this,d,E.GET_USER_JOINED_CHATROOM).then((function(e){var r=(e.data||[]).map((function(e){var r=e.id,o=e.title,n=e.owner,i=e.created,a=e.description,s=e.max_users;return{id:r,name:o,owner:n.split("".concat(t.appKey,"_"))[1],created:i,description:a,maxusers:s}}));return{type:e.type,data:r}}))}var Ma=function(e,t,r,o){return new(r||(r=Promise))((function(n,i){function a(e){try{c(o.next(e))}catch(e){i(e)}}function s(e){try{c(o.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}c((o=o.apply(e,t||[])).next())}))},Ua=function(e,t){var r,o,n,i,a={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,o&&(n=2&i[0]?o.return:i[0]?o.throw||((n=o.return)&&n.call(o),0):o.next)&&!(n=n.call(o,i[1])).done)return n;switch(o=0,n&&(i=[2&i[0],n.value]),i[0]){case 0:case 1:n=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,o=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!((n=(n=a.trys).length>0&&n[n.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!n||i[1]>n[0]&&i[1]<n[3])){a.label=i[1];break}if(6===i[0]&&a.label<n[1]){a.label=n[1],n=i;break}if(n&&a.label<n[2]){a.label=n[2],a.ops.push(i);break}n[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],o=0}finally{r=n=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}};function wa(e){return Ma(this,void 0,void 0,(function(){var t,r,o,n,i,a,s,c,u;return Ua(this,(function(l){switch(l.label){case 0:if("string"!=typeof e.description)throw Error('Invalid parameter: "description"');return(t=yt.call(this).error)?[2,Promise.reject(t)]:(r=this.context,o=r.accessToken,n=r.orgName,i=r.appName,a=r.userId,s=this.context.jid.clientResource,c={ext:e.description},u={url:"".concat(this.apiUrl,"/").concat(n,"/").concat(i,"/users/").concat(a,"/presence/").concat(s,"/1"),type:"POST",dataType:"json",data:JSON.stringify(c),headers:{Authorization:"Bearer "+o,"Content-Type":"application/json"},success:e.success,error:e.error},H.debug("Call publishPresence:",e),[4,ae.call(this,u)]);case 1:return l.sent(),[2]}}))}))}function La(e){if(!Array.isArray(e.usernames))throw Error('Invalid parameter: "usernames"');if(!e.usernames.length)throw Error('"usernames" can not be empty');if("number"!=typeof e.expiry)throw Error('Invalid parameter: "expiry"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.userId,a=r.accessToken,s={usernames:e.usernames},c={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/users/").concat(i,"/presence/").concat(e.expiry),type:"POST",dataType:"json",data:JSON.stringify(s),headers:{Authorization:"Bearer "+a,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call subscribePresence:",e),ae.call(this,c).then((function(e){return e.data={result:null==e?void 0:e.result},e}))}function Pa(e){return Ma(this,void 0,void 0,(function(){var t,r,o,n,i,a,s;return Ua(this,(function(c){switch(c.label){case 0:if(!Array.isArray(e.usernames))throw Error('Invalid parameter: "usernames"');if(!e.usernames.length)throw Error('"usernames" can not be empty');return(t=yt.call(this).error)?[2,Promise.reject(t)]:(r=this.context,o=r.orgName,n=r.appName,i=r.userId,a=r.accessToken,s={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/users/").concat(i,"/presence"),type:"DELETE",dataType:"json",data:JSON.stringify(e.usernames),headers:{Authorization:"Bearer "+a,"Content-Type":"application/json"},success:e.success,error:e.error},H.debug("Call unsubscribePresence:",e),[4,ae.call(this,s)]);case 1:return c.sent(),[2]}}))}))}function Da(e){if("number"!=typeof e.pageNum||"number"!=typeof e.pageSize)throw Error('Invalid parameter: "pageNum or pageSize"');if(e.pageNum<0||e.pageSize<0)throw Error('"pageNum" should >= 0 and "pageSize" should >= 0');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.userId,a=r.accessToken,s={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/users/").concat(i,"/presence/sublist?pageNum=").concat(e.pageNum,"&pageSize=").concat(e.pageSize),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+a,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call getSubscribedPresenceList:",e),ae.call(this,s).then((function(e){return e.data={result:null==e?void 0:e.result},e}))}var ka=Da;function xa(e){if(!Array.isArray(e.usernames))throw Error('Invalid parameter: "usernames"');if(!e.usernames.length)throw Error('"usernames" can not be empty');var t=yt.call(this).error;if(t)return Promise.reject(t);var r={usernames:e.usernames},o=this.context,n=o.orgName,i=o.appName,a=o.userId,s=o.accessToken,c={url:"".concat(this.apiUrl,"/").concat(n,"/").concat(i,"/users/").concat(a,"/presence"),type:"POST",dataType:"json",data:JSON.stringify(r),headers:{Authorization:"Bearer "+s,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call getPresenceStatus:",e),ae.call(this,c).then((function(e){return e.data={result:null==e?void 0:e.result},e}))}function Ga(e){if(!(e.options instanceof Object))throw Error('Invalid parameter: "options"');var t=e.options.paramType;if("number"!=typeof t||t<0||t>2)throw Error('Invalid parameter: "options of paramType"');if(0===t){if("string"!=typeof e.options.remindType)throw Error('Invalid parameter: "options of remindType"')}else if(1===t){if("number"!=typeof e.options.duration)throw Error('Invalid parameter: "options of duration"')}else if(2===t){var r=e.options,o=r.startTime,n=r.endTime;if(!(o instanceof Object&&Object.keys(o).length))throw Error('Invalid parameter: "options of startTime"');if(!o.hours||"number"!=typeof o.hours||!o.minutes||"number"!=typeof o.minutes)throw Error('Invalid parameter: "options of startTime of hours or minutes"');if(!(n instanceof Object&&Object.keys(n).length))throw Error('Invalid parameter: "options of endTime"');if(!n.hours||"number"!=typeof n.hours||!n.minutes||"number"!=typeof n.minutes)throw Error('Invalid parameter: "options of endTime of hours or minutes"')}var i=yt.call(this).error;if(i)return Promise.reject(i);var a=this.context,s=a.accessToken,c=a.orgName,u=a.appName,l=a.userId,d=a.jid,p={};switch(t){case 0:p={type:e.options.remindType};break;case 1:p={ignoreDuration:e.options.duration};break;case 2:var h=e.options;o=h.startTime,n=h.endTime,p={ignoreInterval:"".concat(o.hours,":").concat(o.minutes,"-").concat(n.hours,":").concat(n.minutes)}}var f={url:"".concat(this.apiUrl,"/").concat(c,"/").concat(u,"/users/").concat(l,"/notification/user/").concat(l,"?resource=").concat(d.clientResource),type:"PUT",dataType:"json",data:JSON.stringify(p),headers:{Authorization:"Bearer "+s,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call setSilentModeForAll:",e),ae.call(this,f)}function Ba(e){var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.accessToken,n=r.orgName,i=r.appName,a=r.userId,s={url:"".concat(this.apiUrl,"/").concat(n,"/").concat(i,"/users/").concat(a,"/notification/user/").concat(a),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+o,"Content-Type":"application/json"},success:null==e?void 0:e.success,error:null==e?void 0:e.error};return H.debug("Call getSilentModeForAll:",e),ae.call(this,s)}function Ha(e){if("string"!=typeof e.conversationId||!e.conversationId)throw Error('Invalid parameter: "conversationId"');if("string"!=typeof e.type||!e.type)throw Error('Invalid parameter: "type"');if(!(e.options instanceof Object))throw Error('Invalid parameter: "options"');var t=e.options.paramType;if("number"!=typeof t||t<0||t>2)throw Error('Invalid parameter: "options of paramType"');if(0===t){if("string"!=typeof e.options.remindType)throw Error('Invalid parameter: "options of remindType"')}else if(1===t){if("number"!=typeof e.options.duration)throw Error('Invalid parameter: "options of duration"')}else if(2===t){var r=e.options,o=r.startTime,n=r.endTime;if(!(o instanceof Object&&Object.keys(o).length))throw Error('Invalid parameter: "options of startTime"');if(!o.hours||"number"!=typeof o.hours||!o.minutes||"number"!=typeof o.minutes)throw Error('Invalid parameter: "options of startTime of hours or minutes"');if(!(n instanceof Object&&Object.keys(n).length))throw Error('Invalid parameter: "options of endTime"');if(!n.hours||"number"!=typeof n.hours||!n.minutes||"number"!=typeof n.minutes)throw Error('Invalid parameter: "options of endTime of hours or minutes"')}var i=yt.call(this).error;if(i)return Promise.reject(i);var a=this.context,s=a.accessToken,c=a.orgName,u=a.appName,l=a.userId,d=a.jid,p="chatgroup",h={};switch(t){case 0:h={type:e.options.remindType};break;case 1:h={ignoreDuration:e.options.duration};break;case 2:var f=e.options;o=f.startTime,n=f.endTime,h={ignoreInterval:"".concat(o.hours,":").concat(o.minutes,"-").concat(n.hours,":").concat(n.minutes)}}"singleChat"===e.type&&(p="user");var m={url:"".concat(this.apiUrl,"/").concat(c,"/").concat(u,"/users/").concat(l,"/notification/").concat(p,"/").concat(e.conversationId,"?resource=").concat(d.clientResource),type:"PUT",dataType:"json",data:JSON.stringify(h),headers:{Authorization:"Bearer "+s,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call setSilentModeForConversation:",e),ae.call(this,m)}function ja(e){if("string"!=typeof e.conversationId||!e.conversationId)throw Error('Invalid parameter: "conversationId"');if("string"!=typeof e.type||!e.type)throw Error('Invalid parameter: "type"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.accessToken,n=r.orgName,i=r.appName,a=r.userId,s=r.jid,c="chatgroup";"singleChat"===e.type&&(c="user");var u={url:"".concat(this.apiUrl,"/").concat(n,"/").concat(i,"/users/").concat(a,"/notification/").concat(c,"/").concat(e.conversationId,"?resource=").concat(s.clientResource),type:"PUT",dataType:"json",data:JSON.stringify({type:"DEFAULT"}),headers:{Authorization:"Bearer "+o,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call clearRemindTypeForConversation:",e),ae.call(this,u)}function Fa(e){if("string"!=typeof e.conversationId||!e.conversationId)throw Error('Invalid parameter: "conversationId"');if("string"!=typeof e.type||!e.type)throw Error('Invalid parameter: "type"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.accessToken,n=r.orgName,i=r.appName,a=r.userId,s="chatgroup";"singleChat"===e.type&&(s="user");var c={url:"".concat(this.apiUrl,"/").concat(n,"/").concat(i,"/users/").concat(a,"/notification/").concat(s,"/").concat(e.conversationId),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+o,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call getSilentModeForConversation:",e),ae.call(this,c)}function Wa(e){if(!Array.isArray(e.conversationList))throw Error('Invalid parameter: "conversationList"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.accessToken,n=r.orgName,i=r.appName,a=r.userId,s=[],c=[];e.conversationList.forEach((function(e){"singleChat"===e.type?s.push(e.id):c.push(e.id)}));var u=s.length?s.join(","):"",l=c.length?c.join(","):"",d={url:"".concat(this.apiUrl,"/").concat(n,"/").concat(i,"/users/").concat(a,"/notification?user=").concat(u,"&group=").concat(l),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+o,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call getSilentModeForConversations:",e),ae.call(this,d)}function Ka(e){if("string"!=typeof e.language||!e.language)throw Error('Invalid parameter: "language"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r={translationLanguage:e.language},o=this.context,n=o.accessToken,i=o.orgName,a=o.appName,s=o.userId,c={url:"".concat(this.apiUrl,"/").concat(i,"/").concat(a,"/users/").concat(s,"/notification/language"),type:"PUT",dataType:"json",data:JSON.stringify(r),headers:{Authorization:"Bearer "+n,"Content-Type":"application/json"},success:e.success,error:e.error};return H.debug("Call setPushPerformLanguage:",e),ae.call(this,c)}function qa(e){var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.accessToken,n=r.orgName,i=r.appName,a=r.userId,s={url:"".concat(this.apiUrl,"/").concat(n,"/").concat(i,"/users/").concat(a,"/notification/language"),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+o,"Content-Type":"application/json"},success:null==e?void 0:e.success,error:null==e?void 0:e.error};return H.debug("Call getPushPerformLanguage:",e),ae.call(this,s)}function Va(e){var t=yt.call(this).error;if(t)return Promise.reject(t);if("number"!=typeof e.pageSize)throw Error('Invalid parameter: "pageSize"');var r=this.context,o=r.accessToken,n=r.orgName,i=r.appName,a=r.userId,s={limit:e.pageSize||10,cursor:e.cursor};e.cursor||delete s.cursor;var c={url:"".concat(this.apiUrl,"/").concat(n,"/").concat(i,"/users/").concat(a,"/notification/mute/type"),type:"GET",dataType:"json",data:s,headers:{Authorization:"Bearer "+o,"Content-Type":"application/json"}};return H.debug("Call getSilentModeRemindTypeConversations:",e),ae.call(this,c).then((function(e){return e.data?{data:{conversations:e.data.map((function(e){return"user"in e?{conversationId:e.user,type:"singleChat",remindType:e.value}:{conversationId:e.group,type:"groupChat",remindType:e.value}})),cursor:e.cursor},type:e.type}:e}))}var za=function(e,t,r,o){return new(r||(r=Promise))((function(n,i){function a(e){try{c(o.next(e))}catch(e){i(e)}}function s(e){try{c(o.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}c((o=o.apply(e,t||[])).next())}))},Ja=function(e,t){var r,o,n,i,a={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,o&&(n=2&i[0]?o.return:i[0]?o.throw||((n=o.return)&&n.call(o),0):o.next)&&!(n=n.call(o,i[1])).done)return n;switch(o=0,n&&(i=[2&i[0],n.value]),i[0]){case 0:case 1:n=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,o=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!((n=(n=a.trys).length>0&&n[n.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!n||i[1]>n[0]&&i[1]<n[3])){a.label=i[1];break}if(6===i[0]&&a.label<n[1]){a.label=n[1],n=i;break}if(n&&a.label<n[2]){a.label=n[2],a.ops.push(i);break}n[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],o=0}finally{r=n=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}};function Ya(e){if("string"!=typeof e.name||""===e.name)throw Error("Invalid parameter name: ".concat(e.name));if("string"!=typeof e.messageId||""===e.messageId)throw Error("Invalid parameter messageId: ".concat(e.messageId));if("string"!=typeof e.parentId||""===e.parentId)throw Error("Invalid parameter parentId: ".concat(e.parentId));var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s={name:e.name,msg_id:e.messageId,group_id:e.parentId,owner:this.user},c={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/thread?resource=").concat(a.clientResource),type:"POST",dataType:"json",data:JSON.stringify(s),headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return ae.call(this,c).then((function(e){var t=e.data.thread_id;return e.data={chatThreadId:t},e}))}function Xa(e){if("string"!=typeof e.chatThreadId||""===e.chatThreadId)throw Error("Invalid parameter chatThreadId: ".concat(e.chatThreadId));var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/thread/").concat(e.chatThreadId,"/user/").concat(this.user,"/join?resource=").concat(a.clientResource),type:"POST",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return ae.call(this,s).then((function(e){var t=e.data.detail;return t.messageId=t.msgId,t.parentId=t.groupId,delete t.msgId,delete t.groupId,e}))}function Qa(e){return za(this,void 0,void 0,(function(){var t,r,o,n,i,a,s;return Ja(this,(function(c){switch(c.label){case 0:if("string"!=typeof e.chatThreadId||""===e.chatThreadId)throw Error("Invalid parameter chatThreadId: ".concat(e.chatThreadId));return(t=yt.call(this).error)?[2,Promise.reject(t)]:(r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/thread/").concat(e.chatThreadId,"/user/").concat(this.user,"/quit?resource=").concat(a.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}},[4,ae.call(this,s)]);case 1:return c.sent(),[2]}}))}))}function Za(e){return za(this,void 0,void 0,(function(){var t,r,o,n,i,a,s;return Ja(this,(function(c){switch(c.label){case 0:if("string"!=typeof e.chatThreadId||""===e.chatThreadId)throw Error("Invalid parameter chatThreadId: ".concat(e.chatThreadId));return(t=yt.call(this).error)?[2,Promise.reject(t)]:(r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/thread/").concat(e.chatThreadId,"?resource=").concat(a.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}},[4,ae.call(this,s)]);case 1:return c.sent(),[2]}}))}))}function $a(e){if("string"!=typeof e.chatThreadId||""===e.chatThreadId)throw Error("Invalid parameter chatThreadId: ".concat(e.chatThreadId));if("string"!=typeof e.name||""===e.name)throw Error("Invalid parameter name: ".concat(e.name));var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s={name:e.name},c={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/thread/").concat(e.chatThreadId,"?resource=").concat(a.clientResource),type:"PUT",dataType:"json",data:JSON.stringify(s),headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return ae.call(this,c)}function es(e){if("string"!=typeof e.chatThreadId||""===e.chatThreadId)throw Error("Invalid parameter chatThreadId: ".concat(e.chatThreadId));var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a={limit:e.pageSize||20,cursor:e.cursor||""},s={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/thread/").concat(e.chatThreadId,"/users"),type:"GET",dataType:"json",data:a,headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return ae.call(this,s)}function ts(e){if("string"!=typeof e.chatThreadId||""===e.chatThreadId)throw Error("Invalid parameter chatThreadId: ".concat(e.chatThreadId));if("string"!=typeof e.username||""===e.username)throw Error("Invalid parameter username: ".concat(e.username));var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a=r.jid,s={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/thread/").concat(e.chatThreadId,"/users/").concat(e.username,"?resource=").concat(a.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return ae.call(this,s)}function rs(e){var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a={limit:e.pageSize||20,cursor:e.cursor||""},s={url:e.parentId?"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/threads/chatgroups/").concat(e.parentId,"/user/").concat(this.user):"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/threads/user/").concat(this.user),type:"GET",dataType:"json",data:a,headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return ae.call(this,s).then((function(e){var t=e.entities;return null==t||t.forEach((function(e){e.parentId=e.groupId,e.messageId=e.msgId,delete e.groupId,delete e.msgId})),e}))}function os(e){if("string"!=typeof e.parentId||""===e.parentId)throw Error("Invalid parameter parentId: ".concat(e.parentId));var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a={cursor:e.cursor||"",limit:e.pageSize||20},s={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/threads/chatgroups/").concat(e.parentId),type:"GET",dataType:"json",data:a,headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return ae.call(this,s).then((function(e){var t=e.entities;return null==t||t.forEach((function(e){e.parentId=e.groupId,e.messageId=e.msgId,delete e.groupId,delete e.msgId})),e}))}function ns(e){var t=this;if(!Array.isArray(e.chatThreadIds))throw Error("Invalid parameter chatThreadIds: ".concat(e.chatThreadIds));var r=yt.call(this).error;if(r)return Promise.reject(r);var o=this.context,n=o.orgName,i=o.appName,a=o.accessToken,s={threadIds:e.chatThreadIds},c={url:"".concat(this.apiUrl,"/").concat(n,"/").concat(i,"/thread/message"),type:"POST",dataType:"json",data:JSON.stringify(s),headers:{Authorization:"Bearer "+a,"Content-Type":"application/json"}};return ae.call(this,c).then((function(e){return is.call(t,e)}))}function is(e){var t=this,r=e.entities;return null==r||r.forEach((function(e){e.chatThreadId=e.thread_id,e.last_message&&"{}"!==JSON.stringify(e.last_message)?e.lastMessage=Ce.call(t,e.last_message):e.lastMessage=e.last_message,delete e.thread_id,delete e.last_message})),e}function as(e){if("string"!=typeof e.chatThreadId||""===e.chatThreadId)throw Error("Invalid parameter chatThreadId: ".concat(e.chatThreadId));var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/thread/").concat(e.chatThreadId),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return ae.call(this,a).then((function(e){return e.data.affiliationsCount=e.data.affiliations_count,e.data.messageId=e.data.msgId,e.data.parentId=e.data.groupId,delete e.data.affiliations_count,delete e.data.msgId,delete e.data.groupId,e}))}function ss(){var e=yt.call(this).error;if(e)return Promise.reject(e);var t=this.context,r=t.orgName,o=t.appName,n=t.accessToken,i={url:"".concat(this.apiUrl,"/").concat(r,"/").concat(o,"/users/").concat(this.user,"/translate/support/language"),dataType:"json",type:"GET",headers:{Authorization:"Bearer "+n}};return H.debug("Call getSupportedLanguages"),ae.call(this,i)}function cs(e){if("string"!=typeof e.text||""===e.text)throw Error('Invalid parameter: "text"');if(!Array.isArray(e.languages))throw Error('Invalid parameter: "language"');var t=yt.call(this).error;if(t)return Promise.reject(t);var r=this.context,o=r.orgName,n=r.appName,i=r.accessToken,a="".concat(this.apiUrl,"/").concat(o,"/").concat(n,"/users/").concat(this.user,"/translate"),s={text:e.text,to:e.languages},c={url:a,dataType:"json",type:"POST",data:JSON.stringify(s),headers:{Authorization:"Bearer "+i}};return H.debug("Call translateMessage"),ae.call(this,c)}var us=function(){return us=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},us.apply(this,arguments)},ls=function(e,t,r,o){return new(r||(r=Promise))((function(n,i){function a(e){try{c(o.next(e))}catch(e){i(e)}}function s(e){try{c(o.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}c((o=o.apply(e,t||[])).next())}))},ds=function(e,t){var r,o,n,i,a={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,o&&(n=2&i[0]?o.return:i[0]?o.throw||((n=o.return)&&n.call(o),0):o.next)&&!(n=n.call(o,i[1])).done)return n;switch(o=0,n&&(i=[2&i[0],n.value]),i[0]){case 0:case 1:n=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,o=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!((n=(n=a.trys).length>0&&n[n.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!n||i[1]>n[0]&&i[1]<n[3])){a.label=i[1];break}if(6===i[0]&&a.label<n[1]){a.label=n[1],n=i;break}if(n&&a.label<n[2]){a.label=n[2],a.ops.push(i);break}n[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],o=0}finally{r=n=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}},ps=_e.getEnvInfo(),hs=function(){function r(o){var u,l=this;if(this.name="connection",this.appKey="",this.dnsTotal=0,this.max_cache_length=100,this._reportLogs=!1,this._reportInterval=z,this._isLogging=!1,this._initWithAppId=!1,this.uikitVersion="",this.lastHeartbeat=Date.now(),this.isDebug=o.isDebug||!1,this.isReport=!1,this.uploadPartEnable=!0,this.isHttpDNS=void 0===o.isHttpDNS||o.isHttpDNS,this.heartBeatWait=o.heartBeatWait||3e4,this.autoReconnectNumMax=o.autoReconnectNumMax||5,this.refreshDNSIntervals=this.autoReconnectNumMax<5?this.autoReconnectNumMax:5,this.delivery=o.delivery||!1,this.disconnectedReason=void 0,this.loginInfoCustomExt=void 0,this.https=_e.isUseHttps(o.https),this.dnsArr=["https://rs.easemob.com","https://rs.chat.agora.io","http://************","http://*************","http://*************"].filter((function(e){return e.startsWith(l.https?"https":"")})),this.dnsIndex=0,this.restHosts=[],this.restTotal=0,this.restIndex=0,this.hostIndex=0,this.socketHost=[],this.hostTotal=0,this.times=1,this.autoReconnectNumTotal=0,this.domain="easemob.com",this.dnsTotal=this.dnsArr.length,"appKey"in o&&(this.appKey=o.appKey),"appId"in o&&(this.appId=o.appId,this._initWithAppId=!0),this.appName="",this.orgName="",this.token="",this.grantType="",this.apiUrl=o.apiUrl||"",this.url=o.url||"",this.version="4.15.1",this.deviceId=o.deviceId||"webim",this.isFixedDeviceId=null===(u=o.isFixedDeviceId)||void 0===u||u,this.osType=16,this.useOwnUploadFun=o.useOwnUploadFun||!1,this.compressType=[0],this.encryptType=[0],this.clientResource="",this.expiresIn=0,this.expirationTime=0,this.useReplacedMessageContents=o.useReplacedMessageContents||!1,this.logOut=!0,this.context={jid:{appKey:"",clientResource:"",domain:"easemob.com",name:""},userId:"",appKey:"",appId:"",status:0,restTokenData:"",appName:"",orgName:"",root:{},accessToken:""},this._msgHash={},this._msgPromiseHash={},this._queues=[],this._load_msg_cache=[],this.unMSyncSendMsgMap=new Map,this.mr_cache={},this.reconnecting=!1,this._offlineMessagePullState=ve.SYNC_INIT,this._offlineMessagePullQueue=[],this.uniPushConfig={},this.uniPush=null,this.isRegisterPush=!1,this.pushCertificateName="",void 0!==o.customOSPlatform){if("number"!=typeof o.customOSPlatform||o.customOSPlatform<1||o.customOSPlatform>100)throw Error("Invalid customOSPlatform: ".concat(o.customOSPlatform));this.customOSPlatform=o.customOSPlatform,this.deviceId=o.customDeviceName||this.deviceId}if(this._initWithAppId){if(this.appKey="","string"!=typeof this.appId||!this.appId)throw Error("Invalid appId: ".concat(this.appId))}else{if("string"!=typeof this.appKey||2!==this.appKey.split("#").length)throw Error("Invalid appKey: ".concat(this.appKey));this.devInfos=this.appKey.split("#"),this.orgName=this.devInfos[0],this.appName=this.devInfos[1]}this.listen=vr.bind(this),this.mSync=this.usePlugin(fr),this.eventHandler=this.usePlugin(gr),Object.assign(r.prototype,e),Object.assign(r.prototype,t),Object.assign(r.prototype,n),Object.assign(r.prototype,i),Object.assign(r.prototype,a),Object.assign(r.prototype,s),Object.assign(r.prototype,c),this.dataReport=new Lr({appkey:this.appKey||"default#appkey",org:this.orgName,sdkVersion:this.version,deviceId:this.deviceId,isReport:this.isReport,uikitVersion:o.uikitVersion||""});var d=0,p=this;if(Object.defineProperty(_e,"ajaxUnconventionalErrorTimes",{set:function(e){0!==e&&(H.debug("rest api request fail times: ".concat(e)),(d=e)%5==0&&p.isHttpDNS&&(H.debug("refresh dns config when rest request fail."),Cs.call(p,(function(){})).catch((function(e){H.error("refresh dns config error",e)}))))},get:function(){return d}}),this.isFixedDeviceId){var h=_e.getLocalDeviceInfo();h&&(this.clientResource=JSON.parse(h).deviceId)}H.debug("init SDK: Conn ".concat(this.version," ").concat(ps.platform))}return r.prototype.usePlugin=function(e,t){if("push"!==t)return t?void(this[t]=new e(this)):new e(this);e.config&&e.emPush?(this.uniPushConfig=e.config,this.uniPush=e.emPush):H.error("use push plugin failed","emPush",this.uniPush,"config",this.uniPushConfig)},r.prototype.listen=function(e){},r.prototype.addEventHandler=function(e,t){},r.prototype.removeEventHandler=function(e){},r.prototype.registerUser=function(e){return ls(this,void 0,void 0,(function(){var t,r;return ds(this,(function(o){switch(o.label){case 0:if(!e.username||!e.password)throw Error("Invalid parameter");o.label=1;case 1:return o.trys.push([1,5,,6]),t=this.dataReport.geOperateFun({uid:e.username,operationName:E.REGISTER}),this.isHttpDNS?(this.dnsIndex=0,[4,Cs.call(this,t)]):[3,3];case 2:o.sent(),o.label=3;case 3:return[4,ms.call(this,e,{rpt:t})];case 4:return[2,o.sent()];case 5:throw r=o.sent(),e.error&&e.error(r),r;case 6:return[2]}}))}))},r.prototype.open=function(e){var t;return ls(this,void 0,void 0,(function(){var r,o,n,i,a,s;return ds(this,(function(c){switch(c.label){case 0:if(H.debug("open",e.user,"isLogout:",this.logOut),this._isLogging)throw o=m.create({type:d.WEBIM_CONNCTION_OPEN_ERROR,message:"Currently logging in, please wait."});if(!this.logOut)throw o=m.create({type:d.WEBIM_USER_ALREADY_LOGIN,message:"The user has logged in."});this.retryConnectTimes=0,this._isLogging=!0,c.label=1;case 1:if(c.trys.push([1,6,7,8]),"web"===ps.platform&&(r=_e.detectBrowser(),H.debug("browser",r)),o=Is.call(this,e))throw null===(t=this.onError)||void 0===t||t.call(this,o),o;return e.accessToken&&(this.token=e.accessToken),n=us({},e),i=this.dataReport.geOperateFun({uid:e.user,operationName:E.LOGIN}),this.dnsIndex=0,this.isHttpDNS?[4,Cs.call(this,i)]:[3,3];case 2:c.sent(),c.label=3;case 3:return H.initReport({report:this._reportLogs,reportInterval:this._reportInterval,connection:this}),[4,Es.call(this,n,{rpt:i})];case 4:return a=c.sent(),[4,_s.call(this,i)];case 5:return c.sent(),H.debug("grantType",this.grantType),_e.listenNetwork(vs.bind(this),ys.bind(this)),_e.listenBrowserVisibility((function(){H.debug("visibility: true")}),(function(){H.debug("visibility: false")})),[2,a];case 6:throw s=c.sent(),this._isLogging=!1,e.error&&e.error(s),H.error("login failed",s),s;case 7:return H.reportLog(),[7];case 8:return[2]}}))}))},r.prototype.isOpened=function(){return this.sock&&1===this.sock.readyState||!1},r.prototype.close=function(){var e,t,r;H.debug("call close"),H._stopReportLogs(),this.logOut=!0,this.disconnectedReason=void 0,this.reconnecting=!1,this.context.status=d.STATUS_CLOSING,this.sock&&this.sock.close(),null===(e=this.unMSyncSendMsgMap)||void 0===e||e.clear(),this.stopHeartBeat(),this.rejectMessage(),this.context.status=d.STATUS_CLOSED,this._load_msg_cache=[],this._queues=[],this._msgHash={},this.mr_cache={},this.token="",this.context.accessToken="",this.isRegisterPush=!1,this.clearTokenTimeout(),null===(r=null===(t=null==this?void 0:this._localCache)||void 0===t?void 0:t.getInstance())||void 0===r||r.close(),this.connectionTimer&&clearTimeout(this.connectionTimer),this.provisionTimer&&clearTimeout(this.provisionTimer),this.probingTimer&&clearTimeout(this.probingTimer)},r.prototype.downloadAndParseCombineMessage=function(e){var t=this,r=e.url,o=e.secret;return new Promise((function(e,n){var i,a,s=_e.getEnvInfo(),c="web"!==s.platform&&"node"!==s.platform&&"quick_app"!==s.platform&&(null===(a=null===(i=s.global)||void 0===i?void 0:i.canIUse)||void 0===a?void 0:a.call(i,"getFileSystemManager")),u=function(r){var o=function(e){n({type:d.PARSE_FILE_ERROR,message:"Read file failed",data:e})},i=function(r){return ls(t,void 0,void 0,(function(){var t,o,i,a,s,u,l,p,h,f,m,g,E;return ds(this,(function(v){switch(v.label){case 0:for(t=c?new Uint8Array(r.data):new Uint8Array(r.target.result),o=0,i=0,a=2,s=t.subarray(o,o+a),u=_e.Uint8ArrayToString(s),H.debug("file header:",u),i+=a,l=0,p=2;p<t.length-1;p++)p%2==1&&(l^=t[p]);if(h=t.subarray(t.length-1,t.length),H.debug("checkResult:",l,h[0]===l),h[0]!==l)return[2,n({type:d.PARSE_FILE_ERROR,message:"File verification failed"})];if("cm"!==u)return[3,7];v.label=1;case 1:v.trys.push([1,5,,6]),f=[],m=function(){var e,r,n,s;return ds(this,(function(c){switch(c.label){case 0:return o+=a,i+=a=4,e=t.subarray(o,o+a),o+=a,a=e.reduce((function(t,r,o){return t+(r<<8*(e.length-o-1))}),0),i+=a,r=t.subarray(o,o+a),n=(n=g.root.lookup("easemob.pb.Meta")).decode(r),[4,et.call(g,n,0,!0,!0)];case 1:return s=c.sent(),f.push(s),[2]}}))},g=this,v.label=2;case 2:return i<t.length-1?[5,m()]:[3,4];case 3:return v.sent(),[3,2];case 4:return[2,e(f)];case 5:return E=v.sent(),n({type:d.PARSE_FILE_ERROR,message:"Parse file failed",data:E}),[3,6];case 6:return[3,8];case 7:return[2,n({type:d.PARSE_FILE_ERROR,message:"File verification failed"})];case 8:return[2]}}))}))};if(c){var a=s.global.getFileSystemManager(),u=r.tempFilePath;a.readFile({filePath:u,success:i,fail:o})}else if(r instanceof Blob){var l=new FileReader;l.readAsArrayBuffer(r),l.onerror=o,l.onload=i}},l=function(e){n({type:d.WEBIM_DOWNLOADFILE_ERROR,message:"Download failed, please try again",data:e})};c?s.global.downloadFile({url:r,success:u,fail:l}):_e.download.call(t,{url:r,headers:{Accept:"application/json"},onFileDownloadComplete:u,onFileDownloadError:l,secret:o,accessToken:t.context.accessToken})}))},r.prototype.stopHeartBeat=function(){clearInterval(this.heartBeatID)},r.prototype.clear=function(){this.restTotal=0,this.restIndex=0,this.hostIndex=0,this.hostTotal=0},r.prototype.heartBeat=function(){},r.prototype.renewToken=function(e){var t=this,r=this.isOpened();return H.debug("isOpened",r),r?this.getTokenExpireTimestamp(e).then((function(r){var o=r.expire_timestamp,n=Date.now();return t.expirationTime=o,t.expiresIn=o-n,t.token=e,t.context.accessToken=e,t.clearTokenTimeout(),t.tokenExpireTimeCountDown(t.expiresIn),{status:!0,token:e,expire:o}})):Promise.reject({status:!1})},r.prototype.clearTokenTimeout=function(){H.info("clearTokenTimeout"),this.tokenWillExpireTimer&&clearTimeout(this.tokenWillExpireTimer),this.tokenExpiredTimer&&clearTimeout(this.tokenExpiredTimer),this.tokenWillExpireTimer=null,this.tokenExpiredTimer=null},r.prototype.tokenExpireTimeCountDown=function(e){var t=this;H.info("tokenExpireTimeCountDown",e),e>Math.pow(2,31)-1&&(e=Math.pow(2,31)-1),this.tokenWillExpireTimer=setTimeout((function(){var r;t.onTokenWillExpire&&t.onTokenWillExpire(),null===(r=t.eventHandler)||void 0===r||r.dispatch("onTokenWillExpire"),H.info("onTokenWillExpire",Math.floor(e-e/5))}),e-e/5),this.tokenExpiredTimer=setTimeout((function(){var e;H.info("onTokenExpired",0),t.onTokenExpired&&t.onTokenExpired(),null===(e=t.eventHandler)||void 0===e||e.dispatch("onTokenExpired"),t.close()}),e)},r.prototype.compareTokenExpireTime=function(e,t){var r,o=Number(t)-Number(e);H.debug("compareTokenExpireTime",o),o<=this.expiresIn/5&&o>0?(this.onTokenWillExpire&&this.onTokenWillExpire(),null===(r=this.eventHandler)||void 0===r||r.dispatch("onTokenWillExpire"),H.info("onTokenWillExpire",o)):o<=0&&(this.closeByTokenExpired(),H.info("closeByTokenExpired",o))},r.prototype.closeByTokenExpired=function(){var e;H.info("closed By TokenExpired"),this.onTokenExpired&&this.onTokenExpired(),null===(e=this.eventHandler)||void 0===e||e.dispatch("onTokenExpired"),this.close()},r.prototype.rejectMessage=function(){var e=this,t=Object.keys(this._msgHash);if(t.length>0){var r=m.create({type:d.MESSAGE_WEBSOCKET_DISCONNECTED,message:"websocket disconnected"});t.forEach((function(t){var o,n,i;(null===(o=e.unMSyncSendMsgMap)||void 0===o?void 0:o.has(t))||(e._msgHash[t].reject instanceof Function&&e._msgHash[t].reject(r),null===(i=null===(n=e._localCache)||void 0===n?void 0:n.getInstance())||void 0===i||i.updateLocalMessage(t,{serverMsgId:t,status:Ne.FAIL}),e._msgHash[t].fail instanceof Function&&e._msgHash[t].fail(r),e._msgHash[t].messageTimer&&clearTimeout(e._msgHash[t].messageTimer),delete e._msgHash[t])}))}},r.prototype.resetConnState=function(){this.mSync.stopHeartBeat(),this.times=1,this.autoReconnectNumTotal=0,this.hostIndex=0},r.prototype.reconnect=function(e){var t,r=this;if(this.logout)return H.warn("The user has already logged out when reconnecting");if(H.debug("socket reconnect readyState",this.sock.readyState),(0!==this.sock.readyState||e)&&(1!==this.sock.readyState||e)){H.info("reconnect: time",this.times),H.info("reconnect sock.readyState: ",this.sock.readyState),!1===this.reconnecting&&(this.reconnecting=!0),null===(t=this.eventHandler)||void 0===t||t.dispatch("onReconnecting"),this.rejectMessage(),this.isHttpDNS&&(this.hostIndex<this.socketHost.length-1?this.hostIndex++:this.hostIndex=this.socketHost.length-1);var o=1e3*_e.getRetryDelay(this.times);H.info("reconnect delay: ",o),setTimeout((function(){(1!==r.sock.readyState||e)&&(H.info("login sock.readyState: ",r.sock.readyState),r.sock.close(),Ts.call(r).catch((function(e){H.error("reconnect websocket failed",e)})),r.times++)}),o),this.autoReconnectNumTotal++}},r.prototype.send=function(e){return Promise.resolve(null)},r.prototype.setLoginInfoCustomExt=function(e){if(H.debug("setLoginInfoCustomExt","params:",e),e){if("string"!=typeof e)throw new Error("ext must be a string");if(e.length>1024)throw new Error("ext length must be less than 1024")}this.loginInfoCustomExt=e},r.prototype.onShow=function(){var e=this;H.debug("execute onshow callback",this.lastHeartbeat),!this.logOut&&!this.reconnecting&&Date.now()-this.lastHeartbeat>2e3&&(H.debug("send ping"),this.mSync.sendUnreadDeal(),this.probingTimer&&clearTimeout(this.probingTimer),this.probingTimer=setTimeout((function(){H.error("Websocket connection timeout"),e.logOut||e.reconnecting||e.reconnect(!0)}),8e3))},r._getSock=function(){},r}();function fs(e,t){var r=t.rpt,o=t.isRetry,n=this.apiUrl+"/"+this.orgName+"/"+this.appName+"/users",i={requestName:v.RESISTER,requestUrl:n},a={headers:{"Content-Type":"application/json"},url:n,dataType:"json",data:JSON.stringify({username:e.username,password:e.password,nickname:e.nickname||""})};return _e.ajax(a,E.SDK_INTERNAL).then((function(e){var t=us(us({},e),{type:d.REQUEST_SUCCESS}),n=e.extraInfo,a=n.httpCode,s=n.elapse;return r({isEndApi:!0,isRetry:o,data:us(us({},{requestElapse:s,isSuccess:1,code:a}),i)}),t})).catch((function(e){var t=e.extraInfo,n=t.elapse,a=t.httpCode,s=t.errDesc;throw r({isRetry:o,data:us(us({},{requestElapse:n,isSuccess:0,code:a,codeDesc:s}),i)}),e}))}function ms(e,t){return ls(this,void 0,void 0,(function(){var r,o;return ds(this,(function(n){switch(n.label){case 0:r=t.rpt,n.label=1;case 1:return n.trys.push([1,3,,7]),[4,fs.call(this,e,t)];case 2:return[2,n.sent()];case 3:return!((o=n.sent()).message.includes("Open registration doesn't allow")||o.message.includes("username be unique")||o.message.includes("is not legal"))&&this.isHttpDNS&&this.restIndex+1<this.restTotal?(this.restIndex++,Ss.call(this),[4,ms.call(this,e,{rpt:r})]):[3,5];case 4:return[2,n.sent()];case 5:throw H.error("retry signup failed",o),r({data:{isLastApi:1,isSuccess:0}}),o;case 6:return[3,7];case 7:return[2]}}))}))}function gs(e,t){var r;return ls(this,void 0,void 0,(function(){var o,n,i,a,s,c,u,l,d,p=this;return ds(this,(function(h){switch(h.label){case 0:if(o=Is.call(this,e))throw o;return H.debug("socket readyState",null===(r=this.sock)||void 0===r?void 0:r.readyState),n=t.rpt,i=t.isRetry,Os.call(this,e),this.user=e.user,a=this.context.appName,s=this.context.orgName,c=this.apiUrl+"/"+s+"/"+a+"/token",e.accessToken?(H.debug("login with accessToken"),this.grantType="accessToken",this.token=e.accessToken,this.context.accessToken=e.accessToken,this.context.restTokenData=e.accessToken,[2,{accessToken:e.accessToken}]):[3,1];case 1:return e.agoraToken?(H.debug("login with agoraToken"),this.grantType="agoraToken",this.token=e.agoraToken,this.context.accessToken=e.agoraToken,this.context.restTokenData=e.agoraToken,[2,{accessToken:e.agoraToken}]):[3,2];case 2:return H.debug("login with password"),this.grantType="password",u={grant_type:"password",username:e.user,password:e.pwd,timestamp:+new Date},l=JSON.stringify(u),d={headers:{"Content-Type":"application/json"},url:c,dataType:"json",data:l,version:this.version},H.debug("start get token"),[4,_e.ajax(d,E.SDK_INTERNAL).then((function(e){H.debug("get token success",e),p.token=e.access_token,p.context.restTokenData=e.access_token,p.context.accessToken=e.access_token,p.expiresIn=e.expires_in;var t=e.extraInfo,r=t.httpCode,o=t.elapse,a={requestName:v.LOGIN_BY_PWD,requestElapse:o,requestUrl:c,isSuccess:1,code:r};return n({isRetry:i,data:a}),{accessToken:e.access_token,duration:e.expires_in}})).catch((function(e){var t=e.extraInfo,r=t.elapse,o=t.httpCode,a=t.errDesc,s={requestName:v.LOGIN_BY_PWD,requestElapse:r,requestUrl:c,isSuccess:0,code:o,codeDesc:a};throw n({isRetry:i,data:s}),e}))];case 3:return[2,h.sent()]}}))}))}function Es(e,t){var r;return ls(this,void 0,void 0,(function(){var o,n,i;return ds(this,(function(a){switch(a.label){case 0:o=t.rpt,a.label=1;case 1:return a.trys.push([1,3,,7]),[4,gs.call(this,e,t)];case 2:return[2,a.sent()];case 3:return"invalid password"!==(n=a.sent()).message&&"user not found"!==n.message&&this.isHttpDNS&&this.restIndex+1<this.restTotal?(this.restIndex++,Ss.call(this),[4,Es.call(this,e,{rpt:o,isRetry:!0})]):[3,5];case 4:return[2,a.sent()];case 5:throw H.error("retry login failed",n),o({data:{isLastApi:1,isSuccess:0}}),this.clear(),i=void 0,n.error&&n.error_description?(i=m.create({type:d.WEBIM_CONNCTION_OPEN_USERGRID_ERROR,message:n.error_description,data:n}),this.onError&&this.onError(i),i):(i=m.create({type:d.WEBIM_CONNCTION_OPEN_ERROR,message:null!==(r=n.message)&&void 0!==r?r:"login failed",data:n}),this.onError&&this.onError(i),i);case 6:return[3,7];case 7:return[2]}}))}))}function vs(){var e;H.debug("online"),this.onOnline&&this.onOnline(),null===(e=this.eventHandler)||void 0===e||e.dispatch("onOnline"),this.sock&&1!==this.sock.readyState&&(H.debug("sock.readyState:",this.sock.readyState),this.logOut||this.reconnecting||this.reconnect())}function ys(){var e,t;H.debug("offline"),null===(e=this.sock)||void 0===e||e.close(),this.onOffline&&this.onOffline(),null===(t=this.eventHandler)||void 0===t||t.dispatch("onOffline")}function _s(e){var t,r,o;return ls(this,void 0,void 0,(function(){var n,i,a,s;return ds(this,(function(c){switch(c.label){case 0:n=(new Date).getTime(),c.label=1;case 1:return c.trys.push([1,3,,7]),[4,Ts.call(this,e)];case 2:return i=c.sent(),e&&e({data:{isLastApi:1,isSuccess:1,accessChannel:null===(t=this.socketHost[this.hostIndex])||void 0===t?void 0:t.channel}}),[2,i];case 3:if(a=c.sent(),H.error("connect to msync failed times:",this.retryConnectTimes,a),J.includes(a.type))throw e&&e({data:{isLastApi:1,isSuccess:0,accessChannel:null===(r=this.socketHost[this.hostIndex])||void 0===r?void 0:r.channel,codeDesc:this.disconnectedReason&&JSON.stringify(this.disconnectedReason)||a.message}}),a;return this.retryConnectTimes++,this.retryConnectTimes<3?(this.isHttpDNS&&(this.hostIndex<this.socketHost.length-1?this.hostIndex++:this.hostIndex=this.socketHost.length-1,Ns.call(this)),a.type===d.REQUEST_TIMEOUT&&"provision timeout"===a.message||(s=(new Date).getTime()-n,null==e||e({isRetry:1!==this.retryConnectTimes,data:{requestUrl:this.url,requestName:v.CONNECT_WEBSOCKET,isSuccess:0,code:_.closed,requestElapse:s,codeDesc:this.disconnectedReason&&JSON.stringify(this.disconnectedReason)||"websocket close"}})),[4,_s.call(this,e)]):[3,5];case 4:return c.sent(),[3,6];case 5:throw H.error("connect failed three times",a),e&&e({data:{isLastApi:1,isSuccess:0,accessChannel:null===(o=this.socketHost[this.hostIndex])||void 0===o?void 0:o.channel,codeDesc:this.disconnectedReason&&JSON.stringify(this.disconnectedReason)||a.message}}),a;case 6:return[3,7];case 7:return[2]}}))}))}function Ts(e){var t=this,r=(new Date).getTime(),o="pending";return new Promise((function(n,i){var a;t._getSock?void 0===(a=t._getSock()).readyState&&(t.needSetReadyState=!0):(a=new WebSocket(t.url)).binaryType="arraybuffer",t.sock=a,t.connectionTimer&&clearTimeout(t.connectionTimer),t.connectionTimer=setTimeout((function(){t.disconnectedReason={type:d.REQUEST_TIMEOUT,message:"connection timeout"},a.close(),null==i||i(t.disconnectedReason)}),1e4),H.debug("start connect ws"),t.connectionResolve=n,t.connectionReject=i;var s=function(){if(t.connectionTimer&&clearTimeout(t.connectionTimer),H.debug("websocket onOpen"),e){var o=(new Date).getTime()-r;e({isRetry:0!==t.retryConnectTimes,data:{requestUrl:t.url,requestName:v.CONNECT_WEBSOCKET,isSuccess:1,code:_.success,requestElapse:o}})}var n;n=t._getSock?_e.flow([t.mSync.generateProvision,t.mSync.base64transform])():t.mSync.generateProvision();try{t.provisionTimer&&clearTimeout(t.provisionTimer),t.provisionTimer=setTimeout((function(){H.debug("provision timeout"),t.disconnectedReason=m.create({type:d.REQUEST_TIMEOUT,message:"provision timeout"}),a.close(),null==i||i(t.disconnectedReason)}),1e4),a.send(n)}catch(e){var s=m.create({type:d.SDK_RUNTIME_ERROR,message:"send message error",data:e});t.onError&&t.onError(s)}},c=function(e){t.connectionTimer&&clearTimeout(t.connectionTimer),setTimeout((function(){var r,n,a,s,c,u,l;if(H.debug("websocket onClose, isLogging:",t._isLogging,e),t.needSetReadyState&&(t.sock=us(us({},t.sock),{close:t.sock.close,send:t.sock.send,readyState:3})),t._isLogging)return null==i||i({type:d.WEBIM_CONNCTION_DISCONNECTED,message:"websocket has been disconnected"}),void(o="rejected");if("rejected"!==o)if(o="rejected",t.logOut)t.clear(),t.resetConnState(),null===(n=null===(r=null==t?void 0:t._localCache)||void 0===r?void 0:r.getInstance())||void 0===n||n.close(),t.onClosed&&t.onClosed(),null===(a=t.eventHandler)||void 0===a||a.dispatch("onDisconnected",t.disconnectedReason),null==i||i(t.disconnectedReason||{type:d.WEBIM_CONNCTION_DISCONNECTED,message:"websocket has been disconnected"});else if(t.autoReconnectNumTotal<t.autoReconnectNumMax){t.reconnect();var p={type:d.WEBIM_CONNCTION_DISCONNECTED,message:"websocket has been disconnected"};t.onError&&t.onError(p),t.autoReconnectNumTotal%t.refreshDNSIntervals===0&&t.isHttpDNS&&(H.debug("refresh dns config when websocket close"),Cs.call(t,(function(){})).catch((function(e){H.error("refresh dns config failed",e)})))}else null===(s=t.unMSyncSendMsgMap)||void 0===s||s.clear(),t.rejectMessage(),p={type:d.WEBIM_CONNCTION_DISCONNECTED,message:"websocket has been disconnected"},t.disconnectedReason=m.create({type:d.WEBIM_CONNCTION_DISCONNECTED,message:"reconnection failed"}),t.onError&&t.onError(p),null===(u=null===(c=null==t?void 0:t._localCache)||void 0===c?void 0:c.getInstance())||void 0===u||u.close(),t.onClosed&&t.onClosed(),null===(l=t.eventHandler)||void 0===l||l.dispatch("onDisconnected",t.disconnectedReason),t.resetConnState(),t.reconnecting=!1,null==i||i(p),H.debug("reconnect fail");else H.debug("reject is null")}),0)},u=function(e){var r=t.mSync,o=r.decodeMSync,n=r.distributeMSync;_e.flow([o,n])(e)};"web"===ps.platform?(a.onopen=s,a.onclose=c,a.onmessage=u):(a.onOpen(s),a.onMessage(u),a.onClose(c),"undefined"!=typeof window&&window.WebSocket||a.onError((function(e){var r,n,a,s;if(t.connectionTimer&&clearTimeout(t.connectionTimer),H.debug("websocket onerror, isLogging:",t._isLogging,e),3!==t.sock.readyState&&(t.sock=us(us({},t.sock),{close:t.sock.close,send:t.sock.send,readyState:3})),t.onError&&t.onError({type:d.WEBIM_CONNECTION_ERROR,message:"on socket error",data:e}),t._isLogging)return null==i||i({type:d.WEBIM_CONNCTION_DISCONNECTED,message:"websocket has been disconnected"}),void(o="rejected");if("rejected"!==o){if(o="rejected",!t.logOut)if(t.autoReconnectNumTotal<t.autoReconnectNumMax)H.debug("sock.onError reconnect",t.autoReconnectNumTotal,t.autoReconnectNumMax),t.reconnect(),t.autoReconnectNumTotal%t.refreshDNSIntervals===0&&t.isHttpDNS&&(H.debug("refresh dns config when websocket error"),Cs.call(t,(function(){})).catch((function(e){H.error("refresh dns config failed",e)})));else{var c={type:d.WEBIM_CONNCTION_DISCONNECTED,message:"websocket has been disconnected"};t.disconnectedReason=m.create({type:d.WEBIM_CONNCTION_DISCONNECTED,message:"reconnection failed"}),null===(r=t.unMSyncSendMsgMap)||void 0===r||r.clear(),t.rejectMessage(),t.onError&&t.onError(c),null===(a=null===(n=null==t?void 0:t._localCache)||void 0===n?void 0:n.getInstance())||void 0===a||a.close(),t.onClosed&&t.onClosed(),null===(s=t.eventHandler)||void 0===s||s.dispatch("onDisconnected",t.disconnectedReason),t.resetConnState(),t.reconnecting=!1,null==i||i(c),H.debug("reconnect fail")}}else H.debug("reject is null")})))}))}function Is(e){if("object"!=typeof e||null===e)return m.create({type:d.WEBIM_CONNCTION_OPEN_ERROR,message:"the param is illegal"});if(!e.user||"string"!=typeof e.user){var t=m.create({type:d.WEBIM_CONNCTION_USER_NOT_ASSIGN_ERROR,message:"the user is empty or type is not string"});return H.debug("open params error",t),t}return!("agoraToken"in e)||e.agoraToken&&"string"==typeof e.agoraToken?!("accessToken"in e)||e.accessToken&&"string"==typeof e.accessToken?"accessToken"in e||"agoraToken"in e||e.pwd?void 0:(t=m.create({type:d.WEBIM_CONNCTION_OPEN_ERROR,message:"the accessToken or pwd is illegal"}),H.debug("open params error",t),t):(t=m.create({type:d.WEBIM_CONNCTION_OPEN_ERROR,message:"the accessToken is illegal"}),H.debug("open params error",t),t):(t=m.create({type:d.WEBIM_CONNCTION_OPEN_ERROR,message:"the agoraToken is illegal"}),H.debug("open params error",t),console.warn("agoraToken is deprecated, please use accessToken instead"),t)}function Os(e){this.context.jid={appKey:this.appKey,name:e.user,domain:this.domain,clientResource:this.clientResource},this.context.root=this.root,this.context.userId=e.user,this.context.appKey=this.appKey,this.context.appName=this.appName,this.context.orgName=this.orgName}function Rs(e){var t=this,r=e.rpt,o=e.isRetry,n=this.dnsIndex<this.dnsTotal?this.dnsIndex:0,i="".concat(this.dnsArr[n],"/easemob/server.json");H.debug("call getHttpDNS: "+this.dnsIndex);var a={url:i,dataType:"json",type:"GET",data:{}};return this._initWithAppId?a.data={app_id:this.appId,sdk_version:"4.10.0",file_version:"1"}:a.data={app_key:ps.platform===le.WEB?encodeURIComponent(this.appKey):this.appKey},_e.ajax(a,E.SDK_INTERNAL).then((function(e){if(!e)throw m.create({type:d.SERVER_GET_DNSLIST_FAILED,message:"get DNS failed"});H.debug("getHttpDNS success");var n=e.rest.hosts,a=t.https?"https":"http";if(H.info("httpType: "+a),t._initWithAppId){if(!e.app_key)throw(c=m.create({type:d.SERVER_GET_DNSLIST_FAILED,message:"get DNS failed"})).extraInfo=e.extraInfo,c;t.appKey=e.app_key,t.context.appKey=t.appKey,t.devInfos=t.appKey.split("#"),t.orgName=t.devInfos[0],t.appName=t.devInfos[1],t.dataReport.updateAppKey(t.appKey)}if(!n)throw(c=m.create({type:d.SERVER_GET_DNSLIST_FAILED,message:"DNS hosts resolution failed",data:e.rest})).extraInfo=e.extraInfo,c;var s=n.filter((function(e){if(e.protocol===a)return e}));t.restHosts=s,t.restTotal=s.length;var c,u=e["msync-wx"].hosts;if(!u)throw(c=m.create({type:d.SERVER_GET_DNSLIST_FAILED,message:"DNS msync-wx resolution failed",data:e["msync-wx"]})).extraInfo=e.extraInfo,c;var l=u.filter((function(e){if(e.protocol===a)return e}));if(t.socketHost=l,t.hostTotal=l.length,t.isHttpDNS&&Ss.call(t),t.isHttpDNS&&Ns.call(t),t._reportLogs="true"===e.enableReportLogs,t._reportInterval=Number(e.reportInterval||z),"true"===(null==e?void 0:e.enableDataReport)?(t.dataReport.setIsCollectDt(!0),t.dataReport.setIsReportDt(!0)):(t.dataReport.setIsReportDt(!1),t.dataReport.setIsCollectDt(!1)),"false"===(null==e?void 0:e.uploadinparts_enable)&&(t.uploadPartEnable=!1),e.extraInfo){var p=e.extraInfo,h=p.elapse,f=p.httpCode,g={requestUrl:i,requestName:v.GET_DNS,requestElapse:h,isSuccess:1,code:f};r&&r({isRetry:o,data:g})}})).catch((function(e){var t=e.extraInfo||{},n=t.elapse,a=t.httpCode,s=t.errDesc,c={requestUrl:i,requestName:v.GET_DNS,isSuccess:0,code:a,codeDesc:s,requestElapse:n};throw r&&r({isRetry:o,data:c}),m.create({type:d.SERVER_GET_DNSLIST_FAILED,message:e.message,data:e})}))}function Cs(e,t){return ls(this,void 0,void 0,(function(){var r;return ds(this,(function(o){switch(o.label){case 0:return o.trys.push([0,2,,6]),[4,Rs.call(this,{rpt:e,isRetry:t})];case 1:case 3:return[2,o.sent()];case 2:return r=o.sent(),H.error("get DNS failed",r,"times:",this.dnsIndex),this.dnsIndex++,this.dnsIndex<this.dnsTotal?[4,Cs.call(this,e,!0)]:[3,4];case 4:throw H.error("retryRequestDNS failed"),m.create({type:d.SERVER_GET_DNSLIST_FAILED,message:"get DNS failed",data:r});case 5:return[3,6];case 6:return[2]}}))}))}function Ns(){var e=this.socketHost[this.hostIndex],t=e.domain,r=e.ip,o=e.port,n="";n=t||r,o&&"80"!==o&&"443"!==o&&(n+=":"+o),n&&(this.url="//"+n+"/websocket");var i=this.https?"wss:":"ws:";this.url=i+this.url}function Ss(){var e,t;if(this.restIndex>this.restTotal)return H.debug("restIndex > restTotal"),"";var r="",o=this.restHosts[this.restIndex],n=o.domain,i=o.ip,a=o.port,s=this.https?"https:":"http:";return i&&"undefined"!=typeof window&&"http:"===(null===(e=null===window||void 0===window?void 0:window.location)||void 0===e?void 0:e.protocol)?r=s+"//"+i+":"+a:(r=s+"//"+n,a&&"80"!==a&&"443"!==a&&(r+=":".concat(a)),"undefined"==typeof window||window.location||(r="https://"+n),"undefined"!=typeof window&&window.location&&"file:"===(null===(t=window.location)||void 0===t?void 0:t.protocol)&&(r="https://"+n)),"undefined"==typeof window&&(r=s+"//"+n),this.apiUrl=r,r}var As=_e.getEnvInfo(),bs=As.global;p.util.Long=f(),p.configure();var Ms=p.Root.fromJSON({nested:{easemob:{nested:{pb:{nested:{MessageBody:{fields:{type:{type:"Type",id:1},from:{type:"JID",id:2},to:{type:"JID",id:3},contents:{rule:"repeated",type:"Content",id:4},ext:{rule:"repeated",type:"KeyValue",id:5},ackMessageId:{type:"uint64",id:6},msgConfig:{type:"MessageConfig",id:7},ackContent:{type:"string",id:8},meta:{type:"string",id:9},editMessageId:{type:"uint64",id:11},editScope:{type:"EditScope",id:12}},nested:{Content:{fields:{type:{type:"Type",id:1},text:{type:"string",id:2},latitude:{type:"double",id:3},longitude:{type:"double",id:4},address:{type:"string",id:5},displayName:{type:"string",id:6},remotePath:{type:"string",id:7},secretKey:{type:"string",id:8},fileLength:{type:"int32",id:9},action:{type:"string",id:10},params:{rule:"repeated",type:"KeyValue",id:11},duration:{type:"int32",id:12},size:{type:"Size",id:13},thumbnailRemotePath:{type:"string",id:14},thumbnailSecretKey:{type:"string",id:15},thumbnailDisplayName:{type:"string",id:16},thumbnailFileLength:{type:"int32",id:17},thumbnailSize:{type:"Size",id:18},customEvent:{type:"string",id:19},customExts:{rule:"repeated",type:"KeyValue",id:20},buildingName:{type:"string",id:21},subType:{type:"SubType",id:22},title:{type:"string",id:23},summary:{type:"string",id:24},combineLevel:{type:"int32",id:25}},nested:{Type:{values:{TEXT:0,IMAGE:1,VIDEO:2,LOCATION:3,VOICE:4,FILE:5,COMMAND:6,CUSTOM:7,COMBINE:8}},Size:{fields:{width:{type:"double",id:1},height:{type:"double",id:2}}},SubType:{values:{COMBINE:0,GIF:1}}}},Type:{values:{NORMAL:0,CHAT:1,GROUPCHAT:2,CHATROOM:3,READ_ACK:4,DELIVER_ACK:5,RECALL:6,CHANNEL_ACK:7,EDIT:8}},EditScope:{values:{BODY:1,BODY_AND_EXT:2}},MessageConfig:{fields:{allowGroupAck:{type:"bool",id:1}}}}},KeyValue:{oneofs:{value:{oneof:["varintValue","floatValue","doubleValue","stringValue"]}},fields:{key:{type:"string",id:1},type:{type:"ValueType",id:2},varintValue:{type:"int64",id:3},floatValue:{type:"float",id:4},doubleValue:{type:"double",id:5},stringValue:{type:"string",id:6}},nested:{ValueType:{values:{BOOL:1,INT:2,UINT:3,LLINT:4,FLOAT:5,DOUBLE:6,STRING:7,JSON_STRING:8}}}},JID:{fields:{appKey:{type:"string",id:1},name:{type:"string",id:2},domain:{type:"string",id:3},clientResource:{type:"string",id:4}}},ConferenceBody:{fields:{sessionId:{type:"string",id:1},operation:{type:"Operation",id:2},conferenceId:{type:"string",id:3},type:{type:"Type",id:4},content:{type:"string",id:5},network:{type:"string",id:6},version:{type:"string",id:7},identity:{type:"Identity",id:8},duration:{type:"string",id:9},peerName:{type:"string",id:10},endReason:{type:"EndReason",id:11},status:{type:"Status",id:12},isDirect:{type:"bool",id:13},controlType:{type:"StreamControlType",id:14},routeFlag:{type:"int32",id:15},routeKey:{type:"string",id:16}},nested:{Status:{fields:{errorCode:{type:"int32",id:1}}},Operation:{values:{JOIN:0,INITIATE:1,ACCEPT_INITIATE:2,ANSWER:3,TERMINATE:4,REMOVE:5,STREAM_CONTROL:6,MEDIA_REQUEST:7}},Type:{values:{VOICE:0,VIDEO:1}},Identity:{values:{CALLER:0,CALLEE:1}},EndReason:{values:{HANGUP:0,NORESPONSE:1,REJECT:2,BUSY:3,FAIL:4,UNSUPPORTED:5,OFFLINE:6}},StreamControlType:{values:{PAUSE_VOICE:0,RESUME_VOICE:1,PAUSE_VIDEO:2,RESUME_VIDEO:3}}}},MSync:{fields:{version:{type:"Version",id:1,options:{default:"MSYNC_V1"}},guid:{type:"JID",id:2},auth:{type:"string",id:3},compressAlgorimth:{type:"uint32",id:4},crypto:{type:"uint32",id:5},userAgent:{type:"string",id:6},pov:{type:"uint64",id:7},command:{type:"Command",id:8},deviceId:{type:"uint32",id:10},encryptType:{rule:"repeated",type:"EncryptType",id:11,options:{packed:!1}},encryptKey:{type:"string",id:12},payload:{type:"bytes",id:9}},nested:{Version:{values:{MSYNC_V1:0,MSYNC_V2:1}},Command:{values:{SYNC:0,UNREAD:1,NOTICE:2,PROVISION:3}}}},EncryptType:{values:{ENCRYPT_NONE:0,ENCRYPT_AES_128_CBC:1,ENCRYPT_AES_256_CBC:2}},CommSyncUL:{fields:{meta:{type:"Meta",id:1},key:{type:"uint64",id:2},queue:{type:"JID",id:3},isRoam:{type:"bool",id:4},lastFullRoamKey:{type:"uint64",id:5}}},CommSyncDL:{fields:{status:{type:"Status",id:1},metaId:{type:"uint64",id:2},serverId:{type:"uint64",id:3},metas:{rule:"repeated",type:"Meta",id:4},nextKey:{type:"uint64",id:5},queue:{type:"JID",id:6},isLast:{type:"bool",id:7},timestamp:{type:"uint64",id:8},isRoam:{type:"bool",id:9}}},CommNotice:{fields:{queue:{type:"JID",id:1}}},CommUnreadUL:{fields:{}},CommUnreadDL:{fields:{status:{type:"Status",id:1},unread:{rule:"repeated",type:"MetaQueue",id:2},timestamp:{type:"uint64",id:3}}},MetaQueue:{fields:{queue:{type:"JID",id:1},n:{type:"uint32",id:2}}},Meta:{fields:{id:{type:"uint64",id:1},from:{type:"JID",id:2},to:{type:"JID",id:3},timestamp:{type:"uint64",id:4},ns:{type:"NameSpace",id:5},payload:{type:"bytes",id:6},routetype:{type:"RouteType",id:7},ext:{rule:"repeated",type:"KeyValue",id:8},meta:{type:"bytes",id:9},directedUsers:{rule:"repeated",type:"string",id:10}},nested:{NameSpace:{values:{STATISTIC:0,CHAT:1,MUC:2,ROSTER:3,CONFERENCE:4,NOTIFY:5,QUERY:6}},RouteType:{values:{ROUTE_ALL:0,ROUTE_ONLINE:1,ROUTE_DIRECT:2}}}},Status:{fields:{errorCode:{type:"ErrorCode",id:1},reason:{type:"string",id:2},redirectInfo:{rule:"repeated",type:"RedirectInfo",id:3}},nested:{ErrorCode:{values:{OK:0,FAIL:1,UNAUTHORIZED:2,MISSING_PARAMETER:3,WRONG_PARAMETER:4,REDIRECT:5,TOKEN_EXPIRED:6,PERMISSION_DENIED:7,NO_ROUTE:8,UNKNOWN_COMMAND:9,PB_PARSER_ERROR:10,BIND_ANOTHER_DEVICE:11,IM_FORBIDDEN:12,TOO_MANY_DEVICES:13,PLATFORM_LIMIT:14,USER_MUTED:15,ENCRYPT_DISABLE:16,ENCRYPT_ENABLE:17,DECRYPT_FAILURE:18,PERMISSION_DENIED_EXTERNAL:19}}}},RedirectInfo:{fields:{host:{type:"string",id:1},port:{type:"uint32",id:2}}},Provision:{fields:{osType:{type:"OsType",id:1},version:{type:"string",id:2},networkType:{type:"NetworkType",id:3},appSign:{type:"string",id:4},compressType:{rule:"repeated",type:"CompressType",id:5,options:{packed:!1}},encryptType:{rule:"repeated",type:"EncryptType",id:6,options:{packed:!1}},encryptKey:{type:"string",id:7},status:{type:"Status",id:8},deviceUuid:{type:"string",id:9},isManualLogin:{type:"bool",id:10},password:{type:"string",id:11},deviceName:{type:"string",id:12},resource:{type:"string",id:13},auth:{type:"string",id:14},serviceId:{type:"string",id:16},actionVersion:{type:"string",id:17},authToken:{type:"string",id:18},osCustomValue:{type:"uint32",id:19},sessionId:{type:"string",id:20},reason:{type:"string",id:21}},nested:{OsType:{values:{OS_IOS:0,OS_ANDROID:1,OS_LINUX:2,OS_OSX:3,OS_WIN:4,OS_CUSTOM:5,OS_OTHER:16}},NetworkType:{values:{NETWORK_NONE:0,NETWORK_WIFI:1,NETWORK_4G:2,NETWORK_3G:3,NETWORK_2G:4,NETWORK_WIRE:5}},CompressType:{values:{COMPRESS_NONE:0,COMPRESS_ZLIB:1}}}},MUCBody:{fields:{mucId:{type:"JID",id:1},operation:{type:"Operation",id:2},from:{type:"JID",id:3},to:{rule:"repeated",type:"JID",id:4},setting:{type:"Setting",id:5},reason:{type:"string",id:6},isChatroom:{type:"bool",id:7},status:{type:"Status",id:8},isThread:{type:"bool",id:9},mucParentId:{type:"JID",id:10},mucName:{type:"string",id:11},eventInfo:{type:"EventInfo",id:12},mucMemberCount:{type:"int32",id:13},ext:{type:"string",id:14},leaveOtherRooms:{type:"bool",id:15},members:{rule:"repeated",type:"string",id:16}},nested:{Operation:{values:{CREATE:0,DESTROY:1,JOIN:2,LEAVE:3,APPLY:4,APPLY_ACCEPT:5,APPLY_DECLINE:6,INVITE:7,INVITE_ACCEPT:8,INVITE_DECLINE:9,KICK:10,GET_BLACKLIST:11,BAN:12,ALLOW:13,UPDATE:14,BLOCK:15,UNBLOCK:16,PRESENCE:17,ABSENCE:18,DIRECT_JOINED:19,ASSIGN_OWNER:20,ADD_ADMIN:21,REMOVE_ADMIN:22,ADD_MUTE:23,REMOVE_MUTE:24,UPDATE_ANNOUNCEMENT:25,DELETE_ANNOUNCEMENT:26,UPLOAD_FILE:27,DELETE_FILE:28,ADD_USER_WHITE_LIST:29,REMOVE_USER_WHITE_LIST:30,BAN_GROUP:31,REMOVE_BAN_GROUP:32,THREAD_CREATE:33,THREAD_DESTROY:34,THREAD_JOIN:35,THREAD_LEAVE:36,THREAD_KICK:37,THREAD_UPDATE:38,THREAD_PRESENCE:39,THREAD_ABSENCE:40,DISABLE_GROUP:41,ABLE_GROUP:42,SET_METADATA:43,DELETE_METADATA:44,GROUP_MEMBER_METADATA_UPDATE:45}},Setting:{fields:{name:{type:"string",id:1},desc:{type:"string",id:2},type:{type:"Type",id:3},maxUsers:{type:"int32",id:4},owner:{type:"string",id:5}},nested:{Type:{values:{PRIVATE_OWNER_INVITE:0,PRIVATE_MEMBER_INVITE:1,PUBLIC_JOIN_APPROVAL:2,PUBLIC_JOIN_OPEN:3,PUBLIC_ANONYMOUS:4}}}},Status:{fields:{errorCode:{type:"ErrorCode",id:1},description:{type:"string",id:2},chatroomInfo:{type:"string",id:3}},nested:{ErrorCode:{values:{OK:0,PERMISSION_DENIED:1,WRONG_PARAMETER:2,MUC_NOT_EXIST:3,USER_NOT_EXIST:4,UNKNOWN:5}}}},EventInfo:{fields:{eventType:{type:"EventType",id:1,options:{default:"EVENT_NONE"}},ext:{type:"string",id:2}},nested:{EventType:{values:{EVENT_NONE:0,CIRCLE_CHANNEL:1}}}}}},RosterBody:{fields:{operation:{type:"Operation",id:1},status:{type:"Status",id:2},from:{type:"JID",id:3},to:{rule:"repeated",type:"JID",id:4},reason:{type:"string",id:5},rosterVer:{type:"string",id:6},biDirection:{type:"bool",id:7}},nested:{Operation:{values:{GET_ROSTER:0,GET_BLACKLIST:1,ADD:2,REMOVE:3,ACCEPT:4,DECLINE:5,BAN:6,ALLOW:7,REMOTE_ACCEPT:8,REMOTE_DECLINE:9}},Status:{fields:{errorCode:{type:"ErrorCode",id:1},description:{type:"string",id:2}},nested:{ErrorCode:{values:{OK:0,USER_NOT_EXIST:1,USER_ALREADY_FRIEND:2,USER_ALREADY_BLACKLIST:3}}}}}},StatisticsBody:{fields:{operation:{type:"Operation",id:1},os:{type:"OsType",id:2},version:{type:"string",id:3},network:{type:"NetworkType",id:4},imTime:{type:"uint32",id:5},chatTime:{type:"uint32",id:6},location:{type:"string",id:7},reason:{type:"string",id:10}},nested:{Operation:{values:{INFORMATION:0,USER_REMOVED:1,USER_LOGIN_ANOTHER_DEVICE:2,USER_KICKED_BY_CHANGE_PASSWORD:3,USER_KICKED_BY_OTHER_DEVICE:4}},OsType:{values:{OS_IOS:0,OS_ANDROID:1,OS_LINUX:2,OS_OSX:3,OS_WIN:4,OS_CUSTOM:5,OS_OTHER:16}},NetworkType:{values:{NETWORK_NONE:0,NETWORK_WIFI:1,NETWORK_4G:2,NETWORK_3G:3,NETWORK_2G:4,NETWORK_WIRE:5}}}}}}}}}});hs.prototype.root=Ms;var Us=function(e){this.onOpen=bs.onSocketOpen,this.onClose=bs.onSocketClose,this.onMessage=bs.onSocketMessage,this.onError=bs.onSocketError,this.close=bs.closeSocket,this.send=bs.sendSocketMessage,this.connectSocket=bs.connectSocket,this.readyState=void 0,this.offSocketOpen=bs.offSocketOpen,this.offSocketMessage=bs.offSocketMessage,this.offSocketError=bs.offSocketError,this.offSocketClose=bs.offSocketClose,this.connect=function(){this.offSocketOpen(),this.offSocketMessage(),this.offSocketClose(),this.offSocketError(),bs.connectSocket({url:e.url,header:{"content-type":"application/json"},success:function(e){},fail:function(){}})}};hs.prototype._getSock=function(){var e,t=this;return this.sock&&this.sock.close&&this.sock.close(),(e="zfb"===As.platform||"dd"===As.platform?new Us(this):bs.connectSocket({url:this.url,header:{"content-type":"application/json"},success:function(e){H.debug("socket connect success",e)},fail:function(e){H.debug("socket connect fail",e),t.reconnecting=!1,e.errMsg.indexOf("suspend")},complete:function(){}})).connect&&e.connect(),e},hs.prototype.getUniqueId=_e.getUniqueId,hs.prototype.deviceId="miniProgram_"+As.platform,hs.prototype._localCache=void 0;var ws=H.getLogger("defaultLogger");ws.setConfig({useCache:!1,maxCache:3145728}),ws.enableAll(),_e.ajax=_e.wxRequest;var Ls={connection:hs,message:Fe,utils:_e,logger:ws,statusCode:d}}(),o}()}));