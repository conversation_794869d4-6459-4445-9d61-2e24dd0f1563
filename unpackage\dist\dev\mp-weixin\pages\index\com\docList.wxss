/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*
	字体颜色相关
*/
/*
	背景色 分割线色
*/
/*
	主题色
*/
/*
		渐变可调角度
*/
/*
	定义弹性布局
 */
/*
	单行文本溢出隐藏
*/
.doc_list.data-v-2f69d998 {
  background: #fff;
  border-radius: 8rpx;
}
.doc_list .doc_title.data-v-2f69d998 {
  padding: 0 10rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.doc_list .rec_item.data-v-2f69d998 {
  border-bottom: 1px solid #ebebeb;
  border-radius: 0;
}
.doc_list .rec_item.data-v-2f69d998:last-child {
  border-bottom: none;
}
.list.data-v-2f69d998 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  /* 每行4个元素 */
  gap: 20rpx;
  /* 固定间距 */
  justify-items: center;
  /* 居中对齐内容 */
  align-items: center;
  /* 垂直居中内容 */
  box-sizing: border-box;
  margin-top: 20rpx;
}