"use strict";
const common_vendor = require("../common/vendor.js");
const Login = {
  methods: {
    // 跳转登录
    toLogin() {
      common_vendor.index.navigateTo({
        url: "/pages/login/login"
      });
    },
    // 补全手机号
    toTel() {
      common_vendor.index.navigateTo({
        url: "/pages/login/tel"
      });
    },
    // 逻辑判断
    hasInfo() {
      if (!common_vendor.index.getStorageSync("userId")) {
        this.toLogin();
        return false;
      }
      if (!common_vendor.index.getStorageSync("tel")) {
        this.toTel();
        return false;
      }
      return true;
    }
  }
};
exports.Login = Login;
