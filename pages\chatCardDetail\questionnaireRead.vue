<template>
  <view class="questionnaire">
    <view class="header">
      <view class="title">{{ saveInfo.didName }}</view>
      <view class="tips" v-if="!pageParam.lbType"
        >感谢您能抽出几分钟时间来参加量表，为了方便医生了解病情，请认真作答，谢谢！</view
      >
    </view>
    <!-- 题目 -->
    <view class="quesView">
      <view class="quesView-item" v-for="(item, index) in saveInfo.answerList" :key="index">
        <view class="radioView quesAnswerView" v-if="item.didtType == 1">
          <view class="didName"
            >{{ index + 1 }}.{{ item.didtName }}
            <text v-if="item.isMust == 1" style="color: red">*</text>
          </view>
          <radio-group>
            <label
              class="uni-list-cell uni-list-cell-pd"
              v-for="(radioItem, radioIndex) in item.answerInfo"
              :key="radioIndex"
            >
              <view>
                <radio
                  style="transform: scale(0.7)"
                  :value="JSON.stringify(radioItem.optionCode)"
                  :checked="radioItem.checked"
                  disabled=""
                />
                <text>{{ radioItem.optionName }}</text>
              </view>
            </label>
          </radio-group>
        </view>

        <!-- 多选 -->
        <view class="checkView quesAnswerView" v-if="item.didtType == 2">
          <view class="didName"
            >{{ index + 1 }}.{{ item.didtName }}
            <text v-if="item.isMust == 1" style="color: red">*</text></view
          >
          <checkbox-group>
            <label
              class="uni-list-cell uni-list-cell-pd"
              v-for="(checkItem, checkIndex) in item.answerInfo"
              :key="checkIndex"
            >
              <view>
                <checkbox
                  style="transform: scale(0.7)"
                  :value="JSON.stringify(checkItem.optionCode)"
                  :checked="checkItem.checked"
                />
                <text>{{ checkItem.optionName }}</text>
              </view>
            </label>
          </checkbox-group>
        </view>

        <!-- 填空 -->
        <view class="contentView quesAnswerView" v-if="item.didtType == 3">
          <view class="didName"
            >{{ index + 1 }}.{{ item.didtName }}
            <text v-if="item.isMust == 1" style="color: red">*</text></view
          >
          <view class="content">
            <textarea
              placeholder-style="color:#999999;font-size:13px;"
              disabled
              placeholder="请输入答案"
              v-model="item.answerInfo"
            />
          </view>
        </view>

        <view class="selectView quesAnswerView" v-if="item.didtType == 4">
          <view class="uni-title uni-common-pl didName"
            >{{ index + 1 }}.{{ item.didtName }}
            <text v-if="item.isMust == 1" style="color: red">*</text></view
          >
          <view class="uni-input dateInput">{{ item.answerInfo }} </view>
        </view>

        <view class="imgView quesAnswerView" v-if="item.didtType == 5">
          <view class="didName"
            >{{ index + 1 }}.{{ item.didtName }}
            <text v-if="item.isMust == 1" style="color: red">*</text></view
          >
          <view class="dateInput" v-if="!item.answerInfo">
            <image src="/static/images/up_img.png" class="imgIcon"></image>
            <text>选择图片（2M以内）</text>
            <image
              src="../../static/images/question/image-editor.png"
              class="editIcon"
            ></image>
          </view>
          <view v-if="item.answerInfo" @click="previewImg">
            <image
              :src="item.answerInfo"
              class="upImg"
              mode="aspectFill"
            ></image>
          </view>
        </view>

        <view class="contentView quesAnswerView" v-if="item.didtType == 6">
          <view class="didName"
            >{{ index + 1 }}.{{ item.didtName }}
            <text v-if="item.isMust == 1" style="color: red">*</text></view
          >
          <rate
            size="60"
            :value="item.answerInfo"
            disabled
            :star_empty="require('../../static/images/question/assess.png')"
            :star_fill="
              require('../../static/images/question/assess_active.png')
            "
          ></rate>
        </view>

        <view class="timeView quesAnswerView" v-if="item.didtType == 7">
          <view class="didName"
            >{{ index + 1 }}.{{ item.didtName }}
            <text v-if="item.isMust == 1" style="color: red">*</text></view
          >
          <view class="uni-input dateInput">{{ item.answerInfo }} </view>
        </view>

        <view class="dateView quesAnswerView" v-if="item.didtType == 8">
          <view class="didName"
            >{{ index + 1 }}.{{ item.didtName }}
            <text v-if="item.isMust == 1" style="color: red">*</text></view
          >
          <view class="uni-input dateInput">
            {{ item.answerInfo }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getReplyDiagnosisAnswer } from '@/api/chatCardDetail';
import rate from '@/components/rate/rate.vue';
import * as myJsTools from '@/common/js/myJsTools';
export default {
  components: { rate },
  data() {
    return {
      pageParam: '',
      saveInfo: '',
      time: '',
      pickerIndex: -1,
      date: '',
      feedbackId: '',
    };
  },
  onLoad(option) {
    this.pageParam = JSON.parse(option.param);
  },
  created() {
    this.getQuestionDetailFun();
  },
  methods: {
    previewImg(img) {
      myJsTools.previewImg(img);
    },
    async getQuestionDetailFun() {
      let questionQuery = this.pageParam;
      let res = await getReplyDiagnosisAnswer(questionQuery);
      let questionInfo = res.data;
      let answerList = questionInfo.answerList;
      for (let i = 0; i < answerList.length; i++) {
        let obj = answerList[i];
        if (obj.didtType == '1' || obj.didtType == '2') {
          obj.answerInfo = JSON.parse(obj.answerInfo);
        }
        // 单选
        if (obj.didtType == '4') {
          let answerInfo = JSON.parse(obj.answerInfo);
          obj.answerInfo = '';
          for (let j = 0; j < answerInfo.length; j++) {
            // 查询是否选中
            if (answerInfo[j].checked) {
              obj.answerInfo = answerInfo[j].optionName;
            }
          }
        }
        // 图片类型
        if (obj.didtType == '5') {
          let ossName = obj.answerInfo;
          if (ossName) {
            myJsTools.downAndSaveImg(ossName, (url) => {
              obj.answerInfo = url;
            });
          }
        }
      }
      this.saveInfo = Object.assign({}, questionInfo);
    },
  },
};
</script>

<style lang="scss" scoped>
.quesView-item{
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 1px 4px  rgba(121, 72, 234, 0.15);
}
::v-deep body{
  background: rgba(240, 242, 252, 1) !important;
}
.questionnaire{
  background: rgba(240, 242, 252, 1);
  padding-top: 24rpx;
}
.header {
  background: #ffffff;
  margin: 0rpx 32rpx 0px;
  font-size: 28rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0px 1px 4px  rgba(121, 72, 234, 0.15);
  border-radius: 16rpx;
  .title {
    color:rgba(51, 51, 51, 1);
    font-weight: 600;
    line-height: 44rpx;
  }
  .tips {
    color: #999999;
    line-height: 36rpx;
    margin-top: 10rpx;
  }
}

// 评分
::v-deep .rate-media-cell {
  @include flex;
}

// 修改单选样式
::v-deep .uni-radio-input.uni-radio-input-checked:before {
  content: '';
  width: 20upx;
  height: 20upx;
  border-radius: 10upx;
  display: inline-block;
  @include bg_theme;
}

::v-deep .uni-radio-input-checked {
  background: none !important;
}

.quesView {
  padding: 0 32rpx 32rpx;
  .quesAnswerView {
    margin-top: 24rpx;
    background: #ffffff;
    font-size: 28rpx;
    padding: 32rpx 24rpx;
    box-shadow: 0 0 20rpx rgba(198, 204, 206, 0.2);
    border-radius: 16rpx;
    .didName {
      color: #333333;
      font-weight: 600;
      line-height: 44rpx;
    }
    text {
      color: #666666;
      font-size: 26rpx;
      line-height: 40rpx;
    }
    ::v-deepuni-radio,
    uni-checkbox {
      margin-top: 16px;
    }

    ::v-deepuni-checkbox .uni-checkbox-input.uni-checkbox-input-checked:before {
      font-size: 23px;
      font-weight: 800;
    }
  }
  .contentView {
    ::v-deep.uni-textarea-textarea {
      font-size: 26rpx;
      line-height: 36rpx;
    }
    .content {
      border: 1px solid #d5d5d5;
      margin-top: 24rpx;
      padding: 24rpx;

      textarea {
        width: 100%;
        height: 124upx;
      }
    }
  }
  .dateView,
  .timeView,
  .selectView,
  .imgView {
    .dateInput {
      border: 1px solid #d5d5d5;
      margin-top: 24rpx;
      color: #333333;
      padding: 20rpx 24rpx;
      position: relative;
      font-size: 28rpx;
    }
    .downIcon {
      width: 32rpx;
      height: 32rpx;
      position: absolute;
      right: 24rpx;
    }
    .dateInputPlac {
      color: #999999;
    }
  }
  .imgView {
    .dateInput {
      display: flex;
      align-items: center;
      text {
        color: #999999;
      }
    }
    .imgIcon {
      width: 32rpx;
      height: 34rpx;
      margin-right: 12upx;
    }
    .editIcon {
      width: 32rpx;
      height: 32rpx;
      position: absolute;
      right: 24rpx;
    }
  }
}
.commit {
  position: fixed;
  bottom: 0;
  width: 100%;
  @include bg_theme;
  color: #ffffff;
  height: 98rpx;
  line-height: 98rpx;
  text-align: center;
}
.imgView {
  position: relative;
  .upImg {
    margin-top: 24rpx;
    width: 80%;
  }
  .delIcon {
    width: 32rpx;
    height: 32rpx;
    position: absolute;
    right: 60rpx;
    top: 80rpx;
  }
}
</style>
