"use strict";
const common_vendor = require("./common/vendor.js");
const store_index = require("./store/index.js");
let list = ["navigateTo", "redirectTo", "reLaunch", "switchTab"];
list.forEach((item) => {
  common_vendor.index.addInterceptor(item, {
    invoke(args) {
      const currentPath = args.url.split("?")[0];
      store_index.store.dispatch("updateMenusActive", currentPath);
    },
    fail(err) {
      console.log(err);
    }
  });
});
