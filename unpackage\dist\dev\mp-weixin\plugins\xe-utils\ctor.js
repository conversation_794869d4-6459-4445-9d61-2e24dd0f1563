"use strict";
const plugins_xeUtils_setupDefaults = require("./setupDefaults.js");
const plugins_xeUtils_arrayEach = require("./arrayEach.js");
const plugins_xeUtils_each = require("./each.js");
const plugins_xeUtils_isFunction = require("./isFunction.js");
const plugins_xeUtils_assign = require("./assign.js");
var XEUtils = function() {
};
function mixin() {
  plugins_xeUtils_arrayEach.arrayEach(arguments, function(methods) {
    plugins_xeUtils_each.each(methods, function(fn, name) {
      XEUtils[name] = plugins_xeUtils_isFunction.isFunction(fn) ? function() {
        var result = fn.apply(XEUtils.$context, arguments);
        XEUtils.$context = null;
        return result;
      } : fn;
    });
  });
}
function setup(options) {
  return plugins_xeUtils_assign.assign(plugins_xeUtils_setupDefaults.setupDefaults, options);
}
XEUtils.VERSION = "3.5.12";
XEUtils.mixin = mixin;
XEUtils.setup = setup;
exports.XEUtils = XEUtils;
