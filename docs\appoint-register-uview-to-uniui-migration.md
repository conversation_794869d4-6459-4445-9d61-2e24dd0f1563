# 预约挂号病情描述页面 u-view 到 uni-ui 迁移文档

## 🎯 迁移目标
将 `pages/register/appointRegister/diseaseDetail/index.vue` 文件中的 u-view 组件替换为 uni-ui 组件，提高跨平台兼容性。

## 📝 主要修改内容

### 1. u-icon → uni-icon

**原来的 u-view 组件：**
```vue
<u-icon name="arrow-right" color="rgba(166, 166, 166, 1)" />
```

**新的 uni-ui 组件：**
```vue
<uni-icon type="right" color="rgba(166, 166, 166, 1)" size="25"/>
```

**主要差异：**
- `name` → `type`
- 需要指定 `size` 属性

### 2. u-popup → uni-popup

**原来的 u-view 组件：**
```vue
<u-popup v-model="isFirstVisitHos" mode="bottom">
  <view>
    <!-- 内容 -->
  </view>
</u-popup>
```

**新的 uni-ui 组件：**
```vue
<uni-popup ref="hospitalPopup" type="bottom" @change="onPopupChange">
  <view class="popup-content">
    <!-- 内容 -->
  </view>
</uni-popup>
```

**主要差异：**
- `v-model` → 通过 `ref` 手动控制 + `@change` 事件
- `mode` → `type`
- 需要添加 `popup-content` 样式类

### 3. u-search → uni-search-bar

**原来的 u-view 组件：**
```vue
<u-search 
  placeholder="请输入" 
  v-model="keyword" 
  :show-action="false" 
  shape="round" 
  @search="getSearch"
></u-search>
```

**新的 uni-ui 组件：**
```vue
<uni-search-bar 
  :radius="100" 
  placeholder="请输入" 
  v-model="keyword" 
  @input="onSearchInput"
  @confirm="getSearch"
  @clear="onSearchClear"
></uni-search-bar>
```

**主要差异：**
- `shape="round"` → `:radius="100"`
- `@search` → `@confirm`
- 新增 `@input` 和 `@clear` 事件处理

## 🔧 新增的方法

### 弹窗控制方法
```javascript
// 打开医院选择弹窗
openHospitalPopup() {
  this.$refs.hospitalPopup.open();
},

// 弹窗状态变化处理
onPopupChange(e) {
  this.isFirstVisitHos = e.show;
},

// 关闭弹窗
closePopup() {
  this.isFirstVisitHos = false;
  this.$refs.hospitalPopup.close();
},
```

### 搜索相关方法
```javascript
// 搜索输入处理
onSearchInput(value) {
  this.keyword = value;
},

// 清空搜索处理
onSearchClear() {
  this.keyword = '';
  this.currentPage = 1;
  this.getHosList(); // 清空搜索时重新加载所有医院
},
```

### 修改的方法
```javascript
// 确认选择医院（优化后）
sureEvent() {
  if (this.selectedHospitalIndex === null) {
    this.info.firstVisitHos = this.hosList[0]?.name;
  } else {
    this.info.firstVisitHos = this.hosList[this.selectedHospitalIndex]?.name;
  }
  this.closePopup(); // 使用新的关闭方法
},
```

## 🎨 新增的样式

```scss
.popup-content {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  overflow: hidden;
}

.popup-top {
  display: flex;
  align-items: center;
  line-height: 50px;
  justify-content: space-between;
  padding: 0 20px;
  border-bottom: 1px solid #eee;
  
  .success {
    color: #15a0e6;
  }
  
  .cancel {
    color: #666;
  }
}
```

## ✨ 功能特性

### 改进的交互体验
- ✅ 更流畅的弹窗动画
- ✅ 更好的搜索体验（实时输入监听）
- ✅ 清空搜索功能
- ✅ 更一致的图标样式

### 跨平台兼容性
- ✅ 小程序兼容性更好
- ✅ H5 表现更稳定
- ✅ App 端性能更优

## 🚀 使用方法

### 触发医院选择
```vue
<view class="select-box" @click="openHospitalPopup">
  <view>{{ info.firstVisitHos || '选择' }}</view>
  <uni-icon type="right" color="rgba(166, 166, 166, 1)" size="25"/>
</view>
```

### 弹窗内容
```vue
<uni-popup ref="hospitalPopup" type="bottom" @change="onPopupChange">
  <view class="popup-content">
    <view class="popup-top">
      <view class="cancel" @click="closePopup">取消</view>
      <view class="success" @click="sureEvent">完成</view>
    </view>
    <!-- 搜索和列表内容 -->
  </view>
</uni-popup>
```

## ⚠️ 注意事项

1. **组件引入**：确保项目中已正确引入 uni-ui 组件库
2. **事件处理**：uni-ui 的事件机制与 u-view 略有不同
3. **样式适配**：需要为弹窗内容添加适当的样式
4. **性能优化**：搜索功能建议添加防抖处理

## 🧪 测试建议

1. **基础功能测试**：
   - 点击选择框打开弹窗
   - 搜索医院功能
   - 选择医院并确认
   - 取消操作

2. **交互测试**：
   - 弹窗动画效果
   - 搜索框清空功能
   - 滚动加载更多
   - 点击遮罩关闭

3. **平台兼容性测试**：
   - 微信小程序
   - H5 浏览器
   - App 端

## 📋 后续优化建议

1. 添加搜索防抖功能
2. 优化医院列表的虚拟滚动
3. 添加医院选择的历史记录
4. 支持医院分类筛选
