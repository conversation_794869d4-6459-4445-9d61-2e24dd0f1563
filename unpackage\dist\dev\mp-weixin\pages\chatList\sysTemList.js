"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_date = require("../../utils/date.js");
const api_chat = require("../../api/chat.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  name: "sysTemList",
  data() {
    return {
      list: [],
      loading: false,
      isShowMore: false,
      refreshing: false,
      listQuery: {
        page: 1,
        maxNum: 0,
        limit: 10,
        total: 10
      },
      historyList: [],
      chatId: "",
      status: "loading"
    };
  },
  computed: {
    chatList() {
      let chatList = this.$store.getters.getChatList;
      let list = chatList.chatRecordList;
      if (list) {
        list.sort(function(a, b) {
          return b.time - a.time;
        });
      } else {
        list = [];
      }
      this.listQuery.total = list.length;
      return list;
    }
  },
  async onShow() {
  },
  onPullDownRefresh() {
    this.listQuery.maxNum = 0;
    this.historyList = [];
    this.loadMoreData();
  },
  async created() {
    this.chatId = "admin,admin";
    let obj = {
      chatId: this.chatId
    };
    await this.$store.dispatch("getChatListId", obj);
    setTimeout(() => {
      this.loadMoreData();
    }, 200);
  },
  methods: {
    setIcon(ext) {
      let path = "";
      switch (ext.type) {
        case "HZ_WZ_SF":
        case "HZ_WZ_YSJZ":
        case "HZ_WZ_JSWZ":
        case "HZ_WZ_YSTZ":
        case "HZ_WZ_JJJZ":
          path = CONFIG_ENV.VUE_APP_SHARE;
          break;
        case "HZ_XT_QF":
        case "HZ_SF_SF":
          path = CONFIG_ENV.VUE_APP_SHARE;
          break;
        default:
          path = CONFIG_ENV.VUE_APP_SHARE;
          break;
      }
      return path;
    },
    getTime(timestamp) {
      let time = "";
      let times = utils_date.date.DateDifferenceMsgTime(timestamp);
      if (times.days > 0) {
        time = utils_date.date.getNowDate(Number(timestamp));
      } else if (times.hours > 0) {
        time = times.hours + "小时前";
      } else if (times.minutes > 0) {
        time = times.minutes + "分钟前";
      } else {
        time = "刚刚";
      }
      return time;
    },
    async readMsg(item) {
      if (item.ext.type === "HZ_PLTS_DIAGNOSIS") {
        let isFeedback = null;
        let res = await api_chat.getQuestionStatus({
          sendId: item.ext.sendId,
          patientId: item.ext.patientId
        });
        isFeedback = res.data.isFeedback;
        item.ext.isFeedback = isFeedback;
        let param = {
          sendId: item.ext.sendId,
          patientId: item.ext.patientId,
          docId: item.ext.docId
        };
        if (isFeedback == "1") {
          common_vendor.index.navigateTo({
            url: "/pages/chatCardDetail/questionnaireRead?param=" + JSON.stringify(param)
          });
        } else {
          common_vendor.index.navigateTo({
            url: "/pages/chatCardDetail/questionnaire?action=chatRoom&param=" + JSON.stringify(param)
          });
        }
        return;
      }
      if (item.status == "unread") {
        var bodyId = item.id;
        var ackMsg = new this.$im.message("read", this.$im.conn.getUniqueId());
        ackMsg.set({
          id: bodyId,
          to: item.from
        });
        this.$im.conn.send(ackMsg.body);
        item.status = "read";
        this.$store.commit("updateMessageStatus", item);
      }
      if (item.ext.type == "HZ_WZ_ADD_JZR") {
        common_vendor.index.setStorageSync("patientId", item.ext.patientId);
        common_vendor.index.navigateTo({
          url: "/pages/personalCenter/patientManage/editorPatient/index"
        });
        return;
      }
      if (!item.ext)
        return;
      if (item.ext.type == "HZ_XT_QF") {
        common_vendor.index.navigateTo({
          url: "./system/detail?id=" + item.ext.sendId
        });
        return;
      }
      if (item.ext.type == "HZ_SF_SF")
        return;
      this.$store.commit("SET_NOTICE_DETAIL", item);
      common_vendor.index.navigateTo({
        url: "./notice/notice"
      });
    },
    loadMoreData() {
      common_vendor.index.stopPullDownRefresh();
      this.isShowMore = true;
      if (this.listQuery.maxNum == this.listQuery.total) {
        this.status = "noMore";
        return;
      }
      let maxNum = this.listQuery.maxNum + this.listQuery.limit;
      if (maxNum >= this.listQuery.total) {
        maxNum = this.listQuery.total;
      }
      for (var i = this.listQuery.maxNum; i < maxNum; i++) {
        if (i >= this.listQuery.total) {
          break;
        } else {
          this.historyList.push(this.chatList[i]);
        }
      }
      this.listQuery.maxNum = maxNum;
      this.isShowMore = false;
    }
  }
};
if (!Array) {
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  _easycom_uni_load_more2();
}
const _easycom_uni_load_more = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
if (!Math) {
  _easycom_uni_load_more();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.historyList.length == 0
  }, $data.historyList.length == 0 ? {
    b: common_assets._imports_0$1
  } : {
    d: common_vendor.f($data.historyList, (item, index, i0) => {
      return common_vendor.e({
        a: item.status == "unread"
      }, item.status == "unread" ? {} : {}, {
        b: item.ext.type == "HZ_XT_QF"
      }, item.ext.type == "HZ_XT_QF" ? {} : item.ext.type == "HZ_SF_SF" ? {} : item.ext.type == "HZ_WZ_ADD_JZR" ? {} : item.ext.type == "HZ_PLTS_DIAGNOSIS" ? {} : {}, {
        c: item.ext.type == "HZ_SF_SF",
        d: item.ext.type == "HZ_WZ_ADD_JZR",
        e: item.ext.type == "HZ_PLTS_DIAGNOSIS",
        f: common_vendor.t($options.getTime(item.time)),
        g: common_vendor.t(item.content),
        h: index,
        i: common_vendor.o(($event) => $options.readMsg(item), index)
      });
    }),
    e: common_assets._imports_1$1
  }, {
    c: $data.historyList.length > 0,
    f: $data.isShowMore
  }, $data.isShowMore ? {
    g: common_vendor.p({
      status: $data.status
    })
  } : {}, {
    h: common_vendor.o((...args) => $options.loadMoreData && $options.loadMoreData(...args))
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-254fe4bf"]]);
wx.createComponent(Component);
