"use strict";
const plugins_xeUtils_ctor = require("./ctor.js");
const plugins_xeUtils_assign = require("./assign.js");
const plugins_xeUtils_objectEach = require("./objectEach.js");
const plugins_xeUtils_lastObjectEach = require("./lastObjectEach.js");
const plugins_xeUtils_objectMap = require("./objectMap.js");
const plugins_xeUtils_merge = require("./merge.js");
const plugins_xeUtils_map = require("./map.js");
const plugins_xeUtils_some = require("./some.js");
const plugins_xeUtils_every = require("./every.js");
const plugins_xeUtils_includeArrays = require("./includeArrays.js");
const plugins_xeUtils_arrayEach = require("./arrayEach.js");
const plugins_xeUtils_lastArrayEach = require("./lastArrayEach.js");
const plugins_xeUtils_uniq = require("./uniq.js");
const plugins_xeUtils_union = require("./union.js");
const plugins_xeUtils_toArray = require("./toArray.js");
const plugins_xeUtils_sortBy = require("./sortBy.js");
const plugins_xeUtils_orderBy = require("./orderBy.js");
const plugins_xeUtils_shuffle = require("./shuffle.js");
const plugins_xeUtils_sample = require("./sample.js");
const plugins_xeUtils_slice = require("./slice.js");
const plugins_xeUtils_filter = require("./filter.js");
const plugins_xeUtils_findKey = require("./findKey.js");
const plugins_xeUtils_includes = require("./includes.js");
const plugins_xeUtils_find = require("./find.js");
const plugins_xeUtils_findLast = require("./findLast.js");
const plugins_xeUtils_reduce = require("./reduce.js");
const plugins_xeUtils_copyWithin = require("./copyWithin.js");
const plugins_xeUtils_chunk = require("./chunk.js");
const plugins_xeUtils_zip = require("./zip.js");
const plugins_xeUtils_unzip = require("./unzip.js");
const plugins_xeUtils_zipObject = require("./zipObject.js");
const plugins_xeUtils_flatten = require("./flatten.js");
const plugins_xeUtils_pluck = require("./pluck.js");
const plugins_xeUtils_invoke = require("./invoke.js");
const plugins_xeUtils_toArrayTree = require("./toArrayTree.js");
const plugins_xeUtils_toTreeArray = require("./toTreeArray.js");
const plugins_xeUtils_findTree = require("./findTree.js");
const plugins_xeUtils_eachTree = require("./eachTree.js");
const plugins_xeUtils_mapTree = require("./mapTree.js");
const plugins_xeUtils_filterTree = require("./filterTree.js");
const plugins_xeUtils_searchTree = require("./searchTree.js");
const plugins_xeUtils_arrayIndexOf = require("./arrayIndexOf.js");
const plugins_xeUtils_arrayLastIndexOf = require("./arrayLastIndexOf.js");
const plugins_xeUtils_hasOwnProp = require("./hasOwnProp.js");
const plugins_xeUtils_isArray = require("./isArray.js");
const plugins_xeUtils_isNull = require("./isNull.js");
const plugins_xeUtils_isNaN = require("./isNaN.js");
const plugins_xeUtils_isUndefined = require("./isUndefined.js");
const plugins_xeUtils_isFunction = require("./isFunction.js");
const plugins_xeUtils_isObject = require("./isObject.js");
const plugins_xeUtils_isString = require("./isString.js");
const plugins_xeUtils_isPlainObject = require("./isPlainObject.js");
const plugins_xeUtils_isLeapYear = require("./isLeapYear.js");
const plugins_xeUtils_isDate = require("./isDate.js");
const plugins_xeUtils_eqNull = require("./eqNull.js");
const plugins_xeUtils_each = require("./each.js");
const plugins_xeUtils_forOf = require("./forOf.js");
const plugins_xeUtils_lastForOf = require("./lastForOf.js");
const plugins_xeUtils_indexOf = require("./indexOf.js");
const plugins_xeUtils_lastIndexOf = require("./lastIndexOf.js");
const plugins_xeUtils_keys = require("./keys.js");
const plugins_xeUtils_values = require("./values.js");
const plugins_xeUtils_clone = require("./clone.js");
const plugins_xeUtils_getSize = require("./getSize.js");
const plugins_xeUtils_lastEach = require("./lastEach.js");
const plugins_xeUtils_remove = require("./remove.js");
const plugins_xeUtils_clear = require("./clear.js");
const plugins_xeUtils_isFinite = require("./isFinite.js");
const plugins_xeUtils_isFloat = require("./isFloat.js");
const plugins_xeUtils_isInteger = require("./isInteger.js");
const plugins_xeUtils_isBoolean = require("./isBoolean.js");
const plugins_xeUtils_isNumber = require("./isNumber.js");
const plugins_xeUtils_isRegExp = require("./isRegExp.js");
const plugins_xeUtils_isError = require("./isError.js");
const plugins_xeUtils_isTypeError = require("./isTypeError.js");
const plugins_xeUtils_isEmpty = require("./isEmpty.js");
const plugins_xeUtils_isSymbol = require("./isSymbol.js");
const plugins_xeUtils_isArguments = require("./isArguments.js");
const plugins_xeUtils_isElement = require("./isElement.js");
const plugins_xeUtils_isDocument = require("./isDocument.js");
const plugins_xeUtils_isWindow = require("./isWindow.js");
const plugins_xeUtils_isFormData = require("./isFormData.js");
const plugins_xeUtils_isMap = require("./isMap.js");
const plugins_xeUtils_isWeakMap = require("./isWeakMap.js");
const plugins_xeUtils_isSet = require("./isSet.js");
const plugins_xeUtils_isWeakSet = require("./isWeakSet.js");
const plugins_xeUtils_isMatch = require("./isMatch.js");
const plugins_xeUtils_isEqual = require("./isEqual.js");
const plugins_xeUtils_isEqualWith = require("./isEqualWith.js");
const plugins_xeUtils_getType = require("./getType.js");
const plugins_xeUtils_uniqueId = require("./uniqueId.js");
const plugins_xeUtils_findIndexOf = require("./findIndexOf.js");
const plugins_xeUtils_findLastIndexOf = require("./findLastIndexOf.js");
const plugins_xeUtils_toStringJSON = require("./toStringJSON.js");
const plugins_xeUtils_toJSONString = require("./toJSONString.js");
const plugins_xeUtils_entries = require("./entries.js");
const plugins_xeUtils_pick = require("./pick.js");
const plugins_xeUtils_omit = require("./omit.js");
const plugins_xeUtils_first = require("./first.js");
const plugins_xeUtils_last = require("./last.js");
const plugins_xeUtils_has = require("./has.js");
const plugins_xeUtils_get = require("./get.js");
const plugins_xeUtils_set = require("./set.js");
const plugins_xeUtils_groupBy = require("./groupBy.js");
const plugins_xeUtils_countBy = require("./countBy.js");
const plugins_xeUtils_range = require("./range.js");
const plugins_xeUtils_destructuring = require("./destructuring.js");
const plugins_xeUtils_random = require("./random.js");
const plugins_xeUtils_max = require("./max.js");
const plugins_xeUtils_min = require("./min.js");
const plugins_xeUtils_commafy = require("./commafy.js");
const plugins_xeUtils_round = require("./round.js");
const plugins_xeUtils_ceil = require("./ceil.js");
const plugins_xeUtils_floor = require("./floor.js");
const plugins_xeUtils_toFixed = require("./toFixed.js");
const plugins_xeUtils_toInteger = require("./toInteger.js");
const plugins_xeUtils_toNumber = require("./toNumber.js");
const plugins_xeUtils_toNumberString = require("./toNumberString.js");
const plugins_xeUtils_add = require("./add.js");
const plugins_xeUtils_subtract = require("./subtract.js");
const plugins_xeUtils_multiply = require("./multiply.js");
const plugins_xeUtils_divide = require("./divide.js");
const plugins_xeUtils_sum = require("./sum.js");
const plugins_xeUtils_mean = require("./mean.js");
const plugins_xeUtils_getWhatYear = require("./getWhatYear.js");
const plugins_xeUtils_getWhatQuarter = require("./getWhatQuarter.js");
const plugins_xeUtils_getWhatMonth = require("./getWhatMonth.js");
const plugins_xeUtils_getWhatDay = require("./getWhatDay.js");
const plugins_xeUtils_toStringDate = require("./toStringDate.js");
const plugins_xeUtils_toDateString = require("./toDateString.js");
const plugins_xeUtils_now = require("./now.js");
const plugins_xeUtils_timestamp = require("./timestamp.js");
const plugins_xeUtils_isValidDate = require("./isValidDate.js");
const plugins_xeUtils_isDateSame = require("./isDateSame.js");
const plugins_xeUtils_getWhatWeek = require("./getWhatWeek.js");
const plugins_xeUtils_getYearDay = require("./getYearDay.js");
const plugins_xeUtils_getYearWeek = require("./getYearWeek.js");
const plugins_xeUtils_getMonthWeek = require("./getMonthWeek.js");
const plugins_xeUtils_getDayOfYear = require("./getDayOfYear.js");
const plugins_xeUtils_getDayOfMonth = require("./getDayOfMonth.js");
const plugins_xeUtils_getDateDiff = require("./getDateDiff.js");
const plugins_xeUtils_padEnd = require("./padEnd.js");
const plugins_xeUtils_padStart = require("./padStart.js");
const plugins_xeUtils_repeat = require("./repeat.js");
const plugins_xeUtils_trim = require("./trim.js");
const plugins_xeUtils_trimRight = require("./trimRight.js");
const plugins_xeUtils_trimLeft = require("./trimLeft.js");
const plugins_xeUtils_escape = require("./escape.js");
const plugins_xeUtils_unescape = require("./unescape.js");
const plugins_xeUtils_camelCase = require("./camelCase.js");
const plugins_xeUtils_kebabCase = require("./kebabCase.js");
const plugins_xeUtils_startsWith = require("./startsWith.js");
const plugins_xeUtils_endsWith = require("./endsWith.js");
const plugins_xeUtils_template = require("./template.js");
const plugins_xeUtils_toFormatString = require("./toFormatString.js");
const plugins_xeUtils_toValueString = require("./toValueString.js");
const plugins_xeUtils_noop = require("./noop.js");
const plugins_xeUtils_property = require("./property.js");
const plugins_xeUtils_bind = require("./bind.js");
const plugins_xeUtils_once = require("./once.js");
const plugins_xeUtils_after = require("./after.js");
const plugins_xeUtils_before = require("./before.js");
const plugins_xeUtils_throttle = require("./throttle.js");
const plugins_xeUtils_debounce = require("./debounce.js");
const plugins_xeUtils_delay = require("./delay.js");
const plugins_xeUtils_unserialize = require("./unserialize.js");
const plugins_xeUtils_serialize = require("./serialize.js");
const plugins_xeUtils_parseUrl = require("./parseUrl.js");
const plugins_xeUtils_getBaseURL = require("./getBaseURL.js");
const plugins_xeUtils_locat = require("./locat.js");
const plugins_xeUtils_cookie = require("./cookie.js");
const plugins_xeUtils_browse = require("./browse.js");
plugins_xeUtils_assign.assign(plugins_xeUtils_ctor.XEUtils, {
  // object
  assign: plugins_xeUtils_assign.assign,
  objectEach: plugins_xeUtils_objectEach.objectEach,
  lastObjectEach: plugins_xeUtils_lastObjectEach.lastObjectEach,
  objectMap: plugins_xeUtils_objectMap.objectMap,
  merge: plugins_xeUtils_merge.merge,
  // array
  uniq: plugins_xeUtils_uniq.uniq,
  union: plugins_xeUtils_union.union,
  sortBy: plugins_xeUtils_sortBy.sortBy,
  orderBy: plugins_xeUtils_orderBy.orderBy,
  shuffle: plugins_xeUtils_shuffle.shuffle,
  sample: plugins_xeUtils_sample.sample,
  some: plugins_xeUtils_some.some,
  every: plugins_xeUtils_every.every,
  slice: plugins_xeUtils_slice.slice,
  filter: plugins_xeUtils_filter.filter,
  find: plugins_xeUtils_find.find,
  findLast: plugins_xeUtils_findLast.findLast,
  findKey: plugins_xeUtils_findKey.findKey,
  includes: plugins_xeUtils_includes.includes,
  arrayIndexOf: plugins_xeUtils_arrayIndexOf.arrayIndexOf,
  arrayLastIndexOf: plugins_xeUtils_arrayLastIndexOf.arrayLastIndexOf,
  map: plugins_xeUtils_map.map,
  reduce: plugins_xeUtils_reduce.reduce,
  copyWithin: plugins_xeUtils_copyWithin.copyWithin,
  chunk: plugins_xeUtils_chunk.chunk,
  zip: plugins_xeUtils_zip.zip,
  unzip: plugins_xeUtils_unzip.unzip,
  zipObject: plugins_xeUtils_zipObject.zipObject,
  flatten: plugins_xeUtils_flatten.flatten,
  toArray: plugins_xeUtils_toArray.toArray,
  includeArrays: plugins_xeUtils_includeArrays.includeArrays,
  pluck: plugins_xeUtils_pluck.pluck,
  invoke: plugins_xeUtils_invoke.invoke,
  arrayEach: plugins_xeUtils_arrayEach.arrayEach,
  lastArrayEach: plugins_xeUtils_lastArrayEach.lastArrayEach,
  toArrayTree: plugins_xeUtils_toArrayTree.toArrayTree,
  toTreeArray: plugins_xeUtils_toTreeArray.toTreeArray,
  findTree: plugins_xeUtils_findTree.findTree,
  eachTree: plugins_xeUtils_eachTree.eachTree,
  mapTree: plugins_xeUtils_mapTree.mapTree,
  filterTree: plugins_xeUtils_filterTree.filterTree,
  searchTree: plugins_xeUtils_searchTree.searchTree,
  // base
  hasOwnProp: plugins_xeUtils_hasOwnProp.hasOwnProp,
  eqNull: plugins_xeUtils_eqNull.eqNull,
  isNaN: plugins_xeUtils_isNaN.isNumberNaN,
  isFinite: plugins_xeUtils_isFinite.isNumberFinite,
  isUndefined: plugins_xeUtils_isUndefined.isUndefined,
  isArray: plugins_xeUtils_isArray.isArray,
  isFloat: plugins_xeUtils_isFloat.isFloat,
  isInteger: plugins_xeUtils_isInteger.isInteger,
  isFunction: plugins_xeUtils_isFunction.isFunction,
  isBoolean: plugins_xeUtils_isBoolean.isBoolean,
  isString: plugins_xeUtils_isString.isString,
  isNumber: plugins_xeUtils_isNumber.isNumber,
  isRegExp: plugins_xeUtils_isRegExp.isRegExp,
  isObject: plugins_xeUtils_isObject.isObject,
  isPlainObject: plugins_xeUtils_isPlainObject.isPlainObject,
  isDate: plugins_xeUtils_isDate.isDate,
  isError: plugins_xeUtils_isError.isError,
  isTypeError: plugins_xeUtils_isTypeError.isTypeError,
  isEmpty: plugins_xeUtils_isEmpty.isEmpty,
  isNull: plugins_xeUtils_isNull.isNull,
  isSymbol: plugins_xeUtils_isSymbol.isSymbol,
  isArguments: plugins_xeUtils_isArguments.isArguments,
  isElement: plugins_xeUtils_isElement.isElement,
  isDocument: plugins_xeUtils_isDocument.isDocument,
  isWindow: plugins_xeUtils_isWindow.isWindow,
  isFormData: plugins_xeUtils_isFormData.isFormData,
  isMap: plugins_xeUtils_isMap.isMap,
  isWeakMap: plugins_xeUtils_isWeakMap.isWeakMap,
  isSet: plugins_xeUtils_isSet.isSet,
  isWeakSet: plugins_xeUtils_isWeakSet.isWeakSet,
  isLeapYear: plugins_xeUtils_isLeapYear.isLeapYear,
  isMatch: plugins_xeUtils_isMatch.isMatch,
  isEqual: plugins_xeUtils_isEqual.isEqual,
  isEqualWith: plugins_xeUtils_isEqualWith.isEqualWith,
  getType: plugins_xeUtils_getType.getType,
  uniqueId: plugins_xeUtils_uniqueId.uniqueId,
  getSize: plugins_xeUtils_getSize.getSize,
  indexOf: plugins_xeUtils_indexOf.indexOf,
  lastIndexOf: plugins_xeUtils_lastIndexOf.lastIndexOf,
  findIndexOf: plugins_xeUtils_findIndexOf.findIndexOf,
  findLastIndexOf: plugins_xeUtils_findLastIndexOf.findLastIndexOf,
  toStringJSON: plugins_xeUtils_toStringJSON.toStringJSON,
  toJSONString: plugins_xeUtils_toJSONString.toJSONString,
  keys: plugins_xeUtils_keys.keys,
  values: plugins_xeUtils_values.values,
  entries: plugins_xeUtils_entries.entries,
  pick: plugins_xeUtils_pick.pick,
  omit: plugins_xeUtils_omit.omit,
  first: plugins_xeUtils_first.first,
  last: plugins_xeUtils_last.last,
  each: plugins_xeUtils_each.each,
  forOf: plugins_xeUtils_forOf.forOf,
  lastForOf: plugins_xeUtils_lastForOf.lastForOf,
  lastEach: plugins_xeUtils_lastEach.lastEach,
  has: plugins_xeUtils_has.has,
  get: plugins_xeUtils_get.get,
  set: plugins_xeUtils_set.set,
  groupBy: plugins_xeUtils_groupBy.groupBy,
  countBy: plugins_xeUtils_countBy.countBy,
  clone: plugins_xeUtils_clone.clone,
  clear: plugins_xeUtils_clear.clear,
  remove: plugins_xeUtils_remove.remove,
  range: plugins_xeUtils_range.range,
  destructuring: plugins_xeUtils_destructuring.destructuring,
  // number
  random: plugins_xeUtils_random.random,
  min: plugins_xeUtils_min.min,
  max: plugins_xeUtils_max.max,
  commafy: plugins_xeUtils_commafy.commafy,
  round: plugins_xeUtils_round.round,
  ceil: plugins_xeUtils_ceil.ceil,
  floor: plugins_xeUtils_floor.floor,
  toFixed: plugins_xeUtils_toFixed.toFixed,
  toNumber: plugins_xeUtils_toNumber.toNumber,
  toNumberString: plugins_xeUtils_toNumberString.toNumberString,
  toInteger: plugins_xeUtils_toInteger.toInteger,
  add: plugins_xeUtils_add.add,
  subtract: plugins_xeUtils_subtract.subtract,
  multiply: plugins_xeUtils_multiply.multiply,
  divide: plugins_xeUtils_divide.divide,
  sum: plugins_xeUtils_sum.sum,
  mean: plugins_xeUtils_mean.mean,
  // date
  now: plugins_xeUtils_now.now,
  timestamp: plugins_xeUtils_timestamp.timestamp,
  isValidDate: plugins_xeUtils_isValidDate.isValidDate,
  isDateSame: plugins_xeUtils_isDateSame.isDateSame,
  toStringDate: plugins_xeUtils_toStringDate.toStringDate,
  toDateString: plugins_xeUtils_toDateString.toDateString,
  getWhatYear: plugins_xeUtils_getWhatYear.getWhatYear,
  getWhatQuarter: plugins_xeUtils_getWhatQuarter.getWhatQuarter,
  getWhatMonth: plugins_xeUtils_getWhatMonth.getWhatMonth,
  getWhatWeek: plugins_xeUtils_getWhatWeek.getWhatWeek,
  getWhatDay: plugins_xeUtils_getWhatDay.getWhatDay,
  getYearDay: plugins_xeUtils_getYearDay.getYearDay,
  getYearWeek: plugins_xeUtils_getYearWeek.getYearWeek,
  getMonthWeek: plugins_xeUtils_getMonthWeek.getMonthWeek,
  getDayOfYear: plugins_xeUtils_getDayOfYear.getDayOfYear,
  getDayOfMonth: plugins_xeUtils_getDayOfMonth.getDayOfMonth,
  getDateDiff: plugins_xeUtils_getDateDiff.getDateDiff,
  // string
  trim: plugins_xeUtils_trim.trim,
  trimLeft: plugins_xeUtils_trimLeft.trimLeft,
  trimRight: plugins_xeUtils_trimRight.trimRight,
  escape: plugins_xeUtils_escape.escape,
  unescape: plugins_xeUtils_unescape.unescape,
  camelCase: plugins_xeUtils_camelCase.camelCase,
  kebabCase: plugins_xeUtils_kebabCase.kebabCase,
  repeat: plugins_xeUtils_repeat.repeat,
  padStart: plugins_xeUtils_padStart.padStart,
  padEnd: plugins_xeUtils_padEnd.padEnd,
  startsWith: plugins_xeUtils_startsWith.startsWith,
  endsWith: plugins_xeUtils_endsWith.endsWith,
  template: plugins_xeUtils_template.template,
  toFormatString: plugins_xeUtils_toFormatString.toFormatString,
  toString: plugins_xeUtils_toValueString.toValueString,
  toValueString: plugins_xeUtils_toValueString.toValueString,
  // function
  noop: plugins_xeUtils_noop.noop,
  property: plugins_xeUtils_property.property,
  bind: plugins_xeUtils_bind.bind,
  once: plugins_xeUtils_once.once,
  after: plugins_xeUtils_after.after,
  before: plugins_xeUtils_before.before,
  throttle: plugins_xeUtils_throttle.throttle,
  debounce: plugins_xeUtils_debounce.debounce,
  delay: plugins_xeUtils_delay.delay,
  // url
  unserialize: plugins_xeUtils_unserialize.unserialize,
  serialize: plugins_xeUtils_serialize.serialize,
  parseUrl: plugins_xeUtils_parseUrl.parseUrl,
  // web
  getBaseURL: plugins_xeUtils_getBaseURL.getBaseURL,
  locat: plugins_xeUtils_locat.locat,
  browse: plugins_xeUtils_browse.browse,
  cookie: plugins_xeUtils_cookie.cookie
});
