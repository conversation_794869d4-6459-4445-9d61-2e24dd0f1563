<template>
  <view class="inspect_time">
    <!-- 顶部日历容器 -->
    <view class="time_content">
      <view class="top">
        <!-- 标题 -->
        <TITLE title="请选择您要预约的时段" />
      </view>
      <uni-calendar
        :date="visitDate"
        :start-date="timeBettwen.start"
        :end-date="timeBettwen.end"
        :insert="true"
        :selected="visitDates"
        @change="selevtVisitDate"
      />
    </view>

    <!-- 时间段 -->
    <view class="time_list" v-if="list.length">
      <!-- 标题 -->
      <TITLE title="请选择您要预约的时段" />

      <!-- 单个时间 -->
      <view
        class="list_item"
        v-for="(item, index) in list"
        :key="index"
        @click="setTime(item)"
      >
        <text>{{ item.startendTime }}</text>
        <text class="num">剩余人数{{ item.surplusNum }}人</text>
      </view>
    </view>

    <!-- 底部查看更多 -->
    <view class="lock_more" v-if="false"> 查看更多 </view>

    <!-- 空列表 -->
    <view class="list_empt" v-if="!list.length"
      >当前日期没有可预约的时间段</view
    >
  </view>
</template>

<script>
import TITLE from './com/itemTitle.vue';
import { checkLisAppointNum, checkPacsAppointNum } from '@/api/inspect.js';
import myJsTools from '@/common/js/myJsTools.js';

export default {
  components: {
    TITLE,
  },
  data() {
    return {
      // 默认明天
      visitDate: myJsTools.getDate('day', 0),
      // 标记
      visitDates: [],
      // 区间限制
      timeBettwen: {
        start: myJsTools.getDate('day', 0),
        end: myJsTools.getDate('day', 7),
      },
      // 机构id
      dpoId: '',
      // 检验id
      ppiId: '',
      // 检查id
      pliId: '',
      // 显示的排班
      list: [],
      // 所有排班
      all: {},
    };
  },
  onLoad(opt) {
    let { dpoId, ppiId, pliId } = opt;
    if (!dpoId) return;
    this.dpoId = dpoId;
    if (ppiId) this.ppiId = ppiId;
    if (pliId) this.pliId = pliId;
    this.getList();
  },
  onPullDownRefresh() {
    if (!this.dpoId) {
      uni.stopPullDownRefresh();
      return;
    }
    this.getList();
  },
  methods: {
    // 日期选择
    selevtVisitDate(v) {
      let key = v.fulldate;
      // 判断是否存在
      if (this.all.hasOwnProperty(key)) {
        this.list = this.all[key];
      } else {
        this.list = [];
      }
    },
    // 根据参数觉得调用接口
    async getList() {
      let res;
      if (this.pliId) {
        res = await checkLisAppointNum({
          dpoId: this.dpoId,
          pliId: this.pliId,
        });
      } else {
        res = await checkPacsAppointNum({
          dpoId: this.dpoId,
          ppiId: this.ppiId,
        });
      }
      uni.stopPullDownRefresh();
      // 调用处理值
      this.setResult(res.data);
    },
    // 整理值
    setResult(list) {
      let obj = {};

      list.forEach((v) => {
        if (obj[v.visitTime]) {
          obj[v.visitTime].push(v);
        } else {
          obj[v.visitTime] = [v];
        }
      });

      let arr = [];

      for (const key in obj) {
        let v = {
          date: key,
          info: '可预约',
        };
        let s = new Date(v.date).getTime();
        let now = new Date(this.visitDate);
        // 大于等于今天
        if (s >= now) {
          arr.push(v);
        }
      }
      this.all = obj;
      this.visitDates = arr;

      if (!arr.length) return;
      let str = this.visitDate;
      if (obj[str]) {
        // 设置初始值
        this.list = obj[str];
      }
    },
    // 点击选择时间段
    setTime(item) {
      let pages = getCurrentPages();
      // 获取上一页
      let prev = pages[pages.length - 2];
      prev.timeObj = {
        appointDate: item.visitTime,
        appointStartTime: item.startTime,
        appointEndTime: item.endTime,
        showTime: item.visitTime + ' ' + item.startendTime,
        viRealId: item.viRealId,
      };
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background: #f5f5f5;
}
.inspect_time {
  padding: 32rpx;

  .itemTitle {
    font-weight: normal;
  }

  .time_content {
    background: #fff;
    border-radius: 8rpx;
    overflow: hidden;

    .top {
      padding-left: 32rpx;
      padding-top: 8rpx;
    }
  }

  .time_list {
    margin-top: 24rpx;
    background: #fff;
    border-radius: 8rpx;
    padding: 10rpx 32rpx 32rpx;

    .list_item {
      font-size: 28rpx;
      @include flex(lr);
      height: 80rpx;
      padding: 0 52rpx;
      margin-top: 16rpx;
      border-radius: 40rpx;
      border: 1px solid #efefef;

      text {
        color: #333;

        &.num {
          @include font_theme;
        }
      }
    }
  }

  .lock_more {
    margin-top: 24rpx;
    height: 84rpx;
    border-radius: 42rpx;
    @include border_theme;
    @include flex;
    @include font_theme;
    font-size: 32rpx;
    background: #fff;
  }

  .list_empt {
    height: 180rpx;
    @include flex;
    font-size: 24rpx;
    color: #999;
  }
}

/* 日历样式 */
::v-deep.uni-calendar {
  width: 100%;
  // height: 337px;
  overflow: hidden;
}

::v-deep.uni-calendar__backtoday {
  display: none;
}

::v-deep.uni-calendar__content {
  width: 100%;
  height: 100%;
}

::v-deep.uni-calendar__box {
  width: 100%;
  height: 100%;
}

::v-deep.uni-calendar-item__weeks-box-item {
  width: 48px;
  height: 48px;
}

::v-deep.uni-calendar-item__weeks-box-item {
  width: 40px;
}

::v-deep .uni-calendar-item--isDay {
  color: #fff;
}
</style>
