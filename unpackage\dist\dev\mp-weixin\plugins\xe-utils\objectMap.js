"use strict";
const plugins_xeUtils_each = require("./each.js");
const plugins_xeUtils_isFunction = require("./isFunction.js");
const plugins_xeUtils_property = require("./property.js");
function objectMap(obj, iterate, context) {
  var result = {};
  if (obj) {
    if (iterate) {
      if (!plugins_xeUtils_isFunction.isFunction(iterate)) {
        iterate = plugins_xeUtils_property.property(iterate);
      }
      plugins_xeUtils_each.each(obj, function(val, index) {
        result[index] = iterate.call(context, val, index, obj);
      });
    } else {
      return obj;
    }
  }
  return result;
}
exports.objectMap = objectMap;
