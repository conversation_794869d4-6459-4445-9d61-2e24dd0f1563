!function(e,r){"object"==typeof exports&&"object"==typeof module?module.exports=r():"function"==typeof define&&define.amd?define([],r):"object"==typeof exports?exports.websdk=r():e.websdk=r()}(self,(function(){return(self.webpackChunkwebsdk=self.webpackChunkwebsdk||[]).push([[744],{8868:function(e,r,t){t.r(r),t.d(r,{getPresenceStatus:function(){return h},getSubscribedPresenceList:function(){return p},getSubscribedPresencelist:function(){return f},publishPresence:function(){return i},subscribePresence:function(){return u},unsubscribePresence:function(){return l}}),t(2675),t(9463),t(2259),t(8706),t(4346),t(3792),t(739),t(6099),t(3362),t(7764),t(2953);var n=t(8678),s=t(1750),a=t(2056),o=function(e,r,t,n){return new(t||(t=Promise))((function(s,a){function o(e){try{i(n.next(e))}catch(e){a(e)}}function c(e){try{i(n.throw(e))}catch(e){a(e)}}function i(e){var r;e.done?s(e.value):(r=e.value,r instanceof t?r:new t((function(e){e(r)}))).then(o,c)}i((n=n.apply(e,r||[])).next())}))},c=function(e,r){var t,n,s,a,o={label:0,sent:function(){if(1&s[0])throw s[1];return s[1]},trys:[],ops:[]};return a={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(a){return function(c){return function(a){if(t)throw new TypeError("Generator is already executing.");for(;o;)try{if(t=1,n&&(s=2&a[0]?n.return:a[0]?n.throw||((s=n.return)&&s.call(n),0):n.next)&&!(s=s.call(n,a[1])).done)return s;switch(n=0,s&&(a=[2&a[0],s.value]),a[0]){case 0:case 1:s=a;break;case 4:return o.label++,{value:a[1],done:!1};case 5:o.label++,n=a[1],a=[0];continue;case 7:a=o.ops.pop(),o.trys.pop();continue;default:if(!((s=(s=o.trys).length>0&&s[s.length-1])||6!==a[0]&&2!==a[0])){o=0;continue}if(3===a[0]&&(!s||a[1]>s[0]&&a[1]<s[3])){o.label=a[1];break}if(6===a[0]&&o.label<s[1]){o.label=s[1],s=a;break}if(s&&o.label<s[2]){o.label=s[2],o.ops.push(a);break}s[2]&&o.ops.pop(),o.trys.pop();continue}a=r.call(e,o)}catch(e){a=[6,e],n=0}finally{t=s=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}};function i(e){return o(this,void 0,void 0,(function(){var r,t,o,i,u,l,p,f,h;return c(this,(function(c){switch(c.label){case 0:if("string"!=typeof e.description)throw Error('Invalid parameter: "description"');return(r=s.dO.call(this).error)?[2,Promise.reject(r)]:(t=this.context,o=t.accessToken,i=t.orgName,u=t.appName,l=t.userId,p=this.context.jid.clientResource,f={ext:e.description},h={url:"".concat(this.apiUrl,"/").concat(i,"/").concat(u,"/users/").concat(l,"/presence/").concat(p,"/1"),type:"POST",dataType:"json",data:JSON.stringify(f),headers:{Authorization:"Bearer "+o,"Content-Type":"application/json"},success:e.success,error:e.error},a.vF.debug("Call publishPresence:",e),[4,n.RD.call(this,h)]);case 1:return c.sent(),[2]}}))}))}function u(e){if(!Array.isArray(e.usernames))throw Error('Invalid parameter: "usernames"');if(!e.usernames.length)throw Error('"usernames" can not be empty');if("number"!=typeof e.expiry)throw Error('Invalid parameter: "expiry"');var r=s.dO.call(this).error;if(r)return Promise.reject(r);var t=this.context,o=t.orgName,c=t.appName,i=t.userId,u=t.accessToken,l={usernames:e.usernames},p={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(c,"/users/").concat(i,"/presence/").concat(e.expiry),type:"POST",dataType:"json",data:JSON.stringify(l),headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:e.success,error:e.error};return a.vF.debug("Call subscribePresence:",e),n.RD.call(this,p).then((function(e){return e.data={result:null==e?void 0:e.result},e}))}function l(e){return o(this,void 0,void 0,(function(){var r,t,o,i,u,l,p;return c(this,(function(c){switch(c.label){case 0:if(!Array.isArray(e.usernames))throw Error('Invalid parameter: "usernames"');if(!e.usernames.length)throw Error('"usernames" can not be empty');return(r=s.dO.call(this).error)?[2,Promise.reject(r)]:(t=this.context,o=t.orgName,i=t.appName,u=t.userId,l=t.accessToken,p={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(i,"/users/").concat(u,"/presence"),type:"DELETE",dataType:"json",data:JSON.stringify(e.usernames),headers:{Authorization:"Bearer "+l,"Content-Type":"application/json"},success:e.success,error:e.error},a.vF.debug("Call unsubscribePresence:",e),[4,n.RD.call(this,p)]);case 1:return c.sent(),[2]}}))}))}function p(e){if("number"!=typeof e.pageNum||"number"!=typeof e.pageSize)throw Error('Invalid parameter: "pageNum or pageSize"');if(e.pageNum<0||e.pageSize<0)throw Error('"pageNum" should >= 0 and "pageSize" should >= 0');var r=s.dO.call(this).error;if(r)return Promise.reject(r);var t=this.context,o=t.orgName,c=t.appName,i=t.userId,u=t.accessToken,l={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(c,"/users/").concat(i,"/presence/sublist?pageNum=").concat(e.pageNum,"&pageSize=").concat(e.pageSize),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"},success:e.success,error:e.error};return a.vF.debug("Call getSubscribedPresenceList:",e),n.RD.call(this,l).then((function(e){return e.data={result:null==e?void 0:e.result},e}))}var f=p;function h(e){if(!Array.isArray(e.usernames))throw Error('Invalid parameter: "usernames"');if(!e.usernames.length)throw Error('"usernames" can not be empty');var r=s.dO.call(this).error;if(r)return Promise.reject(r);var t={usernames:e.usernames},o=this.context,c=o.orgName,i=o.appName,u=o.userId,l=o.accessToken,p={url:"".concat(this.apiUrl,"/").concat(c,"/").concat(i,"/users/").concat(u,"/presence"),type:"POST",dataType:"json",data:JSON.stringify(t),headers:{Authorization:"Bearer "+l,"Content-Type":"application/json"},success:e.success,error:e.error};return a.vF.debug("Call getPresenceStatus:",e),n.RD.call(this,p).then((function(e){return e.data={result:null==e?void 0:e.result},e}))}}},function(e){return e(e.s=8868)}])}));