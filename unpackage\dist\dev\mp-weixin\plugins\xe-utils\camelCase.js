"use strict";
const plugins_xeUtils_toValueString = require("./toValueString.js");
const plugins_xeUtils_helperStringSubstring = require("./helperStringSubstring.js");
const plugins_xeUtils_helperStringUpperCase = require("./helperStringUpperCase.js");
const plugins_xeUtils_helperStringLowerCase = require("./helperStringLowerCase.js");
var camelCacheMaps = {};
function camelCase(str) {
  str = plugins_xeUtils_toValueString.toValueString(str);
  if (camelCacheMaps[str]) {
    return camelCacheMaps[str];
  }
  var strLen = str.length;
  var rest = str.replace(/([-]+)/g, function(text, flag, index) {
    return index && index + flag.length < strLen ? "-" : "";
  });
  strLen = rest.length;
  rest = rest.replace(/([A-Z]+)/g, function(text, upper, index) {
    var upperLen = upper.length;
    upper = plugins_xeUtils_helperStringLowerCase.helperStringLowerCase(upper);
    if (index) {
      if (upperLen > 2 && index + upperLen < strLen) {
        return plugins_xeUtils_helperStringUpperCase.helperStringUpperCase(plugins_xeUtils_helperStringSubstring.helperStringSubstring(upper, 0, 1)) + plugins_xeUtils_helperStringSubstring.helperStringSubstring(upper, 1, upperLen - 1) + plugins_xeUtils_helperStringUpperCase.helperStringUpperCase(plugins_xeUtils_helperStringSubstring.helperStringSubstring(upper, upperLen - 1, upperLen));
      }
      return plugins_xeUtils_helperStringUpperCase.helperStringUpperCase(plugins_xeUtils_helperStringSubstring.helperStringSubstring(upper, 0, 1)) + plugins_xeUtils_helperStringSubstring.helperStringSubstring(upper, 1, upperLen);
    } else {
      if (upperLen > 1 && index + upperLen < strLen) {
        return plugins_xeUtils_helperStringSubstring.helperStringSubstring(upper, 0, upperLen - 1) + plugins_xeUtils_helperStringUpperCase.helperStringUpperCase(plugins_xeUtils_helperStringSubstring.helperStringSubstring(upper, upperLen - 1, upperLen));
      }
    }
    return upper;
  }).replace(/(-[a-zA-Z])/g, function(text, upper) {
    return plugins_xeUtils_helperStringUpperCase.helperStringUpperCase(plugins_xeUtils_helperStringSubstring.helperStringSubstring(upper, 1, upper.length));
  });
  camelCacheMaps[str] = rest;
  return rest;
}
exports.camelCase = camelCase;
