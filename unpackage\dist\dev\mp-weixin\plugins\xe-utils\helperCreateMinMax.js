"use strict";
const plugins_xeUtils_isFunction = require("./isFunction.js");
const plugins_xeUtils_eqNull = require("./eqNull.js");
const plugins_xeUtils_get = require("./get.js");
const plugins_xeUtils_arrayEach = require("./arrayEach.js");
function helperCreateMinMax(handle) {
  return function(arr, iterate) {
    if (arr && arr.length) {
      var rest, itemIndex;
      plugins_xeUtils_arrayEach.arrayEach(arr, function(itemVal, index) {
        if (iterate) {
          itemVal = plugins_xeUtils_isFunction.isFunction(iterate) ? iterate(itemVal, index, arr) : plugins_xeUtils_get.get(itemVal, iterate);
        }
        if (!plugins_xeUtils_eqNull.eqNull(itemVal) && (plugins_xeUtils_eqNull.eqNull(rest) || handle(rest, itemVal))) {
          itemIndex = index;
          rest = itemVal;
        }
      });
      return arr[itemIndex];
    }
    return rest;
  };
}
exports.helperCreateMinMax = helperCreateMinMax;
