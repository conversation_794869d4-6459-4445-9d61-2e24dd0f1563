"use strict";
const plugins_xeUtils_keys = require("./keys.js");
function reduce(array, callback, initialValue) {
  if (array) {
    var len, reduceMethod;
    var index = 0;
    var context = null;
    var previous = initialValue;
    var isInitialVal = arguments.length > 2;
    var keyList = plugins_xeUtils_keys.keys(array);
    if (array.length && array.reduce) {
      reduceMethod = function() {
        return callback.apply(context, arguments);
      };
      if (isInitialVal) {
        return array.reduce(reduceMethod, previous);
      }
      return array.reduce(reduceMethod);
    }
    if (isInitialVal) {
      index = 1;
      previous = array[keyList[0]];
    }
    for (len = keyList.length; index < len; index++) {
      previous = callback.call(context, previous, array[keyList[index]], index, array);
    }
    return previous;
  }
}
exports.reduce = reduce;
