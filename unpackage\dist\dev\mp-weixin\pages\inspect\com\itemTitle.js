"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  name: "ItemTitle",
  props: {
    title: String,
    showIcon: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    click() {
      if (!this.showIcon)
        return;
      this.$emit("click");
    }
  }
};
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  _easycom_uni_icons2();
}
const _easycom_uni_icons = () => "../../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
if (!Math) {
  _easycom_uni_icons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($props.title),
    b: $props.showIcon
  }, $props.showIcon ? {
    c: common_vendor.p({
      type: "help",
      color: "#999"
    })
  } : {}, {
    d: common_vendor.o((...args) => $options.click && $options.click(...args))
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-19fbd750"]]);
wx.createComponent(Component);
