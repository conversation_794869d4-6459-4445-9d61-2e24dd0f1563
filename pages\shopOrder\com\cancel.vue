<template>
  <!-- 状态 -->
  <view class="status">
    <block v-if="refundAuditStatus == 0">
      <view class="h2">
        <image class="icon" src="/static/pre/1.png" />
        正在取消
      </view>

      <view class="info">
        您的订单正在取消审核中，请耐心等待
      </view>
    </block>

    <block v-if="refundAuditStatus == 1">
      <view class="h2">
        <image class="icon" src="/static/inspection/x.png" />
        取消驳回
      </view>

      <view class="info">
        您的订单退款申请被驳回
      </view>
    </block>
  </view>
</template>

<script>
export default {
  name: 'Status',
  props: {
    // 申请退费状态
    refundAuditStatus: String | Number,
  },
};
</script>

<style lang="scss" scoped>
.status {
  height: 216rpx;
  background: #ff5050;
  padding: 32rpx;
  color: #fff;
  box-sizing: border-box;

  .h2 {
    font-size: 48rpx;
    font-weight: bold;
    @include flex(left);

    .icon {
      width: 44rpx;
      height: 44rpx;
      margin-right: 16rpx;
    }
  }

  .info {
    font-size: 24rpx;
    margin-top: 24rpx;

    text {
      font-size: 36rpx;
      font-weight: bold;
      padding: 0 8rpx;
    }
  }
}
</style>
