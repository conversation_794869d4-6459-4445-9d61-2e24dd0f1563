"use strict";
const plugins_xeUtils_helperGetDateTime = require("./helperGetDateTime.js");
const plugins_xeUtils_helperNewDate = require("./helperNewDate.js");
const plugins_xeUtils_toStringDate = require("./toStringDate.js");
const plugins_xeUtils_isValidDate = require("./isValidDate.js");
var dateDiffRules = [
  ["yyyy", 31536e6],
  ["MM", 2592e6],
  ["dd", 864e5],
  ["HH", 36e5],
  ["mm", 6e4],
  ["ss", 1e3],
  ["S", 0]
];
function getDateDiff(startDate, endDate) {
  var startTime, endTime, item, diffTime, len, index;
  var result = { done: false, time: 0 };
  startDate = plugins_xeUtils_toStringDate.toStringDate(startDate);
  endDate = endDate ? plugins_xeUtils_toStringDate.toStringDate(endDate) : plugins_xeUtils_helperNewDate.helperNewDate();
  if (plugins_xeUtils_isValidDate.isValidDate(startDate) && plugins_xeUtils_isValidDate.isValidDate(endDate)) {
    startTime = plugins_xeUtils_helperGetDateTime.helperGetDateTime(startDate);
    endTime = plugins_xeUtils_helperGetDateTime.helperGetDateTime(endDate);
    if (startTime < endTime) {
      diffTime = result.time = endTime - startTime;
      result.done = true;
      for (index = 0, len = dateDiffRules.length; index < len; index++) {
        item = dateDiffRules[index];
        if (diffTime >= item[1]) {
          if (index === len - 1) {
            result[item[0]] = diffTime || 0;
          } else {
            result[item[0]] = Math.floor(diffTime / item[1]);
            diffTime -= result[item[0]] * item[1];
          }
        } else {
          result[item[0]] = 0;
        }
      }
    }
  }
  return result;
}
exports.getDateDiff = getDateDiff;
