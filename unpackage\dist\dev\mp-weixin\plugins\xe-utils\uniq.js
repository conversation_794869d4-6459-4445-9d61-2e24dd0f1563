"use strict";
const plugins_xeUtils_each = require("./each.js");
const plugins_xeUtils_includes = require("./includes.js");
const plugins_xeUtils_isFunction = require("./isFunction.js");
const plugins_xeUtils_property = require("./property.js");
function uniq(array, iterate, context) {
  var result = [];
  if (iterate) {
    if (!plugins_xeUtils_isFunction.isFunction(iterate)) {
      iterate = plugins_xeUtils_property.property(iterate);
    }
    var val, valMap = {};
    plugins_xeUtils_each.each(array, function(item, key) {
      val = iterate.call(context, item, key, array);
      if (!valMap[val]) {
        valMap[val] = 1;
        result.push(item);
      }
    });
  } else {
    plugins_xeUtils_each.each(array, function(value) {
      if (!plugins_xeUtils_includes.includes(result, value)) {
        result.push(value);
      }
    });
  }
  return result;
}
exports.uniq = uniq;
