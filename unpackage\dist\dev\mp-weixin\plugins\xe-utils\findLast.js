"use strict";
const plugins_xeUtils_isArray = require("./isArray.js");
const plugins_xeUtils_values = require("./values.js");
function findLast(obj, iterate, context) {
  if (obj) {
    if (!plugins_xeUtils_isArray.isArray(obj)) {
      obj = plugins_xeUtils_values.values(obj);
    }
    for (var len = obj.length - 1; len >= 0; len--) {
      if (iterate.call(context, obj[len], len, obj)) {
        return obj[len];
      }
    }
  }
}
exports.findLast = findLast;
