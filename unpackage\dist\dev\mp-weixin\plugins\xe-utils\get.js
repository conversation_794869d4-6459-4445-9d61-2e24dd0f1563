"use strict";
const plugins_xeUtils_staticHGKeyRE = require("./staticHGKeyRE.js");
const plugins_xeUtils_helperGetHGSKeys = require("./helperGetHGSKeys.js");
const plugins_xeUtils_hasOwnProp = require("./hasOwnProp.js");
const plugins_xeUtils_isUndefined = require("./isUndefined.js");
const plugins_xeUtils_eqNull = require("./eqNull.js");
function get(obj, property, defaultValue) {
  if (plugins_xeUtils_eqNull.eqNull(obj)) {
    return defaultValue;
  }
  var result = getValueByPath(obj, property);
  return plugins_xeUtils_isUndefined.isUndefined(result) ? defaultValue : result;
}
function getDeepProps(obj, key) {
  var matchs = key ? key.match(plugins_xeUtils_staticHGKeyRE.staticHGKeyRE) : "";
  return matchs ? matchs[1] ? obj[matchs[1]] ? obj[matchs[1]][matchs[2]] : void 0 : obj[matchs[2]] : obj[key];
}
function getValueByPath(obj, property) {
  if (obj) {
    var rest, props, len;
    var index = 0;
    if (obj[property] || plugins_xeUtils_hasOwnProp.hasOwnProp(obj, property)) {
      return obj[property];
    } else {
      props = plugins_xeUtils_helperGetHGSKeys.helperGetHGSKeys(property);
      len = props.length;
      if (len) {
        for (rest = obj; index < len; index++) {
          rest = getDeepProps(rest, props[index]);
          if (plugins_xeUtils_eqNull.eqNull(rest)) {
            if (index === len - 1) {
              return rest;
            }
            return;
          }
        }
      }
      return rest;
    }
  }
}
exports.get = get;
