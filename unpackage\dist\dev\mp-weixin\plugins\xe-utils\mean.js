"use strict";
const plugins_xeUtils_helperNumberDivide = require("./helperNumberDivide.js");
const plugins_xeUtils_getSize = require("./getSize.js");
const plugins_xeUtils_sum = require("./sum.js");
function mean(array, iterate, context) {
  return plugins_xeUtils_helperNumberDivide.helperNumberDivide(plugins_xeUtils_sum.sum(array, iterate, context), plugins_xeUtils_getSize.getSize(array));
}
exports.mean = mean;
