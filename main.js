import App from './App'
import './interceptor'
import { createSSRApp } from 'vue'
import store from './store'
import XEUtils from '@/plugins/xe-utils'
import myJsTools from './common/js/myJsTools.js'
import UniImage from '@/components/common/UniImage.vue'
//解决控制台 touchstart 事件警告
import 'default-passive-events'
export function createApp() {
	const app = createSSRApp(App)
	app.use(store)
	app.config.globalProperties.$xeu = XEUtils
	uni.$xeu = XEUtils

	// 全局注册 UniImage 组件
	app.component('UniImage', UniImage)
	// 自定义指令 异步获取图片
	app.directive('img', {
  bind(el, binding) {
    console.log(binding.value)
    if (!binding.value || binding.value == 'null') return
    myJsTools.downAndSaveImg(
      binding.value,
      (url) => {
        if (!url) {
          el.src = el.dataset.src
          return
        }
        el.src = url
        // 如果存在参数 添加点击事件预览
        if (binding.arg == 'click')
          el.onclick = (e) => {
            // 阻止事件继续传播
            e.stopPropagation()
            myJsTools.previewImg(el.src)
          }
      },
      () => {
        let src = el.dataset.src
        if (src) el.src = src
      }
    )
  },
})
	return {
		app
	}
}