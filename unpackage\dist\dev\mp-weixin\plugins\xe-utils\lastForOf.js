"use strict";
const plugins_xeUtils_isArray = require("./isArray.js");
const plugins_xeUtils_hasOwnProp = require("./hasOwnProp.js");
function lastForOf(obj, iterate, context) {
  if (obj) {
    var len, list;
    if (plugins_xeUtils_isArray.isArray(obj)) {
      for (len = obj.length - 1; len >= 0; len--) {
        if (iterate.call(context, obj[len], len, obj) === false) {
          break;
        }
      }
    } else {
      list = plugins_xeUtils_hasOwnProp.hasOwnProp(obj);
      for (len = list.length - 1; len >= 0; len--) {
        if (iterate.call(context, obj[list[len]], list[len], obj) === false) {
          break;
        }
      }
    }
  }
}
exports.lastForOf = lastForOf;
