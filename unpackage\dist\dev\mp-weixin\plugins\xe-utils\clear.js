"use strict";
const plugins_xeUtils_helperDeleteProperty = require("./helperDeleteProperty.js");
const plugins_xeUtils_isPlainObject = require("./isPlainObject.js");
const plugins_xeUtils_isObject = require("./isObject.js");
const plugins_xeUtils_isArray = require("./isArray.js");
const plugins_xeUtils_isNull = require("./isNull.js");
const plugins_xeUtils_assign = require("./assign.js");
const plugins_xeUtils_objectEach = require("./objectEach.js");
function clear(obj, defs, assigns) {
  if (obj) {
    var len;
    var isDefs = arguments.length > 1 && (plugins_xeUtils_isNull.isNull(defs) || !plugins_xeUtils_isObject.isObject(defs));
    var extds = isDefs ? assigns : defs;
    if (plugins_xeUtils_isPlainObject.isPlainObject(obj)) {
      plugins_xeUtils_objectEach.objectEach(obj, isDefs ? function(val, key) {
        obj[key] = defs;
      } : function(val, key) {
        plugins_xeUtils_helperDeleteProperty.helperDeleteProperty(obj, key);
      });
      if (extds) {
        plugins_xeUtils_assign.assign(obj, extds);
      }
    } else if (plugins_xeUtils_isArray.isArray(obj)) {
      if (isDefs) {
        len = obj.length;
        while (len > 0) {
          len--;
          obj[len] = defs;
        }
      } else {
        obj.length = 0;
      }
      if (extds) {
        obj.push.apply(obj, extds);
      }
    }
  }
  return obj;
}
exports.clear = clear;
