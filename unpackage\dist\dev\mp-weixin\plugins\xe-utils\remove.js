"use strict";
const plugins_xeUtils_helperDeleteProperty = require("./helperDeleteProperty.js");
const plugins_xeUtils_isFunction = require("./isFunction.js");
const plugins_xeUtils_isArray = require("./isArray.js");
const plugins_xeUtils_each = require("./each.js");
const plugins_xeUtils_arrayEach = require("./arrayEach.js");
const plugins_xeUtils_lastEach = require("./lastEach.js");
const plugins_xeUtils_clear = require("./clear.js");
const plugins_xeUtils_eqNull = require("./eqNull.js");
function pluckProperty(name) {
  return function(obj, key) {
    return key === name;
  };
}
function remove(obj, iterate, context) {
  if (obj) {
    if (!plugins_xeUtils_eqNull.eqNull(iterate)) {
      var removeKeys = [];
      var rest = [];
      if (!plugins_xeUtils_isFunction.isFunction(iterate)) {
        iterate = pluckProperty(iterate);
      }
      plugins_xeUtils_each.each(obj, function(item, index, rest2) {
        if (iterate.call(context, item, index, rest2)) {
          removeKeys.push(index);
        }
      });
      if (plugins_xeUtils_isArray.isArray(obj)) {
        plugins_xeUtils_lastEach.lastEach(removeKeys, function(item, key) {
          rest.push(obj[item]);
          obj.splice(item, 1);
        });
      } else {
        rest = {};
        plugins_xeUtils_arrayEach.arrayEach(removeKeys, function(key) {
          rest[key] = obj[key];
          plugins_xeUtils_helperDeleteProperty.helperDeleteProperty(obj, key);
        });
      }
      return rest;
    }
    return plugins_xeUtils_clear.clear(obj);
  }
  return obj;
}
exports.remove = remove;
