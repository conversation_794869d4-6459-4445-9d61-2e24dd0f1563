import { Toast } from '@/common/js/pay.js'
import { Base64 } from 'js-base64'
import md5 from 'js-md5'
import store from '@/store'
let baseUrl = store.getters.allEnv[store.getters.envKey].baseUrl
const http = ({ url = '', param = {}, ...other } = {}) => {
  // console.log(param)
  console.log(param, url, '===========接口参数')
  return new Promise((resolve, reject) => {
    if (other.method == 'post' && !url.includes('api/') && other.isNeedToStringParam !== false) {
      param = reSetData(param)
    }
    uni.request({
      url: getUrl(url),
      data: param,
      timeout: 300000,
      header: {
        Authorization: uni.getStorageSync('proPfInfo').token || '',
        hosId: uni.getStorageSync('hosId') || '',
        firmId: store.getters.allEnv[store.getters.envKey].firmId,
        version: store.getters.allEnv[store.getters.envKey].version,
        clientType: store.getters.allEnv[store.getters.envKey].clientType
      },
      ...other,
      success: (res) => {
        uni.hideLoading()
        console.log(res, '接口返回结果')
        if (url.includes('api/')) {
          resolve(res)
          return
        }
        let arr = [20000, 20011, 20014, 20015, 20019, 20020]
        if (res.data && (res.data.message || '').includes('存在该医生下的快速续方')) {
          uni.showToast({
            icon: 'none',
            title: res.data.message,
            duration: 2000
          })
          setTimeout(() => {
            uni.navigateBack({
              delta: 1
            })
          }, 1000)
          reject(res)
          return
        }
        if (res.statusCode >= 200 && res.statusCode < 300) {
          console.log('返回')
          if (arr.includes(res.data.code)) {
            resolve(res.data)
          } else {
            Toast(res.data.message)
            reject(res)
          }
        } else {
          reject(res)
        }
      },
      fail: (err) => {
        uni.hideLoading()
        Toast(err.errMsg)
        reject(err)
      }
    })
  })
}
const getUrl = (url) => {
  if (url.includes('api/')) {
    return url
  }
  if (url.indexOf('://') == -1) {
    url = baseUrl + url
  }
  return url
}
const reSetData = (requestData) => {
  let timestamp = new Date().getTime() + ''
  let nonce = guid()
  let token_info = uni.getStorageSync('proPfInfo').token

  let base64Data = ''
  if (requestData) {
    // console.log(requestData)
    base64Data = Base64.encode(JSON.stringify(requestData))
  } else {
    let map = {}
    base64Data = Base64.encode(JSON.stringify(map))
  }
  // let data = {
  //   data: base64Data,
  // };
  let data = {
    data: JSON.stringify(requestData)
  }
  data.timestamp = timestamp
  data.nonce = nonce
  if (token_info) {
    data.token = token_info
  }
  let s = ''
  Object.keys(data)
    .sort()
    .forEach((k) => {
      if (data[k] && data[k].length > 0) {
        if (s.length > 0) {
          s += '&'
        }
        s += k + '=' + data[k]
      }
    })
  let md5Data = md5(s).toLocaleUpperCase()
  data.sign = md5Data
  // console.log(data)
  return data
}

const guid = () => {
  return S4() + S4() + '-' + S4() + '-' + S4() + '-' + S4() + '-' + S4() + S4() + S4()
}

const S4 = () => {
  return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1)
}
export default http
