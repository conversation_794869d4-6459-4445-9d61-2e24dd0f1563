!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.websdk=t():e.websdk=t()}(self,(function(){return function(){var e,t={34:function(e,t,n){var r=n(4901);e.exports=function(e){return"object"==typeof e?null!==e:r(e)}},75:function(e,t,n){"use strict";n.d(t,{Jz:function(){return r},KU:function(){return i},Po:function(){return u},XI:function(){return d},Xp:function(){return s},ey:function(){return a},lM:function(){return l},nf:function(){return o},uN:function(){return c}}),n(3792),n(6033),n(6099),n(7764),n(2953);var r=7,o=new Map,i="im",s=new Map,a="32f24ab2ddb74f508aa9286c356cec84",u=1e3,c={INIT:9674,API:9675},l="direct",d=-1},81:function(e,t,n){"use strict";var r=n(9565),o=n(9306),i=n(8551),s=n(6823),a=n(851),u=TypeError;e.exports=function(e,t){var n=arguments.length<2?a(e):t;if(o(n))return i(r(n,e));throw new u(s(e)+" is not iterable")}},113:function(e,t,n){"use strict";var r=n(6518),o=n(9213).find,i=n(6469),s="find",a=!0;s in[]&&Array(1)[s]((function(){a=!1})),r({target:"Array",proto:!0,forced:a},{find:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}}),i(s)},150:function(e,t,n){"use strict";n(6518)({target:"Number",stat:!0},{isNaN:function(e){return e!=e}})},235:function(e,t,n){"use strict";var r=n(9213).forEach,o=n(4598)("forEach");e.exports=o?[].forEach:function(e){return r(this,e,arguments.length>1?arguments[1]:void 0)}},280:function(e,t,n){"use strict";var r=n(6518),o=n(7751),i=n(6395),s=n(550),a=n(916).CONSTRUCTOR,u=n(3438),c=o("Promise"),l=i&&!a;r({target:"Promise",stat:!0,forced:i||a},{resolve:function(e){return u(l&&this===c?s:this,e)}})},283:function(e,t,n){"use strict";var r=n(9504),o=n(9039),i=n(4901),s=n(9297),a=n(3724),u=n(350).CONFIGURABLE,c=n(3706),l=n(1181),d=l.enforce,p=l.get,h=String,f=Object.defineProperty,v=r("".slice),E=r("".replace),m=r([].join),g=a&&!o((function(){return 8!==f((function(){}),"length",{value:8}).length})),y=String(String).split("String"),_=e.exports=function(e,t,n){"Symbol("===v(h(t),0,7)&&(t="["+E(h(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),n&&n.getter&&(t="get "+t),n&&n.setter&&(t="set "+t),(!s(e,"name")||u&&e.name!==t)&&(a?f(e,"name",{value:t,configurable:!0}):e.name=t),g&&n&&s(n,"arity")&&e.length!==n.arity&&f(e,"length",{value:n.arity});try{n&&s(n,"constructor")&&n.constructor?a&&f(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}var r=d(e);return s(r,"source")||(r.source=m(y,"string"==typeof t?t:"")),e};Function.prototype.toString=_((function(){return i(this)&&p(this).source||c(this)}),"toString")},298:function(e,t,n){"use strict";var r=n(2195),o=n(5397),i=n(8480).f,s=n(7680),a="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];e.exports.f=function(e){return a&&"Window"===r(e)?function(e){try{return i(e)}catch(e){return s(a)}}(e):i(o(e))}},346:function(e,t){"use strict";var n=function(){function e(e){this.type=e.type,this.message=e.message,this.data=e.data}return e.create=function(t){return new e(t)},e}();t.A=n},350:function(e,t,n){"use strict";var r=n(3724),o=n(9297),i=Function.prototype,s=r&&Object.getOwnPropertyDescriptor,a=o(i,"name"),u=a&&"something"===function(){}.name,c=a&&(!r||r&&s(i,"name").configurable);e.exports={EXISTS:a,PROPER:u,CONFIGURABLE:c}},361:function(e,t,n){"use strict";var r=t,o=n(3262),i=["double","float","int32","uint32","sint32","fixed32","sfixed32","int64","uint64","sint64","fixed64","sfixed64","bool","string","bytes"];function s(e,t){var n=0,r={};for(t|=0;n<e.length;)r[i[n+t]]=e[n++];return r}r.basic=s([1,5,0,0,0,5,5,0,0,0,1,1,0,2,2]),r.defaults=s([0,0,0,0,0,0,0,0,0,0,0,0,!1,"",o.emptyArray,null]),r.long=s([0,0,0,1,1],7),r.mapKey=s([0,0,0,5,5,0,0,0,1,1,0,2],2),r.packed=s([1,5,0,0,0,5,5,0,0,0,1,1,0])},373:function(e,t,n){"use strict";var r=n(4576),o=n(7476),i=n(9039),s=n(9306),a=n(4488),u=n(4644),c=n(3709),l=n(3763),d=n(9519),p=n(3607),h=u.aTypedArray,f=u.exportTypedArrayMethod,v=r.Uint16Array,E=v&&o(v.prototype.sort),m=!(!E||i((function(){E(new v(2),null)}))&&i((function(){E(new v(2),{})}))),g=!!E&&!i((function(){if(d)return d<74;if(c)return c<67;if(l)return!0;if(p)return p<602;var e,t,n=new v(516),r=Array(516);for(e=0;e<516;e++)t=e%4,n[e]=515-e,r[e]=e-2*t+3;for(E(n,(function(e,t){return(e/4|0)-(t/4|0)})),e=0;e<516;e++)if(n[e]!==r[e])return!0}));f("sort",(function(e){return void 0!==e&&s(e),g?E(this,e):a(h(this),function(e){return function(t,n){return void 0!==e?+e(t,n)||0:n!=n?-1:t!=t?1:0===t&&0===n?1/t>0&&1/n<0?1:-1:t>n}}(e))}),!g||m)},397:function(e,t,n){"use strict";var r=n(7751);e.exports=r("document","documentElement")},420:function(e,t,n){"use strict";e.exports=function(e){var t=o.codegen(["m"],e.name+"$verify")('if(typeof m!=="object"||m===null)')("return%j","object expected"),n={};e.oneofsArray.length&&t("var p={}");for(var r=0;r<e.fieldsArray.length;++r){var u=e._fieldsArray[r].resolve(),c="m"+o.safeProp(u.name);if(u.optional&&t("if(%s!=null&&m.hasOwnProperty(%j)){",c,u.name),u.map)t("if(!util.isObject(%s))",c)("return%j",i(u,"object"))("var k=Object.keys(%s)",c)("for(var i=0;i<k.length;++i){"),a(t,u,"k[i]"),s(t,u,r,c+"[k[i]]")("}");else if(u.repeated)t("if(!Array.isArray(%s))",c)("return%j",i(u,"array"))("for(var i=0;i<%s.length;++i){",c),s(t,u,r,c+"[i]")("}");else{if(u.partOf){var l=o.safeProp(u.partOf.name);1===n[u.partOf.name]&&t("if(p%s===1)",l)("return%j",u.partOf.name+": multiple values"),n[u.partOf.name]=1,t("p%s=1",l)}s(t,u,r,c)}u.optional&&t("}")}return t("return null")};var r=n(5643),o=n(3262);function i(e,t){return e.name+": "+t+(e.repeated&&"array"!==t?"[]":e.map&&"object"!==t?"{k:"+e.keyType+"}":"")+" expected"}function s(e,t,n,o){if(t.resolvedType)if(t.resolvedType instanceof r){e("switch(%s){",o)("default:")("return%j",i(t,"enum value"));for(var s=Object.keys(t.resolvedType.values),a=0;a<s.length;++a)e("case %i:",t.resolvedType.values[s[a]]);e("break")("}")}else e("{")("var e=types[%i].verify(%s);",n,o)("if(e)")("return%j+e",t.name+".")("}");else switch(t.type){case"int32":case"uint32":case"sint32":case"fixed32":case"sfixed32":e("if(!util.isInteger(%s))",o)("return%j",i(t,"integer"));break;case"int64":case"uint64":case"sint64":case"fixed64":case"sfixed64":e("if(!util.isInteger(%s)&&!(%s&&util.isInteger(%s.low)&&util.isInteger(%s.high)))",o,o,o,o)("return%j",i(t,"integer|Long"));break;case"float":case"double":e('if(typeof %s!=="number")',o)("return%j",i(t,"number"));break;case"bool":e('if(typeof %s!=="boolean")',o)("return%j",i(t,"boolean"));break;case"string":e("if(!util.isString(%s))",o)("return%j",i(t,"string"));break;case"bytes":e('if(!(%s&&typeof %s.length==="number"||util.isString(%s)))',o,o,o)("return%j",i(t,"buffer"))}return e}function a(e,t,n){switch(t.keyType){case"int32":case"uint32":case"sint32":case"fixed32":case"sfixed32":e("if(!util.key32Re.test(%s))",n)("return%j",i(t,"integer key"));break;case"int64":case"uint64":case"sint64":case"fixed64":case"sfixed64":e("if(!util.key64Re.test(%s))",n)("return%j",i(t,"integer|Long key"));break;case"bool":e("if(!util.key2Re.test(%s))",n)("return%j",i(t,"boolean key"))}return e}},421:function(e){"use strict";e.exports={}},436:function(e,t,n){"use strict";var r,o,i,s=n(6518),a=n(6395),u=n(6193),c=n(4576),l=n(9565),d=n(6840),p=n(2967),h=n(687),f=n(7633),v=n(9306),E=n(4901),m=n(34),g=n(679),y=n(2293),_=n(9225).set,O=n(1955),T=n(3138),R=n(1103),I=n(8265),C=n(1181),S=n(550),N=n(916),A=n(6043),b="Promise",M=N.CONSTRUCTOR,w=N.REJECTION_EVENT,L=N.SUBCLASSING,U=C.getterFor(b),x=C.set,D=S&&S.prototype,P=S,k=D,H=c.TypeError,F=c.document,B=c.process,G=A.f,W=G,j=!!(F&&F.createEvent&&c.dispatchEvent),q="unhandledrejection",K=function(e){var t;return!(!m(e)||!E(t=e.then))&&t},V=function(e,t){var n,r,o,i=t.value,s=1===t.state,a=s?e.ok:e.fail,u=e.resolve,c=e.reject,d=e.domain;try{a?(s||(2===t.rejection&&Q(t),t.rejection=1),!0===a?n=i:(d&&d.enter(),n=a(i),d&&(d.exit(),o=!0)),n===e.promise?c(new H("Promise-chain cycle")):(r=K(n))?l(r,n,u,c):u(n)):c(i)}catch(e){d&&!o&&d.exit(),c(e)}},z=function(e,t){e.notified||(e.notified=!0,O((function(){for(var n,r=e.reactions;n=r.get();)V(n,e);e.notified=!1,t&&!e.rejection&&Y(e)})))},J=function(e,t,n){var r,o;j?((r=F.createEvent("Event")).promise=t,r.reason=n,r.initEvent(e,!1,!0),c.dispatchEvent(r)):r={promise:t,reason:n},!w&&(o=c["on"+e])?o(r):e===q&&T("Unhandled promise rejection",n)},Y=function(e){l(_,c,(function(){var t,n=e.facade,r=e.value;if(X(e)&&(t=R((function(){u?B.emit("unhandledRejection",r,n):J(q,n,r)})),e.rejection=u||X(e)?2:1,t.error))throw t.value}))},X=function(e){return 1!==e.rejection&&!e.parent},Q=function(e){l(_,c,(function(){var t=e.facade;u?B.emit("rejectionHandled",t):J("rejectionhandled",t,e.value)}))},$=function(e,t,n){return function(r){e(t,r,n)}},Z=function(e,t,n){e.done||(e.done=!0,n&&(e=n),e.value=t,e.state=2,z(e,!0))},ee=function(e,t,n){if(!e.done){e.done=!0,n&&(e=n);try{if(e.facade===t)throw new H("Promise can't be resolved itself");var r=K(t);r?O((function(){var n={done:!1};try{l(r,t,$(ee,n,e),$(Z,n,e))}catch(t){Z(n,t,e)}})):(e.value=t,e.state=1,z(e,!1))}catch(t){Z({done:!1},t,e)}}};if(M&&(k=(P=function(e){g(this,k),v(e),l(r,this);var t=U(this);try{e($(ee,t),$(Z,t))}catch(e){Z(t,e)}}).prototype,(r=function(e){x(this,{type:b,done:!1,notified:!1,parent:!1,reactions:new I,rejection:!1,state:0,value:null})}).prototype=d(k,"then",(function(e,t){var n=U(this),r=G(y(this,P));return n.parent=!0,r.ok=!E(e)||e,r.fail=E(t)&&t,r.domain=u?B.domain:void 0,0===n.state?n.reactions.add(r):O((function(){V(r,n)})),r.promise})),o=function(){var e=new r,t=U(e);this.promise=e,this.resolve=$(ee,t),this.reject=$(Z,t)},A.f=G=function(e){return e===P||void 0===e?new o(e):W(e)},!a&&E(S)&&D!==Object.prototype)){i=D.then,L||d(D,"then",(function(e,t){var n=this;return new P((function(e,t){l(i,n,e,t)})).then(e,t)}),{unsafe:!0});try{delete D.constructor}catch(e){}p&&p(D,k)}s({global:!0,constructor:!0,wrap:!0,forced:M},{Promise:P}),h(P,b,!1,!0),f(b)},511:function(e,t,n){"use strict";var r=n(9167),o=n(9297),i=n(1951),s=n(4913).f;e.exports=function(e){var t=r.Symbol||(r.Symbol={});o(t,e)||s(t,e,{value:i.f(e)})}},527:function(e){"use strict";e.exports=d;var t=/[\s{}=;:[\],'"()<>]/g,n=/(?:"([^"\\]*(?:\\.[^"\\]*)*)")/g,r=/(?:'([^'\\]*(?:\\.[^'\\]*)*)')/g,o=/^ *[*/]+ */,i=/^\s*\*?\/*/,s=/\n/g,a=/\s/,u=/\\(.?)/g,c={0:"\0",r:"\r",n:"\n",t:"\t"};function l(e){return e.replace(u,(function(e,t){switch(t){case"\\":case"":return t;default:return c[t]||""}}))}function d(e,u){e=e.toString();var c=0,d=e.length,p=1,h=null,f=null,v=0,E=!1,m=!1,g=[],y=null;function _(e){return Error("illegal "+e+" (line "+p+")")}function O(t){return e.charAt(t)}function T(t,n,r){h=e.charAt(t++),v=p,E=!1,m=r;var a,c=t-(u?2:3);do{if(--c<0||"\n"===(a=e.charAt(c))){E=!0;break}}while(" "===a||"\t"===a);for(var l=e.substring(t,n).split(s),d=0;d<l.length;++d)l[d]=l[d].replace(u?i:o,"").trim();f=l.join("\n").trim()}function R(t){var n=I(t),r=e.substring(t,n);return/^\s*\/{1,2}/.test(r)}function I(e){for(var t=e;t<d&&"\n"!==O(t);)t++;return t}function C(){if(g.length>0)return g.shift();if(y)return function(){var t="'"===y?r:n;t.lastIndex=c-1;var o=t.exec(e);if(!o)throw _("string");return c=t.lastIndex,S(y),y=null,l(o[1])}();var o,i,s,h,f,v=0===c;do{if(c===d)return null;for(o=!1;a.test(s=O(c));)if("\n"===s&&(v=!0,++p),++c===d)return null;if("/"===O(c)){if(++c===d)throw _("comment");if("/"===O(c))if(u){if(h=c,f=!1,R(c)){f=!0;do{if((c=I(c))===d)break;c++}while(R(c))}else c=Math.min(d,I(c)+1);f&&T(h,c,v),p++,o=!0}else{for(f="/"===O(h=c+1);"\n"!==O(++c);)if(c===d)return null;++c,f&&T(h,c-1,v),++p,o=!0}else{if("*"!==(s=O(c)))return"/";h=c+1,f=u||"*"===O(h);do{if("\n"===s&&++p,++c===d)throw _("comment");i=s,s=O(c)}while("*"!==i||"/"!==s);++c,f&&T(h,c-2,v),o=!0}}}while(o);var E=c;if(t.lastIndex=0,!t.test(O(E++)))for(;E<d&&!t.test(O(E));)++E;var m=e.substring(c,c=E);return'"'!==m&&"'"!==m||(y=m),m}function S(e){g.push(e)}function N(){if(!g.length){var e=C();if(null===e)return null;S(e)}return g[0]}return Object.defineProperty({next:C,peek:N,push:S,skip:function(e,t){var n=N();if(n===e)return C(),!0;if(!t)throw _("token '"+n+"', '"+e+"' expected");return!1},cmnt:function(e){var t=null;return void 0===e?v===p-1&&(u||"*"===h||E)&&(t=m?f:null):(v<e&&N(),v!==e||E||!u&&"/"!==h||(t=m?null:f)),t}},"line",{get:function(){return p}})}d.unescape=l},537:function(e,t,n){"use strict";var r=n(550),o=n(4428),i=n(916).CONSTRUCTOR;e.exports=i||!o((function(e){r.all(e).then(void 0,(function(){}))}))},550:function(e,t,n){"use strict";var r=n(4576);e.exports=r.Promise},564:function(e,t,n){"use strict";n.d(t,{rH:function(){return o},s5:function(){return i},u0:function(){return r}}),n(8706);var r=function(e){return"".concat(e.conversationType,"-").concat(e.conversationId)},o=function(e){var t=e.isRecallSelfMsg,n=e.conversation,r=e.recalledMsgTime,o=n.unReadCount,i=void 0===o?0:o,s=n.unreadCountClearTimestamp;return t||(void 0===s?0:s)>r?i:i&&i>0?i-1:0},i=function(e){var t,n=0;if(0===e.length)return n;for(t=0;t<e.length;t++)n=(n<<5)-n+e.charCodeAt(t),n|=0;return n}},565:function(e,t,n){"use strict";n.d(t,{h:function(){return u},p:function(){return a}}),n(5276),n(2062),n(9085),n(6099);var r=n(2056),o=n(8801),i=n(8678),s=function(){return s=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},s.apply(this,arguments)},a={chat:"singleChat",chatroom:"chatRoom",groupchat:"groupChat",singleChat:"singleChat",chatRoom:"chatRoom",groupChat:"groupChat"};function u(e,t){var n,u,c,l=t||{},d=l.formatCustomExts,p=void 0===d||d,h=l.formatChatType,f=void 0!==h&&h,v=e.id,E=e.payload,m=e.timestamp,g=e.to,y=E.bodies&&E.bodies.length>0?E.bodies[0]:{},_={},O={},T=E.type?E.type:g.indexOf("@conference.easemob.com")>-1?"groupChat":"singleChat";T="chat"===T?"singleChat":T,f&&E.type&&(T=a[E.type]);var R="";switch(y.type){case"txt":Object.prototype.hasOwnProperty.call(y,"subType")&&"sub_combine"===y.subType?(R=o.zK.call(this,{remotePath:null==y?void 0:y.url,secret:null==y?void 0:y.secret}),_={id:v,type:"combine",chatType:T,to:E.to,from:E.from,ext:E.ext,time:m,title:y.title||"",summary:y.summary||"",url:R||"",secret:y.secret||"",file_length:y.file_length||0,filename:y.filename||"",compatibleText:y.msg,combineLevel:y.combineLevel||0}):_={id:v,type:"txt",chatType:T,msg:y.msg||"",to:E.to||"",from:E.from,time:m,ext:E.ext};break;case"img":R=this.useOwnUploadFun?null==y?void 0:y.url:o.zK.call(this,{remotePath:null==y?void 0:y.url,secret:null==y?void 0:y.secret}),_={id:v,type:"img",chatType:T,to:E.to,from:E.from,time:m,ext:E.ext,width:(null===(n=y.size)||void 0===n?void 0:n.width)||0,height:(null===(u=y.size)||void 0===u?void 0:u.height)||0,thumb:this.useOwnUploadFun?"":"".concat(R,"&thumbnail=true"),thumb_secret:y.secret,secret:y.secret||"",url:R||"",file_length:y.file_length||0,file:{},isGif:"sub_gif"===y.subType};break;case"video":R=this.useOwnUploadFun?null==y?void 0:y.url:o.zK.call(this,{remotePath:null==y?void 0:y.url,secret:null==y?void 0:y.secret}),_={id:v,type:"video",chatType:T,from:E.from,to:E.to,thumb:i.Wp.formatAttachUrl.call(this,y.thumb),thumb_secret:y.thumb_secret||"",url:R||"",secret:y.secret||"",filename:y.filename,length:y.length||0,file:{},file_length:y.file_length||0,filetype:E.ext.file_type||"",ext:E.ext,time:m};break;case"loc":_={id:v,type:"loc",chatType:T,from:E.from,to:E.to,buildingName:y.buildingName||"",addr:y.addr,lat:y.lat,lng:y.lng,ext:E.ext,time:m};break;case"audio":R=this.useOwnUploadFun?null==y?void 0:y.url:o.zK.call(this,{remotePath:null==y?void 0:y.url,secret:null==y?void 0:y.secret}),_={id:v,type:"audio",chatType:T,from:E.from,to:E.to,secret:y.secret||"",ext:E.ext,time:m,url:R||"",file:{},filename:y.filename,length:y.length||0,file_length:y.file_length||0,filetype:E.ext.file_type||""};break;case"file":R=this.useOwnUploadFun?null==y?void 0:y.url:o.zK.call(this,{remotePath:null==y?void 0:y.url,secret:null==y?void 0:y.secret}),_={id:v,type:"file",chatType:T,from:E.from,to:E.to,ext:E.ext,time:m,url:R||"",secret:y.secret||"",file:{},filename:y.filename||"",file_length:y.file_length||0,filetype:E.ext.file_type||""};break;case"cmd":_={id:v,type:"cmd",chatType:T,from:E.from,to:E.to,ext:E.ext,time:m,action:y.action||""};break;case"custom":var I=y.customExts||{};p&&y.customExts&&(I={},y.customExts.map((function(e){I=s(s({},I),e)}))),_={id:v,type:"custom",chatType:T,from:E.from,to:E.to,ext:E.ext,time:m,customEvent:y.customEvent||"",customExts:I};break;case"combine":R=o.zK.call(this,{remotePath:null==y?void 0:y.url,secret:null==y?void 0:y.secret}),_={id:v,type:"combine",chatType:T,msg:y.msg||"",to:E.to||"",from:E.from,time:m,ext:E.ext,title:y.title||"",summary:y.summary||"",url:R||"",compatibleText:y.text,combineLevel:y.combineLevel||0,secret:y.secret||"",filename:y.filename||"",file_length:y.file_length||0};break;default:r.vF.error("unexpected message: ".concat(e))}if(E.msgConfig&&(O.msgConfig=E.msgConfig),null==E?void 0:E.meta){var C=E.meta;C.thread&&(O.chatThread={messageId:C.thread.msg_parent_id,parentId:C.thread.muc_parent_id,chatThreadName:C.thread.thread_name}),C.reaction&&(O.reactions=C.reaction),C.translations&&(O.translations=C.translations)}var S=null===(c=null==E?void 0:E.meta)||void 0===c?void 0:c.edit_msg;if(S){var N=S.edit_time,A=S.operator,b=S.count;_.modifiedInfo={operationTime:N,operatorId:A,operationCount:b}}return s(s({},_),O)}},566:function(e,t,n){"use strict";var r=n(9504),o=n(9306),i=n(34),s=n(9297),a=n(7680),u=n(616),c=Function,l=r([].concat),d=r([].join),p={};e.exports=u?c.bind:function(e){var t=o(this),n=t.prototype,r=a(arguments,1),u=function(){var n=l(r,a(arguments));return this instanceof u?function(e,t,n){if(!s(p,t)){for(var r=[],o=0;o<t;o++)r[o]="a["+o+"]";p[t]=c("C,a","return new C("+d(r,",")+")")}return p[t](e,n)}(t,n.length,n):t.apply(e,n)};return i(n)&&(u.prototype=n),u}},597:function(e,t,n){"use strict";var r=n(9039),o=n(8227),i=n(9519),s=o("species");e.exports=function(e){return i>=51||!r((function(){var t=[];return(t.constructor={})[s]=function(){return{foo:1}},1!==t[e](Boolean).foo}))}},616:function(e,t,n){"use strict";var r=n(9039);e.exports=!r((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},655:function(e,t,n){"use strict";var r=n(6955),o=String;e.exports=function(e){if("Symbol"===r(e))throw new TypeError("Cannot convert a Symbol value to a string");return o(e)}},679:function(e,t,n){"use strict";var r=n(1625),o=TypeError;e.exports=function(e,t){if(r(t,e))return e;throw new o("Incorrect invocation")}},687:function(e,t,n){"use strict";var r=n(4913).f,o=n(9297),i=n(8227)("toStringTag");e.exports=function(e,t,n){e&&!n&&(e=e.prototype),e&&!o(e,i)&&r(e,i,{configurable:!0,value:t})}},739:function(e,t,n){"use strict";var r=n(6518),o=n(9039),i=n(8981),s=n(2777);r({target:"Date",proto:!0,arity:1,forced:o((function(){return null!==new Date(NaN).toJSON()||1!==Date.prototype.toJSON.call({toISOString:function(){return 1}})}))},{toJSON:function(e){var t=i(this),n=s(t,"number");return"number"!=typeof n||isFinite(n)?t.toISOString():null}})},741:function(e){"use strict";var t=Math.ceil,n=Math.floor;e.exports=Math.trunc||function(e){var r=+e;return(r>0?n:t)(r)}},744:function(e,t,n){"use strict";var r=t,o=n(5643),i=n(3262);function s(e,t,n,r){if(t.resolvedType)if(t.resolvedType instanceof o){e("switch(d%s){",r);for(var i=t.resolvedType.values,s=Object.keys(i),a=0;a<s.length;++a)t.repeated&&i[s[a]]===t.typeDefault&&e("default:"),e("case%j:",s[a])("case %i:",i[s[a]])("m%s=%j",r,i[s[a]])("break");e("}")}else e('if(typeof d%s!=="object")',r)("throw TypeError(%j)",t.fullName+": object expected")("m%s=types[%i].fromObject(d%s)",r,n,r);else{var u=!1;switch(t.type){case"double":case"float":e("m%s=Number(d%s)",r,r);break;case"uint32":case"fixed32":e("m%s=d%s>>>0",r,r);break;case"int32":case"sint32":case"sfixed32":e("m%s=d%s|0",r,r);break;case"uint64":u=!0;case"int64":case"sint64":case"fixed64":case"sfixed64":e("if(util.Long)")("(m%s=util.Long.fromValue(d%s)).unsigned=%j",r,r,u)('else if(typeof d%s==="string")',r)("m%s=parseInt(d%s,10)",r,r)('else if(typeof d%s==="number")',r)("m%s=d%s",r,r)('else if(typeof d%s==="object")',r)("m%s=new util.LongBits(d%s.low>>>0,d%s.high>>>0).toNumber(%s)",r,r,r,u?"true":"");break;case"bytes":e('if(typeof d%s==="string")',r)("util.base64.decode(d%s,m%s=util.newBuffer(util.base64.length(d%s)),0)",r,r,r)("else if(d%s.length)",r)("m%s=d%s",r,r);break;case"string":e("m%s=String(d%s)",r,r);break;case"bool":e("m%s=Boolean(d%s)",r,r)}}return e}function a(e,t,n,r){if(t.resolvedType)t.resolvedType instanceof o?e("d%s=o.enums===String?types[%i].values[m%s]:m%s",r,n,r,r):e("d%s=types[%i].toObject(m%s,o)",r,n,r);else{var i=!1;switch(t.type){case"double":case"float":e("d%s=o.json&&!isFinite(m%s)?String(m%s):m%s",r,r,r,r);break;case"uint64":i=!0;case"int64":case"sint64":case"fixed64":case"sfixed64":e('if(typeof m%s==="number")',r)("d%s=o.longs===String?String(m%s):m%s",r,r,r)("else")("d%s=o.longs===String?util.Long.prototype.toString.call(m%s):o.longs===Number?new util.LongBits(m%s.low>>>0,m%s.high>>>0).toNumber(%s):m%s",r,r,r,r,i?"true":"",r);break;case"bytes":e("d%s=o.bytes===String?util.base64.encode(m%s,0,m%s.length):o.bytes===Array?Array.prototype.slice.call(m%s):m%s",r,r,r,r,r);break;default:e("d%s=m%s",r,r)}}return e}r.fromObject=function(e){var t=e.fieldsArray,n=i.codegen(["d"],e.name+"$fromObject")("if(d instanceof this.ctor)")("return d");if(!t.length)return n("return new this.ctor");n("var m=new this.ctor");for(var r=0;r<t.length;++r){var a=t[r].resolve(),u=i.safeProp(a.name);a.map?(n("if(d%s){",u)('if(typeof d%s!=="object")',u)("throw TypeError(%j)",a.fullName+": object expected")("m%s={}",u)("for(var ks=Object.keys(d%s),i=0;i<ks.length;++i){",u),s(n,a,r,u+"[ks[i]]")("}")("}")):a.repeated?(n("if(d%s){",u)("if(!Array.isArray(d%s))",u)("throw TypeError(%j)",a.fullName+": array expected")("m%s=[]",u)("for(var i=0;i<d%s.length;++i){",u),s(n,a,r,u+"[i]")("}")("}")):(a.resolvedType instanceof o||n("if(d%s!=null){",u),s(n,a,r,u),a.resolvedType instanceof o||n("}"))}return n("return m")},r.toObject=function(e){var t=e.fieldsArray.slice().sort(i.compareFieldsById);if(!t.length)return i.codegen()("return {}");for(var n=i.codegen(["m","o"],e.name+"$toObject")("if(!o)")("o={}")("var d={}"),r=[],s=[],u=[],c=0;c<t.length;++c)t[c].partOf||(t[c].resolve().repeated?r:t[c].map?s:u).push(t[c]);if(r.length){for(n("if(o.arrays||o.defaults){"),c=0;c<r.length;++c)n("d%s=[]",i.safeProp(r[c].name));n("}")}if(s.length){for(n("if(o.objects||o.defaults){"),c=0;c<s.length;++c)n("d%s={}",i.safeProp(s[c].name));n("}")}if(u.length){for(n("if(o.defaults){"),c=0;c<u.length;++c){var l=u[c],d=i.safeProp(l.name);if(l.resolvedType instanceof o)n("d%s=o.enums===String?%j:%j",d,l.resolvedType.valuesById[l.typeDefault],l.typeDefault);else if(l.long)n("if(util.Long){")("var n=new util.Long(%i,%i,%j)",l.typeDefault.low,l.typeDefault.high,l.typeDefault.unsigned)("d%s=o.longs===String?n.toString():o.longs===Number?n.toNumber():n",d)("}else")("d%s=o.longs===String?%j:%i",d,l.typeDefault.toString(),l.typeDefault.toNumber());else if(l.bytes){var p="["+Array.prototype.slice.call(l.typeDefault).join(",")+"]";n("if(o.bytes===String)d%s=%j",d,String.fromCharCode.apply(String,l.typeDefault))("else{")("d%s=%s",d,p)("if(o.bytes!==Array)d%s=util.newBuffer(d%s)",d,d)("}")}else n("d%s=%j",d,l.typeDefault)}n("}")}var h=!1;for(c=0;c<t.length;++c){l=t[c];var f=e._fieldsArray.indexOf(l);d=i.safeProp(l.name),l.map?(h||(h=!0,n("var ks2")),n("if(m%s&&(ks2=Object.keys(m%s)).length){",d,d)("d%s={}",d)("for(var j=0;j<ks2.length;++j){"),a(n,l,f,d+"[ks2[j]]")("}")):l.repeated?(n("if(m%s&&m%s.length){",d,d)("d%s=[]",d)("for(var j=0;j<m%s.length;++j){",d),a(n,l,f,d+"[j]")("}")):(n("if(m%s!=null&&m.hasOwnProperty(%j)){",d,l.name),a(n,l,f,d),l.partOf&&n("if(o.oneofs)")("d%s=%j",i.safeProp(l.partOf.name),l.name)),n("}")}return n("return d")}},757:function(e,t,n){"use strict";var r=n(7751),o=n(4901),i=n(1625),s=n(7040),a=Object;e.exports=s?function(e){return"symbol"==typeof e}:function(e){var t=r("Symbol");return o(t)&&i(t.prototype,a(e))}},788:function(e,t,n){"use strict";var r=n(34),o=n(2195),i=n(8227)("match");e.exports=function(e){var t;return r(e)&&(void 0!==(t=e[i])?!!t:"RegExp"===o(e))}},818:function(e,t,n){"use strict";e.exports=i;var r=n(3449);(i.prototype=Object.create(r.prototype)).constructor=i;var o=n(3610);function i(){r.call(this)}function s(e,t,n){e.length<40?o.utf8.write(e,t,n):t.utf8Write?t.utf8Write(e,n):t.write(e,n)}i._configure=function(){i.alloc=o._Buffer_allocUnsafe,i.writeBytesBuffer=o.Buffer&&o.Buffer.prototype instanceof Uint8Array&&"set"===o.Buffer.prototype.set.name?function(e,t,n){t.set(e,n)}:function(e,t,n){if(e.copy)e.copy(t,n,0,e.length);else for(var r=0;r<e.length;)t[n++]=e[r++]}},i.prototype.bytes=function(e){o.isString(e)&&(e=o._Buffer_from(e,"base64"));var t=e.length>>>0;return this.uint32(t),t&&this._push(i.writeBytesBuffer,t,e),this},i.prototype.string=function(e){var t=o.Buffer.byteLength(e);return this.uint32(t),t&&this._push(s,t,e),this},i._configure()},851:function(e,t,n){"use strict";var r=n(6955),o=n(5966),i=n(4117),s=n(6269),a=n(8227)("iterator");e.exports=function(e){if(!i(e))return o(e,a)||o(e,"@@iterator")||s[r(e)]}},876:function(e){function t(e){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=function(){return[]},t.resolve=t,t.id=876,e.exports=t},916:function(e,t,n){"use strict";var r=n(4576),o=n(550),i=n(4901),s=n(2796),a=n(3706),u=n(8227),c=n(4215),l=n(6395),d=n(9519),p=o&&o.prototype,h=u("species"),f=!1,v=i(r.PromiseRejectionEvent),E=s("Promise",(function(){var e=a(o),t=e!==String(o);if(!t&&66===d)return!0;if(l&&(!p.catch||!p.finally))return!0;if(!d||d<51||!/native code/.test(e)){var n=new o((function(e){e(1)})),r=function(e){e((function(){}),(function(){}))};if((n.constructor={})[h]=r,!(f=n.then((function(){}))instanceof r))return!0}return!(t||"BROWSER"!==c&&"DENO"!==c||v)}));e.exports={CONSTRUCTOR:E,REJECTION_EVENT:v,SUBCLASSING:f}},926:function(e,t,n){"use strict";var r=n(9306),o=n(8981),i=n(7055),s=n(6198),a=TypeError,u="Reduce of empty array with no initial value",c=function(e){return function(t,n,c,l){var d=o(t),p=i(d),h=s(d);if(r(n),0===h&&c<2)throw new a(u);var f=e?h-1:0,v=e?-1:1;if(c<2)for(;;){if(f in p){l=p[f],f+=v;break}if(f+=v,e?f<0:h<=f)throw new a(u)}for(;e?f>=0:h>f;f+=v)f in p&&(l=n(l,p[f],f,d));return l}};e.exports={left:c(!1),right:c(!0)}},1034:function(e,t,n){"use strict";var r=n(9565),o=n(9297),i=n(1625),s=n(7979),a=RegExp.prototype;e.exports=function(e){var t=e.flags;return void 0!==t||"flags"in a||o(e,"flags")||!i(a,e)?t:r(s,e)}},1072:function(e,t,n){"use strict";var r=n(1828),o=n(8727);e.exports=Object.keys||function(e){return r(e,o)}},1080:function(e,t,n){"use strict";e.exports=function(e){for(var t,n=i.codegen(["m","w"],e.name+"$encode")("if(!w)")("w=Writer.create()"),a=e.fieldsArray.slice().sort(i.compareFieldsById),u=0;u<a.length;++u){var c=a[u].resolve(),l=e._fieldsArray.indexOf(c),d=c.resolvedType instanceof r?"int32":c.type,p=o.basic[d];t="m"+i.safeProp(c.name),c.map?(n("if(%s!=null&&Object.hasOwnProperty.call(m,%j)){",t,c.name)("for(var ks=Object.keys(%s),i=0;i<ks.length;++i){",t)("w.uint32(%i).fork().uint32(%i).%s(ks[i])",(c.id<<3|2)>>>0,8|o.mapKey[c.keyType],c.keyType),void 0===p?n("types[%i].encode(%s[ks[i]],w.uint32(18).fork()).ldelim().ldelim()",l,t):n(".uint32(%i).%s(%s[ks[i]]).ldelim()",16|p,d,t),n("}")("}")):c.repeated?(n("if(%s!=null&&%s.length){",t,t),c.packed&&void 0!==o.packed[d]?n("w.uint32(%i).fork()",(c.id<<3|2)>>>0)("for(var i=0;i<%s.length;++i)",t)("w.%s(%s[i])",d,t)("w.ldelim()"):(n("for(var i=0;i<%s.length;++i)",t),void 0===p?s(n,c,l,t+"[i]"):n("w.uint32(%i).%s(%s[i])",(c.id<<3|p)>>>0,d,t)),n("}")):(c.optional&&n("if(%s!=null&&Object.hasOwnProperty.call(m,%j))",t,c.name),void 0===p?s(n,c,l,t):n("w.uint32(%i).%s(%s)",(c.id<<3|p)>>>0,d,t))}return n("return w")};var r=n(5643),o=n(361),i=n(3262);function s(e,t,n,r){return t.resolvedType.group?e("types[%i].encode(%s,w.uint32(%i)).uint32(%i)",n,r,(t.id<<3|3)>>>0,(t.id<<3|4)>>>0):e("types[%i].encode(%s,w.uint32(%i).fork()).ldelim()",n,r,(t.id<<3|2)>>>0)}},1088:function(e,t,n){"use strict";var r=n(6518),o=n(9565),i=n(6395),s=n(350),a=n(4901),u=n(3994),c=n(2787),l=n(2967),d=n(687),p=n(6699),h=n(6840),f=n(8227),v=n(6269),E=n(7657),m=s.PROPER,g=s.CONFIGURABLE,y=E.IteratorPrototype,_=E.BUGGY_SAFARI_ITERATORS,O=f("iterator"),T="keys",R="values",I="entries",C=function(){return this};e.exports=function(e,t,n,s,f,E,S){u(n,t,s);var N,A,b,M=function(e){if(e===f&&D)return D;if(!_&&e&&e in U)return U[e];switch(e){case T:case R:case I:return function(){return new n(this,e)}}return function(){return new n(this)}},w=t+" Iterator",L=!1,U=e.prototype,x=U[O]||U["@@iterator"]||f&&U[f],D=!_&&x||M(f),P="Array"===t&&U.entries||x;if(P&&(N=c(P.call(new e)))!==Object.prototype&&N.next&&(i||c(N)===y||(l?l(N,y):a(N[O])||h(N,O,C)),d(N,w,!0,!0),i&&(v[w]=C)),m&&f===R&&x&&x.name!==R&&(!i&&g?p(U,"name",R):(L=!0,D=function(){return o(x,this)})),f)if(A={values:M(R),keys:E?D:M(T),entries:M(I)},S)for(b in A)(_||L||!(b in U))&&h(U,b,A[b]);else r({target:t,proto:!0,forced:_||L},A);return i&&!S||U[O]===D||h(U,O,D,{name:f}),v[t]=D,A}},1103:function(e){"use strict";e.exports=function(e){try{return{error:!1,value:e()}}catch(e){return{error:!0,value:e}}}},1108:function(e,t,n){"use strict";var r=n(6955);e.exports=function(e){var t=r(e);return"BigInt64Array"===t||"BigUint64Array"===t}},1181:function(e,t,n){"use strict";var r,o,i,s=n(8622),a=n(4576),u=n(34),c=n(6699),l=n(9297),d=n(7629),p=n(6119),h=n(421),f="Object already initialized",v=a.TypeError,E=a.WeakMap;if(s||d.state){var m=d.state||(d.state=new E);m.get=m.get,m.has=m.has,m.set=m.set,r=function(e,t){if(m.has(e))throw new v(f);return t.facade=e,m.set(e,t),t},o=function(e){return m.get(e)||{}},i=function(e){return m.has(e)}}else{var g=p("state");h[g]=!0,r=function(e,t){if(l(e,g))throw new v(f);return t.facade=e,c(e,g,t),t},o=function(e){return l(e,g)?e[g]:{}},i=function(e){return l(e,g)}}e.exports={set:r,get:o,has:i,enforce:function(e){return i(e)?o(e):r(e,{})},getterFor:function(e){return function(t){var n;if(!u(t)||(n=o(t)).type!==e)throw new v("Incompatible receiver, "+e+" required");return n}}}},1240:function(e,t,n){"use strict";var r=n(9504);e.exports=r(1..valueOf)},1291:function(e,t,n){"use strict";var r=n(741);e.exports=function(e){var t=+e;return t!=t||0===t?0:r(t)}},1296:function(e,t,n){"use strict";var r=n(4495);e.exports=r&&!!Symbol.for&&!!Symbol.keyFor},1344:function(e,t,n){"use strict";e.exports=c;var r=n(7209);((c.prototype=Object.create(r.prototype)).constructor=c).className="Field";var o,i=n(5643),s=n(361),a=n(3262),u=/^required|optional|repeated$/;function c(e,t,n,o,i,c,l){if(a.isObject(o)?(l=i,c=o,o=i=void 0):a.isObject(i)&&(l=c,c=i,i=void 0),r.call(this,e,c),!a.isInteger(t)||t<0)throw TypeError("id must be a non-negative integer");if(!a.isString(n))throw TypeError("type must be a string");if(void 0!==o&&!u.test(o=o.toString().toLowerCase()))throw TypeError("rule must be a string rule");if(void 0!==i&&!a.isString(i))throw TypeError("extend must be a string");"proto3_optional"===o&&(o="optional"),this.rule=o&&"optional"!==o?o:void 0,this.type=n,this.id=t,this.extend=i||void 0,this.required="required"===o,this.optional=!this.required,this.repeated="repeated"===o,this.map=!1,this.message=null,this.partOf=null,this.typeDefault=null,this.defaultValue=null,this.long=!!a.Long&&void 0!==s.long[n],this.bytes="bytes"===n,this.resolvedType=null,this.extensionField=null,this.declaringField=null,this._packed=null,this.comment=l}c.fromJSON=function(e,t){return new c(e,t.id,t.type,t.rule,t.extend,t.options,t.comment)},Object.defineProperty(c.prototype,"packed",{get:function(){return null===this._packed&&(this._packed=!1!==this.getOption("packed")),this._packed}}),c.prototype.setOption=function(e,t,n){return"packed"===e&&(this._packed=null),r.prototype.setOption.call(this,e,t,n)},c.prototype.toJSON=function(e){var t=!!e&&Boolean(e.keepComments);return a.toObject(["rule","optional"!==this.rule&&this.rule||void 0,"type",this.type,"id",this.id,"extend",this.extend,"options",this.options,"comment",t?this.comment:void 0])},c.prototype.resolve=function(){if(this.resolved)return this;if(void 0===(this.typeDefault=s.defaults[this.type])&&(this.resolvedType=(this.declaringField?this.declaringField.parent:this.parent).lookupTypeOrEnum(this.type),this.resolvedType instanceof o?this.typeDefault=null:this.typeDefault=this.resolvedType.values[Object.keys(this.resolvedType.values)[0]]),this.options&&null!=this.options.default&&(this.typeDefault=this.options.default,this.resolvedType instanceof i&&"string"==typeof this.typeDefault&&(this.typeDefault=this.resolvedType.values[this.typeDefault])),this.options&&(!0!==this.options.packed&&(void 0===this.options.packed||!this.resolvedType||this.resolvedType instanceof i)||delete this.options.packed,Object.keys(this.options).length||(this.options=void 0)),this.long)this.typeDefault=a.Long.fromNumber(this.typeDefault,"u"===this.type.charAt(0)),Object.freeze&&Object.freeze(this.typeDefault);else if(this.bytes&&"string"==typeof this.typeDefault){var e;a.base64.test(this.typeDefault)?a.base64.decode(this.typeDefault,e=a.newBuffer(a.base64.length(this.typeDefault)),0):a.utf8.write(this.typeDefault,e=a.newBuffer(a.utf8.length(this.typeDefault)),0),this.typeDefault=e}return this.map?this.defaultValue=a.emptyObject:this.repeated?this.defaultValue=a.emptyArray:this.defaultValue=this.typeDefault,this.parent instanceof o&&(this.parent.ctor.prototype[this.name]=this.defaultValue),r.prototype.resolve.call(this)},c.d=function(e,t,n,r){return"function"==typeof t?t=a.decorateType(t).name:t&&"object"==typeof t&&(t=a.decorateEnum(t).name),function(o,i){a.decorateType(o.constructor).add(new c(i,e,t,n,{default:r}))}},c._configure=function(e){o=e}},1392:function(e,t,n){"use strict";var r,o=n(6518),i=n(7476),s=n(7347).f,a=n(8014),u=n(655),c=n(5749),l=n(7750),d=n(1436),p=n(6395),h=i("".slice),f=Math.min,v=d("startsWith");o({target:"String",proto:!0,forced:!(!p&&!v&&(r=s(String.prototype,"startsWith"),r&&!r.writable)||v)},{startsWith:function(e){var t=u(l(this));c(e);var n=a(f(arguments.length>1?arguments[1]:void 0,t.length)),r=u(e);return h(t,n,n+r.length)===r}})},1405:function(e,t,n){"use strict";var r=n(4576),o=n(8745),i=n(4644),s=n(9039),a=n(7680),u=r.Int8Array,c=i.aTypedArray,l=i.exportTypedArrayMethod,d=[].toLocaleString,p=!!u&&s((function(){d.call(new u(1))}));l("toLocaleString",(function(){return o(d,p?a(c(this)):c(this),a(arguments))}),s((function(){return[1,2].toLocaleString()!==new u([1,2]).toLocaleString()}))||!s((function(){u.prototype.toLocaleString.call([1,2])})))},1436:function(e,t,n){"use strict";var r=n(8227)("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(n){try{return t[r]=!1,"/./"[e](t)}catch(e){}}return!1}},1447:function(e,t){"use strict";var n=t;n.length=function(e){for(var t=0,n=0,r=0;r<e.length;++r)(n=e.charCodeAt(r))<128?t+=1:n<2048?t+=2:55296==(64512&n)&&56320==(64512&e.charCodeAt(r+1))?(++r,t+=4):t+=3;return t},n.read=function(e,t,n){if(n-t<1)return"";for(var r,o=null,i=[],s=0;t<n;)(r=e[t++])<128?i[s++]=r:r>191&&r<224?i[s++]=(31&r)<<6|63&e[t++]:r>239&&r<365?(r=((7&r)<<18|(63&e[t++])<<12|(63&e[t++])<<6|63&e[t++])-65536,i[s++]=55296+(r>>10),i[s++]=56320+(1023&r)):i[s++]=(15&r)<<12|(63&e[t++])<<6|63&e[t++],s>8191&&((o||(o=[])).push(String.fromCharCode.apply(String,i)),s=0);return o?(s&&o.push(String.fromCharCode.apply(String,i.slice(0,s))),o.join("")):String.fromCharCode.apply(String,i.slice(0,s))},n.write=function(e,t,n){for(var r,o,i=n,s=0;s<e.length;++s)(r=e.charCodeAt(s))<128?t[n++]=r:r<2048?(t[n++]=r>>6|192,t[n++]=63&r|128):55296==(64512&r)&&56320==(64512&(o=e.charCodeAt(s+1)))?(r=65536+((1023&r)<<10)+(1023&o),++s,t[n++]=r>>18|240,t[n++]=r>>12&63|128,t[n++]=r>>6&63|128,t[n++]=63&r|128):(t[n++]=r>>12|224,t[n++]=r>>6&63|128,t[n++]=63&r|128);return n-i}},1457:function(e,t,n){"use strict";e.exports=s;var r=n(7209);((s.prototype=Object.create(r.prototype)).constructor=s).className="OneOf";var o=n(1344),i=n(3262);function s(e,t,n,o){if(Array.isArray(t)||(n=t,t=void 0),r.call(this,e,n),void 0!==t&&!Array.isArray(t))throw TypeError("fieldNames must be an Array");this.oneof=t||[],this.fieldsArray=[],this.comment=o}function a(e){if(e.parent)for(var t=0;t<e.fieldsArray.length;++t)e.fieldsArray[t].parent||e.parent.add(e.fieldsArray[t])}s.fromJSON=function(e,t){return new s(e,t.oneof,t.options,t.comment)},s.prototype.toJSON=function(e){var t=!!e&&Boolean(e.keepComments);return i.toObject(["options",this.options,"oneof",this.oneof,"comment",t?this.comment:void 0])},s.prototype.add=function(e){if(!(e instanceof o))throw TypeError("field must be a Field");return e.parent&&e.parent!==this.parent&&e.parent.remove(e),this.oneof.push(e.name),this.fieldsArray.push(e),e.partOf=this,a(this),this},s.prototype.remove=function(e){if(!(e instanceof o))throw TypeError("field must be a Field");var t=this.fieldsArray.indexOf(e);if(t<0)throw Error(e+" is not a member of "+this);return this.fieldsArray.splice(t,1),(t=this.oneof.indexOf(e.name))>-1&&this.oneof.splice(t,1),e.partOf=null,this},s.prototype.onAdd=function(e){r.prototype.onAdd.call(this,e);for(var t=0;t<this.oneof.length;++t){var n=e.get(this.oneof[t]);n&&!n.partOf&&(n.partOf=this,this.fieldsArray.push(n))}a(this)},s.prototype.onRemove=function(e){for(var t,n=0;n<this.fieldsArray.length;++n)(t=this.fieldsArray[n]).parent&&t.parent.remove(t);r.prototype.onRemove.call(this,e)},s.d=function(){for(var e=new Array(arguments.length),t=0;t<arguments.length;)e[t]=arguments[t++];return function(t,n){i.decorateType(t.constructor).add(new s(n,e)),Object.defineProperty(t,n,{get:i.oneOfGetter(e),set:i.oneOfSetter(e)})}}},1469:function(e,t,n){"use strict";var r=n(7433);e.exports=function(e,t){return new(r(e))(0===t?0:t)}},1481:function(e,t,n){"use strict";var r=n(6518),o=n(6043);r({target:"Promise",stat:!0,forced:n(916).CONSTRUCTOR},{reject:function(e){var t=o.f(this);return(0,t.reject)(e),t.promise}})},1489:function(e,t,n){"use strict";n(5823)("Uint8",(function(e){return function(t,n,r){return e(this,t,n,r)}}))},1510:function(e,t,n){"use strict";var r=n(6518),o=n(7751),i=n(9297),s=n(655),a=n(5745),u=n(1296),c=a("string-to-symbol-registry"),l=a("symbol-to-string-registry");r({target:"Symbol",stat:!0,forced:!u},{for:function(e){var t=s(e);if(i(c,t))return c[t];var n=o("Symbol")(t);return c[t]=n,l[n]=t,n}})},1531:function(e,t,n){"use strict";var r;n.d(t,{C:function(){return r}}),function(e){e[e.REQUEST_SUCCESS=0]="REQUEST_SUCCESS",e[e.REQUEST_TIMEOUT=-1]="REQUEST_TIMEOUT",e[e.REQUEST_UNKNOWN=-2]="REQUEST_UNKNOWN",e[e.REQUEST_PARAMETER_ERROR=-3]="REQUEST_PARAMETER_ERROR",e[e.REQUEST_ABORT=-4]="REQUEST_ABORT",e[e.WEBIM_CONNCTION_USER_NOT_ASSIGN_ERROR=0]="WEBIM_CONNCTION_USER_NOT_ASSIGN_ERROR",e[e.WEBIM_CONNCTION_OPEN_ERROR=1]="WEBIM_CONNCTION_OPEN_ERROR",e[e.WEBIM_CONNCTION_AUTH_ERROR=2]="WEBIM_CONNCTION_AUTH_ERROR",e[e.WEBIM_CONNCTION_OPEN_USERGRID_ERROR=3]="WEBIM_CONNCTION_OPEN_USERGRID_ERROR",e[e.WEBIM_CONNCTION_ATTACH_ERROR=4]="WEBIM_CONNCTION_ATTACH_ERROR",e[e.WEBIM_CONNCTION_ATTACH_USERGRID_ERROR=5]="WEBIM_CONNCTION_ATTACH_USERGRID_ERROR",e[e.WEBIM_CONNCTION_REOPEN_ERROR=6]="WEBIM_CONNCTION_REOPEN_ERROR",e[e.WEBIM_CONNCTION_SERVER_CLOSE_ERROR=7]="WEBIM_CONNCTION_SERVER_CLOSE_ERROR",e[e.WEBIM_CONNCTION_SERVER_ERROR=8]="WEBIM_CONNCTION_SERVER_ERROR",e[e.WEBIM_CONNCTION_IQ_ERROR=9]="WEBIM_CONNCTION_IQ_ERROR",e[e.WEBIM_CONNCTION_PING_ERROR=10]="WEBIM_CONNCTION_PING_ERROR",e[e.WEBIM_CONNCTION_NOTIFYVERSION_ERROR=11]="WEBIM_CONNCTION_NOTIFYVERSION_ERROR",e[e.WEBIM_CONNCTION_GETROSTER_ERROR=12]="WEBIM_CONNCTION_GETROSTER_ERROR",e[e.WEBIM_CONNCTION_CROSSDOMAIN_ERROR=13]="WEBIM_CONNCTION_CROSSDOMAIN_ERROR",e[e.WEBIM_CONNCTION_LISTENING_OUTOF_MAXRETRIES=14]="WEBIM_CONNCTION_LISTENING_OUTOF_MAXRETRIES",e[e.WEBIM_CONNCTION_RECEIVEMSG_CONTENTERROR=15]="WEBIM_CONNCTION_RECEIVEMSG_CONTENTERROR",e[e.WEBIM_CONNCTION_DISCONNECTED=16]="WEBIM_CONNCTION_DISCONNECTED",e[e.WEBIM_CONNCTION_AJAX_ERROR=17]="WEBIM_CONNCTION_AJAX_ERROR",e[e.WEBIM_CONNCTION_JOINROOM_ERROR=18]="WEBIM_CONNCTION_JOINROOM_ERROR",e[e.WEBIM_CONNCTION_GETROOM_ERROR=19]="WEBIM_CONNCTION_GETROOM_ERROR",e[e.WEBIM_CONNCTION_GETROOMINFO_ERROR=20]="WEBIM_CONNCTION_GETROOMINFO_ERROR",e[e.WEBIM_CONNCTION_GETROOMMEMBER_ERROR=21]="WEBIM_CONNCTION_GETROOMMEMBER_ERROR",e[e.WEBIM_CONNCTION_GETROOMOCCUPANTS_ERROR=22]="WEBIM_CONNCTION_GETROOMOCCUPANTS_ERROR",e[e.WEBIM_CONNCTION_LOAD_CHATROOM_ERROR=23]="WEBIM_CONNCTION_LOAD_CHATROOM_ERROR",e[e.WEBIM_CONNCTION_NOT_SUPPORT_CHATROOM_ERROR=24]="WEBIM_CONNCTION_NOT_SUPPORT_CHATROOM_ERROR",e[e.WEBIM_CONNCTION_JOINCHATROOM_ERROR=25]="WEBIM_CONNCTION_JOINCHATROOM_ERROR",e[e.WEBIM_CONNCTION_QUITCHATROOM_ERROR=26]="WEBIM_CONNCTION_QUITCHATROOM_ERROR",e[e.WEBIM_CONNCTION_APPKEY_NOT_ASSIGN_ERROR=27]="WEBIM_CONNCTION_APPKEY_NOT_ASSIGN_ERROR",e[e.WEBIM_CONNCTION_TOKEN_NOT_ASSIGN_ERROR=28]="WEBIM_CONNCTION_TOKEN_NOT_ASSIGN_ERROR",e[e.WEBIM_CONNCTION_SESSIONID_NOT_ASSIGN_ERROR=29]="WEBIM_CONNCTION_SESSIONID_NOT_ASSIGN_ERROR",e[e.WEBIM_CONNCTION_RID_NOT_ASSIGN_ERROR=30]="WEBIM_CONNCTION_RID_NOT_ASSIGN_ERROR",e[e.WEBIM_CONNCTION_CALLBACK_INNER_ERROR=31]="WEBIM_CONNCTION_CALLBACK_INNER_ERROR",e[e.WEBIM_CONNCTION_CLIENT_OFFLINE=32]="WEBIM_CONNCTION_CLIENT_OFFLINE",e[e.WEBIM_CONNCTION_CLIENT_LOGOUT=33]="WEBIM_CONNCTION_CLIENT_LOGOUT",e[e.WEBIM_CONNCTION_CLIENT_TOO_MUCH_ERROR=34]="WEBIM_CONNCTION_CLIENT_TOO_MUCH_ERROR",e[e.WEBIM_CONNECTION_ACCEPT_INVITATION_FROM_GROUP=35]="WEBIM_CONNECTION_ACCEPT_INVITATION_FROM_GROUP",e[e.WEBIM_CONNECTION_DECLINE_INVITATION_FROM_GROUP=36]="WEBIM_CONNECTION_DECLINE_INVITATION_FROM_GROUP",e[e.WEBIM_CONNECTION_ACCEPT_JOIN_GROUP=37]="WEBIM_CONNECTION_ACCEPT_JOIN_GROUP",e[e.WEBIM_CONNECTION_DECLINE_JOIN_GROUP=38]="WEBIM_CONNECTION_DECLINE_JOIN_GROUP",e[e.WEBIM_CONNECTION_CLOSED=39]="WEBIM_CONNECTION_CLOSED",e[e.WEBIM_CONNECTION_ERROR=40]="WEBIM_CONNECTION_ERROR",e[e.MAX_LIMIT=50]="MAX_LIMIT",e[e.MESSAGE_NOT_FOUND=51]="MESSAGE_NOT_FOUND",e[e.NO_PERMISSION=52]="NO_PERMISSION",e[e.OPERATION_UNSUPPORTED=53]="OPERATION_UNSUPPORTED",e[e.OPERATION_NOT_ALLOWED=54]="OPERATION_NOT_ALLOWED",e[e.WEBIM_TOKEN_EXPIRED=56]="WEBIM_TOKEN_EXPIRED",e[e.WEBIM_SERVER_SERVING_DISABLED=57]="WEBIM_SERVER_SERVING_DISABLED",e[e.WEBIM_UPLOADFILE_BROWSER_ERROR=100]="WEBIM_UPLOADFILE_BROWSER_ERROR",e[e.WEBIM_UPLOADFILE_ERROR=101]="WEBIM_UPLOADFILE_ERROR",e[e.WEBIM_UPLOADFILE_NO_LOGIN=102]="WEBIM_UPLOADFILE_NO_LOGIN",e[e.WEBIM_UPLOADFILE_NO_FILE=103]="WEBIM_UPLOADFILE_NO_FILE",e[e.WEBIM_DOWNLOADFILE_ERROR=200]="WEBIM_DOWNLOADFILE_ERROR",e[e.WEBIM_DOWNLOADFILE_NO_LOGIN=201]="WEBIM_DOWNLOADFILE_NO_LOGIN",e[e.WEBIM_DOWNLOADFILE_BROWSER_ERROR=202]="WEBIM_DOWNLOADFILE_BROWSER_ERROR",e[e.PARSE_FILE_ERROR=203]="PARSE_FILE_ERROR",e[e.USER_NOT_FOUND=204]="USER_NOT_FOUND",e[e.MESSAGE_PARAMETER_ERROR=205]="MESSAGE_PARAMETER_ERROR",e[e.WEBIM_CONNCTION_USER_LOGIN_ANOTHER_DEVICE=206]="WEBIM_CONNCTION_USER_LOGIN_ANOTHER_DEVICE",e[e.WEBIM_CONNCTION_USER_REMOVED=207]="WEBIM_CONNCTION_USER_REMOVED",e[e.WEBIM_USER_ALREADY_LOGIN=208]="WEBIM_USER_ALREADY_LOGIN",e[e.WEBIM_CONNCTION_USER_KICKED_BY_CHANGE_PASSWORD=216]="WEBIM_CONNCTION_USER_KICKED_BY_CHANGE_PASSWORD",e[e.WEBIM_CONNCTION_USER_KICKED_BY_OTHER_DEVICE=217]="WEBIM_CONNCTION_USER_KICKED_BY_OTHER_DEVICE",e[e.USER_MUTED_BY_ADMIN=219]="USER_MUTED_BY_ADMIN",e[e.USER_NOT_FRIEND=221]="USER_NOT_FRIEND",e[e.WEBIM_DOWNLOADFILE_ERROR_NO_PERMISSION=222]="WEBIM_DOWNLOADFILE_ERROR_NO_PERMISSION",e[e.WEBIM_DOWNLOADFILE_ERROR_EXPIRED=223]="WEBIM_DOWNLOADFILE_ERROR_EXPIRED",e[e.WEBIM_MESSAGE_REC_TEXT=300]="WEBIM_MESSAGE_REC_TEXT",e[e.WEBIM_MESSAGE_REC_TEXT_ERROR=301]="WEBIM_MESSAGE_REC_TEXT_ERROR",e[e.WEBIM_MESSAGE_REC_EMOTION=302]="WEBIM_MESSAGE_REC_EMOTION",e[e.WEBIM_MESSAGE_REC_PHOTO=303]="WEBIM_MESSAGE_REC_PHOTO",e[e.SERVER_GET_DNSLIST_FAILED=304]="SERVER_GET_DNSLIST_FAILED",e[e.WEBIM_MESSAGE_REC_AUDIO_FILE=305]="WEBIM_MESSAGE_REC_AUDIO_FILE",e[e.WEBIM_MESSAGE_REC_VEDIO=306]="WEBIM_MESSAGE_REC_VEDIO",e[e.WEBIM_MESSAGE_REC_VEDIO_FILE=307]="WEBIM_MESSAGE_REC_VEDIO_FILE",e[e.WEBIM_MESSAGE_REC_FILE=308]="WEBIM_MESSAGE_REC_FILE",e[e.WEBIM_MESSAGE_SED_TEXT=309]="WEBIM_MESSAGE_SED_TEXT",e[e.WEBIM_MESSAGE_SED_EMOTION=310]="WEBIM_MESSAGE_SED_EMOTION",e[e.WEBIM_MESSAGE_SED_PHOTO=311]="WEBIM_MESSAGE_SED_PHOTO",e[e.WEBIM_MESSAGE_SED_AUDIO=312]="WEBIM_MESSAGE_SED_AUDIO",e[e.WEBIM_MESSAGE_SED_AUDIO_FILE=313]="WEBIM_MESSAGE_SED_AUDIO_FILE",e[e.WEBIM_MESSAGE_SED_VEDIO=314]="WEBIM_MESSAGE_SED_VEDIO",e[e.WEBIM_MESSAGE_SED_VEDIO_FILE=315]="WEBIM_MESSAGE_SED_VEDIO_FILE",e[e.WEBIM_MESSAGE_SED_FILE=316]="WEBIM_MESSAGE_SED_FILE",e[e.WEBIM_MESSAGE_SED_ERROR=317]="WEBIM_MESSAGE_SED_ERROR",e[e.STATUS_INIT=400]="STATUS_INIT",e[e.STATUS_DOLOGIN_USERGRID=401]="STATUS_DOLOGIN_USERGRID",e[e.STATUS_DOLOGIN_IM=402]="STATUS_DOLOGIN_IM",e[e.STATUS_OPENED=403]="STATUS_OPENED",e[e.STATUS_CLOSING=404]="STATUS_CLOSING",e[e.STATUS_CLOSED=405]="STATUS_CLOSED",e[e.STATUS_ERROR=406]="STATUS_ERROR",e[e.SERVER_BUSY=500]="SERVER_BUSY",e[e.MESSAGE_INCLUDE_ILLEGAL_CONTENT=501]="MESSAGE_INCLUDE_ILLEGAL_CONTENT",e[e.MESSAGE_EXTERNAL_LOGIC_BLOCKED=502]="MESSAGE_EXTERNAL_LOGIC_BLOCKED",e[e.SERVER_UNKNOWN_ERROR=503]="SERVER_UNKNOWN_ERROR",e[e.MESSAGE_RECALL_TIME_LIMIT=504]="MESSAGE_RECALL_TIME_LIMIT",e[e.SERVICE_NOT_ENABLED=505]="SERVICE_NOT_ENABLED",e[e.SERVICE_NOT_ALLOW_MESSAGING=506]="SERVICE_NOT_ALLOW_MESSAGING",e[e.SERVICE_NOT_ALLOW_MESSAGING_MUTE=507]="SERVICE_NOT_ALLOW_MESSAGING_MUTE",e[e.MESSAGE_MODERATION_BLOCKED=508]="MESSAGE_MODERATION_BLOCKED",e[e.MESSAGE_CURRENT_LIMITING=509]="MESSAGE_CURRENT_LIMITING",e[e.MESSAGE_WEBSOCKET_DISCONNECTED=510]="MESSAGE_WEBSOCKET_DISCONNECTED",e[e.MESSAGE_SIZE_LIMIT=511]="MESSAGE_SIZE_LIMIT",e[e.MESSAGE_SEND_TIMEOUT=512]="MESSAGE_SEND_TIMEOUT",e[e.GROUP_NOT_EXIST=605]="GROUP_NOT_EXIST",e[e.GROUP_NOT_JOINED=602]="GROUP_NOT_JOINED",e[e.GROUP_MEMBERS_FULL=606]="GROUP_MEMBERS_FULL",e[e.PERMISSION_DENIED=603]="PERMISSION_DENIED",e[e.WEBIM_LOAD_MSG_ERROR=604]="WEBIM_LOAD_MSG_ERROR",e[e.GROUP_ALREADY_JOINED=601]="GROUP_ALREADY_JOINED",e[e.GROUP_MEMBERS_LIMIT=607]="GROUP_MEMBERS_LIMIT",e[e.GROUP_IS_DISABLED=608]="GROUP_IS_DISABLED",e[e.GROUP_MEMBER_ATTRIBUTES_SET_FAILED=609]="GROUP_MEMBER_ATTRIBUTES_SET_FAILED",e[e.REST_PARAMS_STATUS=700]="REST_PARAMS_STATUS",e[e.CHATROOM_NOT_JOINED=702]="CHATROOM_NOT_JOINED",e[e.CHATROOM_MEMBERS_FULL=704]="CHATROOM_MEMBERS_FULL",e[e.CHATROOM_NOT_EXIST=705]="CHATROOM_NOT_EXIST",e[e.LOCAL_DB_OPERATION_FAILED=800]="LOCAL_DB_OPERATION_FAILED",e[e.SDK_RUNTIME_ERROR=999]="SDK_RUNTIME_ERROR",e[e.PRESENCE_PARAM_EXCEED=1100]="PRESENCE_PARAM_EXCEED",e[e.REACTION_ALREADY_ADDED=1101]="REACTION_ALREADY_ADDED",e[e.REACTION_CREATING=1102]="REACTION_CREATING",e[e.REACTION_OPERATION_IS_ILLEGAL=1103]="REACTION_OPERATION_IS_ILLEGAL",e[e.TRANSLATION_NOT_VALID=1200]="TRANSLATION_NOT_VALID",e[e.TRANSLATION_TEXT_TOO_LONG=1201]="TRANSLATION_TEXT_TOO_LONG",e[e.TRANSLATION_FAILED=1204]="TRANSLATION_FAILED",e[e.THREAD_NOT_EXIST=1300]="THREAD_NOT_EXIST",e[e.THREAD_ALREADY_EXIST=1301]="THREAD_ALREADY_EXIST",e[e.MODIFY_MESSAGE_NOT_EXIST=1302]="MODIFY_MESSAGE_NOT_EXIST",e[e.MODIFY_MESSAGE_FORMAT_ERROR=1303]="MODIFY_MESSAGE_FORMAT_ERROR",e[e.MODIFY_MESSAGE_FAILED=1304]="MODIFY_MESSAGE_FAILED",e[e.CONVERSATION_NOT_EXIST=1400]="CONVERSATION_NOT_EXIST"}(r||(r={}))},1575:function(e,t,n){"use strict";var r=n(4644),o=n(926).left,i=r.aTypedArray;(0,r.exportTypedArrayMethod)("reduce",(function(e){var t=arguments.length;return o(i(this),e,t,t>1?arguments[1]:void 0)}))},1625:function(e,t,n){"use strict";var r=n(9504);e.exports=r({}.isPrototypeOf)},1629:function(e,t,n){"use strict";var r=n(6518),o=n(235);r({target:"Array",proto:!0,forced:[].forEach!==o},{forEach:o})},1630:function(e,t,n){"use strict";var r=n(9504),o=n(4644),i=r(n(7029)),s=o.aTypedArray;(0,o.exportTypedArrayMethod)("copyWithin",(function(e,t){return i(s(this),e,t,arguments.length>2?arguments[2]:void 0)}))},1694:function(e,t,n){"use strict";var r=n(4644),o=n(9213).find,i=r.aTypedArray;(0,r.exportTypedArrayMethod)("find",(function(e){return o(i(this),e,arguments.length>1?arguments[1]:void 0)}))},1699:function(e,t,n){"use strict";var r=n(6518),o=n(9504),i=n(5749),s=n(7750),a=n(655),u=n(1436),c=o("".indexOf);r({target:"String",proto:!0,forced:!u("includes")},{includes:function(e){return!!~c(a(s(this)),a(i(e)),arguments.length>1?arguments[1]:void 0)}})},1745:function(e,t,n){"use strict";var r=n(6518),o=n(7476),i=n(9039),s=n(6346),a=n(8551),u=n(5610),c=n(8014),l=s.ArrayBuffer,d=s.DataView,p=d.prototype,h=o(l.prototype.slice),f=o(p.getUint8),v=o(p.setUint8);r({target:"ArrayBuffer",proto:!0,unsafe:!0,forced:i((function(){return!new l(2).slice(1,void 0).byteLength}))},{slice:function(e,t){if(h&&void 0===t)return h(a(this),e);for(var n=a(this).byteLength,r=u(e,n),o=u(void 0===t?n:t,n),i=new l(c(o-r)),s=new d(this),p=new d(i),E=0;r<o;)v(p,E++,f(s,r++));return i}})},1750:function(e,t,n){"use strict";n.d(t,{A4:function(){return l},dO:function(){return u},fu:function(){return c}}),n(1629),n(4423),n(9432),n(6099),n(1699),n(3500);var r=n(1531),o=n(346),i=n(2056);function s(){var e=this.context.appName,t=this.context.orgName;return!(!e||!t)||(this.onError&&this.onError({type:r.C.WEBIM_CONNCTION_AUTH_ERROR,message:"appName or orgName is illegal"}),!1)}function a(){var e,t=this.context.accessToken;if(!t){var n=o.A.create({type:r.C.WEBIM_CONNCTION_TOKEN_NOT_ASSIGN_ERROR,message:"token not assign error"});return i.vF.debug("token not assign error",t),this.onError&&this.onError(n),null===(e=this.eventHandler)||void 0===e||e.dispatch("onError",n),!1}return!0}function u(){if(this._initWithAppId){if(!s.call(this))return{error:o.A.create({type:r.C.WEBIM_CONNECTION_CLOSED,message:"not login"})};if(!a.call(this))return{error:o.A.create({type:r.C.WEBIM_CONNCTION_TOKEN_NOT_ASSIGN_ERROR,message:"token not assign error"})}}else if(!s.call(this)||!a.call(this))return{error:o.A.create({type:r.C.REST_PARAMS_STATUS,message:"appkey or token error"})};return{}}function c(e){var t=e.data,n=e.type;return{data:{status:Object.keys(t.errorKeys).length>0?"fail":"success",errorKeys:t.errorKeys,successKeys:t.successKeys},type:n}}function l(e){var t=e.data,n=void 0;return Object.keys(t.errorKeys).length>0&&Object.keys(t.errorKeys).forEach((function(e){var i=t.errorKeys[e];n=i.includes("is not part of you")?o.A.create({type:r.C.NO_PERMISSION,message:i}):i.includes("size of metadata for this single chatroom exceeds the user defined limit")||i.includes("total size of chatroom metadata for this app exceeds the user defined limit")||i.includes("is exceeding maximum limit")?o.A.create({type:r.C.MAX_LIMIT,message:i}):i.includes("is not Legal")?o.A.create({type:r.C.REQUEST_PARAMETER_ERROR,message:i}):i.includes("Failed to update userMetadata. Concurrent updates not allowed")?o.A.create({type:r.C.OPERATION_NOT_ALLOWED,message:i}):o.A.create({type:r.C.WEBIM_CONNCTION_AJAX_ERROR,message:i})})),n}},1828:function(e,t,n){"use strict";var r=n(9504),o=n(9297),i=n(5397),s=n(9617).indexOf,a=n(421),u=r([].push);e.exports=function(e,t){var n,r=i(e),c=0,l=[];for(n in r)!o(a,n)&&o(r,n)&&u(l,n);for(;t.length>c;)o(r,n=t[c++])&&(~s(l,n)||u(l,n));return l}},1920:function(e,t,n){"use strict";var r=n(4644),o=n(9213).filter,i=n(9948),s=r.aTypedArray;(0,r.exportTypedArrayMethod)("filter",(function(e){var t=o(s(this),e,arguments.length>1?arguments[1]:void 0);return i(this,t)}))},1951:function(e,t,n){"use strict";var r=n(8227);t.f=r},1955:function(e,t,n){"use strict";var r,o,i,s,a,u=n(4576),c=n(3389),l=n(6080),d=n(9225).set,p=n(8265),h=n(9544),f=n(4265),v=n(7860),E=n(6193),m=u.MutationObserver||u.WebKitMutationObserver,g=u.document,y=u.process,_=u.Promise,O=c("queueMicrotask");if(!O){var T=new p,R=function(){var e,t;for(E&&(e=y.domain)&&e.exit();t=T.get();)try{t()}catch(e){throw T.head&&r(),e}e&&e.enter()};h||E||v||!m||!g?!f&&_&&_.resolve?((s=_.resolve(void 0)).constructor=_,a=l(s.then,s),r=function(){a(R)}):E?r=function(){y.nextTick(R)}:(d=l(d,u),r=function(){d(R)}):(o=!0,i=g.createTextNode(""),new m(R).observe(i,{characterData:!0}),r=function(){i.data=o=!o}),O=function(e){T.head||r(),T.add(e)}}e.exports=O},2003:function(e,t,n){"use strict";var r=n(6518),o=n(6395),i=n(916).CONSTRUCTOR,s=n(550),a=n(7751),u=n(4901),c=n(6840),l=s&&s.prototype;if(r({target:"Promise",proto:!0,forced:i,real:!0},{catch:function(e){return this.then(void 0,e)}}),!o&&u(s)){var d=a("Promise").prototype.catch;l.catch!==d&&c(l,"catch",d,{unsafe:!0})}},2008:function(e,t,n){"use strict";var r=n(6518),o=n(9213).filter;r({target:"Array",proto:!0,forced:!n(597)("filter")},{filter:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}})},2010:function(e,t,n){"use strict";var r=n(3724),o=n(350).EXISTS,i=n(9504),s=n(2106),a=Function.prototype,u=i(a.toString),c=/function\b(?:\s|\/\*[\S\s]*?\*\/|\/\/[^\n\r]*[\n\r]+)*([^\s(/]*)/,l=i(c.exec);r&&!o&&s(a,"name",{configurable:!0,get:function(){try{return l(c,u(this))[1]}catch(e){return""}}})},2056:function(e,t,n){"use strict";function r(){console.log&&(console.log.apply?console.log.apply(console,Array.prototype.slice.call(arguments)):Function.prototype.apply.apply(console.log,[console,arguments])),console.trace&&console.trace()}n.d(t,{vF:function(){return v}}),n(2675),n(9463),n(2259),n(8706),n(1629),n(5276),n(3792),n(8598),n(4782),n(9089),n(739),n(3288),n(4170),n(2010),n(6099),n(3362),n(7495),n(7764),n(2953),n(6031),n(3296),n(7208),n(8408);var o="undefined"!=typeof window&&void 0!==window.navigator&&/Trident\/|MSIE /.test(window.navigator.userAgent),i=n(8678);function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}var a,u=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{u(r.next(e))}catch(e){i(e)}}function a(e){try{u(r.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}u((r=r.apply(e,t||[])).next())}))},c=function(e,t){var n,r,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,r=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!((o=(o=s.trys).length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=t.call(e,s)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}},l=function(){},d={};!function(e){e[e.TRACE=0]="TRACE",e[e.DEBUG=1]="DEBUG",e[e.INFO=2]="INFO",e[e.WARN=3]="WARN",e[e.ERROR=4]="ERROR",e[e.SILENT=5]="SILENT"}(a||(a={}));var p=function(){function e(e,t,n){this.name=e||"defaultLogger",this.currentLevel=0,this.useCookiePersist=!1,this.storageLogLevelKey="loglevel",this.levels=a,this.consoleLogVisibility=!0,this.logMethods=["trace","debug","info","warn","error"],this.methodFactory=n||this.defaultMethodFactory,this.report=!1;var r=this._getPersistedLevel();null==r&&(r=null===t?"WARN":t),this.logs=[],this.reportLogs=[],this.reportInterval=3e5,this.timer=null,this.config={useCache:!1,maxCache:3145728,color:"",background:""},this.logBytes=0,this.setLevel(r,!1,"")}return e.prototype.reportLog=function(){var e,t,n;return u(this,void 0,void 0,(function(){var r,o,s,a,u,l,d,p;return c(this,(function(c){switch(c.label){case 0:if(0===(null===(e=this.reportLogs)||void 0===e?void 0:e.length))return[2];for(r=2097152,o=this.reportLogs.join("\n")+"\n",s=o.length,a=[];s>r;)a.push(o.substring(0,r)),s-=r,o=o.substring(r);a.push(o),u=0,l=a,c.label=1;case 1:if(!(u<l.length))return[3,8];if(d=l[u],this.reportLogs=[],!(null===(n=null===(t=this.connection)||void 0===t?void 0:t.context)||void 0===n?void 0:n.accessToken))return[2];c.label=2;case 2:return c.trys.push([2,4,,5]),[4,this.reportData(d)];case 3:return"ok"!==(null==(p=c.sent())?void 0:p.status)&&this.reportLogs.unshift(d),[3,5];case 4:return c.sent(),this.reportLogs.unshift(d),[3,5];case 5:return[4,i.Wp.delay(3e3)];case 6:c.sent(),c.label=7;case 7:return u++,[3,1];case 8:return[2]}}))}))},e.prototype.reportData=function(e){var t,n=this;if(this.connection){var r=this.connection.context||{},o=r.orgName,s=r.appName,a=r.accessToken,u=r.userId;if(o&&s){var c={url:"".concat(null===(t=this.connection)||void 0===t?void 0:t.apiUrl,"/").concat(o,"/").concat(s,"/sdk/users/").concat(u,"/client/logs"),type:"POST",data:JSON.stringify({resource:this.connection.clientResource||"random_".concat(Date.now()),logContent:e}),dataType:"json",headers:{Authorization:"Bearer "+a,"Content-Type":"application/json"}};return i.RD.call(this.connection,c).then((function(e){return n.log("report log success",e),e})).catch((function(e){n.error("report log error",e)}))}}else this.error("report log error","connection is null")},e.prototype._regularlyReportLogs=function(){var e,t=this;this.timer&&clearInterval(this.timer),(null!==(e=this.reportInterval)&&void 0!==e?e:0)<6e4&&(this.reportInterval=6e4),this.timer=setInterval((function(){t.reportLog()}),this.reportInterval||3e5)},e.prototype._stopReportLogs=function(){return u(this,void 0,void 0,(function(){return c(this,(function(e){switch(e.label){case 0:return e.trys.push([0,2,,3]),[4,this.reportLog()];case 1:return e.sent(),[3,3];case 2:return e.sent(),this.error("report log error when stopping upload"),[3,3];case 3:return this.reportLogs=[],clearInterval(this.timer),[2]}}))}))},e.prototype.setConfig=function(e){this.config=e},e.prototype.getLevel=function(){return this.currentLevel},e.prototype.setLevel=function(e,t,n){if("string"==typeof e&&(e=a[e]),void 0===e&&(e=0),!("number"==typeof e&&e>=0&&e<=this.levels.SILENT))throw Error("log.setLevel() called with invalid level: "+e);if(this.currentLevel=e,!1!==t&&this._persistLevel(e),this.replaceLoggingMethods(e,n||""),"undefined"==typeof console&&e<a.SILENT)throw Error("No console available for logging")},e.prototype.setDefaultLevel=function(e){this._getPersistedLevel()||this.setLevel(e,!1,"")},e.prototype.enableAll=function(e){this.setLevel(this.levels.TRACE,!0,"")},e.prototype.disableAll=function(e){this.setLevel(this.levels.SILENT,!0,"")},e.prototype.getLogs=function(){return this.logs},e.prototype.download=function(){if("undefined"!=typeof window&&"undefined"!=typeof document){var e=this.getLogs().join("\n"),t=new Blob([e],{type:"text/plain;charset=UTF-8"}),n=window.URL.createObjectURL(t),r=document.createElement("a");r.style.display="none",r.href=n,r.setAttribute("download","sdklog"),document.body.appendChild(r),r.click()}},e.prototype.setConsoleLogVisibility=function(e){this.consoleLogVisibility=e},e.prototype._bindMethod=function(e,t,n){var r=this,o=e[t],i=this.getTime();if(n)return this._cacheLog;if("function"==typeof o.bind)return function(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];var s=r.getTime();r.consoleLogVisibility&&o.call.apply(o,function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}([e,"".concat(s," IM SDK [").concat("log"===t?"debug":t,"]: ")],n,!1)),r.onLog&&r.onLog({time:s,level:"log"===t?"debug":t,logs:n}),r._cacheReportLogs.apply(r,n)};try{return Function.prototype.bind.call(o,e,"".concat(i," IM SDK [").concat("log"===t?"debug":t,"]: "))}catch(t){return function(){return Function.prototype.apply.apply(o,[e,arguments])}}},e.prototype.getTime=function(){var e=new Date;return e.toTimeString().split(" ")[0]+":"+e.getMilliseconds()},e.prototype._cacheLog=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=(new Date).toLocaleString()+": ",r="";e.forEach((function(e){"object"===s(e)?r+=JSON.stringify(e)+" ":r+=e+" "})),this._cacheLogCall(n+r),this._cacheReportLogs.apply(this,e)},e.prototype._cacheLogCall=function(e){var t=e.length,n=this.logBytes+t,r=this.config.maxCache;if(!(t>=r)){if(n<r)this.logBytes+=t;else for(var o=n-r,i=0;i<o;){var s=this.logs.shift();void 0!==s&&(i+=s.length)}this.logs.push(e)}},e.prototype._cacheReportLogs=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];if(this.report){var n=(new Date).toLocaleString()+": ",r="";e.forEach((function(e){"object"===s(e)?r+=JSON.stringify(e)+" ":r+=e+" "})),this.reportLogs.push(n+r)}},e.prototype._getPersistedLevel=function(){var e;if("undefined"==typeof window)return 5;if("undefined"===(e=window&&window.localStorage&&window.localStorage[this.storageLogLevelKey])){var t=window.document.cookie,n=t.indexOf(encodeURIComponent(this.storageLogLevelKey));-1!==n&&(e=/^([^;]+)/.exec(t.slice(n))[1])}return e||5},e.prototype._persistLevel=function(e){var t=this.logMethods[e]||"SILENT";if("undefined"!=typeof window){if(window.localStorage)try{window.localStorage[this.storageLogLevelKey]=t}catch(e){console.log(e)}this.useCookiePersist&&(window.document.cookie=encodeURIComponent(this.storageLogLevelKey)+"="+t+";")}},e.prototype.replaceLoggingMethods=function(e,t){for(var n=this,r=0;r<this.logMethods.length;r++){var o=this.logMethods[r];this[o]=r<e?function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];n.report&&n._cacheReportLogs.apply(n,e)}:this.methodFactory(o,e,t)}this.log=this.debug},e.prototype.defaultMethodFactory=function(e,t,n){return this.realMethod(e)||this.enableLoggingWhenConsoleArrives.apply(this,[e,t,n])},e.prototype.realMethod=function(e){return"debug"===e&&(e="log"),"undefined"!=typeof console&&("trace"===e&&o?r:void 0!==console[e]?this._bindMethod(console,e,this.config.useCache):void 0!==console.log?this._bindMethod(console,"log",this.config.useCache):l)},e.prototype.enableLoggingWhenConsoleArrives=function(e,t,n){return function(){"undefined"!=typeof console&&(this.replaceLoggingMethods.call(this,t,n),this[e].apply(this,arguments))}.bind(this)},e}(),h=new p;h.getLogger=function(e){if("string"!=typeof e||""===e)throw new TypeError("You must supply a name when creating a logger.");return this};var f="undefined"!=typeof window?window.log:void 0;h.noConflict=function(){return"undefined"!=typeof window&&window.log===h&&(window.log=f),h},h.getLoggers=function(){return d},h.initReport=function(e){var t=e.report,n=e.reportInterval,r=e.connection;h.report=t,h.reportInterval=n,h.connection=r,t&&h._regularlyReportLogs()};var v=h},2062:function(e,t,n){"use strict";var r=n(6518),o=n(9213).map;r({target:"Array",proto:!0,forced:!n(597)("map")},{map:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}})},2087:function(e,t,n){"use strict";var r=n(34),o=Math.floor;e.exports=Number.isInteger||function(e){return!r(e)&&isFinite(e)&&o(e)===e}},2106:function(e,t,n){"use strict";var r=n(283),o=n(4913);e.exports=function(e,t,n){return n.get&&r(n.get,t,{getter:!0}),n.set&&r(n.set,t,{setter:!0}),o.f(e,t,n)}},2140:function(e,t,n){"use strict";var r={};r[n(8227)("toStringTag")]="z",e.exports="[object z]"===String(r)},2170:function(e,t,n){"use strict";var r=n(4644),o=n(9213).every,i=r.aTypedArray;(0,r.exportTypedArrayMethod)("every",(function(e){return o(i(this),e,arguments.length>1?arguments[1]:void 0)}))},2195:function(e,t,n){"use strict";var r=n(9504),o=r({}.toString),i=r("".slice);e.exports=function(e){return i(o(e),8,-1)}},2211:function(e,t,n){"use strict";var r=n(9039);e.exports=!r((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype}))},2239:function(e,t,n){"use strict";e.exports=o;var r=n(3610);function o(e,t){this.lo=e>>>0,this.hi=t>>>0}var i=o.zero=new o(0,0);i.toNumber=function(){return 0},i.zzEncode=i.zzDecode=function(){return this},i.length=function(){return 1};var s=o.zeroHash="\0\0\0\0\0\0\0\0";o.fromNumber=function(e){if(0===e)return i;var t=e<0;t&&(e=-e);var n=e>>>0,r=(e-n)/4294967296>>>0;return t&&(r=~r>>>0,n=~n>>>0,++n>4294967295&&(n=0,++r>4294967295&&(r=0))),new o(n,r)},o.from=function(e){if("number"==typeof e)return o.fromNumber(e);if(r.isString(e)){if(!r.Long)return o.fromNumber(parseInt(e,10));e=r.Long.fromString(e)}return e.low||e.high?new o(e.low>>>0,e.high>>>0):i},o.prototype.toNumber=function(e){if(!e&&this.hi>>>31){var t=1+~this.lo>>>0,n=~this.hi>>>0;return t||(n=n+1>>>0),-(t+4294967296*n)}return this.lo+4294967296*this.hi},o.prototype.toLong=function(e){return r.Long?new r.Long(0|this.lo,0|this.hi,Boolean(e)):{low:0|this.lo,high:0|this.hi,unsigned:Boolean(e)}};var a=String.prototype.charCodeAt;o.fromHash=function(e){return e===s?i:new o((a.call(e,0)|a.call(e,1)<<8|a.call(e,2)<<16|a.call(e,3)<<24)>>>0,(a.call(e,4)|a.call(e,5)<<8|a.call(e,6)<<16|a.call(e,7)<<24)>>>0)},o.prototype.toHash=function(){return String.fromCharCode(255&this.lo,this.lo>>>8&255,this.lo>>>16&255,this.lo>>>24,255&this.hi,this.hi>>>8&255,this.hi>>>16&255,this.hi>>>24)},o.prototype.zzEncode=function(){var e=this.hi>>31;return this.hi=((this.hi<<1|this.lo>>>31)^e)>>>0,this.lo=(this.lo<<1^e)>>>0,this},o.prototype.zzDecode=function(){var e=-(1&this.lo);return this.lo=((this.lo>>>1|this.hi<<31)^e)>>>0,this.hi=(this.hi>>>1^e)>>>0,this},o.prototype.length=function(){var e=this.lo,t=(this.lo>>>28|this.hi<<4)>>>0,n=this.hi>>>24;return 0===n?0===t?e<16384?e<128?1:2:e<2097152?3:4:t<16384?t<128?5:6:t<2097152?7:8:n<128?9:10}},2259:function(e,t,n){"use strict";n(511)("iterator")},2293:function(e,t,n){"use strict";var r=n(8551),o=n(5548),i=n(4117),s=n(8227)("species");e.exports=function(e,t){var n,a=r(e).constructor;return void 0===a||i(n=r(a)[s])?t:o(n)}},2333:function(e,t,n){"use strict";var r=n(1291),o=n(655),i=n(7750),s=RangeError;e.exports=function(e){var t=o(i(this)),n="",a=r(e);if(a<0||a===1/0)throw new s("Wrong number of repetitions");for(;a>0;(a>>>=1)&&(t+=t))1&a&&(n+=t);return n}},2357:function(e,t,n){"use strict";var r=n(3724),o=n(9039),i=n(9504),s=n(2787),a=n(1072),u=n(5397),c=i(n(8773).f),l=i([].push),d=r&&o((function(){var e=Object.create(null);return e[2]=2,!c(e,2)})),p=function(e){return function(t){for(var n,o=u(t),i=a(o),p=d&&null===s(o),h=i.length,f=0,v=[];h>f;)n=i[f++],r&&!(p?n in o:c(o,n))||l(v,e?[n,o[n]]:o[n]);return v}};e.exports={entries:p(!0),values:p(!1)}},2360:function(e,t,n){"use strict";var r,o=n(8551),i=n(6801),s=n(8727),a=n(421),u=n(397),c=n(4055),l=n(6119),d="prototype",p="script",h=l("IE_PROTO"),f=function(){},v=function(e){return"<"+p+">"+e+"</"+p+">"},E=function(e){e.write(v("")),e.close();var t=e.parentWindow.Object;return e=null,t},m=function(){try{r=new ActiveXObject("htmlfile")}catch(e){}var e,t,n;m="undefined"!=typeof document?document.domain&&r?E(r):(t=c("iframe"),n="java"+p+":",t.style.display="none",u.appendChild(t),t.src=String(n),(e=t.contentWindow.document).open(),e.write(v("document.F=Object")),e.close(),e.F):E(r);for(var o=s.length;o--;)delete m[d][s[o]];return m()};a[h]=!0,e.exports=Object.create||function(e,t){var n;return null!==e?(f[d]=o(e),n=new f,f[d]=null,n[h]=e):n=m(),void 0===t?n:i.f(n,t)}},2529:function(e){"use strict";e.exports=function(e,t){return{value:e,done:t}}},2549:function(e,t,n){"use strict";e.exports=n(9100)},2551:function(e,t,n){"use strict";e.exports=o;var r=n(3610);function o(e){if(e)for(var t=Object.keys(e),n=0;n<t.length;++n)this[t[n]]=e[t[n]]}o.create=function(e){return this.$type.create(e)},o.encode=function(e,t){return this.$type.encode(e,t)},o.encodeDelimited=function(e,t){return this.$type.encodeDelimited(e,t)},o.decode=function(e){return this.$type.decode(e)},o.decodeDelimited=function(e){return this.$type.decodeDelimited(e)},o.verify=function(e){return this.$type.verify(e)},o.fromObject=function(e){return this.$type.fromObject(e)},o.toObject=function(e,t){return this.$type.toObject(e,t)},o.prototype.toJSON=function(){return this.$type.toObject(this,r.toJSONOptions)}},2637:function(e,t,n){"use strict";n(6518)({target:"Number",stat:!0},{isInteger:n(2087)})},2652:function(e,t,n){"use strict";var r=n(6080),o=n(9565),i=n(8551),s=n(6823),a=n(4209),u=n(6198),c=n(1625),l=n(81),d=n(851),p=n(9539),h=TypeError,f=function(e,t){this.stopped=e,this.result=t},v=f.prototype;e.exports=function(e,t,n){var E,m,g,y,_,O,T,R=n&&n.that,I=!(!n||!n.AS_ENTRIES),C=!(!n||!n.IS_RECORD),S=!(!n||!n.IS_ITERATOR),N=!(!n||!n.INTERRUPTED),A=r(t,R),b=function(e){return E&&p(E,"normal",e),new f(!0,e)},M=function(e){return I?(i(e),N?A(e[0],e[1],b):A(e[0],e[1])):N?A(e,b):A(e)};if(C)E=e.iterator;else if(S)E=e;else{if(!(m=d(e)))throw new h(s(e)+" is not iterable");if(a(m)){for(g=0,y=u(e);y>g;g++)if((_=M(e[g]))&&c(v,_))return _;return new f(!1)}E=l(e,m)}for(O=C?e.next:E.next;!(T=o(O,E)).done;){try{_=M(T.value)}catch(e){p(E,"throw",e)}if("object"==typeof _&&_&&c(v,_))return _}return new f(!1)}},2675:function(e,t,n){"use strict";n(6761),n(1510),n(7812),n(3110),n(9773)},2712:function(e,t,n){"use strict";var r=n(6518),o=n(926).left,i=n(4598),s=n(9519);r({target:"Array",proto:!0,forced:!n(6193)&&s>79&&s<83||!i("reduce")},{reduce:function(e){var t=arguments.length;return o(this,e,t,t>1?arguments[1]:void 0)}})},2744:function(e,t,n){"use strict";var r=n(9039);e.exports=!r((function(){return Object.isExtensible(Object.preventExtensions({}))}))},2777:function(e,t,n){"use strict";var r=n(9565),o=n(34),i=n(757),s=n(5966),a=n(4270),u=n(8227),c=TypeError,l=u("toPrimitive");e.exports=function(e,t){if(!o(e)||i(e))return e;var n,u=s(e,l);if(u){if(void 0===t&&(t="default"),n=r(u,e,t),!o(n)||i(n))return n;throw new c("Can't convert object to primitive value")}return void 0===t&&(t="number"),a(e,t)}},2787:function(e,t,n){"use strict";var r=n(9297),o=n(4901),i=n(8981),s=n(6119),a=n(2211),u=s("IE_PROTO"),c=Object,l=c.prototype;e.exports=a?c.getPrototypeOf:function(e){var t=i(e);if(r(t,u))return t[u];var n=t.constructor;return o(n)&&t instanceof n?n.prototype:t instanceof c?l:null}},2796:function(e,t,n){"use strict";var r=n(9039),o=n(4901),i=/#|\.prototype\./,s=function(e,t){var n=u[a(e)];return n===l||n!==c&&(o(t)?r(t):!!t)},a=s.normalize=function(e){return String(e).replace(i,".").toLowerCase()},u=s.data={},c=s.NATIVE="N",l=s.POLYFILL="P";e.exports=s},2805:function(e,t,n){"use strict";var r=n(4576),o=n(9039),i=n(4428),s=n(4644).NATIVE_ARRAY_BUFFER_VIEWS,a=r.ArrayBuffer,u=r.Int8Array;e.exports=!s||!o((function(){u(1)}))||!o((function(){new u(-1)}))||!i((function(e){new u,new u(null),new u(1.5),new u(e)}),!0)||o((function(){return 1!==new u(new a(2),1,void 0).length}))},2812:function(e){"use strict";var t=TypeError;e.exports=function(e,n){if(e<n)throw new t("Not enough arguments");return e}},2839:function(e,t,n){"use strict";var r=n(4576).navigator,o=r&&r.userAgent;e.exports=o?String(o):""},2887:function(e,t,n){"use strict";var r=n(4576),o=n(9039),i=n(9504),s=n(4644),a=n(3792),u=n(8227)("iterator"),c=r.Uint8Array,l=i(a.values),d=i(a.keys),p=i(a.entries),h=s.aTypedArray,f=s.exportTypedArrayMethod,v=c&&c.prototype,E=!o((function(){v[u].call([1])})),m=!!v&&v.values&&v[u]===v.values&&"values"===v.values.name,g=function(){return l(h(this))};f("entries",(function(){return p(h(this))}),E),f("keys",(function(){return d(h(this))}),E),f("values",g,E||!m,{name:"values"}),f(u,g,E||!m,{name:"values"})},2892:function(e,t,n){"use strict";var r=n(6518),o=n(6395),i=n(3724),s=n(4576),a=n(9167),u=n(9504),c=n(2796),l=n(9297),d=n(3167),p=n(1625),h=n(757),f=n(2777),v=n(9039),E=n(8480).f,m=n(7347).f,g=n(4913).f,y=n(1240),_=n(3802).trim,O="Number",T=s[O],R=a[O],I=T.prototype,C=s.TypeError,S=u("".slice),N=u("".charCodeAt),A=c(O,!T(" 0o1")||!T("0b1")||T("+0x1")),b=function(e){var t,n=arguments.length<1?0:T(function(e){var t=f(e,"number");return"bigint"==typeof t?t:function(e){var t,n,r,o,i,s,a,u,c=f(e,"number");if(h(c))throw new C("Cannot convert a Symbol value to a number");if("string"==typeof c&&c.length>2)if(c=_(c),43===(t=N(c,0))||45===t){if(88===(n=N(c,2))||120===n)return NaN}else if(48===t){switch(N(c,1)){case 66:case 98:r=2,o=49;break;case 79:case 111:r=8,o=55;break;default:return+c}for(s=(i=S(c,2)).length,a=0;a<s;a++)if((u=N(i,a))<48||u>o)return NaN;return parseInt(i,r)}return+c}(t)}(e));return p(I,t=this)&&v((function(){y(t)}))?d(Object(n),this,b):n};b.prototype=I,A&&!o&&(I.constructor=b),r({global:!0,constructor:!0,wrap:!0,forced:A},{Number:b});var M=function(e,t){for(var n,r=i?E(t):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),o=0;r.length>o;o++)l(t,n=r[o])&&!l(e,n)&&g(e,n,m(t,n))};o&&R&&M(a[O],R),(A||o)&&M(a[O],T)},2953:function(e,t,n){"use strict";var r=n(4576),o=n(7400),i=n(9296),s=n(3792),a=n(6699),u=n(687),c=n(8227)("iterator"),l=s.values,d=function(e,t){if(e){if(e[c]!==l)try{a(e,c,l)}catch(t){e[c]=l}if(u(e,t,!0),o[t])for(var n in s)if(e[n]!==s[n])try{a(e,n,s[n])}catch(t){e[n]=s[n]}}};for(var p in o)d(r[p]&&r[p].prototype,p);d(i,"DOMTokenList")},2967:function(e,t,n){"use strict";var r=n(6706),o=n(34),i=n(7750),s=n(3506);e.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{(e=r(Object.prototype,"__proto__","set"))(n,[]),t=n instanceof Array}catch(e){}return function(n,r){return i(n),s(r),o(n)?(t?e(n,r):n.__proto__=r,n):n}}():void 0)},3110:function(e,t,n){"use strict";var r=n(6518),o=n(7751),i=n(8745),s=n(9565),a=n(9504),u=n(9039),c=n(4901),l=n(757),d=n(7680),p=n(6933),h=n(4495),f=String,v=o("JSON","stringify"),E=a(/./.exec),m=a("".charAt),g=a("".charCodeAt),y=a("".replace),_=a(1..toString),O=/[\uD800-\uDFFF]/g,T=/^[\uD800-\uDBFF]$/,R=/^[\uDC00-\uDFFF]$/,I=!h||u((function(){var e=o("Symbol")("stringify detection");return"[null]"!==v([e])||"{}"!==v({a:e})||"{}"!==v(Object(e))})),C=u((function(){return'"\\udf06\\ud834"'!==v("\udf06\ud834")||'"\\udead"'!==v("\udead")})),S=function(e,t){var n=d(arguments),r=p(t);if(c(r)||void 0!==e&&!l(e))return n[1]=function(e,t){if(c(r)&&(t=s(r,this,f(e),t)),!l(t))return t},i(v,null,n)},N=function(e,t,n){var r=m(n,t-1),o=m(n,t+1);return E(T,e)&&!E(R,o)||E(R,e)&&!E(T,r)?"\\u"+_(g(e,0),16):e};v&&r({target:"JSON",stat:!0,arity:3,forced:I||C},{stringify:function(e,t,n){var r=d(arguments),o=i(I?S:v,null,r);return C&&"string"==typeof o?y(o,O,N):o}})},3138:function(e){"use strict";e.exports=function(e,t){try{1===arguments.length?console.error(e):console.error(e,t)}catch(e){}}},3158:function(e,t,n){"use strict";e.exports=i;var r=n(6237);(i.prototype=Object.create(r.prototype)).constructor=i;var o=n(3610);function i(e){r.call(this,e)}i._configure=function(){o.Buffer&&(i.prototype._slice=o.Buffer.prototype.slice)},i.prototype.string=function(){var e=this.uint32();return this.buf.utf8Slice?this.buf.utf8Slice(this.pos,this.pos=Math.min(this.pos+e,this.len)):this.buf.toString("utf-8",this.pos,this.pos=Math.min(this.pos+e,this.len))},i._configure()},3164:function(e,t,n){"use strict";var r=n(7782),o=n(3602),i=Math.abs;e.exports=function(e,t,n,s){var a=+e,u=i(a),c=r(a);if(u<s)return c*o(u/s/t)*s*t;var l=(1+t/2220446049250313e-31)*u,d=l-(l-u);return d>n||d!=d?c*(1/0):c*d}},3167:function(e,t,n){"use strict";var r=n(4901),o=n(34),i=n(2967);e.exports=function(e,t,n){var s,a;return i&&r(s=t.constructor)&&s!==n&&o(a=s.prototype)&&a!==n.prototype&&i(e,a),e}},3179:function(e,t,n){"use strict";var r=n(2140),o=n(6955);e.exports=r?{}.toString:function(){return"[object "+o(this)+"]"}},3206:function(e,t,n){"use strict";var r=n(4644),o=n(9213).forEach,i=r.aTypedArray;(0,r.exportTypedArrayMethod)("forEach",(function(e){o(i(this),e,arguments.length>1?arguments[1]:void 0)}))},3251:function(e,t,n){"use strict";var r=n(6080),o=n(9565),i=n(5548),s=n(8981),a=n(6198),u=n(81),c=n(851),l=n(4209),d=n(1108),p=n(4644).aTypedArrayConstructor,h=n(5854);e.exports=function(e){var t,n,f,v,E,m,g,y,_=i(this),O=s(e),T=arguments.length,R=T>1?arguments[1]:void 0,I=void 0!==R,C=c(O);if(C&&!l(C))for(y=(g=u(O,C)).next,O=[];!(m=o(y,g)).done;)O.push(m.value);for(I&&T>2&&(R=r(R,arguments[2])),n=a(O),f=new(p(_))(n),v=d(f),t=0;n>t;t++)E=I?R(O[t],t):O[t],f[t]=v?h(E):+E;return f}},3262:function(e,t,n){"use strict";var r,o,i=e.exports=n(3610),s=n(4529);i.codegen=n(8561),i.fetch=n(5212),i.path=n(9207),i.fs=i.inquire("fs"),i.toArray=function(e){if(e){for(var t=Object.keys(e),n=new Array(t.length),r=0;r<t.length;)n[r]=e[t[r++]];return n}return[]},i.toObject=function(e){for(var t={},n=0;n<e.length;){var r=e[n++],o=e[n++];void 0!==o&&(t[r]=o)}return t};var a=/\\/g,u=/"/g;i.isReserved=function(e){return/^(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$/.test(e)},i.safeProp=function(e){return!/^[$\w_]+$/.test(e)||i.isReserved(e)?'["'+e.replace(a,"\\\\").replace(u,'\\"')+'"]':"."+e},i.ucFirst=function(e){return e.charAt(0).toUpperCase()+e.substring(1)};var c=/_([a-z])/g;i.camelCase=function(e){return e.substring(0,1)+e.substring(1).replace(c,(function(e,t){return t.toUpperCase()}))},i.compareFieldsById=function(e,t){return e.id-t.id},i.decorateType=function(e,t){if(e.$type)return t&&e.$type.name!==t&&(i.decorateRoot.remove(e.$type),e.$type.name=t,i.decorateRoot.add(e.$type)),e.$type;r||(r=n(7882));var o=new r(t||e.name);return i.decorateRoot.add(o),o.ctor=e,Object.defineProperty(e,"$type",{value:o,enumerable:!1}),Object.defineProperty(e.prototype,"$type",{value:o,enumerable:!1}),o};var l=0;i.decorateEnum=function(e){if(e.$type)return e.$type;o||(o=n(5643));var t=new o("Enum"+l++,e);return i.decorateRoot.add(t),Object.defineProperty(e,"$type",{value:t,enumerable:!1}),t},i.setProperty=function(e,t,n){if("object"!=typeof e)throw TypeError("dst must be an object");if(!t)throw TypeError("path must be specified");return function e(t,n,r){var o=n.shift();if("__proto__"===o||"prototype"===o)return t;if(n.length>0)t[o]=e(t[o]||{},n,r);else{var i=t[o];i&&(r=[].concat(i).concat(r)),t[o]=r}return t}(e,t=t.split("."),n)},Object.defineProperty(i,"decorateRoot",{get:function(){return s.decorated||(s.decorated=new(n(5330)))}})},3288:function(e,t,n){"use strict";var r=n(9504),o=n(6840),i=Date.prototype,s="Invalid Date",a="toString",u=r(i[a]),c=r(i.getTime);String(new Date(NaN))!==s&&o(i,a,(function(){var e=c(this);return e==e?u(this):s}))},3296:function(e,t,n){"use strict";n(5806)},3362:function(e,t,n){"use strict";n(436),n(6499),n(2003),n(7743),n(1481),n(280)},3389:function(e,t,n){"use strict";var r=n(4576),o=n(3724),i=Object.getOwnPropertyDescriptor;e.exports=function(e){if(!o)return r[e];var t=i(r,e);return t&&t.value}},3392:function(e,t,n){"use strict";var r=n(9504),o=0,i=Math.random(),s=r(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+s(++o+i,36)}},3418:function(e,t,n){"use strict";var r=n(6518),o=n(7916);r({target:"Array",stat:!0,forced:!n(4428)((function(e){Array.from(e)}))},{from:o})},3438:function(e,t,n){"use strict";var r=n(8551),o=n(34),i=n(6043);e.exports=function(e,t){if(r(e),o(t)&&t.constructor===e)return t;var n=i.f(e);return(0,n.resolve)(t),n.promise}},3449:function(e,t,n){"use strict";e.exports=d;var r,o=n(3610),i=o.LongBits,s=o.base64,a=o.utf8;function u(e,t,n){this.fn=e,this.len=t,this.next=void 0,this.val=n}function c(){}function l(e){this.head=e.head,this.tail=e.tail,this.len=e.len,this.next=e.states}function d(){this.len=0,this.head=new u(c,0,0),this.tail=this.head,this.states=null}var p=function(){return o.Buffer?function(){return(d.create=function(){return new r})()}:function(){return new d}};function h(e,t,n){t[n]=255&e}function f(e,t){this.len=e,this.next=void 0,this.val=t}function v(e,t,n){for(;e.hi;)t[n++]=127&e.lo|128,e.lo=(e.lo>>>7|e.hi<<25)>>>0,e.hi>>>=7;for(;e.lo>127;)t[n++]=127&e.lo|128,e.lo=e.lo>>>7;t[n++]=e.lo}function E(e,t,n){t[n]=255&e,t[n+1]=e>>>8&255,t[n+2]=e>>>16&255,t[n+3]=e>>>24}d.create=p(),d.alloc=function(e){return new o.Array(e)},o.Array!==Array&&(d.alloc=o.pool(d.alloc,o.Array.prototype.subarray)),d.prototype._push=function(e,t,n){return this.tail=this.tail.next=new u(e,t,n),this.len+=t,this},f.prototype=Object.create(u.prototype),f.prototype.fn=function(e,t,n){for(;e>127;)t[n++]=127&e|128,e>>>=7;t[n]=e},d.prototype.uint32=function(e){return this.len+=(this.tail=this.tail.next=new f((e>>>=0)<128?1:e<16384?2:e<2097152?3:e<268435456?4:5,e)).len,this},d.prototype.int32=function(e){return e<0?this._push(v,10,i.fromNumber(e)):this.uint32(e)},d.prototype.sint32=function(e){return this.uint32((e<<1^e>>31)>>>0)},d.prototype.uint64=function(e){var t=i.from(e);return this._push(v,t.length(),t)},d.prototype.int64=d.prototype.uint64,d.prototype.sint64=function(e){var t=i.from(e).zzEncode();return this._push(v,t.length(),t)},d.prototype.bool=function(e){return this._push(h,1,e?1:0)},d.prototype.fixed32=function(e){return this._push(E,4,e>>>0)},d.prototype.sfixed32=d.prototype.fixed32,d.prototype.fixed64=function(e){var t=i.from(e);return this._push(E,4,t.lo)._push(E,4,t.hi)},d.prototype.sfixed64=d.prototype.fixed64,d.prototype.float=function(e){return this._push(o.float.writeFloatLE,4,e)},d.prototype.double=function(e){return this._push(o.float.writeDoubleLE,8,e)};var m=o.Array.prototype.set?function(e,t,n){t.set(e,n)}:function(e,t,n){for(var r=0;r<e.length;++r)t[n+r]=e[r]};d.prototype.bytes=function(e){var t=e.length>>>0;if(!t)return this._push(h,1,0);if(o.isString(e)){var n=d.alloc(t=s.length(e));s.decode(e,n,0),e=n}return this.uint32(t)._push(m,t,e)},d.prototype.string=function(e){var t=a.length(e);return t?this.uint32(t)._push(a.write,t,e):this._push(h,1,0)},d.prototype.fork=function(){return this.states=new l(this),this.head=this.tail=new u(c,0,0),this.len=0,this},d.prototype.reset=function(){return this.states?(this.head=this.states.head,this.tail=this.states.tail,this.len=this.states.len,this.states=this.states.next):(this.head=this.tail=new u(c,0,0),this.len=0),this},d.prototype.ldelim=function(){var e=this.head,t=this.tail,n=this.len;return this.reset().uint32(n),n&&(this.tail.next=e.next,this.tail=t,this.len+=n),this},d.prototype.finish=function(){for(var e=this.head.next,t=this.constructor.alloc(this.len),n=0;e;)e.fn(e.val,t,n),n+=e.len,e=e.next;return t},d._configure=function(e){r=e,d.create=p(),r._configure()}},3451:function(e,t,n){"use strict";var r=n(6518),o=n(9504),i=n(421),s=n(34),a=n(9297),u=n(4913).f,c=n(8480),l=n(298),d=n(4124),p=n(3392),h=n(2744),f=!1,v=p("meta"),E=0,m=function(e){u(e,v,{value:{objectID:"O"+E++,weakData:{}}})},g=e.exports={enable:function(){g.enable=function(){},f=!0;var e=c.f,t=o([].splice),n={};n[v]=1,e(n).length&&(c.f=function(n){for(var r=e(n),o=0,i=r.length;o<i;o++)if(r[o]===v){t(r,o,1);break}return r},r({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:l.f}))},fastKey:function(e,t){if(!s(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!a(e,v)){if(!d(e))return"F";if(!t)return"E";m(e)}return e[v].objectID},getWeakData:function(e,t){if(!a(e,v)){if(!d(e))return!0;if(!t)return!1;m(e)}return e[v].weakData},onFreeze:function(e){return h&&f&&d(e)&&!a(e,v)&&m(e),e}};i[v]=!0},3500:function(e,t,n){"use strict";var r=n(4576),o=n(7400),i=n(9296),s=n(235),a=n(6699),u=function(e){if(e&&e.forEach!==s)try{a(e,"forEach",s)}catch(t){e.forEach=s}};for(var c in o)o[c]&&u(r[c]&&r[c].prototype);u(i)},3506:function(e,t,n){"use strict";var r=n(3925),o=String,i=TypeError;e.exports=function(e){if(r(e))return e;throw new i("Can't set "+o(e)+" as a prototype")}},3517:function(e,t,n){"use strict";var r=n(9504),o=n(9039),i=n(4901),s=n(6955),a=n(7751),u=n(3706),c=function(){},l=a("Reflect","construct"),d=/^\s*(?:class|function)\b/,p=r(d.exec),h=!d.test(c),f=function(e){if(!i(e))return!1;try{return l(c,[],e),!0}catch(e){return!1}},v=function(e){if(!i(e))return!1;switch(s(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return h||!!p(d,u(e))}catch(e){return!0}};v.sham=!0,e.exports=!l||o((function(){var e;return f(f.call)||!f(Object)||!f((function(){e=!0}))||e}))?v:f},3602:function(e){"use strict";var t=4503599627370496;e.exports=function(e){return e+t-t}},3607:function(e,t,n){"use strict";var r=n(2839).match(/AppleWebKit\/(\d+)\./);e.exports=!!r&&+r[1]},3610:function(e,t,n){"use strict";var r=t;function o(e,t,n){for(var r=Object.keys(t),o=0;o<r.length;++o)void 0!==e[r[o]]&&n||(e[r[o]]=t[r[o]]);return e}function i(e){function t(e,n){if(!(this instanceof t))return new t(e,n);Object.defineProperty(this,"message",{get:function(){return e}}),Error.captureStackTrace?Error.captureStackTrace(this,t):Object.defineProperty(this,"stack",{value:(new Error).stack||""}),n&&o(this,n)}return(t.prototype=Object.create(Error.prototype)).constructor=t,Object.defineProperty(t.prototype,"name",{get:function(){return e}}),t.prototype.toString=function(){return this.name+": "+this.message},t}r.asPromise=n(8045),r.base64=n(8839),r.EventEmitter=n(4358),r.float=n(9410),r.inquire=n(4153),r.utf8=n(1447),r.pool=n(9390),r.LongBits=n(2239),r.isNode=Boolean(void 0!==n.g&&n.g&&n.g.process&&n.g.process.versions&&n.g.process.versions.node),r.global=r.isNode&&n.g||"undefined"!=typeof window&&window||"undefined"!=typeof self&&self||this,r.emptyArray=Object.freeze?Object.freeze([]):[],r.emptyObject=Object.freeze?Object.freeze({}):{},r.isInteger=Number.isInteger||function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e},r.isString=function(e){return"string"==typeof e||e instanceof String},r.isObject=function(e){return e&&"object"==typeof e},r.isset=r.isSet=function(e,t){var n=e[t];return!(null==n||!e.hasOwnProperty(t))&&("object"!=typeof n||(Array.isArray(n)?n.length:Object.keys(n).length)>0)},r.Buffer=function(){try{var e=r.inquire("buffer").Buffer;return e.prototype.utf8Write?e:null}catch(e){return null}}(),r._Buffer_from=null,r._Buffer_allocUnsafe=null,r.newBuffer=function(e){return"number"==typeof e?r.Buffer?r._Buffer_allocUnsafe(e):new r.Array(e):r.Buffer?r._Buffer_from(e):"undefined"==typeof Uint8Array?e:new Uint8Array(e)},r.Array="undefined"!=typeof Uint8Array?Uint8Array:Array,r.Long=r.global.dcodeIO&&r.global.dcodeIO.Long||r.global.Long||r.inquire("long"),r.key2Re=/^true|false|0|1$/,r.key32Re=/^-?(?:0|[1-9][0-9]*)$/,r.key64Re=/^(?:[\\x00-\\xff]{8}|-?(?:0|[1-9][0-9]*))$/,r.longToHash=function(e){return e?r.LongBits.from(e).toHash():r.LongBits.zeroHash},r.longFromHash=function(e,t){var n=r.LongBits.fromHash(e);return r.Long?r.Long.fromBits(n.lo,n.hi,t):n.toNumber(Boolean(t))},r.merge=o,r.lcFirst=function(e){return e.charAt(0).toLowerCase()+e.substring(1)},r.newError=i,r.ProtocolError=i("ProtocolError"),r.oneOfGetter=function(e){for(var t={},n=0;n<e.length;++n)t[e[n]]=1;return function(){for(var e=Object.keys(this),n=e.length-1;n>-1;--n)if(1===t[e[n]]&&void 0!==this[e[n]]&&null!==this[e[n]])return e[n]}},r.oneOfSetter=function(e){return function(t){for(var n=0;n<e.length;++n)e[n]!==t&&delete this[e[n]]}},r.toJSONOptions={longs:String,enums:String,bytes:String,json:!0},r._configure=function(){var e=r.Buffer;e?(r._Buffer_from=e.from!==Uint8Array.from&&e.from||function(t,n){return new e(t,n)},r._Buffer_allocUnsafe=e.allocUnsafe||function(t){return new e(t)}):r._Buffer_from=r._Buffer_allocUnsafe=null}},3635:function(e,t,n){"use strict";var r=n(9039),o=n(4576).RegExp;e.exports=r((function(){var e=o(".","s");return!(e.dotAll&&e.test("\n")&&"s"===e.flags)}))},3684:function(e,t,n){"use strict";var r=n(4644).exportTypedArrayMethod,o=n(9039),i=n(4576),s=n(9504),a=i.Uint8Array,u=a&&a.prototype||{},c=[].toString,l=s([].join);o((function(){c.call({})}))&&(c=function(){return l(this)});var d=u.toString!==c;r("toString",c,d)},3706:function(e,t,n){"use strict";var r=n(9504),o=n(4901),i=n(7629),s=r(Function.toString);o(i.inspectSource)||(i.inspectSource=function(e){return s(e)}),e.exports=i.inspectSource},3709:function(e,t,n){"use strict";var r=n(2839).match(/firefox\/(\d+)/i);e.exports=!!r&&+r[1]},3717:function(e,t){"use strict";t.f=Object.getOwnPropertySymbols},3724:function(e,t,n){"use strict";var r=n(9039);e.exports=!r((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},3763:function(e,t,n){"use strict";var r=n(2839);e.exports=/MSIE|Trident/.test(r)},3792:function(e,t,n){"use strict";var r=n(5397),o=n(6469),i=n(6269),s=n(1181),a=n(4913).f,u=n(1088),c=n(2529),l=n(6395),d=n(3724),p="Array Iterator",h=s.set,f=s.getterFor(p);e.exports=u(Array,"Array",(function(e,t){h(this,{type:p,target:r(e),index:0,kind:t})}),(function(){var e=f(this),t=e.target,n=e.index++;if(!t||n>=t.length)return e.target=null,c(void 0,!0);switch(e.kind){case"keys":return c(n,!1);case"values":return c(t[n],!1)}return c([n,t[n]],!1)}),"values");var v=i.Arguments=i.Array;if(o("keys"),o("values"),o("entries"),!l&&d&&"values"!==v.name)try{a(v,"name",{value:"values"})}catch(e){}},3802:function(e,t,n){"use strict";var r=n(9504),o=n(7750),i=n(655),s=n(7452),a=r("".replace),u=RegExp("^["+s+"]+"),c=RegExp("(^|[^"+s+"])["+s+"]+$"),l=function(e){return function(t){var n=i(o(t));return 1&e&&(n=a(n,u,"")),2&e&&(n=a(n,c,"$1")),n}};e.exports={start:l(1),end:l(2),trim:l(3)}},3887:function(e,t,n){var r,o,i;!function(s){"use strict";if(null!=t&&"number"!=typeof t.nodeType)e.exports=s();else if(null!=n.amdO)o=[],void 0===(i="function"==typeof(r=s)?r.apply(t,o):r)||(e.exports=i);else{var a=s(),u="undefined"!=typeof self?self:$.global;"function"!=typeof u.btoa&&(u.btoa=a.btoa),"function"!=typeof u.atob&&(u.atob=a.atob)}}((function(){"use strict";var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function t(e){this.message=e}return t.prototype=new Error,t.prototype.name="InvalidCharacterError",{btoa:function(n){for(var r,o,i=String(n),s=0,a=e,u="";i.charAt(0|s)||(a="=",s%1);u+=a.charAt(63&r>>8-s%1*8)){if((o=i.charCodeAt(s+=3/4))>255)throw new t("'btoa' failed: The string to be encoded contains characters outside of the Latin1 range.");r=r<<8|o}return u},atob:function(n){var r=String(n).replace(/[=]+$/,"");if(r.length%4==1)throw new t("'atob' failed: The string to be decoded is not correctly encoded.");for(var o,i,s=0,a=0,u="";i=r.charAt(a++);~i&&(o=s%4?64*o+i:i,s++%4)?u+=String.fromCharCode(255&o>>(-2*s&6)):0)i=e.indexOf(i);return u}}}))},3893:function(e,t,n){"use strict";var r,o,i,s,a,u,c;n.d(t,{G8:function(){return a},He:function(){return c},S5:function(){return r},TT:function(){return i},Wz:function(){return u},jz:function(){return o},z1:function(){return s}}),function(e){e[e.UNKNOWOPERATION=-1]="UNKNOWOPERATION",e[e.REST_GET_SESSION_LIST=1]="REST_GET_SESSION_LIST",e[e.REST_DEL_SESSION=2]="REST_DEL_SESSION",e[e.REST_GET_HISTORY_MESSAGE=3]="REST_GET_HISTORY_MESSAGE",e[e.REST_PIN_CONVERSATION=4]="REST_PIN_CONVERSATION",e[e.REST_MARK_CONVERSATION=5]="REST_MARK_CONVERSATION",e[e.REST_UPLOAD_FILE_IN_PARTS=6]="REST_UPLOAD_FILE_IN_PARTS",e[e.REST_DELETE_MESSAGES_CONVERSATIONS=7]="REST_DELETE_MESSAGES_CONVERSATIONS",e[e.REST_PIN_MESSAGE=8]="REST_PIN_MESSAGE",e[e.REST_FETCH_PINMESSAGES=9]="REST_FETCH_PINMESSAGES",e[e.REST_FETCH_CONVERSATIONS=10]="REST_FETCH_CONVERSATIONS",e[e.REST_OPERATE=100]="REST_OPERATE",e[e.MSYNC_SENDMESSAGE=101]="MSYNC_SENDMESSAGE",e[e.MSYNC_RECALLMESSAGE=102]="MSYNC_RECALLMESSAGE",e[e.MSYNC_MODIFYMESSAGE=103]="MSYNC_MODIFYMESSAGE",e[e.MSYNC_OPERATE=200]="MSYNC_OPERATE",e[e.ROSTER_ADD=201]="ROSTER_ADD",e[e.ROSTER_REMOVE=202]="ROSTER_REMOVE",e[e.ROSTER_ACCEPT=203]="ROSTER_ACCEPT",e[e.ROSTER_DECLINE=204]="ROSTER_DECLINE",e[e.ROSTER_BAN=205]="ROSTER_BAN",e[e.ROSTER_ALLOW=206]="ROSTER_ALLOW",e[e.ROSTER_BLACKLIST=207]="ROSTER_BLACKLIST",e[e.ROSTER_CONTACTS=208]="ROSTER_CONTACTS",e[e.ROSTER_GET_ALL_CONTACTS_REMARKS=209]="ROSTER_GET_ALL_CONTACTS_REMARKS",e[e.ROSTER_GET_ALL_CONTACTS_REMARKS_FROM_SERVER_BY_PAGE=210]="ROSTER_GET_ALL_CONTACTS_REMARKS_FROM_SERVER_BY_PAGE",e[e.ROSTER_SET_CONTACT_REMARK=211]="ROSTER_SET_CONTACT_REMARK",e[e.ROSTER_OPERATE=300]="ROSTER_OPERATE",e[e.USER_LOGIN=301]="USER_LOGIN",e[e.USER_CREATE=302]="USER_CREATE",e[e.USER_UPDATE_USERINFO=303]="USER_UPDATE_USERINFO",e[e.USER_FETCH_USERINFO=304]="USER_FETCH_USERINFO",e[e.USER_UPDATE_NICK=305]="USER_UPDATE_NICK",e[e.USER_UPLOAD_PUSH_TOKEN=306]="USER_UPLOAD_PUSH_TOKEN",e[e.USER_LOGGEDIN_OTHER_PLATFORM=307]="USER_LOGGEDIN_OTHER_PLATFORM",e[e.USER_OPERATE=400]="USER_OPERATE",e[e.GROUP_CREATEGROUP=401]="GROUP_CREATEGROUP",e[e.GROUP_BLOCK_MESSAGE=402]="GROUP_BLOCK_MESSAGE",e[e.GROUP_FETCH_PUBLICGROUPS_WITHCURSOR=403]="GROUP_FETCH_PUBLICGROUPS_WITHCURSOR",e[e.GROUP_FETCH_USERS_GROUP=404]="GROUP_FETCH_USERS_GROUP",e[e.GROUP_CHANGE_OWNER=405]="GROUP_CHANGE_OWNER",e[e.GROUP_FETCH_SPECIFICATION=406]="GROUP_FETCH_SPECIFICATION",e[e.GROUP_CHANGE_GROUPATTRIBUTE=407]="GROUP_CHANGE_GROUPATTRIBUTE",e[e.GROUP_FETCH_MEMEBERS=408]="GROUP_FETCH_MEMEBERS",e[e.GROUP_GET_ADMIN=409]="GROUP_GET_ADMIN",e[e.GROUP_SET_ADMIN=410]="GROUP_SET_ADMIN",e[e.GROUP_REMOVE_ADMIN=411]="GROUP_REMOVE_ADMIN",e[e.GROUP_DESTOTYGROUP=412]="GROUP_DESTOTYGROUP",e[e.GROUP_LEAVEGROUP=413]="GROUP_LEAVEGROUP",e[e.GROUP_INVITE_TO_GROUP=414]="GROUP_INVITE_TO_GROUP",e[e.GROUP_JOIN_PUBLICGROUP=415]="GROUP_JOIN_PUBLICGROUP",e[e.GROUP_ACCEPT_JOINPUBLICGROUPAPPL=416]="GROUP_ACCEPT_JOINPUBLICGROUPAPPL",e[e.GROUP_DECLINE_JOINPUBLICGROUPAPPL=417]="GROUP_DECLINE_JOINPUBLICGROUPAPPL",e[e.GROUP_ACCEPT_INVITATION=418]="GROUP_ACCEPT_INVITATION",e[e.GROUP_DECLINE_INVITATION=419]="GROUP_DECLINE_INVITATION",e[e.GROUP_REMOVE_MEMBER=420]="GROUP_REMOVE_MEMBER",e[e.GROUP_REMOVE_MEMBERS=421]="GROUP_REMOVE_MEMBERS",e[e.GROUP_MUTE_MEMBERS=422]="GROUP_MUTE_MEMBERS",e[e.GROUP_UNMUTE_MEMBERS=423]="GROUP_UNMUTE_MEMBERS",e[e.GROUP_FETCH_MUTES=424]="GROUP_FETCH_MUTES",e[e.GROUP_BLOCK_MEMBER=425]="GROUP_BLOCK_MEMBER",e[e.GROUP_BLOCK_MEMBERS=426]="GROUP_BLOCK_MEMBERS",e[e.GROUP_UNBLOCK_MEMBER=427]="GROUP_UNBLOCK_MEMBER",e[e.GROUP_UNBLOCK_MEMBERS=428]="GROUP_UNBLOCK_MEMBERS",e[e.GROUP_GET_BLOCK_LIST=429]="GROUP_GET_BLOCK_LIST",e[e.GROUP_MUTE_ALLMEMBERS=430]="GROUP_MUTE_ALLMEMBERS",e[e.GROUP_UNMUTE_ALLMEMBERS=431]="GROUP_UNMUTE_ALLMEMBERS",e[e.GROUP_ADD_WHITELIST=432]="GROUP_ADD_WHITELIST",e[e.GROUP_REMOVE_WHITELIST=433]="GROUP_REMOVE_WHITELIST",e[e.GROUP_FETCH_WHITELIST=434]="GROUP_FETCH_WHITELIST",e[e.GROUP_IS_IN_WHITELIST=435]="GROUP_IS_IN_WHITELIST",e[e.GROUP_GET_READ_USERS=436]="GROUP_GET_READ_USERS",e[e.GROUP_FETCH_ANNOUNCEMENT=437]="GROUP_FETCH_ANNOUNCEMENT",e[e.GROUP_UPDATE_ANNOUNCEMENT=438]="GROUP_UPDATE_ANNOUNCEMENT",e[e.GROUP_UPLOAD_SHAREDFILE=439]="GROUP_UPLOAD_SHAREDFILE",e[e.GROUP_DELETE_SHAREDFILE=440]="GROUP_DELETE_SHAREDFILE",e[e.GROUP_FETCH_SHAREDFILE=441]="GROUP_FETCH_SHAREDFILE",e[e.GROUP_DOWNLOAD_SHAREDFILE=442]="GROUP_DOWNLOAD_SHAREDFILE",e[e.GROUP_MEMBER_SET_META_DATA=443]="GROUP_MEMBER_SET_META_DATA",e[e.GROUP_MEMBER_FETCH_META_DATA=444]="GROUP_MEMBER_FETCH_META_DATA",e[e.GROUP_OPERATE=500]="GROUP_OPERATE",e[e.CHATROOM_FETCH_CHATROOMSWITHPAGE=501]="CHATROOM_FETCH_CHATROOMSWITHPAGE",e[e.CHATROOM_CREATECHATROOM=502]="CHATROOM_CREATECHATROOM",e[e.CHATROOM_DESTORYCHATROOM=503]="CHATROOM_DESTORYCHATROOM",e[e.CHATROOM_FETCH_SPECIFICATION=504]="CHATROOM_FETCH_SPECIFICATION",e[e.CHATROOM_CHANGE_ATTRIBUTE=505]="CHATROOM_CHANGE_ATTRIBUTE",e[e.CHATROOM_REMOVE_MEMBER=506]="CHATROOM_REMOVE_MEMBER",e[e.CHATROOM_REMOVE_MEMBERS=507]="CHATROOM_REMOVE_MEMBERS",e[e.CHATROOM_ADD_MEMBERS=508]="CHATROOM_ADD_MEMBERS",e[e.CHATROOM_JOINCAHTROOM=509]="CHATROOM_JOINCAHTROOM",e[e.CHATROOM_LEAVECAHTROOM=510]="CHATROOM_LEAVECAHTROOM",e[e.CHATROOM_FETCH_MEMBERS=511]="CHATROOM_FETCH_MEMBERS",e[e.CHATROOM_GET_ADMIN=512]="CHATROOM_GET_ADMIN",e[e.CHATROOM_SET_ADMIN=513]="CHATROOM_SET_ADMIN",e[e.CHATROOM_REMOVE_ADMIN=514]="CHATROOM_REMOVE_ADMIN",e[e.CHATROOM_MUTE_USER=515]="CHATROOM_MUTE_USER",e[e.CHATROOM_UNMUTE_USER=516]="CHATROOM_UNMUTE_USER",e[e.CHATROOM_FETCH_MUTES=517]="CHATROOM_FETCH_MUTES",e[e.CHATROOM_BLOCK_USER=518]="CHATROOM_BLOCK_USER",e[e.CHATROOM_BLOCK_USERS=519]="CHATROOM_BLOCK_USERS",e[e.CHATROOM_UNBLOCK_USER=520]="CHATROOM_UNBLOCK_USER",e[e.CHATROOM_UNBLOCK_USERS=521]="CHATROOM_UNBLOCK_USERS",e[e.CHATROOM_FETCH_BANS=522]="CHATROOM_FETCH_BANS",e[e.CHATROOM_MUTE_ALLMEMEBERS=523]="CHATROOM_MUTE_ALLMEMEBERS",e[e.CHATROOM_UNMUTE_ALLMEMEBERS=524]="CHATROOM_UNMUTE_ALLMEMEBERS",e[e.CHATROOM_ADD_WHITELIST=525]="CHATROOM_ADD_WHITELIST",e[e.CHATROOM_REMOVE_WHITELIST=526]="CHATROOM_REMOVE_WHITELIST",e[e.CHATROOM_FETCH_WHITELIST=527]="CHATROOM_FETCH_WHITELIST",e[e.CHATROOM_FETCH_MEMBERIN_WHITELIST=528]="CHATROOM_FETCH_MEMBERIN_WHITELIST",e[e.CHATROOM_FETCH_ANNOUNCEMENT=529]="CHATROOM_FETCH_ANNOUNCEMENT",e[e.CHATROOM_UPDATE_ANNOUNCEMENT=530]="CHATROOM_UPDATE_ANNOUNCEMENT",e[e.CHATROOM_REMOVE_SHARE_FILE=531]="CHATROOM_REMOVE_SHARE_FILE",e[e.CHATROOM_GET_SHARE_FILE_LIST=532]="CHATROOM_GET_SHARE_FILE_LIST",e[e.CHATROOM_UPLOAD_FILE=533]="CHATROOM_UPLOAD_FILE",e[e.CHATROOM_SET_META_DATA=534]="CHATROOM_SET_META_DATA",e[e.CHATROOM_DELETE_META_DATA=535]="CHATROOM_DELETE_META_DATA",e[e.CHATROOM_FETCH_META_DATA=536]="CHATROOM_FETCH_META_DATA",e[e.CHATROOM_FETCH_USER_JOINED_CHATROOM=537]="CHATROOM_FETCH_USER_JOINED_CHATROOM",e[e.CHATROOM_OPERATE=600]="CHATROOM_OPERATE"}(r||(r={})),function(e){e.SDK_INTERNAL="SDK_INTERNAL",e.LOGIN="USER_LOGIN",e.REGISTER="USER_CREATE",e.GET_CHATROOM_LIST="CHATROOM_FETCH_CHATROOMSWITHPAGE",e.CREATE_CHATROOM="CHATROOM_CREATECHATROOM",e.DESTROY_CHATROOM="CHATROOM_DESTORYCHATROOM",e.GET_CHATROOM_DETAIL="CHATROOM_FETCH_SPECIFICATION",e.MODIFY_CHATROOM="CHATROOM_CHANGE_ATTRIBUTE",e.REMOVE_CHATROOM_MEMBER="CHATROOM_REMOVE_MEMBER",e.MULTI_REMOVE_CHATROOM_MEMBER="CHATROOM_REMOVE_MEMBERS",e.ADD_USERS_TO_CHATROOM="CHATROOM_ADD_MEMBERS",e.JOIN_CHATROOM="CHATROOM_JOINCAHTROOM",e.QUIT_CHATROOM="CHATROOM_LEAVECAHTROOM",e.LIST_CHATROOM_MEMBERS="CHATROOM_FETCH_MEMBERS",e.GET_CHATROOM_ADMIN="CHATROOM_GET_ADMIN",e.SET_CHATROOM_ADMIN="CHATROOM_SET_ADMIN",e.REMOVE_CHATROOM_ADMIN="CHATROOM_REMOVE_ADMIN",e.MUTE_CHATROOM_MEMBER="CHATROOM_MUTE_USER",e.REMOVE_MUTE_CHATROOM_MEMBER="CHATROOM_UNMUTE_USER",e.GET_MUTE_CHATROOM_MEMBERS="CHATROOM_FETCH_MUTES",e.SET_CHATROOM_MEMBER_TO_BLACK="CHATROOM_BLOCK_USER",e.MULTI_SET_CHATROOM_MEMBER_TO_BLACK="CHATROOM_BLOCK_USERS",e.REMOVE_CHATROOM_MEMBER_BLACK="CHATROOM_UNBLOCK_USER",e.MULTI_REMOVE_CHATROOM_MEMBER_BLACK="CHATROOM_UNBLOCK_USERS",e.GET_CHATROOM_BLOCK_MEMBERS="CHATROOM_FETCH_BANS",e.DISABLED_CHATROOM_SEND_MSG="CHATROOM_MUTE_ALLMEMEBERS",e.ENABLE_CHATROOM_SEND_MSG="CHATROOM_UNMUTE_ALLMEMEBERS",e.ADD_CHATROOM_WHITE_USERS="CHATROOM_ADD_WHITELIST",e.REMOVE_CHATROOM_WHITE_USERS="CHATROOM_REMOVE_WHITELIST",e.GET_CHATROOM_WHITE_USERS="CHATROOM_FETCH_WHITELIST",e.CHECK_CHATROOM_WHITE_USER="CHATROOM_FETCH_MEMBERIN_WHITELIST",e.GET_CHATROOM_ANN="CHATROOM_FETCH_ANNOUNCEMENT",e.UPDATE_CHATROOM_ANN="CHATROOM_UPDATE_ANNOUNCEMENT",e.DELETE_CHATROOM_FILE="CHATROOM_REMOVE_SHARE_FILE",e.GET_CHATROOM_FILES="CHATROOM_GET_SHARE_FILE_LIST",e.UPLOAD_CHATROOM_FILE="CHATROOM_UPLOAD_FILE",e.SET_CHATROOM_ATTR="CHATROOM_SET_META_DATA",e.DELETE_CHATROOM_ATTR="CHATROOM_DELETE_META_DATA",e.GET_CHATROOM_ATTR="CHATROOM_FETCH_META_DATA",e.GET_USER_JOINED_CHATROOM="CHATROOM_FETCH_USER_JOINED_CHATROOM",e.CREATE_GROUP="GROUP_CREATEGROUP",e.BLOCK_GROUP="GROUP_BLOCK_MESSAGE",e.LIST_GROUP="GROUP_FETCH_PUBLICGROUPS_WITHCURSOR",e.GET_USER_GROUP="GROUP_FETCH_USERS_GROUP",e.CHANGE_OWNER="GROUP_CHANGE_OWNER",e.GET_GROUP_INFO="GROUP_FETCH_SPECIFICATION",e.MODIFY_GROUP="GROUP_CHANGE_GROUPATTRIBUTE",e.LIST_GROUP_MEMBER="GROUP_FETCH_MEMEBERS",e.GET_GROUP_ADMIN="GROUP_GET_ADMIN",e.SET_GROUP_ADMIN="GROUP_SET_ADMIN",e.REMOVE_GROUP_ADMIN="GROUP_REMOVE_ADMIN",e.DISSOLVE_GROUP="GROUP_DESTOTYGROUP",e.QUIT_GROUP="GROUP_LEAVEGROUP",e.INVITE_TO_GROUP="GROUP_INVITE_TO_GROUP",e.JOIN_GROUP="GROUP_JOIN_PUBLICGROUP",e.AGREE_JOIN_GROUP="GROUP_ACCEPT_JOINPUBLICGROUPAPPL",e.REJECT_JOIN_GROUP="GROUP_DECLINE_JOINPUBLICGROUPAPPL",e.AGREE_INVITE_GROUP="GROUP_ACCEPT_INVITATION",e.REJECT_INVITE_GROUP="GROUP_DECLINE_INVITATION",e.REMOVE_GROUP_MEMBER="GROUP_REMOVE_MEMBER",e.MULTI_REMOVE_GROUP_MEMBER="GROUP_REMOVE_MEMBERS",e.MUTE_GROUP_MEMBER="GROUP_MUTE_MEMBERS",e.UNMUTE_GROUP_MEMBER="GROUP_UNMUTE_MEMBERS",e.GET_GROUP_MUTE_LIST="GROUP_FETCH_MUTES",e.BLOCK_GROUP_MEMBER="GROUP_BLOCK_MEMBER",e.BLOCK_GROUP_MEMBERS="GROUP_BLOCK_MEMBERS",e.UNBLOCK_GROUP_MEMBER="GROUP_UNBLOCK_MEMBER",e.UNBLOCK_GROUP_MEMBERS="GROUP_UNBLOCK_MEMBERS",e.GET_GROUP_BLACK_LIST="GROUP_GET_BLOCK_LIST",e.DISABLED_SEND_GROUP_MSG="GROUP_MUTE_ALLMEMBERS",e.ENABLE_SEND_GROUP_MSG="GROUP_UNMUTE_ALLMEMBERS",e.ADD_USERS_TO_GROUP_WHITE="GROUP_ADD_WHITELIST",e.REMOVE_GROUP_WHITE_MEMBER="GROUP_REMOVE_WHITELIST",e.GET_GROUP_WHITE_LIST="GROUP_FETCH_WHITELIST",e.IS_IN_GROUP_WHITE_LIST="GROUP_IS_IN_WHITELIST",e.GET_GROUP_MSG_READ_USER="GROUP_GET_READ_USERS",e.GET_GROUP_ANN="GROUP_FETCH_ANNOUNCEMENT",e.UPDATE_GROUP_ANN="GROUP_UPDATE_ANNOUNCEMENT",e.UPLOAD_GROUP_FILE="GROUP_UPLOAD_SHAREDFILE",e.DELETE_GROUP_FILE="GROUP_DELETE_SHAREDFILE",e.GET_GROUP_FILE_LIST="GROUP_FETCH_SHAREDFILE",e.DOWN_GROUP_FILE="GROUP_DOWNLOAD_SHAREDFILE",e.SET_GROUP_MEMBER_ATTRS="GROUP_MEMBER_SET_META_DATA",e.GET_GROUP_MEMBER_ATTR="GROUP_MEMBER_FETCH_META_DATA",e.GET_SESSION_LIST="REST_GET_SESSION_LIST",e.REST_FETCH_CONVERSATIONS="REST_FETCH_CONVERSATIONS",e.DELETE_SESSION="REST_DEL_SESSION",e.GET_HISTORY_MSG="REST_GET_HISTORY_MESSAGE",e.PIN_CONVERSATION="REST_PIN_CONVERSATION",e.REST_UPLOAD_FILE_IN_PARTS="REST_UPLOAD_FILE_IN_PARTS",e.REST_DELETE_MESSAGES_CONVERSATIONS="REST_DELETE_MESSAGES_CONVERSATIONS",e.MARK_CONVERSATION="REST_MARK_CONVERSATION",e.REST_FETCH_PINMESSAGES="REST_FETCH_PINMESSAGES",e.REST_PIN_MESSAGE="REST_PIN_MESSAGE",e.UPDATE_USER_INFO="USER_UPDATE_USERINFO",e.GET_USER_INFO="USER_FETCH_USERINFO",e.UPDATE_USER_NICK="USER_UPDATE_NICK",e.UPLOAD_PUSH_TOKEN="USER_UPLOAD_PUSH_TOKEN",e.USER_LOGGEDIN_OTHER_PLATFORM="USER_LOGGEDIN_OTHER_PLATFORM",e.GET_BLACK_LIST="ROSTER_BLACKLIST",e.GET_CONTACTS="ROSTER_CONTACTS",e.ROSTER_GET_ALL_CONTACTS_REMARKS="ROSTER_GET_ALL_CONTACTS_REMARKS",e.ROSTER_GET_ALL_CONTACTS_REMARKS_FROM_SERVER_BY_PAGE="ROSTER_GET_ALL_CONTACTS_REMARKS_FROM_SERVER_BY_PAGE",e.ROSTER_SET_CONTACT_REMARK="ROSTER_SET_CONTACT_REMARK",e.ROSTER_ADD="ROSTER_ADD",e.ROSTER_REMOVE="ROSTER_REMOVE",e.ROSTER_ACCEPT="ROSTER_ACCEPT",e.ROSTER_DECLINE="ROSTER_DECLINE",e.ROSTER_BAN="ROSTER_BAN",e.ROSTER_ALLOW="ROSTER_ALLOW",e.SEND_MSG="MSYNC_SENDMESSAGE",e.UPLOAD_MSG_ATTACH="UPLOAD_MSG_ATTACH",e.SEND_RECALL_MSG="MSYNC_RECALLMESSAGE",e.MODIFY_MESSAGE="MSYNC_MODIFYMESSAGE",e.REST_FETCHHISTORYMESSAGE="REST_FETCHHISTORYMESSAGE",e.MSYNC_SYNCOFFLINEMESSAGE="MSYNC_SYNCOFFLINEMESSAGE"}(o||(o={})),function(e){e.GET_DNS="REST_DNSLIST",e.LOGIN_BY_AGORA_TOKEN="LOGIN_BY_AGORA_TOKEN",e.LOGIN_BY_PWD="LOGIN_BY_PWD",e.RESISTER="REGISTER",e.REST_INIT_UPLOAD_TASK_IN_PARTS="REST_INIT_UPLOAD_TASK_IN_PARTS",e.REST_UPLOAD_PART="REST_UPLOAD_PART",e.REST_COMPLETE_UPLOAD_PART="REST_COMPLETE_UPLOAD_PART",e.REST_ABORT_UPLOAD_PART="REST_ABORT_UPLOAD_PART",e.CONNECT_WEBSOCKET="CONNECT_WEBSOCKET"}(i||(i={})),function(e){e[e["5G"]=7]="5G",e[e["4G"]=7]="4G",e[e["3G"]=7]="3G",e[e["2G"]=7]="2G",e[e["SLOW-2G"]=7]="SLOW-2G",e[e.WIFI=2]="WIFI",e[e.LAN=1]="LAN",e[e.DISCONNECTED=0]="DISCONNECTED",e[e.NONE=0]="NONE",e[e.UNKNOWN=-1]="UNKNOWN",e[e["WEBIM UNABLE TO GET"]=-2]="WEBIM UNABLE TO GET"}(s||(s={})),function(e){e[e.success=200]="success",e[e.failed=500]="failed",e[e.disconnect=-1]="disconnect",e[e.closed=401]="closed",e[e.notFound=404]="notFound",e[e.reachLimit=429]="reachLimit"}(a||(a={})),function(e){e[e.web=0]="web",e[e.native=1]="native"}(u||(u={})),function(e){e[e.singleChat=0]="singleChat",e[e.groupChat=1]="groupChat",e[e.chatRoom=2]="chatRoom"}(c||(c={}))},3925:function(e,t,n){"use strict";var r=n(34);e.exports=function(e){return r(e)||null===e}},3989:function(e,t,n){"use strict";n.d(t,{Ec:function(){return f},Ez:function(){return o},Gc:function(){return s},HJ:function(){return d},Qb:function(){return u},Rk:function(){return l},WR:function(){return p},_h:function(){return i},oi:function(){return c},rO:function(){return h},x0:function(){return a}});var r=n(1531),o=15e3,i=15e3,s=3e5,a=1e4,u=1e4,c=8e3,l=3e5,d=3e4,p=5,h=3,f=[r.C.MAX_LIMIT,r.C.WEBIM_TOKEN_EXPIRED,r.C.WEBIM_CONNCTION_TOKEN_NOT_ASSIGN_ERROR,r.C.USER_NOT_FOUND,r.C.WEBIM_CONNCTION_AUTH_ERROR,r.C.REQUEST_PARAMETER_ERROR,r.C.WEBIM_CONNCTION_AUTH_ERROR,r.C.WEBIM_SERVER_SERVING_DISABLED]},3994:function(e,t,n){"use strict";var r=n(7657).IteratorPrototype,o=n(2360),i=n(6980),s=n(687),a=n(6269),u=function(){return this};e.exports=function(e,t,n,c){var l=t+" Iterator";return e.prototype=o(r,{next:i(+!c,n)}),s(e,l,!1,!0),a[l]=u,e}},4055:function(e,t,n){"use strict";var r=n(4576),o=n(34),i=r.document,s=o(i)&&o(i.createElement);e.exports=function(e){return s?i.createElement(e):{}}},4117:function(e){"use strict";e.exports=function(e){return null==e}},4124:function(e,t,n){"use strict";var r=n(9039),o=n(34),i=n(2195),s=n(5652),a=Object.isExtensible,u=r((function(){a(1)}));e.exports=u||s?function(e){return!!o(e)&&(!s||"ArrayBuffer"!==i(e))&&(!a||a(e))}:a},4153:function(e,t,n){"use strict";e.exports=function(e){try{var t=n(876)(e);if(t&&(t.length||Object.keys(t).length))return t}catch(e){}return null}},4170:function(e,t,n){"use strict";var r=n(6518),o=n(566);r({target:"Function",proto:!0,forced:Function.bind!==o},{bind:o})},4209:function(e,t,n){"use strict";var r=n(8227),o=n(6269),i=r("iterator"),s=Array.prototype;e.exports=function(e){return void 0!==e&&(o.Array===e||s[i]===e)}},4213:function(e,t,n){"use strict";var r=n(3724),o=n(9504),i=n(9565),s=n(9039),a=n(1072),u=n(3717),c=n(8773),l=n(8981),d=n(7055),p=Object.assign,h=Object.defineProperty,f=o([].concat);e.exports=!p||s((function(){if(r&&1!==p({b:1},p(h({},"a",{enumerable:!0,get:function(){h(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},t={},n=Symbol("assign detection"),o="abcdefghijklmnopqrst";return e[n]=7,o.split("").forEach((function(e){t[e]=e})),7!==p({},e)[n]||a(p({},t)).join("")!==o}))?function(e,t){for(var n=l(e),o=arguments.length,s=1,p=u.f,h=c.f;o>s;)for(var v,E=d(arguments[s++]),m=p?f(a(E),p(E)):a(E),g=m.length,y=0;g>y;)v=m[y++],r&&!i(h,E,v)||(n[v]=E[v]);return n}:p},4215:function(e,t,n){"use strict";var r=n(4576),o=n(2839),i=n(2195),s=function(e){return o.slice(0,e.length)===e};e.exports=s("Bun/")?"BUN":s("Cloudflare-Workers")?"CLOUDFLARE":s("Deno/")?"DENO":s("Node.js/")?"NODE":r.Bun&&"string"==typeof Bun.version?"BUN":r.Deno&&"object"==typeof Deno.version?"DENO":"process"===i(r.process)?"NODE":r.window&&r.document?"BROWSER":"REST"},4265:function(e,t,n){"use strict";var r=n(2839);e.exports=/ipad|iphone|ipod/i.test(r)&&"undefined"!=typeof Pebble},4270:function(e,t,n){"use strict";var r=n(9565),o=n(4901),i=n(34),s=TypeError;e.exports=function(e,t){var n,a;if("string"===t&&o(n=e.toString)&&!i(a=r(n,e)))return a;if(o(n=e.valueOf)&&!i(a=r(n,e)))return a;if("string"!==t&&o(n=e.toString)&&!i(a=r(n,e)))return a;throw new s("Can't convert object to primitive value")}},4346:function(e,t,n){"use strict";n(6518)({target:"Array",stat:!0},{isArray:n(4376)})},4358:function(e){"use strict";function t(){this._listeners={}}e.exports=t,t.prototype.on=function(e,t,n){return(this._listeners[e]||(this._listeners[e]=[])).push({fn:t,ctx:n||this}),this},t.prototype.off=function(e,t){if(void 0===e)this._listeners={};else if(void 0===t)this._listeners[e]=[];else for(var n=this._listeners[e],r=0;r<n.length;)n[r].fn===t?n.splice(r,1):++r;return this},t.prototype.emit=function(e){var t=this._listeners[e];if(t){for(var n=[],r=1;r<arguments.length;)n.push(arguments[r++]);for(r=0;r<t.length;)t[r].fn.apply(t[r++].ctx,n)}return this}},4359:function(e,t,n){"use strict";var r=n(6518),o=n(6346);r({global:!0,constructor:!0,forced:!n(7811)},{DataView:o.DataView})},4373:function(e,t,n){"use strict";var r=n(8981),o=n(5610),i=n(6198);e.exports=function(e){for(var t=r(this),n=i(t),s=arguments.length,a=o(s>1?arguments[1]:void 0,n),u=s>2?arguments[2]:void 0,c=void 0===u?n:o(u,n);c>a;)t[a++]=e;return t}},4376:function(e,t,n){"use strict";var r=n(2195);e.exports=Array.isArray||function(e){return"Array"===r(e)}},4394:function(e,t,n){"use strict";var r=t;function o(){r.util._configure(),r.Writer._configure(r.BufferWriter),r.Reader._configure(r.BufferReader)}r.build="minimal",r.Writer=n(3449),r.BufferWriter=n(818),r.Reader=n(6237),r.BufferReader=n(3158),r.util=n(3610),r.rpc=n(5047),r.roots=n(4529),r.configure=o,o()},4423:function(e,t,n){"use strict";var r=n(6518),o=n(9617).includes,i=n(9039),s=n(6469);r({target:"Array",proto:!0,forced:i((function(){return!Array(1).includes()}))},{includes:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}}),s("includes")},4428:function(e,t,n){"use strict";var r=n(8227)("iterator"),o=!1;try{var i=0,s={next:function(){return{done:!!i++}},return:function(){o=!0}};s[r]=function(){return this},Array.from(s,(function(){throw 2}))}catch(e){}e.exports=function(e,t){try{if(!t&&!o)return!1}catch(e){return!1}var n=!1;try{var i={};i[r]=function(){return{next:function(){return{done:n=!0}}}},e(i)}catch(e){}return n}},4488:function(e,t,n){"use strict";var r=n(7680),o=Math.floor,i=function(e,t){var n=e.length;if(n<8)for(var s,a,u=1;u<n;){for(a=u,s=e[u];a&&t(e[a-1],s)>0;)e[a]=e[--a];a!==u++&&(e[a]=s)}else for(var c=o(n/2),l=i(r(e,0,c),t),d=i(r(e,c),t),p=l.length,h=d.length,f=0,v=0;f<p||v<h;)e[f+v]=f<p&&v<h?t(l[f],d[v])<=0?l[f++]:d[v++]:f<p?l[f++]:d[v++];return e};e.exports=i},4495:function(e,t,n){"use strict";var r=n(9519),o=n(9039),i=n(4576).String;e.exports=!!Object.getOwnPropertySymbols&&!o((function(){var e=Symbol("symbol detection");return!i(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&r&&r<41}))},4496:function(e,t,n){"use strict";var r=n(4644),o=n(9617).includes,i=r.aTypedArray;(0,r.exportTypedArrayMethod)("includes",(function(e){return o(i(this),e,arguments.length>1?arguments[1]:void 0)}))},4527:function(e,t,n){"use strict";var r=n(3724),o=n(4376),i=TypeError,s=Object.getOwnPropertyDescriptor,a=r&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(e){return e instanceof TypeError}}();e.exports=a?function(e,t){if(o(e)&&!s(e,"length").writable)throw new i("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t}},4529:function(e){"use strict";e.exports={}},4554:function(e,t,n){"use strict";var r=n(6518),o=n(8981),i=n(5610),s=n(1291),a=n(6198),u=n(4527),c=n(6837),l=n(1469),d=n(4659),p=n(4606),h=n(597)("splice"),f=Math.max,v=Math.min;r({target:"Array",proto:!0,forced:!h},{splice:function(e,t){var n,r,h,E,m,g,y=o(this),_=a(y),O=i(e,_),T=arguments.length;for(0===T?n=r=0:1===T?(n=0,r=_-O):(n=T-2,r=v(f(s(t),0),_-O)),c(_+n-r),h=l(y,r),E=0;E<r;E++)(m=O+E)in y&&d(h,E,y[m]);if(h.length=r,n<r){for(E=O;E<_-r;E++)g=E+n,(m=E+r)in y?y[g]=y[m]:p(y,g);for(E=_;E>_-r+n;E--)p(y,E-1)}else if(n>r)for(E=_-r;E>O;E--)g=E+n-1,(m=E+r-1)in y?y[g]=y[m]:p(y,g);for(E=0;E<n;E++)y[E+O]=arguments[E+2];return u(y,_-r+n),h}})},4576:function(e,t,n){"use strict";var r=function(e){return e&&e.Math===Math&&e};e.exports=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof n.g&&n.g)||r("object"==typeof this&&this)||function(){return this}()||Function("return this")()},4598:function(e,t,n){"use strict";var r=n(9039);e.exports=function(e,t){var n=[][e];return!!n&&r((function(){n.call(null,t||function(){return 1},1)}))}},4599:function(e,t,n){"use strict";var r=n(6518),o=n(4576),i=n(9472)(o.setTimeout,!0);r({global:!0,bind:!0,forced:o.setTimeout!==i},{setTimeout:i})},4606:function(e,t,n){"use strict";var r=n(6823),o=TypeError;e.exports=function(e,t){if(!delete e[t])throw new o("Cannot delete property "+r(t)+" of "+r(e))}},4644:function(e,t,n){"use strict";var r,o,i,s=n(7811),a=n(3724),u=n(4576),c=n(4901),l=n(34),d=n(9297),p=n(6955),h=n(6823),f=n(6699),v=n(6840),E=n(2106),m=n(1625),g=n(2787),y=n(2967),_=n(8227),O=n(3392),T=n(1181),R=T.enforce,I=T.get,C=u.Int8Array,S=C&&C.prototype,N=u.Uint8ClampedArray,A=N&&N.prototype,b=C&&g(C),M=S&&g(S),w=Object.prototype,L=u.TypeError,U=_("toStringTag"),x=O("TYPED_ARRAY_TAG"),D="TypedArrayConstructor",P=s&&!!y&&"Opera"!==p(u.opera),k=!1,H={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},F={BigInt64Array:8,BigUint64Array:8},B=function(e){var t=g(e);if(l(t)){var n=I(t);return n&&d(n,D)?n[D]:B(t)}},G=function(e){if(!l(e))return!1;var t=p(e);return d(H,t)||d(F,t)};for(r in H)(i=(o=u[r])&&o.prototype)?R(i)[D]=o:P=!1;for(r in F)(i=(o=u[r])&&o.prototype)&&(R(i)[D]=o);if((!P||!c(b)||b===Function.prototype)&&(b=function(){throw new L("Incorrect invocation")},P))for(r in H)u[r]&&y(u[r],b);if((!P||!M||M===w)&&(M=b.prototype,P))for(r in H)u[r]&&y(u[r].prototype,M);if(P&&g(A)!==M&&y(A,M),a&&!d(M,U))for(r in k=!0,E(M,U,{configurable:!0,get:function(){return l(this)?this[x]:void 0}}),H)u[r]&&f(u[r],x,r);e.exports={NATIVE_ARRAY_BUFFER_VIEWS:P,TYPED_ARRAY_TAG:k&&x,aTypedArray:function(e){if(G(e))return e;throw new L("Target is not a typed array")},aTypedArrayConstructor:function(e){if(c(e)&&(!y||m(b,e)))return e;throw new L(h(e)+" is not a typed array constructor")},exportTypedArrayMethod:function(e,t,n,r){if(a){if(n)for(var o in H){var i=u[o];if(i&&d(i.prototype,e))try{delete i.prototype[e]}catch(n){try{i.prototype[e]=t}catch(e){}}}M[e]&&!n||v(M,e,n?t:P&&S[e]||t,r)}},exportTypedArrayStaticMethod:function(e,t,n){var r,o;if(a){if(y){if(n)for(r in H)if((o=u[r])&&d(o,e))try{delete o[e]}catch(e){}if(b[e]&&!n)return;try{return v(b,e,n?t:P&&b[e]||t)}catch(e){}}for(r in H)!(o=u[r])||o[e]&&!n||v(o,e,t)}},getTypedArrayConstructor:B,isView:function(e){if(!l(e))return!1;var t=p(e);return"DataView"===t||d(H,t)||d(F,t)},isTypedArray:G,TypedArray:b,TypedArrayPrototype:M}},4659:function(e,t,n){"use strict";var r=n(3724),o=n(4913),i=n(6980);e.exports=function(e,t,n){r?o.f(e,t,i(0,n)):e[t]=n}},4723:function(e,t,n){"use strict";var r;n.d(t,{s:function(){return r}}),function(e){e[e.mark_0=0]="mark_0",e[e.mark_1=1]="mark_1",e[e.mark_2=2]="mark_2",e[e.mark_3=3]="mark_3",e[e.mark_4=4]="mark_4",e[e.mark_5=5]="mark_5",e[e.mark_6=6]="mark_6",e[e.mark_7=7]="mark_7",e[e.mark_8=8]="mark_8",e[e.mark_9=9]="mark_9",e[e.mark_10=10]="mark_10",e[e.mark_11=11]="mark_11",e[e.mark_12=12]="mark_12",e[e.mark_13=13]="mark_13",e[e.mark_14=14]="mark_14",e[e.mark_15=15]="mark_15",e[e.mark_16=16]="mark_16",e[e.mark_17=17]="mark_17",e[e.mark_18=18]="mark_18",e[e.mark_19=19]="mark_19"}(r||(r={}))},4743:function(e,t,n){"use strict";var r=n(6518),o=n(4576),i=n(6346),s=n(7633),a="ArrayBuffer",u=i[a];r({global:!0,constructor:!0,forced:o[a]!==u},{ArrayBuffer:u}),s(a)},4782:function(e,t,n){"use strict";var r=n(6518),o=n(4376),i=n(3517),s=n(34),a=n(5610),u=n(6198),c=n(5397),l=n(4659),d=n(8227),p=n(597),h=n(7680),f=p("slice"),v=d("species"),E=Array,m=Math.max;r({target:"Array",proto:!0,forced:!f},{slice:function(e,t){var n,r,d,p=c(this),f=u(p),g=a(e,f),y=a(void 0===t?f:t,f);if(o(p)&&(n=p.constructor,(i(n)&&(n===E||o(n.prototype))||s(n)&&null===(n=n[v]))&&(n=void 0),n===E||void 0===n))return h(p,g,y);for(r=new(void 0===n?E:n)(m(y-g,0)),d=0;g<y;g++,d++)g in p&&l(r,d,p[g]);return r.length=d,r}})},4863:function(e,t,n){"use strict";e.exports=I,I.filename=null,I.defaults={keepCase:!1};var r=n(527),o=n(5330),i=n(7882),s=n(1344),a=n(8252),u=n(1457),c=n(5643),l=n(9687),d=n(8811),p=n(361),h=n(3262),f=/^[1-9][0-9]*$/,v=/^-?[1-9][0-9]*$/,E=/^0[x][0-9a-fA-F]+$/,m=/^-?0[x][0-9a-fA-F]+$/,g=/^0[0-7]+$/,y=/^-?0[0-7]+$/,_=/^(?![eE])[0-9]*(?:\.[0-9]*)?(?:[eE][+-]?[0-9]+)?$/,O=/^[a-zA-Z_][a-zA-Z_0-9]*$/,T=/^(?:\.?[a-zA-Z_][a-zA-Z_0-9]*)(?:\.[a-zA-Z_][a-zA-Z_0-9]*)*$/,R=/^(?:\.[a-zA-Z_][a-zA-Z_0-9]*)+$/;function I(e,t,n){t instanceof o||(n=t,t=new o),n||(n=I.defaults);var C,S,N,A,b,M=n.preferTrailingComment||!1,w=r(e,n.alternateCommentMode||!1),L=w.next,U=w.push,x=w.peek,D=w.skip,P=w.cmnt,k=!0,H=!1,F=t,B=n.keepCase?function(e){return e}:h.camelCase;function G(e,t,n){var r=I.filename;return n||(I.filename=null),Error("illegal "+(t||"token")+" '"+e+"' ("+(r?r+", ":"")+"line "+w.line+")")}function W(){var e,t=[];do{if('"'!==(e=L())&&"'"!==e)throw G(e);t.push(L()),D(e),e=x()}while('"'===e||"'"===e);return t.join("")}function j(e){var t=L();switch(t){case"'":case'"':return U(t),W();case"true":case"TRUE":return!0;case"false":case"FALSE":return!1}try{return function(e){var t=1;switch("-"===e.charAt(0)&&(t=-1,e=e.substring(1)),e){case"inf":case"INF":case"Inf":return t*(1/0);case"nan":case"NAN":case"Nan":case"NaN":return NaN;case"0":return 0}if(f.test(e))return t*parseInt(e,10);if(E.test(e))return t*parseInt(e,16);if(g.test(e))return t*parseInt(e,8);if(_.test(e))return t*parseFloat(e);throw G(e,"number",!0)}(t)}catch(n){if(e&&T.test(t))return t;throw G(t,"value")}}function q(e,t){var n,r;do{!t||'"'!==(n=x())&&"'"!==n?e.push([r=K(L()),D("to",!0)?K(L()):r]):e.push(W())}while(D(",",!0));D(";")}function K(e,t){switch(e){case"max":case"MAX":case"Max":return *********;case"0":return 0}if(!t&&"-"===e.charAt(0))throw G(e,"id");if(v.test(e))return parseInt(e,10);if(m.test(e))return parseInt(e,16);if(y.test(e))return parseInt(e,8);throw G(e,"id")}function V(){if(void 0!==C)throw G("package");if(C=L(),!T.test(C))throw G(C,"name");F=F.define(C),D(";")}function z(){var e,t=x();switch(t){case"weak":e=N||(N=[]),L();break;case"public":L();default:e=S||(S=[])}t=W(),D(";"),e.push(t)}function J(){if(D("="),A=W(),!(H="proto3"===A)&&"proto2"!==A)throw G(A,"syntax");D(";")}function Y(e,t){switch(t){case"option":return $(e,t),D(";"),!0;case"message":return function(e,t){if(!O.test(t=L()))throw G(t,"type name");var n=new i(t);X(n,(function(e){if(!Y(n,e))switch(e){case"map":!function(e){D("<");var t=L();if(void 0===p.mapKey[t])throw G(t,"type");D(",");var n=L();if(!T.test(n))throw G(n,"type");D(">");var r=L();if(!O.test(r))throw G(r,"name");D("=");var o=new a(B(r),K(L()),t,n);X(o,(function(e){if("option"!==e)throw G(e);$(o,e),D(";")}),(function(){te(o)})),e.add(o)}(n);break;case"required":case"repeated":Q(n,e);break;case"optional":Q(n,H?"proto3_optional":"optional");break;case"oneof":!function(e,t){if(!O.test(t=L()))throw G(t,"name");var n=new u(B(t));X(n,(function(e){"option"===e?($(n,e),D(";")):(U(e),Q(n,"optional"))})),e.add(n)}(n,e);break;case"extensions":q(n.extensions||(n.extensions=[]));break;case"reserved":q(n.reserved||(n.reserved=[]),!0);break;default:if(!H||!T.test(e))throw G(e);U(e),Q(n,"optional")}})),e.add(n)}(e,t),!0;case"enum":return function(e,t){if(!O.test(t=L()))throw G(t,"name");var n=new c(t);X(n,(function(e){switch(e){case"option":$(n,e),D(";");break;case"reserved":q(n.reserved||(n.reserved=[]),!0);break;default:!function(e,t){if(!O.test(t))throw G(t,"name");D("=");var n=K(L(),!0),r={};X(r,(function(e){if("option"!==e)throw G(e);$(r,e),D(";")}),(function(){te(r)})),e.add(t,n,r.comment)}(n,e)}})),e.add(n)}(e,t),!0;case"service":return function(e,t){if(!O.test(t=L()))throw G(t,"service name");var n=new l(t);X(n,(function(e){if(!Y(n,e)){if("rpc"!==e)throw G(e);!function(e,t){var n=P(),r=t;if(!O.test(t=L()))throw G(t,"name");var o,i,s,a,u=t;if(D("("),D("stream",!0)&&(i=!0),!T.test(t=L()))throw G(t);if(o=t,D(")"),D("returns"),D("("),D("stream",!0)&&(a=!0),!T.test(t=L()))throw G(t);s=t,D(")");var c=new d(u,r,o,s,i,a);c.comment=n,X(c,(function(e){if("option"!==e)throw G(e);$(c,e),D(";")})),e.add(c)}(n,e)}})),e.add(n)}(e,t),!0;case"extend":return function(e,t){if(!T.test(t=L()))throw G(t,"reference");var n=t;X(null,(function(t){switch(t){case"required":case"repeated":Q(e,t,n);break;case"optional":Q(e,H?"proto3_optional":"optional",n);break;default:if(!H||!T.test(t))throw G(t);U(t),Q(e,"optional",n)}}))}(e,t),!0}return!1}function X(e,t,n){var r=w.line;if(e&&("string"!=typeof e.comment&&(e.comment=P()),e.filename=I.filename),D("{",!0)){for(var o;"}"!==(o=L());)t(o);D(";",!0)}else n&&n(),D(";"),e&&("string"!=typeof e.comment||M)&&(e.comment=P(r)||e.comment)}function Q(e,t,n){var r=L();if("group"!==r){if(!T.test(r))throw G(r,"type");var o=L();if(!O.test(o))throw G(o,"name");o=B(o),D("=");var a=new s(o,K(L()),r,t,n);if(X(a,(function(e){if("option"!==e)throw G(e);$(a,e),D(";")}),(function(){te(a)})),"proto3_optional"===t){var c=new u("_"+o);a.setOption("proto3_optional",!0),c.add(a),e.add(c)}else e.add(a);H||!a.repeated||void 0===p.packed[r]&&void 0!==p.basic[r]||a.setOption("packed",!1,!0)}else!function(e,t){var n=L();if(!O.test(n))throw G(n,"name");var r=h.lcFirst(n);n===r&&(n=h.ucFirst(n)),D("=");var o=K(L()),a=new i(n);a.group=!0;var u=new s(r,o,n,t);u.filename=I.filename,X(a,(function(e){switch(e){case"option":$(a,e),D(";");break;case"required":case"repeated":Q(a,e);break;case"optional":Q(a,H?"proto3_optional":"optional");break;default:throw G(e)}})),e.add(a).add(u)}(e,t)}function $(e,t){var n=D("(",!0);if(!T.test(t=L()))throw G(t,"name");var r,o=t,i=o;n&&(D(")"),i=o="("+o+")",t=x(),R.test(t)&&(r=t.substr(1),o+=t,L())),D("="),function(e,t,n,r){e.setParsedOption&&e.setParsedOption(t,n,r)}(e,i,Z(e,o),r)}function Z(e,t){if(D("{",!0)){for(var n={};!D("}",!0);){if(!O.test(b=L()))throw G(b,"name");var r,o=b;"{"===x()?r=Z(e,t+"."+b):(D(":"),"{"===x()?r=Z(e,t+"."+b):(r=j(!0),ee(e,t+"."+b,r)));var i=n[o];i&&(r=[].concat(i).concat(r)),n[o]=r,D(",",!0)}return n}var s=j(!0);return ee(e,t,s),s}function ee(e,t,n){e.setOption&&e.setOption(t,n)}function te(e){if(D("[",!0)){do{$(e,"option")}while(D(",",!0));D("]")}return e}for(;null!==(b=L());)switch(b){case"package":if(!k)throw G(b);V();break;case"import":if(!k)throw G(b);z();break;case"syntax":if(!k)throw G(b);J();break;case"option":$(F,b),D(";");break;default:if(Y(F,b)){k=!1;continue}throw G(b)}return I.filename=null,{package:C,imports:S,weakImports:N,syntax:A,root:t}}},4901:function(e){"use strict";var t="object"==typeof document&&document.all;e.exports=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(e){return"function"==typeof e}},4913:function(e,t,n){"use strict";var r=n(3724),o=n(5917),i=n(8686),s=n(8551),a=n(6969),u=TypeError,c=Object.defineProperty,l=Object.getOwnPropertyDescriptor,d="enumerable",p="configurable",h="writable";t.f=r?i?function(e,t,n){if(s(e),t=a(t),s(n),"function"==typeof e&&"prototype"===t&&"value"in n&&h in n&&!n[h]){var r=l(e,t);r&&r[h]&&(e[t]=n.value,n={configurable:p in n?n[p]:r[p],enumerable:d in n?n[d]:r[d],writable:!1})}return c(e,t,n)}:c:function(e,t,n){if(s(e),t=a(t),s(n),o)try{return c(e,t,n)}catch(e){}if("get"in n||"set"in n)throw new u("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},5031:function(e,t,n){"use strict";var r=n(7751),o=n(9504),i=n(8480),s=n(3717),a=n(8551),u=o([].concat);e.exports=r("Reflect","ownKeys")||function(e){var t=i.f(a(e)),n=s.f;return n?u(t,n(e)):t}},5044:function(e,t,n){"use strict";var r=n(4644),o=n(4373),i=n(5854),s=n(6955),a=n(9565),u=n(9504),c=n(9039),l=r.aTypedArray,d=r.exportTypedArrayMethod,p=u("".slice);d("fill",(function(e){var t=arguments.length;l(this);var n="Big"===p(s(this),0,3)?i(e):+e;return a(o,this,n,t>1?arguments[1]:void 0,t>2?arguments[2]:void 0)}),c((function(){var e=0;return new Int8Array(2).fill({valueOf:function(){return e++}}),1!==e})))},5047:function(e,t,n){"use strict";t.Service=n(7595)},5086:function(e,t,n){"use strict";var r=n(6518),o=n(9213).some;r({target:"Array",proto:!0,forced:!n(4598)("some")},{some:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}})},5095:function(e){"use strict";e.exports=r;var t,n=/\/|\./;function r(e,t){n.test(e)||(e="google/protobuf/"+e+".proto",t={nested:{google:{nested:{protobuf:{nested:t}}}}}),r[e]=t}r("any",{Any:{fields:{type_url:{type:"string",id:1},value:{type:"bytes",id:2}}}}),r("duration",{Duration:t={fields:{seconds:{type:"int64",id:1},nanos:{type:"int32",id:2}}}}),r("timestamp",{Timestamp:t}),r("empty",{Empty:{fields:{}}}),r("struct",{Struct:{fields:{fields:{keyType:"string",type:"Value",id:1}}},Value:{oneofs:{kind:{oneof:["nullValue","numberValue","stringValue","boolValue","structValue","listValue"]}},fields:{nullValue:{type:"NullValue",id:1},numberValue:{type:"double",id:2},stringValue:{type:"string",id:3},boolValue:{type:"bool",id:4},structValue:{type:"Struct",id:5},listValue:{type:"ListValue",id:6}}},NullValue:{values:{NULL_VALUE:0}},ListValue:{fields:{values:{rule:"repeated",type:"Value",id:1}}}}),r("wrappers",{DoubleValue:{fields:{value:{type:"double",id:1}}},FloatValue:{fields:{value:{type:"float",id:1}}},Int64Value:{fields:{value:{type:"int64",id:1}}},UInt64Value:{fields:{value:{type:"uint64",id:1}}},Int32Value:{fields:{value:{type:"int32",id:1}}},UInt32Value:{fields:{value:{type:"uint32",id:1}}},BoolValue:{fields:{value:{type:"bool",id:1}}},StringValue:{fields:{value:{type:"string",id:1}}},BytesValue:{fields:{value:{type:"bytes",id:1}}}}),r("field_mask",{FieldMask:{fields:{paths:{rule:"repeated",type:"string",id:1}}}}),r.get=function(e){return r[e]||null}},5212:function(e,t,n){"use strict";e.exports=i;var r=n(8045),o=n(4153)("fs");function i(e,t,n){return"function"==typeof t?(n=t,t={}):t||(t={}),n?!t.xhr&&o&&o.readFile?o.readFile(e,(function(r,o){return r&&"undefined"!=typeof XMLHttpRequest?i.xhr(e,t,n):r?n(r):n(null,t.binary?o:o.toString("utf8"))})):i.xhr(e,t,n):r(i,this,e,t)}i.xhr=function(e,t,n){var r=new XMLHttpRequest;r.onreadystatechange=function(){if(4===r.readyState){if(0!==r.status&&200!==r.status)return n(Error("status "+r.status));if(t.binary){var e=r.response;if(!e){e=[];for(var o=0;o<r.responseText.length;++o)e.push(255&r.responseText.charCodeAt(o))}return n(null,"undefined"!=typeof Uint8Array?new Uint8Array(e):e)}return n(null,r.responseText)}},t.binary&&("overrideMimeType"in r&&r.overrideMimeType("text/plain; charset=x-user-defined"),r.responseType="arraybuffer"),r.open("GET",e),r.send()}},5276:function(e,t,n){"use strict";var r=n(6518),o=n(7476),i=n(9617).indexOf,s=n(4598),a=o([].indexOf),u=!!a&&1/a([1],1,-0)<0;r({target:"Array",proto:!0,forced:u||!s("indexOf")},{indexOf:function(e){var t=arguments.length>1?arguments[1]:void 0;return u?a(this,e,t)||0:i(this,e,t)}})},5323:function(e,t,n){"use strict";n.d(t,{B:function(){return a}}),n(8706),n(739),n(4170),n(6099),n(3362);var r=n(1750),o=n(8678),i=n(2056),s=n(3893);function a(e){if("string"!=typeof e.deviceId||""===e.deviceId)throw Error('Invalid parameter: "deviceId"');var t=r.dO.call(this).error;if(t)return Promise.reject(t);var n=this.context,a=n.orgName,u=n.appName,c=n.accessToken,l={url:"".concat(this.apiUrl,"/").concat(a,"/").concat(u,"/users/").concat(this.user),type:"PUT",data:JSON.stringify({device_id:e.deviceId,device_token:e.deviceToken,notifier_name:e.notifierName}),dataType:"json",headers:{Authorization:"Bearer "+c,"Content-Type":"application/json"},success:e.success,error:e.error};i.vF.debug("Call uploadPushTokenToServer",e);var d=o.RD.bind(this,l,s.jz.UPLOAD_PUSH_TOKEN);return o.Wp.retryPromise(d).then((function(e){var t=e.entities[0]||{};return{type:e.type,data:t}}))}},5325:function(e,t,n){"use strict";var r=e.exports=n(4394);r.build="light",r.load=function(e,t,n){return"function"==typeof t?(n=t,t=new r.Root):t||(t=new r.Root),t.load(e,n)},r.loadSync=function(e,t){return t||(t=new r.Root),t.loadSync(e)},r.encoder=n(1080),r.decoder=n(7728),r.verifier=n(420),r.converter=n(744),r.ReflectionObject=n(7209),r.Namespace=n(8923),r.Root=n(5330),r.Enum=n(5643),r.Type=n(7882),r.Field=n(1344),r.OneOf=n(1457),r.MapField=n(8252),r.Service=n(9687),r.Method=n(8811),r.Message=n(2551),r.wrappers=n(6434),r.types=n(361),r.util=n(3262),r.ReflectionObject._configure(r.Root),r.Namespace._configure(r.Type,r.Service,r.Enum),r.Root._configure(r.Type),r.Field._configure(r.Type)},5330:function(e,t,n){"use strict";e.exports=d;var r=n(8923);((d.prototype=Object.create(r.prototype)).constructor=d).className="Root";var o,i,s,a=n(1344),u=n(5643),c=n(1457),l=n(3262);function d(e){r.call(this,"",e),this.deferred=[],this.files=[]}function p(){}d.fromJSON=function(e,t){return t||(t=new d),e.options&&t.setOptions(e.options),t.addJSON(e.nested)},d.prototype.resolvePath=l.path.resolve,d.prototype.fetch=l.fetch,d.prototype.load=function e(t,n,r){"function"==typeof n&&(r=n,n=void 0);var o=this;if(!r)return l.asPromise(e,o,t,n);var a=r===p;function u(e,t){if(r){var n=r;if(r=null,a)throw e;n(e,t)}}function c(e){var t=e.lastIndexOf("google/protobuf/");if(t>-1){var n=e.substring(t);if(n in s)return n}return null}function d(e,t){try{if(l.isString(t)&&"{"===t.charAt(0)&&(t=JSON.parse(t)),l.isString(t)){i.filename=e;var r,s=i(t,o,n),d=0;if(s.imports)for(;d<s.imports.length;++d)(r=c(s.imports[d])||o.resolvePath(e,s.imports[d]))&&h(r);if(s.weakImports)for(d=0;d<s.weakImports.length;++d)(r=c(s.weakImports[d])||o.resolvePath(e,s.weakImports[d]))&&h(r,!0)}else o.setOptions(t.options).addJSON(t.nested)}catch(e){u(e)}a||f||u(null,o)}function h(e,t){if(!(o.files.indexOf(e)>-1))if(o.files.push(e),e in s)a?d(e,s[e]):(++f,setTimeout((function(){--f,d(e,s[e])})));else if(a){var n;try{n=l.fs.readFileSync(e).toString("utf8")}catch(e){return void(t||u(e))}d(e,n)}else++f,o.fetch(e,(function(n,i){--f,r&&(n?t?f||u(null,o):u(n):d(e,i))}))}var f=0;l.isString(t)&&(t=[t]);for(var v,E=0;E<t.length;++E)(v=o.resolvePath("",t[E]))&&h(v);if(a)return o;f||u(null,o)},d.prototype.loadSync=function(e,t){if(!l.isNode)throw Error("not supported");return this.load(e,t,p)},d.prototype.resolveAll=function(){if(this.deferred.length)throw Error("unresolvable extensions: "+this.deferred.map((function(e){return"'extend "+e.extend+"' in "+e.parent.fullName})).join(", "));return r.prototype.resolveAll.call(this)};var h=/^[A-Z]/;function f(e,t){var n=t.parent.lookup(t.extend);if(n){var r=new a(t.fullName,t.id,t.type,t.rule,void 0,t.options);return r.declaringField=t,t.extensionField=r,n.add(r),!0}return!1}d.prototype._handleAdd=function(e){if(e instanceof a)void 0===e.extend||e.extensionField||f(0,e)||this.deferred.push(e);else if(e instanceof u)h.test(e.name)&&(e.parent[e.name]=e.values);else if(!(e instanceof c)){if(e instanceof o)for(var t=0;t<this.deferred.length;)f(0,this.deferred[t])?this.deferred.splice(t,1):++t;for(var n=0;n<e.nestedArray.length;++n)this._handleAdd(e._nestedArray[n]);h.test(e.name)&&(e.parent[e.name]=e)}},d.prototype._handleRemove=function(e){if(e instanceof a){if(void 0!==e.extend)if(e.extensionField)e.extensionField.parent.remove(e.extensionField),e.extensionField=null;else{var t=this.deferred.indexOf(e);t>-1&&this.deferred.splice(t,1)}}else if(e instanceof u)h.test(e.name)&&delete e.parent[e.name];else if(e instanceof r){for(var n=0;n<e.nestedArray.length;++n)this._handleRemove(e._nestedArray[n]);h.test(e.name)&&delete e.parent[e.name]}},d._configure=function(e,t,n){o=e,i=t,s=n}},5370:function(e,t,n){"use strict";var r=n(6198);e.exports=function(e,t,n){for(var o=0,i=arguments.length>2?n:r(t),s=new e(i);i>o;)s[o]=t[o++];return s}},5397:function(e,t,n){"use strict";var r=n(7055),o=n(7750);e.exports=function(e){return r(o(e))}},5506:function(e,t,n){"use strict";var r=n(6518),o=n(2357).entries;r({target:"Object",stat:!0},{entries:function(e){return o(e)}})},5548:function(e,t,n){"use strict";var r=n(3517),o=n(6823),i=TypeError;e.exports=function(e){if(r(e))return e;throw new i(o(e)+" is not a constructor")}},5575:function(e,t,n){"use strict";var r=n(6518),o=n(4576),i=n(9472)(o.setInterval,!0);r({global:!0,bind:!0,forced:o.setInterval!==i},{setInterval:i})},5610:function(e,t,n){"use strict";var r=n(1291),o=Math.max,i=Math.min;e.exports=function(e,t){var n=r(e);return n<0?o(n+t,0):i(n,t)}},5617:function(e,t,n){"use strict";var r=n(3164);e.exports=Math.fround||function(e){return r(e,1.1920928955078125e-7,34028234663852886e22,11754943508222875e-54)}},5643:function(e,t,n){"use strict";e.exports=s;var r=n(7209);((s.prototype=Object.create(r.prototype)).constructor=s).className="Enum";var o=n(8923),i=n(3262);function s(e,t,n,o,i){if(r.call(this,e,n),t&&"object"!=typeof t)throw TypeError("values must be an object");if(this.valuesById={},this.values=Object.create(this.valuesById),this.comment=o,this.comments=i||{},this.reserved=void 0,t)for(var s=Object.keys(t),a=0;a<s.length;++a)"number"==typeof t[s[a]]&&(this.valuesById[this.values[s[a]]=t[s[a]]]=s[a])}s.fromJSON=function(e,t){var n=new s(e,t.values,t.options,t.comment,t.comments);return n.reserved=t.reserved,n},s.prototype.toJSON=function(e){var t=!!e&&Boolean(e.keepComments);return i.toObject(["options",this.options,"values",this.values,"reserved",this.reserved&&this.reserved.length?this.reserved:void 0,"comment",t?this.comment:void 0,"comments",t?this.comments:void 0])},s.prototype.add=function(e,t,n){if(!i.isString(e))throw TypeError("name must be a string");if(!i.isInteger(t))throw TypeError("id must be an integer");if(void 0!==this.values[e])throw Error("duplicate name '"+e+"' in "+this);if(this.isReservedId(t))throw Error("id "+t+" is reserved in "+this);if(this.isReservedName(e))throw Error("name '"+e+"' is reserved in "+this);if(void 0!==this.valuesById[t]){if(!this.options||!this.options.allow_alias)throw Error("duplicate id "+t+" in "+this);this.values[e]=t}else this.valuesById[this.values[e]=t]=e;return this.comments[e]=n||null,this},s.prototype.remove=function(e){if(!i.isString(e))throw TypeError("name must be a string");var t=this.values[e];if(null==t)throw Error("name '"+e+"' does not exist in "+this);return delete this.valuesById[t],delete this.values[e],delete this.comments[e],this},s.prototype.isReservedId=function(e){return o.isReservedId(this.reserved,e)},s.prototype.isReservedName=function(e){return o.isReservedName(this.reserved,e)}},5652:function(e,t,n){"use strict";var r=n(9039);e.exports=r((function(){if("function"==typeof ArrayBuffer){var e=new ArrayBuffer(8);Object.isExtensible(e)&&Object.defineProperty(e,"a",{value:8})}}))},5745:function(e,t,n){"use strict";var r=n(7629);e.exports=function(e,t){return r[e]||(r[e]=t||{})}},5749:function(e,t,n){"use strict";var r=n(788),o=TypeError;e.exports=function(e){if(r(e))throw new o("The method doesn't accept regular expressions");return e}},5806:function(e,t,n){"use strict";n(7764);var r,o=n(6518),i=n(3724),s=n(7416),a=n(4576),u=n(6080),c=n(9504),l=n(6840),d=n(2106),p=n(679),h=n(9297),f=n(4213),v=n(7916),E=n(7680),m=n(8183).codeAt,g=n(6098),y=n(655),_=n(687),O=n(2812),T=n(8406),R=n(1181),I=R.set,C=R.getterFor("URL"),S=T.URLSearchParams,N=T.getState,A=a.URL,b=a.TypeError,M=a.parseInt,w=Math.floor,L=Math.pow,U=c("".charAt),x=c(/./.exec),D=c([].join),P=c(1..toString),k=c([].pop),H=c([].push),F=c("".replace),B=c([].shift),G=c("".split),W=c("".slice),j=c("".toLowerCase),q=c([].unshift),K="Invalid scheme",V="Invalid host",z="Invalid port",J=/[a-z]/i,Y=/[\d+-.a-z]/i,X=/\d/,Q=/^0x/i,$=/^[0-7]+$/,Z=/^\d+$/,ee=/^[\da-f]+$/i,te=/[\0\t\n\r #%/:<>?@[\\\]^|]/,ne=/[\0\t\n\r #/:<>?@[\\\]^|]/,re=/^[\u0000-\u0020]+/,oe=/(^|[^\u0000-\u0020])[\u0000-\u0020]+$/,ie=/[\t\n\r]/g,se=function(e){var t,n,r,o;if("number"==typeof e){for(t=[],n=0;n<4;n++)q(t,e%256),e=w(e/256);return D(t,".")}if("object"==typeof e){for(t="",r=function(e){for(var t=null,n=1,r=null,o=0,i=0;i<8;i++)0!==e[i]?(o>n&&(t=r,n=o),r=null,o=0):(null===r&&(r=i),++o);return o>n?r:t}(e),n=0;n<8;n++)o&&0===e[n]||(o&&(o=!1),r===n?(t+=n?":":"::",o=!0):(t+=P(e[n],16),n<7&&(t+=":")));return"["+t+"]"}return e},ae={},ue=f({},ae,{" ":1,'"':1,"<":1,">":1,"`":1}),ce=f({},ue,{"#":1,"?":1,"{":1,"}":1}),le=f({},ce,{"/":1,":":1,";":1,"=":1,"@":1,"[":1,"\\":1,"]":1,"^":1,"|":1}),de=function(e,t){var n=m(e,0);return n>32&&n<127&&!h(t,e)?e:encodeURIComponent(e)},pe={ftp:21,file:null,http:80,https:443,ws:80,wss:443},he=function(e,t){var n;return 2===e.length&&x(J,U(e,0))&&(":"===(n=U(e,1))||!t&&"|"===n)},fe=function(e){var t;return e.length>1&&he(W(e,0,2))&&(2===e.length||"/"===(t=U(e,2))||"\\"===t||"?"===t||"#"===t)},ve=function(e){return"."===e||"%2e"===j(e)},Ee={},me={},ge={},ye={},_e={},Oe={},Te={},Re={},Ie={},Ce={},Se={},Ne={},Ae={},be={},Me={},we={},Le={},Ue={},xe={},De={},Pe={},ke=function(e,t,n){var r,o,i,s=y(e);if(t){if(o=this.parse(s))throw new b(o);this.searchParams=null}else{if(void 0!==n&&(r=new ke(n,!0)),o=this.parse(s,null,r))throw new b(o);(i=N(new S)).bindURL(this),this.searchParams=i}};ke.prototype={type:"URL",parse:function(e,t,n){var o,i,s,a,u,c=this,l=t||Ee,d=0,p="",f=!1,m=!1,g=!1;for(e=y(e),t||(c.scheme="",c.username="",c.password="",c.host=null,c.port=null,c.path=[],c.query=null,c.fragment=null,c.cannotBeABaseURL=!1,e=F(e,re,""),e=F(e,oe,"$1")),e=F(e,ie,""),o=v(e);d<=o.length;){switch(i=o[d],l){case Ee:if(!i||!x(J,i)){if(t)return K;l=ge;continue}p+=j(i),l=me;break;case me:if(i&&(x(Y,i)||"+"===i||"-"===i||"."===i))p+=j(i);else{if(":"!==i){if(t)return K;p="",l=ge,d=0;continue}if(t&&(c.isSpecial()!==h(pe,p)||"file"===p&&(c.includesCredentials()||null!==c.port)||"file"===c.scheme&&!c.host))return;if(c.scheme=p,t)return void(c.isSpecial()&&pe[c.scheme]===c.port&&(c.port=null));p="","file"===c.scheme?l=be:c.isSpecial()&&n&&n.scheme===c.scheme?l=ye:c.isSpecial()?l=Re:"/"===o[d+1]?(l=_e,d++):(c.cannotBeABaseURL=!0,H(c.path,""),l=xe)}break;case ge:if(!n||n.cannotBeABaseURL&&"#"!==i)return K;if(n.cannotBeABaseURL&&"#"===i){c.scheme=n.scheme,c.path=E(n.path),c.query=n.query,c.fragment="",c.cannotBeABaseURL=!0,l=Pe;break}l="file"===n.scheme?be:Oe;continue;case ye:if("/"!==i||"/"!==o[d+1]){l=Oe;continue}l=Ie,d++;break;case _e:if("/"===i){l=Ce;break}l=Ue;continue;case Oe:if(c.scheme=n.scheme,i===r)c.username=n.username,c.password=n.password,c.host=n.host,c.port=n.port,c.path=E(n.path),c.query=n.query;else if("/"===i||"\\"===i&&c.isSpecial())l=Te;else if("?"===i)c.username=n.username,c.password=n.password,c.host=n.host,c.port=n.port,c.path=E(n.path),c.query="",l=De;else{if("#"!==i){c.username=n.username,c.password=n.password,c.host=n.host,c.port=n.port,c.path=E(n.path),c.path.length--,l=Ue;continue}c.username=n.username,c.password=n.password,c.host=n.host,c.port=n.port,c.path=E(n.path),c.query=n.query,c.fragment="",l=Pe}break;case Te:if(!c.isSpecial()||"/"!==i&&"\\"!==i){if("/"!==i){c.username=n.username,c.password=n.password,c.host=n.host,c.port=n.port,l=Ue;continue}l=Ce}else l=Ie;break;case Re:if(l=Ie,"/"!==i||"/"!==U(p,d+1))continue;d++;break;case Ie:if("/"!==i&&"\\"!==i){l=Ce;continue}break;case Ce:if("@"===i){f&&(p="%40"+p),f=!0,s=v(p);for(var _=0;_<s.length;_++){var O=s[_];if(":"!==O||g){var T=de(O,le);g?c.password+=T:c.username+=T}else g=!0}p=""}else if(i===r||"/"===i||"?"===i||"#"===i||"\\"===i&&c.isSpecial()){if(f&&""===p)return"Invalid authority";d-=v(p).length+1,p="",l=Se}else p+=i;break;case Se:case Ne:if(t&&"file"===c.scheme){l=we;continue}if(":"!==i||m){if(i===r||"/"===i||"?"===i||"#"===i||"\\"===i&&c.isSpecial()){if(c.isSpecial()&&""===p)return V;if(t&&""===p&&(c.includesCredentials()||null!==c.port))return;if(a=c.parseHost(p))return a;if(p="",l=Le,t)return;continue}"["===i?m=!0:"]"===i&&(m=!1),p+=i}else{if(""===p)return V;if(a=c.parseHost(p))return a;if(p="",l=Ae,t===Ne)return}break;case Ae:if(!x(X,i)){if(i===r||"/"===i||"?"===i||"#"===i||"\\"===i&&c.isSpecial()||t){if(""!==p){var R=M(p,10);if(R>65535)return z;c.port=c.isSpecial()&&R===pe[c.scheme]?null:R,p=""}if(t)return;l=Le;continue}return z}p+=i;break;case be:if(c.scheme="file","/"===i||"\\"===i)l=Me;else{if(!n||"file"!==n.scheme){l=Ue;continue}switch(i){case r:c.host=n.host,c.path=E(n.path),c.query=n.query;break;case"?":c.host=n.host,c.path=E(n.path),c.query="",l=De;break;case"#":c.host=n.host,c.path=E(n.path),c.query=n.query,c.fragment="",l=Pe;break;default:fe(D(E(o,d),""))||(c.host=n.host,c.path=E(n.path),c.shortenPath()),l=Ue;continue}}break;case Me:if("/"===i||"\\"===i){l=we;break}n&&"file"===n.scheme&&!fe(D(E(o,d),""))&&(he(n.path[0],!0)?H(c.path,n.path[0]):c.host=n.host),l=Ue;continue;case we:if(i===r||"/"===i||"\\"===i||"?"===i||"#"===i){if(!t&&he(p))l=Ue;else if(""===p){if(c.host="",t)return;l=Le}else{if(a=c.parseHost(p))return a;if("localhost"===c.host&&(c.host=""),t)return;p="",l=Le}continue}p+=i;break;case Le:if(c.isSpecial()){if(l=Ue,"/"!==i&&"\\"!==i)continue}else if(t||"?"!==i)if(t||"#"!==i){if(i!==r&&(l=Ue,"/"!==i))continue}else c.fragment="",l=Pe;else c.query="",l=De;break;case Ue:if(i===r||"/"===i||"\\"===i&&c.isSpecial()||!t&&("?"===i||"#"===i)){if(".."===(u=j(u=p))||"%2e."===u||".%2e"===u||"%2e%2e"===u?(c.shortenPath(),"/"===i||"\\"===i&&c.isSpecial()||H(c.path,"")):ve(p)?"/"===i||"\\"===i&&c.isSpecial()||H(c.path,""):("file"===c.scheme&&!c.path.length&&he(p)&&(c.host&&(c.host=""),p=U(p,0)+":"),H(c.path,p)),p="","file"===c.scheme&&(i===r||"?"===i||"#"===i))for(;c.path.length>1&&""===c.path[0];)B(c.path);"?"===i?(c.query="",l=De):"#"===i&&(c.fragment="",l=Pe)}else p+=de(i,ce);break;case xe:"?"===i?(c.query="",l=De):"#"===i?(c.fragment="",l=Pe):i!==r&&(c.path[0]+=de(i,ae));break;case De:t||"#"!==i?i!==r&&("'"===i&&c.isSpecial()?c.query+="%27":c.query+="#"===i?"%23":de(i,ae)):(c.fragment="",l=Pe);break;case Pe:i!==r&&(c.fragment+=de(i,ue))}d++}},parseHost:function(e){var t,n,r;if("["===U(e,0)){if("]"!==U(e,e.length-1))return V;if(t=function(e){var t,n,r,o,i,s,a,u=[0,0,0,0,0,0,0,0],c=0,l=null,d=0,p=function(){return U(e,d)};if(":"===p()){if(":"!==U(e,1))return;d+=2,l=++c}for(;p();){if(8===c)return;if(":"!==p()){for(t=n=0;n<4&&x(ee,p());)t=16*t+M(p(),16),d++,n++;if("."===p()){if(0===n)return;if(d-=n,c>6)return;for(r=0;p();){if(o=null,r>0){if(!("."===p()&&r<4))return;d++}if(!x(X,p()))return;for(;x(X,p());){if(i=M(p(),10),null===o)o=i;else{if(0===o)return;o=10*o+i}if(o>255)return;d++}u[c]=256*u[c]+o,2!==++r&&4!==r||c++}if(4!==r)return;break}if(":"===p()){if(d++,!p())return}else if(p())return;u[c++]=t}else{if(null!==l)return;d++,l=++c}}if(null!==l)for(s=c-l,c=7;0!==c&&s>0;)a=u[c],u[c--]=u[l+s-1],u[l+--s]=a;else if(8!==c)return;return u}(W(e,1,-1)),!t)return V;this.host=t}else if(this.isSpecial()){if(e=g(e),x(te,e))return V;if(t=function(e){var t,n,r,o,i,s,a,u=G(e,".");if(u.length&&""===u[u.length-1]&&u.length--,(t=u.length)>4)return e;for(n=[],r=0;r<t;r++){if(""===(o=u[r]))return e;if(i=10,o.length>1&&"0"===U(o,0)&&(i=x(Q,o)?16:8,o=W(o,8===i?1:2)),""===o)s=0;else{if(!x(10===i?Z:8===i?$:ee,o))return e;s=M(o,i)}H(n,s)}for(r=0;r<t;r++)if(s=n[r],r===t-1){if(s>=L(256,5-t))return null}else if(s>255)return null;for(a=k(n),r=0;r<n.length;r++)a+=n[r]*L(256,3-r);return a}(e),null===t)return V;this.host=t}else{if(x(ne,e))return V;for(t="",n=v(e),r=0;r<n.length;r++)t+=de(n[r],ae);this.host=t}},cannotHaveUsernamePasswordPort:function(){return!this.host||this.cannotBeABaseURL||"file"===this.scheme},includesCredentials:function(){return""!==this.username||""!==this.password},isSpecial:function(){return h(pe,this.scheme)},shortenPath:function(){var e=this.path,t=e.length;!t||"file"===this.scheme&&1===t&&he(e[0],!0)||e.length--},serialize:function(){var e=this,t=e.scheme,n=e.username,r=e.password,o=e.host,i=e.port,s=e.path,a=e.query,u=e.fragment,c=t+":";return null!==o?(c+="//",e.includesCredentials()&&(c+=n+(r?":"+r:"")+"@"),c+=se(o),null!==i&&(c+=":"+i)):"file"===t&&(c+="//"),c+=e.cannotBeABaseURL?s[0]:s.length?"/"+D(s,"/"):"",null!==a&&(c+="?"+a),null!==u&&(c+="#"+u),c},setHref:function(e){var t=this.parse(e);if(t)throw new b(t);this.searchParams.update()},getOrigin:function(){var e=this.scheme,t=this.port;if("blob"===e)try{return new He(e.path[0]).origin}catch(e){return"null"}return"file"!==e&&this.isSpecial()?e+"://"+se(this.host)+(null!==t?":"+t:""):"null"},getProtocol:function(){return this.scheme+":"},setProtocol:function(e){this.parse(y(e)+":",Ee)},getUsername:function(){return this.username},setUsername:function(e){var t=v(y(e));if(!this.cannotHaveUsernamePasswordPort()){this.username="";for(var n=0;n<t.length;n++)this.username+=de(t[n],le)}},getPassword:function(){return this.password},setPassword:function(e){var t=v(y(e));if(!this.cannotHaveUsernamePasswordPort()){this.password="";for(var n=0;n<t.length;n++)this.password+=de(t[n],le)}},getHost:function(){var e=this.host,t=this.port;return null===e?"":null===t?se(e):se(e)+":"+t},setHost:function(e){this.cannotBeABaseURL||this.parse(e,Se)},getHostname:function(){var e=this.host;return null===e?"":se(e)},setHostname:function(e){this.cannotBeABaseURL||this.parse(e,Ne)},getPort:function(){var e=this.port;return null===e?"":y(e)},setPort:function(e){this.cannotHaveUsernamePasswordPort()||(""===(e=y(e))?this.port=null:this.parse(e,Ae))},getPathname:function(){var e=this.path;return this.cannotBeABaseURL?e[0]:e.length?"/"+D(e,"/"):""},setPathname:function(e){this.cannotBeABaseURL||(this.path=[],this.parse(e,Le))},getSearch:function(){var e=this.query;return e?"?"+e:""},setSearch:function(e){""===(e=y(e))?this.query=null:("?"===U(e,0)&&(e=W(e,1)),this.query="",this.parse(e,De)),this.searchParams.update()},getSearchParams:function(){return this.searchParams.facade},getHash:function(){var e=this.fragment;return e?"#"+e:""},setHash:function(e){""!==(e=y(e))?("#"===U(e,0)&&(e=W(e,1)),this.fragment="",this.parse(e,Pe)):this.fragment=null},update:function(){this.query=this.searchParams.serialize()||null}};var He=function(e){var t=p(this,Fe),n=O(arguments.length,1)>1?arguments[1]:void 0,r=I(t,new ke(e,!1,n));i||(t.href=r.serialize(),t.origin=r.getOrigin(),t.protocol=r.getProtocol(),t.username=r.getUsername(),t.password=r.getPassword(),t.host=r.getHost(),t.hostname=r.getHostname(),t.port=r.getPort(),t.pathname=r.getPathname(),t.search=r.getSearch(),t.searchParams=r.getSearchParams(),t.hash=r.getHash())},Fe=He.prototype,Be=function(e,t){return{get:function(){return C(this)[e]()},set:t&&function(e){return C(this)[t](e)},configurable:!0,enumerable:!0}};if(i&&(d(Fe,"href",Be("serialize","setHref")),d(Fe,"origin",Be("getOrigin")),d(Fe,"protocol",Be("getProtocol","setProtocol")),d(Fe,"username",Be("getUsername","setUsername")),d(Fe,"password",Be("getPassword","setPassword")),d(Fe,"host",Be("getHost","setHost")),d(Fe,"hostname",Be("getHostname","setHostname")),d(Fe,"port",Be("getPort","setPort")),d(Fe,"pathname",Be("getPathname","setPathname")),d(Fe,"search",Be("getSearch","setSearch")),d(Fe,"searchParams",Be("getSearchParams")),d(Fe,"hash",Be("getHash","setHash"))),l(Fe,"toJSON",(function(){return C(this).serialize()}),{enumerable:!0}),l(Fe,"toString",(function(){return C(this).serialize()}),{enumerable:!0}),A){var Ge=A.createObjectURL,We=A.revokeObjectURL;Ge&&l(He,"createObjectURL",u(Ge,A)),We&&l(He,"revokeObjectURL",u(We,A))}_(He,"URL"),o({global:!0,constructor:!0,forced:!s,sham:!i},{URL:He})},5823:function(e,t,n){"use strict";var r=n(6518),o=n(4576),i=n(9565),s=n(3724),a=n(2805),u=n(4644),c=n(6346),l=n(679),d=n(6980),p=n(6699),h=n(2087),f=n(8014),v=n(7696),E=n(8229),m=n(8319),g=n(6969),y=n(9297),_=n(6955),O=n(34),T=n(757),R=n(2360),I=n(1625),C=n(2967),S=n(8480).f,N=n(3251),A=n(9213).forEach,b=n(7633),M=n(2106),w=n(4913),L=n(7347),U=n(5370),x=n(1181),D=n(3167),P=x.get,k=x.set,H=x.enforce,F=w.f,B=L.f,G=o.RangeError,W=c.ArrayBuffer,j=W.prototype,q=c.DataView,K=u.NATIVE_ARRAY_BUFFER_VIEWS,V=u.TYPED_ARRAY_TAG,z=u.TypedArray,J=u.TypedArrayPrototype,Y=u.isTypedArray,X="BYTES_PER_ELEMENT",Q="Wrong length",$=function(e,t){M(e,t,{configurable:!0,get:function(){return P(this)[t]}})},Z=function(e){var t;return I(j,e)||"ArrayBuffer"===(t=_(e))||"SharedArrayBuffer"===t},ee=function(e,t){return Y(e)&&!T(t)&&t in e&&h(+t)&&t>=0},te=function(e,t){return t=g(t),ee(e,t)?d(2,e[t]):B(e,t)},ne=function(e,t,n){return t=g(t),!(ee(e,t)&&O(n)&&y(n,"value"))||y(n,"get")||y(n,"set")||n.configurable||y(n,"writable")&&!n.writable||y(n,"enumerable")&&!n.enumerable?F(e,t,n):(e[t]=n.value,e)};s?(K||(L.f=te,w.f=ne,$(J,"buffer"),$(J,"byteOffset"),$(J,"byteLength"),$(J,"length")),r({target:"Object",stat:!0,forced:!K},{getOwnPropertyDescriptor:te,defineProperty:ne}),e.exports=function(e,t,n){var s=e.match(/\d+/)[0]/8,u=e+(n?"Clamped":"")+"Array",c="get"+e,d="set"+e,h=o[u],g=h,y=g&&g.prototype,_={},T=function(e,t){F(e,t,{get:function(){return function(e,t){var n=P(e);return n.view[c](t*s+n.byteOffset,!0)}(this,t)},set:function(e){return function(e,t,r){var o=P(e);o.view[d](t*s+o.byteOffset,n?m(r):r,!0)}(this,t,e)},enumerable:!0})};K?a&&(g=t((function(e,t,n,r){return l(e,y),D(O(t)?Z(t)?void 0!==r?new h(t,E(n,s),r):void 0!==n?new h(t,E(n,s)):new h(t):Y(t)?U(g,t):i(N,g,t):new h(v(t)),e,g)})),C&&C(g,z),A(S(h),(function(e){e in g||p(g,e,h[e])})),g.prototype=y):(g=t((function(e,t,n,r){l(e,y);var o,a,u,c=0,d=0;if(O(t)){if(!Z(t))return Y(t)?U(g,t):i(N,g,t);o=t,d=E(n,s);var p=t.byteLength;if(void 0===r){if(p%s)throw new G(Q);if((a=p-d)<0)throw new G(Q)}else if((a=f(r)*s)+d>p)throw new G(Q);u=a/s}else u=v(t),o=new W(a=u*s);for(k(e,{buffer:o,byteOffset:d,byteLength:a,length:u,view:new q(o)});c<u;)T(e,c++)})),C&&C(g,z),y=g.prototype=R(J)),y.constructor!==g&&p(y,"constructor",g),H(y).TypedArrayConstructor=g,V&&p(y,V,u);var I=g!==h;_[u]=g,r({global:!0,constructor:!0,forced:I,sham:!K},_),X in g||p(g,X,s),X in y||p(y,X,s),b(u)}):e.exports=function(){}},5854:function(e,t,n){"use strict";var r=n(2777),o=TypeError;e.exports=function(e){var t=r(e,"number");if("number"==typeof t)throw new o("Can't convert number to bigint");return BigInt(t)}},5917:function(e,t,n){"use strict";var r=n(3724),o=n(9039),i=n(4055);e.exports=!r&&!o((function(){return 7!==Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a}))},5966:function(e,t,n){"use strict";var r=n(9306),o=n(4117);e.exports=function(e,t){var n=e[t];return o(n)?void 0:r(n)}},6031:function(e,t,n){"use strict";n(5575),n(4599)},6033:function(e,t,n){"use strict";n(8523)},6043:function(e,t,n){"use strict";var r=n(9306),o=TypeError,i=function(e){var t,n;this.promise=new e((function(e,r){if(void 0!==t||void 0!==n)throw new o("Bad Promise constructor");t=e,n=r})),this.resolve=r(t),this.reject=r(n)};e.exports.f=function(e){return new i(e)}},6072:function(e,t,n){"use strict";var r=n(4644),o=n(926).right,i=r.aTypedArray;(0,r.exportTypedArrayMethod)("reduceRight",(function(e){var t=arguments.length;return o(i(this),e,t,t>1?arguments[1]:void 0)}))},6080:function(e,t,n){"use strict";var r=n(7476),o=n(9306),i=n(616),s=r(r.bind);e.exports=function(e,t){return o(e),void 0===t?e:i?s(e,t):function(){return e.apply(t,arguments)}}},6098:function(e,t,n){"use strict";var r=n(9504),o=2147483647,i=/[^\0-\u007E]/,s=/[.\u3002\uFF0E\uFF61]/g,a="Overflow: input needs wider integers to process",u=RangeError,c=r(s.exec),l=Math.floor,d=String.fromCharCode,p=r("".charCodeAt),h=r([].join),f=r([].push),v=r("".replace),E=r("".split),m=r("".toLowerCase),g=function(e){return e+22+75*(e<26)},y=function(e,t,n){var r=0;for(e=n?l(e/700):e>>1,e+=l(e/t);e>455;)e=l(e/35),r+=36;return l(r+36*e/(e+38))},_=function(e){var t=[];e=function(e){for(var t=[],n=0,r=e.length;n<r;){var o=p(e,n++);if(o>=55296&&o<=56319&&n<r){var i=p(e,n++);56320==(64512&i)?f(t,((1023&o)<<10)+(1023&i)+65536):(f(t,o),n--)}else f(t,o)}return t}(e);var n,r,i=e.length,s=128,c=0,v=72;for(n=0;n<e.length;n++)(r=e[n])<128&&f(t,d(r));var E=t.length,m=E;for(E&&f(t,"-");m<i;){var _=o;for(n=0;n<e.length;n++)(r=e[n])>=s&&r<_&&(_=r);var O=m+1;if(_-s>l((o-c)/O))throw new u(a);for(c+=(_-s)*O,s=_,n=0;n<e.length;n++){if((r=e[n])<s&&++c>o)throw new u(a);if(r===s){for(var T=c,R=36;;){var I=R<=v?1:R>=v+26?26:R-v;if(T<I)break;var C=T-I,S=36-I;f(t,d(g(I+C%S))),T=l(C/S),R+=36}f(t,d(g(T))),v=y(c,O,m===E),c=0,m++}}c++,s++}return h(t,"")};e.exports=function(e){var t,n,r=[],o=E(v(m(e),s,"."),".");for(t=0;t<o.length;t++)n=o[t],f(r,c(i,n)?"xn--"+_(n):n);return h(r,".")}},6099:function(e,t,n){"use strict";var r=n(2140),o=n(6840),i=n(3179);r||o(Object.prototype,"toString",i,{unsafe:!0})},6119:function(e,t,n){"use strict";var r=n(5745),o=n(3392),i=r("keys");e.exports=function(e){return i[e]||(i[e]=o(e))}},6193:function(e,t,n){"use strict";var r=n(4215);e.exports="NODE"===r},6198:function(e,t,n){"use strict";var r=n(8014);e.exports=function(e){return r(e.length)}},6237:function(e,t,n){"use strict";e.exports=u;var r,o=n(3610),i=o.LongBits,s=o.utf8;function a(e,t){return RangeError("index out of range: "+e.pos+" + "+(t||1)+" > "+e.len)}function u(e){this.buf=e,this.pos=0,this.len=e.length}var c,l="undefined"!=typeof Uint8Array?function(e){if(e instanceof Uint8Array||Array.isArray(e))return new u(e);throw Error("illegal buffer")}:function(e){if(Array.isArray(e))return new u(e);throw Error("illegal buffer")},d=function(){return o.Buffer?function(e){return(u.create=function(e){return o.Buffer.isBuffer(e)?new r(e):l(e)})(e)}:l};function p(){var e=new i(0,0),t=0;if(!(this.len-this.pos>4)){for(;t<3;++t){if(this.pos>=this.len)throw a(this);if(e.lo=(e.lo|(127&this.buf[this.pos])<<7*t)>>>0,this.buf[this.pos++]<128)return e}return e.lo=(e.lo|(127&this.buf[this.pos++])<<7*t)>>>0,e}for(;t<4;++t)if(e.lo=(e.lo|(127&this.buf[this.pos])<<7*t)>>>0,this.buf[this.pos++]<128)return e;if(e.lo=(e.lo|(127&this.buf[this.pos])<<28)>>>0,e.hi=(e.hi|(127&this.buf[this.pos])>>4)>>>0,this.buf[this.pos++]<128)return e;if(t=0,this.len-this.pos>4){for(;t<5;++t)if(e.hi=(e.hi|(127&this.buf[this.pos])<<7*t+3)>>>0,this.buf[this.pos++]<128)return e}else for(;t<5;++t){if(this.pos>=this.len)throw a(this);if(e.hi=(e.hi|(127&this.buf[this.pos])<<7*t+3)>>>0,this.buf[this.pos++]<128)return e}throw Error("invalid varint encoding")}function h(e,t){return(e[t-4]|e[t-3]<<8|e[t-2]<<16|e[t-1]<<24)>>>0}function f(){if(this.pos+8>this.len)throw a(this,8);return new i(h(this.buf,this.pos+=4),h(this.buf,this.pos+=4))}u.create=d(),u.prototype._slice=o.Array.prototype.subarray||o.Array.prototype.slice,u.prototype.uint32=(c=4294967295,function(){if(c=(127&this.buf[this.pos])>>>0,this.buf[this.pos++]<128)return c;if(c=(c|(127&this.buf[this.pos])<<7)>>>0,this.buf[this.pos++]<128)return c;if(c=(c|(127&this.buf[this.pos])<<14)>>>0,this.buf[this.pos++]<128)return c;if(c=(c|(127&this.buf[this.pos])<<21)>>>0,this.buf[this.pos++]<128)return c;if(c=(c|(15&this.buf[this.pos])<<28)>>>0,this.buf[this.pos++]<128)return c;if((this.pos+=5)>this.len)throw this.pos=this.len,a(this,10);return c}),u.prototype.int32=function(){return 0|this.uint32()},u.prototype.sint32=function(){var e=this.uint32();return e>>>1^-(1&e)},u.prototype.bool=function(){return 0!==this.uint32()},u.prototype.fixed32=function(){if(this.pos+4>this.len)throw a(this,4);return h(this.buf,this.pos+=4)},u.prototype.sfixed32=function(){if(this.pos+4>this.len)throw a(this,4);return 0|h(this.buf,this.pos+=4)},u.prototype.float=function(){if(this.pos+4>this.len)throw a(this,4);var e=o.float.readFloatLE(this.buf,this.pos);return this.pos+=4,e},u.prototype.double=function(){if(this.pos+8>this.len)throw a(this,4);var e=o.float.readDoubleLE(this.buf,this.pos);return this.pos+=8,e},u.prototype.bytes=function(){var e=this.uint32(),t=this.pos,n=this.pos+e;if(n>this.len)throw a(this,e);return this.pos+=e,Array.isArray(this.buf)?this.buf.slice(t,n):t===n?new this.buf.constructor(0):this._slice.call(this.buf,t,n)},u.prototype.string=function(){var e=this.bytes();return s.read(e,0,e.length)},u.prototype.skip=function(e){if("number"==typeof e){if(this.pos+e>this.len)throw a(this,e);this.pos+=e}else do{if(this.pos>=this.len)throw a(this)}while(128&this.buf[this.pos++]);return this},u.prototype.skipType=function(e){switch(e){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:for(;4!=(e=7&this.uint32());)this.skipType(e);break;case 5:this.skip(4);break;default:throw Error("invalid wire type "+e+" at offset "+this.pos)}return this},u._configure=function(e){r=e,u.create=d(),r._configure();var t=o.Long?"toLong":"toNumber";o.merge(u.prototype,{int64:function(){return p.call(this)[t](!1)},uint64:function(){return p.call(this)[t](!0)},sint64:function(){return p.call(this).zzDecode()[t](!1)},fixed64:function(){return f.call(this)[t](!0)},sfixed64:function(){return f.call(this)[t](!1)}})}},6269:function(e){"use strict";e.exports={}},6279:function(e,t,n){"use strict";var r=n(6840);e.exports=function(e,t,n){for(var o in t)r(e,o,t[o],n);return e}},6319:function(e,t,n){"use strict";var r=n(8551),o=n(9539);e.exports=function(e,t,n,i){try{return i?t(r(n)[0],n[1]):t(n)}catch(t){o(e,"throw",t)}}},6346:function(e,t,n){"use strict";var r=n(4576),o=n(9504),i=n(3724),s=n(7811),a=n(350),u=n(6699),c=n(2106),l=n(6279),d=n(9039),p=n(679),h=n(1291),f=n(8014),v=n(7696),E=n(5617),m=n(8490),g=n(2787),y=n(2967),_=n(4373),O=n(7680),T=n(3167),R=n(7740),I=n(687),C=n(1181),S=a.PROPER,N=a.CONFIGURABLE,A="ArrayBuffer",b="DataView",M="prototype",w="Wrong index",L=C.getterFor(A),U=C.getterFor(b),x=C.set,D=r[A],P=D,k=P&&P[M],H=r[b],F=H&&H[M],B=Object.prototype,G=r.Array,W=r.RangeError,j=o(_),q=o([].reverse),K=m.pack,V=m.unpack,z=function(e){return[255&e]},J=function(e){return[255&e,e>>8&255]},Y=function(e){return[255&e,e>>8&255,e>>16&255,e>>24&255]},X=function(e){return e[3]<<24|e[2]<<16|e[1]<<8|e[0]},Q=function(e){return K(E(e),23,4)},$=function(e){return K(e,52,8)},Z=function(e,t,n){c(e[M],t,{configurable:!0,get:function(){return n(this)[t]}})},ee=function(e,t,n,r){var o=U(e),i=v(n),s=!!r;if(i+t>o.byteLength)throw new W(w);var a=o.bytes,u=i+o.byteOffset,c=O(a,u,u+t);return s?c:q(c)},te=function(e,t,n,r,o,i){var s=U(e),a=v(n),u=r(+o),c=!!i;if(a+t>s.byteLength)throw new W(w);for(var l=s.bytes,d=a+s.byteOffset,p=0;p<t;p++)l[d+p]=u[c?p:t-p-1]};if(s){var ne=S&&D.name!==A;d((function(){D(1)}))&&d((function(){new D(-1)}))&&!d((function(){return new D,new D(1.5),new D(NaN),1!==D.length||ne&&!N}))?ne&&N&&u(D,"name",A):((P=function(e){return p(this,k),T(new D(v(e)),this,P)})[M]=k,k.constructor=P,R(P,D)),y&&g(F)!==B&&y(F,B);var re=new H(new P(2)),oe=o(F.setInt8);re.setInt8(0,2147483648),re.setInt8(1,2147483649),!re.getInt8(0)&&re.getInt8(1)||l(F,{setInt8:function(e,t){oe(this,e,t<<24>>24)},setUint8:function(e,t){oe(this,e,t<<24>>24)}},{unsafe:!0})}else k=(P=function(e){p(this,k);var t=v(e);x(this,{type:A,bytes:j(G(t),0),byteLength:t}),i||(this.byteLength=t,this.detached=!1)})[M],F=(H=function(e,t,n){p(this,F),p(e,k);var r=L(e),o=r.byteLength,s=h(t);if(s<0||s>o)throw new W("Wrong offset");if(s+(n=void 0===n?o-s:f(n))>o)throw new W("Wrong length");x(this,{type:b,buffer:e,byteLength:n,byteOffset:s,bytes:r.bytes}),i||(this.buffer=e,this.byteLength=n,this.byteOffset=s)})[M],i&&(Z(P,"byteLength",L),Z(H,"buffer",U),Z(H,"byteLength",U),Z(H,"byteOffset",U)),l(F,{getInt8:function(e){return ee(this,1,e)[0]<<24>>24},getUint8:function(e){return ee(this,1,e)[0]},getInt16:function(e){var t=ee(this,2,e,arguments.length>1&&arguments[1]);return(t[1]<<8|t[0])<<16>>16},getUint16:function(e){var t=ee(this,2,e,arguments.length>1&&arguments[1]);return t[1]<<8|t[0]},getInt32:function(e){return X(ee(this,4,e,arguments.length>1&&arguments[1]))},getUint32:function(e){return X(ee(this,4,e,arguments.length>1&&arguments[1]))>>>0},getFloat32:function(e){return V(ee(this,4,e,arguments.length>1&&arguments[1]),23)},getFloat64:function(e){return V(ee(this,8,e,arguments.length>1&&arguments[1]),52)},setInt8:function(e,t){te(this,1,e,z,t)},setUint8:function(e,t){te(this,1,e,z,t)},setInt16:function(e,t){te(this,2,e,J,t,arguments.length>2&&arguments[2])},setUint16:function(e,t){te(this,2,e,J,t,arguments.length>2&&arguments[2])},setInt32:function(e,t){te(this,4,e,Y,t,arguments.length>2&&arguments[2])},setUint32:function(e,t){te(this,4,e,Y,t,arguments.length>2&&arguments[2])},setFloat32:function(e,t){te(this,4,e,Q,t,arguments.length>2&&arguments[2])},setFloat64:function(e,t){te(this,8,e,$,t,arguments.length>2&&arguments[2])}});I(P,A),I(H,b),e.exports={ArrayBuffer:P,DataView:H}},6395:function(e){"use strict";e.exports=!1},6434:function(e,t,n){"use strict";var r=t,o=n(2551);r[".google.protobuf.Any"]={fromObject:function(e){if(e&&e["@type"]){var t=e["@type"].substring(e["@type"].lastIndexOf("/")+1),n=this.lookup(t);if(n){var r="."===e["@type"].charAt(0)?e["@type"].substr(1):e["@type"];return-1===r.indexOf("/")&&(r="/"+r),this.create({type_url:r,value:n.encode(n.fromObject(e)).finish()})}}return this.fromObject(e)},toObject:function(e,t){var n="",r="";if(t&&t.json&&e.type_url&&e.value){r=e.type_url.substring(e.type_url.lastIndexOf("/")+1),n=e.type_url.substring(0,e.type_url.lastIndexOf("/")+1);var i=this.lookup(r);i&&(e=i.decode(e.value))}if(!(e instanceof this.ctor)&&e instanceof o){var s=e.$type.toObject(e,t);return""===n&&(n="type.googleapis.com/"),r=n+("."===e.$type.fullName[0]?e.$type.fullName.substr(1):e.$type.fullName),s["@type"]=r,s}return this.toObject(e,t)}}},6468:function(e,t,n){"use strict";var r=n(6518),o=n(4576),i=n(9504),s=n(2796),a=n(6840),u=n(3451),c=n(2652),l=n(679),d=n(4901),p=n(4117),h=n(34),f=n(9039),v=n(4428),E=n(687),m=n(3167);e.exports=function(e,t,n){var g=-1!==e.indexOf("Map"),y=-1!==e.indexOf("Weak"),_=g?"set":"add",O=o[e],T=O&&O.prototype,R=O,I={},C=function(e){var t=i(T[e]);a(T,e,"add"===e?function(e){return t(this,0===e?0:e),this}:"delete"===e?function(e){return!(y&&!h(e))&&t(this,0===e?0:e)}:"get"===e?function(e){return y&&!h(e)?void 0:t(this,0===e?0:e)}:"has"===e?function(e){return!(y&&!h(e))&&t(this,0===e?0:e)}:function(e,n){return t(this,0===e?0:e,n),this})};if(s(e,!d(O)||!(y||T.forEach&&!f((function(){(new O).entries().next()})))))R=n.getConstructor(t,e,g,_),u.enable();else if(s(e,!0)){var S=new R,N=S[_](y?{}:-0,1)!==S,A=f((function(){S.has(1)})),b=v((function(e){new O(e)})),M=!y&&f((function(){for(var e=new O,t=5;t--;)e[_](t,t);return!e.has(-0)}));b||((R=t((function(e,t){l(e,T);var n=m(new O,e,R);return p(t)||c(t,n[_],{that:n,AS_ENTRIES:g}),n}))).prototype=T,T.constructor=R),(A||M)&&(C("delete"),C("has"),g&&C("get")),(M||N)&&C(_),y&&T.clear&&delete T.clear}return I[e]=R,r({global:!0,constructor:!0,forced:R!==O},I),E(R,e),y||n.setStrong(R,e,g),R}},6469:function(e,t,n){"use strict";var r=n(8227),o=n(2360),i=n(4913).f,s=r("unscopables"),a=Array.prototype;void 0===a[s]&&i(a,s,{configurable:!0,value:o(null)}),e.exports=function(e){a[s][e]=!0}},6499:function(e,t,n){"use strict";var r=n(6518),o=n(9565),i=n(9306),s=n(6043),a=n(1103),u=n(2652);r({target:"Promise",stat:!0,forced:n(537)},{all:function(e){var t=this,n=s.f(t),r=n.resolve,c=n.reject,l=a((function(){var n=i(t.resolve),s=[],a=0,l=1;u(e,(function(e){var i=a++,u=!1;l++,o(n,t,e).then((function(e){u||(u=!0,s[i]=e,--l||r(s))}),c)})),--l||r(s)}));return l.error&&c(l.value),n.promise}})},6518:function(e,t,n){"use strict";var r=n(4576),o=n(7347).f,i=n(6699),s=n(6840),a=n(9433),u=n(7740),c=n(2796);e.exports=function(e,t){var n,l,d,p,h,f=e.target,v=e.global,E=e.stat;if(n=v?r:E?r[f]||a(f,{}):r[f]&&r[f].prototype)for(l in t){if(p=t[l],d=e.dontCallGetSet?(h=o(n,l))&&h.value:n[l],!c(v?l:f+(E?".":"#")+l,e.forced)&&void 0!==d){if(typeof p==typeof d)continue;u(p,d)}(e.sham||d&&d.sham)&&i(p,"sham",!0),s(n,l,p,e)}}},6614:function(e,t,n){"use strict";var r=n(4644),o=n(8014),i=n(5610),s=r.aTypedArray,a=r.getTypedArrayConstructor;(0,r.exportTypedArrayMethod)("subarray",(function(e,t){var n=s(this),r=n.length,u=i(e,r);return new(a(n))(n.buffer,n.byteOffset+u*n.BYTES_PER_ELEMENT,o((void 0===t?r:i(t,r))-u))}))},6651:function(e,t,n){"use strict";var r=n(4644),o=n(9617).indexOf,i=r.aTypedArray;(0,r.exportTypedArrayMethod)("indexOf",(function(e){return o(i(this),e,arguments.length>1?arguments[1]:void 0)}))},6699:function(e,t,n){"use strict";var r=n(3724),o=n(4913),i=n(6980);e.exports=r?function(e,t,n){return o.f(e,t,i(1,n))}:function(e,t,n){return e[t]=n,e}},6706:function(e,t,n){"use strict";var r=n(9504),o=n(9306);e.exports=function(e,t,n){try{return r(o(Object.getOwnPropertyDescriptor(e,t)[n]))}catch(e){}}},6761:function(e,t,n){"use strict";var r=n(6518),o=n(4576),i=n(9565),s=n(9504),a=n(6395),u=n(3724),c=n(4495),l=n(9039),d=n(9297),p=n(1625),h=n(8551),f=n(5397),v=n(6969),E=n(655),m=n(6980),g=n(2360),y=n(1072),_=n(8480),O=n(298),T=n(3717),R=n(7347),I=n(4913),C=n(6801),S=n(8773),N=n(6840),A=n(2106),b=n(5745),M=n(6119),w=n(421),L=n(3392),U=n(8227),x=n(1951),D=n(511),P=n(8242),k=n(687),H=n(1181),F=n(9213).forEach,B=M("hidden"),G="Symbol",W="prototype",j=H.set,q=H.getterFor(G),K=Object[W],V=o.Symbol,z=V&&V[W],J=o.RangeError,Y=o.TypeError,X=o.QObject,Q=R.f,$=I.f,Z=O.f,ee=S.f,te=s([].push),ne=b("symbols"),re=b("op-symbols"),oe=b("wks"),ie=!X||!X[W]||!X[W].findChild,se=function(e,t,n){var r=Q(K,t);r&&delete K[t],$(e,t,n),r&&e!==K&&$(K,t,r)},ae=u&&l((function(){return 7!==g($({},"a",{get:function(){return $(this,"a",{value:7}).a}})).a}))?se:$,ue=function(e,t){var n=ne[e]=g(z);return j(n,{type:G,tag:e,description:t}),u||(n.description=t),n},ce=function(e,t,n){e===K&&ce(re,t,n),h(e);var r=v(t);return h(n),d(ne,r)?(n.enumerable?(d(e,B)&&e[B][r]&&(e[B][r]=!1),n=g(n,{enumerable:m(0,!1)})):(d(e,B)||$(e,B,m(1,g(null))),e[B][r]=!0),ae(e,r,n)):$(e,r,n)},le=function(e,t){h(e);var n=f(t),r=y(n).concat(fe(n));return F(r,(function(t){u&&!i(de,n,t)||ce(e,t,n[t])})),e},de=function(e){var t=v(e),n=i(ee,this,t);return!(this===K&&d(ne,t)&&!d(re,t))&&(!(n||!d(this,t)||!d(ne,t)||d(this,B)&&this[B][t])||n)},pe=function(e,t){var n=f(e),r=v(t);if(n!==K||!d(ne,r)||d(re,r)){var o=Q(n,r);return!o||!d(ne,r)||d(n,B)&&n[B][r]||(o.enumerable=!0),o}},he=function(e){var t=Z(f(e)),n=[];return F(t,(function(e){d(ne,e)||d(w,e)||te(n,e)})),n},fe=function(e){var t=e===K,n=Z(t?re:f(e)),r=[];return F(n,(function(e){!d(ne,e)||t&&!d(K,e)||te(r,ne[e])})),r};c||(V=function(){if(p(z,this))throw new Y("Symbol is not a constructor");var e=arguments.length&&void 0!==arguments[0]?E(arguments[0]):void 0,t=L(e),n=function(e){var r=void 0===this?o:this;r===K&&i(n,re,e),d(r,B)&&d(r[B],t)&&(r[B][t]=!1);var s=m(1,e);try{ae(r,t,s)}catch(e){if(!(e instanceof J))throw e;se(r,t,s)}};return u&&ie&&ae(K,t,{configurable:!0,set:n}),ue(t,e)},N(z=V[W],"toString",(function(){return q(this).tag})),N(V,"withoutSetter",(function(e){return ue(L(e),e)})),S.f=de,I.f=ce,C.f=le,R.f=pe,_.f=O.f=he,T.f=fe,x.f=function(e){return ue(U(e),e)},u&&(A(z,"description",{configurable:!0,get:function(){return q(this).description}}),a||N(K,"propertyIsEnumerable",de,{unsafe:!0}))),r({global:!0,constructor:!0,wrap:!0,forced:!c,sham:!c},{Symbol:V}),F(y(oe),(function(e){D(e)})),r({target:G,stat:!0,forced:!c},{useSetter:function(){ie=!0},useSimple:function(){ie=!1}}),r({target:"Object",stat:!0,forced:!c,sham:!u},{create:function(e,t){return void 0===t?g(e):le(g(e),t)},defineProperty:ce,defineProperties:le,getOwnPropertyDescriptor:pe}),r({target:"Object",stat:!0,forced:!c},{getOwnPropertyNames:he}),P(),k(V,G),w[B]=!0},6801:function(e,t,n){"use strict";var r=n(3724),o=n(8686),i=n(4913),s=n(8551),a=n(5397),u=n(1072);t.f=r&&!o?Object.defineProperties:function(e,t){s(e);for(var n,r=a(t),o=u(t),c=o.length,l=0;c>l;)i.f(e,n=o[l++],r[n]);return e}},6812:function(e,t,n){"use strict";var r=n(4644),o=n(8745),i=n(8379),s=r.aTypedArray;(0,r.exportTypedArrayMethod)("lastIndexOf",(function(e){var t=arguments.length;return o(i,s(this),t>1?[e,arguments[1]]:[e])}))},6823:function(e){"use strict";var t=String;e.exports=function(e){try{return t(e)}catch(e){return"Object"}}},6837:function(e){"use strict";var t=TypeError;e.exports=function(e){if(e>9007199254740991)throw t("Maximum allowed index exceeded");return e}},6840:function(e,t,n){"use strict";var r=n(4901),o=n(4913),i=n(283),s=n(9433);e.exports=function(e,t,n,a){a||(a={});var u=a.enumerable,c=void 0!==a.name?a.name:t;if(r(n)&&i(n,c,a),a.global)u?e[t]=n:s(t,n);else{try{a.unsafe?e[t]&&(u=!0):delete e[t]}catch(e){}u?e[t]=n:o.f(e,t,{value:n,enumerable:!1,configurable:!a.nonConfigurable,writable:!a.nonWritable})}return e}},6933:function(e,t,n){"use strict";var r=n(9504),o=n(4376),i=n(4901),s=n(2195),a=n(655),u=r([].push);e.exports=function(e){if(i(e))return e;if(o(e)){for(var t=e.length,n=[],r=0;r<t;r++){var c=e[r];"string"==typeof c?u(n,c):"number"!=typeof c&&"Number"!==s(c)&&"String"!==s(c)||u(n,a(c))}var l=n.length,d=!0;return function(e,t){if(d)return d=!1,t;if(o(this))return t;for(var r=0;r<l;r++)if(n[r]===e)return t}}}},6938:function(e,t,n){"use strict";var r=n(2360),o=n(2106),i=n(6279),s=n(6080),a=n(679),u=n(4117),c=n(2652),l=n(1088),d=n(2529),p=n(7633),h=n(3724),f=n(3451).fastKey,v=n(1181),E=v.set,m=v.getterFor;e.exports={getConstructor:function(e,t,n,l){var d=e((function(e,o){a(e,p),E(e,{type:t,index:r(null),first:null,last:null,size:0}),h||(e.size=0),u(o)||c(o,e[l],{that:e,AS_ENTRIES:n})})),p=d.prototype,v=m(t),g=function(e,t,n){var r,o,i=v(e),s=y(e,t);return s?s.value=n:(i.last=s={index:o=f(t,!0),key:t,value:n,previous:r=i.last,next:null,removed:!1},i.first||(i.first=s),r&&(r.next=s),h?i.size++:e.size++,"F"!==o&&(i.index[o]=s)),e},y=function(e,t){var n,r=v(e),o=f(t);if("F"!==o)return r.index[o];for(n=r.first;n;n=n.next)if(n.key===t)return n};return i(p,{clear:function(){for(var e=v(this),t=e.first;t;)t.removed=!0,t.previous&&(t.previous=t.previous.next=null),t=t.next;e.first=e.last=null,e.index=r(null),h?e.size=0:this.size=0},delete:function(e){var t=this,n=v(t),r=y(t,e);if(r){var o=r.next,i=r.previous;delete n.index[r.index],r.removed=!0,i&&(i.next=o),o&&(o.previous=i),n.first===r&&(n.first=o),n.last===r&&(n.last=i),h?n.size--:t.size--}return!!r},forEach:function(e){for(var t,n=v(this),r=s(e,arguments.length>1?arguments[1]:void 0);t=t?t.next:n.first;)for(r(t.value,t.key,this);t&&t.removed;)t=t.previous},has:function(e){return!!y(this,e)}}),i(p,n?{get:function(e){var t=y(this,e);return t&&t.value},set:function(e,t){return g(this,0===e?0:e,t)}}:{add:function(e){return g(this,e=0===e?0:e,e)}}),h&&o(p,"size",{configurable:!0,get:function(){return v(this).size}}),d},setStrong:function(e,t,n){var r=t+" Iterator",o=m(t),i=m(r);l(e,t,(function(e,t){E(this,{type:r,target:e,state:o(e),kind:t,last:null})}),(function(){for(var e=i(this),t=e.kind,n=e.last;n&&n.removed;)n=n.previous;return e.target&&(e.last=n=n?n.next:e.state.first)?d("keys"===t?n.key:"values"===t?n.value:[n.key,n.value],!1):(e.target=null,d(void 0,!0))}),n?"entries":"values",!n,!0),p(t)}}},6955:function(e,t,n){"use strict";var r=n(2140),o=n(4901),i=n(2195),s=n(8227)("toStringTag"),a=Object,u="Arguments"===i(function(){return arguments}());e.exports=r?i:function(e){var t,n,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(e){}}(t=a(e),s))?n:u?i(t):"Object"===(r=i(t))&&o(t.callee)?"Arguments":r}},6969:function(e,t,n){"use strict";var r=n(2777),o=n(757);e.exports=function(e){var t=r(e,"string");return o(t)?t:t+""}},6980:function(e){"use strict";e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},7029:function(e,t,n){"use strict";var r=n(8981),o=n(5610),i=n(6198),s=n(4606),a=Math.min;e.exports=[].copyWithin||function(e,t){var n=r(this),u=i(n),c=o(e,u),l=o(t,u),d=arguments.length>2?arguments[2]:void 0,p=a((void 0===d?u:o(d,u))-l,u-c),h=1;for(l<c&&c<l+p&&(h=-1,l+=p-1,c+=p-1);p-- >0;)l in n?n[c]=n[l]:s(n,c),c+=h,l+=h;return n}},7040:function(e,t,n){"use strict";var r=n(4495);e.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},7055:function(e,t,n){"use strict";var r=n(9504),o=n(9039),i=n(2195),s=Object,a=r("".split);e.exports=o((function(){return!s("z").propertyIsEnumerable(0)}))?function(e){return"String"===i(e)?a(e,""):s(e)}:s},7208:function(e,t,n){"use strict";var r=n(6518),o=n(9565);r({target:"URL",proto:!0,enumerable:!0},{toJSON:function(){return o(URL.prototype.toString,this)}})},7209:function(e,t,n){"use strict";e.exports=i,i.className="ReflectionObject";var r,o=n(3262);function i(e,t){if(!o.isString(e))throw TypeError("name must be a string");if(t&&!o.isObject(t))throw TypeError("options must be an object");this.options=t,this.parsedOptions=null,this.name=e,this.parent=null,this.resolved=!1,this.comment=null,this.filename=null}Object.defineProperties(i.prototype,{root:{get:function(){for(var e=this;null!==e.parent;)e=e.parent;return e}},fullName:{get:function(){for(var e=[this.name],t=this.parent;t;)e.unshift(t.name),t=t.parent;return e.join(".")}}}),i.prototype.toJSON=function(){throw Error()},i.prototype.onAdd=function(e){this.parent&&this.parent!==e&&this.parent.remove(this),this.parent=e,this.resolved=!1;var t=e.root;t instanceof r&&t._handleAdd(this)},i.prototype.onRemove=function(e){var t=e.root;t instanceof r&&t._handleRemove(this),this.parent=null,this.resolved=!1},i.prototype.resolve=function(){return this.resolved||this.root instanceof r&&(this.resolved=!0),this},i.prototype.getOption=function(e){if(this.options)return this.options[e]},i.prototype.setOption=function(e,t,n){return n&&this.options&&void 0!==this.options[e]||((this.options||(this.options={}))[e]=t),this},i.prototype.setParsedOption=function(e,t,n){this.parsedOptions||(this.parsedOptions=[]);var r=this.parsedOptions;if(n){var i=r.find((function(t){return Object.prototype.hasOwnProperty.call(t,e)}));if(i){var s=i[e];o.setProperty(s,n,t)}else(i={})[e]=o.setProperty({},n,t),r.push(i)}else{var a={};a[e]=t,r.push(a)}return this},i.prototype.setOptions=function(e,t){if(e)for(var n=Object.keys(e),r=0;r<n.length;++r)this.setOption(n[r],e[n[r]],t);return this},i.prototype.toString=function(){var e=this.constructor.className,t=this.fullName;return t.length?e+" "+t:e},i._configure=function(e){r=e}},7301:function(e,t,n){"use strict";var r=n(4644),o=n(9213).some,i=r.aTypedArray;(0,r.exportTypedArrayMethod)("some",(function(e){return o(i(this),e,arguments.length>1?arguments[1]:void 0)}))},7323:function(e,t,n){"use strict";var r,o,i=n(9565),s=n(9504),a=n(655),u=n(7979),c=n(8429),l=n(5745),d=n(2360),p=n(1181).get,h=n(3635),f=n(8814),v=l("native-string-replace",String.prototype.replace),E=RegExp.prototype.exec,m=E,g=s("".charAt),y=s("".indexOf),_=s("".replace),O=s("".slice),T=(o=/b*/g,i(E,r=/a/,"a"),i(E,o,"a"),0!==r.lastIndex||0!==o.lastIndex),R=c.BROKEN_CARET,I=void 0!==/()??/.exec("")[1];(T||I||R||h||f)&&(m=function(e){var t,n,r,o,s,c,l,h=this,f=p(h),C=a(e),S=f.raw;if(S)return S.lastIndex=h.lastIndex,t=i(m,S,C),h.lastIndex=S.lastIndex,t;var N=f.groups,A=R&&h.sticky,b=i(u,h),M=h.source,w=0,L=C;if(A&&(b=_(b,"y",""),-1===y(b,"g")&&(b+="g"),L=O(C,h.lastIndex),h.lastIndex>0&&(!h.multiline||h.multiline&&"\n"!==g(C,h.lastIndex-1))&&(M="(?: "+M+")",L=" "+L,w++),n=new RegExp("^(?:"+M+")",b)),I&&(n=new RegExp("^"+M+"$(?!\\s)",b)),T&&(r=h.lastIndex),o=i(E,A?n:h,L),A?o?(o.input=O(o.input,w),o[0]=O(o[0],w),o.index=h.lastIndex,h.lastIndex+=o[0].length):h.lastIndex=0:T&&o&&(h.lastIndex=h.global?o.index+o[0].length:r),I&&o&&o.length>1&&i(v,o[0],n,(function(){for(s=1;s<arguments.length-2;s++)void 0===arguments[s]&&(o[s]=void 0)})),o&&N)for(o.groups=c=d(null),s=0;s<N.length;s++)c[(l=N[s])[0]]=o[l[1]];return o}),e.exports=m},7337:function(e,t,n){"use strict";var r=n(6518),o=n(9504),i=n(5610),s=RangeError,a=String.fromCharCode,u=String.fromCodePoint,c=o([].join);r({target:"String",stat:!0,arity:1,forced:!!u&&1!==u.length},{fromCodePoint:function(e){for(var t,n=[],r=arguments.length,o=0;r>o;){if(t=+arguments[o++],i(t,1114111)!==t)throw new s(t+" is not a valid code point");n[o]=t<65536?a(t):a(55296+((t-=65536)>>10),t%1024+56320)}return c(n,"")}})},7347:function(e,t,n){"use strict";var r=n(3724),o=n(9565),i=n(8773),s=n(6980),a=n(5397),u=n(6969),c=n(9297),l=n(5917),d=Object.getOwnPropertyDescriptor;t.f=r?d:function(e,t){if(e=a(e),t=u(t),l)try{return d(e,t)}catch(e){}if(c(e,t))return s(!o(i.f,e,t),e[t])}},7400:function(e){"use strict";e.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},7416:function(e,t,n){"use strict";var r=n(9039),o=n(8227),i=n(3724),s=n(6395),a=o("iterator");e.exports=!r((function(){var e=new URL("b?a=1&b=2&c=3","https://a"),t=e.searchParams,n=new URLSearchParams("a=1&a=2&b=3"),r="";return e.pathname="c%20d",t.forEach((function(e,n){t.delete("b"),r+=n+e})),n.delete("a",2),n.delete("b",void 0),s&&(!e.toJSON||!n.has("a",1)||n.has("a",2)||!n.has("a",void 0)||n.has("b"))||!t.size&&(s||!i)||!t.sort||"https://a/c%20d?a=1&c=3"!==e.href||"3"!==t.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!t[a]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("https://тест").host||"#%D0%B1"!==new URL("https://a#б").hash||"a1c3"!==r||"x"!==new URL("https://x",void 0).host}))},7433:function(e,t,n){"use strict";var r=n(4376),o=n(3517),i=n(34),s=n(8227)("species"),a=Array;e.exports=function(e){var t;return r(e)&&(t=e.constructor,(o(t)&&(t===a||r(t.prototype))||i(t)&&null===(t=t[s]))&&(t=void 0)),void 0===t?a:t}},7452:function(e){"use strict";e.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},7476:function(e,t,n){"use strict";var r=n(2195),o=n(9504);e.exports=function(e){if("Function"===r(e))return o(e)}},7495:function(e,t,n){"use strict";var r=n(6518),o=n(7323);r({target:"RegExp",proto:!0,forced:/./.exec!==o},{exec:o})},7517:function(e,t,n){"use strict";n.r(t),n.d(t,{Message:function(){return E}});var r=n(8678),o=function(){function e(e){var t=e.id;this.id=t,this.type=e.type}return e.prototype.set=function(e){this.body={id:this.id,ackId:e.id,type:"read",to:e.to,from:e.from||"",chatType:e.chatType}},e.create=function(e){return{id:r.Wp.getUniqueId(),chatType:e.chatType,ackId:e.id,type:"read",to:e.to,from:e.from||"",ackContent:e.ackContent,isChatThread:e.isChatThread,priority:e.priority,deliverOnlineOnly:e.deliverOnlineOnly}},e}(),i=function(){function e(e){this.id=e.id,this.type=e.type}return e.prototype.set=function(e){this.body={id:this.id,ackId:e.ackId,type:"delivery",to:e.to,from:e.from||""}},e.create=function(e){return{id:r.Wp.getUniqueId(),ackId:e.ackId,type:"delivery",to:e.to,from:e.from||"",isChatThread:e.isChatThread,priority:e.priority,deliverOnlineOnly:e.deliverOnlineOnly}},e}(),s=(n(9089),function(){function e(e){var t=e.type,n=e.id;this.id=n,this.type=t}return e.prototype.set=function(e){this.body={id:this.id,chatType:e.chatType||"singleChat",type:"channel",to:e.to,from:e.from||"",time:Date.now()}},e.prototype.setChatType=function(e){return!!this.body&&(this.body.chatType=e,!0)},e.prototype.setGroup=function(e){return!!this.body&&(this.body.group=e,!0)},e.create=function(e){return{id:r.Wp.getUniqueId(),type:"channel",chatType:e.chatType||"singleChat",to:e.to,from:e.from||"",time:Date.now(),isChatThread:e.isChatThread,priority:e.priority,deliverOnlineOnly:e.deliverOnlineOnly}},e}()),a=(n(4346),function(){function e(e){var t=e.type,n=e.id||r.Wp.getUniqueId();this.id=n,this.type=t,this.value=""}return e.prototype.set=function(e){this.body={id:this.id,chatType:e.chatType||"singleChat",type:"txt",to:e.to,msg:e.msg,from:e.from,roomType:e.roomType,success:e.success,fail:e.fail,ext:e.ext,time:Date.now(),isChatThread:e.isChatThread},this.value=e.msg},e.prototype.setChatType=function(e){return!!this.body&&(this.body.chatType=e,!0)},e.prototype.setGroup=function(e){return!!this.body&&(this.body.group=e,!0)},e.create=function(e){var t,n;return(null===(t=e.msgConfig)||void 0===t?void 0:t.languages)&&Array.isArray(null===(n=e.msgConfig)||void 0===n?void 0:n.languages),{type:"txt",id:r.Wp.getUniqueId(),msg:e.msg,to:e.to,from:e.from||"",chatType:e.chatType,ext:e.ext,time:Date.now(),msgConfig:e.msgConfig,isChatThread:e.isChatThread,priority:e.priority,deliverOnlineOnly:e.deliverOnlineOnly,receiverList:e.receiverList}},e}()),u=function(){function e(e){var t=e.type,n=e.id;this.id=n||r.Wp.getUniqueId(),this.type=t}return e.prototype.set=function(e){this.body={id:this.id,chatType:e.chatType||"singleChat",type:"cmd",to:e.to,action:e.action,from:e.from,roomType:e.roomType,success:e.success,fail:e.fail,ext:e.ext,time:Date.now()}},e.prototype.setChatType=function(e){return!!this.body&&(this.body.chatType=e,!0)},e.prototype.setGroup=function(e){return!!this.body&&(this.body.group=e,!0)},e.create=function(e){return{id:r.Wp.getUniqueId(),type:"cmd",to:e.to,from:e.from||"",chatType:e.chatType||"singleChat",action:e.action,time:Date.now(),ext:e.ext,msgConfig:e.msgConfig,isChatThread:e.isChatThread,priority:e.priority,deliverOnlineOnly:e.deliverOnlineOnly,receiverList:e.receiverList}},e}(),c=function(){function e(e){var t=e.type,n=e.id||r.Wp.getUniqueId();this.id=n,this.type=t}return e.prototype.set=function(e){this.body={id:this.id,chatType:e.chatType||"singleChat",type:"custom",to:e.to,customEvent:e.customEvent,customExts:e.customExts,from:e.from,roomType:e.roomType,success:e.success,fail:e.fail,ext:e.ext,time:Date.now()}},e.prototype.setChatType=function(e){return!!this.body&&(this.body.chatType=e,!0)},e.prototype.setGroup=function(e){return!!this.body&&(this.body.group=e,!0)},e.create=function(e){return{id:r.Wp.getUniqueId(),chatType:e.chatType||"singleChat",type:"custom",to:e.to,customEvent:e.customEvent,customExts:e.customExts,from:e.from||"",ext:e.ext,time:Date.now(),msgConfig:e.msgConfig,isChatThread:e.isChatThread,priority:e.priority,deliverOnlineOnly:e.deliverOnlineOnly,receiverList:e.receiverList}},e}(),l=function(){function e(e){var t=e.type,n=e.id;this.id=n||r.Wp.getUniqueId(),this.type=t}return e.prototype.set=function(e){this.body={id:this.id,chatType:e.chatType||"singleChat",type:"loc",to:e.to,addr:e.addr,buildingName:e.buildingName,lat:e.lat,lng:e.lng,from:e.from,roomType:e.roomType,success:e.success,fail:e.fail,ext:e.ext,time:Date.now()}},e.prototype.setChatType=function(e){return!!this.body&&(this.body.chatType=e,!0)},e.prototype.setGroup=function(e){return!!this.body&&(this.body.group=e,!0)},e.create=function(e){return{id:r.Wp.getUniqueId(),chatType:e.chatType||"singleChat",type:"loc",to:e.to,addr:e.addr,buildingName:e.buildingName,lat:e.lat,lng:e.lng,from:e.from||"",ext:e.ext,time:Date.now(),msgConfig:e.msgConfig,isChatThread:e.isChatThread,priority:e.priority,deliverOnlineOnly:e.deliverOnlineOnly,receiverList:e.receiverList}},e}(),d=function(){function e(e){var t=e.type,n=e.id||r.Wp.getUniqueId();this.id=n,this.type=t}return e.prototype.set=function(e){e.file=e.file||e.fileInputId&&r.Wp.getFileUrl(e.fileInputId),this.body={id:this.id,chatType:e.chatType||"singleChat",type:"img",file:e.file,width:e.width,height:e.height,to:e.to,from:e.from||"",roomType:e.roomType,success:e.success,fail:e.fail,ext:e.ext,time:Date.now(),onFileUploadError:e.onFileUploadError,onFileUploadComplete:e.onFileUploadComplete,onFileUploadProgress:e.onFileUploadProgress,body:e.body,isChatThread:e.isChatThread}},e.prototype.setChatType=function(e){return!!this.body&&(this.body.chatType=e,!0)},e.prototype.setGroup=function(e){return!!this.body&&(this.body.group=e,!0)},e.create=function(e){return{id:r.Wp.getUniqueId(),chatType:e.chatType,type:"img",url:e.url,width:e.width,height:e.height,file:e.file,to:e.to,from:e.from||"",ext:e.ext,time:Date.now(),msgConfig:e.msgConfig,file_length:e.file_length,onFileUploadError:e.onFileUploadError,onFileUploadComplete:e.onFileUploadComplete,onFileUploadProgress:e.onFileUploadProgress,isChatThread:e.isChatThread,priority:e.priority,deliverOnlineOnly:e.deliverOnlineOnly,receiverList:e.receiverList,thumbnailWidth:e.thumbnailWidth,thumbnailHeight:e.thumbnailHeight,isGif:e.isGif}},e}(),p=function(){function e(e){var t=e.type,n=e.id||r.Wp.getUniqueId();this.id=n,this.type=t}return e.prototype.set=function(e){e.file=e.file||e.fileInputId&&r.Wp.getFileUrl(e.fileInputId),this.body={id:this.id,chatType:e.chatType||"singleChat",type:"audio",file:e.file,filename:e.filename,length:e.length,file_length:e.file_length,fileInputId:e.fileInputId,to:e.to,from:e.from,roomType:e.roomType,success:e.success,fail:e.fail,ext:e.ext,time:Date.now(),onFileUploadError:e.onFileUploadError,onFileUploadComplete:e.onFileUploadComplete,onFileUploadProgress:e.onFileUploadProgress,body:e.body,isChatThread:e.isChatThread}},e.prototype.setChatType=function(e){return!!this.body&&(this.body.chatType=e,!0)},e.prototype.setGroup=function(e){return!!this.body&&(this.body.group=e,!0)},e.create=function(e){return{id:r.Wp.getUniqueId(),chatType:e.chatType,type:"audio",filename:e.filename,length:e.length,file:e.file,to:e.to,from:e.from||"",ext:e.ext,time:Date.now(),onFileUploadError:e.onFileUploadError,onFileUploadComplete:e.onFileUploadComplete,onFileUploadProgress:e.onFileUploadProgress,body:e.body,file_length:e.file_length,msgConfig:e.msgConfig,isChatThread:e.isChatThread,priority:e.priority,deliverOnlineOnly:e.deliverOnlineOnly,receiverList:e.receiverList}},e}(),h=function(){function e(e){var t=e.type,n=e.id;this.id=n,this.type=t}return e.prototype.set=function(e){e.file=e.file||e.fileInputId&&r.Wp.getFileUrl(e.fileInputId),this.body={id:this.id,chatType:e.chatType||"singleChat",type:"video",file:e.file,filename:e.filename,length:e.length,file_length:e.file_length,fileInputId:e.fileInputId,to:e.to,from:e.from,roomType:e.roomType,success:e.success,fail:e.fail,ext:e.ext,time:Date.now(),apiUrl:e.apiUrl,onFileUploadError:e.onFileUploadError,onFileUploadComplete:e.onFileUploadComplete,onFileUploadProgress:e.onFileUploadProgress,body:e.body,isChatThread:e.isChatThread}},e.prototype.setChatType=function(e){return!!this.body&&(this.body.chatType=e,!0)},e.prototype.setGroup=function(e){return!!this.body&&(this.body.group=e,!0)},e.create=function(e){return{id:r.Wp.getUniqueId(),chatType:e.chatType||"singleChat",type:"video",file:e.file,filename:e.filename,length:e.length,file_length:e.file_length,fileInputId:e.fileInputId,to:e.to,from:e.from||"",ext:e.ext,time:Date.now(),onFileUploadError:e.onFileUploadError,onFileUploadComplete:e.onFileUploadComplete,onFileUploadProgress:e.onFileUploadProgress,body:e.body,msgConfig:e.msgConfig,isChatThread:e.isChatThread,priority:e.priority,deliverOnlineOnly:e.deliverOnlineOnly,receiverList:e.receiverList}},e}(),f=function(){function e(e){var t=e.type,n=e.id;this.id=n,this.type=t}return e.prototype.set=function(e){e.file=e.file||e.fileInputId&&r.Wp.getFileUrl(e.fileInputId),this.body={id:this.id,chatType:e.chatType||"singleChat",type:"file",file:e.file,filename:e.filename,fileInputId:e.fileInputId,to:e.to,from:e.from,roomType:e.roomType,success:e.success,fail:e.fail,ext:e.ext,time:Date.now(),onFileUploadError:e.onFileUploadError,onFileUploadComplete:e.onFileUploadComplete,onFileUploadProgress:e.onFileUploadProgress,body:e.body,isChatThread:e.isChatThread}},e.prototype.setChatType=function(e){return!!this.body&&(this.body.chatType=e,!0)},e.prototype.setGroup=function(e){return!!this.body&&(this.body.group=e,!0)},e.create=function(e){return{id:r.Wp.getUniqueId(),chatType:e.chatType||"singleChat",type:"file",file:e.file,filename:e.filename,fileInputId:e.fileInputId,file_length:e.file_length,to:e.to,from:e.from||"",ext:e.ext,onFileUploadError:e.onFileUploadError,onFileUploadComplete:e.onFileUploadComplete,onFileUploadProgress:e.onFileUploadProgress,body:e.body,time:Date.now(),msgConfig:e.msgConfig,isChatThread:e.isChatThread,priority:e.priority,deliverOnlineOnly:e.deliverOnlineOnly,receiverList:e.receiverList}},e}(),v=function(){function e(e){var t=e.type,n=e.id||r.Wp.getUniqueId();this.id=n,this.type=t,this.value=""}return e.create=function(e){return{type:"combine",id:r.Wp.getUniqueId(),to:e.to,from:e.from||"",chatType:e.chatType,ext:e.ext,time:Date.now(),msgConfig:e.msgConfig,isChatThread:e.isChatThread,priority:e.priority,deliverOnlineOnly:e.deliverOnlineOnly,receiverList:e.receiverList,compatibleText:e.compatibleText,title:e.title,summary:e.summary,messageList:e.messageList,onFileUploadError:e.onFileUploadError,onFileUploadComplete:e.onFileUploadComplete}},e}(),E=function(){function e(t,n){return this.type=t,this.id=n||r.Wp.getUniqueId(),e.createOldMsg({type:t,id:this.id})}return e.createOldMsg=function(e){switch(e.type){case"read":return new o({type:"read",id:e.id});case"delivery":return new i({type:"delivery",id:e.id});case"channel":return new s({type:"channel",id:e.id});case"txt":return new a({type:"txt",id:e.id});case"cmd":return new u({type:"cmd",id:e.id});case"custom":return new c({type:"custom",id:e.id});case"loc":return new l({type:"loc",id:e.id});case"img":return new d({type:"img",id:e.id});case"audio":return new p({type:"audio",id:e.id});case"video":return new h({type:"video",id:e.id});case"file":return new f({type:"file",id:e.id})}},e.create=function(e){return"txt"!==(t=e).type||"version"in t?function(e){return"img"===e.type&&!("version"in e)}(e)?d.create(e):function(e){return"cmd"===e.type&&!("version"in e)}(e)?u.create(e):function(e){return"file"===e.type&&!("version"in e)}(e)?f.create(e):function(e){return"audio"===e.type&&!("version"in e)}(e)?p.create(e):function(e){return"video"===e.type&&!("version"in e)}(e)?h.create(e):function(e){return"custom"===e.type&&!("version"in e)}(e)?c.create(e):function(e){return"loc"===e.type&&!("version"in e)}(e)?l.create(e):function(e){return"channel"===e.type&&!("version"in e)}(e)?s.create(e):function(e){return"delivery"===e.type&&!("version"in e)}(e)?i.create(e):function(e){return"read"===e.type&&!("version"in e)}(e)?o.create(e):function(e){return"combine"===e.type&&!("version"in e)}(e)?v.create(e):{}:a.create(e);var t},e.prototype.set=function(e){},e.getFileUrl=r.Wp.getFileUrl,e.download=r.Wp.download,e.parseDownloadResponse=r.Wp.parseDownloadResponse,e}()},7595:function(e,t,n){"use strict";e.exports=o;var r=n(3610);function o(e,t,n){if("function"!=typeof e)throw TypeError("rpcImpl must be a function");r.EventEmitter.call(this),this.rpcImpl=e,this.requestDelimited=Boolean(t),this.responseDelimited=Boolean(n)}(o.prototype=Object.create(r.EventEmitter.prototype)).constructor=o,o.prototype.rpcCall=function e(t,n,o,i,s){if(!i)throw TypeError("request must be specified");var a=this;if(!s)return r.asPromise(e,a,t,n,o,i);if(a.rpcImpl)try{return a.rpcImpl(t,n[a.requestDelimited?"encodeDelimited":"encode"](i).finish(),(function(e,n){if(e)return a.emit("error",e,t),s(e);if(null!==n){if(!(n instanceof o))try{n=o[a.responseDelimited?"decodeDelimited":"decode"](n)}catch(e){return a.emit("error",e,t),s(e)}return a.emit("data",n,t),s(null,n)}a.end(!0)}))}catch(e){return a.emit("error",e,t),void setTimeout((function(){s(e)}),0)}else setTimeout((function(){s(Error("already ended"))}),0)},o.prototype.end=function(e){return this.rpcImpl&&(e||this.rpcImpl(null,null,null),this.rpcImpl=null,this.emit("end").off()),this}},7629:function(e,t,n){"use strict";var r=n(6395),o=n(4576),i=n(9433),s="__core-js_shared__",a=e.exports=o[s]||i(s,{});(a.versions||(a.versions=[])).push({version:"3.42.0",mode:r?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.42.0/LICENSE",source:"https://github.com/zloirock/core-js"})},7633:function(e,t,n){"use strict";var r=n(7751),o=n(2106),i=n(8227),s=n(3724),a=i("species");e.exports=function(e){var t=r(e);s&&t&&!t[a]&&o(t,a,{configurable:!0,get:function(){return this}})}},7657:function(e,t,n){"use strict";var r,o,i,s=n(9039),a=n(4901),u=n(34),c=n(2360),l=n(2787),d=n(6840),p=n(8227),h=n(6395),f=p("iterator"),v=!1;[].keys&&("next"in(i=[].keys())?(o=l(l(i)))!==Object.prototype&&(r=o):v=!0),!u(r)||s((function(){var e={};return r[f].call(e)!==e}))?r={}:h&&(r=c(r)),a(r[f])||d(r,f,(function(){return this})),e.exports={IteratorPrototype:r,BUGGY_SAFARI_ITERATORS:v}},7680:function(e,t,n){"use strict";var r=n(9504);e.exports=r([].slice)},7696:function(e,t,n){"use strict";var r=n(1291),o=n(8014),i=RangeError;e.exports=function(e){if(void 0===e)return 0;var t=r(e),n=o(t);if(t!==n)throw new i("Wrong length or index");return n}},7706:function(e,t,n){"use strict";var r;n.d(t,{P:function(){return r}}),n(346),function(e){e[e.SYNC_INIT=0]="SYNC_INIT",e[e.SYNC_START=1]="SYNC_START",e[e.SYNC_FINISH=2]="SYNC_FINISH"}(r||(r={}))},7728:function(e,t,n){"use strict";e.exports=function(e){var t=i.codegen(["r","l"],e.name+"$decode")("if(!(r instanceof Reader))")("r=Reader.create(r)")("var c=l===undefined?r.len:r.pos+l,m=new this.ctor"+(e.fieldsArray.filter((function(e){return e.map})).length?",k,value":""))("while(r.pos<c){")("var t=r.uint32()");e.group&&t("if((t&7)===4)")("break"),t("switch(t>>>3){");for(var n=0;n<e.fieldsArray.length;++n){var a=e._fieldsArray[n].resolve(),u=a.resolvedType instanceof r?"int32":a.type,c="m"+i.safeProp(a.name);t("case %i:",a.id),a.map?(t("if(%s===util.emptyObject)",c)("%s={}",c)("var c2 = r.uint32()+r.pos"),void 0!==o.defaults[a.keyType]?t("k=%j",o.defaults[a.keyType]):t("k=null"),void 0!==o.defaults[u]?t("value=%j",o.defaults[u]):t("value=null"),t("while(r.pos<c2){")("var tag2=r.uint32()")("switch(tag2>>>3){")("case 1: k=r.%s(); break",a.keyType)("case 2:"),void 0===o.basic[u]?t("value=types[%i].decode(r,r.uint32())",n):t("value=r.%s()",u),t("break")("default:")("r.skipType(tag2&7)")("break")("}")("}"),void 0!==o.long[a.keyType]?t('%s[typeof k==="object"?util.longToHash(k):k]=value',c):t("%s[k]=value",c)):a.repeated?(t("if(!(%s&&%s.length))",c,c)("%s=[]",c),void 0!==o.packed[u]&&t("if((t&7)===2){")("var c2=r.uint32()+r.pos")("while(r.pos<c2)")("%s.push(r.%s())",c,u)("}else"),void 0===o.basic[u]?t(a.resolvedType.group?"%s.push(types[%i].decode(r))":"%s.push(types[%i].decode(r,r.uint32()))",c,n):t("%s.push(r.%s())",c,u)):void 0===o.basic[u]?t(a.resolvedType.group?"%s=types[%i].decode(r)":"%s=types[%i].decode(r,r.uint32())",c,n):t("%s=r.%s()",c,u),t("break")}for(t("default:")("r.skipType(t&7)")("break")("}")("}"),n=0;n<e._fieldsArray.length;++n){var l=e._fieldsArray[n];l.required&&t("if(!m.hasOwnProperty(%j))",l.name)("throw util.ProtocolError(%j,{instance:m})",s(l))}return t("return m")};var r=n(5643),o=n(361),i=n(3262);function s(e){return"missing required '"+e.name+"'"}},7740:function(e,t,n){"use strict";var r=n(9297),o=n(5031),i=n(7347),s=n(4913);e.exports=function(e,t,n){for(var a=o(t),u=s.f,c=i.f,l=0;l<a.length;l++){var d=a[l];r(e,d)||n&&r(n,d)||u(e,d,c(t,d))}}},7743:function(e,t,n){"use strict";var r=n(6518),o=n(9565),i=n(9306),s=n(6043),a=n(1103),u=n(2652);r({target:"Promise",stat:!0,forced:n(537)},{race:function(e){var t=this,n=s.f(t),r=n.reject,c=a((function(){var s=i(t.resolve);u(e,(function(e){o(s,t,e).then(n.resolve,r)}))}));return c.error&&r(c.value),n.promise}})},7750:function(e,t,n){"use strict";var r=n(4117),o=TypeError;e.exports=function(e){if(r(e))throw new o("Can't call method on "+e);return e}},7751:function(e,t,n){"use strict";var r=n(4576),o=n(4901);e.exports=function(e,t){return arguments.length<2?(n=r[e],o(n)?n:void 0):r[e]&&r[e][t];var n}},7764:function(e,t,n){"use strict";var r=n(8183).charAt,o=n(655),i=n(1181),s=n(1088),a=n(2529),u="String Iterator",c=i.set,l=i.getterFor(u);s(String,"String",(function(e){c(this,{type:u,string:o(e),index:0})}),(function(){var e,t=l(this),n=t.string,o=t.index;return o>=n.length?a(void 0,!0):(e=r(n,o),t.index+=e.length,a(e,!1))}))},7782:function(e){"use strict";e.exports=Math.sign||function(e){var t=+e;return 0===t||t!=t?t:t<0?-1:1}},7811:function(e){"use strict";e.exports="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},7812:function(e,t,n){"use strict";var r=n(6518),o=n(9297),i=n(757),s=n(6823),a=n(5745),u=n(1296),c=a("symbol-to-string-registry");r({target:"Symbol",stat:!0,forced:!u},{keyFor:function(e){if(!i(e))throw new TypeError(s(e)+" is not a symbol");if(o(c,e))return c[e]}})},7860:function(e,t,n){"use strict";var r=n(2839);e.exports=/web0s(?!.*chrome)/i.test(r)},7882:function(e,t,n){"use strict";e.exports=g;var r=n(8923);((g.prototype=Object.create(r.prototype)).constructor=g).className="Type";var o=n(5643),i=n(1457),s=n(1344),a=n(8252),u=n(9687),c=n(2551),l=n(6237),d=n(3449),p=n(3262),h=n(1080),f=n(7728),v=n(420),E=n(744),m=n(6434);function g(e,t){r.call(this,e,t),this.fields={},this.oneofs=void 0,this.extensions=void 0,this.reserved=void 0,this.group=void 0,this._fieldsById=null,this._fieldsArray=null,this._oneofsArray=null,this._ctor=null}function y(e){return e._fieldsById=e._fieldsArray=e._oneofsArray=null,delete e.encode,delete e.decode,delete e.verify,e}Object.defineProperties(g.prototype,{fieldsById:{get:function(){if(this._fieldsById)return this._fieldsById;this._fieldsById={};for(var e=Object.keys(this.fields),t=0;t<e.length;++t){var n=this.fields[e[t]],r=n.id;if(this._fieldsById[r])throw Error("duplicate id "+r+" in "+this);this._fieldsById[r]=n}return this._fieldsById}},fieldsArray:{get:function(){return this._fieldsArray||(this._fieldsArray=p.toArray(this.fields))}},oneofsArray:{get:function(){return this._oneofsArray||(this._oneofsArray=p.toArray(this.oneofs))}},ctor:{get:function(){return this._ctor||(this.ctor=g.generateConstructor(this)())},set:function(e){var t=e.prototype;t instanceof c||((e.prototype=new c).constructor=e,p.merge(e.prototype,t)),e.$type=e.prototype.$type=this,p.merge(e,c,!0),this._ctor=e;for(var n=0;n<this.fieldsArray.length;++n)this._fieldsArray[n].resolve();var r={};for(n=0;n<this.oneofsArray.length;++n)r[this._oneofsArray[n].resolve().name]={get:p.oneOfGetter(this._oneofsArray[n].oneof),set:p.oneOfSetter(this._oneofsArray[n].oneof)};n&&Object.defineProperties(e.prototype,r)}}}),g.generateConstructor=function(e){for(var t,n=p.codegen(["p"],e.name),r=0;r<e.fieldsArray.length;++r)(t=e._fieldsArray[r]).map?n("this%s={}",p.safeProp(t.name)):t.repeated&&n("this%s=[]",p.safeProp(t.name));return n("if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)")("this[ks[i]]=p[ks[i]]")},g.fromJSON=function(e,t){var n=new g(e,t.options);n.extensions=t.extensions,n.reserved=t.reserved;for(var c=Object.keys(t.fields),l=0;l<c.length;++l)n.add((void 0!==t.fields[c[l]].keyType?a.fromJSON:s.fromJSON)(c[l],t.fields[c[l]]));if(t.oneofs)for(c=Object.keys(t.oneofs),l=0;l<c.length;++l)n.add(i.fromJSON(c[l],t.oneofs[c[l]]));if(t.nested)for(c=Object.keys(t.nested),l=0;l<c.length;++l){var d=t.nested[c[l]];n.add((void 0!==d.id?s.fromJSON:void 0!==d.fields?g.fromJSON:void 0!==d.values?o.fromJSON:void 0!==d.methods?u.fromJSON:r.fromJSON)(c[l],d))}return t.extensions&&t.extensions.length&&(n.extensions=t.extensions),t.reserved&&t.reserved.length&&(n.reserved=t.reserved),t.group&&(n.group=!0),t.comment&&(n.comment=t.comment),n},g.prototype.toJSON=function(e){var t=r.prototype.toJSON.call(this,e),n=!!e&&Boolean(e.keepComments);return p.toObject(["options",t&&t.options||void 0,"oneofs",r.arrayToJSON(this.oneofsArray,e),"fields",r.arrayToJSON(this.fieldsArray.filter((function(e){return!e.declaringField})),e)||{},"extensions",this.extensions&&this.extensions.length?this.extensions:void 0,"reserved",this.reserved&&this.reserved.length?this.reserved:void 0,"group",this.group||void 0,"nested",t&&t.nested||void 0,"comment",n?this.comment:void 0])},g.prototype.resolveAll=function(){for(var e=this.fieldsArray,t=0;t<e.length;)e[t++].resolve();var n=this.oneofsArray;for(t=0;t<n.length;)n[t++].resolve();return r.prototype.resolveAll.call(this)},g.prototype.get=function(e){return this.fields[e]||this.oneofs&&this.oneofs[e]||this.nested&&this.nested[e]||null},g.prototype.add=function(e){if(this.get(e.name))throw Error("duplicate name '"+e.name+"' in "+this);if(e instanceof s&&void 0===e.extend){if(this._fieldsById?this._fieldsById[e.id]:this.fieldsById[e.id])throw Error("duplicate id "+e.id+" in "+this);if(this.isReservedId(e.id))throw Error("id "+e.id+" is reserved in "+this);if(this.isReservedName(e.name))throw Error("name '"+e.name+"' is reserved in "+this);return e.parent&&e.parent.remove(e),this.fields[e.name]=e,e.message=this,e.onAdd(this),y(this)}return e instanceof i?(this.oneofs||(this.oneofs={}),this.oneofs[e.name]=e,e.onAdd(this),y(this)):r.prototype.add.call(this,e)},g.prototype.remove=function(e){if(e instanceof s&&void 0===e.extend){if(!this.fields||this.fields[e.name]!==e)throw Error(e+" is not a member of "+this);return delete this.fields[e.name],e.parent=null,e.onRemove(this),y(this)}if(e instanceof i){if(!this.oneofs||this.oneofs[e.name]!==e)throw Error(e+" is not a member of "+this);return delete this.oneofs[e.name],e.parent=null,e.onRemove(this),y(this)}return r.prototype.remove.call(this,e)},g.prototype.isReservedId=function(e){return r.isReservedId(this.reserved,e)},g.prototype.isReservedName=function(e){return r.isReservedName(this.reserved,e)},g.prototype.create=function(e){return new this.ctor(e)},g.prototype.setup=function(){for(var e=this.fullName,t=[],n=0;n<this.fieldsArray.length;++n)t.push(this._fieldsArray[n].resolve().resolvedType);this.encode=h(this)({Writer:d,types:t,util:p}),this.decode=f(this)({Reader:l,types:t,util:p}),this.verify=v(this)({types:t,util:p}),this.fromObject=E.fromObject(this)({types:t,util:p}),this.toObject=E.toObject(this)({types:t,util:p});var r=m[e];if(r){var o=Object.create(this);o.fromObject=this.fromObject,this.fromObject=r.fromObject.bind(o),o.toObject=this.toObject,this.toObject=r.toObject.bind(o)}return this},g.prototype.encode=function(e,t){return this.setup().encode(e,t)},g.prototype.encodeDelimited=function(e,t){return this.encode(e,t&&t.len?t.fork():t).ldelim()},g.prototype.decode=function(e,t){return this.setup().decode(e,t)},g.prototype.decodeDelimited=function(e){return e instanceof l||(e=l.create(e)),this.decode(e,e.uint32())},g.prototype.verify=function(e){return this.setup().verify(e)},g.prototype.fromObject=function(e){return this.setup().fromObject(e)},g.prototype.toObject=function(e,t){return this.setup().toObject(e,t)},g.d=function(e){return function(t){p.decorateType(t,e)}}},7916:function(e,t,n){"use strict";var r=n(6080),o=n(9565),i=n(8981),s=n(6319),a=n(4209),u=n(3517),c=n(6198),l=n(4659),d=n(81),p=n(851),h=Array;e.exports=function(e){var t=i(e),n=u(this),f=arguments.length,v=f>1?arguments[1]:void 0,E=void 0!==v;E&&(v=r(v,f>2?arguments[2]:void 0));var m,g,y,_,O,T,R=p(t),I=0;if(!R||this===h&&a(R))for(m=c(t),g=n?new this(m):h(m);m>I;I++)T=E?v(t[I],I):t[I],l(g,I,T);else for(g=n?new this:[],O=(_=d(t,R)).next;!(y=o(O,_)).done;I++)T=E?s(_,v,[y.value,I],!0):y.value,l(g,I,T);return g.length=I,g}},7979:function(e,t,n){"use strict";var r=n(8551);e.exports=function(){var e=r(this),t="";return e.hasIndices&&(t+="d"),e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.unicodeSets&&(t+="v"),e.sticky&&(t+="y"),t}},8014:function(e,t,n){"use strict";var r=n(1291),o=Math.min;e.exports=function(e){var t=r(e);return t>0?o(t,9007199254740991):0}},8045:function(e){"use strict";e.exports=function(e,t){for(var n=new Array(arguments.length-1),r=0,o=2,i=!0;o<arguments.length;)n[r++]=arguments[o++];return new Promise((function(o,s){n[r]=function(e){if(i)if(i=!1,e)s(e);else{for(var t=new Array(arguments.length-1),n=0;n<t.length;)t[n++]=arguments[n];o.apply(null,t)}};try{e.apply(t||null,n)}catch(e){i&&(i=!1,s(e))}}))}},8183:function(e,t,n){"use strict";var r=n(9504),o=n(1291),i=n(655),s=n(7750),a=r("".charAt),u=r("".charCodeAt),c=r("".slice),l=function(e){return function(t,n){var r,l,d=i(s(t)),p=o(n),h=d.length;return p<0||p>=h?e?"":void 0:(r=u(d,p))<55296||r>56319||p+1===h||(l=u(d,p+1))<56320||l>57343?e?a(d,p):r:e?c(d,p,p+2):l-56320+(r-55296<<10)+65536}};e.exports={codeAt:l(!1),charAt:l(!0)}},8227:function(e,t,n){"use strict";var r=n(4576),o=n(5745),i=n(9297),s=n(3392),a=n(4495),u=n(7040),c=r.Symbol,l=o("wks"),d=u?c.for||c:c&&c.withoutSetter||s;e.exports=function(e){return i(l,e)||(l[e]=a&&i(c,e)?c[e]:d("Symbol."+e)),l[e]}},8229:function(e,t,n){"use strict";var r=n(9590),o=RangeError;e.exports=function(e,t){var n=r(e);if(n%t)throw new o("Wrong offset");return n}},8232:function(e,t,n){"use strict";var r;n.d(t,{q:function(){return r}}),function(e){e[e.CREATE=0]="CREATE",e[e.FAIL=1]="FAIL",e[e.INPROGRESS=2]="INPROGRESS",e[e.SUCCESS=3]="SUCCESS"}(r||(r={}))},8242:function(e,t,n){"use strict";var r=n(9565),o=n(7751),i=n(8227),s=n(6840);e.exports=function(){var e=o("Symbol"),t=e&&e.prototype,n=t&&t.valueOf,a=i("toPrimitive");t&&!t[a]&&s(t,a,(function(e){return r(n,this)}),{arity:1})}},8252:function(e,t,n){"use strict";e.exports=s;var r=n(1344);((s.prototype=Object.create(r.prototype)).constructor=s).className="MapField";var o=n(361),i=n(3262);function s(e,t,n,o,s,a){if(r.call(this,e,t,o,void 0,void 0,s,a),!i.isString(n))throw TypeError("keyType must be a string");this.keyType=n,this.resolvedKeyType=null,this.map=!0}s.fromJSON=function(e,t){return new s(e,t.id,t.keyType,t.type,t.options,t.comment)},s.prototype.toJSON=function(e){var t=!!e&&Boolean(e.keepComments);return i.toObject(["keyType",this.keyType,"type",this.type,"id",this.id,"extend",this.extend,"options",this.options,"comment",t?this.comment:void 0])},s.prototype.resolve=function(){if(this.resolved)return this;if(void 0===o.mapKey[this.keyType])throw Error("invalid key type: "+this.keyType);return r.prototype.resolve.call(this)},s.d=function(e,t,n){return"function"==typeof n?n=i.decorateType(n).name:n&&"object"==typeof n&&(n=i.decorateEnum(n).name),function(r,o){i.decorateType(r.constructor).add(new s(o,e,t,n))}}},8265:function(e){"use strict";var t=function(){this.head=null,this.tail=null};t.prototype={add:function(e){var t={item:e,next:null},n=this.tail;n?n.next=t:this.head=t,this.tail=t},get:function(){var e=this.head;if(e)return null===(this.head=e.next)&&(this.tail=null),e.item}},e.exports=t},8309:function(e,t,n){"use strict";n(4359)},8319:function(e){"use strict";var t=Math.round;e.exports=function(e){var n=t(e);return n<0?0:n>255?255:255&n}},8345:function(e,t,n){"use strict";var r=n(2805);(0,n(4644).exportTypedArrayStaticMethod)("from",n(3251),r)},8379:function(e,t,n){"use strict";var r=n(8745),o=n(5397),i=n(1291),s=n(6198),a=n(4598),u=Math.min,c=[].lastIndexOf,l=!!c&&1/[1].lastIndexOf(1,-0)<0,d=a("lastIndexOf"),p=l||!d;e.exports=p?function(e){if(l)return r(c,this,arguments)||0;var t=o(this),n=s(t);if(0===n)return-1;var a=n-1;for(arguments.length>1&&(a=u(a,i(arguments[1]))),a<0&&(a=n+a);a>=0;a--)if(a in t&&t[a]===e)return a||0;return-1}:c},8406:function(e,t,n){"use strict";n(3792),n(7337);var r=n(6518),o=n(4576),i=n(3389),s=n(7751),a=n(9565),u=n(9504),c=n(3724),l=n(7416),d=n(6840),p=n(2106),h=n(6279),f=n(687),v=n(3994),E=n(1181),m=n(679),g=n(4901),y=n(9297),_=n(6080),O=n(6955),T=n(8551),R=n(34),I=n(655),C=n(2360),S=n(6980),N=n(81),A=n(851),b=n(2529),M=n(2812),w=n(8227),L=n(4488),U=w("iterator"),x="URLSearchParams",D=x+"Iterator",P=E.set,k=E.getterFor(x),H=E.getterFor(D),F=i("fetch"),B=i("Request"),G=i("Headers"),W=B&&B.prototype,j=G&&G.prototype,q=o.TypeError,K=o.encodeURIComponent,V=String.fromCharCode,z=s("String","fromCodePoint"),J=parseInt,Y=u("".charAt),X=u([].join),Q=u([].push),$=u("".replace),Z=u([].shift),ee=u([].splice),te=u("".split),ne=u("".slice),re=u(/./.exec),oe=/\+/g,ie=/^[0-9a-f]+$/i,se=function(e,t){var n=ne(e,t,t+2);return re(ie,n)?J(n,16):NaN},ae=function(e){for(var t=0,n=128;n>0&&0!==(e&n);n>>=1)t++;return t},ue=function(e){var t=null;switch(e.length){case 1:t=e[0];break;case 2:t=(31&e[0])<<6|63&e[1];break;case 3:t=(15&e[0])<<12|(63&e[1])<<6|63&e[2];break;case 4:t=(7&e[0])<<18|(63&e[1])<<12|(63&e[2])<<6|63&e[3]}return t>1114111?null:t},ce=function(e){for(var t=(e=$(e,oe," ")).length,n="",r=0;r<t;){var o=Y(e,r);if("%"===o){if("%"===Y(e,r+1)||r+3>t){n+="%",r++;continue}var i=se(e,r+1);if(i!=i){n+=o,r++;continue}r+=2;var s=ae(i);if(0===s)o=V(i);else{if(1===s||s>4){n+="�",r++;continue}for(var a=[i],u=1;u<s&&!(3+ ++r>t||"%"!==Y(e,r));){var c=se(e,r+1);if(c!=c){r+=3;break}if(c>191||c<128)break;Q(a,c),r+=2,u++}if(a.length!==s){n+="�";continue}var l=ue(a);null===l?n+="�":o=z(l)}}n+=o,r++}return n},le=/[!'()~]|%20/g,de={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},pe=function(e){return de[e]},he=function(e){return $(K(e),le,pe)},fe=v((function(e,t){P(this,{type:D,target:k(e).entries,index:0,kind:t})}),x,(function(){var e=H(this),t=e.target,n=e.index++;if(!t||n>=t.length)return e.target=null,b(void 0,!0);var r=t[n];switch(e.kind){case"keys":return b(r.key,!1);case"values":return b(r.value,!1)}return b([r.key,r.value],!1)}),!0),ve=function(e){this.entries=[],this.url=null,void 0!==e&&(R(e)?this.parseObject(e):this.parseQuery("string"==typeof e?"?"===Y(e,0)?ne(e,1):e:I(e)))};ve.prototype={type:x,bindURL:function(e){this.url=e,this.update()},parseObject:function(e){var t,n,r,o,i,s,u,c=this.entries,l=A(e);if(l)for(n=(t=N(e,l)).next;!(r=a(n,t)).done;){if(i=(o=N(T(r.value))).next,(s=a(i,o)).done||(u=a(i,o)).done||!a(i,o).done)throw new q("Expected sequence with length 2");Q(c,{key:I(s.value),value:I(u.value)})}else for(var d in e)y(e,d)&&Q(c,{key:d,value:I(e[d])})},parseQuery:function(e){if(e)for(var t,n,r=this.entries,o=te(e,"&"),i=0;i<o.length;)(t=o[i++]).length&&(n=te(t,"="),Q(r,{key:ce(Z(n)),value:ce(X(n,"="))}))},serialize:function(){for(var e,t=this.entries,n=[],r=0;r<t.length;)e=t[r++],Q(n,he(e.key)+"="+he(e.value));return X(n,"&")},update:function(){this.entries.length=0,this.parseQuery(this.url.query)},updateURL:function(){this.url&&this.url.update()}};var Ee=function(){m(this,me);var e=P(this,new ve(arguments.length>0?arguments[0]:void 0));c||(this.size=e.entries.length)},me=Ee.prototype;if(h(me,{append:function(e,t){var n=k(this);M(arguments.length,2),Q(n.entries,{key:I(e),value:I(t)}),c||this.length++,n.updateURL()},delete:function(e){for(var t=k(this),n=M(arguments.length,1),r=t.entries,o=I(e),i=n<2?void 0:arguments[1],s=void 0===i?i:I(i),a=0;a<r.length;){var u=r[a];if(u.key!==o||void 0!==s&&u.value!==s)a++;else if(ee(r,a,1),void 0!==s)break}c||(this.size=r.length),t.updateURL()},get:function(e){var t=k(this).entries;M(arguments.length,1);for(var n=I(e),r=0;r<t.length;r++)if(t[r].key===n)return t[r].value;return null},getAll:function(e){var t=k(this).entries;M(arguments.length,1);for(var n=I(e),r=[],o=0;o<t.length;o++)t[o].key===n&&Q(r,t[o].value);return r},has:function(e){for(var t=k(this).entries,n=M(arguments.length,1),r=I(e),o=n<2?void 0:arguments[1],i=void 0===o?o:I(o),s=0;s<t.length;){var a=t[s++];if(a.key===r&&(void 0===i||a.value===i))return!0}return!1},set:function(e,t){var n=k(this);M(arguments.length,1);for(var r,o=n.entries,i=!1,s=I(e),a=I(t),u=0;u<o.length;u++)(r=o[u]).key===s&&(i?ee(o,u--,1):(i=!0,r.value=a));i||Q(o,{key:s,value:a}),c||(this.size=o.length),n.updateURL()},sort:function(){var e=k(this);L(e.entries,(function(e,t){return e.key>t.key?1:-1})),e.updateURL()},forEach:function(e){for(var t,n=k(this).entries,r=_(e,arguments.length>1?arguments[1]:void 0),o=0;o<n.length;)r((t=n[o++]).value,t.key,this)},keys:function(){return new fe(this,"keys")},values:function(){return new fe(this,"values")},entries:function(){return new fe(this,"entries")}},{enumerable:!0}),d(me,U,me.entries,{name:"entries"}),d(me,"toString",(function(){return k(this).serialize()}),{enumerable:!0}),c&&p(me,"size",{get:function(){return k(this).entries.length},configurable:!0,enumerable:!0}),f(Ee,x),r({global:!0,constructor:!0,forced:!l},{URLSearchParams:Ee}),!l&&g(G)){var ge=u(j.has),ye=u(j.set),_e=function(e){if(R(e)){var t,n=e.body;if(O(n)===x)return t=e.headers?new G(e.headers):new G,ge(t,"content-type")||ye(t,"content-type","application/x-www-form-urlencoded;charset=UTF-8"),C(e,{body:S(0,I(n)),headers:S(0,t)})}return e};if(g(F)&&r({global:!0,enumerable:!0,dontCallGetSet:!0,forced:!0},{fetch:function(e){return F(e,arguments.length>1?_e(arguments[1]):{})}}),g(B)){var Oe=function(e){return m(this,W),new B(e,arguments.length>1?_e(arguments[1]):{})};W.constructor=Oe,Oe.prototype=W,r({global:!0,constructor:!0,dontCallGetSet:!0,forced:!0},{Request:Oe})}}e.exports={URLSearchParams:Ee,getState:k}},8408:function(e,t,n){"use strict";n(8406)},8429:function(e,t,n){"use strict";var r=n(9039),o=n(4576).RegExp,i=r((function(){var e=o("a","y");return e.lastIndex=2,null!==e.exec("abcd")})),s=i||r((function(){return!o("a","y").sticky})),a=i||r((function(){var e=o("^r","gy");return e.lastIndex=2,null!==e.exec("str")}));e.exports={BROKEN_CARET:a,MISSED_STICKY:s,UNSUPPORTED_Y:i}},8434:function(e,t,n){"use strict";n.d(t,{xz:function(){return k},up:function(){return P},Ay:function(){return Ne}}),n(2675),n(9463),n(2259),n(8706),n(2008),n(113),n(8980),n(1629),n(3418),n(4423),n(5276),n(4346),n(3792),n(2062),n(2712),n(4782),n(5086),n(4554),n(4743),n(1745),n(8309),n(9089),n(739),n(3288),n(4170),n(2010),n(6033),n(2892),n(2637),n(150),n(9085),n(9432),n(6099),n(3362),n(8781),n(1699),n(7764),n(1489),n(1630),n(2170),n(5044),n(1920),n(1694),n(9955),n(3206),n(8345),n(4496),n(6651),n(2887),n(9369),n(6812),n(8995),n(1575),n(6072),n(8747),n(8845),n(9423),n(7301),n(373),n(6614),n(1405),n(3684),n(3500),n(2953),n(6031),n(3296),n(7208),n(8408);var r=n(3887),o=n.n(r),i=n(8570),s=n.n(i),a=n(1531),u=n(8801),c=n(2056),l=function(){return l=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},l.apply(this,arguments)},d=["public","members_only","allow_user_invites","invite_need_confirm"],p={name:"name",title:"name",description:"description",public:"public",members_only:"approval",allow_user_invites:"allowInvites",max_users:"maxUsers",invite_need_confirm:"inviteNeedConfirm",custom:"ext",last_modified:"lastModified",avatar:"avatar"};function h(e,t){var n,r,o,i,s,a,u=this,c=this.context,h=c.userId,f=c.jid,v=t.from.name===h&&f.clientResource!==t.from.clientResource;return t.isThread?(o={id:t.mucId.name,name:t.mucName,operation:"",parentId:t.mucParentId.name,operator:t.from.name,userName:t.to.length?t.to[0].name:""},i={chatThreadId:t.mucId.name,chatThreadName:t.mucName,operation:"",parentId:t.mucParentId.name}):(r={type:"",owner:t.from.name,gid:t.mucId.name,from:t.from.name,fromJid:t.from,to:t.to.length?t.to[0].name:"",toJid:t.to,chatroom:t.isChatroom,status:t.status},s={operation:"",id:t.mucId.name,from:t.from.name},t.isChatroom&&(null===(n=null==t?void 0:t.eventInfo)||void 0===n?void 0:n.ext)&&(a=JSON.parse(t.eventInfo.ext))),({45:function(){var e,n,r;s.operation="memberAttributesUpdate";var o=JSON.parse(null===(e=null==t?void 0:t.eventInfo)||void 0===e?void 0:e.ext)||{};s.attributes=o.properties||{},s.userId=o.username||"",v?null===(n=u.eventHandler)||void 0===n||n.dispatch("onMultiDeviceEvent",s):null===(r=u.eventHandler)||void 0===r||r.dispatch("onGroupEvent",s)},44:function(){var e;s.operation="removeChatRoomAttributes",s.attributes=a.result.successKeys,a.result.successKeys.length>0&&(null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomEvent",s))},43:function(){var e;s.operation="updateChatRoomAttributes";var t={};a.result.successKeys.forEach((function(e){t[e]=a.properties[e]})),s.attributes=t,a.result.successKeys.length>0&&(null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomEvent",s))},42:function(){},41:function(){},40:function(){},39:function(){},38:function(){var e;i.operation="chatThreadNameUpdate",null===(e=u.eventHandler)||void 0===e||e.dispatch("onMultiDeviceEvent",i)},37:function(){var e;o.operation="userRemove",null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatThreadChange",o)},36:function(){var e;i.operation="chatThreadLeave",null===(e=u.eventHandler)||void 0===e||e.dispatch("onMultiDeviceEvent",i)},35:function(){var e;i.operation="chatThreadJoin",null===(e=u.eventHandler)||void 0===e||e.dispatch("onMultiDeviceEvent",i)},34:function(){var e;i.operation="chatThreadDestroy",null===(e=u.eventHandler)||void 0===e||e.dispatch("onMultiDeviceEvent",i)},33:function(){var e;i.operation="chatThreadCreate",null===(e=u.eventHandler)||void 0===e||e.dispatch("onMultiDeviceEvent",i)},32:function(){var e,n,o,i;r.type=t.isChatroom?"rmChatRoomMute":"rmGroupMute",u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),s.operation="unmuteAllMembers",t.isChatroom?null===(o=u.eventHandler)||void 0===o||o.dispatch("onChatroomEvent",s):null===(i=u.eventHandler)||void 0===i||i.dispatch("onGroupEvent",s)},31:function(){var e,n,o,i;r.type=t.isChatroom?"muteChatRoom":"muteGroup",u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),s.operation="muteAllMembers",t.isChatroom?null===(o=u.eventHandler)||void 0===o||o.dispatch("onChatroomEvent",s):null===(i=u.eventHandler)||void 0===i||i.dispatch("onGroupEvent",s)},30:function(){var e,n,o,i;r.type=t.isChatroom?"rmUserFromChatRoomWhiteList":"rmUserFromGroupWhiteList",u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),s.operation="removeAllowlistMember",s.userId=r.to,t.isChatroom?null===(o=u.eventHandler)||void 0===o||o.dispatch("onChatroomEvent",s):null===(i=u.eventHandler)||void 0===i||i.dispatch("onGroupEvent",s)},29:function(){var e,n,o,i;r.type=t.isChatroom?"addUserToChatRoomWhiteList":"addUserToGroupWhiteList",u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),s.operation="addUserToAllowlist",s.userId=r.to,t.isChatroom?null===(o=u.eventHandler)||void 0===o||o.dispatch("onChatroomEvent",s):null===(i=u.eventHandler)||void 0===i||i.dispatch("onGroupEvent",s)},28:function(){var e,n,o,i;r.type="deleteFile",u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),s.operation="deleteFile",t.isChatroom?null===(o=u.eventHandler)||void 0===o||o.dispatch("onChatroomEvent",s):null===(i=u.eventHandler)||void 0===i||i.dispatch("onGroupEvent",s)},27:function(){var e,n,o,i;r.type="uploadFile",u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),s.operation="uploadFile",t.isChatroom?null===(o=u.eventHandler)||void 0===o||o.dispatch("onChatroomEvent",s):null===(i=u.eventHandler)||void 0===i||i.dispatch("onGroupEvent",s)},26:function(){var e,n,o,i;r.type="deleteAnnouncement",u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),s.operation="deleteAnnouncement",t.isChatroom?null===(o=u.eventHandler)||void 0===o||o.dispatch("onChatroomEvent",s):null===(i=u.eventHandler)||void 0===i||i.dispatch("onGroupEvent",s)},25:function(){var e,n,o,i;r.type="updateAnnouncement",r.announcement=t.reason,u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),s.operation="updateAnnouncement",s.announcement=t.reason,t.isChatroom?null===(o=u.eventHandler)||void 0===o||o.dispatch("onChatroomEvent",s):null===(i=u.eventHandler)||void 0===i||i.dispatch("onGroupEvent",s)},24:function(){var e,n,o,i;r.type="removeMute",u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),s.operation="unmuteMember",s.userId=r.to,t.isChatroom?null===(o=u.eventHandler)||void 0===o||o.dispatch("onChatroomEvent",s):null===(i=u.eventHandler)||void 0===i||i.dispatch("onGroupEvent",s)},23:function(){var e,n,o,i;if(r.type="addMute",u.onPresence&&u.onPresence(r),t.ext&&"string"==typeof t.ext){var a=JSON.parse(t.ext);a.user_mute_time&&(r.muteTimestamp=a.user_mute_time,s.muteTimestamp=a.user_mute_time)}t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),s.operation="muteMember",s.userId=r.to,t.isChatroom?null===(o=u.eventHandler)||void 0===o||o.dispatch("onChatroomEvent",s):null===(i=u.eventHandler)||void 0===i||i.dispatch("onGroupEvent",s)},22:function(){var e,n,o,i;r.type="removeAdmin",u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),s.operation="removeAdmin",t.isChatroom?null===(o=u.eventHandler)||void 0===o||o.dispatch("onChatroomEvent",s):null===(i=u.eventHandler)||void 0===i||i.dispatch("onGroupEvent",s)},21:function(){var e,n,o,i;r.type="addAdmin",u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),s.operation="setAdmin",s.userId=r.to,t.isChatroom?null===(o=u.eventHandler)||void 0===o||o.dispatch("onChatroomEvent",s):null===(i=u.eventHandler)||void 0===i||i.dispatch("onGroupEvent",s)},20:function(){var e,n,o,i;r.type="changeOwner",u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),s.operation="changeOwner",t.isChatroom?null===(o=u.eventHandler)||void 0===o||o.dispatch("onChatroomEvent",s):null===(i=u.eventHandler)||void 0===i||i.dispatch("onGroupEvent",s)},19:function(){var e,n,o,i;r.type="direct_joined",r.groupName=t.mucName,u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),s.operation="directJoined",s.name=t.mucName,t.isChatroom?null===(o=u.eventHandler)||void 0===o||o.dispatch("onChatroomEvent",s):null===(i=u.eventHandler)||void 0===i||i.dispatch("onGroupEvent",s)},18:function(){var e,n,o,i,a;r.type=t.isChatroom?"leaveChatRoom":"leaveGroup",u.onPresence&&u.onPresence(r);var c=t.mucMemberCount;if(c&&(s.memberCount=c),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),s.operation="memberAbsence",t.members&&t.members.length>0){var d=l({},s);d.members=t.members,d.operation="membersAbsence",delete d.from,t.isChatroom?t.members.forEach((function(e){var t;null===(t=u.eventHandler)||void 0===t||t.dispatch("onChatroomEvent",l(l({},s),{from:e}))})):null===(o=u.eventHandler)||void 0===o||o.dispatch("onGroupEvent",d)}else t.isChatroom?null===(i=u.eventHandler)||void 0===i||i.dispatch("onChatroomEvent",s):null===(a=u.eventHandler)||void 0===a||a.dispatch("onGroupEvent",s)},17:function(){var e,n,o,i,a;t.isChatroom&&t.ext&&(s.ext=t.ext,r.ext=t.ext),r.type=t.isChatroom?"memberJoinChatRoomSuccess":"memberJoinPublicGroupSuccess",u.onPresence&&u.onPresence(r);var c=t.mucMemberCount;if(t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),s.operation="memberPresence",c&&(s.memberCount=c),t.members&&t.members.length>0){var d=l({},s);d.members=t.members,d.operation="membersPresence",delete d.from,t.isChatroom?t.members.forEach((function(e){var t;null===(t=u.eventHandler)||void 0===t||t.dispatch("onChatroomEvent",l(l({},s),{from:e}))})):null===(o=u.eventHandler)||void 0===o||o.dispatch("onGroupEvent",d)}else t.isChatroom?null===(i=u.eventHandler)||void 0===i||i.dispatch("onChatroomEvent",s):null===(a=u.eventHandler)||void 0===a||a.dispatch("onGroupEvent",s)},16:function(){var e,n;r.type="unblock",u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r)},15:function(){var e,n;r.type="block",u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r)},14:function(){var e,n,o,i,a,c=t.isChatroom;if(!c){var l=JSON.parse((null===(e=null==t?void 0:t.eventInfo)||void 0===e?void 0:e.ext)||"{}",(function(e,t){return"last_modified"===e?Number(t):d.includes(e)?"true"===t||!0===t:t}));s.detail=r.detail={},Object.keys(l).forEach((function(e){var t=p[e];if(t){var n=l[e];s.detail&&(s.detail[t]=n),r.detail&&(r.detail[t]=n)}}))}r.type="update",u.onPresence&&u.onPresence(r),c?null===(n=u.eventHandler)||void 0===n||n.dispatch("onChatroomChange",r):null===(o=u.eventHandler)||void 0===o||o.dispatch("onGroupChange",r),s.operation="updateInfo",c?null===(i=u.eventHandler)||void 0===i||i.dispatch("onChatroomEvent",s):null===(a=u.eventHandler)||void 0===a||a.dispatch("onGroupEvent",s)},13:function(){var e,n,o,i;r.type="allow",r.reason=t.reason,u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),t.reason&&(s.reason=t.reason),s.operation="unblockMember",t.isChatroom?null===(o=u.eventHandler)||void 0===o||o.dispatch("onChatroomEvent",s):null===(i=u.eventHandler)||void 0===i||i.dispatch("onGroupEvent",s)},12:function(){var e,n;r.type="ban",u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r)},11:function(){var e,n;r.type="getBlackList",u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r)},10:function(){var e,n,o,i;r.type="removedFromGroup",r.kicked=r.to,u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),s.operation="removeMember",t.isChatroom?null===(o=u.eventHandler)||void 0===o||o.dispatch("onChatroomEvent",s):null===(i=u.eventHandler)||void 0===i||i.dispatch("onGroupEvent",s)},9:function(){var e,n,o,i;r.type="invite_decline",r.kicked=r.to,u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),s.operation="rejectInvite",t.isChatroom?null===(o=u.eventHandler)||void 0===o||o.dispatch("onChatroomEvent",s):null===(i=u.eventHandler)||void 0===i||i.dispatch("onGroupEvent",s)},8:function(){var e,n,o,i;r.type="invite_accept",r.kicked=r.to,u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),s.operation="acceptInvite",t.isChatroom?null===(o=u.eventHandler)||void 0===o||o.dispatch("onChatroomEvent",s):null===(i=u.eventHandler)||void 0===i||i.dispatch("onGroupEvent",s)},7:function(){var e,n,o,i;r.type="invite",r.kicked=r.to,r.groupName=t.mucName,u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),s.operation="inviteToJoin",s.name=t.mucName,t.isChatroom?null===(o=u.eventHandler)||void 0===o||o.dispatch("onChatroomEvent",s):null===(i=u.eventHandler)||void 0===i||i.dispatch("onGroupEvent",s)},6:function(){var e,n,o,i;r.type="joinPublicGroupDeclined",u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),s.operation="joinPublicGroupDeclined",t.isChatroom?null===(o=u.eventHandler)||void 0===o||o.dispatch("onChatroomEvent",s):(s.userId=t.to.length?t.to[0].name:"",null===(i=u.eventHandler)||void 0===i||i.dispatch("onGroupEvent",s))},5:function(){var e,n,o,i;r.type="joinPublicGroupSuccess",u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),s.operation="acceptRequest",t.isChatroom?null===(o=u.eventHandler)||void 0===o||o.dispatch("onChatroomEvent",s):null===(i=u.eventHandler)||void 0===i||i.dispatch("onGroupEvent",s)},4:function(){var e,n,o,i;r.type="joinGroupNotifications",r.reason=t.reason,u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),t.reason&&(s.reason=t.reason),s.operation="requestToJoin",t.isChatroom?null===(o=u.eventHandler)||void 0===o||o.dispatch("onChatroomEvent",s):null===(i=u.eventHandler)||void 0===i||i.dispatch("onGroupEvent",s)},3:function(){var e,n;r.type="leave",u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r)},2:function(){var e,n;r.type="join",u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r)},1:function(){var e,n,o,i;r.type="deleteGroupChat",u.onPresence&&u.onPresence(r),t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomChange",r):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupChange",r),s.operation="destroy",t.isChatroom?null===(o=u.eventHandler)||void 0===o||o.dispatch("onChatroomEvent",s):null===(i=u.eventHandler)||void 0===i||i.dispatch("onGroupEvent",s)},0:function(){var e,n;s.operation="create",t.isChatroom?null===(e=u.eventHandler)||void 0===e||e.dispatch("onChatroomEvent",s):null===(n=u.eventHandler)||void 0===n||n.dispatch("onGroupEvent",s)}}[e]||function(){console.error("No match operation ".concat(e))})()}var f=function(e){var t=this.root.lookup("easemob.pb.MUCBody").decode(e.payload),n=t.operation;c.vF.debug("onMucMessage",t),h.call(this,n,t)},v={handleRosterMsg:function(e){var t,n,r,o,i,s,a=this.root.lookup("easemob.pb.RosterBody").decode(e.payload),u={type:"",to:a.to[0].name,from:a.from.name,status:a.reason};switch(a.operation){case 2:u.type="subscribe",this.onContactInvited&&this.onContactInvited(u),null===(t=this.eventHandler)||void 0===t||t.dispatch("onContactInvited",u);break;case 3:u.type="unsubscribed",this.onContactDeleted&&this.onContactDeleted(u),null===(n=this.eventHandler)||void 0===n||n.dispatch("onContactDeleted",u);break;case 4:u.type="subscribed",this.onContactAdded&&this.onContactAdded(u),null===(r=this.eventHandler)||void 0===r||r.dispatch("onContactAdded",u);break;case 5:u.type="unsubscribed",this.onContactRefuse&&this.onContactRefuse(u),null===(o=this.eventHandler)||void 0===o||o.dispatch("onContactRefuse",u);break;case 6:case 7:break;case 8:u.type="subscribed",this.onContactAgreed&&this.onContactAgreed(u),null===(i=this.eventHandler)||void 0===i||i.dispatch("onContactAgreed",u);break;case 9:u.type="unsubscribed",this.onContactRefuse&&this.onContactRefuse(u),null===(s=this.eventHandler)||void 0===s||s.dispatch("onContactRefuse",u);break;default:c.vF.error("handleRosterMsg:",a)}this.onPresence&&u.type&&this.onPresence(u)}},E=n(346),m=function(e){var t,n,r,o,i,s,u=this.root.lookup("easemob.pb.StatisticsBody").decode(e.payload),l=u.operation,d=u.reason;switch(l){case 0:this.onStatisticMessage&&this.onStatisticMessage(u),null===(t=this.eventHandler)||void 0===t||t.dispatch("onStatisticMessage",u);break;case 1:s=E.A.create({type:a.C.WEBIM_CONNCTION_USER_REMOVED,message:"user has been removed"}),this.disconnectedReason=s,this.logOut=!0,this.onError&&this.onError(s),null===(n=this.eventHandler)||void 0===n||n.dispatch("onError",s);break;case 2:s=E.A.create({type:a.C.WEBIM_CONNCTION_USER_LOGIN_ANOTHER_DEVICE,message:"the user is already logged on another device"}),d&&(s.data={loginInfoCustomExt:u.reason}),this.disconnectedReason=s,this.logOut=!0,this.onError&&this.onError(s),null===(r=this.eventHandler)||void 0===r||r.dispatch("onError",s);break;case 3:s=E.A.create({type:a.C.WEBIM_CONNCTION_USER_KICKED_BY_CHANGE_PASSWORD,message:"the user was kicked by changing password"}),this.disconnectedReason=s,this.logOut=!0,this.onError&&this.onError(s),null===(o=this.eventHandler)||void 0===o||o.dispatch("onError",s);break;case 4:s=E.A.create({type:a.C.WEBIM_CONNCTION_USER_KICKED_BY_OTHER_DEVICE,message:"the user was kicked by other device"}),this.disconnectedReason=s,this.logOut=!0,this.onError&&this.onError(s),null===(i=this.eventHandler)||void 0===i||i.dispatch("onError",s);break;default:c.vF.error("handleStatisticsMsg:",u)}},g=(n(5506),n(8678)),y=n(565),_=n(4723);function O(e){var t,n=[],r=[],o=e.data;o&&o.values&&o.values.forEach((function(e){Object.entries(e.status).forEach((function(e){r.push({device:e[0],status:Number(e[1])})})),n.push({userId:e.uid,lastTime:Number(e.last_time),expire:Number(e.expiry),ext:e.ext,statusDetails:r})})),this.onPresenceStatusChange&&this.onPresenceStatusChange(n),null===(t=this.eventHandler)||void 0===t||t.dispatch("onPresenceStatusChange",n)}function T(e){var t=this;e.data.forEach((function(e){var n,r={from:e.from,to:e.to,chatType:"chat"===e.channel_type?"singleChat":"groupChat",messageId:e.messageId,reactions:e.reactions,ts:e.ts};null===(n=t.eventHandler)||void 0===n||n.dispatch("onReactionChange",r)}))}function R(e){var t,n,r,o;if(e.data){var i=e.data,s={id:i.id||"",name:i.name||"",parentId:i.muc_parent_id||"",messageId:i.msg_parent_id||"",timestamp:i.timestamp||0,operator:i.from||"",operation:""};switch(i.operation){case"create":s.operation="create",s.createTimestamp=s.timestamp,s.messageCount=0,null===(t=this.eventHandler)||void 0===t||t.dispatch("onChatThreadChange",s);break;case"update_msg":s.operation="update",s.messageCount=i.message_count,i.last_message&&"{}"!==JSON.stringify(i.last_message)?s.lastMessage=y.h.call(this,i.last_message):"{}"===JSON.stringify(i.last_message)&&(s.lastMessage={}),null===(n=this.eventHandler)||void 0===n||n.dispatch("onChatThreadChange",s);break;case"update":s.operation="update",s.messageCount=i.message_count,null===(r=this.eventHandler)||void 0===r||r.dispatch("onChatThreadChange",s);break;case"delete":s.operation="destroy",null===(o=this.eventHandler)||void 0===o||o.dispatch("onChatThreadChange",s)}}}function I(e){var t,n=e.data;if(n.resource!==this.clientResource){var r={operation:"deleteRoaming",conversationId:n.to,chatType:"chat"===n.chatType?"singleChat":"groupChat",resource:n.resource};null===(t=this.eventHandler)||void 0===t||t.dispatch("onMultiDeviceEvent",r)}}function C(e){var t,n,r=e.data,o="";if(this.clientResource!==r.res||r.from!==this.user){switch(r.op){case"del":o="deleteConversation";break;case"top":o="pinnedConversation";break;case"not_top":o="unpinnedConversation";break;case"mark":o="markConversation";break;case"mark_delete":o="unMarkConversation";break;case"pin":o="pin";break;case"pin_delete":o="unpin";break;default:return void c.vF.error("unexpected conversation op:",r.op)}if("pin"!==o&&"unpin"!==o){var i={operation:o,conversationId:r.id,conversationType:"chat"===r.type?"singleChat":"groupChat",timestamp:r.ts};"markConversation"!==i.operation&&"unMarkConversation"!==i.operation||r.ext&&(i.conversationMark=_.s[r.ext]),null===(n=this.eventHandler)||void 0===n||n.dispatch("onMultiDeviceEvent",i)}else{var s=r.ext,a=r.from,u=r.id,l=r.type,d=r.ts,p={messageId:s||"",conversationId:"chat"===l?a:u,conversationType:y.p[l],operation:o,operatorId:a,time:d};null===(t=this.eventHandler)||void 0===t||t.dispatch("onMessagePinEvent",p)}}}function S(e){var t,n=this;null===(t=e.values)||void 0===t||t.forEach((function(e){var t,r,o,i,s;if(e.operator_resource!==n.clientResource)if("ignoreInterval"in e.data){var a={operation:"setSilentModeForUser",resource:e.operator_resource,data:e.data};null===(t=n.eventHandler)||void 0===t||t.dispatch("onMultiDeviceEvent",a)}else"group"in e?(a={operation:0===Object.keys(null!==(r=e.data)&&void 0!==r?r:{}).length?"removeSilentModeForConversation":"setSilentModeForConversation",resource:e.operator_resource,conversationId:e.group,type:"groupChat",data:e.data},null===(o=n.eventHandler)||void 0===o||o.dispatch("onMultiDeviceEvent",a)):(a={operation:0===Object.keys(null!==(i=e.data)&&void 0!==i?i:{}).length?"removeSilentModeForConversation":"setSilentModeForConversation",resource:e.operator_resource,conversationId:e.user,type:"singleChat",data:e.data},null===(s=n.eventHandler)||void 0===s||s.dispatch("onMultiDeviceEvent",a))}))}var N=function(e){var t=g.Wp.parseNotify(e.payload);switch(t.type){case"presence":O.call(this,t);break;case"reaction":T.call(this,t);break;case"thread":R.call(this,t);break;case"roaming_delete":I.call(this,t);break;case"conv":C.call(this,t);break;case"user_notification_mute":S.call(this,t);break;default:c.vF.error("unexpected notify type: ".concat(t.type))}},A=n(8232),b=n(564),M=n(7706),w=n(5323),L=n(75),U=n(3893),x=n(3989);function D(e){return D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},D(e)}var P,k,H,F,B=function(){return B=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},B.apply(this,arguments)},G=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{u(r.next(e))}catch(e){i(e)}}function a(e){try{u(r.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}u((r=r.apply(e,t||[])).next())}))},W=function(e,t){var n,r,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,r=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!((o=(o=s.trys).length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=t.call(e,s)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}};!function(e){e[e.NORMAL=0]="NORMAL",e[e.SINGLECHAT=1]="SINGLECHAT",e[e.GROUPCHAT=2]="GROUPCHAT",e[e.CHATROOM=3]="CHATROOM",e[e.READ_ACK=4]="READ_ACK",e[e.DELIVER_ACK=5]="DELIVER_ACK",e[e.RECALL=6]="RECALL",e[e.CHANNEL_ACK=7]="CHANNEL_ACK",e[e.EDIT=8]="EDIT"}(P||(P={})),function(e){e[e.BODY=1]="BODY",e[e.BODY_AND_EXT=2]="BODY_AND_EXT"}(k||(k={})),function(e){e[e.APNs=0]="APNs",e[e.FCM=1]="FCM",e[e.HMSPUSH=2]="HMSPUSH",e[e.MIPUSH=3]="MIPUSH",e[e.MEIZUPUSH=4]="MEIZUPUSH",e[e.VIVOPUSH=5]="VIVOPUSH",e[e.OPPOPUSH=6]="OPPOPUSH",e[e.HONORPUSH=7]="HONORPUSH"}(H||(H={})),function(e){e[e.OK=0]="OK",e[e.FAIL=1]="FAIL",e[e.UNAUTHORIZED=2]="UNAUTHORIZED",e[e.MISSING_PARAMETER=3]="MISSING_PARAMETER",e[e.WRONG_PARAMETER=4]="WRONG_PARAMETER",e[e.REDIRECT=5]="REDIRECT",e[e.TOKEN_EXPIRED=6]="TOKEN_EXPIRED",e[e.PERMISSION_DENIED=7]="PERMISSION_DENIED",e[e.NO_ROUTE=8]="NO_ROUTE",e[e.UNKNOWN_COMMAND=9]="UNKNOWN_COMMAND",e[e.PB_PARSER_ERROR=10]="PB_PARSER_ERROR",e[e.BIND_ANOTHER_DEVICE=11]="BIND_ANOTHER_DEVICE",e[e.IM_FORBIDDEN=12]="IM_FORBIDDEN",e[e.TOO_MANY_DEVICES=13]="TOO_MANY_DEVICES",e[e.PLATFORM_LIMIT=14]="PLATFORM_LIMIT",e[e.USER_MUTED=15]="USER_MUTED",e[e.ENCRYPT_DISABLE=16]="ENCRYPT_DISABLE",e[e.ENCRYPT_ENABLE=17]="ENCRYPT_ENABLE",e[e.DECRYPT_FAILURE=18]="DECRYPT_FAILURE",e[e.PERMISSION_DENIED_EXTERNAL=19]="PERMISSION_DENIED_EXTERNAL"}(F||(F={}));var j=["txt","img","video","audio","file","loc","custom","cmd","combine"],q=g.Wp.getEnvInfo();function K(){var e,t,n,r,o="webim",i="",s="",a=[],u=(new Date).valueOf(),l=g.Wp.getDevicePlatform(q);if(this.isFixedDeviceId){var d=g.Wp.getLocalDeviceInfo();if(d){var p=JSON.parse(d);o=p.deviceId,i=p.deviceName,s=p.deviceUuid,void 0!==this.customOSPlatform&&(i=this.deviceId,o="custom".concat(this.customOSPlatform,"_").concat(s))}else"webim"===this.deviceId?(s="".concat(l,"_").concat(u.toString()),o="".concat(this.deviceId,"_").concat(s),i=this.deviceId,void 0!==this.customOSPlatform&&(o="custom".concat(this.customOSPlatform,"_").concat(s))):(o=i=s="webim_".concat(l,"_").concat(this.deviceId),void 0!==this.customOSPlatform&&(s="".concat(l,"_").concat(u.toString()),i=this.deviceId,o="custom".concat(this.customOSPlatform,"_").concat(s))),g.Wp.setLocalDeviceInfo(JSON.stringify({deviceId:o,deviceName:i,deviceUuid:s}))}else"webim"===this.deviceId?(s="random_".concat(l,"_").concat(u.toString()),o="".concat(this.deviceId,"_").concat(s),i=this.deviceId):o=i=s="webim_".concat(l,"_").concat(this.deviceId),void 0!==this.customOSPlatform&&(s="random_".concat(l,"_").concat(u.toString()),i=this.deviceId,o="custom".concat(this.customOSPlatform,"_").concat(s));this.context.jid&&(this.context.jid.clientResource=o);var h=this.root.lookup("easemob.pb.Provision"),f=h.decode(a);void 0!==this.customOSPlatform&&(f.osCustomValue=this.customOSPlatform,this.osType=5),f.compressType=this.compressType,f.encryptType=this.encryptType,f.osType=this.osType,f.version=this.version,f.deviceName=i,f.resource=o,f.deviceUuid=s,f.authToken='{"token":"$t$'+this.token+'"}',f.sessionId=Date.now().toString()+":",this.loginInfoCustomExt&&(f.reason=this.loginInfoCustomExt),c.vF.debug("provision info",{version:this.version,resource:o,isFixedDeviceId:this.isFixedDeviceId,loginInfoCustomExt:!!this.loginInfoCustomExt,token:(null===(t=null===(e=this.token)||void 0===e?void 0:e.slice)||void 0===t?void 0:t.call(e,0,10))+"...",userId:null===(r=null===(n=this.context)||void 0===n?void 0:n.jid)||void 0===r?void 0:r.name}),f.actionVersion="v5.1",f=h.encode(f).finish();var v=this.root.lookup("easemob.pb.MSync"),E=v.decode(a);return E.version=this.version,E.guid=this.context.jid,E.auth="$t$"+this.token,E.command=3,E.deviceId=i,E.serviceId=this.dataReport.getServiceId(),E.encryptType=this.encryptType,E.payload=f,v.encode(E).finish()}function V(e,t){var n=this,r=g.Wp.getEnvInfo();if("zfb"===r.platform||"dd"===r.platform){for(var i="",s=0;s<e.length;s++)i+=String.fromCharCode(e[s]);return{data:i=o().btoa(i),isBuffer:!1,complete:function(){},fail:function(e){"sendSocketMessage:fail taskID not exist"!==e.errMsg&&"SocketTast.send:fail SocketTask.readyState is not OPEN"!==e.errMsg||(c.vF.debug("send message fail and reconnect"),n.reconnecting||n.reconnect()),t&&n._msgHash&&n._msgHash[t]&&n._msgHash[t].fail({id:t})}}}var a=e;return{data:a.buffer.slice(a.byteOffset,a.byteOffset+a.byteLength),fail:function(e){"sendSocketMessage:fail taskID not exist"!==e.errMsg&&"SocketTast.send:fail SocketTask.readyState is not OPEN"!==e.errMsg||n.reconnecting||n.reconnect(),t&&n._msgHash&&n._msgHash[t]&&n._msgHash[t].fail({id:t})}}}function z(e,t){var n;return G(this,void 0,void 0,(function(){var r,o,i,a,l,d;return W(this,(function(p){switch(p.label){case 0:for(c.vF.debug("distributeMeta, metas length: ",e.length),r=[],o=function(n){var o=e[n],a=new(s())(o.id.low,o.id.high,o.id.unsigned).toString(),l=o.from.name,d=o.to?o.to.name:"",p=!!o.to&&-1!==o.to.domain.indexOf("conference");if(i._load_msg_cache.some((function(e){return e.msgId===a})))return"continue";switch(i._load_msg_cache.length<=i.max_cache_length||i._load_msg_cache.shift(),i._load_msg_cache.push({msgId:a,from:l,to:d,isGroup:p}),o.ns){case 0:m.call(i,o);break;case 1:r.push(u.Ay.call(i,o,t,!1,!0,!0));break;case 2:f.call(i,o);break;case 3:v.handleRosterMsg.call(i,o);break;case 4:i.registerConfrIQHandler&&i.registerConfrIQHandler(o,t,i);break;case 5:N.call(i,o);break;default:c.vF.error("distributeMeta",o)}},i=this,a=0;a<e.length;a++)o(a);return[4,Promise.all(r)];case 1:return l=p.sent(),(d=l.filter((function(e){return e&&"cmd"!==e.type}))).length>0&&(null===(n=this.eventHandler)||void 0===n||n.dispatch("onMessage",d)),[2]}}))}))}function J(e,t){z.call(this,e,t)}function Y(e){var t,n=this;Ee.clear(),this._offlineMessagePullState===M.P.SYNC_INIT&&(this._offlineMessagePullState=M.P.SYNC_START,this._offlineMessagePullQueue=e.unread.reduce((function(e,t,r){return e.find((function(e){return e.name===t.queue.name}))||e.push(t),n.pullOfflineMessageInfo.pullCount+=t.n,e}),[]),this.pullOfflineMessageInfo.startTime=Date.now(),this._offlineMessageRpt=this.dataReport.geOperateFun({operationName:U.jz.MSYNC_SYNCOFFLINEMESSAGE}),null===(t=this.eventHandler)||void 0===t||t.dispatch("onOfflineMessageSyncStart"))}function X(e){var t;if(this._offlineMessagePullState===M.P.SYNC_START){var n=this._offlineMessagePullQueue.findIndex((function(t){return t.queue.name===e.queue.name}));if(n>-1){this._offlineMessagePullQueue.splice(n,1);var r=Ee.get(e.queue.name)||0;r>0&&this._offlineMessageRpt({isEndApi:!1,data:{isSuccess:1,requestName:U.jz.MSYNC_SYNCOFFLINEMESSAGE,requestMethod:"WEBSOCKET",requestUrl:this.url,code:U.G8.success,resultCount:r,requestElapse:Date.now()-this.pullOfflineMessageInfo.startTime}})}0===this._offlineMessagePullQueue.length&&(this._offlineMessagePullState=M.P.SYNC_FINISH,null===(t=this.eventHandler)||void 0===t||t.dispatch("onOfflineMessageSyncFinish"),this._offlineMessageRpt({data:{isLastApi:1,isSuccess:1,requestName:U.jz.MSYNC_SYNCOFFLINEMESSAGE,requestMethod:"WEBSOCKET",requestUrl:this.url,code:U.G8.success,resultCount:this.pullOfflineMessageInfo.pullCount}}),Ee.clear())}}function Q(e){var t=this.root.lookup("easemob.pb.CommUnreadDL");t=t.decode(e.payload);var n=new(s())(t.timestamp.low,t.timestamp.high,t.timestamp.unsigned).toString();if(this.expirationTime&&this.compareTokenExpireTime(Number(n),this.expirationTime),0===t.unread.length)ae.call(this);else{Y.call(this,t),c.vF.debug("pull unread message",t.unread);for(var r=0;r<t.unread.length;r++)ee.call(this,t.unread[r].queue)}ae.call(this)}function $(){var e=[],t=this.root.lookup("easemob.pb.StatisticsBody"),n=t.decode(e);n.operation=0,n=t.encode(n).finish();var r=this.root.lookup("easemob.pb.Meta").decode(e);r.id=(new Date).valueOf(),r.ns=0,r.payload=n;var o=this.root.lookup("easemob.pb.CommSyncUL"),i=o.decode(e);i.meta=r,i=o.encode(i).finish();var s=this.root.lookup("easemob.pb.MSync"),a=s.decode(e);return a.version=this.version,a.encryptType=[0],a.command=0,a.payload=i,s.encode(a).finish()}function Z(e){var t=[],n=this.root.lookup("easemob.pb.CommSyncUL"),r=n.decode(t);r.queue=e,r=n.encode(r).finish();var o=this.root.lookup("easemob.pb.MSync"),i=o.decode(t);return i.version=this.version,i.encryptType=this.encryptType,i.command=0,i.payload=r,o.encode(i).finish()}function ee(e){c.vF.debug("sendBackqueue",e);var t=Z.call(this,e);Se.call(this,t)}function te(e,t){var n=[],r=this.root.lookup("easemob.pb.CommSyncUL"),o=r.decode(n);o.queue=t,o.key=new(s())(e.low,e.high,e.unsigned).toString(),o=r.encode(o).finish();var i=this.root.lookup("easemob.pb.MSync"),a=i.decode(n);return a.version=this.version,a.encryptType=this.encryptType,a.command=0,a.payload=o,i.encode(a).finish()}function ne(){var e=this;this.uniPush&&!1===this.isRegisterPush&&(this.isRegisterPush=!0,this.uniPush.onRegister((function(t){c.vF.debug("push token onRegister",t);var n=e.uniPushConfig||{},r=n.APNsCertificateName,o=void 0===r?"":r,i=n.HMSCertificateName,s=void 0===i?"":i,a=n.HONORCertificateName,u=void 0===a?"":a,l=n.MEIZUCertificateName,d=void 0===l?"":l,p=n.MICertificateName,h=void 0===p?"":p,f=n.OPPOCertificateName,v=void 0===f?"":f,E=n.VIVOCertificateName,m=void 0===E?"":E,g=n.FCMCertificateName,y=void 0===g?"":g;switch(t.push_type){case H.APNs:e.pushCertificateName=o;break;case H.FCM:e.pushCertificateName=y;break;case H.HMSPUSH:e.pushCertificateName=s;break;case H.HONORPUSH:e.pushCertificateName=u;break;case H.MEIZUPUSH:e.pushCertificateName=d;break;case H.MIPUSH:e.pushCertificateName=h;break;case H.OPPOPUSH:e.pushCertificateName=v;break;case H.VIVOPUSH:e.pushCertificateName=m;break;default:c.vF.error("unexpected push type",t.push_type)}e.pushCertificateName&&t.push_token&&w.B.call(e,{deviceId:e.clientResource,deviceToken:t.push_token,notifierName:e.pushCertificateName}).then((function(){c.vF.debug("uploadPushToken success")})).catch((function(e){c.vF.debug("uploadPushToken failed",e)}))})))}function re(){var e;this._offlineMessagePullState=M.P.SYNC_INIT,this._offlineMessagePullQueue=[],this.times=1,this.autoReconnectNumTotal=0,this.onOpened&&this.onOpened(),ne.call(this),null===(e=this.eventHandler)||void 0===e||e.dispatch("onConnected"),se.call(this),ce.call(this),ue.call(this),ae.call(this),this.pullOfflineMessageInfo={pullCount:0,startTime:0}}function oe(e,t){c.vF.debug("sendLastSession",e);var n=te.call(this,e,t);Se.call(this,n)}function ie(e){var t,n,r,o,i,s,u,l,d,p,h,f,v,m,g=this,y=this.root.lookup("easemob.pb.Provision").decode(e.payload);if(this.context.jid&&(this.context.jid.clientResource=y.resource),this.clientResource=y.resource,this.provisionTimer&&clearTimeout(this.provisionTimer),c.vF.debug("receiveProvisionResult",y.status),this._isLogging=!1,y.status.errorCode===F.OK){if(this.disconnectedReason=void 0,y.authToken){var _=JSON.parse(y.authToken).expires_in;if(!this.tokenExpiredTimer&&!this.tokenWillExpireTimer){var O=Date.now();this.expirationTime=_;var T=this.expirationTime-O;this.expiresIn=T<0?0:T,this.tokenExpireTimeCountDown(this.expiresIn)}}this.reconnecting=!1,this.logOut=!1,this.needSetReadyState&&(this.sock=B(B({},this.sock),{close:this.sock.close,send:this.sock.send,readyState:1})),this._localCache?(null===(t=this._localCache)||void 0===t?void 0:t.getInstance())?re.call(this):this._localCache&&new this._localCache({user:this.user,dbName:"cache_".concat(Math.abs((0,b.s5)(this.appKey||this.appId)),"_").concat(this.user),version:2,onInit:function(){return G(g,void 0,void 0,(function(){return W(this,(function(e){return c.vF.debug("localCache init success"),re.call(this),[2]}))}))}}):re.call(this),null===(n=this.connectionResolve)||void 0===n||n.call(this)}else{var R=void 0,I=y.status,C=I.reason,S=I.errorCode;switch(c.vF.debug("provision errorCode: ",S),c.vF.debug("provision reason: ",C),S){case F.FAIL:R="Sorry, user register limit"===C?E.A.create({type:a.C.MAX_LIMIT,message:"Sorry, the number of user registrations limit has been reached"}):"Sorry, user register rate limit"===C?E.A.create({type:a.C.MAX_LIMIT,message:"Sorry, user register rate limit"}):"Sorry, token expired"===C?E.A.create({type:a.C.WEBIM_TOKEN_EXPIRED,message:"Sorry, token expired"}):"Sorry, token or password does not match login info"===C?E.A.create({type:a.C.WEBIM_CONNCTION_TOKEN_NOT_ASSIGN_ERROR,message:"INVALID_TOKEN"}):"Sorry, user not found"===C?E.A.create({type:a.C.USER_NOT_FOUND,message:"User not found"}):E.A.create({type:a.C.WEBIM_CONNCTION_AUTH_ERROR,message:"Auth failed"}),null===(r=this.connectionReject)||void 0===r||r.call(this,R),null===(o=this.eventHandler)||void 0===o||o.dispatch("onError",R);break;case F.WRONG_PARAMETER:R=E.A.create({type:a.C.REQUEST_PARAMETER_ERROR,message:"Invalid parameter"}),null===(i=this.connectionReject)||void 0===i||i.call(this,R),null===(s=this.eventHandler)||void 0===s||s.dispatch("onError",R);break;case F.UNAUTHORIZED:R=E.A.create({type:a.C.WEBIM_CONNCTION_AUTH_ERROR,message:"Auth failed"}),null===(u=this.connectionReject)||void 0===u||u.call(this,R),null===(l=this.eventHandler)||void 0===l||l.dispatch("onError",R);break;case F.IM_FORBIDDEN:R=E.A.create({type:a.C.WEBIM_SERVER_SERVING_DISABLED,message:"Server serving disabled."}),null===(d=this.connectionReject)||void 0===d||d.call(this,R),null===(p=this.eventHandler)||void 0===p||p.dispatch("onError",R);break;case F.PERMISSION_DENIED:R="Sorry, the app month live count limit"===C?E.A.create({type:a.C.MAX_LIMIT,message:"Sorry, the monthly active user limit for this app has been reached"}):"Sorry, the app day live count limit"===C?E.A.create({type:a.C.MAX_LIMIT,message:"Sorry, the daily active user limit for this app has been reached"}):"Sorry, the app online count limit"===C?E.A.create({type:a.C.MAX_LIMIT,message:"Sorry, the maximum number limit of online users for this app has been reached"}):E.A.create({type:a.C.WEBIM_CONNCTION_AUTH_ERROR,message:"Auth failed"}),null===(h=this.connectionReject)||void 0===h||h.call(this,R),null===(f=this.eventHandler)||void 0===f||f.dispatch("onError",R);break;default:R=E.A.create({type:a.C.WEBIM_CONNCTION_AUTH_ERROR,message:C}),null===(v=this.connectionReject)||void 0===v||v.call(this,R),null===(m=this.eventHandler)||void 0===m||m.dispatch("onError",R)}this.disconnectedReason=R}}function se(){var e,t;if((null===(e=this.unMSyncSendMsgMap)||void 0===e?void 0:e.size)>0){for(var n=Array.from(this.unMSyncSendMsgMap.keys()),r=0;r<n.length;r++){var o=this.unMSyncSendMsgMap.get(n[r]);Se.call(this,o,n[r])}null===(t=this.unMSyncSendMsgMap)||void 0===t||t.clear()}}function ae(){var e=$.call(this);Se.call(this,e)}function ue(){c.vF.debug("sendUnreadDeal");var e=de.call(this);Se.call(this,e)}function ce(){var e=this;le.call(this),this.heartBeatID=setInterval((function(){(Date.now()-e.lastHeartbeat)/1e3>=30&&(c.vF.debug("send heart beat"),ue.call(e),e.lastHeartbeat=Date.now())}),this.heartBeatWait)}function le(){clearInterval(this.heartBeatID)}function de(){var e=this.root.lookup("easemob.pb.MSync"),t=e.decode([]);return t.version=this.version,t.encryptType=this.encryptType,t.command=1,e.encode(t).finish()}function pe(e,t){return e.some((function(e){return e.name===t.name}))}function he(e){var t=this.root.lookup("easemob.pb.CommNotice").decode(e.payload),n=pe(this._queues,t.queue);c.vF.debug("receive notice",t.queue,this._queues),n||this.clientResource===t.queue.clientResource&&t.queue.name===this.context.userId||(this._queues.push(t.queue),1===this._queues.length&&ee.call(this,t.queue))}function fe(e){return G(this,void 0,void 0,(function(){var t,n,r,i,s,a,u,c;return W(this,(function(l){if(t=g.Wp.getEnvInfo(),n=this.root.lookup("easemob.pb.MSync"),"miniCore"===this.name||"web"===t.platform){try{i=new Uint8Array(e.data),r=n.decode(i)}catch(e){throw new Error("decode message fail.")}return[2,r]}if("zfb"===t.platform||"dd"===t.platform){for(s=o().atob(e.data),a=[],u=0,c=s.length;u<c;++u)a.push(s.charCodeAt(u));return[2,n.decode(a)]}try{r=n.decode(e.data)}catch(e){throw new Error("decode message fail.")}return[2,r]}))}))}function ve(e){var t=this;this.lastHeartbeat=Date.now(),this.probingTimer&&clearTimeout(this.probingTimer),e.then((function(e){if(e)switch(e.command){case 0:me.call(t,e);break;case 1:Q.call(t,e);break;case 2:he.call(t,e);break;case 3:ie.call(t,e);break;default:c.vF.error("unexpected msync command: ".concat(e.command))}else c.vF.error("unexpected msync result: ".concat(e))}))}var Ee=new Map;function me(e){var t,n,r,o,i,l,d,p,h,f,v,m,y,_,O,T,R,I,C,S,N,b,w,x,D,P,k,H,F,j,q,K,V,z,Y,Q,$,Z,te,ne,re,ie,se,ae,ue,ce,le,de,pe,he,fe,ve,me,ge,ye,_e,Oe;return G(this,void 0,void 0,(function(){var G,Te,Re,Ie,Ce,Se,Ne,Ae,be,Me,we,Le,Ue,xe,De,Pe,ke,He,Fe,Be,Ge,We,je,qe,Ke,Ve,ze,Je,Ye,Xe,Qe,$e,Ze,et,tt,nt,rt,ot,it,st,at,ut,ct,lt,dt,pt,ht,ft=this;return W(this,(function(W){switch(W.label){case 0:if(G=(G=this.root.lookup("easemob.pb.CommSyncDL")).decode(e.payload),Te=new(s())(G.serverId.low,G.serverId.high,G.serverId.unsigned).toString(),Re=new(s())(G.metaId.low,G.metaId.high,G.metaId.unsigned).toString(),!(Number(Re)>0))return[3,23];if(clearTimeout(null===(t=this._msgHash[Re])||void 0===t?void 0:t.messageTimer),!G.status)return[3,22];if(0!==G.status.errorCode)return[3,21];if(null==(Ie=this._msgHash[Re])?void 0:Ie.isHandleChatroom){try{if(Ce="join"===(null===(n=this._msgHash[Re])||void 0===n?void 0:n.operation)){Se={};try{G.metas.length>0&&(Ne=this.root.lookup("easemob.pb.MUCBody"),Ae=Ne.decode(G.metas[0].payload),(be=JSON.parse(Ae.status.chatroomInfo||"{}")[this.user])&&(Se={isAllMembersMuted:be.is_all_mute,createTimestamp:be.create_timestamp,isInAllowlist:be.is_in_white_list,memberCount:be.member_count,muteExpireTimestamp:be.mute_duration?be.mute_duration:-1}))}catch(e){c.vF.error("decode chat room info error",e)}(null===(r=this._msgHash[Re])||void 0===r?void 0:r.resolve)instanceof Function&&this._msgHash[Re].resolve({type:0,data:{action:"apply",id:this._msgHash[Re].roomId,result:!0,user:this.context.userId,info:Se}}),(null===(o=this._msgHash[Re])||void 0===o?void 0:o.success)instanceof Function&&this._msgHash[Re].success({type:0,data:{action:"apply",id:this._msgHash[Re].roomId,result:!0,user:this.context.userId,info:Se}})}(null===(i=this._msgHash[Re])||void 0===i?void 0:i.resolve)instanceof Function&&!Ce&&this._msgHash[Re].resolve({type:0,data:{result:!0}}),(null===(l=this._msgHash[Re])||void 0===l?void 0:l.success)instanceof Function&&!Ce&&this._msgHash[Re].success({type:0,data:{result:!0}}),L.Xp.has(Re)&&(Me=L.Xp.get(Re),it=Me.rpt,st=Me.requestName,it({isEndApi:!0,data:{isSuccess:1,requestName:st,requestMethod:"WEBSOCKET",requestUrl:this.url,code:U.G8.success}}),L.Xp.delete(Re))}catch(e){L.Xp.has(Re)&&(we=L.Xp.get(Re),it=we.rpt,st=we.requestName,it({isEndApi:!0,data:{isSuccess:0,requestName:st,requestMethod:"WEBSOCKET",requestUrl:this.url,code:U.G8.failed,codeDesc:"when executing success function error"}}),L.Xp.delete(Re)),ut=E.A.create({type:a.C.WEBIM_CONNCTION_CALLBACK_INNER_ERROR,message:"when executing success function error",data:e}),this.onError&&this.onError(ut),null===(d=this.eventHandler)||void 0===d||d.dispatch("onError",ut)}delete this._msgHash[Re]}return!Ie||Ie.isHandleChatroom?[3,20]:(Le=null,Ue="",xe=0,De="",Pe=null,this._msgHash[Re].thirdMessage?(this._msgHash[Re].thirdMessage.id=G.serverId,this._msgHash[Re].thirdMessage.timestamp=G.timestamp,[4,u.Ay.call(this,this._msgHash[Re].thirdMessage,G.status,!0,!0)]):[3,2]);case 1:Pe=W.sent(),W.label=2;case 2:if(0===G.metas.length)return[3,13];W.label=3;case 3:switch(W.trys.push([3,12,,13]),ke=G.metas[0],He=G.status,ke.ns){case 1:return[3,4];case 5:return[3,9]}return[3,10];case 4:return this.useReplacedMessageContents?[4,u.Ay.call(this,ke,He,!0,!0)]:[3,8];case 5:return Pe=W.sent(),[4,null===(h=null===(p=this._localCache)||void 0===p?void 0:p.getInstance())||void 0===h?void 0:h.getMessageByServerMsgId(Re)];case 6:return(Fe=W.sent())?[4,null===(v=null===(f=this._localCache)||void 0===f?void 0:f.getInstance())||void 0===v?void 0:v.putMessageToDB(B(B(B({},Fe),Pe),{id:Re}))]:[3,8];case 7:W.sent(),W.label=8;case 8:return[3,11];case 9:return(Be=g.Wp.parseNotify(G.metas[0].payload)).edit_msg&&(Ge=Be.edit_msg,We=Ge.count,je=Ge.operator,qe=Ge.edit_time,Ke=Ge.sender,Ve=Ge.send_time,ze=Ge.chat_type,Le={operationTime:qe,operatorId:je,operationCount:We},Ue=Ke,xe=Number(Ve),De=u.Q[ze]),[3,11];case 10:return c.vF.error("decode local meta error",ke),[3,11];case 11:return[3,13];case 12:return Je=W.sent(),ut=E.A.create({type:a.C.WEBIM_LOAD_MSG_ERROR,message:"decode local meta message error",data:Je}),this.onError&&this.onError(ut),null===(m=this.eventHandler)||void 0===m||m.dispatch("onError",ut),[3,13];case 13:L.nf.has(Re)&&(L.nf.get(Re).rpt({isEndApi:!0,data:{isSuccess:1,requestMethod:"WEBSOCKET",requestUrl:this.url,code:U.G8.success,msgId:Te,messageType:null!==(y=U.He[null==Pe?void 0:Pe.chatType])&&void 0!==y?y:U.He.singleChat}}),L.nf.delete(Re)),W.label=14;case 14:return W.trys.push([14,18,,19]),Ye={localMsgId:Re,serverMsgId:Te},Le&&(Xe=this._msgHash[Re],Qe=Xe.editMessageId,$e=Xe.ext,Ze=Xe.customExts,et=Xe.customEvent,Pe.modifiedInfo=Le,Pe.from=Ue,Pe.time=Number(xe),Pe.chatType=De,Pe.id=Qe,null===(T=null===(O=null===(_=this._localCache)||void 0===_?void 0:_.getInstance())||void 0===O?void 0:O.getMessageByServerMsgId(Qe))||void 0===T||T.then((function(e){var t,n,r,o,i,s;e&&("txt"===e.type?null===(n=null===(t=ft._localCache)||void 0===t?void 0:t.getInstance())||void 0===n||n.putMessageToDB(B(B({},e),{msg:Pe.msg,modifiedInfo:Pe.modifiedInfo,translations:Pe.translations,ext:$e?Pe.ext:e.ext})):"custom"===e.type?null===(o=null===(r=ft._localCache)||void 0===r?void 0:r.getInstance())||void 0===o||o.putMessageToDB(B(B({},e),{ext:$e?Pe.ext:e.ext,customExts:Ze,customEvent:et,modifiedInfo:Pe.modifiedInfo})):null===(s=null===(i=ft._localCache)||void 0===i?void 0:i.getInstance())||void 0===s||s.putMessageToDB(B(B({},e),{ext:$e?Pe.ext:e.ext,modifiedInfo:Pe.modifiedInfo})))}))),Pe&&(Le?("txt"===Pe.type&&(Ye.message=Pe),Ye.modifiedInfo=Le):Ye.message=Pe),[4,null===(I=null===(R=this._localCache)||void 0===R?void 0:R.getInstance())||void 0===I?void 0:I.updateLocalMessage(Re,{serverMsgId:Te,status:A.q.SUCCESS})];case 15:return W.sent(),(null===(C=this._msgHash[Re])||void 0===C?void 0:C.success)instanceof Function?[4,this._msgHash[Re].success(Re,Te)]:[3,17];case 16:W.sent(),W.label=17;case 17:return(null===(S=this._msgHash[Re])||void 0===S?void 0:S.resolve)instanceof Function&&this._msgHash[Re].resolve(Ye),[3,19];case 18:return tt=W.sent(),ut=E.A.create({type:a.C.WEBIM_CONNCTION_CALLBACK_INNER_ERROR,message:"when executing success function error",data:tt}),this.onError&&this.onError(ut),null===(N=this.eventHandler)||void 0===N||N.dispatch("onError",ut),[3,19];case 19:this.onReceivedMessage&&this.onReceivedMessage({id:Re,mid:Te,to:this._msgHash[Re].to,time:0}),null===(b=this.eventHandler)||void 0===b||b.dispatch("onReceivedMessage",{id:Re,mid:Te,to:this._msgHash[Re].to}),delete this._msgHash[Re],W.label=20;case 20:return[3,22];case 21:if(15===G.status.errorCode)(null===(w=this._msgHash[Re])||void 0===w?void 0:w.fail)instanceof Function&&this._msgHash[Re].fail({type:a.C.SERVICE_NOT_ALLOW_MESSAGING_MUTE,reason:"you were muted"}),this._msgHash[Re].reject instanceof Function&&this._msgHash[Re].reject({type:a.C.SERVICE_NOT_ALLOW_MESSAGING_MUTE,reason:"you were muted"}),null===(D=null===(x=this._localCache)||void 0===x?void 0:x.getInstance())||void 0===D||D.updateLocalMessage(Re,{serverMsgId:Te,status:A.q.FAIL});else if(1===G.status.errorCode){switch(nt=void 0,G.status.reason){case"blocked":nt=a.C.PERMISSION_DENIED;break;case"group not found":nt=a.C.GROUP_NOT_EXIST;break;case"not in group or chatroom":nt=a.C.GROUP_NOT_JOINED;break;case"exceed recall time limit":nt=a.C.MESSAGE_RECALL_TIME_LIMIT;break;case"message recall disabled":nt=a.C.SERVICE_NOT_ENABLED;break;case"not in group or chatroom white list":nt=a.C.SERVICE_NOT_ALLOW_MESSAGING;break;case"nonroster":nt=a.C.USER_NOT_FRIEND,G.status.reason="not contact";break;case"group is disabled":nt=a.C.GROUP_IS_DISABLED,G.status.reason="group is disabled";break;case"limit directed users":nt=a.C.MAX_LIMIT;break;case"Sorry, edit limit reached":nt=a.C.MAX_LIMIT,G.status.reason="Modify message limit reached";break;case"Sorry, message does not exist":nt=a.C.MODIFY_MESSAGE_NOT_EXIST,G.status.reason="The message does not exist.";break;case"Sorry, You do not have permission":nt=a.C.PERMISSION_DENIED,G.status.reason="You do not have the modified permission.";break;case"Sorry, format is incorrect":nt=a.C.MODIFY_MESSAGE_FORMAT_ERROR,G.status.reason="The modify messaged format error.";break;case"Sorry, edit is not available":nt=a.C.SERVICE_NOT_ENABLED,G.status.reason="The message modify function is not activated.";break;case"Sorry, edit fail":nt=a.C.MODIFY_MESSAGE_FAILED,G.status.reason="Modify message failed.";break;default:G.status.reason.includes("grpID")&&G.status.reason.includes("does not exist!")?(nt=a.C.CHATROOM_NOT_EXIST,G.status.reason="The chat room dose not exist."):G.status.reason.includes("username")&&G.status.reason.includes("doesn't exist!")?nt=a.C.USER_NOT_FOUND:"group member list is full!"===G.status.reason?nt=a.C.CHATROOM_MEMBERS_FULL:G.status.reason.includes("can not join this group")&&G.status.reason.includes("is in the blacklist")?(nt=a.C.PERMISSION_DENIED,G.status.reason="permission denied"):"can not operate this group, reason: group is disabled"===G.status.reason?nt=a.C.GROUP_IS_DISABLED:G.status.reason.includes("moderation")?nt=a.C.MESSAGE_MODERATION_BLOCKED:G.status.reason.includes("group ID illegal, please check it")?(nt=a.C.REQUEST_PARAMETER_ERROR,G.status.reason="Invalid parameter"):nt=G.status.reason.includes("has create too many chatrooms")||G.status.reason.includes("has joined too many chatrooms")?a.C.MAX_LIMIT:G.status.reason.includes("auto create failed")?a.C.SERVER_BUSY:a.C.SERVER_UNKNOWN_ERROR}this._msgHash[Re]&&((null===(P=this._msgHash[Re])||void 0===P?void 0:P.isHandleChatroom)?(at=E.A.create({type:nt,message:G.status.reason||"",data:""}),this._msgHash[Re].reject instanceof Function&&this._msgHash[Re].reject(at),this._msgHash[Re].error instanceof Function&&this._msgHash[Re].error(at),L.Xp.has(Re)&&(rt=U.G8.failed,nt===a.C.CHATROOM_NOT_EXIST?rt=U.G8.notFound:nt===a.C.CHATROOM_MEMBERS_FULL&&(rt=U.G8.reachLimit),ot=L.Xp.get(Re),it=ot.rpt,st=ot.requestName,it({isEndApi:!0,data:{isSuccess:0,requestName:st,requestMethod:"WEBSOCKET",requestUrl:this.url,code:rt,codeDesc:G.status.reason}}),L.Xp.delete(Re))):(at=E.A.create({type:nt,message:G.status.reason||"",data:{id:Re,mid:Te}}),this._msgHash[Re].reject instanceof Function&&this._msgHash[Re].reject(at),this._msgHash[Re].fail instanceof Function&&this._msgHash[Re].fail({type:nt,reason:G.status.reason?G.status.reason:"",data:{id:Re,mid:Te}}),null===(H=null===(k=this._localCache)||void 0===k?void 0:k.getInstance())||void 0===H||H.updateLocalMessage(Re,{serverMsgId:Te,status:A.q.FAIL})),delete this._msgHash[Re])}else if(7===G.status.errorCode)"sensitive words"===G.status.reason&&this._msgHash[Re]?(at=E.A.create({type:a.C.MESSAGE_INCLUDE_ILLEGAL_CONTENT,message:"sensitive words",data:{id:Re,mid:Te}}),this._msgHash[Re].reject instanceof Function&&this._msgHash[Re].reject(at),this._msgHash[Re].fail instanceof Function&&this._msgHash[Re].fail({type:a.C.MESSAGE_INCLUDE_ILLEGAL_CONTENT,data:{id:Re,mid:Te,reason:"sensitive words"}}),null===(j=null===(F=this._localCache)||void 0===F?void 0:F.getInstance())||void 0===j||j.updateLocalMessage(Re,{serverMsgId:Te,status:A.q.FAIL})):"blocked by mod_antispam"===G.status.reason&&this._msgHash[Re]?(at=E.A.create({type:a.C.MESSAGE_INCLUDE_ILLEGAL_CONTENT,message:"blocked by mod_antispam",data:{id:Re,mid:Te}}),this._msgHash[Re].reject instanceof Function&&this._msgHash[Re].reject(at),this._msgHash[Re].fail instanceof Function&&this._msgHash[Re].fail({type:a.C.MESSAGE_INCLUDE_ILLEGAL_CONTENT,data:{id:Re,mid:Te,reason:"blocked by mod_antispam"}}),null===(K=null===(q=this._localCache)||void 0===q?void 0:q.getInstance())||void 0===K||K.updateLocalMessage(Re,{serverMsgId:Te,status:A.q.FAIL})):"user is mute"===G.status.reason&&this._msgHash[Re]?(at=E.A.create({type:a.C.USER_MUTED_BY_ADMIN,message:"user is mute",data:{id:Re,mid:Te}}),this._msgHash[Re].reject instanceof Function&&this._msgHash[Re].reject(at),this._msgHash[Re].fail instanceof Function&&this._msgHash[Re].fail(at),null===(z=null===(V=this._localCache)||void 0===V?void 0:V.getInstance())||void 0===z||z.updateLocalMessage(Re,{serverMsgId:Te,status:A.q.FAIL})):"traffic limit"===G.status.reason&&this._msgHash[Re]?(at=E.A.create({type:a.C.MESSAGE_CURRENT_LIMITING,message:"traffic limit",data:{id:Re,mid:Te}}),this._msgHash[Re].reject instanceof Function&&this._msgHash[Re].reject(at),this._msgHash[Re].fail instanceof Function&&this._msgHash[Re].fail(at),null===(Q=null===(Y=this._localCache)||void 0===Y?void 0:Y.getInstance())||void 0===Q||Q.updateLocalMessage(Re,{serverMsgId:Te,status:A.q.FAIL})):"Sorry, data is too large"===G.status.reason&&this._msgHash[Re]&&(at=E.A.create({type:a.C.MESSAGE_SIZE_LIMIT,message:"Sorry, data is too large",data:{id:Re,mid:Te}}),this._msgHash[Re].reject instanceof Function&&this._msgHash[Re].reject(at),this._msgHash[Re].fail instanceof Function&&this._msgHash[Re].fail(at),null===(Z=null===($=this._localCache)||void 0===$?void 0:$.getInstance())||void 0===Z||Z.updateLocalMessage(Re,{serverMsgId:Te,status:A.q.FAIL}));else if(19===G.status.errorCode)this._msgHash[Re]&&(L.nf.has(Re)&&(L.nf.get(Re).rpt({isEndApi:!0,data:{isSuccess:0,requestMethod:"WEBSOCKET",requestUrl:this.url,code:a.C.MESSAGE_EXTERNAL_LOGIC_BLOCKED,codeDesc:G.status.reason||"",msgId:Te,messageType:null!==(ne=U.He[null===(te=this._msgHash[Re])||void 0===te?void 0:te.chatType])&&void 0!==ne?ne:U.He.singleChat}}),L.nf.delete(Re)),at=E.A.create({type:a.C.MESSAGE_EXTERNAL_LOGIC_BLOCKED,message:G.status.reason||"",data:{id:Re,mid:Te}}),this._msgHash[Re].reject instanceof Function&&this._msgHash[Re].reject(at),this._msgHash[Re].fail instanceof Function&&this._msgHash[Re].fail({type:a.C.MESSAGE_EXTERNAL_LOGIC_BLOCKED,data:{id:Re,mid:Te,reason:G.status.reason}}),null===(ie=null===(re=this._localCache)||void 0===re?void 0:re.getInstance())||void 0===ie||ie.updateLocalMessage(Re,{serverMsgId:Te,status:A.q.FAIL}));else if(this._msgHash[Re]){L.nf.has(Re)&&(L.nf.get(Re).rpt({isEndApi:!0,data:{isSuccess:0,requestMethod:"WEBSOCKET",requestUrl:this.url,code:a.C.WEBIM_LOAD_MSG_ERROR,codeDesc:(null===(se=G.status)||void 0===se?void 0:se.reason)||"",msgId:Te,messageType:null!==(ue=U.He[null===(ae=this._msgHash[Re])||void 0===ae?void 0:ae.chatType])&&void 0!==ue?ue:U.He.singleChat}}),L.nf.delete(Re));try{at=E.A.create({type:a.C.WEBIM_LOAD_MSG_ERROR,message:(null===(ce=G.status)||void 0===ce?void 0:ce.reason)||"",data:{id:Re,mid:Te,reason:G.status&&G.status.reason}}),this._msgHash[Re].reject instanceof Function&&this._msgHash[Re].reject(at),null===(de=null===(le=this._localCache)||void 0===le?void 0:le.getInstance())||void 0===de||de.updateLocalMessage(Re,{serverMsgId:Te,status:A.q.FAIL}),this._msgHash[Re].fail instanceof Function&&this._msgHash[Re].fail({type:a.C.WEBIM_LOAD_MSG_ERROR,data:{errorCode:G.status&&G.status.errorCode,reason:G.status&&G.status.reason}})}catch(e){ut=E.A.create({type:a.C.WEBIM_CONNCTION_CALLBACK_INNER_ERROR,message:"when executing fail function error",data:e}),this.onError&&this.onError(ut),null===(pe=this.eventHandler)||void 0===pe||pe.dispatch("onError",ut)}delete this._msgHash[Re]}else L.nf.has(Re)&&(L.nf.get(Re).rpt({isEndApi:!0,data:{isSuccess:0,requestMethod:"WEBSOCKET",requestUrl:this.url,code:a.C.WEBIM_CONNCTION_CALLBACK_INNER_ERROR,codeDesc:"on message error",msgId:Te,messageType:null!==(fe=U.He[null===(he=this._msgHash[Re])||void 0===he?void 0:he.chatType])&&void 0!==fe?fe:U.He.singleChat}}),L.nf.delete(Re)),ut=E.A.create({type:a.C.WEBIM_CONNCTION_CALLBACK_INNER_ERROR,message:"on message error"}),this.onError&&this.onError(ut),null===(ve=this.eventHandler)||void 0===ve||ve.dispatch("onError",ut);W.label=22;case 22:return[2];case 23:if(G.metas&&0!==G.metas.length){this._offlineMessagePullState!==M.P.SYNC_FINISH&&Ee.set(G.queue.name,G.metas.length);try{J.call(this,G.metas,G.status)}catch(e){ut=E.A.create({type:a.C.WEBIM_LOAD_MSG_ERROR,message:"decode message error",data:e}),c.vF.error("decode message error",e),this.onError&&this.onError(ut),null===(me=this.eventHandler)||void 0===me||me.dispatch("onError",ut)}finally{G.isLast?(c.vF.debug("metas & CommSyncDLMessage.isLast",G.isLast),ct=-1,this._queues.some((function(e,t){return e.name===G.name&&(ct=t,!0)}))&&ct>0&&this._queues.splice(ct,1),this._queues.length>0&&ee.call(this,this._queues[0]),X.call(this,G)):G.nextKey&&(pt=new(s())(G.nextKey.low,G.nextKey.high,G.nextKey.unsigned).toString(),c.vF.debug("nexKey:",pt,"this.nextKey:",this.nexKey),pt!==this.nexKey||(null===(ge=G.queue)||void 0===ge?void 0:ge.name)!==this.currentQueue?(this.nexKey=pt,this.currentQueue=null===(ye=G.queue)||void 0===ye?void 0:ye.name,oe.call(this,G.nextKey,G.queue)):(c.vF.debug("nexKey and queue are same, delete it"),lt=-1,this._queues.some((function(e,t){return e.name===G.queue.name&&(lt=t,!0)}))&&this._queues.splice(lt,1),this._queues.length>0&&ee.call(this,this._queues[0])))}}else G.isLast?(c.vF.debug("CommSyncDLMessage.isLast",G.isLast),dt=-1,this._queues.some((function(e,t){return e.name===G.queue.name&&(dt=t,!0)}))&&this._queues.splice(dt,1),this._queues.length>0&&ee.call(this,this._queues[0]),X.call(this,G)):G.nextKey&&(pt=new(s())(G.nextKey.low,G.nextKey.high,G.nextKey.unsigned).toString(),c.vF.debug("nexKey:",pt,"this.nextKey:",this.nexKey),pt!==this.nexKey||(null===(_e=G.queue)||void 0===_e?void 0:_e.name)!==this.currentQueue?(this.nexKey=pt,this.currentQueue=null===(Oe=G.queue)||void 0===Oe?void 0:Oe.name,oe.call(this,G.nextKey,G.queue)):(c.vF.debug("nexKey and queue are same, delete it"),ht=-1,this._queues.some((function(e,t){return e.name===G.queue.name&&(ht=t,!0)}))&&this._queues.splice(ht,1),this._queues.length>0&&ee.call(this,this._queues[0])));return[2]}}))}))}function ge(e){var t=[],n=this.root.lookup("easemob.pb.KeyValue"),r=[];for(var o in e){var i=n.decode(t);if(i.key=o,void 0!==e[o]){if("object"===D(e[o]))i.type=8,i.stringValue=JSON.stringify(e[o]);else if("string"==typeof e[o])i.type=7,i.stringValue=e[o];else if("boolean"==typeof e[o])i.type=1,i.varintValue=!0===e[o]?1:0;else if(Number.isInteger(e[o]))i.type=2,i.varintValue=e[o];else if("bigint"==typeof e[o]||"symbol"===D(e[o])||"function"==typeof e[o]||Number.isNaN(e[o]))i.type=7,i.stringValue=e[o].toString();else{if("number"!=typeof e[o]||Number.isInteger(e[o]))continue;i.type=6,i.doubleValue=e[o]}r.push(i)}}return r}function ye(e){var t,n,r,o=[];if(this.root){var i,u=this.root.lookup("easemob.pb.MessageBody.Content").decode(o);switch(i=!e.group&&"groupchat"!==(null===(t=null==e?void 0:e.chatType)||void 0===t?void 0:t.toLowerCase())||e.roomType?e.group&&e.roomType||"chatroom"===(null===(n=null==e?void 0:e.chatType)||void 0===n?void 0:n.toLowerCase())?"chatRoom":"singleChat":"groupChat",e.type){case"txt":u.type=0,u.text=e.msg;break;case"img":u.type=1,e.body?(u.displayName=e.body.filename,u.remotePath=e.body.url,u.secretKey=e.body.secret,u.fileLength=e.body.file_length,u.size=e.body.size,u.thumbnailDisplayName=e.body.filename):e.file?(u.displayName=e.file.filename,u.remotePath=e.file.url,u.secretKey=e.file.secret,u.fileLength=e.file.file_length,u.size=e.file.size,u.thumbnailDisplayName=e.file.filename):(u.displayName=e.filename,u.remotePath=e.url,u.secretKey=e.secret,u.fileLength=e.file_length,u.size=e.size,u.thumbnailDisplayName=e.filename),e.isBuildCombinedMsg&&(u.remotePath=e.url,u.secretKey=e.secret,u.size={height:e.height,width:e.width}),e.isGif&&(u.subType=1);break;case"video":u.type=2,e.body?(u.displayName=e.body.filename,u.remotePath=e.body.url,u.secretKey=e.body.secret,u.fileLength=e.body.file_length,u.duration=e.body.length,u.thumbnailDisplayName=e.body.filename,u.thumbnailRemotePath=this.useOwnUploadFun?"":"".concat(e.body.url,"?vframe=true"),u.thumbnailSecretKey=this.useOwnUploadFun?"":e.body.secret):e.isBuildCombinedMsg&&(u.displayName=e.filename,u.remotePath=e.url,u.secretKey=e.secret,u.fileLength=e.file_length,u.duration=e.length,u.thumbnailDisplayName=e.filename,u.thumbnailRemotePath=e.thumb,u.thumbnailSecretKey=e.thumb_secret);break;case"loc":u.type=3,u.latitude=e.lat,u.longitude=e.lng,u.address=e.addr,u.buildingName=e.buildingName,u.latitude=e.lat;break;case"audio":u.type=4,e.body?(u.displayName=e.body.filename,u.remotePath=e.body.url,u.secretKey=e.body.secret,u.fileLength=e.body.file_length,u.duration=e.body.length,u.thumbnailDisplayName=e.body.filename):e.isBuildCombinedMsg&&(u.displayName=e.filename,u.remotePath=e.url,u.secretKey=e.secret,u.fileLength=e.file_length,u.duration=e.length,u.thumbnailDisplayName=e.filename);break;case"file":u.type=5,e.body?(u.displayName=e.body.filename,u.remotePath=e.body.url,u.secretKey=e.body.secret,u.fileLength=e.body.file_length,u.thumbnailDisplayName=e.body.filename):e.isBuildCombinedMsg&&(u.displayName=e.filename,u.remotePath=e.url,u.secretKey=e.secret,u.fileLength=e.file_length,u.thumbnailDisplayName=e.filename);break;case"cmd":u.type=6,u.action=e.action;break;case"custom":u.type=7,u.customEvent=e.customEvent,u.customExts=ge.call(this,e.customExts);break;case"combine":u.type=0,u.subType=0,u.text=e.compatibleText,u.displayName=e.filename,u.remotePath=e.url,u.secretKey=e.secret,u.fileLength=e.file_length,u.title=e.title,u.summary=e.summary,u.combineLevel=e.combineLevel}var c=[];e.ext&&(c=ge.call(this,e.ext));var l=this.root.lookup("easemob.pb.MessageBody"),d=l.decode(o),p=e.from||this.context.jid.name;d.from={name:e.isBuildCombinedMsg?p:this.context.jid.name};var h=e.to.indexOf("/"),f=h>-1?e.to.substring(0,h):e.to;if(d.to={name:f},"channel"===e.type)d.type=P.CHANNEL_ACK;else if("recall"===e.type)d.type=P.RECALL,d.ackMessageId=e.ackId;else if("delivery"===e.type)d.type=P.DELIVER_ACK,d.ackMessageId=e.ackId;else if("read"===e.type)d.type=P.READ_ACK,d.ackMessageId=e.ackId,"groupChat"===i&&(d.msgConfig={allowGroupAck:!0},d.ackContent=e.ackContent);else if("chatRoom"===i)d.type=P.CHATROOM;else if("groupChat"===i){if(d.type=P.GROUPCHAT,e.msgConfig){var v=e.msgConfig.allowGroupAck;d.msgConfig={allowGroupAck:!!v}}}else"singleChat"===i&&(d.type=P.SINGLECHAT);e.editMessageId&&(d.type=P.EDIT,d.editMessageId=e.editMessageId,d.editScope=k.BODY,e.ext&&(d.editScope=k.BODY_AND_EXT)),d.contents=[u],d.ext=c;var E=function(e){var t={};return"translations"in e&&(t.translations=e.translations),"isChatThread"in e&&e.isChatThread&&(t.thread={}),Object.keys(t).length>0?JSON.stringify(t):""}(e);E&&(d.meta=E),d=l.encode(d).finish();var m=this.root.lookup("easemob.pb.Meta"),g=m.decode(o);g.id=e.id;var y="easemob.com";if("chatRoom"!==i&&"groupChat"!==i||(y="conference.easemob.com"),g.to={appKey:this.appKey,name:f,domain:y},h>-1&&(g.to.clientResource=e.to.substring(h+1,e.to.length)),"chatRoom"===i&&(g.ext=ge.call(this,function(e){return{chatroom_msg_tag:"high"===e.priority?0:"low"===e.priority?2:1}}(e))),"recall"===e.type&&e.metaExt&&(g.ext=ge.call(this,{recallMessageExtensionInfo:e.metaExt})),g.ns=1,g.payload=d,g.routetype=e.deliverOnlineOnly?1:0,"singleChat"!==i&&Array.isArray(e.receiverList)&&(null===(r=e.receiverList)||void 0===r?void 0:r.length)>0&&(g.directedUsers=e.receiverList,g.routetype=2),e.isBuildCombinedMsg)return g.timestamp=s().fromValue(e.time),m.encode(g).finish();var _=this.root.lookup("easemob.pb.CommSyncUL"),O=_.decode(o);O.meta=g,!e.isPing&&Oe.call(this,e,g),O=_.encode(O).finish();var T=this.root.lookup("easemob.pb.MSync"),R=T.decode(o);return R.version=this.version,R.encryptType=this.encryptType,R.command=0,R.payload=O,T.encode(R).finish()}e.fail&&e.fail({type:a.C.WEBIM_CONNCTION_CLIENT_OFFLINE,message:"Not logged in"})}function _e(e){var t,n,r=this,o=B({},e);if(o.editMessageId){var i=ye.call(this,o);Se.call(this,i)}else{if(e.file)return o.accessToken=this.token,o.appKey=this.appKey,o.apiUrl=this.apiUrl,o.body&&o.body.url?ye.call(this,o):o.isGif&&"image/gif"!==o.file.data.type?void this._msgHash[o.id].reject({type:a.C.MESSAGE_INCLUDE_ILLEGAL_CONTENT,message:"The file type is not gif."}):new Promise((function(t,n){var i=o.onFileUploadComplete;o.onFileUploadComplete=function(t){var n,s,u,c,l,d,p;if(t.entities){if(t.entities[0]["file-metadata"]){var h=t.entities[0]["file-metadata"]["content-length"];o.file_length=h,o.filetype=t.entities[0]["file-metadata"]["content-type"],h>204800&&(o.thumbnail=!0)}var f="".concat(r.apiUrl,"/").concat(r.orgName,"/").concat(r.appName,"/chatfiles/").concat(t.entities[0].uuid);o.body={type:o.type||"file",secret:t.entities[0]["share-secret"],filename:o.file.filename||o.filename,url:f,length:o.length||0,filetype:o.filetype||o.file.filetype,file_length:(null===(u=null===(s=o.file)||void 0===s?void 0:s.data)||void 0===u?void 0:u.size)||0,size:{width:o.width||0,height:o.height||0}},o.file.url=t.uri,e.secret=t.entities[0]["share-secret"],t.url=e.url="".concat(f,"?em-redirect=true&share-secret=").concat(t.entities[0]["share-secret"]),e.file_length=o.file_length=(null===(l=null===(c=o.file)||void 0===c?void 0:c.data)||void 0===l?void 0:l.size)||0,"img"===o.type&&(e.thumb="".concat(e.url,"&thumbnail=true"),t.thumb="".concat(t.url,"&thumbnail=true")),"video"===o.type&&(e.thumb="".concat(e.url,"&vframe=true"),e.thumb_secret=t.entities[0]["share-secret"],t.thumb="".concat(t.url,"&vframe=true")),i instanceof Function&&i(t,o.id);var v=ye.call(r,o);L.nf.size<=L.Po&&L.nf.set(o.id,{rpt:r.dataReport.geOperateFun({operationName:U.jz.SEND_MSG})}),null===(p=null===(d=r._localCache)||void 0===d?void 0:d.getInstance())||void 0===p||p.storeMessage(e,A.q.INPROGRESS),Se.call(r,v)}else null===(n=r._msgHash[o.id])||void 0===n||n.reject({type:a.C.WEBIM_UPLOADFILE_ERROR,message:"Failed to upload the file",data:t})},o.onFileUploadError=function(e){var t;o.onFileUploadError instanceof Function&&o.onFileUploadError(e),null===(t=r._msgHash[o.id])||void 0===t||t.reject({type:a.C.WEBIM_UPLOADFILE_ERROR,message:e.message||"Failed to upload the file",data:e})},g.Wp.uploadFile.call(r,o,U.jz.UPLOAD_MSG_ATTACH)}));if("combine"===e.type){o.accessToken=this.token,o.appKey=this.appKey,o.apiUrl=this.apiUrl;var s=o.onFileUploadComplete,u=o.onFileUploadError;return new Promise((function(t,n){var i,l,d,p;if((null===(i=e.messageList)||void 0===i?void 0:i.length)>300||0===(null===(l=e.messageList)||void 0===l?void 0:l.length))return r._msgHash[o.id].reject({type:a.C.MAX_LIMIT,message:"The number of combined messages exceeded the limit."});var h=function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}([],e.messageList,!0),f=0;if(h.forEach((function(e){(null==e?void 0:e.combineLevel)>f&&(f=null==e?void 0:e.combineLevel)})),o.combineLevel=f+1,o.combineLevel>10)return r._msgHash[o.id].reject({type:a.C.MAX_LIMIT,message:"The level of the merged message exceeded the limit."});var v=Te.call(r,h),E=function(t,n){var i,a,u,c,l,d,p="".concat(r.apiUrl,"/").concat(r.orgName,"/").concat(r.appName,"/chatfiles/").concat(t.entities[0].uuid),h=null===(i=t.entities[0])||void 0===i?void 0:i["share-secret"],f="".concat(p,"?em-redirect=true");h&&(f="".concat(f,"&share-secret=").concat(h)),s instanceof Function&&s({url:f,secret:h}),e.url=f,e.secret=h,o.url=f,o.secret=t.entities[0]["share-secret"],o.filename=(null===(a=o.file)||void 0===a?void 0:a.filename)||(null==n?void 0:n.fileName),o.file_length=(null===(c=null===(u=o.file)||void 0===u?void 0:u.data)||void 0===c?void 0:c.size)||(null==n?void 0:n.fileLength)||0,null===(d=null===(l=r._localCache)||void 0===l?void 0:l.getInstance())||void 0===d||d.storeMessage(e,A.q.INPROGRESS);var v=ye.call(r,o);Se.call(r,v)},m=g.Wp.getEnvInfo();if("web"!==m.platform&&"node"!==m.platform&&"quick_app"!==m.platform&&(null===(p=null===(d=m.global)||void 0===d?void 0:d.canIUse)||void 0===p?void 0:p.call(d,"getFileSystemManager"))){var y=m.global.getFileSystemManager(),_="".concat(r.apiUrl,"/").concat(r.orgName,"/").concat(r.appName,"/chatfiles");y.writeFile({filePath:"".concat(m.global.env.USER_DATA_PATH,"/combine"),data:v.buffer,encoding:"binary",success:function(e){m.global.uploadFile({url:_,filePath:"".concat(m.global.env.USER_DATA_PATH,"/combine"),name:"file",header:{"Content-Type":"multipart/form-data",Authorization:"Bearer "+o.accessToken},success:function(e){if(200===e.statusCode){c.vF.debug("upload success",e);var t=JSON.parse(e.data);E(t,{fileName:"combine",fileLength:v.length})}else c.vF.debug("upload fail"),o.onFileUploadError instanceof Function&&o.onFileUploadError(e),this._msgHash[o.id].reject({type:a.C.WEBIM_UPLOADFILE_ERROR,message:"Failed to upload the merge message.Please try again",data:e})},fail:function(t){c.vF.debug("upload fail"),o.onFileUploadError instanceof Function&&o.onFileUploadError(e),this._msgHash[o.id].reject({type:a.C.WEBIM_UPLOADFILE_ERROR,message:"Failed to upload the merge message.Please try again",data:t})}})},fail:function(e){c.vF.debug("write file fail",e),this._msgHash[o.id].reject({type:a.C.WEBIM_UPLOADFILE_ERROR,message:"Failed to upload the merge message.Please try again",data:e})}})}else{var O=new File([v],"combine",{type:"application/octet-stream"}),T={url:URL.createObjectURL(O),filename:o.id,data:O};o.file=T,o.onFileUploadComplete=function(e){E(e)},o.onFileUploadError=function(e){u instanceof Function&&u(e),r._msgHash[o.id].reject({type:a.C.WEBIM_UPLOADFILE_ERROR,message:"Failed to upload the merge message.Please try again",data:e})},g.Wp.uploadFile.call(r,o,U.jz.UPLOAD_MSG_ATTACH)}}))}"img"===e.type&&(o.body||(o.body=B(B({},o),{size:{width:o.width||0,height:o.height||0}}))),null===(n=null===(t=this._localCache)||void 0===t?void 0:t.getInstance())||void 0===n||n.storeMessage(e,A.q.INPROGRESS),i=ye.call(this,o),Se.call(this,i)}}function Oe(e,t){if(e.editMessageId)return t.ignoreCallback=!0,void(this._msgHash[e.id].thirdMessage=t);j.includes(e.type)&&(this._msgHash[e.id].thirdMessage=t)}function Te(e){for(var t=Uint8Array.from("cm",(function(e){return e.charCodeAt(0)})),n=0;n<e.length;n++){for(var r=e[n],o=B(B({},r),{isBuildCombinedMsg:!0}),i=this.mSync.encodeChatMsg.call(this,o),s=i.length,a=new Uint8Array(4),u=0;u<4;u++)a[u]=s>>8*(3-u)&255;c.vF.debug("message length:",a);var l=new Uint8Array(t.length+a.length+i.length);l.set(t),l.set(a,t.length),l.set(i,t.length+a.length),t=l}var d=new Uint8Array(t.length+1),p=0;for(n=2;n<t.length;n++)n%2==1&&(p^=t[n]);return d.set(t),c.vF.debug("checkResult:",p),d[t.length]=p,d}function Re(e,t,n){var r=[],o=this.root.lookup("easemob.pb.MUCBody"),i=e.roomId,s=e.ext,a=void 0===s?"":s,u=e.leaveOtherRooms,c=void 0!==u&&u,l=o.decode(r);l.mucId={appKey:this.appKey,name:i,domain:"conference.easemob.com"},l.operation="join"===n?2:3,l.from={name:this.context.userId},l.isChatroom=!0,"join"===n&&(l.ext=a,l.leaveOtherRooms=c),l=o.encode(l).finish();var d=this.root.lookup("easemob.pb.Meta").decode(r);d.id=t,d.from={appKey:this.appKey,name:this.context.userId,domain:"easemob.com",client_resource:this.context.jid.clientResource},d.to={domain:"easemob.com"},d.ns=2,d.payload=l;var p=this.root.lookup("easemob.pb.CommSyncUL"),h=p.decode(r);h.meta=d,h=p.encode(h).finish();var f=this.root.lookup("easemob.pb.MSync"),v=f.decode(r);return v.version=this.version,v.encryptType=this.encryptType,v.command=0,v.payload=h,f.encode(v).finish()}function Ie(e,t){var n=g.Wp.getUniqueId(),r=Re.call(this,e,n,t),o="join"===t?U.jz.JOIN_CHATROOM:U.jz.QUIT_CHATROOM,i=this.dataReport.geOperateFun({operationName:o});return L.Xp.size<=L.Po&&L.Xp.set(n,{rpt:i,requestName:o}),Se.call(this,B(B({},e),{isHandleChatroom:!0,joinMsg:r,id:n,operation:t}),n)}function Ce(e){var t=this;return new Promise((function(n,r){var o,i,s,u,l,d;if(c.vF.debug("call send"),t.logOut)return c.vF.debug("send message failed",a.C.WEBIM_CONNECTION_CLOSED),r({type:a.C.WEBIM_CONNECTION_CLOSED,message:"not login"});if(!e.id||"string"!=typeof e.id||""===e.id)return r({type:a.C.MESSAGE_PARAMETER_ERROR,message:'Missing required parameter: "id"'});if(!e.to||"string"!=typeof e.to||""===e.to)return r({type:a.C.MESSAGE_PARAMETER_ERROR,message:'Missing required parameter: "to"'});var p="file"===e.type||"img"===e.type||"audio"===e.type||"video"===e.type,h="delivery"===e.type||"read"===e.type||"channel"===e.type,f="cmd"===e.type,v="recall"===e.type,E=null==e?void 0:e.editMessageId,m=!(h||v||f||E);if(e.id){if(!p&&!h||p&&t.useOwnUploadFun){var g="recall"===e.type?U.jz.SEND_RECALL_MSG:U.jz.SEND_MSG;E&&(g=U.jz.MODIFY_MESSAGE),L.nf.size<=L.Po&&L.nf.set(e.id,{rpt:t.dataReport.geOperateFun({operationName:g})})}m&&(null===(i=null===(o=t._localCache)||void 0===o?void 0:o.getInstance())||void 0===i||i.storeMessage(e,A.q.CREATE)),t._msgHash[e.id]=B(B({},e),{resolve:n,reject:r})}if(p||"combine"===e.type)return _e.call(t,e);if("txt"===e.type&&(null===(s=e.msgConfig)||void 0===s?void 0:s.languages)&&Array.isArray(null===(u=e.msgConfig)||void 0===u?void 0:u.languages)&&e.msgConfig.languages.length>0){var y=t.translateMessage||t.translation.translateMessage;if(!y)throw new Error("there is no method to translate message");y.call(t,{text:e.msg,languages:e.msgConfig.languages}).then((function(n){var r,o,i,s=null===(r=n.data[0])||void 0===r?void 0:r.translations;s=s.map((function(e){return{code:e.to,text:e.text}})),e.translations=s,t._msgHash[e.id].translations=s,null===(i=null===(o=t._localCache)||void 0===o?void 0:o.getInstance())||void 0===i||i.storeMessage(e,A.q.INPROGRESS);var a=ye.call(t,e);Se.call(t,a,e.id)})).catch((function(e){r(e)}))}else{m&&(null===(d=null===(l=t._localCache)||void 0===l?void 0:l.getInstance())||void 0===d||d.storeMessage(e,A.q.INPROGRESS));var _=ye.call(t,e);Se.call(t,_,e.id)}}))}function Se(e,t){var n,r=this;if(e.isHandleChatroom){if(!this.isOpened()){var o={data:"",type:a.C.WEBIM_CONNCTION_DISCONNECTED,message:"websocket has been disconnected"};if(L.Xp.has(e.id)){var i=L.Xp.get(e.id);(0,i.rpt)({isEndApi:!0,data:{isSuccess:0,requestName:i.requestName,requestMethod:"WEBSOCKET",requestUrl:this.url,code:U.G8.disconnect,codeDesc:"websocket has been disconnected"}}),L.Xp.delete(e.id)}return Promise.reject(o)}return new Promise((function(n,o){var i;r._msgHash[e.id]=B(B({},e),{resolve:n,reject:o});var s=g.Wp.getEnvInfo();i="miniCore"===r.name||"web"===s.platform?e.joinMsg:V.call(r,e.joinMsg,t);var u=t&&setTimeout((function(){var t,n,o;if(r._msgHash[e.id]){var i={type:a.C.REQUEST_TIMEOUT,message:"request timeout"};null===(n=(t=r._msgHash[e.id]).reject)||void 0===n||n.call(t,i),clearTimeout(null===(o=r._msgHash[e.id])||void 0===o?void 0:o.messageTimer),delete r._msgHash[e.id],r.reconnecting||r.reconnect(!0)}}),x.Ez);r._msgHash[e.id].messageTimer=u,r.sock.send(i)}))}if(!this.isOpened())return null===(n=this.unMSyncSendMsgMap)||void 0===n||n.set(t,e),!this.logOut&&this.autoReconnectNumTotal<this.autoReconnectNumMax&&(c.vF.debug("need to reconnect",this.autoReconnectNumTotal,this.autoReconnectNumMax),this.offLineSendConnecting=!0,this.reconnecting||this.reconnect()),void(this.onError&&this.onError({type:a.C.WEBIM_CONNCTION_DISCONNECTED,message:"websocket has been disconnected"}));var s,u=t&&setTimeout((function(){var e,n,o;if(r._msgHash[null!=t?t:""]){var i={type:a.C.MESSAGE_SEND_TIMEOUT,message:"send message timeout"};null===(n=(e=r._msgHash[null!=t?t:""]).reject)||void 0===n||n.call(e,i),clearTimeout(null===(o=r._msgHash[null!=t?t:""])||void 0===o?void 0:o.messageTimer),delete r._msgHash[null!=t?t:""],r.reconnecting||r.reconnect(!0)}}),x.Ez);this._msgHash[null!=t?t:""]&&(this._msgHash[null!=t?t:""].messageTimer=u);var l=g.Wp.getEnvInfo();s="miniCore"===this.name||"web"===l.platform?e:V.call(this,e,t),this.sock.send(s)}var Ne=function(e,t){return e.send=Ce,e.sendMsg=Ce,c.vF.debug("init Msync by ".concat(e.name)),{generateProvision:K.bind(e),base64transform:V.bind(e),distributeMeta:z.bind(e),decodeMeta:J.bind(e),decodeUnreadDL:Q.bind(e),_rebuild:$.bind(e),_lastsession:te.bind(e),receiveProvision:ie.bind(e),isInQueue:pe.bind(e),decodeNotice:he.bind(e),decodeMSync:fe.bind(e),distributeMSync:ve.bind(e),encodeChatMsg:ye.bind(e),upLoadFile:_e.bind(e),send:Ce.bind(e),stopHeartBeat:le.bind(e),handleChatRoom:Ie.bind(e),sendUnreadDeal:ue.bind(e)}}},8480:function(e,t,n){"use strict";var r=n(1828),o=n(8727).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return r(e,o)}},8490:function(e){"use strict";var t=Array,n=Math.abs,r=Math.pow,o=Math.floor,i=Math.log,s=Math.LN2;e.exports={pack:function(e,a,u){var c,l,d,p=t(u),h=8*u-a-1,f=(1<<h)-1,v=f>>1,E=23===a?r(2,-24)-r(2,-77):0,m=e<0||0===e&&1/e<0?1:0,g=0;for((e=n(e))!=e||e===1/0?(l=e!=e?1:0,c=f):(c=o(i(e)/s),e*(d=r(2,-c))<1&&(c--,d*=2),(e+=c+v>=1?E/d:E*r(2,1-v))*d>=2&&(c++,d/=2),c+v>=f?(l=0,c=f):c+v>=1?(l=(e*d-1)*r(2,a),c+=v):(l=e*r(2,v-1)*r(2,a),c=0));a>=8;)p[g++]=255&l,l/=256,a-=8;for(c=c<<a|l,h+=a;h>0;)p[g++]=255&c,c/=256,h-=8;return p[g-1]|=128*m,p},unpack:function(e,t){var n,o=e.length,i=8*o-t-1,s=(1<<i)-1,a=s>>1,u=i-7,c=o-1,l=e[c--],d=127&l;for(l>>=7;u>0;)d=256*d+e[c--],u-=8;for(n=d&(1<<-u)-1,d>>=-u,u+=t;u>0;)n=256*n+e[c--],u-=8;if(0===d)d=1-a;else{if(d===s)return n?NaN:l?-1/0:1/0;n+=r(2,t),d-=a}return(l?-1:1)*n*r(2,d-t)}}},8523:function(e,t,n){"use strict";n(6468)("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),n(6938))},8551:function(e,t,n){"use strict";var r=n(34),o=String,i=TypeError;e.exports=function(e){if(r(e))return e;throw new i(o(e)+" is not an object")}},8561:function(e){"use strict";function t(e,n){"string"==typeof e&&(n=e,e=void 0);var r=[];function o(e){if("string"!=typeof e){var n=i();if(t.verbose&&console.log("codegen: "+n),n="return "+n,e){for(var s=Object.keys(e),a=new Array(s.length+1),u=new Array(s.length),c=0;c<s.length;)a[c]=s[c],u[c]=e[s[c++]];return a[c]=n,Function.apply(null,a).apply(null,u)}return Function(n)()}for(var l=new Array(arguments.length-1),d=0;d<l.length;)l[d]=arguments[++d];if(d=0,e=e.replace(/%([%dfijs])/g,(function(e,t){var n=l[d++];switch(t){case"d":case"f":return String(Number(n));case"i":return String(Math.floor(n));case"j":return JSON.stringify(n);case"s":return String(n)}return"%"})),d!==l.length)throw Error("parameter count mismatch");return r.push(e),o}function i(t){return"function "+(t||n||"")+"("+(e&&e.join(",")||"")+"){\n  "+r.join("\n  ")+"\n}"}return o.toString=i,o}e.exports=t,t.verbose=!1},8570:function(e){e.exports=n;var t=null;try{t=new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([0,97,115,109,1,0,0,0,1,13,2,96,0,1,127,96,4,127,127,127,127,1,127,3,7,6,0,1,1,1,1,1,6,6,1,127,1,65,0,11,7,50,6,3,109,117,108,0,1,5,100,105,118,95,115,0,2,5,100,105,118,95,117,0,3,5,114,101,109,95,115,0,4,5,114,101,109,95,117,0,5,8,103,101,116,95,104,105,103,104,0,0,10,191,1,6,4,0,35,0,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,126,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,127,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,128,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,129,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,130,34,4,66,32,135,167,36,0,32,4,167,11])),{}).exports}catch(e){}function n(e,t,n){this.low=0|e,this.high=0|t,this.unsigned=!!n}function r(e){return!0===(e&&e.__isLong__)}n.prototype.__isLong__,Object.defineProperty(n.prototype,"__isLong__",{value:!0}),n.isLong=r;var o={},i={};function s(e,t){var n,r,s;return t?(s=0<=(e>>>=0)&&e<256)&&(r=i[e])?r:(n=u(e,(0|e)<0?-1:0,!0),s&&(i[e]=n),n):(s=-128<=(e|=0)&&e<128)&&(r=o[e])?r:(n=u(e,e<0?-1:0,!1),s&&(o[e]=n),n)}function a(e,t){if(isNaN(e))return t?m:E;if(t){if(e<0)return m;if(e>=h)return T}else{if(e<=-f)return R;if(e+1>=f)return O}return e<0?a(-e,t).neg():u(e%p|0,e/p|0,t)}function u(e,t,r){return new n(e,t,r)}n.fromInt=s,n.fromNumber=a,n.fromBits=u;var c=Math.pow;function l(e,t,n){if(0===e.length)throw Error("empty string");if("NaN"===e||"Infinity"===e||"+Infinity"===e||"-Infinity"===e)return E;if("number"==typeof t?(n=t,t=!1):t=!!t,(n=n||10)<2||36<n)throw RangeError("radix");var r;if((r=e.indexOf("-"))>0)throw Error("interior hyphen");if(0===r)return l(e.substring(1),t,n).neg();for(var o=a(c(n,8)),i=E,s=0;s<e.length;s+=8){var u=Math.min(8,e.length-s),d=parseInt(e.substring(s,s+u),n);if(u<8){var p=a(c(n,u));i=i.mul(p).add(a(d))}else i=(i=i.mul(o)).add(a(d))}return i.unsigned=t,i}function d(e,t){return"number"==typeof e?a(e,t):"string"==typeof e?l(e,t):u(e.low,e.high,"boolean"==typeof t?t:e.unsigned)}n.fromString=l,n.fromValue=d;var p=4294967296,h=p*p,f=h/2,v=s(1<<24),E=s(0);n.ZERO=E;var m=s(0,!0);n.UZERO=m;var g=s(1);n.ONE=g;var y=s(1,!0);n.UONE=y;var _=s(-1);n.NEG_ONE=_;var O=u(-1,2147483647,!1);n.MAX_VALUE=O;var T=u(-1,-1,!0);n.MAX_UNSIGNED_VALUE=T;var R=u(0,-2147483648,!1);n.MIN_VALUE=R;var I=n.prototype;I.toInt=function(){return this.unsigned?this.low>>>0:this.low},I.toNumber=function(){return this.unsigned?(this.high>>>0)*p+(this.low>>>0):this.high*p+(this.low>>>0)},I.toString=function(e){if((e=e||10)<2||36<e)throw RangeError("radix");if(this.isZero())return"0";if(this.isNegative()){if(this.eq(R)){var t=a(e),n=this.div(t),r=n.mul(t).sub(this);return n.toString(e)+r.toInt().toString(e)}return"-"+this.neg().toString(e)}for(var o=a(c(e,6),this.unsigned),i=this,s="";;){var u=i.div(o),l=(i.sub(u.mul(o)).toInt()>>>0).toString(e);if((i=u).isZero())return l+s;for(;l.length<6;)l="0"+l;s=""+l+s}},I.getHighBits=function(){return this.high},I.getHighBitsUnsigned=function(){return this.high>>>0},I.getLowBits=function(){return this.low},I.getLowBitsUnsigned=function(){return this.low>>>0},I.getNumBitsAbs=function(){if(this.isNegative())return this.eq(R)?64:this.neg().getNumBitsAbs();for(var e=0!=this.high?this.high:this.low,t=31;t>0&&!(e&1<<t);t--);return 0!=this.high?t+33:t+1},I.isZero=function(){return 0===this.high&&0===this.low},I.eqz=I.isZero,I.isNegative=function(){return!this.unsigned&&this.high<0},I.isPositive=function(){return this.unsigned||this.high>=0},I.isOdd=function(){return!(1&~this.low)},I.isEven=function(){return!(1&this.low)},I.equals=function(e){return r(e)||(e=d(e)),(this.unsigned===e.unsigned||this.high>>>31!=1||e.high>>>31!=1)&&this.high===e.high&&this.low===e.low},I.eq=I.equals,I.notEquals=function(e){return!this.eq(e)},I.neq=I.notEquals,I.ne=I.notEquals,I.lessThan=function(e){return this.comp(e)<0},I.lt=I.lessThan,I.lessThanOrEqual=function(e){return this.comp(e)<=0},I.lte=I.lessThanOrEqual,I.le=I.lessThanOrEqual,I.greaterThan=function(e){return this.comp(e)>0},I.gt=I.greaterThan,I.greaterThanOrEqual=function(e){return this.comp(e)>=0},I.gte=I.greaterThanOrEqual,I.ge=I.greaterThanOrEqual,I.compare=function(e){if(r(e)||(e=d(e)),this.eq(e))return 0;var t=this.isNegative(),n=e.isNegative();return t&&!n?-1:!t&&n?1:this.unsigned?e.high>>>0>this.high>>>0||e.high===this.high&&e.low>>>0>this.low>>>0?-1:1:this.sub(e).isNegative()?-1:1},I.comp=I.compare,I.negate=function(){return!this.unsigned&&this.eq(R)?R:this.not().add(g)},I.neg=I.negate,I.add=function(e){r(e)||(e=d(e));var t=this.high>>>16,n=65535&this.high,o=this.low>>>16,i=65535&this.low,s=e.high>>>16,a=65535&e.high,c=e.low>>>16,l=0,p=0,h=0,f=0;return h+=(f+=i+(65535&e.low))>>>16,p+=(h+=o+c)>>>16,l+=(p+=n+a)>>>16,l+=t+s,u((h&=65535)<<16|(f&=65535),(l&=65535)<<16|(p&=65535),this.unsigned)},I.subtract=function(e){return r(e)||(e=d(e)),this.add(e.neg())},I.sub=I.subtract,I.multiply=function(e){if(this.isZero())return E;if(r(e)||(e=d(e)),t)return u(t.mul(this.low,this.high,e.low,e.high),t.get_high(),this.unsigned);if(e.isZero())return E;if(this.eq(R))return e.isOdd()?R:E;if(e.eq(R))return this.isOdd()?R:E;if(this.isNegative())return e.isNegative()?this.neg().mul(e.neg()):this.neg().mul(e).neg();if(e.isNegative())return this.mul(e.neg()).neg();if(this.lt(v)&&e.lt(v))return a(this.toNumber()*e.toNumber(),this.unsigned);var n=this.high>>>16,o=65535&this.high,i=this.low>>>16,s=65535&this.low,c=e.high>>>16,l=65535&e.high,p=e.low>>>16,h=65535&e.low,f=0,m=0,g=0,y=0;return g+=(y+=s*h)>>>16,m+=(g+=i*h)>>>16,g&=65535,m+=(g+=s*p)>>>16,f+=(m+=o*h)>>>16,m&=65535,f+=(m+=i*p)>>>16,m&=65535,f+=(m+=s*l)>>>16,f+=n*h+o*p+i*l+s*c,u((g&=65535)<<16|(y&=65535),(f&=65535)<<16|(m&=65535),this.unsigned)},I.mul=I.multiply,I.divide=function(e){if(r(e)||(e=d(e)),e.isZero())throw Error("division by zero");var n,o,i;if(t)return this.unsigned||-2147483648!==this.high||-1!==e.low||-1!==e.high?u((this.unsigned?t.div_u:t.div_s)(this.low,this.high,e.low,e.high),t.get_high(),this.unsigned):this;if(this.isZero())return this.unsigned?m:E;if(this.unsigned){if(e.unsigned||(e=e.toUnsigned()),e.gt(this))return m;if(e.gt(this.shru(1)))return y;i=m}else{if(this.eq(R))return e.eq(g)||e.eq(_)?R:e.eq(R)?g:(n=this.shr(1).div(e).shl(1)).eq(E)?e.isNegative()?g:_:(o=this.sub(e.mul(n)),i=n.add(o.div(e)));if(e.eq(R))return this.unsigned?m:E;if(this.isNegative())return e.isNegative()?this.neg().div(e.neg()):this.neg().div(e).neg();if(e.isNegative())return this.div(e.neg()).neg();i=E}for(o=this;o.gte(e);){n=Math.max(1,Math.floor(o.toNumber()/e.toNumber()));for(var s=Math.ceil(Math.log(n)/Math.LN2),l=s<=48?1:c(2,s-48),p=a(n),h=p.mul(e);h.isNegative()||h.gt(o);)h=(p=a(n-=l,this.unsigned)).mul(e);p.isZero()&&(p=g),i=i.add(p),o=o.sub(h)}return i},I.div=I.divide,I.modulo=function(e){return r(e)||(e=d(e)),t?u((this.unsigned?t.rem_u:t.rem_s)(this.low,this.high,e.low,e.high),t.get_high(),this.unsigned):this.sub(this.div(e).mul(e))},I.mod=I.modulo,I.rem=I.modulo,I.not=function(){return u(~this.low,~this.high,this.unsigned)},I.and=function(e){return r(e)||(e=d(e)),u(this.low&e.low,this.high&e.high,this.unsigned)},I.or=function(e){return r(e)||(e=d(e)),u(this.low|e.low,this.high|e.high,this.unsigned)},I.xor=function(e){return r(e)||(e=d(e)),u(this.low^e.low,this.high^e.high,this.unsigned)},I.shiftLeft=function(e){return r(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?u(this.low<<e,this.high<<e|this.low>>>32-e,this.unsigned):u(0,this.low<<e-32,this.unsigned)},I.shl=I.shiftLeft,I.shiftRight=function(e){return r(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?u(this.low>>>e|this.high<<32-e,this.high>>e,this.unsigned):u(this.high>>e-32,this.high>=0?0:-1,this.unsigned)},I.shr=I.shiftRight,I.shiftRightUnsigned=function(e){if(r(e)&&(e=e.toInt()),0==(e&=63))return this;var t=this.high;return e<32?u(this.low>>>e|t<<32-e,t>>>e,this.unsigned):u(32===e?t:t>>>e-32,0,this.unsigned)},I.shru=I.shiftRightUnsigned,I.shr_u=I.shiftRightUnsigned,I.toSigned=function(){return this.unsigned?u(this.low,this.high,!1):this},I.toUnsigned=function(){return this.unsigned?this:u(this.low,this.high,!0)},I.toBytes=function(e){return e?this.toBytesLE():this.toBytesBE()},I.toBytesLE=function(){var e=this.high,t=this.low;return[255&t,t>>>8&255,t>>>16&255,t>>>24,255&e,e>>>8&255,e>>>16&255,e>>>24]},I.toBytesBE=function(){var e=this.high,t=this.low;return[e>>>24,e>>>16&255,e>>>8&255,255&e,t>>>24,t>>>16&255,t>>>8&255,255&t]},n.fromBytes=function(e,t,r){return r?n.fromBytesLE(e,t):n.fromBytesBE(e,t)},n.fromBytesLE=function(e,t){return new n(e[0]|e[1]<<8|e[2]<<16|e[3]<<24,e[4]|e[5]<<8|e[6]<<16|e[7]<<24,t)},n.fromBytesBE=function(e,t){return new n(e[4]<<24|e[5]<<16|e[6]<<8|e[7],e[0]<<24|e[1]<<16|e[2]<<8|e[3],t)}},8598:function(e,t,n){"use strict";var r=n(6518),o=n(9504),i=n(7055),s=n(5397),a=n(4598),u=o([].join);r({target:"Array",proto:!0,forced:i!==Object||!a("join",",")},{join:function(e){return u(s(this),void 0===e?",":e)}})},8622:function(e,t,n){"use strict";var r=n(4576),o=n(4901),i=r.WeakMap;e.exports=o(i)&&/native code/.test(String(i))},8678:function(e,t,n){"use strict";n.d(t,{om:function(){return I},i9:function(){return M},RD:function(){return C},RG:function(){return P},LP:function(){return A},X0:function(){return L},En:function(){return w},QM:function(){return D},Wp:function(){return F}}),n(8706),n(4423),n(5276),n(3792),n(8921),n(4782),n(739),n(3288),n(2010),n(2892),n(9868),n(9085),n(9432),n(6099),n(3362),n(7495),n(8781),n(7764),n(2953),n(6031),n(3296),n(7208),n(8408);var r=n(1531),o=(n(1699),function(){return o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},o.apply(this,arguments)}),i=function(e,t,n,i){var s,a,u,c,l,d,p,h,f,v,E,m,g,y,_,O,T,R,I,C,S,N,A,b,M,w,L,U,x=e.response;x&&"string"==typeof x&&(x=JSON.parse(x));var D=e.status,P={elapse:i,httpCode:D,errDesc:null==x?void 0:x.error_description};if(400===D){if(40002===x.error_code)return void t({type:r.C.THREAD_ALREADY_EXIST,message:null==x?void 0:x.error_description,extraInfo:P});if(40009===x.error_code)return void t({type:r.C.OPERATION_UNSUPPORTED,message:null==x?void 0:x.error_description,extraInfo:P});if(60005===x.error_code)return void t({type:r.C.GROUP_MEMBER_ATTRIBUTES_SET_FAILED,message:(null==x?void 0:x.desc)||(null==x?void 0:x.error_description),extraInfo:P});if(60010===x.error_code)return void((null===(s=null==x?void 0:x.error_description)||void 0===s?void 0:s.includes("exceeds chatgroup user metadata single value limit"))?t({type:r.C.MAX_LIMIT,message:(null==x?void 0:x.desc)||x.error_description,extraInfo:P}):t({type:r.C.NO_PERMISSION,message:(null==x?void 0:x.desc)||x.error_description,extraInfo:P}));if(60011===x.error_code)return void t({type:r.C.CHATROOM_NOT_JOINED,message:null==x?void 0:x.desc,extraInfo:P});if(14403===x.error_code)return void t({type:r.C.WEBIM_UPLOADFILE_ERROR,message:null==x?void 0:x.error_description,data:x,extraInfo:P});if(60006===x.error_code||60007===x.error_code||60009===x.error_code||60012===x.error_code)return void t({type:r.C.MAX_LIMIT,message:(null==x?void 0:x.desc)||(null==x?void 0:x.error_description),extraInfo:P});if(91104===x.error_code)return void t({type:r.C.NO_PERMISSION,message:null==x?void 0:x.error_description,extraInfo:P});if(null===(a=x.error_description)||void 0===a?void 0:a.includes("are not members of this group"))return(null===(u=e.responseURL)||void 0===u?void 0:u.includes("chatgroups"))?t({type:r.C.GROUP_NOT_JOINED,data:e.response||e.responseText,message:x.error_description,extraInfo:P}):t({type:r.C.CHATROOM_NOT_JOINED,data:e.response||e.responseText,message:x.error_description,extraInfo:P}),void n({type:r.C.WEBIM_CONNCTION_AJAX_ERROR,message:null==x?void 0:x.error_description,data:e.responseText,extraInfo:P});if("the app not open presence"===(null==x?void 0:x.result))return void t({type:r.C.SERVICE_NOT_ENABLED,message:null==x?void 0:x.result,extraInfo:P});if(null===(c=null==x?void 0:x.error_description)||void 0===c?void 0:c.includes("remark length must less"))return void t({type:r.C.MAX_LIMIT,message:null==x?void 0:x.error_description,extraInfo:P});switch(null==x?void 0:x.error_description){case"the user is already operation this message":t({type:r.C.REACTION_ALREADY_ADDED,message:null==x?void 0:x.error_description,extraInfo:P});break;case"The quantity has exceeded the limit!":t({type:r.C.MAX_LIMIT,message:null==x?void 0:x.error_description,extraInfo:P});break;case"The user not in this group!":t({type:r.C.GROUP_NOT_JOINED,message:null==x?void 0:x.error_description,extraInfo:P});break;case"the user operation is illegal!":t({type:r.C.REACTION_OPERATION_IS_ILLEGAL,message:null==x?void 0:x.error_description,extraInfo:P});break;case"this appKey is not open reaction service!":case"this appKey not open message roaming":t({type:r.C.SERVICE_NOT_ENABLED,message:null==x?void 0:x.error_description,extraInfo:P});break;case"this message is creating reaction, please try again.":t({type:r.C.REACTION_CREATING,message:null==x?void 0:x.error_description,extraInfo:P});break;case"groupId can not be null!":t({type:r.C.GROUP_NOT_EXIST,message:null==x?void 0:x.error_description,extraInfo:P});break;case"The input text is too long.":t({type:r.C.TRANSLATION_TEXT_TOO_LONG,message:null==x?void 0:x.error_description,extraInfo:P});break;case"The target language is not valid.":t({type:r.C.TRANSLATION_NOT_VALID,message:null==x?void 0:x.error_description,extraInfo:P});break;case"report failed, get message by id failed":t({type:r.C.MESSAGE_NOT_FOUND,message:null==x?void 0:x.error_description,extraInfo:P});break;case"ext is too big ":t({type:r.C.PRESENCE_PARAM_EXCEED,message:null==x?void 0:x.error_description,extraInfo:P});break;case"Request body not readable.Please check content type is correct!":case"param mark must be not empty, please check!":case"param mark illegal, please check it!":case"param pin_msg_id illegal, please check it!":t({type:r.C.REQUEST_PARAMETER_ERROR,message:null==x?void 0:x.error_description,extraInfo:P});break;case"updateRemark | they are not friends, please add as a friend first.":t({type:r.C.USER_NOT_FRIEND,message:null==x?void 0:x.error_description,extraInfo:P});break;default:t({type:r.C.WEBIM_CONNCTION_AJAX_ERROR,message:null==x?void 0:x.error_description,data:e.responseText,extraInfo:P}),n({type:r.C.WEBIM_CONNCTION_AJAX_ERROR,message:null==x?void 0:x.error_description,data:e.responseText,extraInfo:P})}}else if(401===D)40001===x.error_code||60001===x.error_code||"Unable to authenticate (OAuth)"===x.error_description?t({type:r.C.NO_PERMISSION,message:null==x?void 0:x.error_description,extraInfo:P}):(n({type:r.C.WEBIM_CONNCTION_AJAX_ERROR,data:e.responseText,message:e.responseText,extraInfo:P}),t({type:r.C.WEBIM_CONNCTION_AJAX_ERROR,data:e.responseText,message:e.responseText,extraInfo:P}));else if(403===D)4e4===x.error_code||60004===x.error_code||15002===x.error_code?t({type:r.C.SERVICE_NOT_ENABLED,message:null==x?void 0:x.error_description,extraInfo:P}):40003===x.error_code||40004===x.error_code?t({type:r.C.THREAD_ALREADY_EXIST,message:null==x?void 0:x.error_description,extraInfo:P}):40005===x.error_code||40007===x.error_code||91002===x.error_code?t({type:r.C.MAX_LIMIT,message:null==x?void 0:x.error_description,extraInfo:P}):60002===x.error_code?t({type:r.C.PERMISSION_DENIED,message:null==x?void 0:x.error_description,extraInfo:P}):91101===x.error_code?t({type:r.C.MAX_LIMIT,message:null==x?void 0:x.error_description,extraInfo:P}):91102===x.error_code&&t({type:r.C.REQUEST_PARAMETER_ERROR,message:x.error_description,extraInfo:P}),"group member list is full!"===x.error_description?(null===(l=e.responseURL)||void 0===l?void 0:l.includes("chatgroups"))?t({type:r.C.GROUP_MEMBERS_FULL,data:e.response||e.responseText,message:x.error_description,extraInfo:P}):t({type:r.C.CHATROOM_MEMBERS_FULL,data:e.response||e.responseText,message:x.error_description,extraInfo:P}):(null===(d=x.error_description)||void 0===d?void 0:d.includes("invite users to join group failed"))&&(null===(p=x.error_description)||void 0===p?void 0:p.includes("already in group"))?(null===(h=e.responseURL)||void 0===h?void 0:h.includes("chatgroups"))&&t({type:r.C.GROUP_ALREADY_JOINED,data:e.response||e.responseText,message:x.error_description,extraInfo:P}):(null===(f=x.error_description)||void 0===f?void 0:f.includes("are not members of this group"))?(null===(v=e.responseURL)||void 0===v?void 0:v.includes("chatgroups"))?t({type:r.C.GROUP_NOT_JOINED,data:e.response||e.responseText,message:x.error_description,extraInfo:P}):t({type:r.C.CHATROOM_NOT_JOINED,data:e.response||e.responseText,message:x.error_description,extraInfo:P}):(null===(E=x.error_description)||void 0===E?void 0:E.includes("service not open!"))||(null===(m=x.error_description)||void 0===m?void 0:m.includes("message report not open"))||(null===(g=x.error_description)||void 0===g?void 0:g.includes("messageroaming function not open"))?t({type:r.C.SERVICE_NOT_ENABLED,data:e.response||e.responseText,message:x.error_description,extraInfo:P}):(null===(y=x.error_description)||void 0===y?void 0:y.includes("members size is greater than max user size !"))?t({type:r.C.GROUP_MEMBERS_LIMIT,data:e.response||e.responseText,message:x.error_description,extraInfo:P}):(null===(_=x.error_description)||void 0===_?void 0:_.includes("can not operate this group, reason: group is disabled"))?t({type:r.C.GROUP_IS_DISABLED,data:e.response||e.responseText,message:x.error_description,extraInfo:P}):(null===(O=x.error_description)||void 0===O?void 0:O.includes("Invitee's contact max count"))||(null===(T=x.error_description)||void 0===T?void 0:T.includes("Inviter's contact max count"))?t({type:r.C.MAX_LIMIT,data:e.response||e.responseText,message:x.error_description,extraInfo:P}):t({type:r.C.PERMISSION_DENIED,data:e.response||e.responseText,message:"permission denied",extraInfo:o(o({},P),{errDesc:"permission denied"})}),n({type:r.C.WEBIM_CONNCTION_AJAX_ERROR,data:e.responseText,message:e.responseText,extraInfo:P});else if(404===D)40011===x.error_code?t({type:r.C.THREAD_NOT_EXIST,message:null==x?void 0:x.error_description,extraInfo:P}):40012===x.error_code?t({type:r.C.NO_PERMISSION,message:null==x?void 0:x.error_description,extraInfo:P}):60003===x.error_code||20004===x.error_code?t({type:r.C.GROUP_NOT_JOINED,message:null==x?void 0:x.error_description,extraInfo:P}):91001===x.error_code&&t({type:r.C.CONVERSATION_NOT_EXIST,message:null==x?void 0:x.error_description,extraInfo:P}),(null===(R=x.error_description)||void 0===R?void 0:R.includes("do not find this group"))||(null===(I=x.error_description)||void 0===I?void 0:I.includes("does not exist"))?(null===(C=e.responseURL)||void 0===C?void 0:C.includes("chatgroups"))?t({type:r.C.GROUP_NOT_EXIST,data:e.response||e.responseText,message:"The chat room dose not exist.",extraInfo:o(o({},P),{errDesc:"The chat room dose not exist."})}):t({type:r.C.CHATROOM_NOT_EXIST,data:e.response||e.responseText,message:"The chat room dose not exist.",extraInfo:o(o({},P),{errDesc:"The chat room dose not exist."})}):(null===(S=x.error_description)||void 0===S?void 0:S.includes("username"))&&(null===(N=x.error_description)||void 0===N?void 0:N.includes("doesn't exist!'"))||(null===(A=x.error_description)||void 0===A?void 0:A.includes("user not found"))||(null===(b=x.error_description)||void 0===b?void 0:b.includes("Service resource not found"))&&"UserNotFoundException"===(null==x?void 0:x.exception)?t({type:r.C.USER_NOT_FOUND,data:e.response||e.responseText,message:x.error_description,extraInfo:P}):(null===(M=x.error_description)||void 0===M?void 0:M.includes("user session pin message not exist"))?t({type:r.C.MESSAGE_NOT_FOUND,message:x.error_description,extraInfo:P}):t({type:r.C.WEBIM_CONNCTION_AJAX_ERROR,data:e.response||e.responseText,message:e.responseText,extraInfo:P}),n({type:r.C.WEBIM_CONNCTION_AJAX_ERROR,data:e.response||e.responseText,message:e.responseText,extraInfo:P});else if(406===D)90004===x.error_code&&t({type:r.C.OPERATION_NOT_ALLOWED,message:null==x?void 0:x.error_description,extraInfo:P});else if(429===D||503===D){if(null===(w=x.error_description)||void 0===w?void 0:w.includes("The request has reached the maximum limit"))return void t({type:r.C.MAX_LIMIT,message:e.responseText,extraInfo:P});if(null===(L=x.error_description)||void 0===L?void 0:L.includes("upload client logs reached limit"))return void t({type:r.C.MAX_LIMIT,message:e.responseText});t({type:r.C.SERVER_BUSY,data:e.response||e.responseText,message:"Server is busy.",extraInfo:o(o({},P),{errDesc:"Server is busy."})}),n({type:r.C.WEBIM_CONNCTION_AJAX_ERROR,data:e.responseText,message:"Server is busy.",extraInfo:o(o({},P),{errDesc:"Server is busy."})})}else if(500===D){if(40006===x.error_code||40008===x.error_code||40010===x.error_code)return void t({type:r.C.SERVER_UNKNOWN_ERROR,message:null==x?void 0:x.error_description,extraInfo:P});if(90005===x.error_code||99999===x.error_code)return void t({type:r.C.REQUEST_UNKNOWN,message:null==x?void 0:x.error_description,extraInfo:P});if(null===(U=x.error_description)||void 0===U?void 0:U.includes("translte failed!"))return void t({type:r.C.TRANSLATION_FAILED,message:e.responseText,extraInfo:P});t({type:r.C.WEBIM_CONNCTION_AJAX_ERROR,data:e.responseText,message:"",extraInfo:P}),n({type:r.C.WEBIM_CONNCTION_AJAX_ERROR,data:e.responseText,message:"",extraInfo:P})}else t({type:r.C.WEBIM_CONNCTION_AJAX_ERROR,data:e.responseText,message:e.responseText,extraInfo:o(o({},P),{errDesc:"ajax error"})}),n({type:r.C.WEBIM_CONNCTION_AJAX_ERROR,data:e.responseText,message:e.responseText,extraInfo:o(o({},P),{errDesc:"ajax error"})})},s=n(3893),a=n(75),u=n(2056),c=5242880,l=5242880,d={size:0},p={singleChat:"CHAT",groupChat:"GROUP",chatRoom:"ROOM"},h=(n(2675),n(9463),n(2259),n(8980),n(2712),n(4554),n(3989)),f=function(){return f=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},f.apply(this,arguments)};function v(){var e=this.context,t=e.orgName,n=e.appName,r=e.accessToken,o={url:"".concat(this.apiUrl,"/").concat(t,"/").concat(n,"/sdk/chatfiles/part-upload"),dataType:"json",type:"POST",headers:{Authorization:"Bearer "+r}};return u.vF.debug("Call multipartInit"),C.call(this,o,s.jz.SDK_INTERNAL).then((function(e){var t=e.entities[0];return{data:{fileMaxSize:t.file_upper_limit,partMinSize:t.part_lower_limit,uuid:t.uuid},extraInfo:e.extraInfo,type:e.type}}))}function E(e){var t=this;return new Promise((function(n,o){var i,s,a=(new Date).getTime(),c=t.context,l=c.orgName,d=c.appName,p=c.accessToken,v=e.uuid,E=e.partNumber,m=e.part,g=e.onProgress,y="".concat(t.apiUrl,"/").concat(l,"/").concat(d,"/sdk/chatfiles/part-upload/").concat(v),_=new XMLHttpRequest;u.vF.debug("Call multipartUpload"),_.upload&&(null===(s=(i=_.upload).addEventListener)||void 0===s||s.call(i,"progress",(function(e){null==g||g(e)}),!1)),_.addEventListener("abort",(function(){o({type:r.C.REQUEST_ABORT,message:"Request Abort",errorType:"onabort",xhr:_,extraInfo:{elapse:(new Date).getTime()-a,httpCode:_.status||-1,errDesc:"Request Abort",url:y}})}),!1),_.addEventListener("error",(function(){o({type:r.C.WEBIM_UPLOADFILE_ERROR,data:_,extraInfo:{elapse:(new Date).getTime()-a,httpCode:_.status||-1,errDesc:"request error",url:y}})}),!1),_.addEventListener("load",(function(){try{var e=JSON.parse(_.responseText);if(200!==_.status)return o({type:r.C.WEBIM_UPLOADFILE_ERROR,data:e,extraInfo:{elapse:(new Date).getTime()-a,httpCode:_.status||-1,errDesc:"part upload failed",url:y}}),!1;try{n(f(f({},e),{extraInfo:{elapse:(new Date).getTime()-a,httpCode:_.status||-1,url:y}}))}catch(e){o({type:r.C.WEBIM_CONNCTION_CALLBACK_INNER_ERROR,data:e,extraInfo:{elapse:(new Date).getTime()-a,httpCode:_.status||-1,errDesc:"part upload failed",url:y}})}}catch(e){o({type:r.C.WEBIM_UPLOADFILE_ERROR,data:_.responseText,extraInfo:{elapse:(new Date).getTime()-a,httpCode:_.status||-1,errDesc:"part upload failed",url:y}})}}),!1),_.addEventListener("timeout",(function(){o({type:r.C.REQUEST_TIMEOUT,message:"request timeout",extraInfo:{elapse:(new Date).getTime()-a,httpCode:_.status||-1,errDesc:"request timeout",url:y}})}),!1);var O=new FormData;O.append("part_file",m),O.append("part_number",E),_.timeout=h.Gc,_.open("PUT",y),_.setRequestHeader("restrict-access","true"),_.setRequestHeader("Accept","*/*"),_.setRequestHeader("Authorization","Bearer "+p),_.send(O)}))}function m(e){var t=e.uuid,n=e.thumbnailHeight,r=e.thumbnailWidth,o=e.conversationInfo,i=o.conversationId,a=o.conversationType,c=p[a],l=this.context,d=l.orgName,h=l.appName,f=l.accessToken,v="".concat(this.apiUrl,"/").concat(d,"/").concat(h,"/sdk/chatfiles/part-upload/").concat(t,"?restrict-access=true&chat-type=").concat(c,"&chat-target=").concat(i);r&&(v+="&thumbnail-width=".concat(r)),n&&(v+="&thumbnail-height=".concat(n));var E={url:v,dataType:"json",type:"POST",headers:{Authorization:"Bearer "+f}};return u.vF.debug("Call multipartComplete"),C.call(this,E,s.jz.SDK_INTERNAL)}function g(e){var t=e.uuid,n=this.context,r=n.orgName,o=n.appName,i=n.accessToken,a={url:"".concat(this.apiUrl,"/").concat(r,"/").concat(o,"/sdk/chatfiles/part-upload/").concat(t),dataType:"json",type:"DELETE",headers:{Authorization:"Bearer "+i}};return u.vF.debug("Call multipartAbort"),C.call(this,a,s.jz.SDK_INTERNAL)}var y=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{u(r.next(e))}catch(e){i(e)}}function a(e){try{u(r.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}u((r=r.apply(e,t||[])).next())}))},_=function(e,t){var n,r,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,r=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!((o=(o=s.trys).length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=t.call(e,s)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}},O=function(){function e(e,t){var n=this;this.handleUploadProgress=function(e,t){var r,o;if(e.total){n.progressArr[t]=e.loaded;var i=n.progressArr.reduce((function(e,t){return e+t}),0);null===(o=(r=n.options).onFileUploadProgress)||void 0===o||o.call(r,{isTrusted:e.isTrusted,type:e.type,loaded:i>n.file.size?n.file.size:i,total:n.file.size,lengthComputable:e.lengthComputable})}},this.uuid="",this.pool=[],this.progressArr=[],this.connection=e,this.options=t,this.partSize=c,this.file=t.file,this.init(),this.rpt=this.connection.dataReport.geOperateFun({operationName:s.jz.REST_UPLOAD_FILE_IN_PARTS})}return e.prototype.init=function(){var e,t,n,o;return y(this,void 0,void 0,(function(){var i,a,l,p,h,f,E,m,g,y,O,T,R,I,C;return _(this,(function(_){switch(_.label){case 0:return _.trys.push([0,4,,6]),[4,v.call(this.connection)];case 1:return i=_.sent(),a=i.data||{},l=a.fileMaxSize,p=void 0===l?0:l,h=a.partMinSize,f=void 0===h?c:h,E=a.uuid,m=void 0===E?"":E,g=i.extraInfo,T=g.elapse,R=g.httpCode,C=g.url,this.partSize=f,this.uuid=m,d.size=f,u.vF.debug("multipartInit success","uuid: ".concat(m),"fileMaxSize: ".concat(p),"partMinSize: ".concat(f)),this.file.size>p?[4,this.multipartAbort()]:[3,3];case 2:return _.sent(),null===(t=(e=this.options).onFileUploadError)||void 0===t||t.call(e,{code:r.C.WEBIM_UPLOADFILE_ERROR,message:"The file size exceeds the maximum limit"}),[2];case 3:return this.rpt({data:{requestUrl:C,requestName:s.TT.REST_INIT_UPLOAD_TASK_IN_PARTS,requestElapse:T,requestMethod:"POST",isSuccess:1,code:R}}),this.upload(),[3,6];case 4:return y=_.sent(),O=(null==y?void 0:y.extraInfo)||{},T=O.elapse,R=O.httpCode,I=O.errDesc,C=O.url,this.rpt({data:{requestUrl:C,requestName:s.TT.REST_INIT_UPLOAD_TASK_IN_PARTS,requestElapse:T,requestMethod:"POST",isSuccess:0,codeDesc:I,code:R}}),[4,this.multipartAbort()];case 5:return _.sent(),null===(o=null===(n=this.options)||void 0===n?void 0:n.onInitFail)||void 0===o||o.call(n),[3,6];case 6:return[2]}}))}))},e.prototype.upload=function(){var e,t,n;return y(this,void 0,void 0,(function(){var r,o,i,a,u,c,l=this;return _(this,(function(d){switch(d.label){case 0:(new FileReader).readAsArrayBuffer(this.file),r=this.file.size,o=Math.ceil(r/this.partSize),d.label=1;case 1:d.trys.push([1,7,,9]),i=function(t){var n,o,i,u;return _(this,(function(c){switch(c.label){case 0:return n=t*a.partSize,o=Math.min(r,n+a.partSize),i=null===(e=a.file)||void 0===e?void 0:e.slice(n,o),(u=E.call(a.connection,{uuid:a.uuid,partNumber:"".concat(t+1),part:i,onProgress:function(e){l.handleUploadProgress(e,t)}})).then((function(e){var t=(null==e?void 0:e.extraInfo)||{},n=t.elapse,r=t.httpCode,o=t.url;l.rpt({data:{requestUrl:o,requestName:s.TT.REST_UPLOAD_PART,requestElapse:n,isSuccess:1,requestMethod:"PUT",code:r}}),l.handleTask(u)})),u.catch((function(e){var t=(null==e?void 0:e.extraInfo)||{},n=t.elapse,r=t.httpCode,o=t.url,i=t.errDesc;l.rpt({data:{requestUrl:o,requestName:s.TT.REST_UPLOAD_PART,requestElapse:n,isSuccess:0,requestMethod:"PUT",code:r,codeDesc:i}}),delete e.extraInfo})),a.pool.push(u),4!==a.pool.length?[3,2]:[4,Promise.race(a.pool)];case 1:c.sent(),c.label=2;case 2:return[2]}}))},a=this,u=0,d.label=2;case 2:return u<o?[5,i(u)]:[3,5];case 3:d.sent(),d.label=4;case 4:return u++,[3,2];case 5:return[4,Promise.all(this.pool)];case 6:return d.sent(),this.multipartComplete(),[3,9];case 7:return c=d.sent(),[4,this.multipartAbort()];case 8:return d.sent(),this.rpt({data:{isLastApi:1,isSuccess:0}}),null===(n=(t=this.options).onFileUploadError)||void 0===n||n.call(t,c),[3,9];case 9:return[2]}}))}))},e.prototype.multipartComplete=function(){var e,t,n,r;return y(this,void 0,void 0,(function(){var o,i,a,u,c,l,d,p,h,f,v;return _(this,(function(E){switch(E.label){case 0:return E.trys.push([0,2,,3]),o=this.options.thumbnailInfo||{},i=o.width,a=o.height,[4,m.call(this.connection,{uuid:this.uuid,thumbnailHeight:a,thumbnailWidth:i,conversationInfo:this.options.conversationInfo})];case 1:return u=E.sent(),c=(null==u?void 0:u.extraInfo)||{},p=c.elapse,h=c.httpCode,v=c.url,this.rpt({data:{requestUrl:v,requestName:s.TT.REST_COMPLETE_UPLOAD_PART,requestElapse:p,requestMethod:"POST",isSuccess:1,code:h}}),this.rpt({data:{isLastApi:1,isSuccess:1}}),null===(t=(e=this.options).onFileUploadComplete)||void 0===t||t.call(e,u),[3,3];case 2:return l=E.sent(),d=(null==l?void 0:l.extraInfo)||{},p=d.elapse,h=d.httpCode,f=d.errDesc,v=d.url,this.rpt({data:{requestUrl:v,requestName:s.TT.REST_COMPLETE_UPLOAD_PART,requestElapse:p,requestMethod:"POST",isSuccess:0,codeDesc:f,code:h}}),this.rpt({data:{isLastApi:1,isSuccess:0}}),null===(r=(n=this.options).onFileUploadError)||void 0===r||r.call(n,l),[3,3];case 3:return[2]}}))}))},e.prototype.multipartAbort=function(){return y(this,void 0,void 0,(function(){var e,t,n,r,o,i,a,u;return _(this,(function(c){switch(c.label){case 0:if(!this.uuid)return[2];c.label=1;case 1:return c.trys.push([1,3,,4]),[4,g.call(this.connection,{uuid:this.uuid})];case 2:return e=c.sent(),t=(null==e?void 0:e.extraInfo)||{},o=t.elapse,i=t.httpCode,u=t.url,this.rpt({data:{requestUrl:u,requestName:s.TT.REST_ABORT_UPLOAD_PART,requestElapse:o,requestMethod:"DELETE",isSuccess:1,code:i}}),[3,4];case 3:return n=c.sent(),r=(null==n?void 0:n.extraInfo)||{},o=r.elapse,i=r.httpCode,a=r.errDesc,u=r.url,this.rpt({data:{requestUrl:u,requestName:s.TT.REST_ABORT_UPLOAD_PART,requestElapse:o,requestMethod:"DELETE",isSuccess:0,codeDesc:a,code:i}}),[3,4];case 4:return[2]}}))}))},e.prototype.handleTask=function(e){var t=this.pool.findIndex((function(t){return t===e}));this.pool.splice(t,1)},e}(),T=function(e){var t,n,o=this,i=(new Date).getTime(),a=e.apiUrl,u=e.orgName,c=e.appName,l=e.operateName,d=e.accessToken,f=e.conversationInfo,v=f.conversationType,E=f.conversationId,m=p[v],g=e.uploadUrl||"".concat(a,"/").concat(u,"/").concat(c,"/chatfiles?chat-type=").concat(m,"&chat-target=").concat(E),y=function(t){var n=(new Date).getTime()-i;o.dataReport&&l&&[s.jz.UPLOAD_MSG_ATTACH,s.jz.UPLOAD_CHATROOM_FILE,s.jz.UPLOAD_GROUP_FILE].includes(l)&&o.dataReport.geOperateFun({operationName:l})({isEndApi:!0,data:{isSuccess:0,requestMethod:"POST",requestName:l,requestElapse:n,requestUrl:g,code:(null==O?void 0:O.status)||0,codeDesc:"upload file error"}}),e.onFileUploadError&&e.onFileUploadError(t)};function _(e){y({type:r.C.WEBIM_UPLOADFILE_ERROR,data:O})}var O=new XMLHttpRequest;O.upload&&(null===(n=(t=O.upload).addEventListener)||void 0===n||n.call(t,"progress",e.onFileUploadProgress||I,!1)),O.addEventListener("abort",e.onFileUploadCanceled||I,!1),O.addEventListener("error",_,!1),O.addEventListener("load",(function(t){try{var n=JSON.parse(O.responseText);if(400===O.status)return y({type:r.C.WEBIM_UPLOADFILE_ERROR,data:n}),!1;try{!function(t){var n=(new Date).getTime()-i;o.dataReport&&l&&[s.jz.UPLOAD_MSG_ATTACH,s.jz.UPLOAD_CHATROOM_FILE,s.jz.UPLOAD_GROUP_FILE].includes(l)&&o.dataReport.geOperateFun({operationName:l})({isEndApi:!0,data:{isSuccess:(null==t?void 0:t.error)?0:1,requestMethod:"POST",requestName:l,requestElapse:n,requestUrl:g,code:O.status,codeDesc:(null==t?void 0:t.error_description)||""}}),e.onFileUploadComplete&&e.onFileUploadComplete(t)}(n)}catch(t){y({type:r.C.WEBIM_CONNCTION_CALLBACK_INNER_ERROR,data:t})}}catch(t){y({type:r.C.WEBIM_UPLOADFILE_ERROR,data:O.responseText})}}),!1),O.addEventListener("timeout",_,!1),O.timeout=h.Gc,O.open("POST",g),O.setRequestHeader("restrict-access","true"),O.setRequestHeader("Accept","*/*"),O.setRequestHeader("Authorization","Bearer "+d);var T=new FormData;T.append("file",e.file.data),e.thumbnailWidth&&T.append("thumbnail-width",e.thumbnailWidth+""),e.thumbnailHeight&&T.append("thumbnail-height",e.thumbnailHeight+""),O.send(T)},R=function(){return R=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},R.apply(this,arguments)},I=function(){};function C(e,t){var n,o=this;return U().platform===M.WEB?new Promise((function(t,o){var s=e.dataType||"text",a=e.success||I,c=e.error||I,l=new XMLHttpRequest;l.timeout=h._h;var d=!1;l.ontimeout=function(){u.vF.warn("request timeout"),d=!0;var e={type:r.C.REQUEST_TIMEOUT,message:"Request Timeout",errorType:"timeout_error",xhr:l};c(e),o(e)},l.onerror=function(){o({type:r.C.REQUEST_UNKNOWN,message:"Request Unknow Error",errorType:"onerror",xhr:l})},l.onabort=function(){o({type:r.C.REQUEST_ABORT,message:"Request Abort",errorType:"onabort",xhr:l})},l.onreadystatechange=function(){if(4===l.readyState){var e=(new Date).getTime()-n,p=l.status||0,h={elapse:e,httpCode:p};if(200===p){F.ajaxUnconventionalErrorTimes=0;try{switch(s){case"text":return a(l.responseText),void t(l.responseText);case"json":var f=JSON.parse(l.responseText);return f.extraInfo=h,a(f),void t(f);case"xml":return l.responseXML&&l.responseXML.documentElement?(a(l.responseXML.documentElement),void t(l.responseXML.documentElement)):(c({type:r.C.WEBIM_CONNCTION_AJAX_ERROR,data:l.responseText,message:"XHR.responseXML is null or XHR.responseXML.documentElement is null"}),void o({type:r.C.WEBIM_CONNCTION_AJAX_ERROR,data:l.responseText,message:"XHR.responseXML is null or XHR.responseXML.documentElement is null"}));default:c({type:r.C.WEBIM_CONNCTION_AJAX_ERROR,data:l.responseText,message:"Invalid dataType"}),o({type:r.C.WEBIM_CONNCTION_AJAX_ERROR,data:l.responseText,message:"Invalid dataType"})}return t(l.response||l.responseText),void a(l.response||l.responseText,l)}catch(e){o(e)}return}0===p?setTimeout((function(){u.vF.debug("request timeout:",d),!d&&i(l,o,c,e)}),0):([400,401,403,404,429,500,503].includes(p)||(u.vF.debug("rest api request fail status:",p),F.ajaxUnconventionalErrorTimes++),i(l,o,c,e))}0===l.readyState&&(c({type:r.C.WEBIM_CONNCTION_AJAX_ERROR,data:l.responseText,message:"Request not initialized"}),o({type:r.C.WEBIM_CONNCTION_AJAX_ERROR,data:l.responseText,message:"Request not initialized"}))},e.responseType&&l.responseType&&(l.responseType=e.responseType),e.mimeType&&l.overrideMimeType(e.mimeType);var p=e.type||"POST",f=e.data||null,v="";if("get"===p.toLowerCase()&&f){for(var E in f)f.hasOwnProperty(E)&&(v+=E+"="+f[E]+"&");v=v?v.slice(0,-1):v,e.url+=(e.url.indexOf("?")>0?"&":"?")+(v?v+"&":v)+"_v="+(new Date).getTime(),f=null,v=""}n=(new Date).getTime(),l.open(p,e.url);var m=e.headers||{};for(var g in m["Content-Type"]||(m["Content-Type"]="application/json"),m)m.hasOwnProperty(g)&&l.setRequestHeader(g,m[g]);l.send(f)})).then((function(n){var i,a;if(o.dataReport&&t&&t!==s.jz.SDK_INTERNAL){var u=o.dataReport.geOperateFun({operationName:t}),c=R({isSuccess:1,requestUrl:e.url,requestName:t,requestMethod:e.type},k(n.extraInfo));t===s.jz.REST_FETCH_CONVERSATIONS?c.resultCount=null===(i=n.data.channel_infos)||void 0===i?void 0:i.length:t===s.jz.REST_FETCHHISTORYMESSAGE&&(c.resultCount=null===(a=n.data.msgs)||void 0===a?void 0:a.length),u({isEndApi:!0,data:c})}return t===s.jz.SDK_INTERNAL&&(n.extraInfo.url=e.url),t!==s.jz.SDK_INTERNAL&&delete n.extraInfo,"Object"===S(n)?R(R({},n),{type:r.C.REQUEST_SUCCESS}):{data:n,type:r.C.REQUEST_SUCCESS}})).catch((function(r){var i,a;if(o.dataReport&&t&&t!==s.jz.SDK_INTERNAL&&o.dataReport.geOperateFun({operationName:t})({isEndApi:!0,data:R({isSuccess:0,requestUrl:e.url,requestName:t,requestMethod:e.type},k(r.extraInfo))}),t===s.jz.SDK_INTERNAL)if(r.extraInfo)r.extraInfo.url=e.url;else{var u={elapse:(new Date).getTime()-n,httpCode:null!==(a=null===(i=r.xhr)||void 0===i?void 0:i.status)&&void 0!==a?a:0,url:e.url};r.extraInfo=u}throw t!==s.jz.SDK_INTERNAL&&delete r.extraInfo,r})):N.call(this,e,t)}function S(e){return Object.prototype.toString.call(e).slice(8,-1)}function N(e,t){var n=this;return new Promise((function(n,o){var i=e.success||I,s=e.error||I,c=e.type||"POST",l=e.data||null,d="",p=(new Date).getTime(),f=F.getEnvInfo();if("get"===c.toLowerCase()&&l){for(var v in l)l.hasOwnProperty(v)&&(d+=v+"="+l[v]+"&");d=d?d.slice(0,-1):d,e.url+=(e.url.indexOf("?")>0?"&":"?")+(d?d+"&":d)+"_v="+(new Date).getTime(),l=null,d=""}var E={url:e.url,data:e.data,method:c,headers:{},timeout:h._h,success:function(e){var r,a,c,l,d,h={elapse:(new Date).getTime()-p,httpCode:Number((null===(r=e.statusCode)||void 0===r?void 0:r.toString())||(null===(a=e.status)||void 0===a?void 0:a.toString())),errDesc:(null===(c=null==e?void 0:e.data)||void 0===c?void 0:c.error_description)||""};if("200"===(null===(l=e.statusCode)||void 0===l?void 0:l.toString())||"200"===(null===(d=e.status)||void 0===d?void 0:d.toString())){e.data.extraInfo=h;var f=e.data;i(f),n(f)}else e.extraInfo=h,s(f=e),o(f),u.vF.debug(t,"reject reason: ",f)},complete:function(){},fail:function(e){var n={elapse:(new Date).getTime()-p,httpCode:a.XI,errDesc:"request:fail"};if(e.extraInfo=n,e.data={error:"request:fail",error_description:"request:fail"},"request:fail timeout"===e.errMsg)return o({type:r.C.REQUEST_TIMEOUT,message:"Request Timeout",extraInfo:n}),void s({type:r.C.REQUEST_TIMEOUT,message:"Request Timeout",extraInfo:n});s(e),o(e),u.vF.error(t,"fail reason:",e)}};if("zfb"===f.platform||"dd"===f.platform?E.headers=e.headers:E.header=e.headers,"dd"===f.platform)return dd.httpRequest(E);f.global.request(E)})).then((function(o){var i,a;if(n.dataReport&&t&&t!==s.jz.SDK_INTERNAL){var u=n.dataReport.geOperateFun({operationName:t}),c=R({isSuccess:1,requestUrl:e.url,requestName:t,requestMethod:e.type},k(o.extraInfo));t===s.jz.REST_FETCH_CONVERSATIONS?c.resultCount=null===(i=o.data.channel_infos)||void 0===i?void 0:i.length:t===s.jz.REST_FETCHHISTORYMESSAGE&&(c.resultCount=null===(a=o.data.msgs)||void 0===a?void 0:a.length),u({isEndApi:!0,data:c})}return t!==s.jz.SDK_INTERNAL&&delete o.extraInfo,"Object"===S(o)?R(R({},o),{type:r.C.REQUEST_SUCCESS}):{data:o,type:r.C.REQUEST_SUCCESS}})).catch((function(r){n.dataReport&&t&&t!==s.jz.SDK_INTERNAL&&n.dataReport.geOperateFun({operationName:t})({isEndApi:!0,data:R({isSuccess:0,requestUrl:e.url,requestName:t,requestMethod:e.type},k(r.extraInfo))}),t!==s.jz.SDK_INTERNAL&&delete r.extraInfo;var o=JSON.stringify(r.data||{}),a=R(R({},r),{status:r.statusCode||r.status||0,response:o,responseText:o});i(a,(function(e){throw R(R({},r),{message:e.message,type:e.type})}),I,0)}))}function A(){this.autoIncrement?this.autoIncrement++:this.autoIncrement=1;var e=new Date,t=new Date(2010,1,1);return(e.getTime()-t.getTime()+this.autoIncrement).toString()}function b(e,t){var n;return function(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];if(!e)return n;n=t?e.apply(t,r):e.apply(void 0,r),e=null}}var M,w=b((function(e,t){var n=U();if(n.platform!==M.WEB){var r=n.global,o=function(n){n.isConnected?e():t()};r.offNetworkStatusChange&&r.offNetworkStatusChange(o),r.onNetworkStatusChange&&r.onNetworkStatusChange(o)}else"undefined"!=typeof addEventListener&&(window.addEventListener("online",e),window.addEventListener("offline",t))})),L=b((function(e,t){var n,r,o=U();if(o.platform===M.WEB)document&&document.addEventListener("visibilitychange",(function(){document.hidden?null==t||t():null==e||e()}),!1);else{var i=o.global;i.onAppShow&&(null===(n=i.onAppShow)||void 0===n||n.call(i,e)),i.onAppHide&&(null===(r=i.onAppHide)||void 0===r||r.call(i,t))}}));function U(){return"undefined"!=typeof swan&&x(swan)?{platform:M.BAIDU,global:swan}:"undefined"!=typeof tt&&x(tt)?{platform:M.TT,global:tt}:"undefined"!=typeof dd&&x(dd)?{platform:M.DD,global:dd}:"undefined"!=typeof my&&x(my)?{platform:M.ZFB,global:my}:"undefined"!=typeof wx&&x(wx)||"undefined"!=typeof wx&&"function"==typeof wx.createCanvas?{platform:M.WX,global:wx}:"undefined"!=typeof qq&&x(qq)?{platform:M.QQ,global:qq}:"undefined"!=typeof uni&&x(uni)?{platform:M.UNI,global:uni}:"undefined"!=typeof window&&window.WebSocket?{platform:M.WEB,global:window}:{platform:M.NODE,global:n.g||{}}}function x(e){for(var t=["canIUse","getSystemInfo"],n=0,r=t.length;n<r;n++)if(!e[t[n]])return!1;return!0}function D(e,t){var n,o,i,s=this,a=e.accessToken,u=e.appKey,c=null===(o=null===(n=null==e?void 0:e.file)||void 0===n?void 0:n.data)||void 0===o?void 0:o.size,p=[],h="",f="";if(a)if(u&&(p=u.split("#"),h=p[0],f=p[1]),h||f)if(c<=0)e.onFileUploadError&&e.onFileUploadError({type:r.C.WEBIM_UPLOADFILE_ERROR,message:"fileSize must be greater than 0"});else if(e.uploadUrl)T.call(this,R(R({},e),{orgName:h,appName:f,operateName:t,conversationInfo:{conversationId:e.to,conversationType:e.chatType}}));else{var v=d.size||l;this.uploadPartEnable&&c>1.5*v?new O(this,{file:null===(i=null==e?void 0:e.file)||void 0===i?void 0:i.data,onFileUploadProgress:e.onFileUploadProgress||I,onFileUploadComplete:e.onFileUploadComplete||I,onFileUploadError:e.onFileUploadError||I,onFileUploadCanceled:e.onFileUploadCanceled||I,conversationInfo:{conversationId:e.to,conversationType:e.chatType},onInitFail:function(){T.call(s,R(R({},e),{orgName:h,appName:f,operateName:t,conversationInfo:{conversationId:e.to,conversationType:e.chatType}}))},thumbnailInfo:{width:e.thumbnailWidth,height:e.thumbnailHeight}}):T.call(this,R(R({},e),{orgName:h,appName:f,operateName:t,conversationInfo:{conversationId:e.to,conversationType:e.chatType}}))}else e.onFileUploadError&&e.onFileUploadError({type:r.C.WEBIM_UPLOADFILE_ERROR,message:this._initWithAppId?"appId illegal":"AppKey illegal"});else e.onFileUploadError&&e.onFileUploadError({type:r.C.WEBIM_UPLOADFILE_NO_LOGIN,message:"AccessToken cannot be empty"})}function P(e,t){var n;e.onFileDownloadComplete=e.onFileDownloadComplete||I,e.onFileDownloadError=e.onFileDownloadError||I;var o=(new Date).getTime(),i=new XMLHttpRequest,a=this;i.addEventListener("load",(function(){var n,c=(new Date).getTime()-o;a.dataReport&&t&&t===s.jz.DOWN_GROUP_FILE&&a.dataReport.geOperateFun({operationName:t})({isEndApi:!0,data:{isSuccess:200===i.status?1:0,requestMethod:e.method||"GET",requestName:t,requestElapse:c,requestUrl:null==e?void 0:e.url,code:i.status,codeDesc:200===i.status?"":"download file error"}}),200===i.status?e.onFileDownloadComplete&&e.onFileDownloadComplete(i.response):403===i.status||404===i.status?((n=new FileReader).addEventListener("loadend",(function(t){var n;try{var o=JSON.parse(null===(n=t.target)||void 0===n?void 0:n.result);u.vF.error("download file failed","status:".concat(i.status),o),403===i.status?"chatfile no permission"===o.error_description?e.onFileDownloadError&&e.onFileDownloadError({type:r.C.WEBIM_DOWNLOADFILE_ERROR_NO_PERMISSION,id:e.id,xhr:i}):e.onFileDownloadError&&e.onFileDownloadError({type:r.C.PERMISSION_DENIED,id:e.id,xhr:i}):404===i.status&&("file_expired"===o.error?e.onFileDownloadError&&e.onFileDownloadError({type:r.C.WEBIM_DOWNLOADFILE_ERROR_EXPIRED,id:e.id,xhr:i}):e.onFileDownloadError&&e.onFileDownloadError({type:r.C.WEBIM_DOWNLOADFILE_ERROR,id:e.id,xhr:i}))}catch(t){u.vF.error("Error parsing download error response:",t),e.onFileDownloadError&&e.onFileDownloadError({type:r.C.WEBIM_DOWNLOADFILE_ERROR,id:e.id,xhr:i})}})),n.readAsText(i.response)):500===i.status?(u.vF.error("download file failed","status:".concat(i.status),"type:".concat(r.C.SERVER_UNKNOWN_ERROR)),e.onFileDownloadError&&e.onFileDownloadError({type:r.C.SERVER_UNKNOWN_ERROR,id:e.id,xhr:i})):(u.vF.error("download file failed","status:".concat(i.status),"type:".concat(r.C.WEBIM_DOWNLOADFILE_ERROR)),e.onFileDownloadError&&e.onFileDownloadError({type:r.C.WEBIM_DOWNLOADFILE_ERROR,id:e.id,xhr:i}))}),!1),i.addEventListener("error",(function(n){var u=(new Date).getTime()-o;a.dataReport&&t&&t===s.jz.DOWN_GROUP_FILE&&a.dataReport.geOperateFun({operationName:t})({isEndApi:!0,data:{isSuccess:0,requestMethod:e.method||"GET",requestName:t,requestElapse:u,requestUrl:null==e?void 0:e.url,code:(null==i?void 0:i.status)||0,codeDesc:"download file error"}}),e.onFileDownloadError&&e.onFileDownloadError({type:r.C.WEBIM_DOWNLOADFILE_ERROR,id:e.id,xhr:i})}),!1);var c=e.method||"GET",l=e.responseType||"blob",d=e.mimeType||"text/plain; charset=x-user-defined";i.open(c,e.url),"undefined"!=typeof Blob?i.responseType=l:i.overrideMimeType(d);var p={"X-Requested-With":"XMLHttpRequest","share-secret":e.secret,Authorization:"Bearer "+(null===(n=null==this?void 0:this.context)||void 0===n?void 0:n.accessToken)},h=e.headers||{};for(var f in h)p[f]=h[f];for(var f in p)p[f]&&i.setRequestHeader(f,p[f]);i.send(null)}function k(e){void 0===e&&(e={});var t=e.elapse,n=void 0===t?0:t,r=e.httpCode,o=void 0===r?0:r,i=e.errDesc;return{requestElapse:n,code:o,codeDesc:void 0===i?"":i}}!function(e){e.WEB="web",e.WX="wx",e.QQ="qq",e.ZFB="zfb",e.DD="dd",e.TT="tt",e.BAIDU="baidu",e.QUICK_APP="quick_app",e.UNI="uni",e.NODE="node"}(M||(M={}));var H="localDeviceInfo",F={autoIncrement:0,ajaxUnconventionalErrorTimes:0,isUseHttps:function(e){var t;return"boolean"==typeof e?e:!("undefined"!=typeof window)||"https:"===(null===(t=window.location)||void 0===t?void 0:t.protocol)},getRetryDelay:function(e){return e<=5?Number((Math.random()+1).toFixed(2)):e>5&&e<20?Math.floor(4*Math.random())+3:Math.floor(6*Math.random())+5},ajax:C,getUniqueId:A,getFileUrl:function(e){var t={url:"",filename:"",filetype:"",data:{}},n="string"==typeof e?document.getElementById(e):e;if(window.URL.createObjectURL){if(!n.files)throw Error("this is not HTMLInputElement");var r=n.files;if(r.length>0){var o=r.item(0);t.data=o,t.url=window.URL.createObjectURL(o),t.filename=(null==o?void 0:o.name)||""}}else{if("string"!=typeof e)throw Error("in IE fileInputId must be string");o=document.getElementById(e).value,t.url=o;var i=o.lastIndexOf("/"),s=o.lastIndexOf("\\"),a=Math.max(i,s);t.filename=a<0?o:o.substring(a+1)}var u=t.filename.lastIndexOf(".");return-1!==u&&(t.filetype=t.filename.substring(u+1).toLowerCase()),t},uploadFile:D,flow:function(e){for(var t=e.length,n=t;n--;)if("function"!=typeof e[n])throw new TypeError("Expected a function");return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];for(var o=0,i=t?e[o].apply(this,n):n[0];++o<t;)i=e[o].call(this,i);return i}},listenNetwork:w,listenBrowserVisibility:L,getEnvInfo:U,wxRequest:N,parseDownloadResponse:function(e){if(!window||!window.URL)throw Error("parseDownloadResponse can be used in broswer only");return e&&e.type&&"application/json"===e.type||0>Object.prototype.toString.call(e).indexOf("Blob")?this.url+"?token=":window.URL.createObjectURL(e)},download:P,parseNotify:function(e){for(var t="",n=0;n<e.length;n++)t+="%"+e[n].toString(16);return JSON.parse(decodeURIComponent(t))},getExtraData:k,retryPromise:function(e,t,n){return new Promise((function(r,o){var i=function(t){e().then(r).catch((function(e){t>0?setTimeout((function(){i(t-1)}),n||1e3):o(e)}))};i(t||3)}))},formatAttachUrl:function(e){return e&&"string"==typeof e?"".concat(this.apiUrl).concat(e.slice(e.indexOf("/",9))):""},Uint8ArrayToString:function(e){for(var t="",n=0;n<e.length;n++)t+=String.fromCharCode(e[n]);return t},getLocalDeviceInfo:function(){return function(e){var t,n=F.getEnvInfo(),r=n.platform,o="";r!==M.NODE&&r!==M.QUICK_APP||(o="");try{r===M.WEB?o=localStorage.getItem(e)||"":r===M.WX||r===M.QQ||r===M.TT||r===M.BAIDU||r===M.UNI?o=n.global.getStorageSync(e)||"":r!==M.ZFB&&r!==M.DD||(o=(null===(t=n.global.getStorageSync({key:e}))||void 0===t?void 0:t.data)||"")}catch(t){u.vF.debug("get local ".concat(e," failed: "),t)}return u.vF.debug("".concat(e," "),o),o}(H)},setLocalDeviceInfo:function(e){!function(e,t){var n=F.getEnvInfo(),r=n.platform;if(r!==M.NODE&&r!==M.QUICK_APP)if(r===M.WEB)try{localStorage.setItem(e,t)}catch(t){u.vF.error("set local ".concat(e," failed: "),t)}else n.global.setStorage({key:e,data:t,success:function(t){u.vF.debug("set local ".concat(e," success: "),t)},fail:function(t){u.vF.error("set local ".concat(e," failed: "),t)}})}(H,e)},detectBrowser:function(){if("undefined"==typeof navigator)return"unknown";var e=navigator.userAgent;return/MicroMessenger/i.test(e)?"WeChat":/QQBrowser/i.test(e)?"QQ":!/Chrome/i.test(e)||/Edg/i.test(e)||/OPR/i.test(e)?!/Safari/i.test(e)||/Chrome/i.test(e)||/CriOS/i.test(e)?/Firefox/i.test(e)?"Firefox":/MSIE/i.test(e)||/Trident/i.test(e)?"IE":/Edg/i.test(e)?"Edge":"unknown":"Safari":"Chrome"},getDevicePlatform:function(e){return e.platform===M.WX&&"undefined"!=typeof uni&&x(uni)?M.UNI:e.platform},delay:function(e){return new Promise((function(t){return setTimeout(t,e)}))}}},8686:function(e,t,n){"use strict";var r=n(3724),o=n(9039);e.exports=r&&o((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},8706:function(e,t,n){"use strict";var r=n(6518),o=n(9039),i=n(4376),s=n(34),a=n(8981),u=n(6198),c=n(6837),l=n(4659),d=n(1469),p=n(597),h=n(8227),f=n(9519),v=h("isConcatSpreadable"),E=f>=51||!o((function(){var e=[];return e[v]=!1,e.concat()[0]!==e})),m=function(e){if(!s(e))return!1;var t=e[v];return void 0!==t?!!t:i(e)};r({target:"Array",proto:!0,arity:1,forced:!E||!p("concat")},{concat:function(e){var t,n,r,o,i,s=a(this),p=d(s,0),h=0;for(t=-1,r=arguments.length;t<r;t++)if(m(i=-1===t?s:arguments[t]))for(o=u(i),c(h+o),n=0;n<o;n++,h++)n in i&&l(p,h,i[n]);else c(h+1),l(p,h++,i);return p.length=h,p}})},8727:function(e){"use strict";e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},8745:function(e,t,n){"use strict";var r=n(616),o=Function.prototype,i=o.apply,s=o.call;e.exports="object"==typeof Reflect&&Reflect.apply||(r?s.bind(i):function(){return s.apply(i,arguments)})},8747:function(e,t,n){"use strict";var r=n(4644),o=r.aTypedArray,i=r.exportTypedArrayMethod,s=Math.floor;i("reverse",(function(){for(var e,t=this,n=o(t).length,r=s(n/2),i=0;i<r;)e=t[i],t[i++]=t[--n],t[n]=e;return t}))},8773:function(e,t){"use strict";var n={}.propertyIsEnumerable,r=Object.getOwnPropertyDescriptor,o=r&&!n.call({1:2},1);t.f=o?function(e){var t=r(this,e);return!!t&&t.enumerable}:n},8781:function(e,t,n){"use strict";var r=n(350).PROPER,o=n(6840),i=n(8551),s=n(655),a=n(9039),u=n(1034),c="toString",l=RegExp.prototype,d=l[c],p=a((function(){return"/a/b"!==d.call({source:"a",flags:"b"})})),h=r&&d.name!==c;(p||h)&&o(l,c,(function(){var e=i(this);return"/"+s(e.source)+"/"+s(u(e))}),{unsafe:!0})},8801:function(e,t,n){"use strict";n.d(t,{Q:function(){return r},zK:function(){return g}}),n(2675),n(9463),n(2259),n(8706),n(1629),n(4423),n(3792),n(739),n(3288),n(2010),n(2892),n(9085),n(9432),n(6099),n(3362),n(8781),n(1699),n(7764),n(3500),n(2953);var r,o=n(8570),i=n.n(o),s=n(565),a=n(8678),u=n(8434),c=n(564),l=n(2056),d=n(7517),p=n(8232),h=function(){return h=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},h.apply(this,arguments)},f=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{u(r.next(e))}catch(e){i(e)}}function a(e){try{u(r.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}u((r=r.apply(e,t||[])).next())}))},v=function(e,t){var n,r,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,r=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!((o=(o=s.trys).length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=t.call(e,s)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}};!function(e){e["chat:user"]="singleChat",e["chat:group"]="groupChat",e["chat:room"]="chatRoom"}(r||(r={}));var E={0:"TEXT",1:"IMAGE",2:"VIDEO",3:"LOCATION",4:"VOICE",5:"FILE",6:"COMMAND",7:"CUSTOM",8:"COMBINE"};function m(e){for(var t={},n=0;n<e.length;n++)if(8===e[n].type)t[e[n].key]=JSON.parse(e[n].stringValue);else if(7===e[n].type)t[e[n].key]=e[n].stringValue;else if(6===e[n].type)t[e[n].key]=e[n].doubleValue;else if(5===e[n].type)t[e[n].key]=e[n].floatValue;else if(1===e[n].type){var r=e[n].varintValue,o=new(i())(r.low,r.high,r.unsigned).toString();t[e[n].key]=0!==Number(o)}else 2!==e[n].type&&3!==e[n].type&&4!==e[n].type||(r=e[n].varintValue,o=new(i())(r.low,r.high,r.unsigned).toString(),t[e[n].key]=Number(o));return t}function g(e){var t="";return e.remotePath&&(t=a.Wp.formatAttachUrl.call(this,e.remotePath),e.remotePath.includes("?em-redirect")||(t="".concat(t,"?em-redirect=true"),e.secretKey&&(t="".concat(t,"&share-secret=").concat(e.secretKey)))),t}function y(e,t){var n,r,o=t.from&&t.from.name;if(o===this.context.userId){var i=null===(n=null==e?void 0:e.from)||void 0===n?void 0:n.clientResource;o===(null===(r=null==t?void 0:t.to)||void 0===r?void 0:r.name)&&i&&i!==this.clientResource&&(o="".concat(o,"/").concat(i))}return o}function _(e,t){var n,r=t.to&&t.to.name;return(null===(n=null==e?void 0:e.to)||void 0===n?void 0:n.clientResource)&&(r="".concat(r,"/").concat(e.to.clientResource)),r}function O(e){var t,n,r,o,i,u,c,d,h,y,_,O,T,R,I,C,S,N,A,b,M,w,L,U,x,D,P;return f(this,void 0,void 0,(function(){var f,k,H,F,B,G,W,j,q,K,V,z,J,Y,X,Q,$,Z,ee,te,ne,re,oe,ie,se,ae,ue,ce,le,de,pe,he,fe,ve,Ee,me,ge,ye,_e,Oe,Te,Re,Ie,Ce,Se,Ne,Ae,be,Me,we;return v(this,(function(v){switch(v.label){case 0:switch(f=e.status,k=e.thirdMessage,H=e.msgBody,F=e.msgId,B=e.type,G=e.from,W=e.to,j=e.time,q=e.onlineState,K=e.chatType,V=e.ignoreCallback,z=e.priority,J=e.format,Y=e.broadcast,X=void 0!==Y&&Y,Q=e.isContentReplaced,$=void 0!==Q&&Q,Z={},ee={},te=f.errorCode>0,ne=f.errorCode,re=f.reason,oe={},ie=[],se=[],ae={},ue=null,ce=null,de=!1,pe=!1,k.ext&&(oe=m(k.ext)),k.meta&&"string"==typeof k.meta&&((he=JSON.parse(k.meta)).reaction&&(ie=he.reaction).forEach((function(e){e.isAddedBySelf=e.state,delete e.state})),he.translations&&(se=he.translations),he.edit_msg&&(fe=he.edit_msg,ve=fe.count,Ee=fe.operator,me=fe.edit_time,ae={operationTime:me,operatorId:Ee,operationCount:ve}),he.thread&&"{}"!==JSON.stringify(he.thread)&&(ue={messageId:he.thread.msg_parent_id,parentId:he.thread.muc_parent_id,chatThreadName:he.thread.thread_name}),he.thread_overview&&"{}"!==JSON.stringify(he.thread_overview)&&(ce={id:he.thread_overview.id,parentId:he.thread_overview.muc_parent_id,name:he.thread_overview.name,lastMessage:he.thread_overview.last_message&&"{}"!==JSON.stringify(he.thread_overview.last_message)?s.h.call(this,he.thread_overview.last_message):null,createTimestamp:he.thread_overview.create_timestamp,updateTimestamp:he.thread_overview.update_timestamp,messageCount:he.thread_overview.message_count||0}),he.isDelivered&&(de=!0),he.isRead&&(pe=!0)),H.type){case 0:return[3,1];case 1:return[3,7];case 2:return[3,10];case 3:return[3,13];case 4:return[3,16];case 5:return[3,19];case 6:return[3,22];case 7:return[3,23];case 8:return[3,26]}return[3,27];case 1:return Object.prototype.hasOwnProperty.call(H,"subType")&&0===H.subType?(le=g.call(this,H),ge={id:F,type:"combine",chatType:K,to:W,from:G,ext:oe,time:Number(j),onlineState:q,title:H.title||"",summary:H.summary||"",url:le||"",secret:H.secretKey||"",file_length:H.fileLength||0,filename:H.displayName||"",compatibleText:H.text,combineLevel:H.combineLevel||0},Z.msgConfig&&(ge.msgConfig=Z.msgConfig),ie.length>0&&(ge.reactions=ie),ue&&(ge.chatThread=ue),ce&&(ge.chatThreadOverview=ce),"chatRoom"===K&&(ge.priority=z,ge.broadcast=X),ae.operationCount>0&&(ge.modifiedInfo=ae),$&&(ge.isContentReplaced=$),pe&&(ge.isRead=!0),de&&(ge.isDelivered=!0),ee=ge,V?[3,3]:[4,null===(n=null===(t=this._localCache)||void 0===t?void 0:t.getInstance())||void 0===n?void 0:n.storeMessage(ge,p.q.SUCCESS)]):[3,4];case 2:v.sent(),null===(r=this.eventHandler)||void 0===r||r.dispatch("onCombineMessage",ge),v.label=3;case 3:return[3,28];case 4:return!(Z={id:F,type:B,contentsType:E[H.type],from:G,to:W,data:H.text,ext:oe,sourceMsg:H.text,time:j,msgConfig:k.msgConfig,onlineState:q}).msgConfig&&delete k.msgConfig,Z.error=te,Z.errorText=re,Z.errorCode=ne,!V&&this.onTextMessage&&this.onTextMessage(Z),ye={id:F,type:"txt",chatType:K,msg:H.text,to:W,from:G,ext:oe,time:Number(j),onlineState:q},Z.msgConfig&&(ye.msgConfig=Z.msgConfig),ie.length>0&&(ye.reactions=ie),ue&&(ye.chatThread=ue),ce&&(ye.chatThreadOverview=ce),se.length>0&&(ye.translations=se),ae.operationCount>0&&(ye.modifiedInfo=ae),"chatRoom"===K&&(ye.priority=z,ye.broadcast=X),$&&(ye.isContentReplaced=$),pe&&(ye.isRead=!0),de&&(ye.isDelivered=!0),ee=ye,V?[3,6]:[4,null===(i=null===(o=this._localCache)||void 0===o?void 0:o.getInstance())||void 0===i?void 0:i.storeMessage(ye,p.q.SUCCESS)];case 5:v.sent(),null===(u=this.eventHandler)||void 0===u||u.dispatch("onTextMessage",ye),v.label=6;case 6:return[3,28];case 7:return _e=(null===(c=null==H?void 0:H.size)||void 0===c?void 0:c.width)||0,Oe=(null===(d=null==H?void 0:H.size)||void 0===d?void 0:d.height)||0,le=this.useOwnUploadFun?H.remotePath:g.call(this,H),!(Z={id:F,type:B,contentsType:E[H.type],from:G,to:W,url:le,secret:H.secretKey,filename:H.displayName,thumb:this.useOwnUploadFun?"":"".concat(le,"&thumbnail=true"),thumb_secret:H.secretKey,file_length:H.fileLength||"",width:_e,height:Oe,filetype:H.filetype||"",accessToken:this.token,ext:oe,time:j,msgConfig:k.msgConfig,onlineState:q}).delay&&delete Z.delay,!Z.msgConfig&&delete k.msgConfig,Z.error=te,Z.errorText=re,Z.errorCode=ne,!V&&this.onPictureMessage&&this.onPictureMessage(Z),Te={id:F,type:"img",chatType:K,from:G,to:W,url:le||"",width:_e,height:Oe,secret:H.secretKey||"",thumb:this.useOwnUploadFun?"":"".concat(le,"&thumbnail=true"),thumb_secret:H.secretKey,file_length:H.fileLength||0,ext:oe,time:Number(j),onlineState:q},Z.msgConfig&&(Te.msgConfig=Z.msgConfig),ie.length>0&&(Te.reactions=ie),ue&&(Te.chatThread=ue),ce&&(Te.chatThreadOverview=ce),"chatRoom"===K&&(Te.priority=z,Te.broadcast=X),ae.operationCount>0&&(Te.modifiedInfo=ae),$&&(Te.isContentReplaced=$),pe&&(Te.isRead=!0),de&&(Te.isDelivered=!0),Object.prototype.hasOwnProperty.call(H,"subType")&&1===H.subType&&(Te.isGif=!0),ee=Te,V?[3,9]:[4,null===(y=null===(h=this._localCache)||void 0===h?void 0:h.getInstance())||void 0===y?void 0:y.storeMessage(Te,p.q.SUCCESS)];case 8:v.sent(),null===(_=this.eventHandler)||void 0===_||_.dispatch("onImageMessage",Te),v.label=9;case 9:return[3,28];case 10:return le=this.useOwnUploadFun?H.remotePath:g.call(this,H),!(Z={id:F,type:B,contentsType:E[H.type],from:G,to:W,url:le,secret:H.secretKey,filename:H.displayName,length:H.duration||"",file_length:H.fileLength||"",filetype:H.filetype||"",accessToken:this.token||"",ext:oe,time:j,msgConfig:k.msgConfig,onlineState:q}).delay&&delete Z.delay,!Z.msgConfig&&delete k.msgConfig,Z.error=te,Z.errorText=re,Z.errorCode=ne,!V&&this.onVideoMessage&&this.onVideoMessage(Z),Re={id:F,type:"video",chatType:K,from:G,to:W,url:le,secret:H.secretKey,thumb:a.Wp.formatAttachUrl.call(this,H.thumbnailRemotePath),thumb_secret:H.thumbnailSecretKey,filename:H.displayName,length:H.duration||0,file:{},file_length:H.fileLength||0,filetype:H.filetype||"",accessToken:this.token||"",ext:oe,time:Number(j),onlineState:q},Z.msgConfig&&(Re.msgConfig=Z.msgConfig),ie.length>0&&(Re.reactions=ie),ue&&(Re.chatThread=ue),ce&&(Re.chatThreadOverview=ce),"chatRoom"===K&&(Re.priority=z,Re.broadcast=X),ae.operationCount>0&&(Re.modifiedInfo=ae),$&&(Re.isContentReplaced=$),pe&&(Re.isRead=!0),de&&(Re.isDelivered=!0),ee=Re,V?[3,12]:[4,null===(T=null===(O=this._localCache)||void 0===O?void 0:O.getInstance())||void 0===T?void 0:T.storeMessage(Re,p.q.SUCCESS)];case 11:v.sent(),null===(R=this.eventHandler)||void 0===R||R.dispatch("onVideoMessage",Re),v.label=12;case 12:return[3,28];case 13:return!(Z={id:F,type:B,contentsType:E[H.type],from:G,to:W,addr:H.address,buildingName:H.buildingName,lat:H.latitude,lng:H.longitude,ext:oe,time:j,msgConfig:k.msgConfig,onlineState:q}).delay&&delete Z.delay,!Z.msgConfig&&delete k.msgConfig,Z.error=te,Z.errorText=re,Z.errorCode=ne,!V&&this.onLocationMessage&&this.onLocationMessage(Z),Ie={id:F,type:"loc",chatType:K,from:G,to:W,buildingName:H.buildingName,addr:H.address,lat:H.latitude,lng:H.longitude,ext:oe,time:Number(j),onlineState:q},Z.msgConfig&&(Ie.msgConfig=Z.msgConfig),ie.length>0&&(Ie.reactions=ie),ue&&(Ie.chatThread=ue),ce&&(Ie.chatThreadOverview=ce),"chatRoom"===K&&(Ie.priority=z,Ie.broadcast=X),ae.operationCount>0&&(Ie.modifiedInfo=ae),$&&(Ie.isContentReplaced=$),pe&&(Ie.isRead=!0),de&&(Ie.isDelivered=!0),ee=Ie,V?[3,15]:[4,null===(C=null===(I=this._localCache)||void 0===I?void 0:I.getInstance())||void 0===C?void 0:C.storeMessage(Ie,p.q.SUCCESS)];case 14:v.sent(),null===(S=this.eventHandler)||void 0===S||S.dispatch("onLocationMessage",Ie),v.label=15;case 15:return[3,28];case 16:return le=this.useOwnUploadFun?H.remotePath:g.call(this,H),!(Z={id:F,type:B,contentsType:E[H.type],from:G,to:W,url:le,secret:H.secretKey,filename:H.displayName,file_length:H.fileLength||"",accessToken:this.token||"",ext:oe,length:H.duration,time:j,msgConfig:k.msgConfig,onlineState:q}).delay&&delete Z.delay,!Z.msgConfig&&delete k.msgConfig,Z.error=te,Z.errorText=re,Z.errorCode=ne,!V&&this.onAudioMessage&&this.onAudioMessage(Z),Ce={id:F,type:"audio",chatType:K,from:G,to:W,url:le,secret:H.secretKey,file:{},filename:H.displayName,length:H.duration||0,file_length:H.fileLength||0,filetype:H.filetype||"",accessToken:this.token||"",ext:oe,time:Number(j),onlineState:q},Z.msgConfig&&(Ce.msgConfig=Z.msgConfig),ie.length>0&&(Ce.reactions=ie),ue&&(Ce.chatThread=ue),ce&&(Ce.chatThreadOverview=ce),"chatRoom"===K&&(Ce.priority=z,Ce.broadcast=X),ae.operationCount>0&&(Ce.modifiedInfo=ae),$&&(Ce.isContentReplaced=$),pe&&(Ce.isRead=!0),de&&(Ce.isDelivered=!0),ee=Ce,V?[3,18]:[4,null===(A=null===(N=this._localCache)||void 0===N?void 0:N.getInstance())||void 0===A?void 0:A.storeMessage(Ce,p.q.SUCCESS)];case 17:v.sent(),null===(b=this.eventHandler)||void 0===b||b.dispatch("onAudioMessage",Ce),v.label=18;case 18:return[3,28];case 19:return le=this.useOwnUploadFun?H.remotePath:g.call(this,H),!(Z={id:F,type:B,contentsType:E[H.type],from:G,to:W,url:le,secret:H.secretKey,filename:H.displayName,file_length:H.fileLength,accessToken:this.token||"",ext:oe,time:j,msgConfig:k.msgConfig,onlineState:q}).delay&&delete Z.delay,!Z.msgConfig&&delete k.msgConfig,Z.error=te,Z.errorText=re,Z.errorCode=ne,!V&&this.onFileMessage&&this.onFileMessage(Z),Se={id:F,type:"file",chatType:K,from:G,to:W,url:le,secret:H.secretKey,file:{},filename:H.displayName,length:H.duration||0,file_length:H.fileLength||0,filetype:H.filetype||"",accessToken:this.token||"",ext:oe,time:Number(j),onlineState:q},Z.msgConfig&&(Se.msgConfig=Z.msgConfig),ie.length>0&&(Se.reactions=ie),ue&&(Se.chatThread=ue),ce&&(Se.chatThreadOverview=ce),"chatRoom"===K&&(Se.priority=z,Se.broadcast=X),ae.operationCount>0&&(Se.modifiedInfo=ae),$&&(Se.isContentReplaced=$),pe&&(Se.isRead=!0),de&&(Se.isDelivered=!0),ee=Se,V?[3,21]:[4,null===(w=null===(M=this._localCache)||void 0===M?void 0:M.getInstance())||void 0===w?void 0:w.storeMessage(Se,p.q.SUCCESS)];case 20:v.sent(),null===(L=this.eventHandler)||void 0===L||L.dispatch("onFileMessage",Se),v.label=21;case 21:return[3,28];case 22:return!(Z={id:F,type:B,contentsType:E[H.type],from:G,to:W,action:H.action,ext:oe,time:j,msgConfig:k.msgConfig,onlineState:q}).msgConfig&&delete k.msgConfig,Z.error=te,Z.errorText=re,Z.errorCode=ne,!V&&this.onCmdMessage&&this.onCmdMessage(Z),Ne={id:F,type:"cmd",chatType:K,from:G,to:W,action:H.action,ext:oe,time:Number(j),onlineState:q},Z.msgConfig&&(Ne.msgConfig=Z.msgConfig),ie.length>0&&(Ne.reactions=ie),ue&&(Ne.chatThread=ue),ce&&(Ne.chatThreadOverview=ce),"chatRoom"===K&&(Ne.priority=z,Ne.broadcast=X),ae.operationCount>0&&(Ne.modifiedInfo=ae),$&&(Ne.isContentReplaced=$),pe&&(Ne.isRead=!0),de&&(Ne.isDelivered=!0),ee=Ne,V||null===(U=this.eventHandler)||void 0===U||U.dispatch("onCmdMessage",Ne),[3,28];case 23:return Ae={},be={},k.contents[0].customExts&&(Ae=m(k.contents[0].customExts)),k.contents[0].params&&(be=m(k.contents[0].params)),Z={id:F,type:B,contentsType:E[H.type],from:G,to:W,customEvent:H.customEvent,params:be,customExts:Ae,ext:oe,time:j,onlineState:q},!V&&this.onCustomMessage&&this.onCustomMessage(Z),Me={id:F,type:"custom",chatType:K,from:G,to:W,customEvent:H.customEvent,params:be,customExts:Ae,ext:oe,time:Number(j),onlineState:q},Z.msgConfig&&(Me.msgConfig=Z.msgConfig),ie.length>0&&(Me.reactions=ie),ue&&(Me.chatThread=ue),ce&&(Me.chatThreadOverview=ce),ae.operationCount>0&&(Me.modifiedInfo=ae),"chatRoom"===K&&(Me.priority=z,Me.broadcast=X),$&&(Me.isContentReplaced=$),pe&&(Me.isRead=!0),de&&(Me.isDelivered=!0),ee=Me,V?[3,25]:[4,null===(D=null===(x=this._localCache)||void 0===x?void 0:x.getInstance())||void 0===D?void 0:D.storeMessage(Me,p.q.SUCCESS)];case 24:v.sent(),null===(P=this.eventHandler)||void 0===P||P.dispatch("onCustomMessage",Me),v.label=25;case 25:return[3,28];case 26:return le=g.call(this,H),we={id:F,type:"combine",chatType:K,to:W,from:G,ext:oe,time:Number(j),onlineState:q,title:H.title||"",summary:H.summary||"",url:le||"",secret:H.secretKey||"",file_length:H.fileLength||0,filename:H.displayName||"",compatibleText:H.text,combineLevel:H.combineLevel||0},Z.msgConfig&&(we.msgConfig=Z.msgConfig),ie.length>0&&(we.reactions=ie),ue&&(we.chatThread=ue),ce&&(we.chatThreadOverview=ce),"chatRoom"===K&&(we.priority=z,we.broadcast=X),ae.operationCount>0&&(we.modifiedInfo=ae),$&&(we.isContentReplaced=$),pe&&(we.isRead=!0),de&&(we.isDelivered=!0),ee=we,!V&&this.eventHandler&&this.eventHandler.dispatch("onCombineMessage",we),[3,28];case 27:return l.vF.error("Unknow message type, message:",H),[3,28];case 28:return J?[2,ee]:[2,Z]}}))}))}function T(e,t,n){if(this.delivery&&e!==t){var r=this.getUniqueId(),o=new d.Message("delivery",r);o.set({ackId:n,to:e}),l.vF.debug("send delivery ack"),this.send(o.body)}}t.Ay=function(e,t,n,o,s){var d,p,E,g,R,I,C,S,N,A,b,M,w,L,U,x,D,P,k,H,F,B,G,W,j,q,K,V;return f(this,void 0,void 0,(function(){var f,z,J,Y,X,Q,$,Z,ee,te,ne,re,oe,ie,se,ae,ue,ce,le,de,pe,he,fe,ve,Ee,me,ge,ye,_e,Oe,Te,Re,Ie,Ce,Se,Ne,Ae,be,Me,we,Le,Ue;return v(this,(function(v){switch(v.label){case 0:if(f=new(i())(e.timestamp.low,e.timestamp.high,e.timestamp.unsigned).toString(),z=this.root.lookup("easemob.pb.MessageBody"),J=z.decode(e.payload),Y=3,X=!1,Q=new(i())(e.id.low,e.id.high,e.id.unsigned).toString(),s&&e.from&&e.from.name===this.context.userId&&e.from.clientResource===this.clientResource&&J.type===u.up.CHATROOM)return[2,l.vF.debug("Discard your own chat room message:",Q)];if(e.meta&&e.meta.length){if($=a.Wp.parseNotify(e.meta),Z=$.is_online,ee=$.callback_replace,this.useReplacedMessageContents&&ee&&(X=!0),Z||0===Z)switch(Z){case 0:Y=0;break;case 1:Y=1;break;default:Y=2}}else Y=3;switch(te=J.ackMessageId?new(i())(J.ackMessageId.low,J.ackMessageId.high,J.ackMessageId.unsigned).toString():"",ne="",re=y.call(this,e,J),oe=_.call(this,e,J),l.vF.debug("meta thirdMessage:",{metaId:Q,metaNs:e.ns,type:J.type,from:re,to:oe,contentType:null===(p=null===(d=J.contents)||void 0===d?void 0:d[0])||void 0===p?void 0:p.type,contentLen:null===(E=J.contents)||void 0===E?void 0:E.length}),J.type){case u.up.SINGLECHAT:return[3,1];case u.up.GROUPCHAT:return[3,2];case u.up.CHATROOM:return[3,3];case u.up.READ_ACK:return[3,4];case u.up.DELIVER_ACK:return[3,5];case u.up.RECALL:return[3,6];case u.up.CHANNEL_ACK:return[3,15];case u.up.EDIT:return[3,16]}return[3,20];case 1:return ne="chat","agoraToken"===this.grantType&&(ne="singleChat"),this.delivery&&!n&&re!==this.context.userId&&T.call(this,re,oe,Q),[3,21];case 2:return ne="groupchat","agoraToken"===this.grantType&&(ne="groupChat"),[3,21];case 3:return ne="chatroom","agoraToken"===this.grantType&&(ne="chatRoom"),Y=1,[3,21];case 4:return ne="read_ack",ie=void 0,J.ext[0]&&JSON.parse(J.ext[0].stringValue)?(ie={id:Q,type:"read",from:re,to:oe,mid:te,groupReadCount:J.ext[0]&&JSON.parse(J.ext[0].stringValue),ackContent:J.ackContent,onlineState:Y},this.onReadMessage&&this.onReadMessage(ie),null===(g=this.eventHandler)||void 0===g||g.dispatch("onReadMessage",ie),[2]):(ie={id:Q,type:"read",from:re,to:oe,mid:te,onlineState:Y},this.onReadMessage&&this.onReadMessage(ie),null===(R=this.eventHandler)||void 0===R||R.dispatch("onReadMessage",ie),[2]);case 5:return ne="deliver_ack",this.onDeliveredMessage&&this.onDeliveredMessage({id:Q,type:"delivery",from:re,to:oe,mid:te,onlineState:Y}),se={id:Q,type:"delivery",from:re,to:oe,mid:te,onlineState:Y},null===(I=this.eventHandler)||void 0===I||I.dispatch("onDeliveredMessage",se),[2];case 6:return ne="recall",ae="",e.ext&&(ae=(null===(C=m(e.ext))||void 0===C?void 0:C.recallMessageExtensionInfo)||""),ue={id:Q,from:re||"admin",to:oe,mid:te,ext:ae,onlineState:Y},ce=oe===this.user?re:oe,[4,null===(N=null===(S=this._localCache)||void 0===S?void 0:S.getInstance())||void 0===N?void 0:N.getMessageByServerMsgId(ue.mid)];case 7:return(le=v.sent())?[4,null===(b=null===(A=this._localCache)||void 0===A?void 0:A.getInstance())||void 0===b?void 0:b.removeMsgByServerMsgId(ue.mid)]:[3,9];case 8:v.sent(),v.label=9;case 9:return"singleChat"!==(null==le?void 0:le.chatType)&&"groupChat"!==(null==le?void 0:le.chatType)?[3,14]:(de=(null==le?void 0:le.from)===this.user||""===(null==le?void 0:le.from),[4,null===(w=null===(M=this._localCache)||void 0===M?void 0:M.getInstance())||void 0===w?void 0:w.getConversationLastMessage(ce,le.chatType)]);case 10:return pe=v.sent(),[4,null===(U=null===(L=this._localCache)||void 0===L?void 0:L.getInstance())||void 0===U?void 0:U.getConversationBySessionId((0,c.u0)({conversationId:ce,conversationType:le.chatType}))];case 11:return(he=v.sent())?[4,null===(D=null===(x=this._localCache)||void 0===x?void 0:x.getInstance())||void 0===D?void 0:D.updateLocalConversation((0,c.u0)({conversationId:ce,conversationType:le.chatType}),{lastMessageId:null==pe?void 0:pe.serverMsgId,unReadCount:(0,c.rH)({conversation:he,isRecallSelfMsg:de,recalledMsgTime:le.time})})]:[3,13];case 12:v.sent(),v.label=13;case 13:v.label=14;case 14:return this.onRecallMessage&&this.onRecallMessage(ue),null===(P=this.eventHandler)||void 0===P||P.dispatch("onRecallMessage",ue),[2];case 15:return this.onChannelMessage&&this.onChannelMessage({id:Q,type:"channel",chatType:"singleChat",from:re,to:oe,time:Number(f),onlineState:Y}),fe={id:Q,type:"channel",chatType:"singleChat",from:re,to:oe,time:Number(f),onlineState:Y},null===(k=this.eventHandler)||void 0===k||k.dispatch("onChannelMessage",fe),[2];case 16:return ve={errorCode:0,reason:""},Ee="",me=0,ge="",J.meta&&"string"==typeof J.meta&&(ye=JSON.parse(J.meta)).edit_msg&&(_e=ye.edit_msg,Oe=_e.sender,Te=_e.send_time,Re=_e.chat_type,Ee=Oe,me=Te,ge=r[Re]),Ie=J.editMessageId&&new(i())(J.editMessageId.low,J.editMessageId.high,J.editMessageId.unsigned).toString(),Ce=J.editScope,[4,O.call(this,{status:ve,thirdMessage:J,msgBody:J.contents[0],msgId:Ie,type:ne,from:Ee,to:oe,time:me,onlineState:Y,ignoreCallback:!0,format:!0,isContentReplaced:X})];case 17:return(Se=v.sent()).chatType=ge,e.ignoreCallback?[2,Se]:Se?[4,null===(F=null===(H=this._localCache)||void 0===H?void 0:H.getInstance())||void 0===F?void 0:F.getMessageByServerMsgId(Se.id)]:[3,19];case 18:(Ne=v.sent())&&(Ae=Ne.ext||{},(Ce===u.xz.BODY_AND_EXT||Object.keys(Se.ext||{}).length>0)&&(Ae=Se.ext),"txt"===Se.type&&"txt"===Ne.type?null===(G=null===(B=this._localCache)||void 0===B?void 0:B.getInstance())||void 0===G||G.putMessageToDB(h(h({},Ne),{msg:Se.msg,modifiedInfo:Se.modifiedInfo,translations:Se.translations,ext:Ae})):"custom"===Se.type&&"custom"===Ne.type?null===(j=null===(W=this._localCache)||void 0===W?void 0:W.getInstance())||void 0===j||j.putMessageToDB(h(h({},Ne),{ext:Ae,customExts:Se.customExts,customEvent:Se.customEvent,modifiedInfo:Se.modifiedInfo})):null===(K=null===(q=this._localCache)||void 0===q?void 0:q.getInstance())||void 0===K||K.putMessageToDB(h(h({},Ne),{ext:Ae,modifiedInfo:Se.modifiedInfo}))),v.label=19;case 19:return null===(V=this.eventHandler)||void 0===V||V.dispatch("onModifiedMessage",Se),[2];case 20:return l.vF.error("unexpected message type: ".concat(J.type)),[2];case 21:be="normal",we=!1,"chat"===ne.toLowerCase()||"singleChat"===ne?Me="singleChat":"groupchat"===ne.toLowerCase()||"groupChat"===ne?Me="groupChat":(Me="chatRoom",Y=1,e.ext&&(Le=m(e.ext),we=!!(null==Le?void 0:Le.is_broadcast),be=0===Le.chatroom_msg_tag?"high":2===Le.chatroom_msg_tag?"low":"normal")),Ue=0,v.label=22;case 22:return Ue<J.contents.length?[4,O.call(this,{status:t,thirdMessage:J,msgBody:J.contents[Ue],msgId:Q,type:ne,from:re,to:oe,time:f,onlineState:Y,chatType:Me,ignoreCallback:n,priority:be,format:o,broadcast:we,isContentReplaced:X})]:[3,25];case 23:return[2,v.sent()];case 24:return Ue++,[3,22];case 25:return[2]}}))}))}},8811:function(e,t,n){"use strict";e.exports=i;var r=n(7209);((i.prototype=Object.create(r.prototype)).constructor=i).className="Method";var o=n(3262);function i(e,t,n,i,s,a,u,c,l){if(o.isObject(s)?(u=s,s=a=void 0):o.isObject(a)&&(u=a,a=void 0),void 0!==t&&!o.isString(t))throw TypeError("type must be a string");if(!o.isString(n))throw TypeError("requestType must be a string");if(!o.isString(i))throw TypeError("responseType must be a string");r.call(this,e,u),this.type=t||"rpc",this.requestType=n,this.requestStream=!!s||void 0,this.responseType=i,this.responseStream=!!a||void 0,this.resolvedRequestType=null,this.resolvedResponseType=null,this.comment=c,this.parsedOptions=l}i.fromJSON=function(e,t){return new i(e,t.type,t.requestType,t.responseType,t.requestStream,t.responseStream,t.options,t.comment,t.parsedOptions)},i.prototype.toJSON=function(e){var t=!!e&&Boolean(e.keepComments);return o.toObject(["type","rpc"!==this.type&&this.type||void 0,"requestType",this.requestType,"requestStream",this.requestStream,"responseType",this.responseType,"responseStream",this.responseStream,"options",this.options,"comment",t?this.comment:void 0,"parsedOptions",this.parsedOptions])},i.prototype.resolve=function(){return this.resolved?this:(this.resolvedRequestType=this.parent.lookupType(this.requestType),this.resolvedResponseType=this.parent.lookupType(this.responseType),r.prototype.resolve.call(this))}},8814:function(e,t,n){"use strict";var r=n(9039),o=n(4576).RegExp;e.exports=r((function(){var e=o("(?<a>b)","g");return"b"!==e.exec("b").groups.a||"bc"!=="b".replace(e,"$<a>c")}))},8839:function(e,t){"use strict";var n=t;n.length=function(e){var t=e.length;if(!t)return 0;for(var n=0;--t%4>1&&"="===e.charAt(t);)++n;return Math.ceil(3*e.length)/4-n};for(var r=new Array(64),o=new Array(123),i=0;i<64;)o[r[i]=i<26?i+65:i<52?i+71:i<62?i-4:i-59|43]=i++;n.encode=function(e,t,n){for(var o,i=null,s=[],a=0,u=0;t<n;){var c=e[t++];switch(u){case 0:s[a++]=r[c>>2],o=(3&c)<<4,u=1;break;case 1:s[a++]=r[o|c>>4],o=(15&c)<<2,u=2;break;case 2:s[a++]=r[o|c>>6],s[a++]=r[63&c],u=0}a>8191&&((i||(i=[])).push(String.fromCharCode.apply(String,s)),a=0)}return u&&(s[a++]=r[o],s[a++]=61,1===u&&(s[a++]=61)),i?(a&&i.push(String.fromCharCode.apply(String,s.slice(0,a))),i.join("")):String.fromCharCode.apply(String,s.slice(0,a))};var s="invalid encoding";n.decode=function(e,t,n){for(var r,i=n,a=0,u=0;u<e.length;){var c=e.charCodeAt(u++);if(61===c&&a>1)break;if(void 0===(c=o[c]))throw Error(s);switch(a){case 0:r=c,a=1;break;case 1:t[n++]=r<<2|(48&c)>>4,r=c,a=2;break;case 2:t[n++]=(15&r)<<4|(60&c)>>2,r=c,a=3;break;case 3:t[n++]=(3&r)<<6|c,a=0}}if(1===a)throw Error(s);return n-i},n.test=function(e){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(e)}},8845:function(e,t,n){"use strict";var r=n(4576),o=n(9565),i=n(4644),s=n(6198),a=n(8229),u=n(8981),c=n(9039),l=r.RangeError,d=r.Int8Array,p=d&&d.prototype,h=p&&p.set,f=i.aTypedArray,v=i.exportTypedArrayMethod,E=!c((function(){var e=new Uint8ClampedArray(2);return o(h,e,{length:1,0:3},1),3!==e[1]})),m=E&&i.NATIVE_ARRAY_BUFFER_VIEWS&&c((function(){var e=new d(2);return e.set(1),e.set("2",1),0!==e[0]||2!==e[1]}));v("set",(function(e){f(this);var t=a(arguments.length>1?arguments[1]:void 0,1),n=u(e);if(E)return o(h,this,n,t);var r=this.length,i=s(n),c=0;if(i+t>r)throw new l("Wrong length");for(;c<i;)this[t+c]=n[c++]}),!E||m)},8921:function(e,t,n){"use strict";var r=n(6518),o=n(8379);r({target:"Array",proto:!0,forced:o!==[].lastIndexOf},{lastIndexOf:o})},8923:function(e,t,n){"use strict";e.exports=d;var r=n(7209);((d.prototype=Object.create(r.prototype)).constructor=d).className="Namespace";var o,i,s,a=n(1344),u=n(1457),c=n(3262);function l(e,t){if(e&&e.length){for(var n={},r=0;r<e.length;++r)n[e[r].name]=e[r].toJSON(t);return n}}function d(e,t){r.call(this,e,t),this.nested=void 0,this._nestedArray=null}function p(e){return e._nestedArray=null,e}d.fromJSON=function(e,t){return new d(e,t.options).addJSON(t.nested)},d.arrayToJSON=l,d.isReservedId=function(e,t){if(e)for(var n=0;n<e.length;++n)if("string"!=typeof e[n]&&e[n][0]<=t&&e[n][1]>t)return!0;return!1},d.isReservedName=function(e,t){if(e)for(var n=0;n<e.length;++n)if(e[n]===t)return!0;return!1},Object.defineProperty(d.prototype,"nestedArray",{get:function(){return this._nestedArray||(this._nestedArray=c.toArray(this.nested))}}),d.prototype.toJSON=function(e){return c.toObject(["options",this.options,"nested",l(this.nestedArray,e)])},d.prototype.addJSON=function(e){if(e)for(var t,n=Object.keys(e),r=0;r<n.length;++r)t=e[n[r]],this.add((void 0!==t.fields?o.fromJSON:void 0!==t.values?s.fromJSON:void 0!==t.methods?i.fromJSON:void 0!==t.id?a.fromJSON:d.fromJSON)(n[r],t));return this},d.prototype.get=function(e){return this.nested&&this.nested[e]||null},d.prototype.getEnum=function(e){if(this.nested&&this.nested[e]instanceof s)return this.nested[e].values;throw Error("no such enum: "+e)},d.prototype.add=function(e){if(!(e instanceof a&&void 0!==e.extend||e instanceof o||e instanceof s||e instanceof i||e instanceof d||e instanceof u))throw TypeError("object must be a valid nested object");if(this.nested){var t=this.get(e.name);if(t){if(!(t instanceof d&&e instanceof d)||t instanceof o||t instanceof i)throw Error("duplicate name '"+e.name+"' in "+this);for(var n=t.nestedArray,r=0;r<n.length;++r)e.add(n[r]);this.remove(t),this.nested||(this.nested={}),e.setOptions(t.options,!0)}}else this.nested={};return this.nested[e.name]=e,e.onAdd(this),p(this)},d.prototype.remove=function(e){if(!(e instanceof r))throw TypeError("object must be a ReflectionObject");if(e.parent!==this)throw Error(e+" is not a member of "+this);return delete this.nested[e.name],Object.keys(this.nested).length||(this.nested=void 0),e.onRemove(this),p(this)},d.prototype.define=function(e,t){if(c.isString(e))e=e.split(".");else if(!Array.isArray(e))throw TypeError("illegal path");if(e&&e.length&&""===e[0])throw Error("path must be relative");for(var n=this;e.length>0;){var r=e.shift();if(n.nested&&n.nested[r]){if(!((n=n.nested[r])instanceof d))throw Error("path conflicts with non-namespace objects")}else n.add(n=new d(r))}return t&&n.addJSON(t),n},d.prototype.resolveAll=function(){for(var e=this.nestedArray,t=0;t<e.length;)e[t]instanceof d?e[t++].resolveAll():e[t++].resolve();return this.resolve()},d.prototype.lookup=function(e,t,n){if("boolean"==typeof t?(n=t,t=void 0):t&&!Array.isArray(t)&&(t=[t]),c.isString(e)&&e.length){if("."===e)return this.root;e=e.split(".")}else if(!e.length)return this;if(""===e[0])return this.root.lookup(e.slice(1),t);var r=this.get(e[0]);if(r){if(1===e.length){if(!t||t.indexOf(r.constructor)>-1)return r}else if(r instanceof d&&(r=r.lookup(e.slice(1),t,!0)))return r}else for(var o=0;o<this.nestedArray.length;++o)if(this._nestedArray[o]instanceof d&&(r=this._nestedArray[o].lookup(e,t,!0)))return r;return null===this.parent||n?null:this.parent.lookup(e,t)},d.prototype.lookupType=function(e){var t=this.lookup(e,[o]);if(!t)throw Error("no such type: "+e);return t},d.prototype.lookupEnum=function(e){var t=this.lookup(e,[s]);if(!t)throw Error("no such Enum '"+e+"' in "+this);return t},d.prototype.lookupTypeOrEnum=function(e){var t=this.lookup(e,[o,s]);if(!t)throw Error("no such Type or Enum '"+e+"' in "+this);return t},d.prototype.lookupService=function(e){var t=this.lookup(e,[i]);if(!t)throw Error("no such Service '"+e+"' in "+this);return t},d._configure=function(e,t,n){o=e,i=t,s=n}},8980:function(e,t,n){"use strict";var r=n(6518),o=n(9213).findIndex,i=n(6469),s="findIndex",a=!0;s in[]&&Array(1)[s]((function(){a=!1})),r({target:"Array",proto:!0,forced:a},{findIndex:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}}),i(s)},8981:function(e,t,n){"use strict";var r=n(7750),o=Object;e.exports=function(e){return o(r(e))}},8995:function(e,t,n){"use strict";var r=n(4644),o=n(9213).map,i=r.aTypedArray,s=r.getTypedArrayConstructor;(0,r.exportTypedArrayMethod)("map",(function(e){return o(i(this),e,arguments.length>1?arguments[1]:void 0,(function(e,t){return new(s(e))(t)}))}))},9039:function(e){"use strict";e.exports=function(e){try{return!!e()}catch(e){return!0}}},9085:function(e,t,n){"use strict";var r=n(6518),o=n(4213);r({target:"Object",stat:!0,arity:2,forced:Object.assign!==o},{assign:o})},9089:function(e,t,n){"use strict";var r=n(6518),o=n(9504),i=Date,s=o(i.prototype.getTime);r({target:"Date",stat:!0},{now:function(){return s(new i)}})},9100:function(e,t,n){"use strict";var r=e.exports=n(5325);r.build="full",r.tokenize=n(527),r.parse=n(4863),r.common=n(5095),r.Root._configure(r.Type,r.parse,r.common)},9167:function(e,t,n){"use strict";var r=n(4576);e.exports=r},9207:function(e,t){"use strict";var n=t,r=n.isAbsolute=function(e){return/^(?:\/|\w+:)/.test(e)},o=n.normalize=function(e){var t=(e=e.replace(/\\/g,"/").replace(/\/{2,}/g,"/")).split("/"),n=r(e),o="";n&&(o=t.shift()+"/");for(var i=0;i<t.length;)".."===t[i]?i>0&&".."!==t[i-1]?t.splice(--i,2):n?t.splice(i,1):++i:"."===t[i]?t.splice(i,1):++i;return o+t.join("/")};n.resolve=function(e,t,n){return n||(t=o(t)),r(t)?t:(n||(e=o(e)),(e=e.replace(/(?:\/|^)[^/]+$/,"")).length?o(e+"/"+t):t)}},9213:function(e,t,n){"use strict";var r=n(6080),o=n(9504),i=n(7055),s=n(8981),a=n(6198),u=n(1469),c=o([].push),l=function(e){var t=1===e,n=2===e,o=3===e,l=4===e,d=6===e,p=7===e,h=5===e||d;return function(f,v,E,m){for(var g,y,_=s(f),O=i(_),T=a(O),R=r(v,E),I=0,C=m||u,S=t?C(f,T):n||p?C(f,0):void 0;T>I;I++)if((h||I in O)&&(y=R(g=O[I],I,_),e))if(t)S[I]=y;else if(y)switch(e){case 3:return!0;case 5:return g;case 6:return I;case 2:c(S,g)}else switch(e){case 4:return!1;case 7:c(S,g)}return d?-1:o||l?l:S}};e.exports={forEach:l(0),map:l(1),filter:l(2),some:l(3),every:l(4),find:l(5),findIndex:l(6),filterReject:l(7)}},9225:function(e,t,n){"use strict";var r,o,i,s,a=n(4576),u=n(8745),c=n(6080),l=n(4901),d=n(9297),p=n(9039),h=n(397),f=n(7680),v=n(4055),E=n(2812),m=n(9544),g=n(6193),y=a.setImmediate,_=a.clearImmediate,O=a.process,T=a.Dispatch,R=a.Function,I=a.MessageChannel,C=a.String,S=0,N={},A="onreadystatechange";p((function(){r=a.location}));var b=function(e){if(d(N,e)){var t=N[e];delete N[e],t()}},M=function(e){return function(){b(e)}},w=function(e){b(e.data)},L=function(e){a.postMessage(C(e),r.protocol+"//"+r.host)};y&&_||(y=function(e){E(arguments.length,1);var t=l(e)?e:R(e),n=f(arguments,1);return N[++S]=function(){u(t,void 0,n)},o(S),S},_=function(e){delete N[e]},g?o=function(e){O.nextTick(M(e))}:T&&T.now?o=function(e){T.now(M(e))}:I&&!m?(s=(i=new I).port2,i.port1.onmessage=w,o=c(s.postMessage,s)):a.addEventListener&&l(a.postMessage)&&!a.importScripts&&r&&"file:"!==r.protocol&&!p(L)?(o=L,a.addEventListener("message",w,!1)):o=A in v("script")?function(e){h.appendChild(v("script"))[A]=function(){h.removeChild(this),b(e)}}:function(e){setTimeout(M(e),0)}),e.exports={set:y,clear:_}},9296:function(e,t,n){"use strict";var r=n(4055)("span").classList,o=r&&r.constructor&&r.constructor.prototype;e.exports=o===Object.prototype?void 0:o},9297:function(e,t,n){"use strict";var r=n(9504),o=n(8981),i=r({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return i(o(e),t)}},9306:function(e,t,n){"use strict";var r=n(4901),o=n(6823),i=TypeError;e.exports=function(e){if(r(e))return e;throw new i(o(e)+" is not a function")}},9369:function(e,t,n){"use strict";var r=n(4644),o=n(9504),i=r.aTypedArray,s=r.exportTypedArrayMethod,a=o([].join);s("join",(function(e){return a(i(this),e)}))},9390:function(e){"use strict";e.exports=function(e,t,n){var r=n||8192,o=r>>>1,i=null,s=r;return function(n){if(n<1||n>o)return e(n);s+n>r&&(i=e(r),s=0);var a=t.call(i,s,s+=n);return 7&s&&(s=1+(7|s)),a}}},9410:function(e){"use strict";function t(e){return"undefined"!=typeof Float32Array?function(){var t=new Float32Array([-0]),n=new Uint8Array(t.buffer),r=128===n[3];function o(e,r,o){t[0]=e,r[o]=n[0],r[o+1]=n[1],r[o+2]=n[2],r[o+3]=n[3]}function i(e,r,o){t[0]=e,r[o]=n[3],r[o+1]=n[2],r[o+2]=n[1],r[o+3]=n[0]}function s(e,r){return n[0]=e[r],n[1]=e[r+1],n[2]=e[r+2],n[3]=e[r+3],t[0]}function a(e,r){return n[3]=e[r],n[2]=e[r+1],n[1]=e[r+2],n[0]=e[r+3],t[0]}e.writeFloatLE=r?o:i,e.writeFloatBE=r?i:o,e.readFloatLE=r?s:a,e.readFloatBE=r?a:s}():function(){function t(e,t,n,r){var o=t<0?1:0;if(o&&(t=-t),0===t)e(1/t>0?0:2147483648,n,r);else if(isNaN(t))e(2143289344,n,r);else if(t>34028234663852886e22)e((o<<31|2139095040)>>>0,n,r);else if(t<11754943508222875e-54)e((o<<31|Math.round(t/1401298464324817e-60))>>>0,n,r);else{var i=Math.floor(Math.log(t)/Math.LN2);e((o<<31|i+127<<23|8388607&Math.round(t*Math.pow(2,-i)*8388608))>>>0,n,r)}}function s(e,t,n){var r=e(t,n),o=2*(r>>31)+1,i=r>>>23&255,s=8388607&r;return 255===i?s?NaN:o*(1/0):0===i?1401298464324817e-60*o*s:o*Math.pow(2,i-150)*(s+8388608)}e.writeFloatLE=t.bind(null,n),e.writeFloatBE=t.bind(null,r),e.readFloatLE=s.bind(null,o),e.readFloatBE=s.bind(null,i)}(),"undefined"!=typeof Float64Array?function(){var t=new Float64Array([-0]),n=new Uint8Array(t.buffer),r=128===n[7];function o(e,r,o){t[0]=e,r[o]=n[0],r[o+1]=n[1],r[o+2]=n[2],r[o+3]=n[3],r[o+4]=n[4],r[o+5]=n[5],r[o+6]=n[6],r[o+7]=n[7]}function i(e,r,o){t[0]=e,r[o]=n[7],r[o+1]=n[6],r[o+2]=n[5],r[o+3]=n[4],r[o+4]=n[3],r[o+5]=n[2],r[o+6]=n[1],r[o+7]=n[0]}function s(e,r){return n[0]=e[r],n[1]=e[r+1],n[2]=e[r+2],n[3]=e[r+3],n[4]=e[r+4],n[5]=e[r+5],n[6]=e[r+6],n[7]=e[r+7],t[0]}function a(e,r){return n[7]=e[r],n[6]=e[r+1],n[5]=e[r+2],n[4]=e[r+3],n[3]=e[r+4],n[2]=e[r+5],n[1]=e[r+6],n[0]=e[r+7],t[0]}e.writeDoubleLE=r?o:i,e.writeDoubleBE=r?i:o,e.readDoubleLE=r?s:a,e.readDoubleBE=r?a:s}():function(){function t(e,t,n,r,o,i){var s=r<0?1:0;if(s&&(r=-r),0===r)e(0,o,i+t),e(1/r>0?0:2147483648,o,i+n);else if(isNaN(r))e(0,o,i+t),e(2146959360,o,i+n);else if(r>17976931348623157e292)e(0,o,i+t),e((s<<31|2146435072)>>>0,o,i+n);else{var a;if(r<22250738585072014e-324)e((a=r/5e-324)>>>0,o,i+t),e((s<<31|a/4294967296)>>>0,o,i+n);else{var u=Math.floor(Math.log(r)/Math.LN2);1024===u&&(u=1023),e(4503599627370496*(a=r*Math.pow(2,-u))>>>0,o,i+t),e((s<<31|u+1023<<20|1048576*a&1048575)>>>0,o,i+n)}}}function s(e,t,n,r,o){var i=e(r,o+t),s=e(r,o+n),a=2*(s>>31)+1,u=s>>>20&2047,c=4294967296*(1048575&s)+i;return 2047===u?c?NaN:a*(1/0):0===u?5e-324*a*c:a*Math.pow(2,u-1075)*(c+4503599627370496)}e.writeDoubleLE=t.bind(null,n,0,4),e.writeDoubleBE=t.bind(null,r,4,0),e.readDoubleLE=s.bind(null,o,0,4),e.readDoubleBE=s.bind(null,i,4,0)}(),e}function n(e,t,n){t[n]=255&e,t[n+1]=e>>>8&255,t[n+2]=e>>>16&255,t[n+3]=e>>>24}function r(e,t,n){t[n]=e>>>24,t[n+1]=e>>>16&255,t[n+2]=e>>>8&255,t[n+3]=255&e}function o(e,t){return(e[t]|e[t+1]<<8|e[t+2]<<16|e[t+3]<<24)>>>0}function i(e,t){return(e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3])>>>0}e.exports=t(t)},9423:function(e,t,n){"use strict";var r=n(4644),o=n(9039),i=n(7680),s=r.aTypedArray,a=r.getTypedArrayConstructor;(0,r.exportTypedArrayMethod)("slice",(function(e,t){for(var n=i(s(this),e,t),r=a(this),o=0,u=n.length,c=new r(u);u>o;)c[o]=n[o++];return c}),o((function(){new Int8Array(1).slice()})))},9432:function(e,t,n){"use strict";var r=n(6518),o=n(8981),i=n(1072);r({target:"Object",stat:!0,forced:n(9039)((function(){i(1)}))},{keys:function(e){return i(o(e))}})},9433:function(e,t,n){"use strict";var r=n(4576),o=Object.defineProperty;e.exports=function(e,t){try{o(r,e,{value:t,configurable:!0,writable:!0})}catch(n){r[e]=t}return t}},9463:function(e,t,n){"use strict";var r=n(6518),o=n(3724),i=n(4576),s=n(9504),a=n(9297),u=n(4901),c=n(1625),l=n(655),d=n(2106),p=n(7740),h=i.Symbol,f=h&&h.prototype;if(o&&u(h)&&(!("description"in f)||void 0!==h().description)){var v={},E=function(){var e=arguments.length<1||void 0===arguments[0]?void 0:l(arguments[0]),t=c(f,this)?new h(e):void 0===e?h():h(e);return""===e&&(v[t]=!0),t};p(E,h),E.prototype=f,f.constructor=E;var m="Symbol(description detection)"===String(h("description detection")),g=s(f.valueOf),y=s(f.toString),_=/^Symbol\((.*)\)[^)]+$/,O=s("".replace),T=s("".slice);d(f,"description",{configurable:!0,get:function(){var e=g(this);if(a(v,e))return"";var t=y(e),n=m?T(t,7,-1):O(t,_,"$1");return""===n?void 0:n}}),r({global:!0,constructor:!0,forced:!0},{Symbol:E})}},9472:function(e,t,n){"use strict";var r,o=n(4576),i=n(8745),s=n(4901),a=n(4215),u=n(2839),c=n(7680),l=n(2812),d=o.Function,p=/MSIE .\./.test(u)||"BUN"===a&&((r=o.Bun.version.split(".")).length<3||"0"===r[0]&&(r[1]<3||"3"===r[1]&&"0"===r[2]));e.exports=function(e,t){var n=t?2:1;return p?function(r,o){var a=l(arguments.length,1)>n,u=s(r)?r:d(r),p=a?c(arguments,n):[],h=a?function(){i(u,this,p)}:u;return t?e(h,o):e(h)}:e}},9504:function(e,t,n){"use strict";var r=n(616),o=Function.prototype,i=o.call,s=r&&o.bind.bind(i,i);e.exports=r?s:function(e){return function(){return i.apply(e,arguments)}}},9519:function(e,t,n){"use strict";var r,o,i=n(4576),s=n(2839),a=i.process,u=i.Deno,c=a&&a.versions||u&&u.version,l=c&&c.v8;l&&(o=(r=l.split("."))[0]>0&&r[0]<4?1:+(r[0]+r[1])),!o&&s&&(!(r=s.match(/Edge\/(\d+)/))||r[1]>=74)&&(r=s.match(/Chrome\/(\d+)/))&&(o=+r[1]),e.exports=o},9539:function(e,t,n){"use strict";var r=n(9565),o=n(8551),i=n(5966);e.exports=function(e,t,n){var s,a;o(e);try{if(!(s=i(e,"return"))){if("throw"===t)throw n;return n}s=r(s,e)}catch(e){a=!0,s=e}if("throw"===t)throw n;if(a)throw s;return o(s),n}},9544:function(e,t,n){"use strict";var r=n(2839);e.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(r)},9565:function(e,t,n){"use strict";var r=n(616),o=Function.prototype.call;e.exports=r?o.bind(o):function(){return o.apply(o,arguments)}},9590:function(e,t,n){"use strict";var r=n(1291),o=RangeError;e.exports=function(e){var t=r(e);if(t<0)throw new o("The argument can't be less than 0");return t}},9617:function(e,t,n){"use strict";var r=n(5397),o=n(5610),i=n(6198),s=function(e){return function(t,n,s){var a=r(t),u=i(a);if(0===u)return!e&&-1;var c,l=o(s,u);if(e&&n!=n){for(;u>l;)if((c=a[l++])!=c)return!0}else for(;u>l;l++)if((e||l in a)&&a[l]===n)return e||l||0;return!e&&-1}};e.exports={includes:s(!0),indexOf:s(!1)}},9687:function(e,t,n){"use strict";e.exports=a;var r=n(8923);((a.prototype=Object.create(r.prototype)).constructor=a).className="Service";var o=n(8811),i=n(3262),s=n(5047);function a(e,t){r.call(this,e,t),this.methods={},this._methodsArray=null}function u(e){return e._methodsArray=null,e}a.fromJSON=function(e,t){var n=new a(e,t.options);if(t.methods)for(var r=Object.keys(t.methods),i=0;i<r.length;++i)n.add(o.fromJSON(r[i],t.methods[r[i]]));return t.nested&&n.addJSON(t.nested),n.comment=t.comment,n},a.prototype.toJSON=function(e){var t=r.prototype.toJSON.call(this,e),n=!!e&&Boolean(e.keepComments);return i.toObject(["options",t&&t.options||void 0,"methods",r.arrayToJSON(this.methodsArray,e)||{},"nested",t&&t.nested||void 0,"comment",n?this.comment:void 0])},Object.defineProperty(a.prototype,"methodsArray",{get:function(){return this._methodsArray||(this._methodsArray=i.toArray(this.methods))}}),a.prototype.get=function(e){return this.methods[e]||r.prototype.get.call(this,e)},a.prototype.resolveAll=function(){for(var e=this.methodsArray,t=0;t<e.length;++t)e[t].resolve();return r.prototype.resolve.call(this)},a.prototype.add=function(e){if(this.get(e.name))throw Error("duplicate name '"+e.name+"' in "+this);return e instanceof o?(this.methods[e.name]=e,e.parent=this,u(this)):r.prototype.add.call(this,e)},a.prototype.remove=function(e){if(e instanceof o){if(this.methods[e.name]!==e)throw Error(e+" is not a member of "+this);return delete this.methods[e.name],e.parent=null,u(this)}return r.prototype.remove.call(this,e)},a.prototype.create=function(e,t,n){for(var r,o=new s.Service(e,t,n),a=0;a<this.methodsArray.length;++a){var u=i.lcFirst((r=this._methodsArray[a]).resolve().name).replace(/[^$\w_]/g,"");o[u]=i.codegen(["r","c"],i.isReserved(u)?u+"_":u)("return this.rpcCall(m,q,s,r,c)")({m:r,q:r.resolvedRequestType.ctor,s:r.resolvedResponseType.ctor})}return o}},9773:function(e,t,n){"use strict";var r=n(6518),o=n(4495),i=n(9039),s=n(3717),a=n(8981);r({target:"Object",stat:!0,forced:!o||i((function(){s.f(1)}))},{getOwnPropertySymbols:function(e){var t=s.f;return t?t(a(e)):[]}})},9868:function(e,t,n){"use strict";var r=n(6518),o=n(9504),i=n(1291),s=n(1240),a=n(2333),u=n(9039),c=RangeError,l=String,d=Math.floor,p=o(a),h=o("".slice),f=o(1..toFixed),v=function(e,t,n){return 0===t?n:t%2==1?v(e,t-1,n*e):v(e*e,t/2,n)},E=function(e,t,n){for(var r=-1,o=n;++r<6;)o+=t*e[r],e[r]=o%1e7,o=d(o/1e7)},m=function(e,t){for(var n=6,r=0;--n>=0;)r+=e[n],e[n]=d(r/t),r=r%t*1e7},g=function(e){for(var t=6,n="";--t>=0;)if(""!==n||0===t||0!==e[t]){var r=l(e[t]);n=""===n?r:n+p("0",7-r.length)+r}return n};r({target:"Number",proto:!0,forced:u((function(){return"0.000"!==f(8e-5,3)||"1"!==f(.9,0)||"1.25"!==f(1.255,2)||"1000000000000000128"!==f(0xde0b6b3a7640080,0)}))||!u((function(){f({})}))},{toFixed:function(e){var t,n,r,o,a=s(this),u=i(e),d=[0,0,0,0,0,0],f="",y="0";if(u<0||u>20)throw new c("Incorrect fraction digits");if(a!=a)return"NaN";if(a<=-1e21||a>=1e21)return l(a);if(a<0&&(f="-",a=-a),a>1e-21)if(n=(t=function(e){for(var t=0,n=e;n>=4096;)t+=12,n/=4096;for(;n>=2;)t+=1,n/=2;return t}(a*v(2,69,1))-69)<0?a*v(2,-t,1):a/v(2,t,1),n*=4503599627370496,(t=52-t)>0){for(E(d,0,n),r=u;r>=7;)E(d,1e7,0),r-=7;for(E(d,v(10,r,1),0),r=t-1;r>=23;)m(d,1<<23),r-=23;m(d,1<<r),E(d,1,1),m(d,2),y=g(d)}else E(d,0,n),E(d,1<<-t,0),y=g(d)+p("0",u);return u>0?f+((o=y.length)<=u?"0."+p("0",u-o)+y:h(y,0,o-u)+"."+h(y,o-u)):f+y}})},9948:function(e,t,n){"use strict";var r=n(5370),o=n(4644).getTypedArrayConstructor;e.exports=function(e,t){return r(o(e),t)}},9955:function(e,t,n){"use strict";var r=n(4644),o=n(9213).findIndex,i=r.aTypedArray;(0,r.exportTypedArrayMethod)("findIndex",(function(e){return o(i(this),e,arguments.length>1?arguments[1]:void 0)}))}},n={};function r(e){var o=n[e];if(void 0!==o)return o.exports;var i=n[e]={exports:{}};return t[e].call(i.exports,i,i.exports,r),i.exports}r.m=t,r.amdO={},e=[],r.O=function(t,n,o,i){if(!n){var s=1/0;for(l=0;l<e.length;l++){n=e[l][0],o=e[l][1],i=e[l][2];for(var a=!0,u=0;u<n.length;u++)(!1&i||s>=i)&&Object.keys(r.O).every((function(e){return r.O[e](n[u])}))?n.splice(u--,1):(a=!1,i<s&&(s=i));if(a){e.splice(l--,1);var c=o();void 0!==c&&(t=c)}}return t}i=i||0;for(var l=e.length;l>0&&e[l-1][2]>i;l--)e[l]=e[l-1];e[l]=[n,o,i]},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,{a:t}),t},r.d=function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},function(){var e={891:0};r.O.j=function(t){return 0===e[t]};var t=function(t,n){var o,i,s=n[0],a=n[1],u=n[2],c=0;if(s.some((function(t){return 0!==e[t]}))){for(o in a)r.o(a,o)&&(r.m[o]=a[o]);if(u)var l=u(r)}for(t&&t(n);c<s.length;c++)i=s[c],r.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return r.O(l)},n=self.webpackChunkwebsdk=self.webpackChunkwebsdk||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))}();var o={};return function(){"use strict";r.r(o),r.d(o,{MiniCore:function(){return G},default:function(){return j}}),r(2675),r(9463),r(2259),r(8706),r(2008),r(1629),r(4423),r(3792),r(2712),r(4743),r(1745),r(8309),r(9089),r(739),r(3288),r(4170),r(2010),r(6033),r(2892),r(9085),r(9432),r(6099),r(3362),r(1699),r(7764),r(1392),r(1489),r(1630),r(2170),r(5044),r(1920),r(1694),r(9955),r(3206),r(4496),r(6651),r(2887),r(9369),r(6812),r(8995),r(1575),r(6072),r(8747),r(8845),r(9423),r(7301),r(373),r(6614),r(1405),r(3684),r(3500),r(2953),r(6031);var e=r(2549),t=r.n(e),n=r(8570),i=r.n(n),s=r(7517),a=r(8678),u=r(2056),c=r(8434),l=["onTextMessage","onFileMessage","onAudioMessage","onVideoMessage","onImageMessage","onLocationMessage","onCustomMessage","onCMDMessage"],d=function(){function e(e,t,n){this.handlerData={},this.handlerData={},e.addEventHandler=this.addEventHandler.bind(this),e.removeEventHandler=this.removeEventHandler.bind(this)}return e.prototype.addEventHandler=function(e,t){this.handlerData[e]=t},e.prototype.removeEventHandler=function(e){delete this.handlerData[e]},e.prototype.dispatch=function(e,t){for(var n in l.includes(e)&&t?u.vF.debug("dispatch event: "+e,{id:t.id,type:t.type,time:t.time,from:t.from,to:t.to,chatType:t.chatType}):"onMessage"===e?u.vF.debug("dispatch event: "+e,null==t?void 0:t.length):u.vF.debug("dispatch event: "+e,t||""),this.handlerData){var r=this.handlerData[n][e];r&&r(t)}},e}(),p=(r(5276),{biz:"",debug:!1,token:""}),h="https://data-reporting.agora.io/report",f="https://data-reporting.sh.agoralab.co/report",v=r(75),E=r(3893),m=function(e){var t=Number(E.S5[e]);return t===E.S5.USER_LOGIN?"MANUALLOGIN":t===E.S5.MSYNC_SENDMESSAGE?"SENDMESSAGE":t>E.S5.UNKNOWOPERATION&&t<E.S5.REST_OPERATE?"REST":t>E.S5.REST_OPERATE&&t<E.S5.MSYNC_OPERATE?"MESSAGE":t>E.S5.MSYNC_OPERATE&&t<E.S5.ROSTER_OPERATE?"ROSTER":t>E.S5.ROSTER_OPERATE&&t<E.S5.USER_OPERATE?"USER":t>E.S5.USER_OPERATE&&t<E.S5.GROUP_OPERATE?"GROUP":t>E.S5.GROUP_OPERATE&&t<E.S5.CHATROOM_OPERATE?"CHATROOM":"OPERATION"},g=function(){return(new Date).getTime()},y=function(e){return[a.i9.BAIDU,a.i9.WX,a.i9.DD,a.i9.ZFB,a.i9.TT,a.i9.QUICK_APP,a.i9.UNI].includes(e.platform)},_=function(){return e=1,t=99999,e=Math.ceil(e),t=Math.floor(t),Math.floor(Math.random()*(t-e))+e;var e,t},O=r(3989),T={},R=(r(2062),r(4554),function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}),I=1e3,C=function(){function e(e){this.eventQueue=[],this.stock=I,this.config=e,this.governor()}return e.prototype.add=function(e){this.stock<=0?console.warn("Event Report limit ".concat(I," per minute")):(this.eventQueue.push(e),this.consume(),this.stock-=1)},e.prototype.consume=function(){var e=this;0!==this.eventQueue.length&&(this.timer&&this.eventQueue.length<=10&&clearTimeout(this.timer),this.timer=setTimeout((function(){var t,n=e.eventQueue.splice(0,10),r=n.filter((function(e){return e.appId===n[0].appId})),o=n.filter((function(e){return e.appId!==n[0].appId}));(t=e.eventQueue).unshift.apply(t,o),e.batchSend(r)}),1e3))},e.prototype.governor=function(){var e=this,t=setInterval((function(){e.stock=I}),6e4);"undefined"!=typeof addEventListener&&addEventListener("beforeunload",(function(){clearInterval(t)}))},e.prototype.batchSend=function(e){var t,n;if(void 0===e&&(e=[]),0!==e.length)try{var r=e.map((function(e){e.biz,e.appId;var t=e.eventId,n=R(e,["biz","appId","eventId"]);return{eventId:Number(t),body:n}}));!function(e,t){try{var n=t.biz,r=t.appId,o=t.data,i=t.debug,s=t.onSuccess;if(!n)throw new Error("biz is not defined");T.global||(T=a.Wp.getEnvInfo());var u=T;if(y(u)){var c={url:i?f:h,data:o,method:"POST",timeout:O.Gc,success:function(){null==s||s()},fail:function(){},complete:function(){}},l={token:e,appid:null!=r?r:"",sendts:"".concat(Math.floor((new Date).getTime()/1e3)),biz:n,debug:"".concat(i)};if("zfb"===u.platform||"dd"===u.platform?c.headers=l:c.header=l,"dd"===u.platform)return u.global.httpRequest(c);u.global.request(c)}else{var d=new XMLHttpRequest;d.onreadystatechange=function(){2===d.readyState&&(null==s||s())},d.open("POST",i?f:h),d.setRequestHeader("Content-Type","application/json"),d.setRequestHeader("token",e),d.setRequestHeader("appid",null!=r?r:""),d.setRequestHeader("sendts","".concat(Math.floor((new Date).getTime()/1e3))),d.setRequestHeader("biz",n),d.setRequestHeader("debug","".concat(i)),d.send(JSON.stringify(o))}}catch(e){console.error(e)}}(this.config.token,{biz:null===(t=e[0])||void 0===t?void 0:t.biz,appId:null===(n=e[0])||void 0===n?void 0:n.appId,data:r,debug:this.config.debug,onSuccess:this.consume.bind(this)})}catch(e){console.error(e)}},e}(),S=function(){return S=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},S.apply(this,arguments)},N=new(function(){function e(e){this.inited=!1,this.appId="",this.biz="",this.eventQueue={},this.config=p,e&&this.init(e)}return e.prototype.init=function(e){var t;if(void 0===e&&(e={}),!e.biz||!e.token)throw new Error("Event Report: biz or token is not defined");try{this.appId=null!==(t=e.appId)&&void 0!==t?t:"",this.biz=e.biz,this.config=S(S({},p),e),this.eventQueue=new C(this.config),this.log(e),this.inited=!0}catch(e){console.error(e)}},e.prototype.updateAppId=function(e){this.appId=e},e.prototype.send=function(e,t,n){var r;if(void 0===t&&(t={}),this.inited){var o=S(S({},t),{eventId:Number(e),biz:this.biz,appId:null!==(r=null==n?void 0:n.appId)&&void 0!==r?r:this.appId});this.eventQueue.add(o),this.log(o)}else console.error("Event Report: init is not called")},e.prototype.log=function(e){try{if(this.config.debug){var t=e.payload,n=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}(e,["payload"]);console.log("%c Event Report: ".concat(this.config.biz," "),"background: #8A97FC; color: #fff"),console.table(n),t&&(console.info("payload:"),console.table(t))}}catch(e){console.error(e)}},e}()),A=function(){return A=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},A.apply(this,arguments)},b={requestName:"",subrequestid:"1",requestMethod:"GET",requestUrl:"",requestElapse:0,code:0,codeDesc:"",isLastApi:0,isSuccess:1},M=function(){function e(e){this.platform=a.Wp.getEnvInfo(),this.isReportDt=e.isReport||!1,this.isCollectDt=!0,y(this.platform)&&!this.isReportDt&&(this.isCollectDt=!1),this.eventQueue=[],this.accessChannel=v.lM,this.options=function(e,t){t.platform||(t=a.Wp.getEnvInfo());var n=e.org,r=e.appkey,o=e.deviceId,i=e.sdkVersion,s=e.uikitVersion,u="undefined"!=typeof navigator?null===navigator||void 0===navigator?void 0:navigator.userAgent:"".concat(t.platform,"_mini_program");return{org:n,appkey:r,deviceId:o,sdkServiceId:"sid_".concat(a.Wp.getUniqueId(),"_").concat(_()),did:u,sdkVersion:i,os:v.Jz,sdkProduct:E.Wz.web,uikitVersion:s}}(e,this.platform),this.sid=this.options.sdkServiceId,this.init(e)}return e.getInstance=function(t){return e.instance||(e.instance=new e(t)),e.instance},e.prototype.getServiceId=function(){return this.sid||"sid_0"},e.prototype.setIsReportDt=function(e){this.isReportDt=e,e&&this.rptEventQueue()},e.prototype.setIsCollectDt=function(e){this.isCollectDt=e,e||(this.eventQueue=[])},e.prototype.rptEventQueue=function(){var e=this;this.eventQueue.length&&this.eventQueue.forEach((function(t,n){N.send(t.eventId,t.dt),n>=e.eventQueue.length-1&&(e.eventQueue=[])}))},e.prototype.init=function(t){e.instance||(e.instance=this,N.init({biz:v.KU,token:v.ey,appId:t.appkey,debug:!1}),this.reportInit())},e.prototype.updateAppKey=function(e){this.options.appkey=e,this.eventQueue.forEach((function(t){"default#appkey"===t.dt.appkey&&(t.dt.appkey=e)}))},e.prototype.reportInit=function(){if(this.isCollectDt){var e=this.options,t=e.did,n=e.os,r=e.sdkVersion,o=e.deviceId;this.reportData(v.uN.INIT,{did:t,os:n,sdkVersion:r,deviceId:o})}},e.prototype.geOperateFun=function(e){var t=this;if(!this.isCollectDt)return function(){};var n=1,r=0,o="",i=g(),s=e.uid,u=e.operationName;s&&(this.uid=s);var c={uid:this.uid,operationId:"opr_".concat(a.Wp.getUniqueId(),"_").concat(_()),requestid:"req_".concat(a.Wp.getUniqueId(),"_").concat(_()),operationName:u};return function(e){var s,l,d,p;if(e.data.isSuccess?(r=0,o=""):(0===e.data.code&&(e.data.code=v.XI),r=null!==(s=e.data.code)&&void 0!==s?s:r,o=null!==(l=e.data.codeDesc)&&void 0!==l?l:o),(null===(d=e.data)||void 0===d?void 0:d.accessChannel)&&(t.accessChannel=null===(p=e.data)||void 0===p?void 0:p.accessChannel),e.isRetry?(n++,e.data.subrequestid="".concat(n)):(c.requestid="req_".concat(a.Wp.getUniqueId(),"_").concat(_()),n=1),e.data.isLastApi){var h=g();e.data.requestElapse=h-i,e.data.requestMethod="",e.data.subrequestid="0",e.data.code=200===r?0:r,e.data.codeDesc=o}else e.data.requestName||(e.data.requestName=u);e.data.requestElapse||(e.data.requestElapse=g()-i),t.reportData.call(t,v.uN.API,A(A(A(A({},b),c),e.data),{accessChannel:t.accessChannel,operationType:m(u)})),e.isEndApi&&t.reportData.call(t,v.uN.API,A(A(A({},c),b),{isSuccess:e.data.isSuccess,isLastApi:1,subrequestid:"0",requestMethod:"",code:200===r?0:r,codeDesc:o,requestElapse:e.data.requestElapse,accessChannel:t.accessChannel,operationType:m(u)}))}},e.prototype.reportData=function(e,t){return n=this,r=void 0,i=function(){var n,r,o,i,s,u,c,l,d;return function(e,t){var n,r,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,r=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!((o=(o=s.trys).length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=t.call(e,s)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}}(this,(function(p){switch(p.label){case 0:return p.trys.push([0,3,,4]),n=g(),r=0,e!==v.uN.API?[3,2]:[4,(h=this.platform,new Promise((function(e){var t;h.platform||(h=a.Wp.getEnvInfo());var n="";if(h.platform===a.i9.WEB){var r=navigator.connection;(null==r?void 0:r.type)?(null==r||r.type,n=E.z1.WIFI):(null==r?void 0:r.effectiveType)&&(n=E.z1[r.effectiveType.toLocaleUpperCase()]),e(n)}else h.platform===a.i9.NODE?(n=E.z1.UNKNOWN,e(n)):null===(t=h.global)||void 0===t||t.getNetworkType({success:function(t){n=E.z1[t.networkType.toLocaleUpperCase()],e(n)}})})))];case 1:r=p.sent(),p.label=2;case 2:return o=this.options,i=o.appkey,s=o.sdkServiceId,u=o.sdkProduct,c=o.uikitVersion,l=A({lts:n,net:r,appkey:i,sdkServiceId:s,sdkProduct:u,uikitVersion:c},t),this.isReportDt?N.send(e,l):this.isCollectDt&&this.eventQueue.push({eventId:e,dt:l}),[3,4];case 3:return d=p.sent(),console.warn(d),[3,4];case 4:return[2]}var h}))},new((o=void 0)||(o=Promise))((function(e,t){function s(e){try{u(i.next(e))}catch(e){t(e)}}function a(e){try{u(i.throw(e))}catch(e){t(e)}}function u(t){var n;t.done?e(t.value):(n=t.value,n instanceof o?n:new o((function(e){e(n)}))).then(s,a)}u((i=i.apply(n,r||[])).next())}));var n,r,o,i},e}(),w=r(346),L=r(1531),U=r(7706),x=r(8801),D=r(8232);function P(e){return P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},P(e)}var k=function(){return k=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},k.apply(this,arguments)},H=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{u(r.next(e))}catch(e){i(e)}}function a(e){try{u(r.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}u((r=r.apply(e,t||[])).next())}))},F=function(e,t){var n,r,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,r=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!((o=(o=s.trys).length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=t.call(e,s)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}};t().util.Long=i(),t().configure();var B=t().Root.fromJSON({nested:{easemob:{nested:{pb:{nested:{MessageBody:{fields:{type:{type:"Type",id:1},from:{type:"JID",id:2},to:{type:"JID",id:3},contents:{rule:"repeated",type:"Content",id:4},ext:{rule:"repeated",type:"KeyValue",id:5},ackMessageId:{type:"uint64",id:6},msgConfig:{type:"MessageConfig",id:7},ackContent:{type:"string",id:8},meta:{type:"string",id:9},editMessageId:{type:"uint64",id:11},editScope:{type:"EditScope",id:12}},nested:{Content:{fields:{type:{type:"Type",id:1},text:{type:"string",id:2},latitude:{type:"double",id:3},longitude:{type:"double",id:4},address:{type:"string",id:5},displayName:{type:"string",id:6},remotePath:{type:"string",id:7},secretKey:{type:"string",id:8},fileLength:{type:"int32",id:9},action:{type:"string",id:10},params:{rule:"repeated",type:"KeyValue",id:11},duration:{type:"int32",id:12},size:{type:"Size",id:13},thumbnailRemotePath:{type:"string",id:14},thumbnailSecretKey:{type:"string",id:15},thumbnailDisplayName:{type:"string",id:16},thumbnailFileLength:{type:"int32",id:17},thumbnailSize:{type:"Size",id:18},customEvent:{type:"string",id:19},customExts:{rule:"repeated",type:"KeyValue",id:20},buildingName:{type:"string",id:21},subType:{type:"SubType",id:22},title:{type:"string",id:23},summary:{type:"string",id:24},combineLevel:{type:"int32",id:25}},nested:{Type:{values:{TEXT:0,IMAGE:1,VIDEO:2,LOCATION:3,VOICE:4,FILE:5,COMMAND:6,CUSTOM:7,COMBINE:8}},Size:{fields:{width:{type:"double",id:1},height:{type:"double",id:2}}},SubType:{values:{COMBINE:0,GIF:1}}}},Type:{values:{NORMAL:0,CHAT:1,GROUPCHAT:2,CHATROOM:3,READ_ACK:4,DELIVER_ACK:5,RECALL:6,CHANNEL_ACK:7,EDIT:8}},EditScope:{values:{BODY:1,BODY_AND_EXT:2}},MessageConfig:{fields:{allowGroupAck:{type:"bool",id:1}}}}},KeyValue:{oneofs:{value:{oneof:["varintValue","floatValue","doubleValue","stringValue"]}},fields:{key:{type:"string",id:1},type:{type:"ValueType",id:2},varintValue:{type:"int64",id:3},floatValue:{type:"float",id:4},doubleValue:{type:"double",id:5},stringValue:{type:"string",id:6}},nested:{ValueType:{values:{BOOL:1,INT:2,UINT:3,LLINT:4,FLOAT:5,DOUBLE:6,STRING:7,JSON_STRING:8}}}},JID:{fields:{appKey:{type:"string",id:1},name:{type:"string",id:2},domain:{type:"string",id:3},clientResource:{type:"string",id:4}}},ConferenceBody:{fields:{sessionId:{type:"string",id:1},operation:{type:"Operation",id:2},conferenceId:{type:"string",id:3},type:{type:"Type",id:4},content:{type:"string",id:5},network:{type:"string",id:6},version:{type:"string",id:7},identity:{type:"Identity",id:8},duration:{type:"string",id:9},peerName:{type:"string",id:10},endReason:{type:"EndReason",id:11},status:{type:"Status",id:12},isDirect:{type:"bool",id:13},controlType:{type:"StreamControlType",id:14},routeFlag:{type:"int32",id:15},routeKey:{type:"string",id:16}},nested:{Status:{fields:{errorCode:{type:"int32",id:1}}},Operation:{values:{JOIN:0,INITIATE:1,ACCEPT_INITIATE:2,ANSWER:3,TERMINATE:4,REMOVE:5,STREAM_CONTROL:6,MEDIA_REQUEST:7}},Type:{values:{VOICE:0,VIDEO:1}},Identity:{values:{CALLER:0,CALLEE:1}},EndReason:{values:{HANGUP:0,NORESPONSE:1,REJECT:2,BUSY:3,FAIL:4,UNSUPPORTED:5,OFFLINE:6}},StreamControlType:{values:{PAUSE_VOICE:0,RESUME_VOICE:1,PAUSE_VIDEO:2,RESUME_VIDEO:3}}}},MSync:{fields:{version:{type:"Version",id:1,options:{default:"MSYNC_V1"}},guid:{type:"JID",id:2},auth:{type:"string",id:3},compressAlgorimth:{type:"uint32",id:4},crypto:{type:"uint32",id:5},userAgent:{type:"string",id:6},pov:{type:"uint64",id:7},command:{type:"Command",id:8},deviceId:{type:"uint32",id:10},encryptType:{rule:"repeated",type:"EncryptType",id:11,options:{packed:!1}},encryptKey:{type:"string",id:12},payload:{type:"bytes",id:9}},nested:{Version:{values:{MSYNC_V1:0,MSYNC_V2:1}},Command:{values:{SYNC:0,UNREAD:1,NOTICE:2,PROVISION:3}}}},EncryptType:{values:{ENCRYPT_NONE:0,ENCRYPT_AES_128_CBC:1,ENCRYPT_AES_256_CBC:2}},CommSyncUL:{fields:{meta:{type:"Meta",id:1},key:{type:"uint64",id:2},queue:{type:"JID",id:3},isRoam:{type:"bool",id:4},lastFullRoamKey:{type:"uint64",id:5}}},CommSyncDL:{fields:{status:{type:"Status",id:1},metaId:{type:"uint64",id:2},serverId:{type:"uint64",id:3},metas:{rule:"repeated",type:"Meta",id:4},nextKey:{type:"uint64",id:5},queue:{type:"JID",id:6},isLast:{type:"bool",id:7},timestamp:{type:"uint64",id:8},isRoam:{type:"bool",id:9}}},CommNotice:{fields:{queue:{type:"JID",id:1}}},CommUnreadUL:{fields:{}},CommUnreadDL:{fields:{status:{type:"Status",id:1},unread:{rule:"repeated",type:"MetaQueue",id:2},timestamp:{type:"uint64",id:3}}},MetaQueue:{fields:{queue:{type:"JID",id:1},n:{type:"uint32",id:2}}},Meta:{fields:{id:{type:"uint64",id:1},from:{type:"JID",id:2},to:{type:"JID",id:3},timestamp:{type:"uint64",id:4},ns:{type:"NameSpace",id:5},payload:{type:"bytes",id:6},routetype:{type:"RouteType",id:7},ext:{rule:"repeated",type:"KeyValue",id:8},meta:{type:"bytes",id:9},directedUsers:{rule:"repeated",type:"string",id:10}},nested:{NameSpace:{values:{STATISTIC:0,CHAT:1,MUC:2,ROSTER:3,CONFERENCE:4,NOTIFY:5,QUERY:6}},RouteType:{values:{ROUTE_ALL:0,ROUTE_ONLINE:1,ROUTE_DIRECT:2}}}},Status:{fields:{errorCode:{type:"ErrorCode",id:1},reason:{type:"string",id:2},redirectInfo:{rule:"repeated",type:"RedirectInfo",id:3}},nested:{ErrorCode:{values:{OK:0,FAIL:1,UNAUTHORIZED:2,MISSING_PARAMETER:3,WRONG_PARAMETER:4,REDIRECT:5,TOKEN_EXPIRED:6,PERMISSION_DENIED:7,NO_ROUTE:8,UNKNOWN_COMMAND:9,PB_PARSER_ERROR:10,BIND_ANOTHER_DEVICE:11,IM_FORBIDDEN:12,TOO_MANY_DEVICES:13,PLATFORM_LIMIT:14,USER_MUTED:15,ENCRYPT_DISABLE:16,ENCRYPT_ENABLE:17,DECRYPT_FAILURE:18,PERMISSION_DENIED_EXTERNAL:19}}}},RedirectInfo:{fields:{host:{type:"string",id:1},port:{type:"uint32",id:2}}},Provision:{fields:{osType:{type:"OsType",id:1},version:{type:"string",id:2},networkType:{type:"NetworkType",id:3},appSign:{type:"string",id:4},compressType:{rule:"repeated",type:"CompressType",id:5,options:{packed:!1}},encryptType:{rule:"repeated",type:"EncryptType",id:6,options:{packed:!1}},encryptKey:{type:"string",id:7},status:{type:"Status",id:8},deviceUuid:{type:"string",id:9},isManualLogin:{type:"bool",id:10},password:{type:"string",id:11},deviceName:{type:"string",id:12},resource:{type:"string",id:13},auth:{type:"string",id:14},serviceId:{type:"string",id:16},actionVersion:{type:"string",id:17},authToken:{type:"string",id:18},osCustomValue:{type:"uint32",id:19},sessionId:{type:"string",id:20},reason:{type:"string",id:21}},nested:{OsType:{values:{OS_IOS:0,OS_ANDROID:1,OS_LINUX:2,OS_OSX:3,OS_WIN:4,OS_CUSTOM:5,OS_OTHER:16}},NetworkType:{values:{NETWORK_NONE:0,NETWORK_WIFI:1,NETWORK_4G:2,NETWORK_3G:3,NETWORK_2G:4,NETWORK_WIRE:5}},CompressType:{values:{COMPRESS_NONE:0,COMPRESS_ZLIB:1}}}},MUCBody:{fields:{mucId:{type:"JID",id:1},operation:{type:"Operation",id:2},from:{type:"JID",id:3},to:{rule:"repeated",type:"JID",id:4},setting:{type:"Setting",id:5},reason:{type:"string",id:6},isChatroom:{type:"bool",id:7},status:{type:"Status",id:8},isThread:{type:"bool",id:9},mucParentId:{type:"JID",id:10},mucName:{type:"string",id:11},eventInfo:{type:"EventInfo",id:12},mucMemberCount:{type:"int32",id:13},ext:{type:"string",id:14},leaveOtherRooms:{type:"bool",id:15},members:{rule:"repeated",type:"string",id:16}},nested:{Operation:{values:{CREATE:0,DESTROY:1,JOIN:2,LEAVE:3,APPLY:4,APPLY_ACCEPT:5,APPLY_DECLINE:6,INVITE:7,INVITE_ACCEPT:8,INVITE_DECLINE:9,KICK:10,GET_BLACKLIST:11,BAN:12,ALLOW:13,UPDATE:14,BLOCK:15,UNBLOCK:16,PRESENCE:17,ABSENCE:18,DIRECT_JOINED:19,ASSIGN_OWNER:20,ADD_ADMIN:21,REMOVE_ADMIN:22,ADD_MUTE:23,REMOVE_MUTE:24,UPDATE_ANNOUNCEMENT:25,DELETE_ANNOUNCEMENT:26,UPLOAD_FILE:27,DELETE_FILE:28,ADD_USER_WHITE_LIST:29,REMOVE_USER_WHITE_LIST:30,BAN_GROUP:31,REMOVE_BAN_GROUP:32,THREAD_CREATE:33,THREAD_DESTROY:34,THREAD_JOIN:35,THREAD_LEAVE:36,THREAD_KICK:37,THREAD_UPDATE:38,THREAD_PRESENCE:39,THREAD_ABSENCE:40,DISABLE_GROUP:41,ABLE_GROUP:42,SET_METADATA:43,DELETE_METADATA:44,GROUP_MEMBER_METADATA_UPDATE:45}},Setting:{fields:{name:{type:"string",id:1},desc:{type:"string",id:2},type:{type:"Type",id:3},maxUsers:{type:"int32",id:4},owner:{type:"string",id:5}},nested:{Type:{values:{PRIVATE_OWNER_INVITE:0,PRIVATE_MEMBER_INVITE:1,PUBLIC_JOIN_APPROVAL:2,PUBLIC_JOIN_OPEN:3,PUBLIC_ANONYMOUS:4}}}},Status:{fields:{errorCode:{type:"ErrorCode",id:1},description:{type:"string",id:2},chatroomInfo:{type:"string",id:3}},nested:{ErrorCode:{values:{OK:0,PERMISSION_DENIED:1,WRONG_PARAMETER:2,MUC_NOT_EXIST:3,USER_NOT_EXIST:4,UNKNOWN:5}}}},EventInfo:{fields:{eventType:{type:"EventType",id:1,options:{default:"EVENT_NONE"}},ext:{type:"string",id:2}},nested:{EventType:{values:{EVENT_NONE:0,CIRCLE_CHANNEL:1}}}}}},RosterBody:{fields:{operation:{type:"Operation",id:1},status:{type:"Status",id:2},from:{type:"JID",id:3},to:{rule:"repeated",type:"JID",id:4},reason:{type:"string",id:5},rosterVer:{type:"string",id:6},biDirection:{type:"bool",id:7}},nested:{Operation:{values:{GET_ROSTER:0,GET_BLACKLIST:1,ADD:2,REMOVE:3,ACCEPT:4,DECLINE:5,BAN:6,ALLOW:7,REMOTE_ACCEPT:8,REMOTE_DECLINE:9}},Status:{fields:{errorCode:{type:"ErrorCode",id:1},description:{type:"string",id:2}},nested:{ErrorCode:{values:{OK:0,USER_NOT_EXIST:1,USER_ALREADY_FRIEND:2,USER_ALREADY_BLACKLIST:3}}}}}},StatisticsBody:{fields:{operation:{type:"Operation",id:1},os:{type:"OsType",id:2},version:{type:"string",id:3},network:{type:"NetworkType",id:4},imTime:{type:"uint32",id:5},chatTime:{type:"uint32",id:6},location:{type:"string",id:7},reason:{type:"string",id:10}},nested:{Operation:{values:{INFORMATION:0,USER_REMOVED:1,USER_LOGIN_ANOTHER_DEVICE:2,USER_KICKED_BY_CHANGE_PASSWORD:3,USER_KICKED_BY_OTHER_DEVICE:4}},OsType:{values:{OS_IOS:0,OS_ANDROID:1,OS_LINUX:2,OS_OSX:3,OS_WIN:4,OS_CUSTOM:5,OS_OTHER:16}},NetworkType:{values:{NETWORK_NONE:0,NETWORK_WIFI:1,NETWORK_4G:2,NETWORK_3G:3,NETWORK_2G:4,NETWORK_WIRE:5}}}}}}}}}}),G=function(){function e(e){var t,n,r,o=this;if(this.name="miniCore",this.appKey="",this.heartBeatWait=O.HJ,this.Message=s.Message,this.Code=L.C,this.getUniqueId=a.LP,this._queues=[],this._load_msg_cache=[],this.max_cache_length=100,this.isReport=!1,this.dnsIndex=0,this.autoReconnectNumTotal=0,this.times=1,this._reportLogs=!1,this._reportInterval=O.Rk,this._isLogging=!1,this._initWithAppId=!1,this.lastHeartbeat=Date.now(),this.logger=this.getLogger(),this.url=null!==(t=e.url)&&void 0!==t?t:"",this.apiUrl=null!==(n=e.apiUrl)&&void 0!==n?n:"",this.isHttpDNS=!!e.isHttpDNS,this.uploadPartEnable=!0,this.root=B,this.mr_cache={},this.isHttpDNS=void 0===e.isHttpDNS||e.isHttpDNS,this.https=a.Wp.isUseHttps(),this.delivery=e.delivery||!1,this.restIndex=0,this.hostIndex=0,this.autoReconnectNumMax=e.autoReconnectNumMax||O.WR,this.refreshDNSIntervals=this.autoReconnectNumMax<5?this.autoReconnectNumMax:5,this.useReplacedMessageContents=e.useReplacedMessageContents||!1,this.unMSyncSendMsgMap=new Map,this.socketHost=[],this.reconnecting=!1,this._offlineMessagePullState=U.P.SYNC_INIT,this._offlineMessagePullQueue=[],this.dnsArr=["https://rs.easemob.com","https://rs.chat.agora.io","http://************","http://*************","http://*************"].filter((function(e){return e.startsWith(o.https?"https":"")})),this.dnsTotal=this.dnsArr.length,this.version="".concat("4.15.1",".1")||0,this.deviceId=e.deviceId||"webim",this.isFixedDeviceId=null===(r=e.isFixedDeviceId)||void 0===r||r,this.logOut=!0,this.osType=16,this.useOwnUploadFun=e.useOwnUploadFun||!1,this.compressType=[0],this.encryptType=[0],this._msgHash={},this.disconnectedReason=void 0,this.loginInfoCustomExt=void 0,this.appName="",this.orgName="",this.context={jid:{appKey:"",clientResource:"",domain:"easemob.com",name:""},userId:"",appKey:"",status:0,restTokenData:"",appName:"",orgName:"",root:{},accessToken:""},this.dnsTotal=this.dnsArr.length,"appKey"in e&&(this.appKey=e.appKey),"appId"in e&&(this.appId=e.appId,this._initWithAppId=!0),this.mSync=this.usePlugin(c.Ay),this.eventHandler=this.usePlugin(d),void 0!==e.customOSPlatform){if("number"!=typeof e.customOSPlatform||e.customOSPlatform<1||e.customOSPlatform>100)throw Error("Invalid customOSPlatform: ".concat(e.customOSPlatform));this.customOSPlatform=e.customOSPlatform,this.deviceId=e.customDeviceName||this.deviceId}if(this._initWithAppId){if(this.appKey="","string"!=typeof this.appId||!this.appId)throw Error("Invalid appId: ".concat(this.appId))}else{if("string"!=typeof this.appKey||2!==this.appKey.split("#").length)throw Error("Invalid appKey: ".concat(this.appKey));this.devInfos=this.appKey.split("#"),this.orgName=this.devInfos[0],this.appName=this.devInfos[1]}if(this.dataReport=new M({appkey:this.appKey||"default#appkey",org:this.orgName,sdkVersion:this.version,deviceId:this.deviceId,isReport:this.isReport,uikitVersion:e.uikitVersion||""}),this.isFixedDeviceId){var i=a.Wp.getLocalDeviceInfo();i&&(this.clientResource=JSON.parse(i).deviceId)}u.vF.debug("init SDK: miniCore ".concat(this.version))}return e.prototype.addEventHandler=function(e,t){},e.prototype.removeEventHandler=function(e){},e.prototype.usePlugin=function(e,t){var n=this;if("function"==typeof e){if(!t)return new e(this);this[t]=new e(this)}else"object"===P(e)&&(t?(this[t]={},e.LocalCache&&(this._localCache=e.LocalCache,e=e.LocalCacheApi),Object.keys(e).forEach((function(r){var o,i;n[t][r]=null===(i=null===(o=e[r])||void 0===o?void 0:o.bind)||void 0===i?void 0:i.call(o,n)}))):Object.assign(this,e))},e.prototype.isOpened=function(){return this.sock&&1===this.sock.readyState||!1},e.prototype._validateOpenParams=function(e){if("object"!==P(e)||null===e)return w.A.create({type:L.C.WEBIM_CONNCTION_OPEN_ERROR,message:"the param is illegal"});if(!e.username||"string"!=typeof e.username){var t=w.A.create({type:L.C.WEBIM_CONNCTION_USER_NOT_ASSIGN_ERROR,message:"the username cannot be empty"});return u.vF.debug("open params error",t),t}return!("agoraToken"in e)||e.agoraToken&&"string"==typeof e.agoraToken?!("accessToken"in e)||e.accessToken&&"string"==typeof e.accessToken?"accessToken"in e||"agoraToken"in e||e.password?void 0:(t=w.A.create({type:L.C.WEBIM_CONNCTION_OPEN_ERROR,message:"the accessToken or password is illegal"}),u.vF.debug("open params error",t),t):(t=w.A.create({type:L.C.WEBIM_CONNCTION_OPEN_ERROR,message:"the accessToken is illegal"}),u.vF.debug("open params error",t),t):(t=w.A.create({type:L.C.WEBIM_CONNCTION_OPEN_ERROR,message:"the agoraToken is illegal"}),u.vF.debug("open params error",t),console.warn("agoraToken is deprecated, please use accessToken instead"),t)},e.prototype.open=function(e){var t;return H(this,void 0,void 0,(function(){var n,r,o,i,s,c;return F(this,(function(l){switch(l.label){case 0:if(u.vF.debug("open",e.username),this._isLogging)throw w.A.create({type:L.C.WEBIM_CONNCTION_OPEN_ERROR,message:"Currently logging in, please wait."});if(!this.logOut)throw w.A.create({type:L.C.WEBIM_USER_ALREADY_LOGIN,message:"The user has logged in."});if(this.retryConnectTimes=0,this._isLogging=!0,n=this._validateOpenParams(e))throw n;r=a.Wp.getEnvInfo(),l.label=1;case 1:if(l.trys.push([1,4,5,6]),"web"===r.platform&&(o=a.Wp.detectBrowser(),u.vF.debug("browser",o)),u.vF.debug("socket readyState",null===(t=this.sock)||void 0===t?void 0:t.readyState),this.isOpened.call(this))throw w.A.create({type:L.C.WEBIM_CONNCTION_OPEN_ERROR,message:"You are already logged in"});return i=this.dataReport.geOperateFun({uid:e.username,operationName:E.jz.LOGIN}),[4,this._open(e,!1,i)];case 2:return s=l.sent(),u.vF.initReport({report:this._reportLogs,reportInterval:this._reportInterval,connection:this}),[4,this.retryConnectMSync(i)];case 3:return l.sent(),[2,s];case 4:throw c=l.sent(),this._isLogging=!1,c;case 5:return u.vF.reportLog(),[7];case 6:return[2]}}))}))},e.prototype._open=function(e,t,n){return void 0===t&&(t=!1),H(this,void 0,void 0,(function(){var r,o,i,s,c,l,d,p,h,f,v,m,g;return F(this,(function(y){switch(y.label){case 0:return this.setContext(e),!this.isHttpDNS||t?[3,2]:(this.dnsIndex=0,[4,this.getDNS({rpt:n,isRetry:!!t})]);case 1:y.sent(),y.label=2;case 2:return this.setContext(e),r=this.apiUrl+"/"+this.orgName+"/"+this.appName+"/token",e.agoraToken?(u.vF.debug("login with agoraToken"),this.grantType="agoraToken",this.token=e.agoraToken,this.context.accessToken=e.agoraToken,this.context.restTokenData=e.agoraToken,this.user=e.username,[2,{accessToken:e.agoraToken}]):[3,3];case 3:return e.accessToken?(u.vF.debug("login with accessToken"),this.grantType="accessToken",this.token=e.accessToken,this.context.accessToken=e.accessToken,this.context.restTokenData=e.accessToken,this.user=e.username,[2,{accessToken:e.accessToken}]):[3,4];case 4:return u.vF.debug("login with password"),o={headers:{"Content-type":"application/json"},url:r,dataType:"json",data:JSON.stringify({grant_type:"password",username:e.username,password:e.password,timestamp:+new Date})},[4,W(a.Wp.ajax(o,E.jz.SDK_INTERNAL))];case 5:return i=y.sent(),s=i[0],c=i[1],s?(l=w.A.create({type:L.C.WEBIM_CONNCTION_OPEN_ERROR,message:s.error_description||s.message,data:s}),d=(null==s?void 0:s.extraInfo)||{},m=d.elapse,v=d.httpCode,p=d.errDesc,h={requestName:E.TT.LOGIN_BY_PWD,requestElapse:m,requestUrl:r,isSuccess:0,code:v,codeDesc:p},n&&n({isRetry:!1,data:h}),"invalid password"!==s.message&&"user not found"!==s.message&&this.isHttpDNS&&this.restIndex+1<this.restTotal?(this.restIndex++,this.setRestUrl(),[4,this._open(e,!0,n)]):[3,7]):[3,8];case 6:return[2,y.sent()];case 7:throw n&&n({data:{isLastApi:1,isSuccess:0}}),this.clear(),delete s.extraInfo,l;case 8:if(!c)throw new Error("get token fail.");return this.expiresIn=c.expires_in,this.grantType="password",this.token=c.access_token,this.user=e.username,this.context.accessToken=c.access_token,c.extraInfo&&(f=c.extraInfo,v=f.httpCode,m=f.elapse,g={requestName:E.TT.LOGIN_BY_PWD,requestElapse:m,requestUrl:r,isSuccess:1,code:v},n&&n({isRetry:!1,data:g})),[2,{accessToken:c.access_token,duration:c.expires_in}]}}))}))},e.prototype.retryConnectMSync=function(e){var t,n,r;return H(this,void 0,void 0,(function(){var o,i,s,a;return F(this,(function(c){switch(c.label){case 0:o=(new Date).getTime(),c.label=1;case 1:return c.trys.push([1,3,,7]),[4,this.connectMSync.call(this,e)];case 2:return i=c.sent(),e&&e({data:{isLastApi:1,isSuccess:1,accessChannel:null===(t=this.socketHost[this.hostIndex])||void 0===t?void 0:t.channel}}),[2,i];case 3:if(s=c.sent(),u.vF.error("connect to msync failed times:",this.retryConnectTimes+1,s),O.Ec.includes(s.type))throw e&&e({data:{isLastApi:1,isSuccess:0,accessChannel:null===(n=this.socketHost[this.hostIndex])||void 0===n?void 0:n.channel,codeDesc:this.disconnectedReason&&JSON.stringify(this.disconnectedReason)||s.message}}),s;return this.retryConnectTimes++,this.retryConnectTimes<O.rO?(this.isHttpDNS&&(this.hostIndex<this.socketHost.length-1?this.hostIndex++:this.hostIndex=this.socketHost.length-1,this.setSocketUrl()),s.type===L.C.REQUEST_TIMEOUT&&"provision timeout"===s.message||(a=(new Date).getTime()-o,null==e||e({isRetry:1!==this.retryConnectTimes,data:{requestUrl:this.url,requestName:E.TT.CONNECT_WEBSOCKET,isSuccess:0,code:E.G8.closed,requestElapse:a,codeDesc:this.disconnectedReason&&JSON.stringify(this.disconnectedReason)||"websocket close"}})),[4,this.retryConnectMSync(e)]):[3,5];case 4:return c.sent(),[3,6];case 5:throw u.vF.error("connect to msync failed",s),e&&e({data:{isLastApi:1,isSuccess:0,accessChannel:null===(r=this.socketHost[this.hostIndex])||void 0===r?void 0:r.channel,codeDesc:this.disconnectedReason&&JSON.stringify(this.disconnectedReason)||s.message}}),s;case 6:return[3,7];case 7:return[2]}}))}))},e.prototype.connectMSync=function(e){var t=this,n="pending",r=(new Date).getTime();return new Promise((function(o,i){t.connectionResolve=o,t.connectionReject=i;var s=new WebSocket(t.url);s.binaryType="arraybuffer",t.sock=s,t.connectionTimer&&clearTimeout(t.connectionTimer),t.connectionTimer=setTimeout((function(){t.disconnectedReason={type:L.C.REQUEST_TIMEOUT,message:"connection timeout"},s.close(),null==i||i(t.disconnectedReason)}),O.x0),s.onopen=function(){if(t.connectionTimer&&clearTimeout(t.connectionTimer),e){var n=(new Date).getTime()-r;e({isRetry:0!==t.retryConnectTimes,data:{requestUrl:t.url,requestName:E.TT.CONNECT_WEBSOCKET,isSuccess:1,code:E.G8.success,requestElapse:n}})}(0,a.En)(t.online.bind(t),t.offline.bind(t)),(0,a.X0)((function(){u.vF.debug("visibility true")}),(function(){u.vF.debug("visibility false")}));var o=t.mSync.generateProvision();t.provisionTimer&&clearTimeout(t.provisionTimer),t.provisionTimer=setTimeout((function(){u.vF.debug("provision timeout"),t.disconnectedReason=w.A.create({type:L.C.REQUEST_TIMEOUT,message:"provision timeout"}),s.close(),null==i||i(t.disconnectedReason)}),O.Qb),s.send(o)},s.onclose=function(e){var r,o,s,a,c,l,d;if(t.connectionTimer&&clearTimeout(t.connectionTimer),u.vF.debug("websocket onclose, isLogging:",t._isLogging),t._isLogging)return null==i||i({type:L.C.WEBIM_CONNCTION_DISCONNECTED,message:"websocket has been disconnected"}),void(n="rejected");"rejected"!==n?(n="rejected",t.logOut?(t.clear(),null===(o=null===(r=null==t?void 0:t._localCache)||void 0===r?void 0:r.getInstance())||void 0===o||o.close(),null===(s=t.eventHandler)||void 0===s||s.dispatch("onDisconnected",t.disconnectedReason),t.resetConnState(),null==i||i({type:L.C.WEBIM_CONNCTION_DISCONNECTED,message:"logout"})):t.autoReconnectNumTotal<t.autoReconnectNumMax?(t.reconnect(),t.autoReconnectNumTotal%t.refreshDNSIntervals===0&&t.isHttpDNS&&(u.vF.debug("refresh dns config when websocket close"),t.getDNS({isRetry:!1}).catch((function(e){u.vF.debug("get dns error",e)})))):(t.disconnectedReason=w.A.create({type:L.C.WEBIM_CONNCTION_DISCONNECTED,message:"reconnection failed"}),null===(a=t.unMSyncSendMsgMap)||void 0===a||a.clear(),t.rejectMessage(),null===(l=null===(c=null==t?void 0:t._localCache)||void 0===c?void 0:c.getInstance())||void 0===l||l.close(),null===(d=t.eventHandler)||void 0===d||d.dispatch("onDisconnected",t.disconnectedReason),t.resetConnState(),t.reconnecting=!1,null==i||i(t.disconnectedReason),u.vF.debug("reconnect fail"))):u.vF.debug("onclose reject is null")},s.onmessage=function(e){var n=t.mSync,r=n.decodeMSync,o=n.distributeMSync;a.Wp.flow([r,o])(e,!1)},s.onerror=function(r){var o,s,a,c;if(t.connectionTimer&&clearTimeout(t.connectionTimer),u.vF.debug("websocket onerror, isLogging:",t._isLogging),t._isLogging)return null==i||i({type:L.C.WEBIM_CONNCTION_DISCONNECTED,message:"websocket has been disconnected"}),void(n="rejected");"rejected"!==n?(n="rejected",t.logOut||(t.autoReconnectNumTotal<t.autoReconnectNumMax?(u.vF.debug("sock.onError reconnect",t.autoReconnectNumTotal,t.autoReconnectNumMax),t.reconnect(),t.autoReconnectNumTotal%t.refreshDNSIntervals===0&&t.isHttpDNS&&(u.vF.debug("refresh dns config when websocket error"),t.getDNS({rpt:e,isRetry:!1}).catch((function(e){u.vF.debug("get dns error",e)})))):(t.disconnectedReason=w.A.create({type:L.C.WEBIM_CONNCTION_DISCONNECTED,message:"reconnection failed"}),null===(o=t.unMSyncSendMsgMap)||void 0===o||o.clear(),t.rejectMessage(),null===(a=null===(s=null==t?void 0:t._localCache)||void 0===s?void 0:s.getInstance())||void 0===a||a.close(),null===(c=t.eventHandler)||void 0===c||c.dispatch("onDisconnected",t.disconnectedReason),t.resetConnState(),t.reconnecting=!1,u.vF.debug("reconnect fail"),null==i||i(t.disconnectedReason)))):u.vF.debug("onerror reject is null")}}))},e.prototype.close=function(){var e,t,n;u.vF.debug("call close"),u.vF._stopReportLogs(),this.logOut=!0,this.disconnectedReason=void 0,this.reconnecting=!1,this.context.status=L.C.STATUS_CLOSING,this.sock&&this.sock.close(),null===(e=this.unMSyncSendMsgMap)||void 0===e||e.clear(),this.stopHeartBeat(),this.rejectMessage(),this.context.status=L.C.STATUS_CLOSED,this._load_msg_cache=[],this._queues=[],this._msgHash={},this.mr_cache={},this.token="",this.context.accessToken="",this.clearTokenTimeout(),null===(n=null===(t=this._localCache)||void 0===t?void 0:t.getInstance())||void 0===n||n.close(),this.connectionTimer&&clearTimeout(this.connectionTimer),this.provisionTimer&&clearTimeout(this.provisionTimer),this.probingTimer&&clearTimeout(this.probingTimer)},e.prototype.registerUser=function(e){return H(this,void 0,void 0,(function(){var t,n,r,o,i,s,u,c,l,d,p,h,f,v;return F(this,(function(m){switch(m.label){case 0:return t=this.dataReport.geOperateFun({uid:e.username,operationName:E.jz.REGISTER}),this.isHttpDNS?(this.dnsIndex=0,[4,this.getDNS({rpt:t,isRetry:!1})]):[3,2];case 1:m.sent(),m.label=2;case 2:return n=this.apiUrl,r=n+"/"+this.orgName+"/"+this.appName+"/users",o=JSON.stringify({username:e.username,password:e.password,nickname:e.nickname||""}),i={headers:{"Content-type":"application/json"},url:r,dataType:"json",data:o},[4,W(a.Wp.ajax(i,E.jz.SDK_INTERNAL))];case 3:return s=m.sent(),u=s[0],c=s[1],l={requestName:E.TT.RESISTER,requestUrl:r},u?(d=u.extraInfo||{},f=d.httpCode,v=d.elapse,p=d.errDesc,t({isRetry:!1,data:k(k({},{requestElapse:v,isSuccess:0,code:f,codeDesc:p}),l)}),!(u.message.includes("Open registration doesn't allow")||u.message.includes("username be unique")||u.message.includes("is not legal"))&&this.isHttpDNS&&this.restIndex+1<this.restTotal?(this.restIndex++,this.setRestUrl(),[4,this.registerUser(e)]):[3,5]):[3,7];case 4:return[2,m.sent()];case 5:throw t({data:{isLastApi:1,isSuccess:0}}),delete u.extraInfo,u;case 6:return[3,8];case 7:if(!c)throw new Error("register user fail.");return h=c.extraInfo||{},f=h.httpCode,v=h.elapse,t({isEndApi:!0,isRetry:!1,data:k(k({},{requestElapse:v,isSuccess:1,code:f}),l)}),[2,{data:null==c?void 0:c.entities[0],type:0}];case 8:return[2]}}))}))},e.prototype.renewToken=function(e){var t=this,n=this.isOpened();return u.vF.debug("isOpened",n),n?this.getTokenExpireTimestamp(e).then((function(n){var r=n.expire_timestamp,o=Date.now();return t.expirationTime=r,t.expiresIn=r-o,t.token=e,t.context.accessToken=e,t.clearTokenTimeout(),t.tokenExpireTimeCountDown(t.expiresIn),{status:!0,token:e,expire:n.expire_timestamp}})):Promise.reject({status:!1})},e.prototype.send=function(e){return Promise.resolve(null)},e.prototype.getTokenExpireTimestamp=function(e){if("string"!=typeof e||""===e)throw Error('Invalid parameter: "token"');var t=this.context,n=t.orgName,r=t.appName,o={url:"".concat(this.apiUrl,"/").concat(n,"/").concat(r,"/sdk/users/").concat(this.user,"/token/expires"),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+e,"Content-Type":"application/json"}};return u.vF.debug("Call getTokenExpireTimestamp",e),a.Wp.ajax(o,E.jz.SDK_INTERNAL)},e.prototype.clearTokenTimeout=function(){u.vF.debug("clearTokenTimeout"),this.tokenWillExpireTimer&&clearTimeout(this.tokenWillExpireTimer),this.tokenExpiredTimer&&clearTimeout(this.tokenExpiredTimer),this.tokenWillExpireTimer=null,this.tokenExpiredTimer=null},e.prototype.compareTokenExpireTime=function(e,t){var n,r=Number(t)-Number(e);u.vF.debug("compareTokenExpireTime",r),r<=this.expiresIn/5&&r>0?(null===(n=this.eventHandler)||void 0===n||n.dispatch("onTokenWillExpire"),u.vF.info("onTokenWillExpire",r)):r<=0&&(this.closeByTokenExpired(),u.vF.info("closeByTokenExpired",r))},e.prototype.closeByTokenExpired=function(){var e;u.vF.info("closed By TokenExpired"),null===(e=this.eventHandler)||void 0===e||e.dispatch("onTokenExpired"),this.close()},e.prototype.tokenExpireTimeCountDown=function(e){var t=this;e>Math.pow(2,31)-1&&(e=Math.pow(2,31)-1),this.tokenWillExpireTimer=setTimeout((function(){var n;null===(n=t.eventHandler)||void 0===n||n.dispatch("onTokenWillExpire"),u.vF.info("onTokenWillExpire",Math.floor(e-e/5))}),e-e/5),this.tokenExpiredTimer=setTimeout((function(){var e;u.vF.info("onTokenExpired",0),null===(e=t.eventHandler)||void 0===e||e.dispatch("onTokenExpired"),t.close()}),e)},e.prototype.stopHeartBeat=function(){clearInterval(this.heartBeatID)},e.prototype.setContext=function(e){this.context.jid={appKey:this.appKey,name:e.username,domain:this.context.jid.domain,clientResource:this.clientResource},this.context.root=this.root,this.context.userId=e.username,this.context.appKey=this.appKey,this.context.appName=this.appName,this.context.orgName=this.orgName},e.prototype.getDNS=function(e){var t;return H(this,void 0,void 0,(function(){var n,r,o,i,s,c,l,d,p,h,f,v,m,g,y,_,T,R,I,C,S,N;return F(this,(function(A){switch(A.label){case 0:return n=e.rpt,r=e.isRetry,o=this.dnsIndex<this.dnsTotal?this.dnsIndex:0,i="".concat(this.dnsArr[o],"/easemob/server.json"),s={url:i,dataType:"json",type:"GET",data:{}},this._initWithAppId?s.data={app_id:this.appId,sdk_version:"4.10.0",file_version:"1"}:s.data={app_key:encodeURIComponent(this.appKey)},[4,W(a.Wp.ajax(s,E.jz.SDK_INTERNAL))];case 1:return c=A.sent(),l=c[0],d=c[1],l?(p=l.extraInfo||{},h=p.elapse,f=p.httpCode,v=p.errDesc,m={requestUrl:i,requestName:E.TT.GET_DNS,isSuccess:0,code:f,codeDesc:v,requestElapse:h},n&&n({isRetry:r,data:m}),u.vF.debug("getHttpDNS failed: "+this.dnsIndex),this.dnsIndex++,this.dnsIndex<this.dnsTotal?[4,this.getDNS({rpt:n,isRetry:!0})]:[3,3]):[3,4];case 2:return A.sent(),[2];case 3:throw w.A.create({type:L.C.SERVER_GET_DNSLIST_FAILED,message:"get DNS failed",data:l});case 4:if(!d||!(null===(t=d.rest)||void 0===t?void 0:t.hosts)||!d["msync-wx"])throw w.A.create({type:L.C.SERVER_GET_DNSLIST_FAILED,message:"DNS hosts resolution failed",data:d});if(u.vF.debug("getHttpDNS success: "+this.dnsIndex),g=d.extraInfo||{},y=g.elapse,_=g.httpCode,this._reportLogs="true"===d.enableReportLogs,this._reportInterval=Number(d.reportInterval||O.Rk),T={requestUrl:i,requestName:E.TT.GET_DNS,requestElapse:y,isSuccess:1,code:_},n&&n({isRetry:r,data:T}),R=d.rest.hosts,I=this.https?"https":"http",this._initWithAppId){if(!d.app_key)throw w.A.create({type:L.C.SERVER_GET_DNSLIST_FAILED,message:"get DNS failed"});this.appKey=d.app_key,this.context.appKey=this.appKey,this.devInfos=this.appKey.split("#"),this.orgName=this.devInfos[0],this.appName=this.devInfos[1],this.dataReport.updateAppKey(this.appKey)}return C=R.filter((function(e){if(e.protocol===I)return e})),this.restHosts=C,this.restTotal=this.restHosts.length,S=d["msync-wx"].hosts,N=S.filter((function(e){if(e.protocol===I)return e})),this.socketHost=N,"true"===(null==d?void 0:d.enableDataReport)?(this.dataReport.setIsCollectDt(!0),this.dataReport.setIsReportDt(!0)):(this.dataReport.setIsReportDt(!1),this.dataReport.setIsCollectDt(!1)),"false"===(null==d?void 0:d.uploadinparts_enable)&&(this.uploadPartEnable=!1),this.setRestUrl(),this.setSocketUrl(),[2]}}))}))},e.prototype.setRestUrl=function(){var e,t;if(this.restIndex>this.restHosts.length)return u.vF.debug("restIndex > restTotal"),"";var n="",r=this.restHosts[this.restIndex],o=r.domain,i=r.ip,s=r.port,a=this.https?"https:":"http:";return i&&"undefined"!=typeof window&&"http:"===(null===(e=null===window||void 0===window?void 0:window.location)||void 0===e?void 0:e.protocol)?n=a+"//"+i+":"+s:(n=a+"//"+o,s&&"80"!==s&&"443"!==s&&(n+=":".concat(s)),"undefined"==typeof window||window.location||(n="https://"+o),"undefined"!=typeof window&&window.location&&"file:"===(null===(t=window.location)||void 0===t?void 0:t.protocol)&&(n="https://"+o)),"undefined"==typeof window&&(n=a+"//"+o),this.apiUrl=n,n},e.prototype.setSocketUrl=function(){var e=this.socketHost[this.hostIndex],t=e.domain,n=e.ip,r=e.port,o="";o=t||n,r&&"80"!==r&&"443"!==r&&(o+=":"+r),o&&(this.url="//"+o+"/websocket");var i=this.https?"wss:":"ws:";this.url=i+this.url},e.prototype.online=function(){var e;u.vF.debug("online"),null===(e=this.eventHandler)||void 0===e||e.dispatch("onOnline"),this.sock&&1!==this.sock.readyState&&(u.vF.debug("sock.readyState:",this.sock.readyState),this.logOut||this.reconnecting||this.reconnect())},e.prototype.offline=function(){var e,t;u.vF.debug("offline"),null===(e=this.sock)||void 0===e||e.close(),null===(t=this.eventHandler)||void 0===t||t.dispatch("onOffline")},e.prototype.getLogger=function(){var e=u.vF.getLogger("defaultLogger");return e.setConfig({useCache:!1,maxCache:3145728,color:"",background:""}),e.enableAll(),e},e.prototype.rejectMessage=function(){var e=this,t=Object.keys(this._msgHash);if(t.length>0){var n=w.A.create({type:L.C.MESSAGE_WEBSOCKET_DISCONNECTED,message:"websocket disconnected"});t.forEach((function(t){var r,o,i;(null===(r=e.unMSyncSendMsgMap)||void 0===r?void 0:r.has(t))||(e._msgHash[t].reject instanceof Function&&e._msgHash[t].reject(n),null===(i=null===(o=e._localCache)||void 0===o?void 0:o.getInstance())||void 0===i||i.updateLocalMessage(t,{serverMsgId:t,status:D.q.FAIL}),e._msgHash[t].fail instanceof Function&&e._msgHash[t].fail(n),e._msgHash[t].messageTimer&&clearTimeout(e._msgHash[t].messageTimer),delete e._msgHash[t])}))}},e.prototype.resetConnState=function(){this.mSync.stopHeartBeat(),this.times=1,this.autoReconnectNumTotal=0,this.hostIndex=0},e.prototype.reconnect=function(e){var t,n=this;if(this.logout)return u.vF.warn("The user has already logged out when reconnecting");if(u.vF.debug("socket reconnect readyState",this.sock.readyState),0!==this.sock.readyState&&1!==this.sock.readyState||e){!1===this.reconnecting&&(this.reconnecting=!0),null===(t=this.eventHandler)||void 0===t||t.dispatch("onReconnecting"),u.vF.info("reconnect times: ",this.times),u.vF.info("reconnect sock.readyState: ",this.sock.readyState),this.rejectMessage(),this.isHttpDNS&&(this.hostIndex<this.socketHost.length-1?this.hostIndex++:this.hostIndex=this.socketHost.length-1);var r=1e3*a.Wp.getRetryDelay(this.times);u.vF.info("reconnect delay: ",r),setTimeout((function(){(1!==n.sock.readyState||e)&&(u.vF.info("login sock.readyState: ",n.sock.readyState),n.sock.close(),n.connectMSync(),n.times++)}),r),this.autoReconnectNumTotal++}},e.prototype.clear=function(){this.restTotal=0,this.restIndex=0,this.hostIndex=0,this.hostTotal=0},e.prototype.onShow=function(){var e=this;u.vF.debug("execute onshow callback"),!this.logOut&&!this.reconnecting&&Date.now()-this.lastHeartbeat>2e3&&(u.vF.debug("send ping"),this.mSync.sendUnreadDeal(),this.probingTimer&&clearTimeout(this.probingTimer),this.probingTimer=setTimeout((function(){u.vF.error("Websocket connection timeout"),e.logOut||e.reconnecting||e.reconnect(!0)}),O.oi))},e.prototype.downloadAndParseCombineMessage=function(e){var t=this,n=e.url,r=e.secret;return new Promise((function(e,o){var i,s,c=a.Wp.getEnvInfo(),l="web"!==c.platform&&"node"!==c.platform&&"quick_app"!==c.platform&&(null===(s=null===(i=c.global)||void 0===i?void 0:i.canIUse)||void 0===s?void 0:s.call(i,"getFileSystemManager")),d=function(n){var r=function(e){o({message:"Read file failed",data:e})},i=function(n){return H(t,void 0,void 0,(function(){var t,r,i,s,c,d,p,h,f,v,E,m,g;return F(this,(function(y){switch(y.label){case 0:for(t=l?new Uint8Array(n.data):new Uint8Array(n.target.result),r=0,i=0,s=2,c=t.subarray(r,r+s),d=a.Wp.Uint8ArrayToString(c),u.vF.debug("file header:",d),i+=s,p=0,h=2;h<t.length-1;h++)h%2==1&&(p^=t[h]);if(f=t.subarray(t.length-1,t.length),u.vF.debug("checkResult:",p,f[0]===p),f[0]!==p)return[2,o({message:"File verification failed"})];if("cm"!==d)return[3,7];y.label=1;case 1:y.trys.push([1,5,,6]),v=[],E=function(){var e,n,o,a;return F(this,(function(u){switch(u.label){case 0:return r+=s,i+=s=4,e=t.subarray(r,r+s),r+=s,s=e.reduce((function(t,n,r){return t+(n<<8*(e.length-r-1))}),0),i+=s,n=t.subarray(r,r+s),o=(o=m.root.lookup("easemob.pb.Meta")).decode(n),[4,x.Ay.call(m,o,0,!0,!0)];case 1:return a=u.sent(),v.push(a),[2]}}))},m=this,y.label=2;case 2:return i<t.length-1?[5,E()]:[3,4];case 3:return y.sent(),[3,2];case 4:return[2,e(v)];case 5:return g=y.sent(),o({message:"Parse file failed",data:g}),[3,6];case 6:return[3,8];case 7:return[2,o({message:"File verification failed"})];case 8:return[2]}}))}))};if(l){var s=c.global.getFileSystemManager(),d=n.tempFilePath;s.readFile({filePath:d,success:i,fail:r})}else if(n instanceof Blob){var p=new FileReader;p.readAsArrayBuffer(n),p.onerror=r,p.onload=i}},p=function(e){o({type:L.C.WEBIM_DOWNLOADFILE_ERROR,message:"Download failed, please try again",data:e})};l?c.global.downloadFile({url:n,success:d,fail:p}):a.Wp.download.call(t,{url:n,headers:{Accept:"application/json"},onFileDownloadComplete:d,onFileDownloadError:p,secret:r,accessToken:t.context.accessToken})}))},e.prototype.setLoginInfoCustomExt=function(e){if(u.vF.debug("setLoginInfoCustomExt","params:",e),e){if("string"!=typeof e)throw new Error("ext must be a string");if(e.length>1024)throw new Error("ext length must be less than 1024")}this.loginInfoCustomExt=e},e}(),W=function(e){return e.then((function(e){return[null,e]})).catch((function(e){return[e,null]}))},j=G}(),r.O(o)}()}));