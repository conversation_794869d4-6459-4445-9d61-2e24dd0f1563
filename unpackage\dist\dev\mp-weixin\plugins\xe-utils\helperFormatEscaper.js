"use strict";
const plugins_xeUtils_toValueString = require("./toValueString.js");
const plugins_xeUtils_keys = require("./keys.js");
function helperFormatEscaper(dataMap) {
  var replaceRegexp = new RegExp("(?:" + plugins_xeUtils_keys.keys(dataMap).join("|") + ")", "g");
  return function(str) {
    return plugins_xeUtils_toValueString.toValueString(str).replace(replaceRegexp, function(match) {
      return dataMap[match];
    });
  };
}
exports.helperFormatEscaper = helperFormatEscaper;
