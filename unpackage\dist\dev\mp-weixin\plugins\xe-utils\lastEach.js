"use strict";
const plugins_xeUtils_isArray = require("./isArray.js");
const plugins_xeUtils_lastArrayEach = require("./lastArrayEach.js");
const plugins_xeUtils_lastObjectEach = require("./lastObjectEach.js");
function lastEach(obj, iterate, context) {
  if (obj) {
    return (plugins_xeUtils_isArray.isArray(obj) ? plugins_xeUtils_lastArrayEach.lastArrayEach : plugins_xeUtils_lastObjectEach.lastObjectEach)(obj, iterate, context);
  }
  return obj;
}
exports.lastEach = lastEach;
