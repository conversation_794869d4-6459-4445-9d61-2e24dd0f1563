"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
require("./interceptor.js");
const store_index = require("./store/index.js");
require("./plugins/xe-utils/index.js");
const utils_WebIM = require("./utils/WebIM.js");
const plugins_xeUtils_ctor = require("./plugins/xe-utils/ctor.js");
if (!Math) {
  "./pages/init/index.js";
  "./pages/index/index.js";
  "./pages/chatList/index.js";
  "./pages/shop/index.js";
  "./pages/personalCenter/index.js";
  "./pages/init/initlogin.js";
}
const _sfc_main = {
  __name: "App",
  setup(__props) {
    common_vendor.onLaunch((e) => {
      console.log(e, "onLaunch");
    });
    common_vendor.onShow((e) => {
    });
    common_vendor.index.$login = {
      success: async () => {
      }
    };
    return () => {
    };
  }
};
const UniImage = () => "./components/common/UniImage.js";
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  app.use(store_index.store);
  app.config.globalProperties.$xeu = plugins_xeUtils_ctor.XEUtils;
  app.config.globalProperties.$im = utils_WebIM.WebIM.default;
  common_vendor.index.$xeu = plugins_xeUtils_ctor.XEUtils;
  common_vendor.index.$im = utils_WebIM.WebIM.default;
  app.component("UniImage", UniImage);
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
