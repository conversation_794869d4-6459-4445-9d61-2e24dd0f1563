<template>
  <div class="repeat">
    <div class="detail">
      <!-- 标题 -->
      <view class="title">
        <text>药品明细</text>
        <image @click="showEwm" class="ewm_icon" src="/static/pre/ewm.png" />
      </view>
      <!-- 药品 -->
      <view class="durg_list">
        <!-- 单个药品 -->
        <view class="durg_item" v-for="drug in list" :key="drug.drugId">
          <UniImage v-if="drug.drugImg"
            :src="drug.drugImg"
            :data-src="errUrl"
            class="left"
          />
          <image v-else class="left" src="/static/shop/drug.png"></image>
          <!-- 内容 -->
          <view class="right">
            <!-- 药品名称 -->
            <view class="drug_name">{{ drug.drugName }}</view>
            <!-- 规格 -->
            <view class="drug_info">规格: {{ drug.gg }}</view>
            <!-- 价位数量 -->
            <view class="right_menu">
              <text class="price">￥{{ drug.price }}</text>
              <text class="num">x{{ drug.quan }}</text>
            </view>
          </view>
        </view>

        <!-- 统计 -->
        <view class="count">
          <text class="num">共{{ list.length }}种药品</text>
          <view class="count_price">
            合计：<text>￥{{ durg_price.toFixed(2) }}</text>
          </view>
        </view>
      </view>
    </div>

    <!-- 弹框 -->
    <uni-popup ref="popup" type="center">
      <EWM v-if="orderNo" :code="orderNo" />
    </uni-popup>

    <FOOTER @click="send">立即支付</FOOTER>
  </div>
</template>

<script>
import EWM from './com/ewm.vue';
import FOOTER from '@/components/footer_button/button.vue';
import {
  createSecondFastPrescription,
  updateSecondFastPrescriptionOrder,
  getReceiptWay,
  getSecondFastPrescriptionOrderStatus,
  saveProPayOrderRepurchase,
} from '@/api/order';

import { wxPay } from '@/common/js/pay';

import { Toast } from '@/common/js/pay.js';

// 线下支付
let offlineTimer;
// 线上
let timer;

export default {
  name: 'Repeat',
  components: {
    EWM,
    FOOTER,
  },
  data() {
    return {
      errUrl: require('../../static/shop/drug.png'),
      // 医生id
      docId: '',
      // 业务id
      businessId: '',
      // 药品列表
      list: [],
      // 药品合计金额
      durg_price: 0,
      // 订单号
      orderNo: '',
      // 支付方式
      payList: [],
      // 倒计时
      num: 3,
      // 是否可以继续点击
      isNext: true,
      pendingId: '',
      // reg
      regId: '',
    };
  },
  async onLoad(v) {
    let { businessId } = v;
    this.businessId = businessId;
    this.getDetail();
  },
  methods: {
    // 获取药店支付方式
    async getPayList(subjectId) {
      let { data } = await getReceiptWay({
        subjectId,
      });
      this.payList = data;
    },
    // 展示二维码
    async showEwm() {
      // 存在订单号
      if (this.orderNo) {
        this.$refs.popup.open();
        let param = {
          callId: 1,
          orderNo: this.orderNo,
          payType: 5,
        };
        await updateSecondFastPrescriptionOrder(param);
        if (!offlineTimer) this.getOfflineStatus();
        return;
      }

      let appid = uni.getStorageSync('appId');
      let openid = uni.getStorageSync('wxInfo').openId;

      // 组合参数
      let obj = {
        appid,
        openid,
        regId: this.regId,
        pendingId: this.pendingId,
        isMedicare: '0',
        payType: 5,
        callId: 1,
        status: 0,
      };

      let {
        data: { orderNo },
      } = await createSecondFastPrescription(obj);

      this.orderNo = orderNo;
      this.$refs.popup.open();
      this.getOfflineStatus();
    },
    // 获取药品信息
    async getDetail() {
      let { data } = await saveProPayOrderRepurchase({
        businessId: this.businessId,
      });
      // 药品 价位 订单 代缴费
      let { details, cost, orderNo, pendingId, regId, drugstoreId } = data;
      this.orderNo = orderNo;
      this.list = details;
      this.durg_price = cost;
      this.pendingId = pendingId;
      this.regId = regId;

      // 获取支付方式
      this.getPayList(drugstoreId);
    },
    // 选择支付方式
    async selePay() {
      let that = this;
      return new Promise((resolve, reject) => {
        // 如果不可选支付
        if (!this.payList.length) {
          Toast('当前没有配置支付方式');
          return reject();
        }
        // 如果只有一个支付方式
        if (this.payList.length == 1) {
          let item = this.payList[0];
          if (item.receiptType == 1) {
            resolve({ index: 1, item });
          } else {
            resolve({ index: 2, item });
          }
          return;
        }
        // 如果在线支付
        uni.showActionSheet({
          itemList: ['微信支付', '支付宝支付'],
          success(res) {
            let index = res.tapIndex + 1;
            let item;
            item = that.payList.filter((it) => it.receiptType == index)[0];
            if (!item) {
              Toast('暂不支持该支付方式');
              return reject();
            }
            resolve({ index, item });
          },
          fail(err) {
            reject(err);
          },
        });
      });
    },
    // 不存在订单 提交订单
    async send() {
      // 判断是否存在orderNo
      if (this.orderNo) {
        this.hasOrder();
        return;
      }

      this.isNext = false;
      let { index, item } = await this.selePay();

      uni.showLoading({
        mask: true,
      });

      let appid = uni.getStorageSync('appId');
      let openid = uni.getStorageSync('wxInfo').openId;
      let callId = item.appid;
      let subjectId = item.subjectId;
      let subjectName = item.subjectName;

      // 组合参数
      let obj = {
        appid,
        openid,
        regId: this.regId,
        pendingId: this.pendingId,
        isMedicare: '0',
        payType: index,
        callId,
        status: 0,
        subjectId,
        subjectName,
      };

      try {
        let { data } = await createSecondFastPrescription(obj);
        this.orderNo = data.orderNo;
        delete data.orderNo;
        uni.hideLoading();
        this.isNext = true;
        // 微信
        if (index == 1) {
          this.toWx(data);
          return;
        }

        // 支付宝
        if (index == 2) {
          let money = this.durg_price;
          uni.navigateTo({
            url:
              '/pages/pay/pay?price=' +
              money +
              '&repeat=' +
              this.orderNo +
              '&url=' +
              btoa(data.url),
          });
          return;
        }
      } catch (error) {
        this.isNext = true;
        uni.hideLoading();
      }
    },
    // 存在订单 提交订单
    async hasOrder() {
      this.isNext = false;
      let { index, item } = await this.selePay();

      uni.showLoading({
        mask: true,
      });

      let obj = {
        callId: item.appid,
        payType: index,
        orderNo: this.orderNo,
      };
      try {
        let { data } = await updateSecondFastPrescriptionOrder(obj);
        delete data.orderNo;
        uni.hideLoading();
        this.isNext = true;
        // 微信
        if (index == 1) {
          this.toWx(data);
          return;
        }

        // 支付宝
        if (index == 2) {
          let money = this.durg_price;
          uni.navigateTo({
            url:
              '/pages/pay/pay?price=' +
              money +
              '&repeat=' +
              this.orderNo +
              '&url=' +
              btoa(data.url),
          });
          return;
        }
      } catch (error) {
        this.isNext = true;
        uni.hideLoading();
      }
    },
    // 微信支付
    async toWx(info) {
      await wxPay(info);
      // 请求支付状态
      this.getStatus();
    },
    // 获取线下支付状态
    async getOfflineStatus() {
      let { data } = await getSecondFastPrescriptionOrderStatus({
        orderNo: this.orderNo,
        payType: 5,
      });
      if (data && data.regStatus == 2) {
        clearTimeout(offlineTimer);
        // 跳转页面
        this.toPath();
        return;
      }
      offlineTimer = setTimeout(this.getOfflineStatus, 2000);
    },
    // 查询线上支付状态
    async getStatus() {
      this.num--;
      let {
        data: { regStatus },
      } = await getSecondFastPrescriptionOrderStatus({
        orderNo: this.orderNo,
        payType: 1, // 微信
      });
      if (regStatus == 2) {
        clearTimeout(timer);
        // 跳转页面
        this.toPath();
        return;
      }
      // 停止轮询
      if (this.num <= 0) {
        clearTimeout(timer);
        return;
      }
      timer = setTimeout(this.getStatus, 2000);
    },
    // 跳转页面
    toPath() {
      // 先清除定时器
      if (offlineTimer) clearTimeout(offlineTimer);
      if (timer) clearTimeout(timer);
      uni.redirectTo({
        url: '/pages/quick/result?orderNo=' + this.orderNo,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
page {
  background-color: #f5f5f5;
}
.repeat {
  padding-bottom: 100rpx;

  .detail {
    padding: 24rpx 32rpx;

    .title {
      font-size: 32rpx;
      @include flex(lr);
      height: 88rpx;
      background-color: #fff;
      border-radius: 8rpx 8rpx 0 0;
      padding: 0 24rpx;
      border-bottom: 1px solid #f5f5f5;
      font-weight: bold;

      .ewm_icon {
        width: 44rpx;
        height: 44rpx;
      }
    }

    .durg_list {
      background-color: #fff;
      padding: 24rpx 24rpx 0;
      margin-bottom: 24rpx;
      border-radius: 0 0 8rpx 8rpx;

      .durg_item {
        @include flex;
        padding: 16rpx 0;

        &:last-child {
          .right {
            border-bottom: none;
          }
        }

        .left {
          width: 128rpx;
          height: 128rpx;
          margin-right: 24rpx;
          border: 1px solid #eee;
          border-radius: 8rpx;
          flex: none;
        }

        .right {
          flex: 1;
          min-height: 128rpx;
          @include flex(lr);
          flex-direction: column;
          align-items: stretch;
          border-bottom: 1px solid #eee;

          .drug_name {
            flex: 1;
            font-size: 28rpx;
            font-weight: bold;
          }

          .drug_info {
            flex: 1;
            font-size: 24rpx;
            color: #999;
          }

          .right_menu {
            flex: 2;
            @include flex(lr);
            align-items: flex-end;

            .price {
              font-size: 28rpx;
              color: #ff3b30;
              font-weight: bold;
            }

            .num {
              font-size: 28rpx;
            }
          }
        }
      }

      .count {
        @include flex(lr);
        height: 88rpx;
        font-size: 28rpx;

        .count_num {
          color: #333;
        }

        .count_price {
          text {
            color: #ff3b30;
          }
        }
      }
    }
  }
}
</style>
