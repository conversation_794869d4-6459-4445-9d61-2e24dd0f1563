"use strict";
const common_request_request = require("../common/request/request.js");
function basicgetPatientChatList(param = {}) {
  return common_request_request.http({
    url: "basic/proPatientBusiness/getPatientChatList",
    param,
    method: "post"
  });
}
function getPrescriptionCard(param = {}) {
  return common_request_request.http({
    url: "business/proPrescriptionController/getPrescriptionCardInfo",
    param,
    method: "post"
  });
}
function getQuestionStatus(param = {}) {
  return common_request_request.http({
    url: "basic/proflocksend/getPatientFeedback",
    param,
    method: "post"
  });
}
function updateChatList(param = {}) {
  return common_request_request.http({
    url: "business/proregister/updateChatOperation",
    param,
    method: "post"
  });
}
function patientToDoctorSendMessageToMq(param = {}) {
  return common_request_request.http({
    url: "basic/sendMessageToMq/patientToDoctorSendMessageToMq",
    param,
    method: "post"
  });
}
exports.basicgetPatientChatList = basicgetPatientChatList;
exports.getPrescriptionCard = getPrescriptionCard;
exports.getQuestionStatus = getQuestionStatus;
exports.patientToDoctorSendMessageToMq = patientToDoctorSendMessageToMq;
exports.updateChatList = updateChatList;
