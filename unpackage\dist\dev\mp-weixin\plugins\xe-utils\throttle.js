"use strict";
function throttle(callback, wait, options) {
  var args, context;
  var opts = options || {};
  var runFlag = false;
  var timeout = 0;
  var optLeading = "leading" in opts ? opts.leading : true;
  var optTrailing = "trailing" in opts ? opts.trailing : false;
  var runFn = function() {
    {
      runFlag = true;
      callback.apply(context, args);
      timeout = setTimeout(endFn, wait);
    }
  };
  var endFn = function() {
    timeout = 0;
    if (!runFlag && optTrailing === true) {
      runFn();
    }
  };
  var cancelFn = function() {
    var rest = timeout !== 0;
    clearTimeout(timeout);
    args = null;
    context = null;
    runFlag = false;
    timeout = 0;
    return rest;
  };
  var throttled = function() {
    args = arguments;
    context = this;
    runFlag = false;
    if (timeout === 0) {
      if (optLeading === true) {
        runFn();
      } else if (optTrailing === true) {
        timeout = setTimeout(endFn, wait);
      }
    }
  };
  throttled.cancel = cancelFn;
  return throttled;
}
exports.throttle = throttle;
