<template>
  <div class="epr">
    <div class="item">
      <p class="title">主诉</p>
      <p class="cont">
        {{ detail.chiefComplaint }}
      </p>
    </div>

    <div class="item">
      <p class="title">现病史</p>
      <p class="cont">
        {{ detail.presentDiseaseHistory }}
      </p>
    </div>

    <div class="item">
      <p class="title">既往病史</p>
      <p class="cont">
        {{ detail.previousHistory }}
      </p>
    </div>

   <!-- <div class="item">
      <p class="title">药物过敏史</p>
      <p class="cont">
        {{ detail.drugAllergyHistory }}
      </p>
    </div>

    <div class="item">
      <p class="title">遗传病史</p>
      <p class="cont">
        {{ detail.geneticHistory }}
      </p>
    </div> -->

    <div class="item">
      <p class="title">家族病史</p>
      <p class="cont">
        {{detail.familyMedicalHistory}}
      </p>
    </div>
    <div class="item">
      <p class="title">婚育史</p>
      <p class="cont">
        {{detail.marriageChildhoodHistory}}
      </p>
    </div>
    <div class="item">
      <p class="title">体格检查</p>
      <p class="cont">
        {{detail.physicalExamination}}
      </p>
    </div>
    <div class="item">
      <p class="title">专科检查</p>
      <p class="cont">
        {{detail.specialityCheckup}}
      </p>
    </div>
    <div class="item">
      <p class="title">辅助检查</p>
      <p class="cont">
        {{ detail.supplementaryExamination }}
      </p>
    </div>
   <!-- <div class="item">
      <p class="title">手术史</p>
      <p class="cont">
        {{ detail.surgicalHistory }}
      </p>
    </div>

    <div class="item">
      <p class="title">预防接种史</p>
      <p class="cont">
        {{ detail.vaccinationHistory }}
      </p>
    </div> -->

    <div class="item last">
      <p class="title">诊断</p>
      <p class="cont">
        {{ detail.diagName }}
      </p>
    </div>
    <div class="item">
      <p class="title">治疗意见</p>
      <p class="cont">
        {{ detail.cureOpinion }}
      </p>
    </div>
    <div class="item">
      <p class="title">医嘱</p>
      <p class="cont">
        {{ detail.medicalAdvice }}
      </p>
    </div>

    <!-- 占位 -->
  <!--  <div class="br"></div>

    <div class="item" v-if="detail.lisReport">
      <p class="title">检验报告</p>
      <p class="cont">
        <template v-for="item in detail.lisReport.split(';')">
          <UniImage :src="item" :preview="true" alt="" :key="item" />
        </template>
      </p>
    </div>

    <div class="item" v-if="detail.pacsReport">
      <p class="title">检查报告</p>
      <p class="cont">
        <template v-for="item in detail.pacsReport.split(';')">
          <UniImage :src="item" :preview="true" alt="" :key="item" />
        </template>
      </p>
    </div>

    <div class="item last" v-if="detail.qtImg">
      <p class="title">其他照片</p>
      <p class="cont">
        <template v-for="item in detail.qtImg.split(';')">
          <UniImage :src="item" :preview="true" alt="" :key="item" />
        </template>
      </p>
    </div> -->
  </div>
</template>

<script>
import { getLatestDetail } from '@/api/user';

export default {
  name: 'Epr',
  data() {
    return {
      regId: '',
      detail: {},
    };
  },
  onLoad(v) {
    this.regId = v.regId;
    this.getDetail();
  },
  methods: {
    async getDetail() {
      let { data } = await getLatestDetail(this.regId);
      data.lisReport = data.lisReport || '';
      data.pacsReport = data.pacsReport || '';
      data.qtImg = data.qtImg || '';
      this.detail = data;
    },
  },
};
</script>

<style lang="scss" scoped>
.epr {
  * {
    box-sizing: border-box;
  }
  .item {
    padding: 38rpx 32rpx 0;

    &.last {
      padding-bottom: 32rpx;
    }

    .title {
      font-size: 32rpx;
      margin-bottom: 20rpx;
    }

    .cont {
      width: 100%;
      min-height: 148rpx;
      border-radius: 8rpx;
      border: 2rpx solid #e5e5e5;
      padding: 30rpx;
      font-size: 32rpx;
      color: #999;
      @include flex(left);
      align-items: flex-start;
      flex-wrap: wrap;
      gap: 30rpx;

      img {
        width: 188rpx;
        height: 188rpx;
        border-radius: 8rpx;
        border: 1px dashed #e5e5e5;
        object-fit: cover;
      }
    }
  }

  .br {
    height: 32rpx;
    background-color: #f5f5f5;
  }
}
</style>
