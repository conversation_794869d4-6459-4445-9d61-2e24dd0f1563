"use strict";
const common_vendor = require("../../common/vendor.js");
const common_js_myJsTools = require("../../common/js/myJsTools.js");
const utils_myJsTools = require("../../utils/myJsTools.js");
const api_user = require("../../api/user.js");
const api_base = require("../../api/base.js");
const api_order = require("../../api/order.js");
const api_chat = require("../../api/chat.js");
const api_inspect = require("../../api/inspect.js");
common_js_myJsTools.myJsTools.getUrlParam();
const emedia = require("@/utils/WebIM.js")["emedia"];
const propCenter = () => "../../components/propCenter/propCenter.js";
const _sfc_main = {
  components: {
    propCenter
  },
  data() {
    return {
      appid: "",
      code: "",
      wxInfo: "",
      isModelToastShow: false,
      hosId: "",
      action: "",
      // 视频通话相关参数
      conferenceId: "",
      // regId
      regId: "",
      // 图标展示
      icon: CONFIG_ENV.VUE_APP_SHARE,
      // 区分页面
      page: "",
      // repeat
      // 科室id
      deptId: "",
      // D82FF689BD374A5DA4898F4C42519755
      // 是否视频
      isVideo: 0,
      docId: "",
      // 017f66699f194eee9947cd065316598e
      // 医嘱id
      dpmpId: "",
      // f8a85c28621248b381b36ed51d895e9a,
      // 消息id
      msgid: "",
      // "abc1234567" || new Date().getTime(),
      // 检查单
      ppiId: "",
      // 检验单
      pliId: "",
      // 处方
      businessCode: "",
      // 复购
      businessId: "",
      // 46ded25f461d44aba1f6937118261240
      // 是否扫码开方
      isScanCode: false,
      // 点击次数
      n: 0,
      // 订单号
      orderNo: "",
      // 项目id
      projectId: ""
    };
  },
  created() {
    common_vendor.index.setNavigationBarTitle({
      title: CONFIG_ENV.VUE_APP_TITLE || "互联网医院"
    });
  },
  async onLoad() {
    await this.getConfigDetail();
    common_vendor.index.setStorageSync("sourceCode", "0");
    {
      this.action = common_js_myJsTools.myJsTools.request("action") || "";
      if (this.action == "initlogin") {
        if (utils_myJsTools.myJsTools.getItem("proPfInfo") && utils_myJsTools.myJsTools.getItem("proPfInfo").token) {
          common_vendor.index.reLaunch({
            url: "/pages/index/index"
          });
        } else {
          common_vendor.index.redirectTo({
            url: "/pages/init/initlogin"
          });
        }
        return;
      }
      await this.clear();
      this.appid = "wx6683b06aeddb37ac";
      this.hosId = 1;
      this.$store.commit("setAppId", this.appid);
      this.wxInfo = {
        city: "",
        country: "天津",
        headimgurl: "/static/logo.png",
        nickname: "***",
        // openId: 'oXVoe5ggpSjY2ADFWLcCB-pCHlXY', // yh ceshi
        // openId: "oXVoe5lWeKUjzu1SCVkYdQbmblhY", //zq
        // openId: 'oO3Py6r7xMGjxKImmXLchXv3-dTc', //zhh
        // openId: "oXVoe5lWeKUjzu1SCVkYdQbmblhY", // zm ceshi
        // openId: "oO3Py6pVzP_QM63F-aBh34bewmds", // zm prod
        // openId: 'oXVoe5uA6whF57-ij9qNE9IOTZ48', // zd ceshi
        // openId: "oO3Py6oH16behHyvFKBvktTGKmC8", // zd prod
        // openId: "op9Ly7IBrWkO01sR1On9SaesA_UQ", // yh prod
        //openId: "oO3Py6nw4E-gpuh7Y1vQsfcseLbE", // yjc prod
        // openId: "oO3Py6pcrck6m0NZ7d6nuRkWrJwE", // zq prod
        // openId: "oO3Py6kM8qLDKiA_2cG9TZ78qpoQ", // zqy prod
        // openId: "oO3Py6r7xMGjxKImmXLchXv3-dTc", // zhh prod
        // openId: 'oO3Py6tD3LXppfD7pJuVeNTMEsOM', // lxy
        // openId: "oO3Py6gFBhT3i05Na5Bl4cNBUHUU", // wn
        // openId: 'oO3Py6oH16behHyvFKBvktTGKmC8',
        // openId: 'op9Ly7MKchkiJZqK_g0scmosUlnc', // qy  prod
        // openId: 'oO3Py6oH16behHyvFKBvktTGKmC8',
        // openId: 'oO3Py6sURE3pOubN3cNDN5eXPo0k',// yxh  test
        // openId: 'op9Ly7PhJ_ykhyKcW55YEN_-tn-0', // yxh  prod
        // openId: 'oO3Py6klUB5MtdCZVdAH6DLXrbE8', // zzq test
        openId: "oO3Py6inOMTr46Ze-7ljTOKvR6jY",
        // yh 测试环境
        sex: 0
      };
      this.$store.commit("setWxInfo", this.wxInfo);
      this.getUserInfoFun();
    }
  },
  methods: {
    // 开启打印
    setLog() {
    },
    // 清除本地临时存储变量
    async clear() {
      common_vendor.index.removeStorageSync("wxInfo");
      common_vendor.index.removeStorageSync("userId");
      common_vendor.index.removeStorageSync("hosId");
      common_vendor.index.removeStorageSync("registration");
      common_vendor.index.removeStorageSync("patientId");
      common_vendor.index.removeStorageSync("myUsername");
      common_vendor.index.removeStorageSync("chatList");
      common_vendor.index.removeStorageSync("appointReserveInfo");
      common_vendor.index.removeStorageSync("wxInfo");
      common_vendor.index.removeStorageSync("scanAddress");
      common_vendor.index.removeStorageSync("appId");
      common_vendor.index.removeStorageSync("drugArr");
      common_vendor.index.removeStorageSync("selectAddress");
      common_vendor.index.removeStorageSync("infoDetail");
      common_vendor.index.removeStorageSync("wholeArg");
      common_vendor.index.removeStorageSync("offline");
      common_vendor.index.removeStorageSync("qcOffline");
      common_vendor.index.removeStorageSync("shop_address");
      common_vendor.index.removeStorageSync("chatItem");
      common_vendor.index.removeStorageSync("appointInfoDetail");
      common_vendor.index.removeStorageSync("patientInfo");
      common_vendor.index.removeStorageSync("nowAddress");
      common_vendor.index.removeStorageSync("nowHZinfo");
      common_vendor.index.removeStorageSync("proPfInfo");
      common_vendor.index.removeStorageSync("patientIdList");
      common_vendor.index.removeStorageSync("shop_patient");
      common_vendor.index.removeStorageSync("shop_city");
      common_vendor.index.removeStorageSync("offlineAppoint");
      common_vendor.index.removeStorageSync("hideTab");
      common_vendor.index.removeStorageSync("initialFlag");
      common_vendor.index.removeStorageSync("evaluaAction");
      common_vendor.index.removeStorageSync("isShowWorkHosName");
      common_vendor.index.removeStorageSync("showInvoice");
      common_vendor.index.removeStorageSync("ynw_scanid");
      common_vendor.index.removeStorageSync("ynw_patientId");
      common_vendor.index.removeStorageSync("ynw_openid");
      common_vendor.index.removeStorageSync("ynw_drugId");
      common_vendor.index.removeStorageSync("ynw_patientName");
      common_vendor.index.removeStorageSync("ynw_telNo");
      common_vendor.index.removeStorageSync("callSource");
      return Promise.resolve();
    },
    // 查询扫码开方或者快捷开方
    async getConfigDetail() {
      let { data } = await api_base.getSysPlatformConfigByKeyList([
        "fast_prescribe",
        "patientSideDisplayOnlineMall",
        "show_doctor_practice_hospital",
        "patient_show_invoice"
      ]);
      data.forEach((v) => {
        if (v.configKey == "fast_prescribe" && v.configValue == 0) {
          this.isScanCode = true;
        }
        if (v.configKey == "patientSideDisplayOnlineMall" && v.configValue == 0) {
          this.appendStyle();
        }
        if (v.configKey == "show_doctor_practice_hospital" && v.configValue == 1) {
          common_vendor.index.setStorageSync("isShowWorkHosName", true);
        }
        if (v.configKey == "patient_show_invoice" && v.configValue == 1) {
          common_vendor.index.setStorageSync("showInvoice", true);
        }
      });
    },
    // 隐藏商城入口
    appendStyle() {
      let style = document.createElement("style");
      style.type = "text/css";
      let sty = "uni-tabbar .uni-tabbar__item:nth-of-type(4) { display: none !important; }";
      style.innerHTML = sty;
      document.head.appendChild(style);
    },
    // 查询扫码开方状态
    async getStatus() {
      return await api_order.findOrderByMsgid(this.msgid);
    },
    // 获取openid,微信信息,是否关注
    async getOpenIdFun() {
      let para = {
        appid: this.appid,
        code: this.code
      };
      let res = await api_user.getOpenId(para);
      this.wxInfo = res.data;
      if (!this.wxInfo) {
        this.wxInfo = {
          city: "",
          country: "天津",
          headimgurl: "/static/logo.png",
          nickname: "***",
          // openId: "oXVoe5uA6whF57-ij9qNE9IOTZ48",
          // openId: "oXVoe5rOnmnWUlq6DFkBP9cqbeuE",
          // openId: "opL5Fw22wX6jucu1JIcNEM6sMaDM",
          // openId: "oCAnn5pNqcT9oqXIQXFOm_AuX7Uc",
          openId: "oXVoe5uA6whF57-ij9qNE9IOTZ48",
          // 张栋
          sex: 0
        };
      }
      this.$store.commit("setWxInfo", this.wxInfo);
      this.getUserInfoFun();
    },
    // 获取用户信息
    async getUserInfoFun() {
      let para = {
        appid: this.appid,
        openid: this.wxInfo.openId
      };
      if (this.page == "ynw") {
        para.isThirdPart = 1;
        para.thirdPatientId = common_vendor.index.getStorageSync("ynw_patientId");
        para.thirdAppid = "ynw";
        para.scanid = common_vendor.index.getStorageSync("ynw_scanid");
        para.thirdPartyOpenid = common_vendor.index.getStorageSync("ynw_openid");
      } else {
        para.isThirdPart = 0;
      }
      try {
        let res = await api_user.getUsertInfo(para);
        if (res.data && res.data.userId) {
          let proPfInfo = res.data;
          this.$store.commit("setProPfInfo", proPfInfo);
          common_vendor.index.setStorageSync("userId", proPfInfo.userId);
          let myUsername = proPfInfo.userId.toLowerCase();
          common_vendor.index.setStorageSync("myUsername", myUsername);
          common_vendor.index.setStorageSync("tel", res.data.telNo || "");
          common_vendor.index.setStorageSync("patientIdList", proPfInfo.patientIdList || []);
          let hxidIsregist = proPfInfo.hxidIsregist;
          if (hxidIsregist == "1") {
            this.WebIMLogin();
          } else {
            this.WebIMRegister();
          }
        } else {
          if (this.page == "ynw") {
            let data = {
              isThirdPart: 1,
              thirdAppid: "ynw",
              thirdPatientId: common_vendor.index.getStorageSync("ynw_patientId"),
              scanid: common_vendor.index.getStorageSync("ynw_scanid"),
              thirdPartyOpenid: common_vendor.index.getStorageSync("ynw_openid"),
              appid: common_vendor.index.getStorageSync("appId"),
              openid: common_vendor.index.getStorageSync("wxInfo").openId,
              headImgUrl: common_vendor.index.getStorageSync("wxInfo").headimgurl,
              telNo: common_vendor.index.getStorageSync("ynw_telNo"),
              userName: common_vendor.index.getStorageSync("ynw_patientName")
            };
            let u = await api_user.hisPatientLoginBindingUserInfo(data);
            if (u.code != 2e4) {
              common_vendor.index.redirectTo({
                url: "/pages/login/login"
              });
              return;
            }
            this.getUserInfoFun();
          } else {
            this.errToPath();
          }
        }
      } catch (e) {
        if (this.page == "ynw") {
          return;
        }
        this.errToPath();
      }
    },
    // 根据参数跳转
    async toPath() {
      console.log("查看逻辑是否到达这里action", this.action);
      console.log("查看逻辑是否到达这里officialAccount", this.officialAccount);
      console.log("查看逻辑是否到达这里appid", this.appid);
      console.log("查看逻辑是否到达这里code", this.code);
      let action = this.action;
      if (action == "aiBanner") {
        common_vendor.index.navigateTo({
          url: "/pages/aiAssistant/previewImages"
        });
        return;
      }
      let url = "/pages/index/index";
      let { data } = await api_inspect.getOpenIdUserInfo({
        appid: this.appid,
        openid: this.wxInfo.openId
      });
      if (!data) {
        url = "/pages/login/login";
      } else {
        if (!(data == null ? void 0 : data.userId)) {
          url = "/pages/login/login";
        }
      }
      console.log(action);
      if (action) {
        if (!data) {
          url = "/pages/login/login";
          if (action === "offline" || action === "qcOffline") {
            url = "/pages/login/login?action=" + action + "&docId=" + this.docId + "&projectId=" + this.projectId;
            common_vendor.index.reLaunch({
              url
            });
            return;
          }
        } else {
          if (!(data == null ? void 0 : data.userId)) {
            if (action === "offline" || action === "qcOffline") {
              url = "/pages/login/login?action=" + action + "&docId=" + this.docId + "&projectId=" + this.projectId;
              common_vendor.index.reLaunch({
                url
              });
              return;
            }
          }
        }
        if (action == "618") {
          common_vendor.index.setStorageSync("sourceCode", "618");
          if (!data) {
            url = "/pages/login/login?action=" + action;
            common_vendor.index.reLaunch({
              url
            });
            return;
          } else {
            if (!(data == null ? void 0 : data.userId)) {
              url = "/pages/login/login?action=" + action;
              common_vendor.index.reLaunch({
                url
              });
              return;
            } else {
              url = "/pages/shop/index";
              common_vendor.index.switchTab({
                url
              });
              return;
            }
          }
        }
        if (action == "register") {
          url = "/pages/personalCenter/diagnosisRecord/detail?id=" + this.regId;
        } else if (action == "chatList") {
          url = "/pages/chatList/index";
        } else if (action == "visitList") {
          url = "/pages/personalCenter/diagnosisRecord/index";
        } else if (action == "visitDetailList") {
          url = "/pages/prescription/preDetail?businessId=" + common_js_myJsTools.myJsTools.request("businessid") || "";
        } else if (action == "videoCall") {
          url = "/pages/chatList/videoCall?conferenceId=" + this.conferenceId + "&regId=" + this.regId + "&userId=" + common_vendor.index.getStorageSync("userId") + "&token=" + common_vendor.index.getStorageSync("proPfInfo").token + "&deptId=" + this.deptId + "&isVideo=" + this.isVideo + "&docId=" + this.docId + "&hosId=" + common_vendor.index.getStorageSync("hosId");
        } else if (action == "videoAgora") {
          url = "/pages/chatList/videoCallAgora?regId=" + this.regId + "&userId=" + common_vendor.index.getStorageSync("userId") + "&token=" + common_vendor.index.getStorageSync("proPfInfo").token + "&deptId=" + this.deptId + "&isVideo=" + this.isVideo + "&docId=" + this.docId + "&hosId=" + common_vendor.index.getStorageSync("hosId");
        } else if (action == "shopTab") {
          common_vendor.index.setStorageSync("hideTab", 1);
          url = "/pages/shop/index";
        } else if (action == "pacs") {
          url = "/pages/inspect/pacsDetails?id=" + this.ppiId;
          let {
            data: { paymentType, status }
          } = await api_inspect.checkPacsAppointStatus(this.ppiId);
          if (paymentType == 1) {
            url = "/pages/inspect/pacsDetails?id=" + this.ppiId;
          } else if (paymentType == 2 && status == 1) {
            url = "/pages/inspect/pay/pacs?id=" + this.ppiId;
          } else if (!paymentType) {
            url = "/pages/inspect/pacsOrder?id=" + this.ppiId;
          } else {
            url = "/pages/inspect/pacsDetails?id=" + this.ppiId;
          }
        } else if (action == "lis") {
          let {
            data: { paymentType, status }
          } = await api_inspect.checkLisAppointStatus(this.pliId);
          if (paymentType == 1) {
            url = "/pages/inspect/lisDetails?id=" + this.pliId;
          } else if (paymentType == 2 && status == 1) {
            url = "/pages/inspect/pay/lis?id=" + this.pliId;
          } else if (!paymentType) {
            url = "/pages/inspect/lisOrder?id=" + this.pliId;
          } else {
            url = "/pages/inspect/lisDetails?id=" + this.pliId;
          }
        } else if (action == "pre") {
          let {
            data: { payStatus, businessId }
          } = await api_chat.getPrescriptionCard({
            businessCode: this.businessCode
          });
          if (payStatus == 0) {
            url = "/pages/prescription/prescriptionDetail?businessId=" + businessId + "&status=1";
          } else {
            url = "/pages/prescription/preDetail?businessId=" + businessId;
          }
        } else if (action == "shop") {
          url = "/pages/shopOrder/detail?orderNo=" + this.orderNo;
        } else if (action == "cancel") {
          url = "/pages/shopOrder/cancel?orderNo=" + this.orderNo;
        } else if (action == "visitOrderDetailList") {
          url = "/pages/order/detail/drugStatus?orderNo=" + this.orderNo;
        } else if (action == "offline") {
          url = "/pages/shop/scan?docId=" + this.docId;
        } else if (action == "qcOffline") {
          url = "/pages/scanCode/nutritionAssessment?docId=" + this.docId + "&projectId=" + this.projectId;
        } else if (action != "") {
          url = action;
        }
        common_vendor.index.reLaunch({
          url
        });
        return;
      }
      if (this.officialAccount) {
        const offPatn = [
          {
            type: "KSXF",
            path: "/pages/quickContinuation/index"
          },
          {
            type: "FZKF",
            path: "/pages/register/docList2/index?flag=2"
          },
          {
            type: "JKZX",
            path: "/pages/register/docList2/index?flag=1"
          },
          {
            type: "JKSC",
            path: "/pages/shop/index"
          },
          {
            type: "WDYS",
            path: "/pages/personalCenter/myDocter/patients/patients?pageType=myDocter"
          },
          {
            type: "WDDD",
            path: "/pages/order/index"
          },
          {
            type: "GRZX",
            path: "/pages/personalCenter/personalInfo/index"
          },
          {
            type: "aiBanner",
            path: "/pages/aiAssistant/previewImages"
          }
        ];
        const resultPath = offPatn.find(
          (item) => item.type === this.officialAccount
        );
        if (resultPath.path == "/pages/aiAssistant/previewImages") {
          common_vendor.index.reLaunch({
            url: resultPath.path
          });
          return;
        }
        url = resultPath.path;
        if (!data) {
          url = "/pages/login/login";
        } else {
          if (!(data == null ? void 0 : data.userId)) {
            url = "/pages/login/login";
          }
        }
        common_vendor.index.reLaunch({
          url
        });
        return;
      }
      if (this.page == "prescription") {
        url = "/pages/personalCenter/myPrescription/index";
        common_vendor.index.reLaunch({
          url
        });
        return;
      }
      if (this.page == "ynw") {
        common_vendor.index.setStorageSync("hideTab", 1);
        url = "/pages/shop/index";
        common_vendor.index.reLaunch({
          url
        });
        return;
      }
      if (this.page == "repeat") {
        let { data: data2 } = await api_order.findOrderByBusinessId({
          businessId: this.businessId
        });
        if (!data2 || data2.orderStatus == 1) {
          url = "/pages/quick/repeat?businessId=" + this.businessId;
        } else if (data2.orderStatus == 3 && (data2.deliveryType == 2 || !data2.deliveryType)) {
          url = "/pages/quick/result?orderNo=" + data2.orderNo;
        } else if (data2.deliveryType == 1 || data2.orderStatus >= 3) {
          url = "/pages/prescription/preDetail?businessId=" + this.businessId;
        }
        common_vendor.index.reLaunch({
          url
        });
        return;
      }
      if (this.dpmpId) {
        common_vendor.index.setStorageSync("hosId", this.hosId);
        let userId = common_vendor.index.getStorageSync("userId");
        let list = common_vendor.index.getStorageSync("patientIdList") || [];
        let res = await this.getStatus();
        if (res.data) {
          let { source } = res.data;
          if (source == 3) {
            this.isScanCode = true;
          } else {
            this.isScanCode = false;
          }
        }
        if (this.isScanCode) {
          if (!res.data || !userId) {
            url = "/pages/scanCode/index?dpmpId=" + this.dpmpId + "&docId=" + this.docId + "&msgid=" + this.msgid;
          } else if (res.data.orderStatus == 1) {
            url = "/pages/scanCode/result?orderNo=" + res.data.orderNo;
          } else {
            url = "/pages/prescription/preDetail?businessId=" + res.data.prescriptionBusinessId;
          }
        } else {
          if (!res.data || res.data.orderStatus == 1) {
            url = "/pages/quick/index?dpmpId=" + this.dpmpId + "&docId=" + this.docId + "&msgid=" + this.msgid;
          } else if (res.data.orderStatus > 1 && !res.data.patientId) {
            if (!list.length) {
              url = "/pages/quick/info?orderNo=" + res.data.orderNo + "&dpmpId=" + this.dpmpId;
            } else {
              url = "/pages/quick/sele?orderNo=" + res.data.orderNo + "&dpmpId=" + this.dpmpId;
            }
          } else if (res.data.patientId && res.data.deliveryType == 2 && res.data.orderStatus == 3) {
            url = "/pages/quick/result?orderNo=" + res.data.orderNo;
          } else {
            url = "/pages/prescription/preDetail?businessId=" + res.data.prescriptionBusinessId;
          }
        }
        common_vendor.index.reLaunch({
          url
        });
        return;
      }
      if (this.docId && this.hosId) {
        console.log("查看扫码跳转url参数", 222222222222);
        common_vendor.index.setStorageSync("hosId", this.hosId);
        await api_user.queryRegisterStatus({
          appid: this.appid,
          openid: this.wxInfo.openId
        }).then((ret) => {
          console.log("查看扫码跳转url参数", ret);
          if (ret && Number(ret.data) === 1) {
            url = "/pages/register/docHomePage/bindDoctor?action=init&docId=" + this.docId;
          }
        });
      }
      common_vendor.index.reLaunch({
        url
      });
    },
    // 获取信息后跳转
    errToPath() {
      if (common_vendor.index.getStorageSync("userId")) {
        common_vendor.index.removeStorageSync("userId");
      }
      if (common_vendor.index.getStorageSync("myUsername")) {
        common_vendor.index.removeStorageSync("myUsername");
      }
      this.$store.dispatch("clearDB");
      this.toPath();
    },
    // 环信注册
    WebIMRegister() {
      let _this = this;
      let userId = common_vendor.index.getStorageSync("userId");
      let options = {
        username: userId,
        password: userId,
        nickname: userId,
        appKey: _this.$im.config.appkey,
        success: function() {
          _this.updateHxidIsregistStatusFun();
        },
        error: function(err) {
          if (err.statusCode == 400) {
            console.log("查看注册错误信息", err);
            _this.updateHxidIsregistStatusFun();
            return;
          }
          _this.isModelToastShow = true;
        },
        apiUrl: _this.$im.config.apiURL
      };
      _this.$im.conn.registerUser(options);
    },
    // 更改用户环信状态
    async updateHxidIsregistStatusFun() {
      let userId = common_vendor.index.getStorageSync("userId");
      await api_user.updateHxidIsregistStatus({
        userId
      });
      this.WebIMLogin();
    },
    // 环信登录
    WebIMLogin() {
      let _this = this;
      let userId = common_vendor.index.getStorageSync("userId");
      console.log("查看登录userId", userId);
      let options = {
        apiUrl: _this.$im.config.apiURL,
        user: userId,
        pwd: userId,
        grant_type: userId,
        appKey: _this.$im.config.appkey,
        success: function(res) {
          let memName = _this.$im.config.appkey + "_" + userId;
          console.log("登录环信成功", userId);
          emedia.mgr.setIdentity(memName, res.access_token);
          _this.toPath();
        },
        error: function(err) {
          console.log("查看登录错误日志", err);
          _this.isModelToastShow = true;
        }
      };
      _this.$im.conn.open(options).then((res) => {
        let memName = _this.$im.config.appkey + "_" + userId;
        console.log("登录环信成功", userId);
        emedia.mgr.setIdentity(memName, res.access_token);
        _this.toPath();
      }).catch((err) => {
        console.log("查看登录错误日志", err);
        _this.isModelToastShow = true;
      });
    },
    // 环信注册/登录失败，点击确定，退出程序
    outLogin() {
      this.isModelToastShow = false;
      WeixinJSBridge.call("closeWindow");
    }
  }
};
if (!Array) {
  const _easycom_propCenter2 = common_vendor.resolveComponent("propCenter");
  _easycom_propCenter2();
}
const _easycom_propCenter = () => "../../components/propCenter/propCenter.js";
if (!Math) {
  _easycom_propCenter();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.icon,
    b: common_vendor.o((...args) => $options.setLog && $options.setLog(...args)),
    c: $data.isModelToastShow
  }, $data.isModelToastShow ? {
    d: common_vendor.o($options.outLogin)
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-f48d61de"]]);
wx.createPage(MiniProgramPage);
