/*
	视频通话时长相关
*/

import http from '../common/request/request.js';

// 接视频
export const videoPhone = ({regId,isShoppingRegId}) => {
  return http({
    url: 'business/proVideoTimeInfo/videoPhone',
    param: {
      regId,
      isShoppingRegId
    },
    method: 'post',
  });
};

// 查剩余时间
export const getTimeRemaining = ({regId,isShoppingRegId}) => {
  return http({
    url: 'business/proVideoTimeInfo/queryVideoTimeRemaining',
    param: {
      regId,
      isShoppingRegId
    },
    method: 'post',
  });
};

// 挂断视频
export const hangUpVideo = ({ confrId, regId }) => {
  return http({
    url: 'business/proVideoTimeInfo/hangUpVideo',
    param: {
      regId,
      confrId,
    },
    method: 'post',
  });
};

// 挂断声网视频
export const AgoraUpVideo = ({ confrId, regId }) => {
  return http({
    url: 'business/proVideoTimeInfo/AgoraUpVideo',
    param: {
      regId,
      confrId,
    },
    method: 'post',
  });
};

// 获取声网token
export const getAgoraToken = ({ channelName, userId }) => {
  return http({
    url: 'chat/tools/getAgoraToken',
    param: {
      channelName,
      userId,
    },
    method: 'post',
  });
};
