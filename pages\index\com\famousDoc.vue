<template>
  <!-- 名医推荐-->
  <view class="fam_list">
    <!-- 标题 -->
    <view class="fam_title">
      <TITLE title="名医推荐" />
      <view class="card_more" @click="lookOver">
        查看全部医生
<!--        <uni-icons type="arrowright" color="#999" size="14"></uni-icons>-->
      </view>
    </view>
    <view class="list-box">
      <view class="new-doc-item" v-for="(item,index) in list"  @click="toDoc(item)" :key="index">
        <view class="docImg">
          <UniImage class="doc_head" v-if="item.docImg" :src="item.docImg" :default-src="errUrl" :error-src="errUrl" />
          <UniImage class="doc_head" v-else src="/static/images/docHead.png" />
        </view>
        <view class="new-doc-content">
           <view>
             <view class="doc_name">
                <text>{{item.docName}}</text>
             </view>
          </view>
          <view class="new-doc-content-dept">
            <text>{{item.deptName}}</text>-
            <text>{{item.docProf}}</text>
          </view>
          <view class="new-doc-content-dept">
            <text>{{isShowWorkHosName ? item.workHosName : item.hosName}}</text>
          </view>
            <!-- 标签 -->
         <view class="desc_tag" v-if="item.docLable.length>3">
          <view v-for="(l, lk) in item.docLable.slice(0,3)" class="desc_tag_1">
             <view class="desc_tag_text">{{ l.lableName }}</view>
           </view>
         </view>
         <view class="desc_tag" v-else>
          <view v-for="(l, lk) in item.docLable" class="desc_tag_1">
             <view class="desc_tag_text">{{ l.lableName }}</view>
           </view>
         </view>
        </view>
      </view>
    </view>
    <!-- 医生列表-->
<!--    <view class="list-box">-->
<!--      <view class="list-box-1" v-for="(item,index) in list" :key="index">-->
<!--        <view class="list-card" @click="toDoc(item)">-->
<!--          <image class="doc_head" v-if="item.docImg" v-img="item.docImg" :data-src="errUrl" />-->
<!--          <image class="doc_head" v-else src="/static/images/docHead.png"></image>-->
<!--          <view class="doc_name">-->
<!--            <text>{{item.docName}}</text>-->
<!--          </view>-->
<!--          <view class="desc_two">-->
<!--            <text>{{item.deptName}}</text>-->
<!--            <text>{{item.docProf}}</text>-->
<!--          </view>-->
<!--          <view class="host">-->
<!--            <text>{{isShowWorkHosName ? item.workHosName : item.hosName}}</text>-->
<!--          </view>-->
<!--          &lt;!&ndash; 标签 &ndash;&gt;-->
<!--         <view class="desc_tag" v-if="item.docLable.length>3">-->
<!--          <view v-for="(l, lk) in item.docLable.slice(0,3)" class="desc_tag_1">-->
<!--             <view class="desc_tag_text">{{ l.lableName }}</view>-->
<!--           </view>-->
<!--         </view>-->
<!--         <view class="desc_tag" v-else>-->
<!--          <view v-for="(l, lk) in item.docLable" class="desc_tag_1">-->
<!--             <view class="desc_tag_text">{{ l.lableName }}</view>-->
<!--           </view>-->
<!--         </view>-->
<!--        </view>-->
<!--      </view>-->
<!--    </view>-->
  </view>
</template>

<script>
  import TITLE from "@/pages/inspect/com/itemTitle.vue";
  export default {
    components: {
      TITLE,
    },
    props: {
      list: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        errUrl: require("../../../static/images/docHead.png"),
        isShowWorkHosName: uni.getStorageSync("isShowWorkHosName") || false,
      }
    },
    methods: {
      lookOver() {
        uni.navigateTo({
          url: '/pages/index/com/famousDocList'
        });
      },
      // 去医生主页
      toDoc(item) {
        // 医院id
        uni.setStorageSync("hosId", item.hosId);
        uni.navigateTo({
          url: "/pages/register/docHomePage/index?docId=" + item.docId,
        });
      },
    }
  }
</script>
<style lang="scss" scoped>
.new-doc-item{
  display: flex;
  border-bottom: 1px solid #f5f5f5;
  padding: 20rpx 0;
  .docImg{
    margin-right: 24rpx;
    .doc_head{
      width: 110rpx;
      height: 110rpx;
    }
  }
  .new-doc-content{
    flex: 1;
  }
  .new-doc-content-dept{
    font-size: 28rpx;
    font-weight: 400;
    line-height: 40rpx;
    margin-top: 10rpx;
    color: rgba(102, 102, 102, 1);
  }
}
  .fam_list {
    background: #fff;
    border-radius: 8rpx;
    .fam_title {
      padding: 0 10rpx;
      height: 80rpx;
      @include flex(left);
      display: flex;
      justify-content: space-between;
    }
  }

  .list-box {
    display: flex;
    flex-direction: column;
    margin-left: 20rpx;
  }

  .list-card {
    padding: 16rpx;
    background-color: #FBFBFD;
    margin-right: 20rpx;
    margin-bottom: 20rpx;
    border-radius: 8rpx;
    height: 360rpx;
    text-align: center;
  }

  .list-box-1 {
    width: 50%;
    text-align: center;
  }

  .card_more {
    height: 80rpx;
    @include flex;
    font-size: 24rpx;
    color: rgba(135, 79, 240, 1);
  }

  .doc_head {
    width: 104rpx;
    height: 104rpx;
    border-radius: 50%;
    flex: none;
    object-fit: cover;

  }

  .doc_name {
    font-size: 28rpx;
    color: rgba(51, 51, 51, 1);
    font-weight: bold;
  }

  .desc_tag {
    margin-top: 16rpx;
    display: flex;
    flex-direction: row;
    //justify-content: center;

    // text {
    //   padding: 0 12rpx;
    //   height: 36rpx;
    //   border-radius: 18rpx;
    //   background-color: #e8f6fd;
    //   @include font_theme;
    //   font-size: 22rpx;
    //   @include flex;

    // }
  }
  .desc_tag_text{
   padding: 0 12rpx;
   height: 36rpx;
   line-height: 36rpx;
   border-radius: 18rpx;
   background-color: #e8f6fd;
   color: rgba(153, 153, 153, 1);
   font-size: 20rpx;
   //@include flex;
   overflow: hidden; //块元素超出隐藏
   max-width: 120rpx;
   text-overflow: ellipsis; //文本超出块元素时以省略号的形式显示
   white-space: nowrap; //规定段落中的文本不进行换行
  }
  .desc_tag_1{
    margin: 0 2rpx;
    }

  .desc_two {
    margin-top: 8rpx;
    font-size: 24rpx;
    color: #333;

    text:last-child {
      margin-left: 24rpx;
    }
  }

  .host {
    margin-top: 8rpx;
    font-size: 24rpx;
    color: #a6aab2;
    text-align: center;
    /* 超出部分...代替*/
    //width: 135px;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    overflow: hidden;
  }
</style>