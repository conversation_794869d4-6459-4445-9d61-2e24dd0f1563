"use strict";
const plugins_xeUtils_setupDefaults = require("./setupDefaults.js");
const plugins_xeUtils_round = require("./round.js");
const plugins_xeUtils_ceil = require("./ceil.js");
const plugins_xeUtils_floor = require("./floor.js");
const plugins_xeUtils_isNumber = require("./isNumber.js");
const plugins_xeUtils_toValueString = require("./toValueString.js");
const plugins_xeUtils_toFixed = require("./toFixed.js");
const plugins_xeUtils_toNumberString = require("./toNumberString.js");
const plugins_xeUtils_assign = require("./assign.js");
function commafy(num, options) {
  var opts = plugins_xeUtils_assign.assign({}, plugins_xeUtils_setupDefaults.setupDefaults.commafyOptions, options);
  var optDigits = opts.digits;
  var isNum = plugins_xeUtils_isNumber.isNumber(num);
  var rest, result, isNegative, intStr, floatStr;
  if (isNum) {
    rest = (opts.ceil ? plugins_xeUtils_ceil.ceil : opts.floor ? plugins_xeUtils_floor.floor : plugins_xeUtils_round.round)(num, optDigits);
    result = plugins_xeUtils_toNumberString.toNumberString(optDigits ? plugins_xeUtils_toFixed.toFixed(rest, optDigits) : rest).split(".");
    intStr = result[0];
    floatStr = result[1];
    isNegative = intStr && rest < 0;
    if (isNegative) {
      intStr = intStr.substring(1, intStr.length);
    }
  } else {
    rest = plugins_xeUtils_toValueString.toValueString(num).replace(/,/g, "");
    result = rest ? [rest] : [];
    intStr = result[0];
  }
  if (result.length) {
    return (isNegative ? "-" : "") + intStr.replace(new RegExp("(?=(?!(\\b))(.{" + (opts.spaceNumber || 3) + "})+$)", "g"), opts.separator || ",") + (floatStr ? "." + floatStr : "");
  }
  return rest;
}
exports.commafy = commafy;
