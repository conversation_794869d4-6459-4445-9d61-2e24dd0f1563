"use strict";
const plugins_xeUtils_staticLocation = require("./staticLocation.js");
const plugins_xeUtils_helperGetLocatOrigin = require("./helperGetLocatOrigin.js");
const plugins_xeUtils_lastIndexOf = require("./lastIndexOf.js");
function getBaseURL() {
  if (plugins_xeUtils_staticLocation.staticLocation) {
    var pathname = plugins_xeUtils_staticLocation.staticLocation.pathname;
    var lastIndex = plugins_xeUtils_lastIndexOf.lastIndexOf(pathname, "/") + 1;
    return plugins_xeUtils_helperGetLocatOrigin.helperGetLocatOrigin() + (lastIndex === pathname.length ? pathname : pathname.substring(0, lastIndex));
  }
  return "";
}
exports.getBaseURL = getBaseURL;
