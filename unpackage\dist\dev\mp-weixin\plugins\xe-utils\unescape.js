"use strict";
const plugins_xeUtils_staticEscapeMap = require("./staticEscapeMap.js");
const plugins_xeUtils_helperFormatEscaper = require("./helperFormatEscaper.js");
const plugins_xeUtils_each = require("./each.js");
var unescapeMap = {};
plugins_xeUtils_each.each(plugins_xeUtils_staticEscapeMap.staticEscapeMap, function(item, key) {
  unescapeMap[plugins_xeUtils_staticEscapeMap.staticEscapeMap[key]] = key;
});
var unescape = plugins_xeUtils_helperFormatEscaper.helperFormatEscaper(unescapeMap);
exports.unescape = unescape;
