"use strict";
const plugins_xeUtils_keys = require("./keys.js");
const plugins_xeUtils_findIndexOf = require("./findIndexOf.js");
const plugins_xeUtils_isEqual = require("./isEqual.js");
const plugins_xeUtils_some = require("./some.js");
const plugins_xeUtils_includeArrays = require("./includeArrays.js");
function isMatch(obj, source) {
  var objKeys = plugins_xeUtils_keys.keys(obj);
  var sourceKeys = plugins_xeUtils_keys.keys(source);
  if (sourceKeys.length) {
    if (plugins_xeUtils_includeArrays.includeArrays(objKeys, sourceKeys)) {
      return plugins_xeUtils_some.some(sourceKeys, function(key2) {
        return plugins_xeUtils_findIndexOf.findIndexOf(objKeys, function(key1) {
          return key1 === key2 && plugins_xeUtils_isEqual.isEqual(obj[key1], source[key2]);
        }) > -1;
      });
    }
  } else {
    return true;
  }
  return plugins_xeUtils_isEqual.isEqual(obj, source);
}
exports.isMatch = isMatch;
