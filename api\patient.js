import http from '../common/request/request.js';

//资料服务(post请求)
export function getRegList(param = {}) {
  return http({
    url: 'business/proregister/getRegList',
    param,
    method: 'post',
  });
}

// 查询本次挂号是否有处方，检验单
export function getIsPre(param = {}) {
  return http({
    url: 'business/proPrescriptionController/getIsPre',
    param,
    method: 'post',
  });
}

// 查询某个就诊人信息
export function getVisitingPersonInfo(param = {}) {
  return http({
    url: 'basic/proofpatient/getVisitingPersonInfo',
    param,
    method: 'post',
  });
}
