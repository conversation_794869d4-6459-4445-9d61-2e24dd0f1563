<template>
  <view class="spe_box">
  <!-- 标题 -->
  <view class="spe_title">
    <TITLE title="特色专科" />
  </view>
  <!-- 图片-->
  <!-- <UniImage class="spe_img"
    v-if="speImg"
    :src="speImg"
  /> -->
  <image class="spe_img" src="/static/images/featuredDepartments.png" @click="goSpe"></image>
  </view>
</template>

<script>
  import TITLE from "@/pages/inspect/com/itemTitle.vue";
  
  export default{
    components: {
      TITLE,
    },
    props: {
      speImg:''
    },
    data(){
      return{
        
      }
    },
    methods:{
      goSpe(){
      uni.navigateTo({ url: '/pages/index/com/specialtySpecialtyList' });  
      },
    }
  }
</script>

<style lang="scss" scoped>
.spe_box {
  background: #fff;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  .spe_title {
    padding: 0 32rpx;
    height: 80rpx;
    @include flex(left);
  }
  .spe_img{
    width: 94%;
    height: 160rpx;
    border-radius: 8rpx;
    margin: 20rpx;
  }
}
</style>


