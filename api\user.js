import http from "../common/request/request.js";

// 获取openid
export function getOpenId(param = {}) {
    return http({
        url: "chat/syswechatuser/getOpenId",
        param,
        method: "post",
    });
}

// 获取用户信息
export function getUsertInfo(param = {}) {
    return http({
        url: "basic/hisLogin/hisPatientLoginAddToken",
        param,
        method: "post",
    });
}

// 获取手机验证码、
export function sendCaptcha(param = {}) {
    return http({
        url: "basic/hisLogin/sendCaptcha",
        param,
        method: "post",
    });
}

// 绑定用户信息
export function hisPatientLoginBindingUserInfo(param = {}) {
    return http({
        url: "basic/hisLogin/hisPatientLoginBindingUserInfo",
        param,
        method: "post",
    });
}

// 查询某个就诊人信息
export function getPatientInfo(param = {}) {
    return http({
        url: "basic/proofpatient/getVisitingPersonInfo",
        param,
        method: "post",
    });
}

// 修改患者端服务提示状态(post请求)
export function updateIsServiceReminderStatus(param = {}) {
    return http({
        url: "basic/propatient/updateIsServiceReminderStatus",
        param,
        method: "post",
    });
}

// 通知后台环信注册成功
export function updateHxidIsregistStatus(param = {}) {
    return http({
        url: "basic/hisLogin/updateHxidIsregistStatus",
        param,
        method: "post",
    });
}

// 获取该用户下的所有就诊人
export function getPatientList(param = {}) {
    return http({
        url: "basic/proofpatient/findProOfPatientListIsEnabled",
        param,
        method: "post",
    });
}

// 添加就诊人(post请求)
export function addPatient(param = {}) {
    return http({
        url: "basic/proofpatient/addVisitingPerson",
        param,
        method: "post",
    });
}

// 删除就诊人
export function deletePatient(param = {}) {
    return http({
        url: "basic/proofpatient/deleteVisitingPerson",
        param,
        method: "post",
    });
}

// 修改就诊人
export function editorPatient(param = {}) {
    return http({
        url: "basic/proofpatient/updateVisitingPerson",
        param,
        method: "post",
    });
}

// 修改就诊人基本情况
export function updatePatientBasicState(param = {}) {
    return http({
        url: "basic/proofpatient/updatePatientBasicState",
        param,
        method: "post",
    });
}

// 校验手机验证码
export function cationCode(param = {}) {
    return http({
        url: "basic/commonUtil/verificationCode",
        param,
        method: "post",
    });
}

// 添加老的就诊人
export function addOldPatient(param = {}) {
    return http({
        url: "basic/proofpatient/updatePatientNewPhoneAndUserIdByIdNo",
        param,
        method: "post",
    });
}

// 获取患者的就诊状态
export function getPatientReceiveStatus(param = {}) {
    return http({
        url: "business/proreceive/getPatientReceiveStatus",
        param,
        method: "post",
    });
}
export function getGhCanPay(param = {}) {
    return http({
        url: "business/proreceive/getGhCanPay",
        param,
        method: "post",
    });
}
// 获取患者的就诊状态
export function getPatientReceive(param = {}) {
    return http({
        url: "basic/proPatientBusiness/getPatientChatStatus",
        param,
        method: "post",
    });
}

// 获取就诊人信息
export function findPatientByPatientId(param = {}) {
    return http({
        url: "basic/propatient/findPatientByPatientId",
        param,
        method: "post",
    });
}

// 患者端查询个人中心基本信息接口
export function getPatientPersonalCenterInfo(param = {}) {
    return http({
        url: "basic/pharmacistInfo/getPatientPersonalCenterInfo",
        param,
        method: "post",
    });
}
// 患者端修改个人中心基本信息接口
export function updatePatientPersonalCenterInfo(param = {}) {
    return http({
        url: "basic/pharmacistInfo/updatePatientPersonalCenterInfo",
        param,
        method: "post",
    });
}

// 查询该用户下所有有效的就诊人id和name列表
export function findIsEnabledPatientByUserId(param = {}) {
    return http({
        url: "basic/proofpatient/findIsEnabledPatientByUserId",
        param,
        method: "post",
    });
}
//获取关系
export function findPatientRelationDict(param = {}) {
    return http({
        url: "basic/proOfPatientRelationDict/findPatientRelationDict",
        param,
        method: "post",
    });
}

// 补全手机号
export function patientCompletionTelNo(param = {}) {
    return http({
        url: "basic/hisLogin/patientCompletionTelNo",
        param,
        method: "post",
    });
}

// 补全手机号
export function getLatestDetail(regId) {
    return http({
        url: "business/proElectronicMedicalRecord/getLatestDetail",
        param: {
            regId,
        },
        method: "post",
    });
}

export function getPatientNucleicInfo(param = {}) {
    return http({
        url: "business/nucleicTest/getPatientNucleicInfo",
        param,
        method: "post",
    });
}

// 病历资料保存
export function savePatientCase(param = {}) {
    return http({
        url: "basic/proPatienattach/save",
        param,
        method: "post",
    });
}

// 病历资料查询
export function getPatientCase(param = {}) {
    return http({
        url: "basic/proPatienattach/list",
        param,
        method: "post",
    });
}

// 病历资料查询
export function deletePatientCase(param = {}) {
    return http({
        url: "basic/proPatienattach/disable",
        param,
        method: "post",
    });
}
export function empowerConsent(param = {}) {
    return http({
        url: "basic/proPatienattach/empowerConsent",
        param,
        method: "post",
    });
}
export function selectInformConsent(param = {}) {
    return http({
        url: "basic/proPatienattach/selectInformConsent",
        param,
        method: "post",
    });
}
export function findVisitAgreement(param = {}) {
    return http({
        url: "basic/sysvisitagreement/findVisitAgreement",
        param,
        method: "post",
    });
}
export function updatePatientCase(param = {}) {
    return http({
        url: "basic/proPatienattach/update",
        param,
        method: "post",
    });
}
export function findAll(param = {}) {
    return http({
        url: "dictionary/dicdiagnosis/findAll",
        param,
        method: "post",
    });
}
export function findAllListPage(param = {}) {
    return http({
        url: "dictionary/dicdiagnosis/findAllListPage",
        param,
        method: "post",
    });
}
export function findDrugById(param = {}) {
    return http({
        url: "basic/dicDrugController/findDrugById",
        param,
        method: "post",
    });
}
export function doccommondrug(param = {}) {
    return http({
        url: "basic/doccommondrug/add",
        param,
        method: "post",
    });
}
export function getDicDrugType(param = {}) {
    return http({
        url: "basic/dicDrugType/getDicDrugType",
        param,
        method: "post",
    });
}
export function findAllDrug(param = {}) {
    return http({
        url: "basic/dicDrugController/patientFindAllDrug",
        param,
        method: "post",
    });
}
export function findDrugByDocId(param = {}) {
    return http({
        url: "basic/dicDrugController/findDrugByDocId",
        param,
        method: "post",
    });
}
export function saveQuickPrescriptionRegister(param = {}) {
    return http({
        url: "business/paymentBusiness/saveQuickPrescriptionRegister",
        param,
        method: "post",
    });
}
export function createPatientAllocateDoc(param = {}) {
    return http({
        url: "business/quickPrescription/createPatientAllocateDoc",
        param,
        method: "post",
    });
}

export function saveSuggestion(param = {}) {
    return http({
        url: "basic/proComplaintsSuggestions/save",
        param,
        method: "post",
    });
}
export const drugClassificationList = (param) => {
    return http({
        url: "basic/drugClassification/list",
        param,
        method: "post",
    });
};
export const webDicDrugListPage = (param) => {
    return http({
        url: "basic/webDicDrug/listPage",
        param,
        method: "post",
    });
};

// 新需求 判断患者是否注册
export const queryRegisterStatus = (param) => {
    return http({
        url: "basic/dicPatientUser/queryRegisterStatus",
        param,
        method: "post",
    });
};
export const saveScanCodeRegister = (param) => {
    return http({
        url: "business/paymentBusiness/saveScanCodeRegister",
        param,
        method: "post",
    });
};
export const updateScanCodeRegister = (param) => {
    return http({
        url: "business/paymentBusiness/updateScanCodeRegister",
        param,
        method: "post",
    });
};
export const insertRecord = (param) => {
    return http({
        url: "basic/pushResultRecord/insertRecord",
        param,
        method: "post",
    });
};
export const saveOriginal = (param) => {
    return http({
        url: "basic/szAiReport/saveOriginal",
        param,
        method: "post",
    });
};
export const judgePatientFirstQuestionnaire = (param) => {
    return http({
        url: "basic/szAiReport/judgePatientFirstQuestionnaire",
        param,
        method: "post",
    });
};
export const checkFirstFill = (param) => {
    return http({
        url: "basic/replyInquiringDiagnosis/checkFirstFill",
        param,
        method: "post",
    });
};
export const getReportByPatientId = (param) => {
    return http({
        url: "basic/szAiReport/getReportByPatientId",
        param,
        method: "post",
    });
};