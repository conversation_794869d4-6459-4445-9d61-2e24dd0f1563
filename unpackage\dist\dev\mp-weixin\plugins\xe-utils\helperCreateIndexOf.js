"use strict";
const plugins_xeUtils_isArray = require("./isArray.js");
const plugins_xeUtils_isString = require("./isString.js");
const plugins_xeUtils_hasOwnProp = require("./hasOwnProp.js");
function helperCreateIndexOf(name, callback) {
  return function(obj, val) {
    if (obj) {
      if (obj[name]) {
        return obj[name](val);
      }
      if (plugins_xeUtils_isString.isString(obj) || plugins_xeUtils_isArray.isArray(obj)) {
        return callback(obj, val);
      }
      for (var key in obj) {
        if (plugins_xeUtils_hasOwnProp.hasOwnProp(obj, key)) {
          if (val === obj[key]) {
            return key;
          }
        }
      }
    }
    return -1;
  };
}
exports.helperCreateIndexOf = helperCreateIndexOf;
