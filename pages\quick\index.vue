<template>
  <div class="quick">
    <div class="detail">
      <!-- 标题 -->
      <view class="title">
        <text>药品明细</text>
        <image @click="showEwm" class="ewm_icon" src="/static/pre/ewm.png" />
      </view>
      <!-- 药品 -->
      <view class="durg_list">
        <!-- 单个药品 -->
        <view class="durg_item" v-for="drug in list" :key="drug.drugId">
          <UniImage v-if="drug.drugImg"
            :src="drug.drugImg"
            :data-src="errUrl"
            class="left"
          />
          <image v-else class="left" src="/static/shop/drug.png"></image>
          <!-- 内容 -->
          <view class="right">
            <!-- 药品名称 -->
            <view class="drug_name">{{ drug.drugName }}</view>
            <!-- 规格 -->
            <view class="drug_info">规格: {{ drug.gg }}</view>
            <!-- 价位数量 -->
            <view class="right_menu">
              <text class="price">￥{{ drug.price }}</text>
              <text class="num">x{{ drug.quan }}</text>
            </view>
          </view>
        </view>

        <!-- 统计 -->
        <view class="count">
          <text class="num">共{{ list.length }}种药品</text>
          <view class="count_price">
            合计：<text>￥{{ durg_price.toFixed(2) }}</text>
          </view>
        </view>
      </view>
    </div>

    <!-- 弹框 -->
    <uni-popup ref="popup" type="center">
      <EWM v-if="orderNo" :code="orderNo" />
    </uni-popup>

    <FOOTER @click="send">立即支付</FOOTER>
  </div>
</template>

<script>
import EWM from './com/ewm.vue';
import FOOTER from '@/components/footer_button/button.vue';
import { getUsertInfo } from '@/api/user';
import {
  queryFastPrescription,
  createScanCodeFastPrescription,
  getReceiptWay,
  getScanCodeFastPrescriptionOrderStatus,
  updateOrderPatient,
} from '@/api/order';
import {
  fastFkGenerateUser,
  createFastPrescription,
  findPatientInfoByPatientId,
} from '@/api/base';

import { wxPay } from '@/common/js/pay';

import { Toast } from '@/common/js/pay.js';

// 线下支付
let offlineTimer;
// 线上
let timer;

export default {
  name: 'Quick',
  components: {
    EWM,
    FOOTER,
  },
  data() {
    return {
      errUrl: require('../../static/shop/drug.png'),
      // 医生id
      docId: '',
      // 处方模板id
      dpmpId: '',
      // 消息id
      msgid: '',
      // 药品列表
      list: [],
      // 药品合计金额
      durg_price: 0,
      // 订单号
      orderNo: '',
      // 支付方式
      payList: [],
      // 倒计时
      num: 3,
      // 是否可以继续点击
      isNext: true,
      // 是否成功
      isSuc: false,
    };
  },
  async onLoad(v) {
    let { dpmpId, msgid, docId } = v;
    this.dpmpId = dpmpId;
    this.msgid = msgid;
    this.docId = docId;
    // 不存在用户
    if (!uni.getStorageSync('userId')) {
      await this.createdUser();
    }
    this.getDetail();
  },
  methods: {
    // 获取药店支付方式
    async getPayList(subjectId) {
      let { data } = await getReceiptWay({
        subjectId,
      });
      this.payList = data;
    },
    // 创建用户
    async createdUser() {
      let { data } = await fastFkGenerateUser({
        appid: uni.getStorageSync('appId'),
        openid: uni.getStorageSync('wxInfo').openId,
        docId: this.docId,
      });
      let { patientIdList, token, userId } = data;
      uni.setStorageSync('proPfInfo', { token });
      uni.setStorageSync('patientIdList', patientIdList || []);
      uni.setStorageSync('userId', userId);

      this.getUserInfoFun();
    },
    // 获取用户信息
    async getUserInfoFun() {
      let para = {
        appid: uni.getStorageSync('appId'),
        openid: uni.getStorageSync('wxInfo').openId,
      };
      // 请求用户信息
      let res = await getUsertInfo(para);

      let proPfInfo = res.data;
      // 存储用户信息
      this.$store.commit('setProPfInfo', proPfInfo);

      // userId
      uni.setStorageSync('userId', proPfInfo.userId);

      // 该用户下所有的患者id
      uni.setStorageSync('patientIdList', proPfInfo.patientIdList || []);
    },
    // 展示二维码
    async showEwm() {
      clearInterval(offlineTimer);
      uni.showLoading({
        mask: true,
      });
      try {
        let {
          data: { orderNo },
        } = await createScanCodeFastPrescription({
          appid: uni.getStorageSync('appId'),
          callId: 1,
          docId: this.docId,
          dpmpId: this.dpmpId,
          msgId: this.msgid,
          openid: uni.getStorageSync('wxInfo').openId,
          payType: 5, // 扫码确认
        });

        this.orderNo = orderNo;
        this.$refs.popup.open();
        this.getOfflineStatus();
      } catch (error) {
        uni.hideLoading();
      }
    },
    // 获取药品信息
    async getDetail() {
      let { data } = await queryFastPrescription({
        docId: this.docId,
        msgid: this.msgid,
        dpmpId: this.dpmpId,
      });
      let { drugList, totalMoney, drugstoreId } = data;
      this.list = drugList;
      this.durg_price = totalMoney;
      // 获取支付方式
      this.getPayList(drugstoreId);
    },
    // 选择支付方式
    async selePay() {
      let that = this;
      return new Promise((resolve, reject) => {
        // 如果不可选支付
        if (!this.payList.length) {
          Toast('当前没有配置支付方式');
          return reject();
        }
        // 如果只有一个支付方式
        if (this.payList.length == 1) {
          let item = this.payList[0];
          if (item.receiptType == 1) {
            resolve({ index: 1, item });
          } else {
            resolve({ index: 2, item });
          }
          return;
        }
        // 如果在线支付
        uni.showActionSheet({
          itemList: ['微信支付', '支付宝支付'],
          success(res) {
            let index = res.tapIndex + 1;
            let item;
            item = that.payList.filter((it) => it.receiptType == index)[0];
            if (!item) {
              Toast('暂不支持该支付方式');
              return reject();
            }
            resolve({ index, item });
          },
          fail(err) {
            reject(err);
          },
        });
      });
    },
    // 提交订单
    async send() {
      // 如果已支付
      if (this.isSuc) {
        this.toPath();
        return;
      }
      this.isNext = false;
      let { index, item } = await this.selePay();

      uni.showLoading({
        mask: true,
      });
      let obj = {
        appid: uni.getStorageSync('appId'),
        callId: item.appid,
        docId: this.docId,
        dpmpId: this.dpmpId,
        msgId: this.msgid,
        openid: uni.getStorageSync('wxInfo').openId,
        payType: index,
      };
      try {
        let { data } = await createScanCodeFastPrescription(obj);
        this.orderNo = data.orderNo;
        delete data.orderNo;
        uni.hideLoading();
        // 微信
        if (index == 1) {
          this.toWx(data);
          return;
        }

        // 支付宝
        if (index == 2) {
          let money = this.durg_price;
          uni.navigateTo({
            url:
              '/pages/pay/pay?price=' +
              money +
              '&quick=' +
              this.orderNo +
              '&dpmpId=' +
              this.dpmpId +
              '&url=' +
              btoa(data.url),
          });
          return;
        }
      } catch (error) {
        this.isNext = true;
        uni.hideLoading();
      }
    },
    // 微信支付
    async toWx(info) {
      await wxPay(info);
      // 请求支付状态
      this.getStatus();
    },
    // 获取线下支付状态
    async getOfflineStatus() {
      let { data } = await getScanCodeFastPrescriptionOrderStatus({
        orderNo: this.orderNo,
        payType: 5,
      });
      if (data && data.status == 2) {
        clearTimeout(offlineTimer);
        this.isSuc = true;
        // 跳转页面
        this.toPath();
        return;
      }
      offlineTimer = setTimeout(this.getOfflineStatus, 2000);
    },
    // 查询线上支付状态
    async getStatus() {
      this.num--;
      let {
        data: { status },
      } = await getScanCodeFastPrescriptionOrderStatus({
        orderNo: this.orderNo,
        payType: 1, // 微信
      });
      if (status == 2) {
        this.isSuc = true;
        clearTimeout(timer);
        // 跳转页面
        this.toPath();
        return;
      }
      // 停止轮询
      if (this.num <= 0) {
        clearTimeout(timer);
        return;
      }
      timer = setTimeout(this.getStatus, 2000);
    },
    // 跳转页面
    async toPath() {
      // 先清除定时器
      if (offlineTimer) clearTimeout(offlineTimer);
      if (timer) clearTimeout(timer);

      let patientIdList = uni.getStorageSync('patientIdList') || [];
      // 没有就诊人
      if (!patientIdList.length) {
        uni.redirectTo({
          url:
            '/pages/quick/info?orderNo=' +
            this.orderNo +
            '&dpmpId=' +
            this.dpmpId,
        });
        return;
      }
      // 只有一个就诊人
      if (patientIdList.length == 1) {
        let { data } = await findPatientInfoByPatientId(patientIdList[0]);
        await createFastPrescription({
          ...data,
          orderNo: this.orderNo,
          dpmpId: this.dpmpId,
        });
        await updateOrderPatient({
          patientId: data.patientId,
          orderNo: this.orderNo,
        });

        uni.redirectTo({
          url: '/pages/quick/result?orderNo=' + this.orderNo,
        });
        return;
      }
      // 选择就诊人
      if (patientIdList.length > 1) {
        uni.redirectTo({
          url:
            '/pages/quick/sele?orderNo=' +
            this.orderNo +
            '&dpmpId=' +
            this.dpmpId,
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
page {
  background-color: #f5f5f5;
}
.quick {
  padding-bottom: 100rpx;

  .detail {
    padding: 24rpx 32rpx;

    .title {
      font-size: 32rpx;
      @include flex(lr);
      height: 88rpx;
      background-color: #fff;
      border-radius: 8rpx 8rpx 0 0;
      padding: 0 24rpx;
      border-bottom: 1px solid #f5f5f5;
      font-weight: bold;

      .ewm_icon {
        width: 44rpx;
        height: 44rpx;
      }
    }

    .durg_list {
      background-color: #fff;
      padding: 24rpx 24rpx 0;
      margin-bottom: 24rpx;
      border-radius: 0 0 8rpx 8rpx;

      .durg_item {
        @include flex;
        padding: 16rpx 0;

        &:last-child {
          .right {
            border-bottom: none;
          }
        }

        .left {
          width: 128rpx;
          height: 128rpx;
          margin-right: 24rpx;
          border: 1px solid #eee;
          border-radius: 8rpx;
          flex: none;
        }

        .right {
          flex: 1;
          min-height: 128rpx;
          @include flex(lr);
          flex-direction: column;
          align-items: stretch;
          border-bottom: 1px solid #eee;

          .drug_name {
            flex: 1;
            font-size: 28rpx;
            font-weight: bold;
          }

          .drug_info {
            flex: 1;
            font-size: 24rpx;
            color: #999;
          }

          .right_menu {
            flex: 2;
            @include flex(lr);
            align-items: flex-end;

            .price {
              font-size: 28rpx;
              color: #ff3b30;
              font-weight: bold;
            }

            .num {
              font-size: 28rpx;
            }
          }
        }
      }

      .count {
        @include flex(lr);
        height: 88rpx;
        font-size: 28rpx;

        .count_num {
          color: #333;
        }

        .count_price {
          text {
            color: #ff3b30;
          }
        }
      }
    }
  }
}
</style>
