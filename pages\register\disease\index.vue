<template>
  <view>
    <view class="page-container">
      <view class="search-container">
        <uni-search-bar
          placeholder="请输入您想要搜索的内容"
          cancelButton="none"
          @confirm="search"
          @input="changeInput"
        >
        </uni-search-bar>
      </view>
      <template v-if="list.length">
        <view class="section_doc">
          <!-- <scroll-view scroll-y="true" class="section_doc" @scrolltolower="getMore"> -->
          <!-- 疾病列表 -->
          <template v-for="item in list">
            <view class="section_doc_list" @click="goDocList(item.diseaseName)">
              <text>{{ item.diseaseName }}</text>
              <uni-icons type="arrowright" size="22"></uni-icons>
            </view>
          </template>
          <!-- 加载更多 -->
          <view v-if="isShowMore">
            <uni-load-more :status="status"></uni-load-more>
          </view>
        </view>
        <!-- </scroll-view> -->
      </template>

      <!-- 空列表 -->
      <view class="empty_list" v-else>
        <image src="/static/images/index/office_empty.png" />
        <view> 暂无相关疾病 </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getDicDisease } from '@/api/base.js';
export default {
  data() {
    return {
      list: [],
      listQuery: {
        diseaseName: '',
        page: 1,
        limit: 20,
      },
      total: 0,
      isShowMore: false,
      status: 'loading',
    };
  },
  onLoad() {
    this.getList();
  },
  onPullDownRefresh() {
    this.list = [];
    this.listQuery.diseaseName = '';
    this.listQuery.page = 1;
  },
  onReachBottom() {
    if (this.list.length < this.total) {
      this.listQuery.page += 1;
      this.getList();
    }
  },
  methods: {
    getMore() {
      this.isShowMore = true;
      // 共几页
      let num = Math.ceil(this.total / this.listQuery.limit);
      if (this.listQuery.page < num) {
        this.listQuery.page += 1;
        this.getList();
        this.isShowMore = false;
      } else {
        this.status = 'noMore';
      }
    },
    async getList() {
      uni.stopPullDownRefresh();
      let res = await getDicDisease(this.listQuery);
      this.total = res.data.total;
      this.list = this.list.concat(res.data.rows);
    },
    search(e) {
      this.list = [];
      this.listQuery = {
        diseaseName: e.value,
        page: 1,
        limit: 20,
      };
      this.getList();
    },
    changeInput(e) {
      // 清空输入框内容时查询
      if (e.value == '') {
        this.list = [];
        this.listQuery = {
          diseaseName: '',
          page: 1,
          limit: 20,
        };
        this.getList();
      }
    },
    goDocList(id) {
      uni.navigateTo({
        url: '/pages/register/docList/index?name=' + id,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  background-color: #fff;
  padding-bottom: 30rpx;
}

.search-container {
  position: sticky;
  top: 0;
  z-index: 3;
}

/* 搜索框样式 */
::v-deep.uni-searchbar {
  padding: 0;
  height: 88rpx;
  border-bottom: 2rpx solid #ebebeb;
}

::v-deep.uni-searchbar__box {
  height: 88rpx;
  background: #ffffff !important;
  border-radius: 0px !important;
  border: none;
}

/* 科室列表 */
.section_doc {
  padding: 0 32rpx;
  height: calc(100% - 90rpx);
  box-sizing: border-box;
}

.section_doc_list {
  @include flex(lr);
  color: $k-title;
  font-size: 30rpx;
  height: 88rpx;
  background: #e6ebee;
  border-radius: 8rpx;
  margin-top: 16rpx;
  padding: 0 32rpx;
  font-weight: 400;
}

/* 列表为空提示 */
.empty_list {
  @include flex(center);
  flex-direction: column;
  font-size: 26rpx;
  font-weight: 400;
  color: rgba(158, 163, 173, 1);
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.empty_list image {
  margin-bottom: 40rpx;
  width: 386rpx;
  height: 324rpx;
}
</style>
