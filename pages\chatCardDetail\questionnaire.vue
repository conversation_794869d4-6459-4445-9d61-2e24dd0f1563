<template>
  <view class="questionnaire">
    <view class="header">
      <view class="title">{{ saveInfo.didName }}</view>
      <view class="tips" v-if="!pageParam.lbType"
        >感谢您能抽出几分钟时间来参加量表，为了方便医生了解病情，请认真作答，谢谢！</view
      >
    </view>

    <!-- 提交成功弹窗 -->
    <view class="success-dialog-overlay" v-if="showSuccessDialog">
      <view class="success-dialog-container">
        <view class="success-dialog-content">
          <view class="success-dialog-message">
            正在为您生成专属营养评估报告...
          </view>
          <view class="success-dialog-message">
            您的专属AI营养师正在根据您刚刚填写的健康与饮食信息进行专业分析和智能解读，确保每一项建议都贴合您的实际情况。
          </view>
          <view class="success-dialog-message">
            感谢您的配合，结果马上就好
          </view>
        </view>
        <view class="success-dialog-footer">
          <button class="success-dialog-btn" @click="handleSuccessConfirm">
            确认
          </button>
        </view>
      </view>
    </view>
    <view class="quesView">
      <view
        v-for="(item, index) in saveInfo.saveReplyInquiringDiagnosisTopics"
        :key="index"
      >
        <!-- 单选 -->
        <view class="radioView quesAnswerView" v-if="item.didtType == 1">
          <view class="didName"
            >{{ index + 1 }}.{{ item.didtName }}
            <text v-if="item.isMust == 1" style="color: red">*</text>
          </view>
          <radio-group @change="radioChange($event, item, index)">
            <label
              class="uni-list-cell uni-list-cell-pd"
              v-for="(radioItem, radioIndex) in item.answerInfo"
              :key="radioIndex"
            >
              <view>
                <radio
                  style="transform: scale(0.7)"
                  :value="JSON.stringify(radioItem.optionCode)"
                  :checked="radioItem.checked"
                />
                <text>{{ radioItem.optionName }}</text>
              </view>
            </label>
          </radio-group>
        </view>
        <!-- 多选 -->
        <view class="checkView quesAnswerView" v-if="item.didtType == 2">
          <view class="didName"
            >{{ index + 1 }}.{{ item.didtName }}
            <text v-if="item.isMust == 1" style="color: red">*</text>
          </view>
          <checkbox-group @change="checkboxChange($event, item, index)">
            <label
              class="uni-list-cell uni-list-cell-pd"
              v-for="(checkItem, checkIndex) in item.answerInfo"
              :key="checkIndex"
            >
              <view>
                <checkbox
                  style="transform: scale(0.7)"
                  :value="JSON.stringify(checkItem.optionCode)"
                  :checked="checkItem.checked"
                />
                <text>{{ checkItem.optionName }}</text>
              </view>
            </label>
          </checkbox-group>
        </view>
        <!-- 填空题 -->
        <view class="contentView quesAnswerView" v-if="item.didtType == 3">
          <view class="didName"
            >{{ index + 1 }}.{{ item.didtName }}
            <text v-if="item.isMust == 1" style="color: red">*</text>
          </view>
          <view class="content">
            <input
              class="uni-input"
              type="number"
              placeholder-style="color:#999999;font-size:13px;"
              placeholder="请输入数字"
              v-if="item.answerLimitName == '数字'"
              v-model="item.checkedAnswer"
              @focus="handleInputFocus(index)"
              @blur="handleInputBlur"
            />
            <!-- '请输入' + item.answerLimitName + '类型的答案' -->
            <textarea
              placeholder-style="color:#999999;font-size:13px;"
              v-else
              :placeholder="item.answerLimit ? '请输入文字' : '请输入文字'"
              :auto-height="false"
              v-model="item.checkedAnswer"
              @focus="handleInputFocus(index)"
              @blur="handleInputBlur"
            />
          </view>
        </view>
        <!-- 下拉题 -->
        <view class="selectView quesAnswerView" v-if="item.didtType == 4">
          <view class="uni-title uni-common-pl didName"
            >{{ index + 1 }}.{{ item.didtName }}
            <text v-if="item.isMust == 1" style="color: red">*</text>
          </view>

          <!-- 简明膳食地区选择tabs和图片 -->
          <view
            v-if="
              item.didtName && item.didtName.includes('简明膳食：请您参考下图')
            "
            class="diet-region-tabs"
          >
            <view class="diet-tabs-container">
              <view
                v-for="(region, regionIndex) in regionOptions"
                :key="regionIndex"
                class="diet-tab"
                :class="{ active: selectedRegion === region }"
                @click="changeRegion(region)"
              >
                {{ region }}
              </view>
            </view>

            <!-- 地区饮食图片展示 -->
            <view class="diet-image-container">
              <image
                v-if="selectedRegion === '北方'"
                src="/static/images/form/简明膳食自评表（北方）-.png"
                mode="widthFix"
                class="diet-image"
                @click="
                  previewImage(
                    '/static/images/form/简明膳食自评表（北方）-.png'
                  )
                "
              />
              <image
                v-if="selectedRegion === '西北'"
                src="/static/images/form/简明膳食自评表（西北）-.png"
                mode="widthFix"
                class="diet-image"
                @click="
                  previewImage(
                    '/static/images/form/简明膳食自评表（西北）-.png'
                  )
                "
              />
              <image
                v-if="selectedRegion === '江南'"
                src="/static/images/form/简明膳食自评表（江南）-.png"
                mode="widthFix"
                class="diet-image"
                @click="
                  previewImage(
                    '/static/images/form/简明膳食自评表（江南）-.png'
                  )
                "
              />
            </view>
          </view>

          <picker
            @change="bindPickerChange($event, item, index)"
            :value="item.pickerIndex"
            :range="item.answerInfo"
            range-key="optionName"
          >
            <view class="uni-input dateInput" v-if="item.pickerIndex >= 0"
              >{{ item.answerInfo[item.pickerIndex].optionName }}
              <image
                src="../../static/images/question/down.png"
                class="downIcon"
              ></image>
            </view>
            <view class="uni-input dateInput dateInputPlac" v-else
              >请选择答案
              <image
                src="../../static/images/question/down.png"
                class="downIcon"
              ></image>
            </view>
          </picker>
        </view>
        <!-- 上传图片 -->
        <view class="imgView quesAnswerView" v-if="item.didtType == 5">
          <view class="didName"
            >{{ index + 1 }}.{{ item.didtName }}
            <text v-if="item.isMust == 1" style="color: red">*</text>
          </view>
          <view
            class="dateInput"
            @click="selectImg(item, index)"
            v-if="!item.checkedAnswer"
          >
            <image src="../../static/images/up_img.png" class="imgIcon"></image>
            <text>选择图片（2M以内）</text>
            <image
              src="../../static/images/question/image-editor.png"
              class="editIcon"
            ></image>
          </view>
          <view v-if="item.checkedAnswer" @click.stop="selectImg(item, index)">
            <image
              :src="item.checkedAnswer"
              class="upImg"
              mode="aspectFill"
            ></image>
            <image
              src="../../static/images/question/image-delete.png"
              class="delIcon"
              @click.stop="item.checkedAnswer = ''"
            ></image>
          </view>
        </view>
        <!-- 评价 -->
        <view class="contentView quesAnswerView" v-if="item.didtType == 6">
          <view class="didName"
            >{{ index + 1 }}.{{ item.didtName }}
            <text v-if="item.isMust == 1" style="color: red">*</text>
          </view>
          <rate
            @change="rateChange($event, item, index)"
            size="60"
            :star_empty="require('../../static/images/question/assess.png')"
            :star_fill="
              require('../../static/images/question/assess_active.png')
            "
          ></rate>
        </view>
        <!-- 时间 -->
        <view class="timeView quesAnswerView" v-if="item.didtType == 7">
          <view class="didName"
            >{{ index + 1 }}.{{ item.didtName }}
            <text v-if="item.isMust == 1" style="color: red">*</text>
          </view>
          <picker
            mode="time"
            :value="time"
            start="00:00"
            end="23:59"
            @change="bindTimeChange($event, item, index)"
          >
            <view class="uni-input dateInput" v-if="item.checkedAnswer"
              >{{ item.checkedAnswer }}
              <image
                src="../../static/images/question/down.png"
                class="downIcon"
              ></image>
            </view>
            <view class="uni-input dateInput dateInputPlac" v-else
              >请选择时间
              <image
                src="../../static/images/question/down.png"
                class="downIcon"
              ></image>
            </view>
          </picker>
        </view>
        <!-- 日期 -->
        <view class="dateView quesAnswerView" v-if="item.didtType == 8">
          <view class="didName"
            >{{ index + 1 }}.{{ item.didtName }}
            <text v-if="item.isMust == 1" style="color: red">*</text>
          </view>
          <view class="dateInput" v-if="item.disabled">
            {{ item.checkedAnswer }}
          </view>
          <picker
            v-else
            mode="date"
            :value="date"
            start="1900-01-01"
            end="3030-12-31"
            @change="bindDateChange($event, item, index)"
          >
            <view class="uni-input dateInput" v-if="item.checkedAnswer"
              >{{ item.checkedAnswer }}
              <image
                src="../../static/images/question/down.png"
                class="downIcon"
              ></image>
            </view>
            <view class="uni-input dateInput dateInputPlac" v-else
              >请选择日期
              <image
                src="../../static/images/question/down.png"
                class="downIcon"
              ></image>
            </view>
          </picker>
        </view>
      </view>
    </view>

    <!-- 语音输入按钮 -->
    <view class="voice-input-fixed">
      <view
        class="voice-input-btn"
        :class="{ active: isRecording }"
        @click="toggleVoiceInput"
      >
        <image src="/static/images/mic.png" class="mic-icon"></image>
        <text v-if="isRecording" class="recording-text">录音中</text>
      </view>
    </view>

    <!-- 隐藏的VoiceCommon组件，只用于处理语音功能 -->
    <view style="position: absolute; opacity: 0; pointer-events: none">
      <VoiceCommon
        ref="voiceCommon"
        :isRealTime="true"
        @getVoice="handleVoiceResult"
        @init="initVoice"
        :disFlag="false"
      />
    </view>

    <view class="commit" @click="saveQuestionAnswerFun">
      <button>提交</button>
    </view>
  </view>
</template>

<script>
import { Toast } from '@/common/js/pay.js'
import {
  getQuestionDetailEmpty,
  saveQuestionAnswer,
  setQuestionRead,
  saveReplyInquiringDiagnosisAnswerCustom,
} from '@/api/chatCardDetail'
import rate from '@/components/rate/rate.vue'
import { uploadImg } from '@/api/oss'
import myJsTools from '@/common/js/myJsTools.js'
import { invokeWorkflow, stopWorkflow, getDoctorInfoByDocId } from '@/api/qcAi'
import {
  sendGroupMessage,
  gutFunctionLbLbGenerate,
  dailyReminderLbGenerate,
  weeklyCycleGenerate,
} from '@/api/qcAi'
import { insertRecord, saveOriginal } from '@/api/user.js'
import VoiceCommon from '@/components/VoiceCommon.vue'

export default {
  components: { rate, VoiceCommon },
  data() {
    return {
      pageParam: '',
      saveInfo: '',
      time: '',
      pickerIndex: -1,
      date: '',
      feedbackId: '',
      isWorkflowRunning: false,
      workflowSessionId: '',
      lastNodeId: '',
      message_id: '',
      input_schema: '',
      showSuccessDialog: false,
      isSubmitting: false,
      isRecording: false, // 是否在录音中
      focusedIndex: -1, // 当前聚焦的输入框索引
      oldMsg: '',
      // 简明膳食地区tab
      selectedRegion: '北方',
      regionOptions: ['北方', '西北', '江南'],
    }
  },
  onLoad(option) {
    this.pageParam = JSON.parse(option.param)
  },
  created() {
    this.getQuestionDetailEmptyFun()
    this.setQuestionReadFun()
    this.$nextTick(() => {
      if (this.$refs.voiceCommon) {
        this.$refs.voiceCommon.init()
      }
    })
  },
  methods: {
    previewImage(url) {
      uni.previewImage({
        urls: [url],
      })
    },
    // 语音识别相关方法
    initVoice() {
      this.isRecording = false
      uni.showToast({
        title: '连接成功，请点击开始录音',
        icon: 'none',
      })
    },

    // 处理输入框聚焦
    handleInputFocus(index) {
      this.focusedIndex = index
      this.oldMsg =
        this.saveInfo.saveReplyInquiringDiagnosisTopics[index].checkedAnswer
      // 如果正在录音，则停止
      this.isRecording = false
      if (this.$refs.voiceCommon) {
        this.$refs.voiceCommon.stopRecord({
          changedTouches: [{ clientY: this.$refs.voiceCommon.touchStartY }],
        })
      }
    },

    // 处理输入框失焦
    handleInputBlur() {
      // 延迟设置失焦，避免点击语音按钮时就失去焦点
      // setTimeout(() => {
      //   this.focusedIndex = -1;
      // }, 200);
    },

    // 切换语音输入状态
    toggleVoiceInput() {
      if (this.focusedIndex === -1) {
        uni.showToast({
          title: '请先选择题目',
          icon: 'none',
        })
        return
      }
      if (this.isRecording) {
        // 如果正在录音，则停止
        this.isRecording = false
        if (this.$refs.voiceCommon) {
          this.$refs.voiceCommon.stopRecord({
            changedTouches: [{ clientY: this.$refs.voiceCommon.touchStartY }],
          })
        }
      } else {
        // 开始录音
        this.isRecording = true

        // 使用VoiceCommon组件的方法开始录音
        if (this.$refs.voiceCommon) {
          this.$refs.voiceCommon.startRecord({
            touches: [{ clientY: 0 }],
          })
        }
      }
    },

    // 处理语音识别结果
    handleVoiceResult(text) {
      console.log(text, 'handleVoiceResult', this.focusedIndex)
      if (this.focusedIndex >= 0) {
        const item =
          this.saveInfo.saveReplyInquiringDiagnosisTopics[this.focusedIndex]

        // 设置语音识别结果到输入框
        item.checkedAnswer = this.oldMsg + text
        this.$set(
          this.saveInfo.saveReplyInquiringDiagnosisTopics,
          this.focusedIndex,
          item
        )

        // 重置录音状态
        // this.isRecording = false;
      }
    },

    // 处理成功弹框的确认按钮
    async handleSuccessConfirm() {
      this.showSuccessDialog = false
      uni.hideLoading()
      uni.navigateBack({
        delta: 1, //想要返回的层级
      })
    },
    // 保存验证
    saveQuestionAnswerFun() {
      let saveInfo = this.saveInfo
      let saveReplyInquiringDiagnosisTopics =
        saveInfo.saveReplyInquiringDiagnosisTopics
      for (let i = 0; i < saveReplyInquiringDiagnosisTopics.length; i++) {
        let el = saveReplyInquiringDiagnosisTopics[i]
        let isMust = el.isMust
        if (isMust == 1) {
          console.log(el.checkedAnswer, saveReplyInquiringDiagnosisTopics)
          // 如果为必填
          if (!el.checkedAnswer) {
            Toast('第' + el.didtCode + '题为必选项，请选择')
            return
          }
        }
        if (el.didtType == '1' || el.didtType == '4') {
          // 单选 下拉
          for (let j = 0; j < el.answerInfo.length; j++) {
            if (el.answerInfo[j].optionCode == el.checkedAnswer) {
              el.answerInfo[j].checked = true
              el.value = el.answerInfo[j].optionName
            } else {
              el.answerInfo[j].checked = false
            }
          }
        } else if (el.didtType == '2') {
          // 多选
          for (let j = 0; j < el.answerInfo.length; j++) {
            if (
              el.checkedAnswer.indexOf(
                JSON.stringify(el.answerInfo[j].optionCode)
              ) != -1
            ) {
              el.answerInfo[j].checked = true
            } else {
              el.answerInfo[j].checked = false
            }
          }
          el.value = el.answerInfo
            .filter((item) => item.checked)
            .map((item) => item.optionName)
            .join(',')
        } else if (el.didtType == '3') {
          // 填空
          let reg = new RegExp(el.answerLimit)
          if (el.answerLimitName != '数字') {
            if (!reg.test(el.checkedAnswer) && el.checkedAnswer) {
              Toast('第' + el.didtCode + '题答案类型为' + el.answerLimitName)
              return
            }
          }
          el.answerInfo = el.checkedAnswer
          el.value = el.checkedAnswer
        } else {
          el.answerInfo = el.checkedAnswer
          el.value = el.checkedAnswer
        }
      }
      this.onImageUpLoad()
    },
    async onImageUpLoad() {
      uni.showLoading({
        title: '提交中',
        mask: true,
      })
      let data = this.saveInfo.saveReplyInquiringDiagnosisTopics
      let m = false
      for (let j = 0; j < data.length; j++) {
        let el = data[j]
        if (el.didtType == '5' && el.checkedAnswer) {
          await uploadImg({
            folderType: 15,
            imgBody: el.checkedAnswer.split(',')[1],
            otherId: this.pageParam.docId,
          }).then(async (res) => {
            el.answerInfo = res.data.url
            el.checkedAnswer = res.data.url
            const fileRes = await uni.uploadFile({
              url: '/api/v1/knowledge/upload',
              name: 'file',
              file: el.checkedAnswerFile,
            })
            console.log(fileRes, '===========fileRes')
            // let resUrl = JSON.parse(fileRes[1].data).data.file_path.split(
            //   '/tmp-dir/'
            // )
            // el.checkedAnswerUrl = resUrl[1].slice(0, resUrl[1].indexOf('?'))
            el.checkedAnswerUrl = JSON.parse(fileRes[1].data).data.file_path
            console.log(el.checkedAnswerUrl, '===========el.checkedAnswerUrl')
            this.$set(this.saveInfo.saveReplyInquiringDiagnosisTopics, j, el)
          })
        }
        if (j == data.length - 1) {
          this.confirmSaveQuestion()
        }
      }
    },
    // 保存量表提交方法
    async confirmSaveQuestion() {
      let saveInfo = JSON.parse(JSON.stringify(this.saveInfo))
      let saveReplyInquiringDiagnosisTopics =
        saveInfo.saveReplyInquiringDiagnosisTopics
      for (let i = 0; i < saveReplyInquiringDiagnosisTopics.length; i++) {
        let didtType = saveReplyInquiringDiagnosisTopics[i].didtType
        saveReplyInquiringDiagnosisTopics[i].checkedAnswer =
          saveReplyInquiringDiagnosisTopics[i].checkedAnswer.toString()
        if (didtType == '1' || didtType == '2' || didtType == '4') {
          saveReplyInquiringDiagnosisTopics[i].answerInfo = JSON.stringify(
            saveReplyInquiringDiagnosisTopics[i].answerInfo
          )
        }
      }
      saveInfo.feedbackId = this.feedbackId
      let para = saveInfo
      const map = {
        ch: '4',
        seven: '2',
        mrdk: '5',
      }
      para.fillingType = map[this.pageParam.lbType]
      para.projectId = this.pageParam.projectId
      para.docId = this.pageParam.docId
      try {
        await saveReplyInquiringDiagnosisAnswerCustom(para)
        console.log(this.pageParam.lbType, '===========para')

        // ch为肠道膳食量表 需要Ai工作流生成报告
        if (this.pageParam.lbType == 'ch') {
          await this.createReportByAiWorkFlow()
          this.showSuccessDialog = true
        } else if (this.pageParam.lbType == 'seven') {
          await this.createSevenReportByAiWorkFlow()
          this.showSuccessDialog = true
        } else if (this.pageParam.lbType == 'mrdk') {
          await this.createDailyReportByAiWorkFlow()
          this.showSuccessDialog = true
        } else {
          uni.hideLoading()
          uni.navigateBack({
            delta: 1, //想要返回的层级
          })
        }
      } catch (error) {
        uni.hideLoading()
      }
    },
    // 【AI工作流-购药后-肠道功能、简明膳食量表】生成报告
    async createReportByAiWorkFlow() {
      uni.showLoading({
        title: '生成报告中...',
        mask: true,
      })
      try {
        // 将聊天记录转换为字符串格式
        const value = ''

        // 第一步：发起工作流执行
        const { data } = await invokeWorkflow({
          workflow_id: '7a7b4832f9f840cf8a1319f2f7636b9b',
        })
        console.log(data, '===========workflowData')
        const workflowData = data.data
        if (workflowData && workflowData.session_id) {
          this.workflowSessionId = workflowData.session_id
          this.isWorkflowRunning = true
          // 处理工作流返回的事件
          if (workflowData.events && workflowData.events.length > 0) {
            for (const event of workflowData.events) {
              if (event.event === 'guide_word' && event.output_schema) {
                // 显示工作流的引导语
              }
              if (event.event === 'input') {
                this.message_id = event.message_id
                this.input_schema = event.input_schema
                this.lastNodeId = event.node_id
              }
            }
          }
          console.log(this.input_schema, '===========input_schema')
          const values = this.saveInfo.saveReplyInquiringDiagnosisTopics
          uni.showLoading({
            title: '生成报告中...',
            mask: true,
          })
          const res = await gutFunctionLbLbGenerate({
            input: {
              [this.lastNodeId]: {
                name: this.pageParam.patientName,
                // sex: this.pageParam.sex,
                // age_num: this.pageParam.age,
                date: values[0].value,
                abdominal_cavity: values[1].value,
                abdominal_cavity: values[2].value,
                Gastrectomy: values[3].value,
                Abdominal_Radiation_Therapy: values[4].value,
                Medication_intake: values[5].value,
                abdomen: values[6].value,
                Intestinal_bleeding: values[7].value,
                age: values[8].value,
                other: values[9].value,
                eat: values[10].value,
                weight: values[11].value,
              },
            },
            message_id: this.message_id,
            session_id: this.workflowSessionId,
            workflow_id: '7a7b4832f9f840cf8a1319f2f7636b9b',
            stream: false,
            szAiReportVo: {
              reportTittle: '简明膳食&营养评估报告',
              patientName: this.pageParam.patientName,
              reportType: '5',
              userId: uni.getStorageSync('userId'),
              docId: this.pageParam.docId,
              patientId: this.pageParam.patientId,
              ifNeedDocAudit: 0,
              projectId: this.pageParam.projectId,
              qcStatus: 6
            },
            sendChatGroupsDTO: {
              from: this.pageParam.aiDietitianId,
              to: [this.pageParam.groupId || ''],
              type: 'txt',
              body: {
                msg: '简明膳食&营养评估报告',
              },
              ext: {
                patientId: this.pageParam.patientId,
                type: 'report',
                // reportId: res.data.pkId,
                reportType: 5,
                title: '简明膳食&营养评估报告',
              },
            },
          })
          // // 第四步：再次调用工作流，传入完整的聊天记录
          // const { data: summaryData } = await invokeWorkflow({
          //   sessionId: this.workflowSessionId,
          //   input: {
          //     [this.lastNodeId]: {
          //       name: this.pageParam.patientName,
          //       // sex: this.pageParam.sex,
          //       // age_num: this.pageParam.age,
          //       date: values[0].value,
          //       abdominal_cavity: values[1].value,
          //       abdominal_cavity: values[2].value,
          //       Gastrectomy: values[3].value,
          //       Abdominal_Radiation_Therapy: values[4].value,
          //       Medication_intake: values[5].value,
          //       abdomen: values[6].value,
          //       Intestinal_bleeding: values[7].value,
          //       age: values[8].value,
          //       other: values[9].value,
          //       eat: values[10].value,
          //       weight: values[11].value,
          //     },
          //   },
          //   message_id: this.message_id,
          // })
          // // 处理返回的总结内容
          // if (summaryData.data && summaryData.data.events) {
          //   for (const event of summaryData.data.events) {
          //     if (event.output_schema && event.output_schema.message) {
          //       // this.receiveAiMessage(event.output_schema.message);
          //     }
          //     if (event.event === 'stream_msg' && event.status === 'end') {
          //       // 生成的营养评估表报告
          //       console.log(
          //         event.output_schema.message,
          //         '===========event.output_schema.message'
          //       )
          //       // 保存营养评估表报告
          //       const res = await saveOriginal({
          //         reportType: '6',
          //         userId: uni.getStorageSync('userId'),
          //         docId: this.pageParam.docId,
          //         patientId: this.pageParam.patientId,
          //         reportContent: event.output_schema.message,
          //         ifNeedDocAudit: 0,
          //         projectId: this.pageParam.projectId,
          //       })
          //       // 发送报告
          //       try {
          //         const messageData = {
          //           from: this.pageParam.aiDietitianId,
          //           to: [this.pageParam.groupId || ''],
          //           type: 'txt',
          //           body: {
          //             msg: '您好，您的肠道功能、简明膳食量表报告已生成',
          //           },
          //           ext: {
          //             patientId: this.pageParam.patientId,
          //             type: 'report',
          //             reportId: res.data.pkId,
          //             reportType: 5,
          //             title: '您好，您的肠道功能、简明膳食量表报告已生成',
          //           },
          //         }
          //         await sendGroupMessage(messageData)
          //         console.log('肠道功能、简明膳食量表报告发送成功')
          //       } catch (error) {
          //         console.error('肠道功能、简明膳食量表报告发送失败:', error)
          //       }
          //     }
          //   }
          // }
          // // 第五步：停止工作流
          // await stopWorkflow({
          //   sessionId: this.workflowSessionId,
          // })
          // uni.navigateBack({
          //   delta: 1, //想要返回的层级
          // })
          uni.hideLoading()
        }
      } catch (error) {
        console.error('营养评估表报告生成异常:', error)
        uni.hideLoading()
      } finally {
      }
    },
    // 7天报告
    async createSevenReportByAiWorkFlow() {
      uni.showLoading({
        title: '生成报告中...',
        mask: true,
      })
      try {
        // 将聊天记录转换为字符串格式
        const value = ''

        // 第一步：发起工作流执行
        const { data } = await invokeWorkflow({
          workflow_id: 'e44fb7703ca044fea6b06ae7b788ea3d',
        })
        console.log(data, '===========workflowData')
        const workflowData = data.data
        if (workflowData && workflowData.session_id) {
          this.workflowSessionId = workflowData.session_id
          this.isWorkflowRunning = true
          // 处理工作流返回的事件
          if (workflowData.events && workflowData.events.length > 0) {
            for (const event of workflowData.events) {
              if (event.event === 'guide_word' && event.output_schema) {
                // 显示工作流的引导语
              }
              if (event.event === 'input') {
                this.message_id = event.message_id
                this.input_schema = event.input_schema
                this.lastNodeId = event.node_id
              }
            }
          }
          console.log(this.input_schema, '===========input_schema')
          uni.showLoading({
            title: '生成报告中...',
            mask: true,
          })
          const values = this.saveInfo.saveReplyInquiringDiagnosisTopics
          const res = await weeklyCycleGenerate({
            input: {
              [this.lastNodeId]: {
                name: this.pageParam.patientName,
                sex: this.pageParam.sex,
                age: this.pageParam.age,
                height: values[0].value,
                weight: values[1].value,
                month_ago_weight: values[2].value,
                dietary_situation_1month: values[3].value,
                Current_eating_status: values[4].value,
                Reasons_affecting_eating: values[5].value,
              },
            },
            message_id: this.message_id,
            session_id: this.workflowSessionId,
            workflow_id: 'e44fb7703ca044fea6b06ae7b788ea3d',
            stream: false,
            szAiReportVo: {
              reportTittle: '每周营养评估报告',
              patientName: this.pageParam.patientName,
              reportType: '5',
              userId: uni.getStorageSync('userId'),
              docId: this.pageParam.docId,
              patientId: this.pageParam.patientId,
              ifNeedDocAudit: 1,
              projectId: this.pageParam.projectId,
            },
          })
          // 第四步：再次调用工作流，传入完整的聊天记录
          // const { data: summaryData } = await invokeWorkflow({
          //   sessionId: this.workflowSessionId,
          //   input: {
          //     [this.lastNodeId]: {
          //       name: this.pageParam.patientName,
          //       sex: this.pageParam.sex,
          //       age: this.pageParam.age,
          //       height: values[0].value,
          //       weight: values[1].value,
          //       month_ago_weight: values[2].value,
          //       dietary_situation_1month: values[3].value,
          //       Current_eating_status: values[4].value,
          //       Reasons_affecting_eating: values[5].value,
          //     },
          //   },
          //   message_id: this.message_id,
          // })

          // // 处理返回的总结内容
          // if (summaryData.data && summaryData.data.events) {
          //   for (const event of summaryData.data.events) {
          //     if (event.output_schema && event.output_schema.message) {
          //       // this.receiveAiMessage(event.output_schema.message);
          //     }
          //     if (event.event === 'stream_msg' && event.status === 'end') {
          //       // 生成的营养评估表报告
          //       console.log(
          //         event.output_schema.message,
          //         '===========event.output_schema.message'
          //       )
          //       // 保存营养评估表报告
          //       const res = await saveOriginal({
          //         reportType: '5',
          //         userId: uni.getStorageSync('userId'),
          //         docId: this.pageParam.docId,
          //         patientId: this.pageParam.patientId,
          //         reportContent: event.output_schema.message,
          //         ifNeedDocAudit: 1,
          //         projectId: this.pageParam.projectId,
          //         reportTittle: '每周营养评估报告',
          //         patientName: this.pageParam.patientName,
          //       })

          //       // 根据医生ID查询营养师ID 是否有关联
          //       const doctorInfo = await getDoctorInfoByDocId({
          //         docId: this.pageParam.docId,
          //       })
          //       console.log(doctorInfo, '===========doctorInfo')
          //       // 保存营养评估表报告
          //       await insertRecord({
          //         wxTitle:
          //           '患者' +
          //           this.pageParam.patientName +
          //           `的营养评估表-七天循环量表报告已生成，${doctorInfo.data.dietitianId &&
          //             doctorInfo.data.dietitianId != ''
          //             ? '请校准'
          //             : '请确认'
          //           }`,
          //         receiverId:
          //           (doctorInfo.data.dietitianId &&
          //             doctorInfo.data.dietitianId != '')
          //             ? doctorInfo.data.dietitianId
          //             : this.pageParam.docId,
          //         ext: JSON.stringify({
          //           reportId: res.data.pkId,
          //           type: 'report',
          //           reportType: 5,
          //           title: '您好，您的营养评估表-七天循环量表报告已生成',
          //         }),
          //         hxMessage: res.data.pkId,
          //       })
          //     }
          //   }
          // }
          // // 第五步：停止工作流
          // await stopWorkflow({
          //   sessionId: this.workflowSessionId,
          // })
          // uni.navigateBack({
          //   delta: 1, //想要返回的层级
          // })
          uni.hideLoading()
        }
      } catch (error) {
        console.error('营养评估表报告生成异常:', error)
        uni.hideLoading()
      } finally {
      }
    },
    // 每日打卡
    async createDailyReportByAiWorkFlow() {
      uni.showLoading({
        title: '生成报告中...',
        mask: true,
      })
      try {
        // 将聊天记录转换为字符串格式
        const value = ''

        // 第一步：发起工作流执行
        const { data } = await invokeWorkflow({
          workflow_id: 'b237f63f0f404149b8f1242764d7ceaa',
        })
        console.log(data, '===========workflowData')
        const workflowData = data.data
        if (workflowData && workflowData.session_id) {
          this.workflowSessionId = workflowData.session_id
          this.isWorkflowRunning = true
          // 处理工作流返回的事件
          if (workflowData.events && workflowData.events.length > 0) {
            for (const event of workflowData.events) {
              if (event.event === 'guide_word' && event.output_schema) {
                // 显示工作流的引导语
              }
              if (event.event === 'input') {
                this.message_id = event.message_id
                this.input_schema = event.input_schema
                this.lastNodeId = event.node_id
              }
            }
          }
          console.log(this.input_schema, '===========input_schema')
          uni.showLoading({
            title: '生成报告中...',
            mask: true,
          })
          const values = this.saveInfo.saveReplyInquiringDiagnosisTopics
          const res = await dailyReminderLbGenerate({
            input: {
              [this.lastNodeId]: {
                weight: values[0].checkedAnswer,
                deily: values[1].checkedAnswer,
                last_week_weight: values[2].checkedAnswer,
                morning: values[3].checkedAnswerUrl
                  ? [values[3].checkedAnswerUrl]
                  : [],
                noon: values[4].checkedAnswerUrl
                  ? [values[4].checkedAnswerUrl]
                  : [],
                afternoon: values[5].checkedAnswerUrl
                  ? [values[5].checkedAnswerUrl]
                  : [],
              },
            },
            message_id: this.message_id,
            session_id: this.workflowSessionId,
            workflow_id: 'b237f63f0f404149b8f1242764d7ceaa',
            stream: false,
            szAiReportVo: {
              reportTittle: '每日打卡营养评估报告',
              patientName: this.pageParam.patientName,
              reportType: '5',
              userId: uni.getStorageSync('userId'),
              docId: this.pageParam.docId,
              patientId: this.pageParam.patientId,
              ifNeedDocAudit: 1,
              projectId: this.pageParam.projectId,
            },
          })
          console.log(values, '===========values')
          // let user_inputs = ''
          // values.map((item) => {
          //   user_inputs += `${item.didtName}:${
          //     item.didtType == '5' ? item.checkedAnswerUrl : item.value
          //   },`
          // })
          // console.log(user_inputs, '===========user_inputs')
          // 第四步：再次调用工作流，传入完整的聊天记录
          // const { data: summaryData } = await invokeWorkflow({
          //   sessionId: this.workflowSessionId,
          //   input: {
          //     [this.lastNodeId]: {
          //       weight: values[0].checkedAnswer,
          //       deily: values[1].checkedAnswer,
          //       last_week_weight: values[2].checkedAnswer,
          //       morning: values[3].checkedAnswerUrl
          //         ? [values[3].checkedAnswerUrl]
          //         : [],
          //       noon: values[4].checkedAnswerUrl
          //         ? [values[4].checkedAnswerUrl]
          //         : [],
          //       afternoon: values[5].checkedAnswerUrl
          //         ? [values[5].checkedAnswerUrl]
          //         : [],
          //     },
          //   },
          //   message_id: this.message_id,
          // })
          // console.log(summaryData, '===========summaryData')

          // // 处理返回的总结内容
          // if (summaryData.data && summaryData.data.events) {
          //   for (const event of summaryData.data.events) {
          //     if (event.output_schema && event.output_schema.message) {
          //       // this.receiveAiMessage(event.output_schema.message);
          //     }
          //     if (event.event === 'stream_msg' && event.status === 'end') {
          //       // 生成的营养评估表报告
          //       console.log(
          //         event.output_schema.message,
          //         '===========event.output_schema.message'
          //       )
          //       // 保存个性化营养方案报告
          //       const res = await saveOriginal({
          //         reportType: '5',
          //         userId: uni.getStorageSync('userId'),
          //         docId: this.pageParam.docId,
          //         patientId: this.pageParam.patientId,
          //         reportContent: event.output_schema.message,
          //         ifNeedDocAudit: 1,
          //         projectId: this.pageParam.projectId,
          //         patientName: this.pageParam.patientName,
          //         reportTittle: '每日打卡报告',
          //       })
          //       // 根据医生ID查询营养师ID 是否有关联
          //       const doctorInfo = await getDoctorInfoByDocId({
          //         docId: this.pageParam.docId,
          //       })
          //       console.log(doctorInfo, '===========doctorInfo')
          //       // 保存营养评估表报告
          //       await insertRecord({
          //         wxTitle:
          //           '患者' +
          //           this.pageParam.patientName +
          //           `的个性化营养方案已生成，${doctorInfo.data.dietitianId &&
          //             doctorInfo.data.dietitianId != ''
          //             ? '请校准'
          //             : '请确认'
          //           }`,
          //         receiverId:
          //           doctorInfo.data.dietitianId &&
          //             doctorInfo.data.dietitianId != ''
          //             ? doctorInfo.data.dietitianId
          //             : this.pageParam.docId,
          //         ext: JSON.stringify({
          //           reportId: res.data.pkId,
          //           type: 'report',
          //           reportType: 5,
          //           title: '您好，您的个性化营养方案 已生成',
          //         }),
          //         hxMessage: res.data.pkId,
          //       })
          //     }
          //   }
          // }
          // // 第五步：停止工作流
          // await stopWorkflow({
          //   sessionId: this.workflowSessionId,
          // })
          // uni.navigateBack({
          //   delta: 1, //想要返回的层级
          // })
          uni.hideLoading()
        }
      } catch (error) {
        console.error('营养评估表报告生成异常:', error)
        uni.hideLoading()
      } finally {
      }
    },
    // 获取反馈id
    async setQuestionReadFun() {
      let res = await setQuestionRead({
        patientId: this.pageParam.patientId,
        sendId: this.pageParam.sendId,
      })
      let feedbackId = res.message
      this.feedbackId = feedbackId
    },
    // 单选选择赋值
    radioChange(evt, item, index) {
      item.checkedAnswer = parseInt(evt.detail.value)
      this.$set(this.saveInfo.saveReplyInquiringDiagnosisTopics, index, item)
    },
    // 多选选择赋值
    checkboxChange(evt, item, index) {
      item.checkedAnswer = evt.detail.value
      this.$set(this.saveInfo.saveReplyInquiringDiagnosisTopics, index, item)
    },
    // 下拉选择
    bindPickerChange(evt, item, index) {
      this.pickerIndex = evt.detail.value
      item.pickerIndex = evt.detail.value
      item.checkedAnswer = item.answerInfo[evt.detail.value].optionCode
      this.$set(this.saveInfo.saveReplyInquiringDiagnosisTopics, index, item)
    },
    // 评分
    rateChange(evt, item, index) {
      item.checkedAnswer = evt.value
      this.$set(this.saveInfo.saveReplyInquiringDiagnosisTopics, index, item)
    },
    // 时间选择
    bindTimeChange(evt, item, index) {
      item.checkedAnswer = evt.detail.value
      this.$set(this.saveInfo.saveReplyInquiringDiagnosisTopics, index, item)
    },
    // 日期选择
    bindDateChange(evt, item, index) {
      console.log(evt.detail.value, 'evt.detail.value')
      if (this.saveInfo.didOnlyId === 'd9b67e46b2d04d51804d115c21ba2baa') {
        // 不能选未来时间
        if (new Date(evt.detail.value) > new Date()) {
          uni.showToast({
            title: '不能选未来时间',
            icon: 'none',
          })
          return
        }
      }
      item.checkedAnswer = evt.detail.value
      this.$set(this.saveInfo.saveReplyInquiringDiagnosisTopics, index, item)
    },
    // 图片选择
    selectImg(item, index) {
      let _this = this
      uni.chooseImage({
        count: 1, //默认9
        sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
        success: function (res) {
          for (let i = 0; i < res.tempFiles.length; i++) {
            let el = res.tempFiles[i]
            console.log(el, '===========el')
            item.checkedAnswerFile = el // 文件对象
            myJsTools.setImgZip(el, (dataUrl) => {
              item.checkedAnswer = dataUrl
              _this.$set(
                _this.saveInfo.saveReplyInquiringDiagnosisTopics,
                index,
                item
              )
            })
          }
        },
      })
    },
    // 获取量表题目
    async getQuestionDetailEmptyFun() {
      let sendId = this.pageParam.sendId
      let res = await getQuestionDetailEmpty({ sendId })
      let info = res.data
      let saveInfo = {
        didId: info.didId,
        didName: info.didName,
        didOnlyId: info.didOnlyId,
        docId: info.docId,
        docName: info.docName,
        patientId: this.pageParam.patientId,
        saveReplyInquiringDiagnosisTopics: [],
      }
      let replyInquiringDiagnosisTopics = []
      for (let i = 0; i < info.docQuestionListVO.length; i++) {
        let obj = info.docQuestionListVO[i]
        let seveInfoList = {}
        seveInfoList.answerInfo = JSON.parse(obj.optionInfo) || ''
        seveInfoList.checkedAnswer = ''
        seveInfoList.didtCode = obj.didtCode
        seveInfoList.didtId = obj.didtId
        seveInfoList.didtName = obj.didtName
        seveInfoList.didtType = obj.didtType
        seveInfoList.answerLimit = obj.answerLimit
        seveInfoList.answerLimitName = obj.answerLimitName
        seveInfoList.isMust = obj.isMust
        replyInquiringDiagnosisTopics.push(seveInfoList)
      }
      saveInfo.saveReplyInquiringDiagnosisTopics = replyInquiringDiagnosisTopics
      this.saveInfo = Object.assign({}, saveInfo)

      if (this.saveInfo.didOnlyId === 'd9b67e46b2d04d51804d115c21ba2baa') {
        // 默认填充为【填写量表当天】格式 YYYY-MM-DD，置灰，用户不可修改
        let date = new Date().toLocaleDateString()
        this.saveInfo.saveReplyInquiringDiagnosisTopics[0].checkedAnswer = date
          .split('/')
          .join('-')
        this.saveInfo.saveReplyInquiringDiagnosisTopics[0].disabled = true
      }
      // 修改导航栏的标题saveInfo.didName
      uni.setNavigationBarTitle({
        title: saveInfo.didName,
      })
    },
    // 切换简明膳食地区
    changeRegion(region) {
      this.selectedRegion = region
    },
  },
}
</script>

<style lang="scss" scoped>
.header {
  background: #ffffff;
  margin: 24rpx 32rpx 0px;
  font-size: 28rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 0 20rpx rgba(198, 204, 206, 0.2);
  border-radius: 16rpx;
  .title {
    color: #333333;
    line-height: 44rpx;
  }
  .tips {
    color: #999999;
    line-height: 36rpx;
    margin-top: 10rpx;
  }
}

// 评分
::v-deep .rate-media-cell {
  @include flex;
}

::v-deep .uni-radio-input.uni-radio-input-checked:before {
  content: '';
  width: 20upx;
  height: 20upx;
  border-radius: 10upx;
  display: inline-block;
  @include bg_theme;
}

::v-deep .uni-radio-input-checked {
  background: none !important;
}

.quesView {
  padding: 0 32rpx 120rpx;
  .quesAnswerView {
    margin-top: 24rpx;
    background: #ffffff;
    font-size: 28rpx;
    padding: 32rpx 24rpx;
    box-shadow: 0 0 20rpx rgba(198, 204, 206, 0.2);
    border-radius: 16rpx;
    .didName {
      color: #333333;
      font-weight: 600;
      line-height: 44rpx;
    }
    text {
      color: #666666;
      font-size: 26rpx;
      line-height: 40rpx;
    }
    ::v-deepuni-radio,
    uni-checkbox {
      margin-top: 16px;
    }

    ::v-deepuni-checkbox .uni-checkbox-input.uni-checkbox-input-checked:before {
      font-size: 23px;
      font-weight: 800;
    }
  }
  .contentView {
    ::v-deep.uni-textarea-textarea {
      font-size: 26rpx;
      line-height: 36rpx;
    }
    .content {
      border: 1px solid #d5d5d5;
      margin-top: 24rpx;
      padding: 24rpx;

      textarea {
        width: 100%;
        height: 124upx;
      }
    }
  }
  .dateView,
  .timeView,
  .selectView,
  .imgView {
    .dateInput {
      border: 1px solid #d5d5d5;
      margin-top: 24rpx;
      color: $k-title;
      padding: 20rpx 24rpx;
      position: relative;
      font-size: 28rpx;
    }
    .downIcon {
      width: 32rpx;
      height: 32rpx;
      position: absolute;
      right: 24rpx;
    }
    .dateInputPlac {
      color: $k-info-title;
    }
  }
  .imgView {
    .dateInput {
      @include flex(left);
      text {
        color: $k-info-title;
      }
    }
    .imgIcon {
      width: 32rpx;
      height: 34rpx;
      margin-right: 12upx;
    }
    .editIcon {
      width: 32rpx;
      height: 32rpx;
      position: absolute;
      right: 24rpx;
    }
  }
}
.commit {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 108upx;
  padding: 0 32upx;
  @include flex;
  border-radius: 16upx 16upx 0 0;
  background-color: #fff;
  box-sizing: border-box;

  button {
    width: 100%;
    height: 84upx;
    border-radius: 42upx;
    @include flex;
    font-size: 32upx;
    color: #fff;
    @include bg_theme;
  }
}

.imgView {
  position: relative;
  .upImg {
    margin-top: 24rpx;
    width: 80%;
  }
  .delIcon {
    width: 32rpx;
    height: 32rpx;
    position: absolute;
    right: 60rpx;
    top: 80rpx;
  }
}

/* 提交成功弹窗样式 */
.success-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.success-dialog-container {
  width: 80%;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  padding: 30rpx;
}

.success-dialog-content {
  text-align: left;
}

.success-dialog-message {
  color: #666;
  font-size: 30rpx;
  line-height: 1.5;
  margin: 25rpx 0;
  padding: 0 10rpx;
}

.success-dialog-footer {
  margin-top: 30rpx;
  text-align: center;
}

.success-dialog-btn {
  width: 50%;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  background-color: #6e61fd;
  color: #fff;
  font-size: 30rpx;
  text-align: center;
}

/* 固定在底部的语音输入按钮 */
.voice-input-fixed {
  position: fixed;
  bottom: 130rpx; // 位于提交按钮上方
  right: 40rpx;
  z-index: 999;

  .voice-input-btn {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border-radius: 50%;
    padding: 15rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
    border: 1px solid #6e61fd;

    &.active {
      width: auto;
      padding: 15rpx 50rpx 15rpx 25rpx;
      border-radius: 40rpx;
    }

    .mic-icon {
      width: 40rpx;
      height: 40rpx;
    }

    .recording-text {
      font-size: 26rpx;
      color: #333;
      margin-left: 10rpx;
    }
  }
}

/* 地区选择tabs样式 */
.diet-region-tabs {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}

.diet-tabs-container {
  display: flex;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.diet-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.diet-tab.active {
  color: #6e61fd;
  font-weight: bold;
}

.diet-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 25%;
  width: 50%;
  height: 4rpx;
  background-color: #6e61fd;
}

.diet-image-container {
  margin: 20rpx 0;
  border: 1px solid #eee;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #fff;
}

.diet-image {
  width: 100%;
  display: block;
}
</style>
