"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const TITLE = () => "../../inspect/com/itemTitle.js";
const _sfc_main = {
  components: {
    TITLE
  },
  props: {
    speImg: ""
  },
  data() {
    return {};
  },
  methods: {
    goSpe() {
      common_vendor.index.navigateTo({ url: "/pages/index/com/specialtySpecialtyList" });
    }
  }
};
if (!Array) {
  const _component_TITLE = common_vendor.resolveComponent("TITLE");
  _component_TITLE();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.p({
      title: "特色专科"
    }),
    b: common_assets._imports_0$4,
    c: common_vendor.o((...args) => $options.goSpe && $options.goSpe(...args))
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-fd11c83e"]]);
wx.createComponent(Component);
