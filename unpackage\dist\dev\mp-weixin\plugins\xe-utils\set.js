"use strict";
const plugins_xeUtils_staticParseInt = require("./staticParseInt.js");
const plugins_xeUtils_helperGetHGSKeys = require("./helperGetHGSKeys.js");
const plugins_xeUtils_hasOwnProp = require("./hasOwnProp.js");
var sKeyRE = /(.+)?\[(\d+)\]$/;
function setDeepProps(obj, key, isEnd, nextKey, value) {
  if (obj[key]) {
    if (isEnd) {
      obj[key] = value;
    }
  } else {
    var index;
    var rest;
    var currMatchs = key ? key.match(sKeyRE) : null;
    if (isEnd) {
      rest = value;
    } else {
      var nextMatchs = nextKey ? nextKey.match(sKeyRE) : null;
      if (nextMatchs && !nextMatchs[1]) {
        rest = new Array(plugins_xeUtils_staticParseInt.staticParseInt(nextMatchs[2]) + 1);
      } else {
        rest = {};
      }
    }
    if (currMatchs) {
      if (currMatchs[1]) {
        index = plugins_xeUtils_staticParseInt.staticParseInt(currMatchs[2]);
        if (obj[currMatchs[1]]) {
          if (isEnd) {
            obj[currMatchs[1]][index] = rest;
          } else {
            if (obj[currMatchs[1]][index]) {
              rest = obj[currMatchs[1]][index];
            } else {
              obj[currMatchs[1]][index] = rest;
            }
          }
        } else {
          obj[currMatchs[1]] = new Array(index + 1);
          obj[currMatchs[1]][index] = rest;
        }
      } else {
        obj[currMatchs[2]] = rest;
      }
    } else {
      obj[key] = rest;
    }
    return rest;
  }
  return obj[key];
}
function set(obj, property, value) {
  if (obj) {
    if ((obj[property] || plugins_xeUtils_hasOwnProp.hasOwnProp(obj, property)) && !isPrototypePolluted(property)) {
      obj[property] = value;
    } else {
      var rest = obj;
      var props = plugins_xeUtils_helperGetHGSKeys.helperGetHGSKeys(property);
      var len = props.length;
      for (var index = 0; index < len; index++) {
        if (isPrototypePolluted(props[index])) {
          continue;
        }
        var isEnd = index === len - 1;
        rest = setDeepProps(rest, props[index], isEnd, isEnd ? null : props[index + 1], value);
      }
    }
  }
  return obj;
}
function isPrototypePolluted(key) {
  return key === "__proto__" || key === "constructor" || key === "prototype";
}
exports.set = set;
