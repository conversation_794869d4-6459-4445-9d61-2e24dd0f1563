<template>
  <!-- 服务说明 -->
  <view class="page-container">
    <!-- 头部 -->
    <Docter :infoDetail="infoDetail" />

    <!-- 主体内容 -->
    <view class="bg_wh">
      <!-- 问诊信息 -->
      <view class="patient_box">
        <view class="person_info">
          <text>就诊人</text>
          <text>{{ info.patientName }}</text>
        </view>
        <view class="person_info">
          <text>问诊类型</text>
          <text>{{ infoDetail.visitTypeName }}</text>
        </view>
        <view class="person_info">
          <text>服务类型</text>
          <text>{{ infoDetail.type == 0 ? "咨询" : "复诊" }}</text>
        </view>
      </view>
      <!-- 图片 -->
      <view class="img_box">
        <image src="/static/images/index/explain1.png"></image>
        <image src="/static/images/index/explain4.png"></image>
        <image
          v-if="infoDetail.type == 1"
          src="/static/images/index/explain2.png"
        ></image>
        <image
          v-if="infoDetail.type == 1"
          src="/static/images/index/explain3.png"
        ></image>
      </view>
      <!-- 问诊说明 -->
      <view class="content_font">
        <view v-if="infoDetail.type == 1">
          您选择的是复诊服务，请确定您曾与此医生以面诊方式首诊或在下一步上传过往病例照片。医生会根据您的病例信息与所上传资料确定您是否符合在线复诊条件。若医生不接诊，系统将为您退款。
        </view>
        <view v-else style="color: #ff0000">
          您选择的是咨询服务，只能进行健康、就医或用药方面咨询与建议，医生不能为您开具处方。
        </view>
        <view style="margin-top: 30px">
          医生将在交费后{{
            time
          }}小时内接诊，医生接诊后您可以与医生无限次数沟通{{
            timetwo
          }}小时。图文方式可以进行文字、图片、短语音、短视频沟通。由于医生工作特殊性，接诊可能不及时，请您耐心等待，如医生{{
            hour
          }}小时未回复，系统将为您退款
        </view>

        <!-- 浮动 -->
        <view class="fiexd">
          <!-- 协议 -->
          <view class="agreeInfo">
            <checkbox
              value="1"
              @click="onChange"
              :checked="checked"
              color="#fff"
              style="transform: scale(0.8)"
            />
            同意<text class="blue_click" @click="openProtocol(8)"
              >《互联网诊疗风险告知及知情同意书 》</text
            >
          </view>

          <!-- 确认首诊 -->
          <view class="has_frist" v-if="infoDetail.type == 1">
            <checkbox
              value="1"
              name="frist"
              @click="isHasFrist = !isHasFrist"
              :checked="isHasFrist"
              color="#fff"
              style="transform: scale(0.8)"
            />
            <text @click="isHasFrist = !isHasFrist"
              >我确认已在线下首诊并有就诊病历</text
            >
          </view>
        </view>
      </view>
    </view>

    <PROPCENTER
      v-if="ProtocolShow"
      :buttontype="buttontype"
      :buttonCount="count"
      buttonText="阅读并同意 "
      @confirmPropCenter="confirmPropCenter"
    >
      <view class="across_provinces">
        <view class="title">互联网诊疗风险告知及知情同意书</view>
        <!-- 文案 -->
        <view class="info" v-html="content"> </view>
      </view>
    </PROPCENTER>
    <!-- 按钮 -->
    <FooterButton @click="getNext"> 下一步 </FooterButton>
    <modelToast v-if="priceToast" :father="this" :type="1">
      医生设置了您的问诊价格为{{ personalPrice }}元,请您知晓
    </modelToast>
  </view>
</template>

<script>
import PROPCENTER from "@/components/propCenter/propCenter.vue";
import { Toast } from "@/common/js/pay.js";
import { getDocPatientBlackList } from "@/api/register";
import Docter from "@/components/doctor_header/doctor_header.vue";
import FooterButton from "@/components/footer_button/button.vue";
import {
  findVisitAgreement,
  getPersonalityPrice,
  addProsignagreement,
  savePatientRecords,
  getSysPlatformConfigByKeyList,
} from "@/api/base.js";
export default {
  components: {
    Docter,
    FooterButton,
    PROPCENTER,
  },
  data() {
    return {
      infoDetail: {},
      info: {},
      time: "",
      timetwo: "",
      hour: "",
      // 是否有过首诊
      isHasFrist: false,
      priceToast: false,
      personalPrice: "",
      checked: true,
      isRequired: false,
      content: "",
      count: 5, //阅读倒计时  与const seconds = 5 保持一致否则第一次加载出现null
      timer: null, //时间戳
      buttontype: true, //PROPCENTER组件设置按钮禁用样式
      ProtocolShow: true, //窗口显示隐藏切换
    };
  },
  onLoad() {
    this.info = uni.getStorageSync("patientInfo");
    this.infoDetail = uni.getStorageSync("infoDetail");
    console.log(uni.getStorageSync("infoDetail"));
    // 咨询
    if (this.infoDetail.type == 0) {
      this.isHasFrist = true;
    }
    let wholeArg = uni.getStorageSync("wholeArg");
    wholeArg.map((item) => {
      if (item.configKey == "js_no_receive_duration") {
        this.time = item.configValue;
      }
      if (item.configKey == "inquiry_duration") {
        this.timetwo = item.configValue;
      }
      if (item.configKey == "ss_no_receive_duration") {
        this.hour = item.configValue;
      }
    });

    // 如果开启自定义价格 则调用针对患者的自定义价格
    if (this.infoDetail.isSwitch != 0) {
      this.getPersonalityPriceFun();
    }
    this.modalProtocol();
  },
  methods: {
    //查看协议
    openProtocol(type) {
      uni.navigateTo({
        url: "/pages/protocol/index?type=" + type,
      });
    },

    //弹窗协议
    async modalProtocol() {
      let { data } = await findVisitAgreement({
        agreementType: "8",
      });
      this.content = data.agreementContent;
      // 按钮读秒定时器
      const seconds = 5;
      if (!this.timer) {
        this.count = seconds;
        this.timer = setInterval(() => {
          if (this.count > 1 && this.count <= seconds) {
            this.count--;
          } else {
            this.buttontype = false;
            clearInterval(this.timer);
            this.timer = null;
          }
        }, 1000);
      }
    },
    confirmPropCenter() {
      if (this.buttontype) return;
      this.ProtocolShow = false;
    },
    // 获取自定义个性价格
    async getPersonalityPriceFun() {
      let { type, docId, visitTypeCode } = this.infoDetail;
      let para = {
        consultationCode: type,
        docId,
        patientId: uni.getStorageSync("patientId"),
        visitTypeCode,
      };
      let res = await getPersonalityPrice(para);
      let infoDetail = this.infoDetail;
      if (!res.data) return;
      let { personalityPrice: personalPrice } = res.data;
      if (personalPrice > -1) {
        let price = infoDetail.visitPrice;
        // 如果医生正常价格 跟现有价格不一样
        if (personalPrice != price) {
          infoDetail.visitPrice = personalPrice;
          this.infoDetail = infoDetail;
          this.personalPrice = personalPrice;
          // 提示自定义价格
          this.priceToast = true;
          uni.setStorageSync("infoDetail", infoDetail);
        }
      }
    },
    // 点击自定义个性价格弹框按钮
    modeConfirm() {
      this.priceToast = false;
    },
    // 监听复选框的选中
    onChange(e) {
      this.checked = !this.checked;
    },
    // 下一步
    async getNext() {
      if (!this.checked) {
        Toast("请勾选协议");
        return;
      }
      if (!this.isHasFrist) {
        Toast("请确认是否已在线下首诊");
        return;
      }
      uni.showLoading({
        mask: true,
      });
      try {
        const { patientId } = this.info;
        const { docId } = this.infoDetail;
        await getDocPatientBlackList({
          patientId,
          docId,
        });

        let res = await findVisitAgreement({
          agreementType: "1",
        });

        let { agreementId, agreementName, agreementVersions } = res.data;

        let list = {
          agreementId,
          agreementNama: agreementName,
          agreementVersions,
          patientId: uni.getStorageSync("patientId"),
        };

        let result = await addProsignagreement(list);

        let infoDetail = uni.getStorageSync("infoDetail");

        infoDetail.psaId = result.data.uuid;

        uni.setStorageSync("infoDetail", infoDetail);

        await this.getConfig();

        // 获取prid
        let prId = await this.getPrid();

        uni.hideLoading();

        // 跳转需要根据配置决定是否先填写病情描述
        if (this.isRequired) {
          uni.redirectTo({
            url:
              "/pages/register/thatDayRegister/diseaseDetail/index?prId=" +
              prId,
          });
        } else {
          uni.redirectTo({
            url:
              "/pages/register/thatDayRegister/orderDetail/index?isShowDisease=1&prId=" +
              prId,
          });
        }
      } catch (error) {
        uni.hideLoading();
      }
    },
    // 查询配置
    async getConfig() {
      let { data } = await getSysPlatformConfigByKeyList([
        "patient_condition_param_must",
      ]);

      if (data && data.length) {
        let item = data[0];
        if (item.configValue && item.configValue == 1) {
          this.isRequired = true;
        }
      }
    },
    // 生成空prid
    async getPrid() {
      let { patientId, patientName } = this.info;
      let obj = {
        patientId,
        patientName,
      };
      let { data } = await savePatientRecords(obj);
      uni.setStorageSync("prId", data.prId);
      return data.prId;
    },
  },
};
</script>

<style scoped lang="scss">
body {
  background: #f0f2fc !important;
}
.page-container {
  min-height: 100vh;
  box-sizing: border-box;
}

/* 主体内容 */
.bg_wh {
  padding: 0rpx 32rpx 100rpx;
}

.padding_24 {
  padding: 0 24rpx;
}

/* 问诊信息 */
.patient_box {
  margin-top: 20rpx;
  border-radius: 3.85px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 1px 4px rgba(121, 72, 234, 0.15);
  padding: 14rpx 26rpx;
}

.person_info {
  height: 70rpx;
  //border-bottom: 1px solid #ebebeb;
  box-sizing: border-box;
  color: #666666;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.person_info text:first-child {
  width: 180rpx;
}

.person_info:last-child {
  border-bottom: none;
}

/* 图片 */
.img_box {
  margin-top: 20rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border-radius: 3.85px;
  box-shadow: 0px 1px 4px rgba(121, 72, 234, 0.15);

  image {
    width: 25%;
    height: 190rpx;
    vertical-align: top;
  }
}

/* 问诊说明 */
.content_font {
  padding: 28rpx 29rpx;
  color: #666666;
  font-size: 26rpx;
  line-height: 40rpx;
  border-radius: 9.94px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 1px 4px rgba(121, 72, 234, 0.15);
  margin-top: 20rpx;
}

.fiexd {
  width: 100%;
  position: sticky;
  bottom: 108rpx;
  background: #ffffff;
  z-index: 1;
  padding: 32rpx 0;
}

/* 协议信息 */
.agreeInfo {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #333333;
  justify-content: center;

  ::v-deepuni-checkbox .uni-checkbox-input.uni-checkbox-input-checked {
    color: #fff;
    @include bg_theme;
    @include border_theme;
    border-radius: 50%;
  }

  ::v-deep uni-checkbox .uni-checkbox-input {
    border-radius: 50%;
  }

  .agreeInfo .checked {
    margin-right: 20rpx;
    border-radius: 50%;
  }

  .blue_click {
    @include font_theme;
    padding: 0 10rpx;
  }
}

.has_frist {
  @include flex;
  font-size: 24rpx;
  margin-top: 24rpx;

  ::v-deepuni-checkbox .uni-checkbox-input.uni-checkbox-input-checked {
    color: #fff;
    @include bg_theme;
    @include border_theme;
    border-radius: 50%;
  }

  ::v-deep uni-checkbox .uni-checkbox-input {
    border-radius: 50%;
  }
}
.across_provinces {
  margin-bottom: 40rpx;
  .title {
    font-size: 28rpx;
  }
  .info {
    height: 70vh;
    overflow: auto;
    padding: 24rpx 10rpx;
    font-size: 28rpx;
    line-height: 42rpx;
    font-weight: normal;
    text-indent: 40rpx;
    text-align: left;
  }
}
</style>
