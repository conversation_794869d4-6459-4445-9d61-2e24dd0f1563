<template>
  <view class="store">
    <view>
      <view v-for="(item,index) in storeList" :key="index" class="store-item" @click="checkStore(item)">
        <view class="store-item-left">
          <img :src="item.drugstoreImgUrl||'/static/shop/store-empty.png'" alt="">
        </view>
        <view class="store-item-right">
          <view class="title">
            <text>{{ item.drugstoreName }}</text>
            <text>10km</text>
          </view>
          <view>{{ item.address }}</view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import {getDrugStoreInfoPage, queryStoresByDrugId} from "../../api/base";

export default  {
  data() {
    return {
      storeList:[],
      isDrugDetail:null,
      drugId:null
    }
  },
  onLoad(query) {
    console.log('query',query)
    this.isDrugDetail=query.isDrugDetail
    this.drugId=query.drugId
    if(!this.isDrugDetail){
      this.getDrugStoreInfoPage()
    }else{
      this.queryStoresByDrugId()
    }
  },
  methods:{
    async queryStoresByDrugId(){
      let source= uni.getStorageSync('sourceCode')||0
      const data={
        drugId:this.drugId
      }
      const res=await  queryStoresByDrugId(data)
      if(source!='618') {
        console.log(source,'source');
        this.storeList=res.data.filter(item=>item.drugstoreId!='574a40ea01424f7a8b62762b37ff58e2')
        return
      }
      this.storeList=res.data
    },
    async getDrugStoreInfoPage(){
      const res=await getDrugStoreInfoPage({
        // page:1,
        // limit:999,
        // dpsId:"9ffa142fbd22518253bd6445e657a017",
        // "subjectType":"1"
      })
      let source= uni.getStorageSync('sourceCode')||0
      if(source!='618') {
        console.log(source,'source888');
        this.storeList=res.data.filter(item=>item.drugstoreId!='574a40ea01424f7a8b62762b37ff58e2')
        return
      }
      this.storeList=res.data
    },
    checkStore(item){
      if(this.isDrugDetail==1){
        this.$store.commit("shop/SET_DrugDetailStore",{
          drugstoreName:item.drugstoreName,
          drugstoreId:item.drugstoreId,
          ...item
        })
        uni.navigateBack({
          delta: 1
        })
        return
      }
      this.$store.commit("shop/SETCHECKSTORE",{
        drugstoreName:item.drugstoreName,
        drugstoreId:item.drugstoreId,
        ...item
      })
      // uni.setStorageSync("currentStore",item.drugstoreName)
      // uni.setStorageSync("currentStoreId",item.drugstoreId)
      uni.navigateBack({
        delta: 1
      })
    }
  }
}
</script>
<style scoped lang="scss">
.store{
  padding: 30rpx;
}
.store-item{
  width: 100%;
  background: white;
  box-sizing: border-box;
  padding: 20rpx;
  border-radius: 8px;
  display: flex;
  margin-bottom: 10px;
  .store-item-left{
    img{
      width: 80px;
      height: 80px;
      margin-right: 10px;
    }
  }
  .store-item-right{
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
  }
  .title{
    display: flex;
    justify-content: space-between;
  }
}
</style>