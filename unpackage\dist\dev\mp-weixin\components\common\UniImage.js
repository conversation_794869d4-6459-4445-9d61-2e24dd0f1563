"use strict";
const common_vendor = require("../../common/vendor.js");
const common_js_myJsTools = require("../../common/js/myJsTools.js");
const _sfc_main = {
  name: "UniImage",
  props: {
    // 图片源，可以是fileId或完整URL
    src: {
      type: String,
      default: ""
    },
    // 默认图片
    defaultSrc: {
      type: String,
      default: ""
    },
    // 错误时显示的图片
    errorSrc: {
      type: String,
      default: ""
    },
    // 图片裁剪、缩放的模式
    mode: {
      type: String,
      default: "widthFix"
    },
    // 图片懒加载
    lazyLoad: {
      type: Boolean,
      default: true
    },
    // 图片显示动画效果
    fadeShow: {
      type: Boolean,
      default: true
    },
    // 在系统不支持webp的情况下是否单独启用webp
    webp: {
      type: Boolean,
      default: false
    },
    // 开启长按图片显示识别小程序码菜单
    showMenuByLongpress: {
      type: Boolean,
      default: false
    },
    // 是否启用点击预览
    preview: {
      type: Boolean,
      default: false
    },
    // 预览时的图片列表（如果不传则使用当前图片）
    previewUrls: {
      type: Array,
      default: () => []
    },
    // 自定义样式类
    customClass: {
      type: String,
      default: ""
    },
    // 自定义样式
    customStyle: {
      type: Object,
      default: () => ({})
    },
    // 宽度
    width: {
      type: [String, Number],
      default: ""
    },
    // 高度
    height: {
      type: [String, Number],
      default: ""
    }
  },
  data() {
    return {
      currentSrc: "",
      isLoading: true,
      isError: false,
      imageUrlCache: {}
      // 静态缓存，所有实例共享
    };
  },
  computed: {
    imageClass() {
      return `uni-image ${this.customClass}`;
    },
    imageStyle() {
      let style = { ...this.customStyle };
      if (this.width) {
        style.width = typeof this.width === "number" ? `${this.width}rpx` : this.width;
      }
      if (this.height) {
        style.height = typeof this.height === "number" ? `${this.height}rpx` : this.height;
      }
      return style;
    }
  },
  watch: {
    src: {
      handler(newSrc) {
        this.loadImage(newSrc);
      },
      immediate: true
    }
  },
  // 使用全局缓存
  beforeCreate() {
    if (!this.$root.$imageUrlCache) {
      this.$root.$imageUrlCache = {};
    }
    this.imageUrlCache = this.$root.$imageUrlCache;
  },
  methods: {
    // 加载图片
    async loadImage(src) {
      if (!src || src === "null") {
        this.currentSrc = this.defaultSrc;
        return;
      }
      if (src.startsWith("http://") || src.startsWith("https://") || src.startsWith("/")) {
        this.currentSrc = src;
        return;
      }
      if (this.imageUrlCache[src]) {
        this.currentSrc = this.imageUrlCache[src];
        return;
      }
      this.currentSrc = this.defaultSrc;
      this.isLoading = true;
      try {
        await common_js_myJsTools.myJsTools.downAndSaveImg(
          src,
          (url) => {
            if (url) {
              this.imageUrlCache[src] = url;
              this.currentSrc = url;
              this.isLoading = false;
              this.$emit("load-success", url);
            }
          },
          () => {
            console.log("图片加载失败:", src);
            this.imageUrlCache[src] = this.errorSrc;
            this.currentSrc = this.errorSrc;
            this.isLoading = false;
            this.isError = true;
            this.$emit("load-error", src);
          }
        );
      } catch (error) {
        console.error("加载图片失败:", error);
        this.imageUrlCache[src] = this.errorSrc;
        this.currentSrc = this.errorSrc;
        this.isLoading = false;
        this.isError = true;
        this.$emit("load-error", error);
      }
    },
    // 图片加载成功
    handleLoad(e) {
      this.isLoading = false;
      this.isError = false;
      this.$emit("load", e);
    },
    // 图片加载失败
    handleError(e) {
      console.log(e, "handleError handleError");
      this.isLoading = false;
      this.isError = true;
      this.currentSrc = this.errorSrc;
      this.$emit("error", e);
    },
    // 点击事件
    handleClick(e) {
      this.$emit("click", e);
      if (this.preview && !this.isError) {
        this.previewImage();
      }
    },
    // 预览图片
    previewImage() {
      let urls = [];
      let current = 0;
      if (this.previewUrls.length > 0) {
        urls = this.previewUrls;
        current = urls.indexOf(this.currentSrc);
        if (current === -1)
          current = 0;
      } else {
        urls = [this.currentSrc];
        current = 0;
      }
      common_vendor.index.previewImage({
        urls,
        current,
        fail: (err) => {
          console.error("预览图片失败:", err);
          common_vendor.index.showToast({
            title: "预览失败",
            icon: "none"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.n($options.imageClass),
    b: common_vendor.s($options.imageStyle),
    c: $data.currentSrc,
    d: $props.mode,
    e: $props.lazyLoad,
    f: $props.webp,
    g: $props.showMenuByLongpress,
    h: common_vendor.o((...args) => $options.handleError && $options.handleError(...args)),
    i: common_vendor.o((...args) => $options.handleLoad && $options.handleLoad(...args)),
    j: common_vendor.o((...args) => $options.handleClick && $options.handleClick(...args))
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-fa55b860"]]);
wx.createComponent(Component);
