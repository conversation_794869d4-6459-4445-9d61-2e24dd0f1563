<template>
  <view class="nav-container">
    <uni-nav-bar
      :title="title"
      :color="color"
      :left-icon="isBack"
      fixed="true"
      @clickLeft="goBack"
    ></uni-nav-bar>
  </view>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: '互联网医院',
    },
    isBack: {
      type: String,
      default: '',
    },
    color: {
      type: String,
      default: '#333333',
    },
  },
  data() {
    return {};
  },
  methods: {
    goBack() {
      // 获取当前页面栈数量
      let length = getCurrentPages().length;
      // 可以后退
      if (length > 1) {
        uni.navigateBack({
          delta: 1,
        });
      } else {
        // 刷新页面导致页面栈丢失
        history.back();
      }
    },
  },
};
</script>

<style scoped lang="scss">
/* 导航栏样式 */
/deep/.uni-navbar--fixed {
  width: 100%;
  border: none !important;
}

/deep/.uni-navbar__header {
  min-height: 88rpx;
  width: 100%;
}

/deep/.uni-navbar--border {
  border-bottom: none !important;
}
/deep/ .uni-navbar__header-container-inner {
  font-size: 36upx;
}
</style>
