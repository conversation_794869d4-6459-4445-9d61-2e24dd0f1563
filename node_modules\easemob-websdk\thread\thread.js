!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.websdk=t():e.websdk=t()}(self,(function(){return(self.webpackChunkwebsdk=self.webpackChunkwebsdk||[]).push([[423],{4667:function(e,t,a){a.r(t),a.d(t,{changeChatThreadName:function(){return l},createChatThread:function(){return s},destroyChatThread:function(){return u},getChatThreadDetail:function(){return y},getChatThreadLastMessage:function(){return T},getChatThreadMembers:function(){return p},getChatThreads:function(){return I},getJoinedChatThreads:function(){return m},joinChatThread:function(){return d},leaveChatThread:function(){return h},removeChatThreadMember:function(){return f}}),a(2675),a(9463),a(2259),a(8706),a(1629),a(4346),a(3792),a(739),a(2010),a(6099),a(3362),a(7764),a(3500),a(2953);var r=a(8678),n=a(1750),c=a(565),o=function(e,t,a,r){return new(a||(a=Promise))((function(n,c){function o(e){try{s(r.next(e))}catch(e){c(e)}}function i(e){try{s(r.throw(e))}catch(e){c(e)}}function s(e){var t;e.done?n(e.value):(t=e.value,t instanceof a?t:new a((function(e){e(t)}))).then(o,i)}s((r=r.apply(e,t||[])).next())}))},i=function(e,t){var a,r,n,c,o={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return c={next:i(0),throw:i(1),return:i(2)},"function"==typeof Symbol&&(c[Symbol.iterator]=function(){return this}),c;function i(c){return function(i){return function(c){if(a)throw new TypeError("Generator is already executing.");for(;o;)try{if(a=1,r&&(n=2&c[0]?r.return:c[0]?r.throw||((n=r.return)&&n.call(r),0):r.next)&&!(n=n.call(r,c[1])).done)return n;switch(r=0,n&&(c=[2&c[0],n.value]),c[0]){case 0:case 1:n=c;break;case 4:return o.label++,{value:c[1],done:!1};case 5:o.label++,r=c[1],c=[0];continue;case 7:c=o.ops.pop(),o.trys.pop();continue;default:if(!((n=(n=o.trys).length>0&&n[n.length-1])||6!==c[0]&&2!==c[0])){o=0;continue}if(3===c[0]&&(!n||c[1]>n[0]&&c[1]<n[3])){o.label=c[1];break}if(6===c[0]&&o.label<n[1]){o.label=n[1],n=c;break}if(n&&o.label<n[2]){o.label=n[2],o.ops.push(c);break}n[2]&&o.ops.pop(),o.trys.pop();continue}c=t.call(e,o)}catch(e){c=[6,e],r=0}finally{a=n=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,i])}}};function s(e){if("string"!=typeof e.name||""===e.name)throw Error("Invalid parameter name: ".concat(e.name));if("string"!=typeof e.messageId||""===e.messageId)throw Error("Invalid parameter messageId: ".concat(e.messageId));if("string"!=typeof e.parentId||""===e.parentId)throw Error("Invalid parameter parentId: ".concat(e.parentId));var t=n.dO.call(this).error;if(t)return Promise.reject(t);var a=this.context,c=a.orgName,o=a.appName,i=a.accessToken,s=a.jid,d={name:e.name,msg_id:e.messageId,group_id:e.parentId,owner:this.user},h={url:"".concat(this.apiUrl,"/").concat(c,"/").concat(o,"/thread?resource=").concat(s.clientResource),type:"POST",dataType:"json",data:JSON.stringify(d),headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return r.RD.call(this,h).then((function(e){var t=e.data.thread_id;return e.data={chatThreadId:t},e}))}function d(e){if("string"!=typeof e.chatThreadId||""===e.chatThreadId)throw Error("Invalid parameter chatThreadId: ".concat(e.chatThreadId));var t=n.dO.call(this).error;if(t)return Promise.reject(t);var a=this.context,c=a.orgName,o=a.appName,i=a.accessToken,s=a.jid,d={url:"".concat(this.apiUrl,"/").concat(c,"/").concat(o,"/thread/").concat(e.chatThreadId,"/user/").concat(this.user,"/join?resource=").concat(s.clientResource),type:"POST",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return r.RD.call(this,d).then((function(e){var t=e.data.detail;return t.messageId=t.msgId,t.parentId=t.groupId,delete t.msgId,delete t.groupId,e}))}function h(e){return o(this,void 0,void 0,(function(){var t,a,c,o,s,d,h;return i(this,(function(i){switch(i.label){case 0:if("string"!=typeof e.chatThreadId||""===e.chatThreadId)throw Error("Invalid parameter chatThreadId: ".concat(e.chatThreadId));return(t=n.dO.call(this).error)?[2,Promise.reject(t)]:(a=this.context,c=a.orgName,o=a.appName,s=a.accessToken,d=a.jid,h={url:"".concat(this.apiUrl,"/").concat(c,"/").concat(o,"/thread/").concat(e.chatThreadId,"/user/").concat(this.user,"/quit?resource=").concat(d.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+s,"Content-Type":"application/json"}},[4,r.RD.call(this,h)]);case 1:return i.sent(),[2]}}))}))}function u(e){return o(this,void 0,void 0,(function(){var t,a,c,o,s,d,h;return i(this,(function(i){switch(i.label){case 0:if("string"!=typeof e.chatThreadId||""===e.chatThreadId)throw Error("Invalid parameter chatThreadId: ".concat(e.chatThreadId));return(t=n.dO.call(this).error)?[2,Promise.reject(t)]:(a=this.context,c=a.orgName,o=a.appName,s=a.accessToken,d=a.jid,h={url:"".concat(this.apiUrl,"/").concat(c,"/").concat(o,"/thread/").concat(e.chatThreadId,"?resource=").concat(d.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+s,"Content-Type":"application/json"}},[4,r.RD.call(this,h)]);case 1:return i.sent(),[2]}}))}))}function l(e){if("string"!=typeof e.chatThreadId||""===e.chatThreadId)throw Error("Invalid parameter chatThreadId: ".concat(e.chatThreadId));if("string"!=typeof e.name||""===e.name)throw Error("Invalid parameter name: ".concat(e.name));var t=n.dO.call(this).error;if(t)return Promise.reject(t);var a=this.context,c=a.orgName,o=a.appName,i=a.accessToken,s=a.jid,d={name:e.name},h={url:"".concat(this.apiUrl,"/").concat(c,"/").concat(o,"/thread/").concat(e.chatThreadId,"?resource=").concat(s.clientResource),type:"PUT",dataType:"json",data:JSON.stringify(d),headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return r.RD.call(this,h)}function p(e){if("string"!=typeof e.chatThreadId||""===e.chatThreadId)throw Error("Invalid parameter chatThreadId: ".concat(e.chatThreadId));var t=n.dO.call(this).error;if(t)return Promise.reject(t);var a=this.context,c=a.orgName,o=a.appName,i=a.accessToken,s={limit:e.pageSize||20,cursor:e.cursor||""},d={url:"".concat(this.apiUrl,"/").concat(c,"/").concat(o,"/thread/").concat(e.chatThreadId,"/users"),type:"GET",dataType:"json",data:s,headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return r.RD.call(this,d)}function f(e){if("string"!=typeof e.chatThreadId||""===e.chatThreadId)throw Error("Invalid parameter chatThreadId: ".concat(e.chatThreadId));if("string"!=typeof e.username||""===e.username)throw Error("Invalid parameter username: ".concat(e.username));var t=n.dO.call(this).error;if(t)return Promise.reject(t);var a=this.context,c=a.orgName,o=a.appName,i=a.accessToken,s=a.jid,d={url:"".concat(this.apiUrl,"/").concat(c,"/").concat(o,"/thread/").concat(e.chatThreadId,"/users/").concat(e.username,"?resource=").concat(s.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return r.RD.call(this,d)}function m(e){var t=n.dO.call(this).error;if(t)return Promise.reject(t);var a=this.context,c=a.orgName,o=a.appName,i=a.accessToken,s={limit:e.pageSize||20,cursor:e.cursor||""},d={url:e.parentId?"".concat(this.apiUrl,"/").concat(c,"/").concat(o,"/threads/chatgroups/").concat(e.parentId,"/user/").concat(this.user):"".concat(this.apiUrl,"/").concat(c,"/").concat(o,"/threads/user/").concat(this.user),type:"GET",dataType:"json",data:s,headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return r.RD.call(this,d).then((function(e){var t=e.entities;return null==t||t.forEach((function(e){e.parentId=e.groupId,e.messageId=e.msgId,delete e.groupId,delete e.msgId})),e}))}function I(e){if("string"!=typeof e.parentId||""===e.parentId)throw Error("Invalid parameter parentId: ".concat(e.parentId));var t=n.dO.call(this).error;if(t)return Promise.reject(t);var a=this.context,c=a.orgName,o=a.appName,i=a.accessToken,s={cursor:e.cursor||"",limit:e.pageSize||20},d={url:"".concat(this.apiUrl,"/").concat(c,"/").concat(o,"/threads/chatgroups/").concat(e.parentId),type:"GET",dataType:"json",data:s,headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return r.RD.call(this,d).then((function(e){var t=e.entities;return null==t||t.forEach((function(e){e.parentId=e.groupId,e.messageId=e.msgId,delete e.groupId,delete e.msgId})),e}))}function T(e){var t=this;if(!Array.isArray(e.chatThreadIds))throw Error("Invalid parameter chatThreadIds: ".concat(e.chatThreadIds));var a=n.dO.call(this).error;if(a)return Promise.reject(a);var c=this.context,o=c.orgName,i=c.appName,s=c.accessToken,d={threadIds:e.chatThreadIds},h={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(i,"/thread/message"),type:"POST",dataType:"json",data:JSON.stringify(d),headers:{Authorization:"Bearer "+s,"Content-Type":"application/json"}};return r.RD.call(this,h).then((function(e){return g.call(t,e)}))}function g(e){var t=this,a=e.entities;return null==a||a.forEach((function(e){e.chatThreadId=e.thread_id,e.last_message&&"{}"!==JSON.stringify(e.last_message)?e.lastMessage=c.h.call(t,e.last_message):e.lastMessage=e.last_message,delete e.thread_id,delete e.last_message})),e}function y(e){if("string"!=typeof e.chatThreadId||""===e.chatThreadId)throw Error("Invalid parameter chatThreadId: ".concat(e.chatThreadId));var t=n.dO.call(this).error;if(t)return Promise.reject(t);var a=this.context,c=a.orgName,o=a.appName,i=a.accessToken,s={url:"".concat(this.apiUrl,"/").concat(c,"/").concat(o,"/thread/").concat(e.chatThreadId),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return r.RD.call(this,s).then((function(e){return e.data.affiliationsCount=e.data.affiliations_count,e.data.messageId=e.data.msgId,e.data.parentId=e.data.groupId,delete e.data.affiliations_count,delete e.data.msgId,delete e.data.groupId,e}))}}},function(e){return e(e.s=4667)}])}));