/**
 * uni-app 公共方法封装
 * 适用于 uni-app 跨平台开发
 */

const myJsTools = {};

/**
 * 存储  global_config 全局参数配置  patientInfo患者信息存储  active 切换之后的tab保存状态
 *       token_info 登录信息  showCodeLogin 登录方式状态存储  myActive 我的处方tab 状态存储
 *       chatList 聊天记录存储，未读消息记录存储
 */

/**
 * 同步存储数据
 * @param {string} key 存储键名
 * @param {any} value 存储值
 */
myJsTools.setItem = function(key, value) {
  try {
    const ms = 'mystorage';
    let mydata = uni.getStorageSync(ms);

    if (!mydata) {
      mydata = { data: {} };
    } else if (typeof mydata === 'string') {
      mydata = JSON.parse(mydata);
    }

    mydata.data[key] = value;
    uni.setStorageSync(ms, mydata);
  } catch (e) {
    console.error('存储数据失败:', e);
    uni.showToast({
      title: '存储数据失败',
      icon: 'none'
    });
  }
};

/**
 * 异步存储数据
 * @param {string} key 存储键名
 * @param {any} value 存储值
 * @param {function} success 成功回调
 * @param {function} fail 失败回调
 */
myJsTools.setItemAsync = function(key, value, success, fail) {
  const ms = 'mystorage';

  uni.getStorage({
    key: ms,
    success: (res) => {
      let mydata = res.data;
      if (typeof mydata === 'string') {
        mydata = JSON.parse(mydata);
      }
      mydata.data[key] = value;

      uni.setStorage({
        key: ms,
        data: mydata,
        success: success,
        fail: fail
      });
    },
    fail: () => {
      // 如果获取失败，说明还没有数据，创建新的
      const mydata = { data: {} };
      mydata.data[key] = value;

      uni.setStorage({
        key: ms,
        data: mydata,
        success: success,
        fail: fail
      });
    }
  });
};

/**
 * 同步获取数据
 * @param {string} key 存储键名
 * @returns {any} 存储的值
 */
myJsTools.getItem = function(key) {
  try {
    const ms = 'mystorage';
    let mydata = uni.getStorageSync(ms);

    if (!mydata) {
      return null;
    }

    if (typeof mydata === 'string') {
      mydata = JSON.parse(mydata);
    }

    return mydata.data[key] || null;
  } catch (e) {
    console.error('获取数据失败:', e);
    return null;
  }
};

/**
 * 异步获取数据
 * @param {string} key 存储键名
 * @param {function} success 成功回调
 * @param {function} fail 失败回调
 */
myJsTools.getItemAsync = function(key, success, fail) {
  const ms = 'mystorage';

  uni.getStorage({
    key: ms,
    success: (res) => {
      try {
        let mydata = res.data;
        if (typeof mydata === 'string') {
          mydata = JSON.parse(mydata);
        }
        const value = mydata.data[key] || null;
        success && success(value);
      } catch (e) {
        console.error('解析数据失败:', e);
        fail && fail(e);
      }
    },
    fail: (err) => {
      // 如果获取失败，返回 null
      console.log('获取存储数据失败，返回默认值:', err);
      success && success(null);
    }
  });
};

/**
 * 同步删除指定键的数据
 * @param {string} key 存储键名
 */
myJsTools.removeItem = function(key) {
  try {
    const ms = 'mystorage';
    let mydata = uni.getStorageSync(ms);

    if (!mydata) {
      return;
    }

    if (typeof mydata === 'string') {
      mydata = JSON.parse(mydata);
    }

    delete mydata.data[key];
    uni.setStorageSync(ms, mydata);
  } catch (e) {
    console.error('删除数据失败:', e);
  }
};

/**
 * 异步删除指定键的数据
 * @param {string} key 存储键名
 * @param {function} success 成功回调
 * @param {function} fail 失败回调
 */
myJsTools.removeItemAsync = function(key, success, fail) {
  const ms = 'mystorage';

  uni.getStorage({
    key: ms,
    success: (res) => {
      let mydata = res.data;
      if (typeof mydata === 'string') {
        mydata = JSON.parse(mydata);
      }

      delete mydata.data[key];

      uni.setStorage({
        key: ms,
        data: mydata,
        success: success,
        fail: fail
      });
    },
    fail: fail
  });
};

/**
 * 同步清空所有数据
 */
myJsTools.clearItem = function() {
  try {
    const ms = 'mystorage';
    uni.removeStorageSync(ms);
  } catch (e) {
    console.error('清空数据失败:', e);
  }
};

/**
 * 异步清空所有数据
 * @param {function} success 成功回调
 * @param {function} fail 失败回调
 */
myJsTools.clearItemAsync = function(success, fail) {
  const ms = 'mystorage';
  uni.removeStorage({
    key: ms,
    success: success,
    fail: fail
  });
};

myJsTools.HashMap = function() {
  //定义长度
  var length = 0;
  //创建一个对象
  var obj = new Object();

  /**
   * 判断Map是否为空
   */
  this.isEmpty = function() {
    return length == 0;
  };

  /**
   * 判断对象中是否包含给定Key
   */
  this.containsKey = function(key) {
    return key in obj;
  };

  /**
   * 判断对象中是否包含给定的Value
   */
  this.containsValue = function(value) {
    for (var key in obj) {
      if (obj[key] == value) {
        return true;
      }
    }
    return false;
  };

  /**
   *向map中添加数据
   */
  this.put = function(key, value) {
    if (!this.containsKey(key)) {
      length++;
    }
    obj[key] = value;
  };

  /**
   * 根据给定的Key获得Value
   */
  this.get = function(key) {
    return this.containsKey(key) ? obj[key] : null;
  };

  /**
   * 根据给定的Key删除一个值
   */
  this.remove = function(key) {
    if (this.containsKey(key) && delete obj[key]) {
      length--;
    }
  };

  /**
   * 获得Map中的所有Value
   */
  this.values = function() {
    var _values = new Array();
    for (var key in obj) {
      _values.push(obj[key]);
    }
    return _values;
  };

  /**
   * 获得Map中的所有Key
   */
  this.keySet = function() {
    var _keys = new Array();
    for (var key in obj) {
      _keys.push(key);
    }
    return _keys;
  };

  /**
   * 获得Map的长度
   */
  this.size = function() {
    return length;
  };

  /**
   * 清空Map
   */
  this.clear = function() {
    length = 0;
    obj = new Object();
  };
};

export default myJsTools;
