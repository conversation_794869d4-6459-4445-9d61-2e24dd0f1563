"use strict";
const plugins_xeUtils_each = require("./each.js");
function helperCreateGetObjects(name, getIndex) {
  var proMethod = Object[name];
  return function(obj) {
    var result = [];
    if (obj) {
      if (proMethod) {
        return proMethod(obj);
      }
      plugins_xeUtils_each.each(obj, getIndex > 1 ? function(key) {
        result.push(["" + key, obj[key]]);
      } : function() {
        result.push(arguments[getIndex]);
      });
    }
    return result;
  };
}
exports.helperCreateGetObjects = helperCreateGetObjects;
