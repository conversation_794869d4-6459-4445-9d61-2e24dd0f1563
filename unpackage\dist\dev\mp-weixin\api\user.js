"use strict";
const common_request_request = require("../common/request/request.js");
function getOpenId(param = {}) {
  return common_request_request.http({
    url: "chat/syswechatuser/getOpenId",
    param,
    method: "post"
  });
}
function getUsertInfo(param = {}) {
  return common_request_request.http({
    url: "basic/hisLogin/hisPatientLoginAddToken",
    param,
    method: "post"
  });
}
function sendCaptcha(param = {}) {
  return common_request_request.http({
    url: "basic/hisLogin/sendCaptcha",
    param,
    method: "post"
  });
}
function hisPatientLoginBindingUserInfo(param = {}) {
  return common_request_request.http({
    url: "basic/hisLogin/hisPatientLoginBindingUserInfo",
    param,
    method: "post"
  });
}
function updateHxidIsregistStatus(param = {}) {
  return common_request_request.http({
    url: "basic/hisLogin/updateHxidIsregistStatus",
    param,
    method: "post"
  });
}
function getPatientList(param = {}) {
  return common_request_request.http({
    url: "basic/proofpatient/findProOfPatientListIsEnabled",
    param,
    method: "post"
  });
}
const queryRegisterStatus = (param) => {
  return common_request_request.http({
    url: "basic/dicPatientUser/queryRegisterStatus",
    param,
    method: "post"
  });
};
exports.getOpenId = getOpenId;
exports.getPatientList = getPatientList;
exports.getUsertInfo = getUsertInfo;
exports.hisPatientLoginBindingUserInfo = hisPatientLoginBindingUserInfo;
exports.queryRegisterStatus = queryRegisterStatus;
exports.sendCaptcha = sendCaptcha;
exports.updateHxidIsregistStatus = updateHxidIsregistStatus;
