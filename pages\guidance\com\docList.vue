<template>
  <!-- 推荐医生列表 -->
  <view class="doc_list">
    <!-- 顶部标题 -->
    <view class="doc_top">
      <!-- 标题 -->
      <view class="doc_title">
        <text></text>
        {{ tip.title || "为您推荐医生" }}
        <text></text>
      </view>
      <!-- 描述 -->
      <view class="doc_info">{{
        tip.tip || "根据您的描述推荐与您描述相匹配的医生"
      }}</view>
    </view>

    <!-- 推荐列表 -->
    <view class="rec_list">
      <!-- 单个医生信息 -->
      <block v-for="(item, index) in list">
        <view
          class="rec_item"
          v-if="index < 3 || showAll"
          :key="item.docId"
          @click="toDoc(item)"
        >
          <!-- 医生头像 -->
          <UniImage class="doc_head"
            v-if="item.docImg"
            :src="item.docImg"
            :data-src="errUrl"
          />
          <image
            src="/static/images/docHead.png"
            v-else
            class="doc_head"
          ></image>
          <!-- 右侧 -->
          <view class="doc_desc">
            <!-- 名字 -->
            <view class="desc_top">
              <view class="left">
                <text>{{ item.docName }}</text>
                <!-- 科室 -->
                <text>{{ item.docProf }}</text>
              </view>
              <!-- 右侧 -->
              <view class="right">
                <block v-for="v in item.visitType">
                  <text class="audio" v-if="v.visitTypeCode == 2">语音</text>
                  <text class="tw" v-if="v.visitTypeCode == 1">图文</text>
                </block>
              </view>
            </view>
            <!-- 科室 医院 -->
            <view class="desc_two">
              <text>{{ item.deptName }}</text>
              <text class="host">{{
                isShowWorkHosName ? item.workHosName : item.hosName
              }}</text>
            </view>
            <!-- 简介 -->
            <view class="desc_info" v-show="item.specialties">
              <!-- 擅长：对于感冒引起的发烧、头疼、干呕、发晕引起的 -->
              擅长：{{ item.specialties }}
            </view>
            <!-- 标签 -->
            <view class="desc_tag">
              <block v-for="(l, lk) in item.docLable">
                <text :key="lk">{{ l.lableName }}</text>
              </block>
            </view>
          </view>
        </view>
      </block>
      <!-- 查看更多 -->
      <view class="doc_more" v-if="list.length > 3 && !showAll">
        <button @click="showAll = true">查看更多</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "DocList",
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    tip: {
      type: Object,
      default: () => {
        return {
          title: "为您推荐医生",
          tip: "根据您的描述推荐与您描述相匹配的医生",
        };
      },
    },
  },
  data() {
    return {
      showAll: false,
      errUrl: require("../../../static/images/docHead.png"),
      isShowWorkHosName: uni.getStorageSync("isShowWorkHosName") || false,
    };
  },
  methods: {
    // 去医生主页
    toDoc(item) {
      // 医院id
      uni.setStorageSync("hosId", item.hosId);
      uni.navigateTo({
        url: "/pages/register/docHomePage/index?docId=" + item.docId,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.doc_list {
  .doc_top {
    text-align: center;

    .doc_title {
      @include flex;
      font-size: 32rpx;
      color: #333;
      font-weight: bold;
      line-height: 44rpx;

      text {
        width: 44rpx;
        height: 10rpx;
        border-top: 2px solid #e9e9e9;
        border-bottom: 2px solid #e9e9e9;
        margin: 0 10rpx;
      }
    }

    .doc_info {
      font-size: 24rpx;
      color: #999;
      line-height: 44rpx;
      margin-top: 12rpx;
    }
  }

  .rec_list {
    padding-top: 32rpx;

    .rec_item {
      background-color: #fff;
      padding: 24rpx;
      @include flex;
      align-items: flex-start;
      border-radius: 16rpx;
      margin-bottom: 32rpx;
      box-shadow: 0px 0px 16rpx 0px rgba(0, 0, 0, 0.05);

      .doc_head {
        width: 104rpx;
        height: 104rpx;
        border-radius: 8rpx;
        flex: none;
        margin-right: 16px;
      }

      .doc_desc {
        flex: 1;
        overflow: hidden;

        .desc_top {
          @include flex(lr);

          .left {
            font-size: 32rpx;
            color: #333;
            font-weight: bold;

            text:last-child {
              margin-left: 24rpx;
            }
          }

          .right {
            @include flex;
            gap: 20rpx;

            text {
              width: 88rpx;
              height: 36rpx;
              color: #fff;
              font-size: 26rpx;
              border-radius: 8rpx;
              text-align: center;

              &.audio {
                @include bg_theme;
              }

              &.tw {
                background-color: #ffb541;
              }
            }
          }
        }

        .desc_two {
          margin-top: 16rpx;
          font-size: 28rpx;
          color: #333;

          .host {
            margin-left: 24rpx;
          }
        }

        .desc_info {
          margin-top: 16rpx;
          color: #a6aab2;
          font-size: 24rpx;
          @include hide;
        }

        .desc_tag {
          margin-top: 16rpx;
          @include flex(left);
          gap: 16rpx;

          text {
            padding: 0 12rpx;
            height: 36rpx;
            border-radius: 18rpx;
            background-color: #e8f6fd;
            @include font_theme;
            font-size: 22rpx;
            @include flex;
          }
        }
      }
    }

    .doc_more {
      @include flex;

      button {
        width: 200rpx;
        height: 56rpx;
        border-radius: 28rpx;
        border: 2rpx solid #d8d8d8;
        @include flex;
        font-size: 24rpx;
        color: #d8d8d8;

        &:active {
          color: #d0d0d0;
        }
      }
    }
  }
}
</style>
