<template>
  <!-- 在线购药 -->
  <view class="shop">
    <!-- 标题 -->
    <view class="drug_title" @click="click">
      <text>药品信息</text>
      <block v-if="payStatus == 1">
        <uni-icons
          type="arrowright"
          color="#333"
          size="20"
          v-if="showIcon"
        ></uni-icons>
      </block>

      <block v-else>
        <uni-icons type="arrowright" color="#333" size="20"></uni-icons>
      </block>
    </view>
    <!-- 药店 药品列表 -->
    <view class="pharmacy_list" v-for="(p, pn) in list" :key="pn">
      <view class="pharmacy">
        <!-- 药店名称 -->
        <text
          >{{ p.drugstoreName
          }}{{ p.isProprietary == 1 ? "（自营）" : "" }}</text
        >
      </view>

      <!-- 药品列表 -->
      <view class="durg_list">
        <!-- 单个药品 -->
        <view
          class="durg_item"
          v-for="(d, dn) in p.drugShoppingOnlineOrderList"
          :key="dn"
        >
          <UniImage v-if="d.drugImg"
            :src="d.drugImg"
            :data-src="errUrl"
            class="left"
          />
          <image v-else class="left" src="/static/shop/drug.png"></image>
          <!-- 内容 -->
          <view class="right">
            <!-- 药品名称 -->
            <view class="drug_name">{{ d.drugName }}</view>
            <!-- 规格 -->
            <view class="drug_info">规格: {{ d.gg }}</view>
            <view class="drug_red" v-if="d.activeName"
              >单品{{ d.activeName }}</view
            >
            <!-- 价位数量 -->
            <view class="right_menu">
              <view class="price"
                >￥{{ d.drugRealMoney | toFixed }}
                <text class="del" v-if="d.drugShouldMoney != d.drugRealMoney"
                  >￥{{ d.drugShouldMoney | toFixed }}</text
                >
              </view>
              <text class="num">x{{ Number(d.quan) }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 物流信息 -->
      <view
        class="logistics"
        v-if="
          deliveryType == 1 && payStatus > 1 && payStatus != 7 && payStatus != 9
        "
      >
        <view class="item">
          <text>药店：{{ p.drugstoreName }}</text>

          <text>
            <block v-if="p.logisticsStatus == 0">待发货</block>
            <block v-if="p.logisticsStatus == 1">待收货</block>
            <block v-if="p.logisticsStatus == 2">已收货</block>
          </text>
        </view>

        <view class="item" v-if="p.logisticsStatus > 0">
          物流公司：{{ p.logisticsName || p.logisticsCustomName }}
        </view>

        <view class="item" v-if="p.logisticsStatus > 0">
          物流单号：{{ p.logisticsCode }}
        </view>
      </view>

      <!-- 存在otc 并且已审核 并且快递 显示按钮 -->
      <view
        class="status_buts"
        v-if="
          otc && [3, 4, 10].includes(Number(payStatus)) && deliveryType == 1
        "
      >
        <text
          class="one"
          v-if="p.logisticsStatus >= 1"
          @click.stop="lockLogist(p.logisticsCode, p.logisticsName)"
          >查看物流</text
        >
        <text
          v-if="p.logisticsStatus == 1"
          @click.stop="confirmGood(p.merchantsOrderNo)"
          >确认收货</text
        >
        <text v-if="p.logisticsStatus == 0" @click.stop="toast">催物流</text>
      </view>

      <!-- 不存在otc 支付 快递 -->
      <view
        class="status_buts"
        v-if="
          !otc && [3, 4, 10].includes(Number(payStatus)) && deliveryType == 1
        "
      >
        <text
          class="one"
          v-if="p.logisticsStatus >= 1"
          @click.stop="lockLogist(p.logisticsCode, p.logisticsName)"
          >查看物流</text
        >
        <text
          v-if="p.logisticsStatus == 1"
          @click.stop="confirmGood(p.merchantsOrderNo)"
          >确认收货</text
        >
        <!--        <text v-if="p.logisticsStatus == 0" @click.stop="toast">催物流</text>-->
      </view>

      <!-- 倒计时 -->
      <view class="time" v-if="payStatus == 1 && time > 0 && false">
        剩余{{ setTime(time) }}, 订单将自动取消，请尽快支付哦
      </view>
    </view>
  </view>
</template>

<script>
import { Toast } from "@/common/js/pay.js";
export default {
  name: "Shop",
  props: ["list", "payStatus", "deliveryType", "time", "otc", "showIcon"],
  data() {
    return {
      errUrl: require("../../../../static/shop/drug.png"),
    };
  },
  methods: {
    click() {
      if (this.payStatus == 1 && !this.showIcon) return;
      this.$emit("click");
    },
    // 提示
    toast() {
      Toast("已催物流");
    },
    // 查看物流
    lockLogist(code, name) {
      this.$emit("lockLogist", code, name);
    },
    // 确认收货
    confirmGood(merchantsOrderNo) {
      this.$emit("confirmGood", merchantsOrderNo);
    },
    // 统计
    getCount(list) {
      if (!list.length) return 0;
      let n = 0;
      list.forEach((v) => {
        v.drugShoppingOnlineOrderList.forEach((k) => {
          n += Number(k.quan);
        });
      });

      return n;
    },
    setTime(n) {
      if (!n) return "";
      // let h = parseInt(n / 60 / 60);
      let m = parseInt((n / 60) % 60);
      let s = parseInt(n % 60);
      // if (h < 10) h = '0' + h;
      if (m < 10) m = "0" + m;
      if (s < 10) s = "0" + s;
      return m + "分" + s + "秒";
    },
  },
};
</script>

<style lang="scss" scoped>
.shop {
  width: 100%;
  padding: 0 24rpx 24rpx;
  background-color: #fff;
  font-size: 28rpx;
  border-radius: 8rpx;

  .drug_title {
    @include flex(lr);
    font-size: 28rpx;
    height: 80rpx;
    border-bottom: 1px solid #eee;

    text {
      font-weight: bold;
    }
  }

  .order_num {
    @include flex(lr);
    font-size: 28rpx;
    font-weight: bold;
    height: 88rpx;
    border-bottom: 1px solid #ebebeb;

    text::nth-child(2) {
      font-weight: normal;
    }
  }

  .pharmacy_list {
    border-bottom: 1px dashed #ebebeb;

    .pharmacy {
      @include flex(lr);
      font-size: 28rpx;
      font-weight: bold;
      height: 88rpx;
      border-bottom: 1px solid #ebebeb;

      text::nth-child(2) {
        font-weight: normal;
      }

      text {
        color: #333;

        &.wait {
          color: #ff5050;
        }

        &.close {
          color: #999;
        }

        &.done {
          @include font_theme;
        }
      }
    }

    .status_buts {
      @include flex(right);
      height: 88rpx;

      text {
        @include border_theme;
        @include flex;
        font-size: 26rpx;
        margin-left: 24rpx;
        width: 158rpx;
        height: 56rpx;
        border-radius: 28rpx;
        @include bg_theme;
        color: #fff;

        &.one {
          background: none;
          @include font_theme;
        }
      }
    }

    .durg_list {
      .durg_item {
        @include flex;
        padding: 24rpx 0;

        &:last-child {
          .right {
            border-bottom: none;
          }
        }

        .left {
          width: 128rpx;
          height: 128rpx;
          margin-right: 24rpx;
          border: 1px solid #eee;
          border-radius: 8rpx;
          flex: none;
        }

        .right {
          flex: 1;
          min-height: 128rpx;
          @include flex(lr);
          flex-direction: column;
          align-items: stretch;
          border-bottom: 1px solid #eee;

          .drug_name {
            flex: 1;
            font-size: 28rpx;
            font-weight: bold;
          }

          .drug_info {
            flex: 1;
            font-size: 24rpx;
            color: #999;
          }

          .drug_red {
            font-size: 24rpx;
            color: red;
            flex: 1;
          }

          .right_menu {
            flex: 2;
            @include flex(lr);
            align-items: flex-end;

            .price {
              font-size: 28rpx;
              color: #ff3b30;
              font-weight: bold;

              .del {
                font-size: 20rpx;
                color: #999;
                margin-left: 10rpx;
                text-decoration: line-through;
                font-weight: normal;
              }
            }

            .num {
              font-size: 28rpx;
            }
          }
        }
      }
    }
  }

  .time {
    font-size: 24rpx;
    color: #ff3b30;
    height: 60rpx;
    @include flex(left);
  }

  .pay_buts {
    @include flex(right);

    text {
      @include flex;
      width: 158rpx;
      height: 56rpx;
      border-radius: 28rpx;
      @include bg_theme;
      color: #fff;
      font-size: 26rpx;
    }
  }

  .logistics {
    padding-bottom: 24rpx;

    .item {
      line-height: 50rpx;
      @include flex(lr);
      color: #333;
    }

    .item_menu {
      @include flex(right);
      padding-top: 24rpx;
    }
  }
}
</style>
