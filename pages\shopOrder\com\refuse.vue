<template>
  <div class="refuse">
    <div class="warp" v-for="(item, index) in list" :key="index">
      <!-- 后台 -->
      <block v-if="item.auditTime">
        <p class="label">
          驳回理由
        </p>

        <!-- 拒绝时间 -->
        <p class="date">
          <span>{{ item.auditTime }}</span>
          <span></span>
        </p>

        <!-- 具体 -->
        <div class="cont">
          <p class="text" v-if="item.reasonsForFailure">
            {{ item.reasonsForFailure }}
          </p>
          <p class="text" v-if="item.auditDescription">
            {{ item.auditDescription }}
          </p>

          <div class="img_list" v-if="item.pictureList">
            <UniImage v-for="img in item.pictureList.split('|')"
              :src="img" :preview="true"
              :key="img"
              alt=""
              class="img"
            />
          </div>
        </div>

        <!-- 分割 -->
        <div class="br" v-if="true"></div>
      </block>

      <!-- 用户 -->
      <block>
        <p class="label">
          取消信息
        </p>

        <!-- 拒绝时间 -->
        <p class="date">
          <span>{{ item.refundApplicationTime }}</span>
          <span></span>
        </p>

        <!-- 具体 -->
        <div class="cont">
          <p class="text" v-if="item.reasonsForApplication">
            {{ item.reasonsForApplication }}
          </p>
          <p class="text" v-if="item.applicationDescription">
            {{ item.applicationDescription }}
          </p>

          <div class="img_list" v-if="item.applicationPictureList">
            <UniImage v-for="img in item.applicationPictureList.split('|')"
              :src="img" :preview="true"
              :key="img"
              alt=""
              class="img"
            />
          </div>
        </div>
      </block>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Refuse',
  props: {
    // 列表
    list: {
      type: Array,
      default: () => [],
    },
    // 退款金额
    price: Number,
  },
};
</script>

<style lang="scss" scoped>
.refuse {
  padding-bottom: 24rpx;
  background-color: #fff;
  font-size: 28rpx;
  margin-bottom: 24rpx;

  .warp {
    padding: 0 32rpx 24rpx;
    border-bottom: 1px dashed #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .br {
      height: 1px;
      // background-color: #f5f5f5;
      margin-bottom: 24rpx;
    }
  }

  .label {
    color: red;
    line-height: 80rpx;
  }

  .date {
    @include flex(lr);
    color: #333;
    padding-bottom: 24rpx;
    border-bottom: 1px solid #f5f5f5;
  }

  .cont {
    background-color: #f5f5f5;
    padding: 24rpx;

    .text {
      line-height: 40rpx;
    }

    .img_list {
      margin-top: 24rpx;
      @include flex(left);
      flex-wrap: wrap;
      gap: 28rpx;

      .img {
        width: 194rpx;
        height: 194rpx;
        border-radius: 8rpx;
        object-fit: cover;
      }
    }
  }
}
</style>
