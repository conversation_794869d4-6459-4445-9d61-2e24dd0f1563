<template>
  <view class="swiper">
    <!-- #BAE3F7 -->
    <swiper
      v-if="list.length > 0"
      class="swiper_warp"
      indicator-dots
      autoplay
      circular
      indicator-color="rgba(255, 255, 255, 0.29)"
      indicator-active-color="#FFFFFF"
      :interval="swiperTime"
      :duration="300"
    >
      <swiper-item v-for="(item, index) in list" :key="index">
        <image
          class="img-box"
          v-if="item.carouselImg"
          :src="item.carouselImg"
          :lazy-load="true"
          mode="aspectFill"
          @error="handleImageError"
          @click="goDetail(item)"
        />
      </swiper-item>
    </swiper>
    <swiper
      v-else
      class="swiper_warp"
      indicator-dots
      autoplay
      circular
      indicator-color="rgba(255, 255, 255, 0.29)"
      indicator-active-color="#FFFFFF"
      :interval="3000"
      :duration="300"
    >
      <swiper-item>
        <image class="img-box" src="/static/images/topbanner.png"></image>
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
import { getConfigInfoByKey } from '@/api/base.js'
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      errUrl: require('../../../static/images/topbanner.png'),
      swiperTime: 5000,
    }
  },
  created() {
    this.getConfigInfoByKeys()
  },
  methods: {
    //获取轮播图参数
    async getConfigInfoByKeys() {
      let res = await getConfigInfoByKey({
        configKey: 'interval_time_for_patient',
      })
      let newArr = res.data
      this.swiperTime =
        newArr?.configValue > 0 ? Number(newArr?.configValue) * 1000 : 5000
      console.log(newArr)
    },
    // 图片加载错误处理
    handleImageError(e) {
      console.log('图片加载失败:', e)
      // 可以在这里设置默认图片或其他处理逻辑
      uni.showToast({
        title: '图片加载失败',
        icon: 'none',
        duration: 2000
      })
    },
    goDetail(item) {
      console.log('999', item)
      if (item.title === 'AI医生') {
        uni.navigateTo({
          url: '/pages/aiAssistant/previewImages?title=' + item.title,
        })
        return
      }
      if (item.title === 'AI健康咨询') {
        uni.navigateTo({
          url: '/pages/aiAssistant/previewImages?flag=2&title=' + item.title,
        })
        return
      }
      if (item.carouselType == 1) {
        uni.navigateTo({
          url:
            '/pages/index/com/swiperDetail?obj=' +
            JSON.stringify(item.contentInfo) +
            '&title=' +
            item.title,
        })
      } else {
        let linkUrl = item.contentInfo
        // 小程序环境下使用 uni.navigateTo 跳转到 webview 页面
        // H5 环境下可以直接使用 window.location.href
        // #ifdef H5
        window.location.href = linkUrl
        // #endif

        // #ifdef MP
        // 小程序中需要跳转到 webview 页面
        uni.navigateTo({
          url: '/pages/webview/webview?url=' + encodeURIComponent(linkUrl)
        })
        // #endif

        // #ifdef APP-PLUS
        // App 中可以使用内置浏览器打开
        plus.runtime.openURL(linkUrl)
        // #endif
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.swiper {
  background: #fff;
  border-radius: 8rpx;
}

.swiper_warp {
  height: 240rpx;
}

.img-box {
  width: 100%;
  height: 240rpx;
  object-fit: cover;
  border-bottom-left-radius: 28rpx;
  border-bottom-right-radius: 28rpx;
}
</style>
