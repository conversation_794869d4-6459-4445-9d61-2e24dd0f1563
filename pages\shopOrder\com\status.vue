<template>
  <!-- 状态 -->
  <view class="status">
    <!-- 线下支付 -->
    <block v-if="(payType > 5 || payType == 0) && orderStatus < 4">
      <!-- 订单状态1 -->
      <block v-if="orderStatus == 1">
        <block v-if="auditStatus < 3">
          <view class="h2">
            <image class="icon" src="/static/pre/1.png" />待审核</view
          >
          <view class="info">
            您的订单正在审核，请耐心等待
          </view>
        </block>
        <!-- 审核完成 待发货 待取药 -->
        <block v-if="auditStatus == 3">
          <block v-if="deliverytype == 1 || deliverytype == 3">
            <view class="h2">
              <image class="icon" src="/static/pre/1.png" />
              待发货
            </view>

            <view class="info">
              正在加急给您发货，请耐心等待
            </view>
          </block>
          <block v-else>
            <view class="h2">
              <image class="icon" src="/static/pre/1.png" />
              待取药
            </view>

            <view class="info">
              请尽快到店取药，超时未取，将不会为您保留药品
            </view></block
          >
        </block>
        <block v-if="auditStatus == 4">
          <view class="h2">
            <image class="icon" src="/static/inspection/x.png" />
            未通过
          </view>

          <view class="info">
            您的订单审核未通过，请修改之后重新进行提交
          </view></block
        >
        <block v-if="auditStatus == 5"
          ><view class="h2">
            <image class="icon" src="/static/inspection/jujue.png" />
            拒绝接单
          </view>

          <view class="info">
            药店拒绝您的订单
          </view></block
        >
      </block>

      <!-- 订单状态2 -->
      <block v-if="orderStatus == 2">
        <block v-if="deliverytype == 1 || deliverytype == 3"
          ><view class="h2">
            <image class="icon" src="/static/pre/1.png" />
            待发货
          </view>

          <view class="info">
            正在加急给您发货，请耐心等待
          </view></block
        >
        <block v-else>
          <view class="h2">
            <image class="icon" src="/static/pre/1.png" />
            待取药
          </view>

          <view class="info">
            请尽快到店取药，超时未取，将不会为您保留药品
          </view></block
        >
      </block>

      <block v-if="orderStatus == 3">
        <block v-if="deliverytype == 1 || deliverytype == 3"
          ><view class="h2">
            <image class="icon" src="/static/pre/1.png" />
            待收货
          </view>

          <view class="info">
            已为您发货，请注意查收
          </view></block
        >
        <block v-else>
          <view class="h2">
            <image class="icon" src="/static/pre/1.png" />
            待取药
          </view>

          <view class="info">
            请尽快到店取药，超时未取，将不会为您保留药品
          </view></block
        >
      </block>
    </block>

    <!-- 线上支付 -->
    <block v-if="payType < 5 && payType > 0 && orderStatus < 4">
      <block v-if="orderStatus == 1"
        ><view class="h2">
          <image class="icon" src="/static/pre/1.png" />
          待支付
        </view>

        <view class="info">
          剩余 {{ time }}，订单将自动取消，请尽快支付哦
        </view></block
      >
      <block v-if="orderStatus == 2">
        <block v-if="auditStatus == 1 || auditStatus == 2"
          ><view class="h2">
            <image class="icon" src="/static/pre/1.png" />
            待审核
          </view>

          <view class="info">
            您的订单正在审核，请耐心等待
          </view></block
        >
        <!-- 审核完成 待发货 待取药 -->
        <block v-if="auditStatus == 3">
          <block v-if="deliverytype == 1 || deliverytype == 3"
            ><view class="h2">
              <image class="icon" src="/static/pre/1.png" />
              待发货
            </view>

            <view class="info">
              正在加急给您发货，请耐心等待
            </view></block
          >
          <block v-else
            ><view class="h2">
              <image class="icon" src="/static/pre/1.png" />
              待取药
            </view>

            <view class="info">
              请尽快到店取药，超时未取，将不会为您保留药品
            </view></block
          >
        </block>
        <block v-if="auditStatus == 4"
          ><view class="h2">
            <image class="icon" src="/static/inspection/x.png" />
            未通过
          </view>

          <view class="info">
            您的订单审核未通过，请修改之后重新进行提交
          </view></block
        >
        <block v-if="auditStatus == 5">
          <view class="h2">
            <image class="icon" src="/static/inspection/jujue.png" />
            拒绝接单
          </view>

          <view class="info">
            药店拒绝您的订单
          </view></block
        >
      </block>
      <block v-if="orderStatus == 3">
        <block v-if="deliverytype == 1 || deliverytype == 3"
          ><view class="h2">
            <image class="icon" src="/static/pre/1.png" />
            待收货
          </view>

          <view class="info">
            已为您发货，请注意查收
          </view></block
        >
        <block v-else>
          <view class="h2">
            <image class="icon" src="/static/pre/1.png" />
            待取药
          </view>

          <view class="info">
            请尽快到店取药，超时未取，将不会为您保留药品
          </view></block
        >
      </block>
    </block>

    <!-- 其他不需要判断的状态 -->

    <block v-if="false">
      <view class="h2">
        <image class="icon" src="/static/pre/1.png" />
        待支付
      </view>

      <view class="info">
        剩余 ，订单将自动取消，请尽快支付哦
      </view>
    </block>

    <block v-if="false">
      <view class="h2">
        <image class="icon" src="/static/pre/1.png" />
        待审核
      </view>

      <view class="info">
        您的订单正在审核，请耐心等待
      </view>
    </block>

    <block v-if="false">
      <view class="h2">
        <image class="icon" src="/static/inspection/x.png" />
        未通过
      </view>

      <view class="info">
        您的订单审核未通过，请修改之后重新进行提交
      </view>
    </block>

    <block v-if="false">
      <view class="h2">
        <image class="icon" src="/static/inspection/jujue.png" />
        拒绝接单
      </view>

      <view class="info">
        药店拒绝您的订单
      </view>
    </block>

    <block v-if="false">
      <view class="h2">
        <image class="icon" src="/static/pre/1.png" />
        待发货
      </view>

      <view class="info">
        正在加急给您发货，请耐心等待
      </view>
    </block>

    <block v-if="false">
      <view class="h2">
        <image class="icon" src="/static/pre/1.png" />
        待收货
      </view>

      <view class="info">
        已为您发货，请注意查收
      </view>
    </block>

    <block v-if="false">
      <view class="h2">
        <image class="icon" src="/static/pre/1.png" />
        待取药
      </view>

      <view class="info">
        请尽快到店取药，超时未取，将不会为您保留药品
      </view>
    </block>

    <block v-if="orderStatus == 4">
      <view class="h2">
        <image class="icon" src="/static/inspection/done.png" />
        交易完成
      </view>

      <view class="info">
        本次服务已完成
      </view>
    </block>

    <block v-if="orderStatus == 6">
      <view class="h2">
        <image class="icon" src="/static/inspection/x.png" />

        <block v-if="auditStatus == 5">拒绝接单</block>
        <block v-else>已退费</block>
      </view>

      <view class="info">
        <block v-if="auditStatus == 5">药店拒绝您的订单</block>
        <block v-else>已为您退费，预计10个工作日内到账，请注意查收</block>
      </view>
    </block>

    <block v-if="orderStatus == 7">
      <block v-if="auditStatus == 5">
        <view class="h2">
          <image class="icon" src="/static/inspection/jujue.png" />
          拒绝接单
        </view>

        <view class="info">
          药店拒绝您的订单
        </view>
      </block>

      <block v-else>
        <view class="h2">
          <image class="icon" src="/static/inspection/x.png" />
          交易关闭
        </view>

        <view class="info">
          本次服务已关闭
        </view>
      </block>
    </block>
  </view>
</template>

<script>
export default {
  name: 'Status',
  props: {
    // 申请退费状态
    refundAuditStatus: String | Number,
    // 支付方式
    payType: String | Number,
    // 物流方式
    deliverytype: String | Number,
    // 订单状态
    orderStatus: String | Number,
    // 审核状态
    auditStatus: String | Number,
    // 倒计时
    time: String,
  },
};
</script>

<style lang="scss" scoped>
.status {
  height: 216rpx;
  background: #ff5050;
  padding: 32rpx;
  color: #fff;
  box-sizing: border-box;

  .h2 {
    font-size: 48rpx;
    font-weight: bold;
    @include flex(left);

    .icon {
      width: 44rpx;
      height: 44rpx;
      margin-right: 16rpx;
    }
  }

  .info {
    font-size: 24rpx;
    margin-top: 24rpx;

    text {
      font-size: 36rpx;
      font-weight: bold;
      padding: 0 8rpx;
    }
  }
}
</style>
