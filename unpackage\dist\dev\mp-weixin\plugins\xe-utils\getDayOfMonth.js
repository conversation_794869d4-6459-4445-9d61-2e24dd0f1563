"use strict";
const plugins_xeUtils_staticDayTime = require("./staticDayTime.js");
const plugins_xeUtils_staticStrFirst = require("./staticStrFirst.js");
const plugins_xeUtils_staticStrLast = require("./staticStrLast.js");
const plugins_xeUtils_helperGetDateTime = require("./helperGetDateTime.js");
const plugins_xeUtils_getWhatMonth = require("./getWhatMonth.js");
const plugins_xeUtils_toStringDate = require("./toStringDate.js");
const plugins_xeUtils_isValidDate = require("./isValidDate.js");
function getDayOfMonth(date, month) {
  date = plugins_xeUtils_toStringDate.toStringDate(date);
  if (plugins_xeUtils_isValidDate.isValidDate(date)) {
    return Math.floor((plugins_xeUtils_helperGetDateTime.helperGetDateTime(plugins_xeUtils_getWhatMonth.getWhatMonth(date, month, plugins_xeUtils_staticStrLast.staticStrLast)) - plugins_xeUtils_helperGetDateTime.helperGetDateTime(plugins_xeUtils_getWhatMonth.getWhatMonth(date, month, plugins_xeUtils_staticStrFirst.staticStrFirst))) / plugins_xeUtils_staticDayTime.staticDayTime) + 1;
  }
  return NaN;
}
exports.getDayOfMonth = getDayOfMonth;
