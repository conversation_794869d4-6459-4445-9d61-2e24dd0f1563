<template>
  <view class="nav-container">
    <uni-nav-bar
      :title="title"
      :color="color"
      :left-icon="isBack"
      fixed="true"
      @clickLeft="goBack"
    ></uni-nav-bar>
  </view>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: '互联网医院',
    },
    isBack: {
      type: String,
      default: '',
    },
    color: {
      type: String,
      default: '#333333',
    },
  },
  data() {
    return {};
  },
  methods: {
    goBack() {
      this.$emit('goBack');
    },
  },
};
</script>

<style scoped>
/* 导航栏样式 */
/deep/.uni-navbar--fixed {
  width: 100%;
  border: none !important;
}

/deep/.uni-navbar__header {
  height: 88rpx;
  width: 100%;
}

/deep/.uni-navbar--border {
  border-bottom: none !important;
}
</style>
