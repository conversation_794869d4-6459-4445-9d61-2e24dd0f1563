<template>
  <view class="customService">
    <doc-info
      :docId="pageParam.docId"
      :dept-id="pageParam.deptId"
      pageType="service"
      v-if="pageParam.docId"
    ></doc-info>
    <view class="custom">
      <!-- 服务卡片 -->
      <view class="serviceInfo">
        <view class="tips">提示</view>
        <view class="tipsInfo">医生给您推荐了一个服务</view>
        <view class="serviceContent">
          <image src="../../static/images/chat/server.png" mode=""></image>
          <text>{{ serveInfo.customBussTitle }}</text>
          <text class="price">￥{{ serveInfo.customBussPrice }} </text>
        </view>
        <!-- 状态角标 -->
        <image
          class="status_icon"
          src="/static/images/serve/serve_0.png"
          v-if="serveInfo.status == 0"
        ></image>
        <image
          class="status_icon"
          src="/static/images/serve/serve_1.png"
          v-if="serveInfo.status == 1"
        ></image>
        <image
          class="status_icon"
          src="/static/images/serve/serve_2.png"
          v-if="serveInfo.status == 2"
        ></image>
        <image
          class="status_icon"
          src="/static/images/serve/serve_3.png"
          v-if="serveInfo.status == 7"
        ></image>
      </view>

      <!-- 说明 -->
      <view class="serviceDesp">
        <view class="title">服务说明</view>
        <view>
          互联网诊疗仅适用常见疾病、慢性病复诊患者，急重诊患者请前往实体医疗机构就诊。
          复诊时必须提供包含诊断的实体机构的病历资料
          针对同诊断复诊。复诊患者，医生将根据您的实际情况辩证开方、给出调理建议。
        </view>
        <view>
          在线复诊过程中，若由于您无法提供实体医疗机构
          的诊段证明，或医生无法在线得出您和您之前线下实体机构相同的诊断而无法为您开出处方和诊断的情况，将不会退还复诊费用
        </view>
      </view>
      <!-- 协议 -->
      <view class="agreeInfo" v-if="serveInfo.status == 0">
        <checkbox
          value="val"
          @click="changeCheck"
          :checked="agreeVal"
          color="#fff"
          style="transform: scale(0.8)"
        />
        <text>同意</text
        ><text class="protocol" @click="openProtocol(8)"
          >《用户知情同意书》</text
        >
      </view>
    </view>

    <!-- 底部按钮 -->
    <FooterButton v-if="serveInfo.status == 0" @click="payCustomService"
      >立即支付</FooterButton
    >
  </view>
</template>

<script>
import { Toast } from '@/common/js/pay.js';
import DocInfo from '@/components/docInfo/docInfo';
import FooterButton from '@/components/footer_button/button.vue';
import { findCustomService } from '@/api/chatCardDetail';
import { addProsignagreement, findVisitAgreement } from '@/api/base';
import { getPayInfo, queryServePayStatus, getReceiptWay } from '@/api/order';

let num = 3;

export default {
  name: 'customService',
  components: {
    DocInfo,
    FooterButton,
  },
  data() {
    return {
      agreeVal: true,
      pageParam: {},
      val: '',
      userInfo: {}, //用户协议
      priceInfo: {}, //价格服务协议
      serveInfo: {}, //自定义服务接口返回信息
      psaId: '',
      payStatus: false,
      action: '', // 记录从那个页面进入
      payList: [],
      payItem: '',
    };
  },
  onLoad(option) {
    // let param = JSON.parse(option.param);
    // this.pageParam = param;
    this.findCustomServiceFun(option.id);
  },
  onShow() {},
  methods: {
    // 获取支付方式
    async getPayList() {
      let hosId = uni.getStorageSync('hosId');
      let res = await getReceiptWay({
        subjectId: hosId,
      });
      if (res.code != 20000) return;
      this.payList = res.data;
    },

    //查看协议
    openProtocol(type) {
      uni.navigateTo({
        url: '/pages/protocol/index?type=' + type,
      });
    },

    // 获取协议信息
    async getVisitAgreement() {
      uni.showLoading({
        title: '加载中',
      });
      let res = await findVisitAgreement({
        agreementType: '1',
      });
      this.userInfo = res.data;
      uni.hideLoading();
    },

    //改变checkbox状态
    changeCheck() {
      this.agreeVal = !this.agreeVal;
    },

    // 获取自定义服务详情
    async findCustomServiceFun(id) {
      let customBussinessId = this.pageParam.customBussinessId;
      let res = await findCustomService({
        customBussinessId: id,
      });
      if (res.data.status == 0) {
        this.getPayList();
        this.getVisitAgreement();
      }
      this.serveInfo = this.pageParam = res.data;
    },

    // 存用户协议同意记录
    async payCustomService() {
      if (!this.agreeVal) {
        Toast('请同意用户协议及价格服务协议');
        return;
      } else {
        // 获取协议id
        let userInfo = this.userInfo;
        let obj = {
          agreementId: userInfo.agreementId,
          agreementNama: userInfo.agreementName,
          agreementVersions: userInfo.agreementVersions,
          patientId: this.pageParam.patientId,
        };
        let res = await addProsignagreement(obj);
        this.psaId = res.data.uuid;
        this.selePay();
      }
    },

    // 选择支付方式
    selePay() {
      let that = this;
      if (!this.payList.length) {
        Toast('当前商户还没有支付方式');
        return false;
      }
      let money = this.serveInfo.customBussPrice;
      // 如果无需支付
      if (money == 0) {
        this.payItem = this.payList[0];
        this.getPay();
        return;
      }

      // 如果只有一种支付方式
      if (this.payList.length == 1) {
        this.payItem = this.payList[0];
        if (this.payItem.receiptType == 1) {
          this.getPay('wx');
        } else {
          this.getPay();
        }
        return;
      }

      // 选择支付方式
      uni.showActionSheet({
        itemList: ['微信支付', '支付宝支付'],
        success: function(res) {
          let index = res.tapIndex;
          // 微信支付
          if (index == 0) {
            that.payList.forEach((v) => {
              if (v.receiptType == 1) {
                that.payItem = v;
              }
            });
            if (!that.payItem) {
              uni.showModal({
                content: '暂不支持微信支付',
                showCancel: false,
              });
              return;
            }
            that.getPay('wx');
            return;
          } else {
            that.payList.forEach((v) => {
              if (v.receiptType == 2) {
                that.payItem = v;
              }
            });
            if (!that.payItem) {
              uni.showModal({
                content: '暂不支持支付宝支付',
                showCancel: false,
              });
              return;
            }
            that.getPay();
            return;
          }
        },
      });
    },

    // 点击支付
    getPay(type) {
      this.btnFlag = false;
      let me = this;
      let money = this.serveInfo.customBussPrice;
      let payType;
      if (money > 0) {
        if (type == 'wx') {
          payType = 1;
        } else {
          payType = 2;
        }
      } else if (money == 0) {
        payType = '0';
      } else {
        Toast('金额不能小于0');
      }

      uni.showLoading({
        mask: true,
      });

      var para = {
        callId: this.payItem.appid,
        psaId: this.psaId,
        customBussinessId: this.pageParam.customBussinessId,
        openid: uni.getStorageSync('wxInfo').openId,
        payType: payType,
      };

      getPayInfo(para)
        .then((response) => {
          uni.hideLoading();
          // 无需支付，返回成功，直接跳转
          if (response.data.success && response.data.success == '1') {
            uni.removeStorageSync('psaId');
            me.goBack();
            return;
          }

          let info = response.data;

          if (type == 'wx') {
            // 微信支付
            this.wxPay(info);
            return;
          } else {
            uni.navigateTo({
              url:
                '/pages/pay/pay?price=' +
                money +
                '&customBussinessId=' +
                this.pageParam.customBussinessId +
                '&url=' +
                btoa(info.url),
            });
          }
        })
        .catch((err) => {
          uni.hideLoading();
          this.btnFlag = true;
        });
    },

    // 微信支付
    wxPay(info) {
      let that = this;
      WeixinJSBridge.invoke('getBrandWCPayRequest', info, (res) => {
        if (res.err_msg == 'get_brand_wcpay_request:ok') {
          that.getState();
        } else {
          Toast('您已取消订单');
        }
      });
    },

    goBack() {
      // 获取上一页实例
      let pages = getCurrentPages();
      let prePage = pages[pages.length - 2];
      // 上一页是聊天页
      if (prePage.route == 'pages/chatList/chatDetail') {
        let id = this.pageParam.customBussinessId;
        // 调用上一页更改状态接口
        prePage.getServerStatus(id);
      }
      // getServerStatus
      uni.navigateBack({
        delta: 1,
      });
    },

    // 查询状态
    getState() {
      let me = this;
      if (num <= 0) {
        me.goBack();
        return;
      }
      uni.showLoading({
        title: '加载中',
      });
      queryServePayStatus({
        customBussinessId: me.pageParam.customBussinessId,
      }).then((res) => {
        num--;
        uni.hideLoading();
        if (res.data) {
          if (res.data.status == '1') {
            this.payStatus = true;
            me.goBack();
            return;
          } else {
            setTimeout(() => {
              me.getState();
            }, 3000);
            this.payStatus = false;
          }
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.custom {
  padding: 40rpx 32rpx 120rpx;
}

.serviceInfo {
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0px 0px 40rpx rgba(0, 0, 0, 0.1);
  padding: 24rpx 32rpx;
  position: relative;
  overflow: hidden;

  .tips {
    color: #333333;
    font-size: 28rpx;
    line-height: 40rpx;
  }

  .tipsInfo {
    margin-top: 24rpx;
    color: #999999;
    font-size: 28rpx;
    line-height: 40rpx;
    padding-bottom: 24rpx;
    border-bottom: 0.5px solid #ebebeb;
  }

  .serviceContent {
    color: #333333;
    font-size: 28rpx;
    display: flex;
    padding-top: 24rpx;
    align-items: center;
    font-weight: 600;
    position: relative;

    uni-image {
      width: 80rpx;
      height: 80rpx;
      margin-right: 24rpx;
    }
  }

  .status_icon {
    width: 80upx;
    height: 80upx;
    position: absolute;
    right: 0;
    top: 0;
  }
}

.serviceDesp {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 26rpx 32rpx;
  font-size: 28rpx;
  line-height: 40rpx;
  margin-top: 24rpx;
  color: #999999;

  .title {
    color: #333333;
    margin-bottom: 16rpx;
  }
}

.agreeInfo {
  font-size: 28rpx;
  color: #333333;
  text-align: center;
  padding: 22rpx 0;

  .protocol {
    @include font_theme;
  }

  ::v-deepuni-checkbox .uni-checkbox-input.uni-checkbox-input-checked {
    color: #fff;
    @include bg_theme;
    @include border_theme;
  }
}

.price {
  color: #999999;
  font-size: 22rpx;
  position: absolute;
  right: 36rpx;
  // top: 48rpx;
}
</style>
