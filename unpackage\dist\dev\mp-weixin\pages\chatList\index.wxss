/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*
	字体颜色相关
*/
/*
	背景色 分割线色
*/
/*
	主题色
*/
/*
		渐变可调角度
*/
/*
	定义弹性布局
 */
/*
	单行文本溢出隐藏
*/
.scroll-Y.data-v-a0119522 {
  width: 100%;
  height: 100%;
  height: calc(100% - 40px);
}
.ai-assistant-page.data-v-a0119522 {
  background-color: #f8f8f8;
  min-height: calc(100vh - 150px);
  padding-bottom: 20px;
}
.ai-assistant-header.data-v-a0119522 {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}
.ai-assistant-header .ai-logo.data-v-a0119522 {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}
.ai-assistant-header .ai-title.data-v-a0119522 {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
}
.ai-message-list.data-v-a0119522 {
  padding: 0 30rpx;
}
.ai-scroll-view.data-v-a0119522 {
  height: calc(100vh - 200px);
}
.ai-message-item.data-v-a0119522 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.ai-message-content.data-v-a0119522 {
  flex: 1;
  display: flex;
  align-items: flex-start;
}
.ai-message-info.data-v-a0119522 {
  flex: 1;
  margin-left: 20rpx;
}
.ai-message-title.data-v-a0119522 {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 16rpx;
}
.ai-message-details.data-v-a0119522 {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
}
.ai-message-patient.data-v-a0119522 {
  color: #666;
}
.ai-message-time.data-v-a0119522 {
  margin-left: 20rpx;
}
.ai-message-status.data-v-a0119522 {
  width: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.unread-dot.data-v-a0119522 {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #ff3b30;
}
.tips.data-v-a0119522 {
  width: 70rpx;
  height: 70rpx;
}
.search.data-v-a0119522 {
  width: 706rpx;
  height: 60rpx;
  opacity: 1;
  border-radius: 30rpx;
  background: #f2f5ff;
  margin: auto;
  margin-top: 20rpx;
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.search .uni-searchbar.data-v-a0119522 {
  border: none !important;
  background: none;
  width: 80%;
}
.search .uni-searchbar.data-v-a0119522 .uni-searchbar__box {
  border: none !important;
  background: none !important;
  justify-content: left !important;
}
.tab_title.data-v-a0119522 {
  display: flex;
  align-items: center;
  font-size: 34rpx;
  text-align: center;
  height: 88rpx;
  padding: 0 24rpx;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 3;
  box-sizing: border-box;
  background: #ffffff;
}
.tab_title .tab_title_box.data-v-a0119522 {
  flex: 1;
  position: relative;
}
.tab_title .tab_title_box .tab_title_abdge.data-v-a0119522 {
  position: absolute;
  top: 8rpx;
  right: 60rpx;
}
.tab_title .tab_title_box.data-v-a0119522 {
  flex: 1;
  position: relative;
}
.tab_title .tab_title_box .tab_title_abdge1.data-v-a0119522 {
  position: absolute;
  top: 30rpx;
  right: 32rpx;
  background: #dd524d;
  border-radius: 50%;
  height: 10px;
  width: 10px;
}
.tab_title .tab_title_font.data-v-a0119522 {
  color: #666666;
  display: inline-block;
  border-bottom: 6rpx solid transparent;
  line-height: 88rpx;
  padding-top: 6rpx;
  box-sizing: border-box;
}
.tab_title .active.data-v-a0119522 {
  padding-top: 6rpx;
  line-height: 88rpx;
  color: #836aff;
  display: inline-block;
  border-bottom: 6rpx solid #836aff;
  box-sizing: border-box;
  font-weight: 700;
}
[data-theme=nx] .tab_title .active.data-v-a0119522 {
  color: #107dff;
}
[data-theme=test] .tab_title .active.data-v-a0119522 {
  color: #2db99d;
}
[data-theme=nx] .tab_title .active.data-v-a0119522 {
  border-bottom: 6rpx solid #107dff;
}
[data-theme=test] .tab_title .active.data-v-a0119522 {
  border-bottom: 6rpx solid #2db99d;
}
.page.data-v-a0119522 {
  background-color: #fff;
  height: calc(100vh - 70px);
}
.empty.data-v-a0119522 {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  margin-top: 250rpx;
}
.emptyImg.data-v-a0119522 {
  width: 286rpx;
  height: 226rpx;
  margin-bottom: 40rpx;
}
.chatList.data-v-a0119522 {
  padding-left: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 90%;
  height: 192rpx;
  margin: auto;
  border-radius: 5px;
  background: white;
  box-shadow: 0px 1px 4px rgba(121, 72, 234, 0.15);
  margin-bottom: 30rpx;
}
.chatList .header_img.data-v-a0119522 {
  display: flex;
  position: relative;
  margin-right: 24rpx;
  flex: none;
}
.chatList .header_img .userImg.data-v-a0119522 {
  width: 104rpx;
  height: 104rpx;
  border-radius: 50%;
}
u.data-v-a0119522 {
  text-decoration: none;
  display: inline-block;
}
.docName.data-v-a0119522,
.deptName.data-v-a0119522 {
  color: #333;
  font-size: 30rpx;
  font-weight: 600;
}
.deptName.data-v-a0119522 {
  font-weight: 500;
  margin-left: 24rpx;
}
.status.data-v-a0119522 {
  color: #836aff;
  height: 44rpx;
  font-size: 24rpx;
  font-weight: 600;
  border-radius: 30rpx;
  background: #e8faf4;
  padding: 4rpx 16rpx;
}
[data-theme=nx] .status.data-v-a0119522 {
  color: #107dff;
}
[data-theme=test] .status.data-v-a0119522 {
  color: #2db99d;
}
.visitType.data-v-a0119522 {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #874ff0;
  font-size: 20rpx;
  height: 36rpx;
  line-height: 36rpx;
  padding: 0 18rpx;
  background: #f2f5ff;
  border-radius: 8rpx;
  margin-left: 24rpx;
  margin-top: 16rpx;
}
.info.data-v-a0119522 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  align-items: stretch;
  flex-direction: column;
  height: 100%;
  padding-right: 32rpx;
  border-bottom: 1px solid #ebebeb;
  overflow: hidden;
}
.info .info_title.data-v-a0119522 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.info .info_title .patientInfo.data-v-a0119522 {
  max-width: 63%;
}
.lastContent.data-v-a0119522 {
  color: #999999;
  font-size: 13px;
  line-height: 18px;
}
.patientName.data-v-a0119522 {
  color: #ffffff;
  font-size: 18rpx;
  background-color: #676767;
  border-radius: 14rpx;
  height: 28rpx;
  padding: 0 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.hours.data-v-a0119522 {
  color: #999999;
  font-size: 24rpx;
  margin-left: 20rpx;
  text-align: right;
  display: inline-block;
  flex: none;
}
.sysTime.data-v-a0119522 {
  margin-left: 50rpx;
}
.contentInfo.data-v-a0119522 {
  margin-top: 10rpx;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.fitrstChild.data-v-a0119522 {
  flex: 1;
  width: 55%;
}
.fitrstChild .lastContent.data-v-a0119522 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.chatListTop.data-v-a0119522 {
  background: #f5f5f5;
}
.data-v-a0119522 .uni-badge--error {
  color: #fff;
  background-color: #dd524d;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  right: 0px;
  top: 0px;
}
.k_page.data-v-a0119522 {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9;
}
.k_tips.data-v-a0119522 {
  width: 210rpx;
  max-height: 276rpx;
  background-color: #fff;
  border-radius: 8rpx;
  border: 1px solid #e5e5e5;
  box-sizing: border-box;
  padding: 10rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  align-items: stretch;
  flex-direction: column;
  box-shadow: 0 0 10rpx #bbb;
  position: absolute;
  z-index: 10;
}
.k_tips .tips_item.data-v-a0119522 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border-bottom: 1px solid #e5e5e5;
}
.k_tips .tips_item.data-v-a0119522:last-child {
  border-bottom: none;
}
.k_tips .tips_item .item_icon.data-v-a0119522 {
  width: 44rpx;
  height: 44rpx;
  margin-right: 10rpx;
  flex: none;
}
.k_tips .tips_item .item_text.data-v-a0119522 {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}
.ai-assistant-btn.data-v-a0119522 {
  position: fixed;
  right: 20rpx;
  bottom: 200rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.15);
  background: #fff;
  border-radius: 46rpx;
  width: 200rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000000;
  font-size: 26rpx;
  z-index: 999;
}
.ai-assistant-btn .ai-icon.data-v-a0119522 {
  width: 60rpx;
  height: 60rpx;
  margin-right: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.ai-assistant-btn .ai-icon image.data-v-a0119522 {
  width: 100%;
  height: 100%;
}
.patient-filter-scroll.data-v-a0119522 {
  background: #fff;
  padding: 24rpx 0;
  margin-bottom: 20rpx;
  white-space: nowrap;
}
.patient-filter.data-v-a0119522 {
  display: inline-flex;
  padding: 0 30rpx;
}
.patient-item.data-v-a0119522 {
  padding: 12rpx 30rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #666;
  background: #f5f5f5;
  border-radius: 32rpx;
  display: inline-block;
}
.patient-item.active.data-v-a0119522 {
  color: #fff;
  background: #836aff;
}