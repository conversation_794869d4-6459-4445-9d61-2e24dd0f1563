<template>
  <view class="nutrition-assessment-container">
    <!-- 顶部操作按钮区域 -->
    <view class="action-buttons-container">
      <view class="action-btn" @click="(!isSendDisabled&&isStreamFinish)&&goToHistory()">
        <image src="/static/ai/ai-1.png" class="btn-icon"></image>
        <text>历史</text>
      </view>
      <view class="action-btn" @click="(!isSendDisabled&&!isStreamFinish)">
        <image src="/static/ai/ai-2.png" class="btn-icon"></image>
        <text>创建新对话</text>
      </view>
      <view class="action-btn" @click="(!isSendDisabled&&!isStreamFinish)&&goToDailyCheckIn()">
        <image src="/static/ai/ai-3.png" class="btn-icon"></image>
        <text>营养评估</text>
      </view>
      <view class="action-btn">
        <image src="/static/ai/ai-3.png" class="btn-icon"></image>
        <text>肠道功能</text>
      </view>
      <view class="action-btn">
        <image src="/static/ai/ai-3.png" class="btn-icon"></image>
        <text>简明膳食</text>
      </view>
    </view>

    <scroll-view scroll-y class="message-container" :scroll-top="scrollTop" :scroll-with-animation="true" :scroll-into-view="scrollToView" ref="messageScroll">
      <view id="message-list" style="min-height: 101%;">
        <!-- AI欢迎消息 - 左侧显示 -->
        <view class="message-box chat-item-ai">
          <view class="avatar">
            <image src="/static/ai-avatar/ai-营养师.png" mode="aspectFill"></image>
          </view>
          <view class="message-content" style="padding:15px;">
            您好! 为了更好地了解您的营养状况，为您提供个性化的健康建议，需要您填写一份营养评估表。这个过程大约需要5分钟，所有信息仅用于患康评估，我们会严格保密，请放心填写。
          </view>
        </view>
        <view class="message-box chat-item-ai">
          <view class="avatar">
            <image src="/static/ai-avatar/ai-营养师.png" mode="aspectFill"></image>
          </view>
          <view class="message-content" style="color: #836AFF;padding:15px;" @click="handleContinue">
            请您填写
          </view>
        </view>

        <!-- 动态消息列表 - 根据sender决定显示位置 -->
        <view v-for="(item,index) in messageList" :key="index" class="message-box" :class="{'chat-item-ai': item.sender === 'ai', 'chat-item-user': item.sender === 'user'}" :id="`msg-${index}`">
          <!-- AI头像 -->
          <view class="avatar" v-if="item.sender === 'ai'">
            <image src="/static/ai-avatar/ai-营养师.png" mode="aspectFill"></image>
          </view>

          <!-- 消息内容 -->
          <view class="message-content" :class="{'user-message': item.sender === 'user'}">
            <!-- 加载动画 - 当内容为空且是AI消息时显示 -->
            <view v-if="item.sender === 'ai' && item.content === ''" class="loading-dots">
              <view class="dot"></view>
              <view class="dot"></view>
              <view class="dot"></view>
            </view>
            <template v-else>
              <view v-html="compiledMarkdown(item.content)" @click="handleClick(item)"></view>
            </template>
          </view>

          <!-- 用户头像 -->
          <view class="avatar" v-if="item.sender === 'user'">
            <image src="/static/ai/patient.png" mode="aspectFill"></image>
          </view>
        </view>
        <!-- 用来滚动到底部的空元素标记 -->
        <view id="msg-bottom" style="height: 5px;"></view>
      </view>
    </scroll-view>

    <!-- 底部输入区域 -->
    <view class="chat-footer" v-if="!isFinish">
      <view>
        <!-- 输入区域 -->
        <view class="input-area">
          <view class="voice-btn">
            <u-icon name="mic" color="#874ff0" size="28"></u-icon>
          </view>
          <input type="text" v-model="inputMessage" :disabled="isSendDisabled||!isStreamFinish" placeholder="请输入您的问题" confirm-type="send" @confirm="sendMessage" />
          <view class="send-btn" @click="sendMessage">
            <u-icon name="arrow-right" color="#fff" size="22"></u-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 患者选择器浮层 -->
    <view class="patient-selector-overlay" v-if="showPatientSelector">
      <view class="patient-selector">
        <view class="selector-title">选择就诊人</view>
        <scroll-view scroll-y class="patient-list-container">
          <view class="patient-list">
            <view v-for="(patient, index) in patientList" :key="index" class="patient-item" @click="selectPatient(patient)">
              <text>{{patient.patientNameStr || patient.patientName}}</text>
            </view>
            <view class="patient-item add-new" @click="goToAddPatient">
              <text>添加新就诊人</text>
            </view>
          </view>
        </scroll-view>
        <view class="close-btn" @click="showPatientSelector = false">关闭</view>
      </view>
    </view>
  </view>
</template>

<script>
import {
  getPatientList,
  checkFirstFill,
  getReportByPatientId,
} from '@/api/user.js'
import {
  createSession,
  saveUserMessage,
  getAiCompletions,
  getChatList
} from "@/api/qcAi.js";
import { compiledMarkdown } from '@/utils/compiled-markDown.js';

export default {
  data () {
    return {
      compiledMarkdown,
      sendId: '',
      flag: '',
      docId: '',
      projectId: '',
      didOnlyId: '',
      showPatientSelector: false, // 控制显示患者选择器
      patientList: [], // 患者列表
      selectedPatient: null, // 已选患者
      userId: uni.getStorageSync('userId'), // 用户ID
      inputMessage: '', // 用户输入的消息
      isSendDisabled: true, // 是否禁用发送按钮
      isFinish: false, // 是否结束对话
      sessionId: null, // 会话ID
      aiResponseId: null, // 当前AI响应的ID
      scrollTop: 0, // 滚动位置
      scrollToView: 'msg-bottom', // 滚动到视图的ID
    }
  },
  computed: {
    streamContent () {
      return this.$store.state.ai.streamContent;
    },
    messageList () {
      return this.$store.state.ai.messageList;
    },
    isStreamFinish () {
      return this.$store.state.ai.isFinish;
    }
  },
  watch: {
    // 监听消息列表变化，自动滚动到底部
    messageList: {
      handler () {
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      },
      deep: true
    }
  },
  mounted () {
    // 页面加载完成后滚动到底部
    this.$nextTick(() => {
      this.scrollToBottom();
    });
  },
  onLoad (options) {
    this.projectId = options.projectId || '18';
    this.docId = options.docId || 'e090016c7d4842b4a29afe95d58ee359';
    this.didOnlyId = options.didOnlyId || '82ce0d37d66a4f968592f25d0548e2cf';
    if (this.projectId === '18') {
      this.flag = 'qc';
    }
    // 加载患者列表
    this.getPatientList();
    // 创建新会话
    this.startNewConversation();
  },
  onShow () {
    // 加载患者列表
    this.getPatientList();
  },
  methods: {
    handleClick (item) {
      if (item.content === '点击购药') {
        uni.navigateTo({
          url: `/pages/shop/scan?flag=${this.flag}&docId=${this.docId}&projectId=${this.projectId}&didOnlyId=${this.didOnlyId}`,
        })
        return
      }
    },
    // 滚动到底部的方法
    scrollToBottom () {
      // 更新滚动位置，触发scroll-view的滚动
      this.scrollToView = '';
      this.$nextTick(() => {
        this.scrollToView = 'msg-bottom';

        // 获取scroll-view的高度并设置scrollTop
        const query = uni.createSelectorQuery().in(this);
        query.select('#message-list').boundingClientRect(data => {
          if (data) {
            this.scrollTop = data.height + 50;
          }
        }).exec();
      });
    },
    handleBack () {
      uni.navigateBack();
    },
    handleContinue () {
      if (this.isStreamFinish) {
        // 显示患者选择器
        this.showPatientSelector = true;
      }
    },
    // 获取就诊人列表
    async getPatientList () {
      let userId = uni.getStorageSync("userId");
      let { data } = await getPatientList({
        userId,
      });
      if (!data) {
        this.patientList = [];
        return;
      }

      this.patientList = (data || []).map((v) => {
        return {
          ...v,
          patientNameStr: `${v.patientName}(${v.sex}-${v.age})`,
        };
      });
    },
    // 选择患者
    async selectPatient (patient) {
      try {
        // 校验患者是否已填过首次问卷（使用原有逻辑）
        let { data } = await checkFirstFill({
          userId: uni.getStorageSync('userId'),
          patientId: patient.patientId,
          projectId: this.projectId,
          didOnlyId: this.didOnlyId,
        });

        uni.hideLoading();

        if (data) {
          const { data: questionnaireData } = await getReportByPatientId({
            patientId: patient.patientId
          });
          if(!questionnaireData){
            this.selectedPatient = patient;
            this.showPatientSelector = false;

            // 跳转到表单页
            uni.navigateTo({
              url: `/pages/scanCode/nutritionForm?sendId=${this.sendId}&projectId=${this.projectId}&flag=${this.flag}&docId=${this.docId}&didOnlyId=${this.didOnlyId}&patientId=${patient.patientId}`
            });
            // uni.showToast({
            //   title: '报告生成中，请稍等',
            //   icon: 'none',
            // });
            return
          }
          if (questionnaireData&&questionnaireData.isGenerate==0) {
            uni.showToast({
              title: '报告生成中，请稍等',
              icon: 'none',
            });
            return
          }
          // 如果已填写问卷并且报告已生成
          if (questionnaireData&&questionnaireData.isGenerate) {
            // 获取会话ID
            const sessionId = questionnaireData.sessionId;

            if (sessionId) {
              try {
                // 加载该会话的消息历史
                let { data: messageHistoryData } = await getChatList({
                  sessionId: sessionId,
                });
                messageHistoryData=messageHistoryData.filter((item,index)=>index>1)
                if (messageHistoryData && messageHistoryData.length > 0) {
                  // 清空现有消息
                  this.$store.dispatch('ai/initMessageList');

                  // 将后端返回的消息按照时间顺序处理
                  messageHistoryData.forEach((msg) => {
                    const messageItem = {
                      content: msg.textContent,
                      sender: msg.senderObjectType === '1' ? 'ai' : 'user', // 1: AI消息，2: 用户消息
                      timestamp: new Date(msg.createTime).toLocaleString(),
                    };

                    // 添加到当前会话
                    this.$store.commit('ai/ADD_MESSAGE_LIST', messageItem);
                  });
                  const loadingMsg = {
                    content: '点击购药',
                    sender: 'ai',
                    timestamp: new Date().toLocaleString(),
                  };
                  this.$store.commit('ai/ADD_MESSAGE_LIST', loadingMsg);
                  // 设置会话ID
                  this.sessionId = sessionId;
                  this.selectedPatient = patient;
                  this.showPatientSelector = false;
                  uni.hideLoading();
                  // 滚动到底部
                  this.$nextTick(() => {
                    this.scrollToBottom();
                  });
                  return;
                }
              } catch (error) {
                console.error('加载历史消息失败:', error);
              }
            }
          }
          // this.showPatientSelector = false;
          // uni.showToast({
          //   title: '该就诊人已填写过问卷，即将跳转',
          //   icon: 'none',
          //   duration: 1500,
          // });
          // setTimeout(() => {
          //   uni.setStorageSync('nutritionFormId', data);
          //   uni.navigateTo({
          //     url: `/pages/shop/scan?flag=${this.flag}&docId=${this.docId}&projectId=${this.projectId}&didOnlyId=${this.didOnlyId}`,
          //   });
          // }, 1000);
          return;
        }

        // 如果未填过，继续填写表单
        this.selectedPatient = patient;
        this.showPatientSelector = false;

        // 跳转到表单页
        uni.navigateTo({
          url: `/pages/scanCode/nutritionForm?sendId=${this.sendId}&projectId=${this.projectId}&flag=${this.flag}&docId=${this.docId}&didOnlyId=${this.didOnlyId}&patientId=${patient.patientId}`
        });

      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '检查失败，请重试',
          icon: 'none'
        });
        console.error('检查失败:', error);
      }
    },
    // 跳转到添加患者页面
    goToAddPatient () {
      uni.navigateTo({
        url: `/pages/personalCenter/patientManage/addPatient/index`
      });
    },
    // 以下是从chat.vue复制的方法
    goToHistory () {
      // 先return
      uni.navigateTo({
        url: '/pages/qcAi/history'
      });
    },
    async startNewConversation () {
      // 清空当前聊天
      // this.isSendDisabled = false;
      this.selectedPatient = null;
      this.isFinish = false;
      this.$store.dispatch('ai/initAi')
      try {
        // 创建新会话
        const { data } = await createSession({
          userId: this.userId,
          sessionType: 4
        });

        if (data) {
          this.sessionId = data;
          this.$store.commit('ai/SET_MINE_SYSTEM_SESSION_ID', data);
        } else {
          uni.showToast({
            title: '创建会话失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('创建会话异常:', error);
        uni.showToast({
          title: '创建会话失败，请稍后再试',
          icon: 'none'
        });
      }
    },
    goToDailyCheckIn () {
      return
      uni.navigateTo({
        url: `/pages/scanCode/nutritionForm?flag=ai&projectId=18`
      });
    },
    formatTime () {
      const now = new Date();
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    },
    // 添加字符逐个显示的动画效果
    async receiveAiMessage (content, index) {
      // 使用store中的动画机制逐字显示内容
      for (let i = 0; i < content.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 20));
        this.$store.commit('ai/ADD_CHAR_TO_MESSAGE', {
          nodeId: index,
          char: content[i]
        });
        // 10次调一次
        if (i % 10 === 0) {
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        }
      }

      // 消息完成后再次滚动到底部确保显示完整
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },
    // 创建AI消息占位，显示加载动画
    addAiLoadingMessage () {
      const loadingMsg = {
        content: '',
        sender: 'ai',
        timestamp: new Date().toLocaleString(),
        node_id: this.messageList.length,
        message_id: this.sessionId,
        index: this.messageList.length
      };

      this.aiResponseId = loadingMsg.index;
      this.$store.commit('ai/ADD_MESSAGE_LIST', loadingMsg);

      // 添加消息后滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();
      });

      return this.aiResponseId;
    },
    async sendMessage () {
      if (!this.inputMessage.trim()) return;
      if (this.isSendDisabled) return;
      this.isSendDisabled = true;

      const userMessage = this.inputMessage;
      this.inputMessage = '';

      try {
        // 添加用户消息到UI
        this.$store.commit('ai/ADD_MESSAGE_LIST', {
          content: userMessage,
          sender: 'user',
          timestamp: new Date().toLocaleString(),
        });

        // 添加用户消息后滚动到底部
        this.$nextTick(() => {
          this.scrollToBottom();
        });

        // 保存用户消息到服务器
        await saveUserMessage({
          sessionId: this.sessionId,
          content: userMessage,
          userId: this.userId,
          patientId: this.selectedPatient ? this.selectedPatient.patientId : '',
          isAi: false,
          role: "user"
        });

        // 添加AI加载动画占位消息
        this.addAiLoadingMessage();
        this.$nextTick(() => {
          this.scrollToBottom();
        });
        // 构建历史消息数组，用于AI模型上下文
        let messageHistory = [];
        // 获取最近的消息作为上下文
        const recentMessages = this.messageList.slice(-10);
        recentMessages.forEach(msg => {
          messageHistory.push({
            role: msg.sender === 'ai' ? "assistant" : "user",
            content: msg.content
          });
        });

        // 调用AI接口获取回答
        const response = await getAiCompletions({
          messages: messageHistory,
          model: 'c1bb8c359b8044ad8160a52fbf712494',
          content: userMessage // 最新消息
        });
        console.log(response, "===========AI回答接口返回结果");

        let aiReply = "";
        if (response.data && response.data.choices && response.data.choices.length > 0) {
          // OpenAI风格的返回格式
          aiReply = response.data.choices[0].message.content;
        } else if (response.data && response.data.aiReply) {
          // 自定义返回格式
          aiReply = response.data.aiReply;
        } else {
          aiReply = "感谢您的问题，我们的营养师正在为您准备答案，请您先填写一份营养评估表，以便我们更好地了解您的情况";
        }

        // 保存AI回复到服务器
        await saveUserMessage({
          sessionId: this.sessionId,
          content: aiReply,
          userId: this.userId,
          patientId: this.selectedPatient ? this.selectedPatient.patientId : '',
          isAi: true,
          role: "assistant"
        });

        // 更新加载中的消息内容，显示AI回复
        await this.receiveAiMessage(aiReply, this.aiResponseId);
        this.isSendDisabled = false;
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      } catch (error) {
        console.error('发送消息异常:', error);
        this.isSendDisabled = false;

        // 出现异常时也显示一个默认回复
        const defaultReply = "非常抱歉，系统暂时无法回应，请您稍后再试或者填写营养评估表以获得更准确的建议";

        // 保存默认回复到服务器
        await saveUserMessage({
          sessionId: this.sessionId,
          content: defaultReply,
          userId: this.userId,
          patientId: this.selectedPatient ? this.selectedPatient.patientId : '',
          isAi: true,
          role: "assistant"
        });

        // 更新加载中的消息内容，显示默认回复
        this.$store.commit('ai/ADD_CHAR_TO_MESSAGE', {
          nodeId: this.aiResponseId,
          char: defaultReply
        });

        // 错误回复后滚动到底部
        this.scrollToBottom();
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.nutrition-assessment-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}


/* 顶部操作按钮样式 */
.action-buttons-container {
  display: flex;
  justify-content: space-around;
  background-color: #fff;
  padding: 10rpx 0 20rpx;
  border-bottom: 1rpx solid #eee;

  .action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 25%;

    .btn-icon {
      width: 70rpx;
      height: 70rpx;
      margin-bottom: 10rpx;
      padding: 12rpx;
      box-sizing: border-box;
    }

    text {
      font-size: 24rpx;
      color: #666;
      text-align: center;
      white-space: normal;
      width: 100%;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  background-color: #fff;
  border-bottom: 1px solid #eaeaea;
  padding: 0 15px;
  position: relative;
  
  .back-icon, .more-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .icon {
      font-size: 24px;
      color: #333;
    }
  }
  
  .title {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    font-size: 18px;
    font-weight: 500;
    color: #333;
  }
}

.message-container {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.message-box {
  display: flex;
  margin-bottom: 20px;
  
  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    image {
      width: 40px;
      height: 40px;
    }
  }
  
  .message-content {
    flex: 1;
    max-width: 70%;
    background-color: #fff;
    border-radius: 10px;
    padding: 0px 15px;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    
    &.user-message {
      background: linear-gradient(135deg, rgba(81, 106, 251, 1) 0%, rgba(133, 155, 255, 1) 99.97%);
      color: #fff;
      text-align: left;
    }
    
    /* 加载动画样式 */
    .loading-dots {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 15px;
      
      .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #ccc;
        margin: 0 3px;
        animation: bounce 1.4s infinite ease-in-out both;
        
        &:nth-child(1) {
          animation-delay: -0.32s;
        }
        
        &:nth-child(2) {
          animation-delay: -0.16s;
        }
      }
    }
  }
}

/* 左侧AI消息样式 */
.chat-item-ai {
  justify-content: flex-start;
}

/* 右侧用户消息样式 */
.chat-item-user {
  justify-content: flex-end;
  
  .avatar {
    margin-right: 0;
    margin-left: 10px;
  }
  
  .message-content {
    text-align: left;
    margin-right: 0;
  }
}

/* 加载动画关键帧 */
@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1.0);
  }
}

.continue-btn {
  height: 40px;
  line-height: 40px;
  background-color: #fff;
  color: #836AFF;
  font-size: 16px;
  border-radius: 20px;
  text-align: center;
  margin-left: 50px;
  border: 1px solid #eaeaea;
  
  &:active {
    opacity: 0.8;
  }
}

/* 底部输入区域样式 */
.chat-footer {
  background-color: #f9f6ff;
  border-top: 1rpx solid #eeeeee;
  padding: 10rpx 20rpx 20rpx;

  .input-area {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    input {
      flex: 1;
      height: 80rpx;
      background-color: #fff;
      border-radius: 40rpx;
      padding: 0 30rpx;
      font-size: 28rpx;
      border: 1px solid #e6e6fa;
    }

    .send-btn {
      width: 70rpx;
      height: 70rpx;
      background: linear-gradient(135deg, rgba(81, 106, 251, 1) 0%, rgba(133, 155, 255, 1) 99.97%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 20rpx;
    }
  }

  /* 患者选择器样式 */
  .patient-selector {
    margin: 10rpx 0 20rpx;

    .selector-title {
      color: #333;
      font-size: 32rpx;
      font-weight: bold;
      padding: 20rpx 0;
      border-bottom: 1px solid #ececec;
      margin-bottom: 20rpx;
    }

    .patient-list-container {
      height: 300px;
      overflow: hidden;
    }

    .patient-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      padding: 0 5px;
      
      .patient-item {
        width: 48%;
        background-color: #f9f6ff;
        border-radius: 8px;
        padding: 15px 10px;
        margin-bottom: 10px;
        text-align: center;
        
        text {
          font-size: 14px;
          color: #333;
        }
        
        &.add-new {
          background-color: #f9f9f9;
          border: 1px dashed #ccc;
          
          text {
            color: #666;
          }
        }
        
        &:active {
          opacity: 0.8;
        }
      }
    }
    
    .close-btn {
      margin-top: 15px;
      text-align: center;
      padding: 10px;
      background-color: #836AFF;
      color: #fff;
      border-radius: 20px;
      font-size: 14px;
    }
  }
}

/* 浮层样式 */
.patient-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.patient-selector {
  width: 80%;
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
  
  .selector-title {
    color: #333;
    font-size: 16px;
    font-weight: bold;
    padding: 10px 0;
    border-bottom: 1px solid #ececec;
    margin-bottom: 15px;
    text-align: center;
  }
  
  .patient-list-container {
    height: 300px;
    overflow: hidden;
  }
  
  .patient-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 0 5px;
    
    .patient-item {
      width: 48%;
      background-color: #f9f6ff;
      border-radius: 8px;
      padding: 15px 10px;
      margin-bottom: 10px;
      text-align: center;
      
      text {
        font-size: 14px;
        color: #333;
      }
      
      &.add-new {
        background-color: #f9f9f9;
        border: 1px dashed #ccc;
        
        text {
          color: #666;
        }
      }
      
      &:active {
        opacity: 0.8;
      }
    }
  }
  
  .close-btn {
    margin-top: 15px;
    text-align: center;
    padding: 10px;
    background-color: #836AFF;
    color: #fff;
    border-radius: 20px;
    font-size: 14px;
  }
}
.voice-btn {
      width: 70rpx;
      height: 70rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 10rpx;
      background-color: #fff;
      border-radius: 50%;
      border: 1px solid #e6e6fa;
    }

</style> 