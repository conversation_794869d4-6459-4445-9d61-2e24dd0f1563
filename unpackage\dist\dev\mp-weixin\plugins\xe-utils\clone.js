"use strict";
const plugins_xeUtils_staticObjectToString = require("./staticObjectToString.js");
const plugins_xeUtils_objectEach = require("./objectEach.js");
const plugins_xeUtils_arrayEach = require("./arrayEach.js");
function getCativeCtor(val, args) {
  var Ctor = val.__proto__.constructor;
  return args ? new Ctor(args) : new Ctor();
}
function handleValueClone(item, isDeep) {
  return isDeep ? copyValue(item, isDeep) : item;
}
function copyValue(val, isDeep) {
  if (val) {
    switch (plugins_xeUtils_staticObjectToString.objectToString.call(val)) {
      case "[object Object]": {
        var restObj = Object.create(val.__proto__);
        plugins_xeUtils_objectEach.objectEach(val, function(item, key) {
          restObj[key] = handleValueClone(item, isDeep);
        });
        return restObj;
      }
      case "[object Date]":
      case "[object RegExp]": {
        return getCativeCtor(val, val.valueOf());
      }
      case "[object Array]":
      case "[object Arguments]": {
        var restArr = [];
        plugins_xeUtils_arrayEach.arrayEach(val, function(item) {
          restArr.push(handleValueClone(item, isDeep));
        });
        return restArr;
      }
      case "[object Set]": {
        var restSet = getCativeCtor(val);
        restSet.forEach(function(item) {
          restSet.add(handleValueClone(item, isDeep));
        });
        return restSet;
      }
      case "[object Map]": {
        var restMap = getCativeCtor(val);
        restMap.forEach(function(item, key) {
          restMap.set(key, handleValueClone(item, isDeep));
        });
        return restMap;
      }
    }
  }
  return val;
}
function clone(obj, deep) {
  if (obj) {
    return copyValue(obj, deep);
  }
  return obj;
}
exports.clone = clone;
