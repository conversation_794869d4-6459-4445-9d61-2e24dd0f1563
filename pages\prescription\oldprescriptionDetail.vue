<template>
  <view class="prescriptionDetail">
    <view class="cfDetailView">
      <block v-if="prescriptionDetail">
        <!-- 地址 -->
        <view class="address" @click="navigatorFun" v-if="status == 0">
          <!-- 快递 -->
          <view
            class="address-ps"
            v-if="this.address && this.address.deliveryType == 1"
          >
            <view class="address-distribution">
              <text
                class="address-distribution-default"
                v-if="this.address.isDefault == '1'"
                >默认</text
              >
              <text
                class="address-distribution-icon"
                v-if="this.address.lableName"
                >{{ this.address.lableName }}</text
              >
              <text class="address-distribution-content"
                >{{ this.address.addressArea
                }}{{ this.address.addressDetail }}</text
              >
            </view>
            <view class="address-distribution-contact">
              <view class="address-distribution-contact-l">
                <!-- 姓名 -->
                <text>{{ this.address.deliveryName }}</text>
                <!-- 手机号 -->
                <text>{{ setPhone(this.address.telNo) }}</text>
              </view>
              <uni-icons
                type="arrowright"
                color="#333"
                size="22"
                v-if="status == 0"
              ></uni-icons>
            </view>
          </view>

          <!-- 自提 -->
          <view
            class="address-zt"
            v-if="this.address && this.address.deliveryType == 2"
          >
            <view class="address-self">
              <UniImage v-if="address.drugstoreImg" :src="address.drugstoreImg" />
              <image
                v-else
                src="../../static/images/Pharmacy-default.png"
                mode=""
              ></image>
              <view class="address-self-text">
                <view class="address-self-text-top">
                  <text class="address-self-text-top-l">{{
                    this.address.drugstoreName
                  }}</text>
                  <text class="address-self-text-top-r">自提</text>
                </view>
                <view class="address-self-text-map">
                  <text>{{ this.address.drugstoreAddress }}</text>
                </view>
                <view class="address-self-text-map">
                  <uni-icons
                    type="arrowright"
                    color="#333"
                    size="22"
                    v-if="status == 0"
                  ></uni-icons>
                </view>
              </view>
            </view>
            <view class="address-self-contact">
              <view class="address-self-btn" @click.stop="call">
                联系药店
              </view>
              <view class="address-self-btn" @click.stop="goMap"> 去药店 </view>
            </view>
          </view>

          <!-- 配送方式 -->
          <view
            class="address-top"
            v-else-if="!this.address || this.address.deliveryType < 1"
          >
            <view class="title"> 配送方式 </view>
            <uni-icons type="arrowright" color="#333" size="22"></uni-icons>
          </view>

          <!-- 提示 -->
          <view class="address-tip">
            <view class="text"> 提示 </view>
            <view class="content">
              <text style="display: inline-block">
                不同的配送方式，不同的药店价格会有差异，药品价格请以实际支付为标准。
              </text>
              <!-- 根据快递费提示 -->
              <text
                style="
                  display: inline-block;
                  margin-top: 10rpx;
                  color: red;
                  font-weight: bold;
                "
                v-if="address && address.deliveryType == 1"
              >
                <block v-if="logistTip">
                  {{ logistTip }}
                </block>
                <block v-else>
                  快递费：因物流将根据药品实际配重与配送距离核算快递费用，如果您选择的是快递方式，需要您在接收药品时向快递公司支付费用。
                </block>
              </text>
            </view>
          </view>
        </view>

        <block v-if="address">
          <!-- 收货地址 -->
          <ADDRESS
            v-if="status > 0 && address.deliveryType == 1"
            :detail="address"
          />

          <!-- 药店地址 -->
          <DRUGSTORE
            v-if="status > 0 && address.deliveryType == 2"
            :detail="address"
          />
        </block>
      </block>

      <!-- 头部 -->
      <view class="pre_head" v-if="prescriptionDetail">
        <view class="head_item">
          <text class="bold"
            >医生名称：{{ prescriptionDetail.proBusinessInfo.docName }}</text
          >
          <text class="bold"
            >医生科室：{{ prescriptionDetail.proBusinessInfo.deptName }}</text
          >
        </view>

        <view class="head_item">
          <text class="bold"
            >医院名称：{{ prescriptionDetail.proBusinessInfo.hosName }}</text
          >
        </view>

        <view class="head_item">
          <text class="bold"
            >药师姓名：{{
              prescriptionDetail.proBusinessInfo.checkUserName
            }}</text
          >
        </view>

        <view class="head_item">
          <text>处方诊断：{{ prescriptionDetail.proBusinessInfo.diags }}</text>
        </view>

        <view class="head_item">
          <text
            >开方时间：{{ prescriptionDetail.proBusinessInfo.addTime }}</text
          >
        </view>

        <view class="head_item">
          <text
            >处方编号：{{
              prescriptionDetail.proBusinessInfo.businessCode
            }}</text
          >
        </view>
      </view>

      <!-- 药品信息 -->
      <block v-if="prescriptionDetail">
        <!-- 开方详情 已支付 或者待支付可见 -->
        <block
          v-if="
            !hideDetail.includes(status) ||
            prescriptionDetail.beforePayVisible == 1
          "
        >
          <view class="label">处方信息</view>
          <view
            class="cfDetail"
            v-for="(item, index) in prescriptionDetail.prescriptions"
            :key="index"
          >
            <view
              class="xyCf"
              v-if="
                item.proPrescriptionMasterVO.prescriptionType == '1' ||
                item.proPrescriptionMasterVO.prescriptionType == '2' ||
                item.proPrescriptionMasterVO.prescriptionType == '4'
              "
            >
              <!-- 西药 -->
              <view class="title">
                <label> Rp </label>

                <text>{{
                  item.proPrescriptionMasterVO.prescriptionTypeName
                }}</text>
              </view>

              <view
                class="checkBoxView"
                v-for="(xyItem, xyIndex) in item.details"
                :key="xyIndex"
              >
                <view class="list_detail">
                  <view class="drugName">
                    <view
                      >{{ xyIndex + 1 }}.{{ xyItem.drugName }}({{
                        xyItem.gg
                      }})</view
                    >
                  </view>
                  <view class="drugInfo">
                    <view>
                      用法：{{ xyItem.dduName || "口服" }}，每次{{
                        xyItem.eachQuan
                      }}{{ xyItem.eachUnit }}，{{ xyItem.ddufName }}，用药{{
                        xyItem.days
                      }}天
                    </view>
                    <view class="price">
                      价格：￥{{ xyItem.price }}
                      <view> x {{ xyItem.quan }} </view>
                    </view>
                    <view v-if="xyItem.memo">说明：{{ xyItem.memo }}</view>
                  </view>
                </view>
              </view>
              <view class="total">
                合计：<text
                  >￥{{ item.proPrescriptionMasterVO.cost.toFixed(2) }}</text
                >
              </view>
            </view>

            <!-- 中草药 -->
            <view
              class="cyCf"
              v-if="item.proPrescriptionMasterVO.prescriptionType == '3'"
            >
              <view class="title">
                <label> Rp </label>
                <text>{{
                  item.proPrescriptionMasterVO.prescriptionTypeName
                }}</text>
              </view>

              <view
                class="cy_item"
                v-for="(xyItem, xyIndex) in item.details"
                :key="xyIndex"
              >
                <view class="drugName">
                  <view
                    >{{ xyIndex + 1 }}.{{ xyItem.drugName }}({{
                      xyItem.gg
                    }})</view
                  >
                </view>
                <view class="drugInfo">
                  <view class="price"
                    >价格：￥{{ xyItem.price }}
                    <text>x{{ xyItem.eachQuan }}</text>
                  </view>
                  <view v-if="xyItem.memo">说明：{{ xyItem.memo }}</view>
                </view>
              </view>

              <view class="useMethods">
                用法：{{
                  item.proPrescriptionMasterVO.dduName || "口服"
                }}，每日{{ item.proPrescriptionMasterVO.rc }}次，每次{{
                  item.proPrescriptionMasterVO.rj
                }}剂，用药{{ item.proPrescriptionMasterVO.days }}天
              </view>

              <view class="djCf">
                共{{ item.proPrescriptionMasterVO.herbalNum }}付
              </view>

              <!-- <view
                class="djCf"
                v-if="item.proPrescriptionMasterVO.isDj == '1'"
              >
                代煎费：￥{{ item.proPrescriptionMasterVO.djCost | toFixed }}
              </view> -->
              <view>
                剂型：{{ item.proPrescriptionMasterVO.herbalFormulations }}
              </view>
              <view class="price-class" v-if="getDjCost(item) != 0">
                <view v-if="status == 0">
                  单付药品的代煎费为￥{{ getDfPrice(item) | toFixed }}，共{{
                    item.proPrescriptionMasterVO.herbalNum
                  }}付，代煎费用共￥{{ getDjCost(item) | toFixed }}
                </view>
                <view v-else>
                  单付药品的代煎费为￥{{
                    item.proPrescriptionMasterVO.dfPrice | toFixed
                  }}，共{{
                    item.proPrescriptionMasterVO.herbalNum
                  }}付，代煎费用共￥{{
                    item.proPrescriptionMasterVO.djCost | toFixed
                  }}
                </view>
              </view>
              <view class="total">
                <view v-if="status == 0">
                  合计：<text
                    >￥{{
                      (item.proPrescriptionMasterVO.cost + getDjCost(item) || 0)
                        | toFixed
                    }}</text
                  >
                </view>
                <view v-else>
                  合计：<text
                    >￥{{
                      (item.proPrescriptionMasterVO.cost || 0) | toFixed
                    }}</text
                  >
                </view>
              </view>
            </view>
          </view>

          <!-- 合计 -->
          <view class="price_count" v-if="status == 0">
            <view>
              合计:
              <text
                >￥{{ shouldMoney + djCostNum || drugPrice | toFixed }}</text
              >
            </view>
            <view>
              优惠: <text>￥{{ discount | toFixed }}</text>
            </view>
            <view>
              实付:
              <text>￥{{ realMoney + djCostNum || drugPrice | toFixed }}</text>
            </view>
          </view>
          <view class="price_count" v-else>
            <view>
              合计: <text>￥{{ shouldMoney || drugPrice | toFixed }}</text>
            </view>
            <view>
              优惠: <text>￥{{ discount | toFixed }}</text>
            </view>
            <view>
              实付:
              <text>￥{{ realMoney || drugPrice | toFixed }}</text>
            </view>
          </view>
        </block>
      </block>

      <!-- 费用列表 未支付 且不可见 -->
      <view
        class="price_list"
        v-if="
          prescriptionDetail &&
          hideDetail.includes(status) &&
          prescriptionDetail.beforePayVisible == 0
        "
      >
        <!-- 标题 -->
        <view class="label">处方信息</view>
        <!-- 西药方 -->
        <view class="list_item" v-if="x.length">
          <!-- 数量价位 -->
          <view class="item">
            <view class="item_label">{{ x_name }}</view>
            <view class="item_num">共{{ x.length || 0 }}种药品</view>
            <view class="item_price">￥{{ getPriceNum(x) | toFixed }}</view>
          </view>
        </view>
        <!-- 食品方 -->
        <view class="list_item" v-if="s.length">
          <!-- 数量价位 -->
          <view class="item">
            <view class="item_label">{{ s_name }}</view>
            <view class="item_num">共{{ s.length || 0 }}种药品</view>
            <view class="item_price">￥{{ getPriceNum(s) | toFixed }}</view>
          </view>
        </view>
        <!-- 中药方 -->
        <view class="list_item" v-if="z.length">
          <!-- 数量价位 -->
          <view class="item">
            <view class="item_label">{{ z_name }}</view>
            <view class="item_num">共{{ z.length }}种药品</view>
            <view class="item_price">￥{{ getZyPrice(z) | toFixed }}</view>
          </view>
          <!-- 用法用量 -->
          <view class="item_cont" v-if="false">
            <view class="item_label">用法</view>
            <view class="item_detail">{{ zy_use }}</view>
          </view>
        </view>
        <view class="price-class" v-if="z.length && djCostNum != 0">
          共{{ herbalNum }}付，代煎费用共￥{{ djCostNum | toFixed }}
        </view>
        <!-- 小计 -->
        <view class="list_count" v-if="false"
          >共{{ x.length + z.length + s.length }}种药品</view
        >

        <!-- 合计（04-19改动加代煎费） -->
        <view class="price_count" v-if="status == 0">
          <view>
            合计:
            <text>￥{{ shouldMoney + djCostNum || drugPrice | toFixed }}</text>
          </view>
          <view>
            优惠: <text>￥{{ discount | toFixed }}</text>
          </view>
          <view>
            实付:
            <text>￥{{ realMoney + djCostNum || drugPrice | toFixed }}</text>
          </view>
        </view>
        <view class="price_count" v-else>
          <view>
            合计: <text>￥{{ shouldMoney || drugPrice | toFixed }}</text>
          </view>
          <view>
            优惠: <text>￥{{ discount | toFixed }}</text>
          </view>
          <view>
            实付:
            <text>￥{{ realMoney || drugPrice | toFixed }}</text>
          </view>
        </view>
      </view>

      <!-- 快递选择 -->
      <view
        class="logistics"
        v-if="this.address && this.address.deliveryType == 1"
      >
        <view class="logist_title" @click="showTips">
          请选择物流方式<uni-icons
            type="help"
            color="#333"
            size="16"
          ></uni-icons>
        </view>

        <block v-for="(item, index) in LogisticsCostList" :key="index">
          <view class="item" @click="setLogistType(index)" :key="index">
            <text class="name">{{ item.logisticsName }}</text>
            <view class="right">
              <text>{{
                item.logisticsCost == 0 ? "免费" : "￥" + item.logisticsCost
              }}</text>
              <image
                src="/static/shop/sele_act.png"
                v-if="logistIndex == index"
              ></image>
              <image src="/static/shop/sele.png" v-else></image>
            </view>
          </view>
        </block>
      </view>

      <!-- 代煎费选择 存在中药 并且不是已支付状态 -->
      <!-- <view
        class="address sele"
        v-if="
          z.length && hideDetail.includes(status) && status != 10 && isCandj
        "
      >
        <view class="address-top" @click="seleIsDj">
          <view class="title"> 是否代煎 </view>
          <view class="right">
            <text class="text" :class="{ black: is_dj > -1 }">
              <block v-if="is_dj == -1">请选择</block>
              <block v-if="is_dj == 0">是</block>
              <block v-if="is_dj == 1">否</block>
            </text>
            <uni-icons
              type="arrowright"
              color="#333"
              size="22"
              v-if="status == 0"
            ></uni-icons>
          </view>
        </view>
        <view class="address-tip" v-if="status == 0 || is_dj == 0">
          <view class="text"> 提示 </view>
          <view class="content">
            <text style="display: inline-block" v-if="status == 0">
              请选择是否代煎，代煎会产生相应费用，请以实际支付价格为准
            </text> -->
      <!-- 代煎费提示 -->
      <!--       <text
              style="
                display: inline-block;
                margin-top: 10rpx;
                color: red;
                font-weight: bold;
              "
              v-if="is_dj == 0"
            >
              单付药品的代煎费为￥{{ dfPrice | toFixed }}，共{{ herbalNum }}付，
              <block v-if="clPrice">
                代煎材料费为￥{{ (clPrice * cl_num) | toFixed }},
              </block>
              代煎费用共￥{{ djPrice | toFixed }}
            </text>
          </view>
        </view>
      </view> -->

      <!-- 审核中 -->
      <view class="empty" v-if="status == 10">
        <text>注：</text>您的处方正在审核中，请耐心等待
      </view>
      <!-- 付款类型 -->
      <view
        class="logistics"
        v-if="this.address && this.address.isDeposit == 1"
      >
        <block>
          <view class="item paytype" @click="status == 0 && seledepositType(1)">
            <text class="name">{{ Deposit.payText }}</text>
            <view class="right">
              <text
                >￥{{
                  Number(
                    (Number(total) * 100 + Number(courierFees) * 100) / 100
                  ) | toFixed
                }}</text
              >
              <image
                src="/static/shop/sele_act.png"
                v-if="DepositType === 1"
              ></image>
              <image src="/static/shop/sele.png" v-else></image>
            </view>
          </view>
          <view class="item paytype" @click="status == 0 && seledepositType(2)">
            <text class="name">{{ Deposit.DepositText }}</text>
            <view class="right">
              <text>首付款￥{{ Deposit.downPayment | toFixed }}</text>
              <text>尾款￥{{ balancePayment | toFixed }}</text>
              <image
                src="/static/shop/sele_act.png"
                v-if="DepositType === 2"
              ></image>
              <image src="/static/shop/sele.png" v-else></image>
            </view>
          </view>
        </block>
      </view>
    </view>

    <block v-if="prescriptionDetail">
      <!-- 底部 -->
      <view class="footer" v-if="status < 9">
        <!-- 中西食 -->
        <view>
          合计：<text v-if="status == 0"
            >￥{{
              DepositType === 2
                ? Number(this.DepositTotal)
                : Number(
                    (Number(total) * 100 + Number(courierFees) * 100) / 100
                  ) | toFixed
            }}</text
          >

          <text v-else
            >￥{{
              DepositType === 2
                ? Number(this.DepositTotal)
                : ((Number(cost) * 100 + Number(courierFees) * 100) / 100)
                  | toFixed
            }}</text
          >
        </view>
        <view
          class="btns"
          v-if="showType == 1 && ['0', '7', '8'].includes(this.status)"
        >
          <view
            class="btn"
            @click="
              isOpenDj(
                prescriptionDetail.prescriptions,
                prescriptionDetail.beforePayVisible
              )
            "
            >去支付</view
          >
          <view class="btn" @click="getPrescriptionDetail(1)">好友付</view>
        </view>
      </view>

      <!-- 失效处方 -->
      <view class="footer" v-if="status == 9">
        <view></view>
        <view class="btns">
          <view class="btn sx">处方已失效</view>
        </view>
      </view>
    </block>

    <!-- 弹框 -->
    <uni-popup ref="popup" type="bottom">
      <view class="pop">
        <view class="pop_title">
          <text>{{ logisticsTip.tipsTitle }}</text>
          <uni-icons type="closeempty" @click="hideTip"></uni-icons>
        </view>
        <view class="pop_text">
          {{ logisticsTip.tipsContent }}
        </view>
      </view>
    </uni-popup>

    <!-- 代煎费是否正确弹框-->
    <uni-popup ref="djPopup" type="center">
      <view class="pop_box">
        <view class="pop_title">
          <text>请确认</text>
        </view>
        <view
          v-for="(item, index) in prescriptionDetail.prescriptions"
          :key="index"
        >
          <!-- 中草药 -->
          <view
            class="cyCf"
            v-if="item.proPrescriptionMasterVO.prescriptionType == '3'"
          >
            <view class="title">
              <label> Rp </label>
            </view>
            <view class="cy_item_pop">
              <view
                class="cy_item_pop_name"
                v-for="(xyItem, xyIndex) in item.details"
                :key="xyIndex"
              >
                <view class="drugName">
                  {{ xyItem.drugName }}x{{ xyItem.eachQuan }}({{ xyItem.gg }})
                  <span
                    v-if="xyIndex >= item.details.length - 1 ? false : true"
                  >
                    ，
                  </span>
                </view>
              </view>
            </view>
            <view class="drugName">
              剂型为：{{ item.proPrescriptionMasterVO.herbalFormulations }}
            </view>
            <view class="price-class" v-if="getDjCost(item) != 0">
              <view v-if="status == 0">
                单付药品的代煎费为￥{{ getDfPrice(item) | toFixed }}，共{{
                  item.proPrescriptionMasterVO.herbalNum
                }}付，代煎费用共￥{{ getDjCost(item) | toFixed }}
              </view>
              <view v-else>
                单付药品的代煎费为￥{{
                  item.proPrescriptionMasterVO.dfPrice | toFixed
                }}，共{{
                  item.proPrescriptionMasterVO.herbalNum
                }}付，代煎费用共￥{{
                  item.proPrescriptionMasterVO.djCost | toFixed
                }}
              </view>
            </view>
          </view>
        </view>
        <!-- 按钮-->
        <view class="btn-box">
          <view class="btn-left" @click="cancel"> 取消 </view>
          <view class="btn-right" @click="submit"> 确认 </view>
        </view>
      </view>
    </uni-popup>
    <!-- 代煎费是否正确弹框(不可见)-->
    <uni-popup ref="djPopupInvisible" type="center">
      <view class="pop_box">
        <view class="pop_title">
          <text>请确认</text>
        </view>
        <view>
          您当前购买的草药方中含<span class="price-class">{{ z.length }}</span
          >味药，共<span class="price-class">{{ herbalNum }}</span
          >付<span v-if="djCostNum != 0"
            >，代煎费用共<span class="price-class"
              >￥{{ djCostNum | toFixed }}</span
            ></span
          >
        </view>
        <!-- 按钮-->
        <view class="btn-box">
          <view class="btn-left" @click="cancelInvisible"> 取消 </view>
          <view class="btn-right" @click="submit"> 确认 </view>
        </view>
      </view>
    </uni-popup>
    <!-- 引导 -->
    <Tips v-show="showTip" @click="showTip = false" />
  </view>
</template>

<script>
let payTimer;

import payment from "@/mixins/wx";

import { findAddressByUserId } from "@/api/address.js";

import {
  getPatientPrescriptionBusinessOrderInfo,
  getSomePriceSameByDrugStorage,
  savePendingReceiptAndToPay,
  getLogisticsCostList,
  getDrugStorageStore,
} from "@/api/cf";

import {
  getSysPlatformConfigByKeyList,
  queryRegisterPayStatus,
  findDrugStoreDetail,
} from "@/api/base.js";

const mapKey = require("@/common/request/config.js").mapKey;

import uniNumberBox from "@/components/uni-number-box/uni-number-box.vue";

import * as myJsTools from "@/common/js/myJsTools.js";
import { wxPay } from "@/common/js/pay.js";

import tkiQrcode from "@/components/tki-qrcode/tki-qrcode.vue";

import DRUGSTORE from "./com/drugStore.vue";
import ADDRESS from "./com/address.vue";

import { Toast } from "@/common/js/pay.js";

let num = 3;

let timer;

export default {
  name: "Prescription",
  mixins: [payment],
  components: {
    uniNumberBox,
    tkiQrcode,
    DRUGSTORE,
    ADDRESS,
  },
  computed: {
    DepositTotal() {
      //预付款
      let unm;
      if (this.Deposit.downPayment) {
        unm = (Number(this.Deposit.downPayment) * 100) / 100;
      } else {
        unm = 0;
      }
      return unm;
    },

    balancePayment() {
      //尾款
      let balancePayment;
      // if (this.Deposit.downPayment) {
      //   balancePayment =
      //     (Number(this.total) * 100 - Number(this.Deposit.downPayment) * 100) /
      //     100;
      // } else {
      balancePayment =
        (Number(this.total) * 100 +
          Number(this.courierFees) * 100 -
          Number(this.Deposit.downPayment) * 100) /
        100;
      // }
      this.Deposit.balancePayment = balancePayment;
      return balancePayment;
    },

    total() {
      return (
        //改动
        (Number(this.getPriceNum(this.x, true)) * 100 +
          Number(this.getPriceNum(this.s, true)) * 100 +
          Number(this.getZyPrice(this.z, true)) * 100 +
          Number(this.djCostNum) * 100) /
        100
      );
    },
    // 单独药品价位
    drugPrice() {
      return (
        (Number(this.getPriceNum(this.x, true)) * 100 +
          Number(this.getPriceNum(this.s, true)) * 100 +
          Number(this.getZyPrice(this.z, true)) * 100) /
        100
      );
    },
    // 计算代煎费用
    // djPrice() {
    //   // 单付金额 * 总付数 + 材料费
    //   return (
    //     (Number(this.dfPrice) * 100 * this.herbalNum +
    //       Number(this.clPrice) * 100 * this.cl_num) /
    //     100
    //   );
    // },
  },
  data() {
    return {
      // 不可显示详细药品的状态
      hideDetail: ["0", "6", "7", "8", "9", "10"],
      businessId: "",
      prescriptionDetail: "",
      allObj: "",
      address: null,
      // 中药
      z: [],
      // 西药
      x: [],
      // 食品
      s: [],
      z_name: "",
      x_name: "",
      s_name: "",
      // 是否支持代煎
      isCandj: false,
      // 参与计算的代煎费
      dj_price: 0,
      // 是否代煎
      is_dj: -1,
      // 中药使用方法
      zy_use: "",
      // 中药几副
      herbalNum: 1,
      // 药品快递费
      courierFees: 0,
      // 单付代煎费用
      dfPrice: 0,
      // 代煎材料费
      clPrice: 0,
      // 状态不为0时 直接使用详情价格
      cost: 0,
      // 处方中含有几个草药方
      cl_num: 0,
      // 显示类型
      showType: 1,
      // 物流配置
      logistTip: "",
      // 状态  0待支付(未生成订单)  1待发货  2取药码  3待收货  4已取药  5已收货 6交易失败  7待支付(已生成订单未分割) 8待支付(已生成订单已分割) 9 失效 10 待审核
      status: -1,
      // 物流价位
      logistPrice: {},
      // 默认普通快递
      logistIndex: 0,
      // 物流提示
      logisticsTip: "",
      // 支付方式
      payList: [],
      // 支付主体信息
      payItem: "",
      // 挂号id
      ghId: "",
      // 是否点击
      isNext: false,
      nextClick: true,
      // 是否可选择
      isSele: true,
      realMoney: 0,
      shouldMoney: 0,
      discount: 0,
      // 物流列表
      LogisticsCostList: [],
      // 是否代付
      isHelpPay: 0,
      //定金数据
      Deposit: {
        downPayment: null,
        balancePayment: null,
        payText: "全额支付",
        DepositText: "定金支付",
      },
      //付款类型,默认1全额支付,2定金支付
      DepositType: 1,
      djCost: 0,
      dfPriceNum: 0,
      dosageCodeMap: new Map(), //剂型map
      djCostNum: 0, //代煎费（新加）
    };
  },
  onLoad(option) {
    uni.removeStorageSync("nowAddress");
    this.businessId = option.businessId; // 处方ID
    if (option.type) {
      this.showType = option.type;
    }
    this.getDetail(true);
    this.getLogistTip();
  },
  onUnload() {
    if (timer) clearInterval(timer);
  },
  onShow() {
    if (uni.getStorageSync("nowAddress")) {
      this.isNext = false;
      this.courierFees = 0;
      // 获取处方详情
      this.getDetail();
    }
  },
  methods: {
    //单付代煎费(新加)
    getDfPrice(item) {
      return this.dosageCodeMap.get(
        item.proPrescriptionMasterVO.herbalFormulations
      )
        ? this.dosageCodeMap.get(
            item.proPrescriptionMasterVO.herbalFormulations
          )
        : 0;
    },
    //代煎费用（新加）
    getDjCost(item) {
      return (
        (Number(
          this.dosageCodeMap.get(
            item.proPrescriptionMasterVO.herbalFormulations
          )
            ? this.dosageCodeMap.get(
                item.proPrescriptionMasterVO.herbalFormulations
              )
            : 0
        ) *
          100 *
          item.proPrescriptionMasterVO.herbalNum) /
        100
      );
    },
    // 显示提示
    showTips() {
      this.$refs.popup.open();
    },
    // 隐藏提示
    hideTip() {
      this.$refs.popup.close();
    },
    //取消
    cancel() {
      this.$refs.djPopup.close();
    },
    //取消
    cancelInvisible() {
      this.$refs.djPopupInvisible.close();
    },
    //确认
    submit() {
      this.getPrescriptionDetail(0);
    },
    //选择付款类型
    seledepositType(type) {
      this.DepositType = type;
    },
    // 快递切换
    setLogistType(n) {
      if (!this.isSele) return;
      if (this.status > 0) return;
      this.courierFees = this.LogisticsCostList[n].logisticsCost;
      this.logistIndex = n;
    },
    // 物流费用提示
    async getLogistTip() {
      let { data } = await getSysPlatformConfigByKeyList([
        "logisticsTipsRed", // 全局红色
      ]);
      this.logistTip = data[0].configValue;
    },
    // 选择是否代煎
    seleIsDj() {
      if (!this.isSele) return;
      // 已生成订单 不可选择
      if (this.prescriptionDetail.proBusinessInfo.status > 0) return;
      const that = this;
      uni.showActionSheet({
        itemList: ["是", "否"],
        success(res) {
          let index = res.tapIndex;
          that.is_dj = index;
          if (index == 0) {
            // 已选择代煎 价格需要加上代煎费
            that.dj_price = that.djPrice;
          } else {
            // 去掉代煎费
            that.dj_price = 0;
          }
        },
        fail(err) {},
      });
    },
    // 查询快递费用
    async getConfig() {
      if (this.address && this.address.deliveryType == 1) {
        const { drugstoreId } = this.address;
        // 获取全局参数
        let { data } = await getLogisticsCostList({
          orderCost: this.total,
          subjectId: drugstoreId,
        });
        this.LogisticsCostList = data;
        this.logistIndex = 0;

        if (data.length) {
          this.courierFees = data[0].logisticsCost;
        } else {
          this.courierFees = 0;
        }
      } else {
        this.courierFees = 0;
      }
    },
    // 获取中药总金额
    getZyPrice(arr, isReal = false) {
      console.log("88", arr);
      let n = 0;
      arr.forEach((v) => {
        if (isReal) {
          n += v.drugTotalReal * 100;
        } else {
          // 单价 数量 付数
          //n += v.drugTotal * 100 * 1 || v.quan;
          n += v.drugTotal * 100 * 1;
        }
      });
      return n / 100;
    },
    // 获取西药总金额
    getPriceNum(arr, isReal = false) {
      // console.log(arr)
      let n = 0;
      arr.forEach((v) => {
        if (isReal) {
          n += v.drugTotalReal * 100;
        } else {
          n += v.drugTotal * 100;
        }
      });
      return n / 100;
    },
    // 新增中西药拆分数组
    setDrugs(obj) {
      let z = [];
      let z_name = "";
      let x = [];
      let x_name = "";
      let s = [];
      let s_name = "";
      let cl = 0;
      let arr = obj.prescriptions;
      if (!arr.length) return;
      // 草药付数
      let herbalNum = 0;
      //单副代煎费
      let dfPrice = 0;
      //剂型
      let herbalFormulationsNum = 0;
      //代煎费
      let djCost = 0;
      //代煎费（新加）
      let djCostNum = 0;
      // 循环
      arr.forEach((v) => {
        const type = v.proPrescriptionMasterVO.prescriptionType;
        // 西药
        if (type == "1" || type == "2") {
          x = [...x, ...v.details];
          x_name = v.proPrescriptionMasterVO.prescriptionTypeName;
        }
        // 食品
        if (type == "4") {
          s = [...s, ...v.details];
          s_name = v.proPrescriptionMasterVO.prescriptionTypeName;
        }
        // 中药
        if (type == "3") {
          z = [...z, ...v.details];
          z_name = v.proPrescriptionMasterVO.prescriptionTypeName;
          // 几副药
          herbalNum += v.proPrescriptionMasterVO.herbalNum;
          //单副代煎费
          dfPrice += v.proPrescriptionMasterVO.dfPrice;
          //根据剂型算代煎费
          herbalFormulationsNum += this.dosageCodeMap.get(
            v.proPrescriptionMasterVO.herbalFormulations
          )
            ? this.dosageCodeMap.get(
                v.proPrescriptionMasterVO.herbalFormulations
              )
            : 0;
          //代煎费
          djCost += v.proPrescriptionMasterVO.djCost;
          //新加代煎费
          djCostNum +=
            (Number(
              this.dosageCodeMap.get(
                v.proPrescriptionMasterVO.herbalFormulations
              )
                ? this.dosageCodeMap.get(
                    v.proPrescriptionMasterVO.herbalFormulations
                  )
                : 0
            ) *
              100 *
              v.proPrescriptionMasterVO.herbalNum) /
            100;
          //console.log('比较一下',this.dosageCodeMap.get(v.proPrescriptionMasterVO.herbalFormulations))
          // 判断几个草药方
          cl += 1;

          // 中药用法
          this.zy_use =
            v.proPrescriptionMasterVO.dduName ||
            "口服" +
              "，每日" +
              v.proPrescriptionMasterVO.rc +
              "次，每次" +
              v.proPrescriptionMasterVO.rj +
              "剂，用药" +
              v.proPrescriptionMasterVO.days +
              "天";
        }
        this.z = z;
        this.z_name = z_name;
        this.x = x;
        this.x_name = x_name;
        this.s = s;
        this.s_name = s_name;
        this.cl_num = cl;
        this.herbalNum = herbalNum;
        this.djCost = djCost;
        this.dfPriceNum = herbalFormulationsNum;
      });
      this.djCostNum = djCostNum; //新加计算代煎费
      console.log("是否显示", this.herbalNum);
      console.log("djCostNum西哪家", djCostNum, this.dosageCodeMap);
      if (obj.proBusinessInfo.status > 0 && obj.proBusinessInfo.status != 10)
        return;
      this.getConfig();
    },
    //获取处方详情
    async getDetail(isFirst) {
      let prescriptionQueryVO = {
        businessId: this.businessId,
      };
      let nowObj = uni.getStorageSync("nowAddress").address;
      console.log("9999", uni.getStorageSync("nowAddress"));
      // uni.showLoading({
      //   title: "加载中",
      //   mask: true,
      // });

      let res = await getPatientPrescriptionBusinessOrderInfo(
        prescriptionQueryVO
        //   {
        //   businessId: this.businessId,
        // }
      );
      if (res.code != 20000 || !res.data.length) return;
      //新加
      this.dosageCodeMap.clear();
      let newDate = res.data[0];
      newDate.drugStore.forEach((t) => {
        t.drugstoreDosagePrices.forEach((item) => {
          this.dosageCodeMap.set(item.dosageName, item.pendingFryingFee);
        });
      });
      let obj = res.data[0];
      console.log("obj111", obj);
      uni.hideLoading();
      // 状态: 0待支付(未生成订单)  1待发货  2取药码  3待收货  4已取药  5已收货 6交易失败  7待支付(已生成订单未分割) 8待支付(已生成订单已分割) 9 失效 10 待审核
      this.status =
        obj.proBusinessInfo.status == 4 ? 10 : obj.proBusinessInfo.status; //别看上面得备注，4是审核驳回,审核驳回就是待审核
      this.DepositType = 1; //默认全款
      obj.prescriptions.forEach((element) => {
        if (element.proPrescriptionMasterVO.prescriptionType == 3) {
          // 把付数 放入每个详情里面 方便计算
          element.details.forEach((item) => {
            item.herbalNum = element.proPrescriptionMasterVO.herbalNum;
          });
        }
      });

      // 如果状态不为 0 不可选择是否代煎
      if (
        obj.proBusinessInfo.status > 0 &&
        obj.proBusinessInfo.status != 10 &&
        obj.proBusinessInfo.status != 4
      ) {
        this.isSele = false;
        //定金
        this.Deposit.downPayment = Number(obj.proBusinessInfo.downPayment);
        this.Deposit.balancePayment = Number(
          obj.proBusinessInfo.balancePayment
        );
        console.log(
          obj.proBusinessInfo.downPayment,
          obj.proBusinessInfo.balancePayment
        );
        if (
          obj.proBusinessInfo.paymentType &&
          Number(obj.proBusinessInfo.paymentType) != 1
        ) {
          this.$nextTick((_) => {
            this.seledepositType(2);
          });
        }
        // // 代煎
        // if (obj.proBusinessInfo.isDj == 1) {
        //   this.is_dj = 0;
        // } else {
        //   this.is_dj = 1;
        // }
        // this.isCandj = true;
        // // 单付代煎
        // this.dfPrice = obj.proBusinessInfo.dfPrice;
        // // 材料费
        // this.clPrice = obj.proBusinessInfo.clPrice;

        this.address = obj.proAdressInfo;
        this.address.telNo = obj.proAdressInfo.deliveryTelNo;
        //

        // 自提
        if (this.address.deliveryType == 2) {
          this.getDrugDetail();
        } else {
          await this.getConfig();
          let str;
          // 默认快递费
          let logisticsCost = 9999;
          // 未分割
          if (obj.proBusinessInfo.status == 7) {
            str = obj.proBusinessInfo.logisticsCustomName;
            logisticsCost = obj.proBusinessInfo.logisticsCost;
          } else {
            str = obj.drugStore[0]?.logisticsCustomName;
            logisticsCost = obj.drugStore[0]?.logisticsCost;
          }

          if (typeof logisticsCost != "number" || logisticsCost < 0) {
            logisticsCost = 9999;
          }

          const index = this.LogisticsCostList.findIndex(
            (it) => it.logisticsName === str
          );

          // 找不到一样的 那么就添加一条放到前面 用于回显
          if (index == -1) {
            let item = {
              logisticsName: str,
              logisticsCost,
            };
            this.LogisticsCostList = [item, ...this.LogisticsCostList];
            this.$set(this.LogisticsCostList, 0, item);
            this.logistIndex = 0;
          } else {
            this.LogisticsCostList[index].logisticsCost = logisticsCost;
            this.logistIndex = index;
          }

          this.courierFees = logisticsCost;
        }
        this.getOtherInfo(this.address.drugstoreId);
      }

      // 拆分
      // debugger
      this.setDrugs(obj);

      let dias = [];

      obj.diags.forEach((v) => {
        dias.push(v.diagName);
      });

      obj.proBusinessInfo.diags = dias.toString();

      this.prescriptionDetail = obj;
      console.log("this.prescriptionDetail", this.prescriptionDetail);
      this.allObj = JSON.parse(JSON.stringify(obj));

      let arr = [];

      // 优惠
      let discount = 0;
      // 优惠前
      let shouldMoney = 0;
      // 优惠后
      let realMoney = 0;
      // 重新赋值代煎费
      let djCostNum = 0;
      this.prescriptionDetail.prescriptions.forEach((item) => {
        let drugTotalReal = 0;
        let drugTotal = 0;
        //代煎费
        let djCost = 0;
        const type = item.proPrescriptionMasterVO.prescriptionType;
        if (type == "1" || type == "2" || type == "4") {
          item.details.map((citem) => {
            drugTotalReal += Number(citem.drugTotalReal) * 100;
            drugTotal += Number(citem.drugTotal) * 100;
            arr.push({
              drugId: citem.drugId,
              quan: citem.quan,
              dosageCode: item.proPrescriptionMasterVO.herbalFormulations,
            });
          });
          item.proPrescriptionMasterVO.cost = drugTotal / 100;
        } else if (type == "3") {
          let f = Number(item.proPrescriptionMasterVO.herbalNum);

          item.details.map((citem) => {
            console.log(citem.drugTotalReal);
            drugTotalReal += Number(citem.drugTotalReal) * 100 * 1;
            drugTotal += Number(citem.drugTotal) * 100 * 1;
            arr.push({
              drugId: citem.drugId,
              // quan: Number(item.proPrescriptionMasterVO.herbalNum) *
              //   Number(citem.quan),
              quan: Number(citem.quan), //改动（05.24）
              dosageCode: item.proPrescriptionMasterVO.herbalFormulations,
              herbalNum: item.proPrescriptionMasterVO.herbalNum,
            });
          });
          djCost +=
            item.proPrescriptionMasterVO.isDj == 1
              ? item.proPrescriptionMasterVO.djCost * 100
              : 0;
          item.proPrescriptionMasterVO.cost = (drugTotal + djCost) / 100;
        }
        console.log(realMoney, drugTotalReal, "sssss");
        realMoney += djCost;
        shouldMoney += djCost;
        realMoney += drugTotalReal;
        shouldMoney += drugTotal;
        djCostNum += djCost;
        item.proPrescriptionMasterVO.drugTotalReal = drugTotalReal / 100;
        item.proPrescriptionMasterVO.drugTotal = drugTotal / 100;
        console.log("看看", djCost);
        console.log("代煎费", this.djCostNum);
      });
      this.prescriptionDetail.proBusinessInfo.cost = realMoney / 100;
      this.cost = realMoney / 100;
      console.log("this.cost", this.cost);
      discount = (Number(shouldMoney) * 100 - Number(realMoney) * 100) / 100;
      this.shouldMoney = shouldMoney / 100;
      this.realMoney = realMoney / 100;
      console.log("this.shouldMoney11", this.shouldMoney);
      console.log("this.realMoney", this.realMoney);
      this.discount = discount / 100;
      this.djCostNum = djCostNum / 100;
      console.log("重新赋值", this.djCostNum);
      console.log("arr99999", arr);
      uni.setStorageSync("drugArr", arr);

      if (obj.proBusinessInfo.status == 0) {
        this.isSele = true;

        // 判断是否选择了药店
        let nowObj = uni.getStorageSync("nowAddress");
        // console.log(nowObj);
        if (nowObj) {
          let address = nowObj.address;

          const { drugstoreId, isCandj } = address;
          // 是否支持代煎
          this.isCandj = isCandj == 1 ? true : false;
          if (drugstoreId) {
            // 如果选择了药店,获取该药店的药品价格信息
            this.address = address;
            await this.getOtherInfo(
              this.address.drugstoreId,
              uni.getStorageSync("drugArr")
            );
            // 清除
            uni.removeStorageSync("nowAddress");
          }
          return;
        }
        // 如果头一次
        if (!isFirst) return;

        // 新需求 如果只有一个药店 直接选中
        this.autoSeting(arr);
      }
    },
    // 自动选择药店
    async autoSeting(drugArr) {
      /**
       * 流程
       * 1, 获取用户收货地址
       * 2, 取用户收货地址第一个信息（设为已选中），去查询药店列表
       * 3, 选中药店，设置参数
       */
      let { data: userAddressList } = await findAddressByUserId({
        userId: uni.getStorageSync("userId"),
        isDefaul: "",
      });
      let address;
      // 不存在收货地址
      if (!userAddressList || !userAddressList.length) return;
      // 存在收货地址 选第一个
      if (userAddressList && userAddressList.length) {
        address = userAddressList[0];
      }
      let { drugstoreId, docId } = this.prescriptionDetail.proBusinessInfo;

      let query = {
        cityName: "",
        docId,
        drugList: drugArr,
        drugNum: drugArr.length,
        drugstoreIds: drugstoreId ? [drugstoreId] : [],
        latitude: "",
        limit: 10,
        longitude: "",
        page: 1,
        provinceName: "",
      };

      // 根据地址经纬度 获取地址信息
      let obj = await this.getLocation(address);
      const { latitude, longitude } = address;

      // 拼接参数
      query = {
        ...query,
        ...obj,
        latitude,
        longitude,
      };

      // 发起请求
      let { data: drugStore } = await getDrugStorageStore(query);

      // 自提与快递 分两个数组
      let { expressDeliveryDrugStore, selfDeliveryDrugStore } = drugStore;
      console.log("drugStore", drugStore);
      // 超过1个药店 则不默认选择
      if (
        expressDeliveryDrugStore.length > 1 ||
        selfDeliveryDrugStore.length > 1
      ) {
        return;
      }

      expressDeliveryDrugStore.forEach((v) => (v.deliveryType = 1));
      selfDeliveryDrugStore.forEach((v) => (v.deliveryType = 2));

      let drugStroeList = [];

      // 整合药店
      drugStroeList = [...expressDeliveryDrugStore, ...selfDeliveryDrugStore];

      if (!drugStroeList.length) return;
      if (drugStroeList.length > 1) return;

      const seleDrugStroe = drugStroeList[0];

      const { deliveryType, drugstoreId: id, isCandj } = seleDrugStroe;

      address.deliveryType = deliveryType;
      address.drugstoreId = id;

      address.addressArea = address.addressArea;
      address.addressDetail = address.addressDetail;
      seleDrugStroe.telNoSh = seleDrugStroe.telNo;
      address.deliveryName = address.deliveryName;
      address.fromlatitude = address.latitude;
      address.fromlongitude = address.longitude;

      address = {
        ...seleDrugStroe,
        ...address,
      };

      console.log(address);

      this.address = address;

      this.isCandj = isCandj == 1 ? true : false;
      await this.getOtherInfo(id, drugArr);
    },
    // 地址逆向解析
    async getLocation({ latitude, longitude }) {
      try {
        let res = await this.$jsonp("https://apis.map.qq.com/ws/geocoder/v1/", {
          location: latitude + "," + longitude,
          get_poi: 1,
          key: mapKey,
          output: "jsonp",
        });
        if (res.status) {
          return {
            provinceName: "",
            cityName: "",
          };
        }
        let {
          result: { address_component: data },
        } = res;

        return {
          provinceName: data.province,
          cityName: data.city,
        };
      } catch (error) {
        return {
          provinceName: item.provinceName || "",
          cityName: item.cityName || "",
        };
      }
    },
    // 根据药店id查询相关信息
    async getOtherInfo(drugstoreId, drugList = []) {
      console.log("drugstoreId", drugstoreId);
      let { data } = await getSomePriceSameByDrugStorage({
        drugstoreId,
        drugList,
      });
      console.log("data", data);
      //新加剂型map
      this.dosageCodeMap.clear();
      data.drugstoreDosagePrices.forEach((item) => {
        this.dosageCodeMap.set(item.dosageName, item.pendingFryingFee);
      });
      console.log(this.dosageCodeMap);
      this.address.isDeposit = data.isDeposit || null;
      if (this.address.isDeposit && this.status == 0) {
        console.log("判断有没有支持定金的药店");
        // console.log(this.address.deliveryType, "选择得药店自提还是快递");
        console.log(data, "药店");
        if (Number(data.downPayment) == 0) {
          if (Number(this.address.deliveryType) == 1) {
            this.Deposit.payText = "在线支付";
            this.Deposit.DepositText = "货到付款";
          } else if (Number(this.address.deliveryType) == 2) {
            this.Deposit.payText = "在线支付";
            this.Deposit.DepositText = "到店支付";
          }
        } else {
          this.Deposit.payText = "全额支付";
          this.Deposit.DepositText = "定金支付";
        }
        this.Deposit.downPayment = data.downPayment || 0;
      }

      let {
        receiptWay,
        dfPrice: djPrice,
        shoppingTipsList,
        drugPriceMap,
      } = data;
      // 支付方式
      this.payList = receiptWay;

      // 代煎费相关
      // let {
      //   clPrice,
      //   dfPrice
      // } = djPrice;
      // this.clPrice = clPrice;
      // this.dfPrice = dfPrice;

      let str = "";
      shoppingTipsList.forEach((v) => {
        str += `${v.name}：${v.content} `;
      });
      this.logisticsTip = {
        tipsTitle: "运费说明",
        tipsContent: str,
      };
      // 存在药品
      if (drugPriceMap) {
        this.getDrugStoragePrice(drugPriceMap);
      }
    },
    // 获取药店地址信息
    async getDrugDetail() {
      let res = await findDrugStoreDetail({
        //查询药店信息详情
        drugstoreId: this.address.drugstoreId,
      });
      if (!res.data) return;
      let drugstoreAddress =
        res.data.cityName + res.data.area + res.data.address;
      this.$set(this.address, "drugstoreAddress", drugstoreAddress);
      this.address.telNoSh = res.data.telNo;
      this.address.latitude = res.data.latitude;
      this.address.longitude = res.data.longitude;
      if (res.data.drugstoreImg) {
        myJsTools.downAndSaveImg(res.data.drugstoreImg, (url) => {
          this.$set(this.address, "drugstoreImg", url);
        });
      }
    },
    //提交订单
    async saveOrder() {
      if (!this.nextClick) return;
      this.nextClick = false;
      let prescription = JSON.parse(JSON.stringify(this.prescriptionDetail));
      delete prescription.beforePayVisible;
      delete prescription.drugStore;
      delete prescription.proAdressInfo;
      delete prescription.proBusinessOrderInfo;

      let arrOne = [];
      let isDj = 1;

      prescription.prescriptions.forEach((element) => {
        let mapList = [];
        element.details.map((item) => {
          mapList.push({
            ...item,
          });
        });

        const djCost =
          (Number(
            this.dosageCodeMap.get(
              element.proPrescriptionMasterVO.herbalFormulations
            )
              ? this.dosageCodeMap.get(
                  element.proPrescriptionMasterVO.herbalFormulations
                )
              : 0
          ) *
            100 *
            (element.proPrescriptionMasterVO.herbalNum || 0)) /
          100;
        let dfPriceNum = this.dosageCodeMap.get(
          element.proPrescriptionMasterVO.herbalFormulations
        );
        arrOne.push({
          orderPrescriptionDetails: mapList,
          proOrderPrescriptionMaster: {
            ...element.proPrescriptionMasterVO,
            //dfPrice,
            dfPrice: dfPriceNum, //改动
            djCost: isDj == 1 ? djCost : 0,
            clPrice: 0,
            isDj,
          },
        });
      });
      console.log("arrOne", arrOne);
      let {
        deliveryType,
        drugstoreId,
        drugstoreName,
        addressArea,
        deliveryName,
        addressDetail,
        telNoSh,
        deliveryTelNo,
        drugstoreTelNo,
        telNo,
      } = this.address;

      let deliveryAddressDetail = "";

      prescription.proBusinessInfo.cost = this.total;
      prescription.proBusinessInfo.deliveryType = deliveryType;
      prescription.proBusinessInfo.drugstoreId = drugstoreId;
      prescription.proBusinessInfo.drugstoreName = drugstoreName;
      prescription.proBusinessInfo.deliveryName = deliveryName;
      prescription.proBusinessInfo.drugstoreTelNo = telNoSh || drugstoreTelNo;
      prescription.proBusinessInfo.telNo = deliveryTelNo =
        telNo || deliveryTelNo;
      prescription.proBusinessInfo.addressDetail = deliveryAddressDetail =
        (addressArea || "") + addressDetail;

      let logisticsCost = 0;
      let logisticsCustomName = "";

      // 快递 物流名称
      if (deliveryType == 1) {
        const item = this.LogisticsCostList[this.logistIndex];
        prescription.proBusinessInfo.logisticsCustomName = logisticsCustomName =
          item.logisticsName;
        prescription.proBusinessInfo.logisticsCost = logisticsCost =
          item.logisticsCost;
      }

      let orderMoney = this.realMoney;

      if (this.status == 0) {
        //let djPrice = this.is_dj == 0 ? this.djPrice : 0;
        let djPrice = this.djCostNum;
        orderMoney =
          (Number(this.realMoney) * 100 + Number(djPrice) * 100) / 100;
        //console.log('orderMoney支付',orderMoney)
      }

      // 是否代付
      const isHelpPay = this.isHelpPay;

      let index;
      let item;

      if (isHelpPay == 1) {
        if (!this.payList.length) {
          Toast("当前没有配置支付方式");
          return Promise.reject("没有支付方式");
        }

        item = this.payList[0];
        index = "d";
      } else {
        let obj = await this.selePay();
        index = obj.index;
        item = obj.item;
      }
      // let { index, item } = await this.selePay();
      uni.showLoading({
        mask: true,
      });

      // 支付方式
      let payType = index;
      let callId = item.appid;
      let subjectId = item.subjectId;
      let subjectName = item.subjectName;

      let appid = uni.getStorageSync("appId");
      let openid = uni.getStorageSync("wxInfo").openId;

      let obj = {
        appid,
        openid,
        payType,
        callId,
        orderPrescriptions: arrOne,
        proPayPending: prescription.proBusinessInfo,
        subjectId,
        subjectName,
        status: this.status,
        isMedicare: 0,
        deliveryType,
        deliveryName,
        deliveryTelNo: telNo || deliveryTelNo,
        deliveryAddressDetail,
        logisticsCost,
        logisticsCustomName,
        orderMoney,
        isHelpPay,
        paymentType: 1, //付款类型(1.全款 2.定金,3.货到付款,4.到店支付 )
        isDeposit: this.address.isDeposit, //是否支持定金（0:不支持;1:支持）
      };
      console.log("支付", obj);
      // 调用接口
      console.log("查看当前是否使用定金支付", this.DepositType);
      obj.balancePayment = this.Deposit.balancePayment || ""; //尾款
      obj.downPayment = this.Deposit.downPayment || ""; //首款
      if (Number(this.address.isDeposit) == 1 && this.DepositType === 2) {
        if (
          this.address.deliveryType == 1 &&
          Number(this.Deposit.downPayment) == 0
        ) {
          obj.paymentType = 3;
          obj.payType = 0;
        } else if (
          this.address.deliveryType == 2 &&
          Number(this.Deposit.downPayment) == 0
        ) {
          obj.paymentType = 4;
          obj.payType = 0;
        } else {
          obj.paymentType = 2;
        }
      }
      //总金额和运费都为0时 paytype应为0
      if (this.total + this.courierFees === 0) {
        obj.payType = 0;
      }
      try {
        let { data } = await savePendingReceiptAndToPay(obj);

        this.nextClick = true;
        this.ghId = data.ghId;
        delete data.ghId;
        uni.hideLoading();

        if (isHelpPay == 1) {
          // 显示遮罩提示分享
          console.log("- 代付流程");
          let { orderNo } = data;
          const money = Number(orderMoney) * 100 + Number(logisticsCost) * 100;
          await this.setShare(money / 100, orderNo, 1, this.ghId);
          this.showTip = true;
          this.getDetail();
          return;
        }

        // 微信
        if (index == 1) {
          this.wxPay(data);
          return;
        }

        // 支付宝
        if (index == 2) {
          let money = 0;
          if (this.status == 0) {
            // 加快递费
            money =
              (Number(this.total) * 100 + Number(this.courierFees) * 100) / 100;
          } else {
            money =
              (Number(this.cost) * 100 + Number(this.courierFees) * 100) / 100;
          }
          uni.navigateTo({
            url:
              "/pages/pay/pay?price=" +
              money +
              "&ghId=" +
              this.ghId +
              "&url=" +
              btoa(data.url),
          });
          this.getDetail();
        }
      } catch (error) {
        uni.hideLoading();
        // this.getDetail();
        this.nextClick = true;
      }
    },
    //确认代煎费是否正确弹窗
    isOpenDj(arr, beforePayVisible) {
      if (this.status == 0) {
        // 去下单
        if (!this.address) {
          Toast("请选择地址");
          return false;
        }
        if (!this.isNext) return;
      }
      let type = false;
      arr.forEach((item) => {
        if (item.proPrescriptionMasterVO.prescriptionType == 3) {
          type = true;
        }
      });
      if (type == true) {
        //显示是否可见页面的弹框
        if (beforePayVisible == 1) {
          this.$refs.djPopup.open();
        } else {
          this.$refs.djPopupInvisible.open();
        }
      } else {
        this.getPrescriptionDetail(0);
      }
    },
    //按钮点击
    getPrescriptionDetail(isHelpPay = 0) {
      // if (this.status == 0) {
      //   // 去下单
      //   if (!this.address) {
      //     Toast("请选择地址");
      //     return false;
      //   }
      //   // 是否存在代煎费
      //   // if (this.z.length && this.isCandj) {
      //   //   if (this.is_dj == -1) {
      //   //     Toast("请选择是否代煎");
      //   //     return;
      //   //   }
      //   // }
      //   if (!this.isNext) return;
      // }
      this.isHelpPay = isHelpPay;
      clearTimeout(payTimer);
      payTimer = setTimeout(this.saveOrder, 500);
    },

    // 选择支付方式
    async selePay() {
      this.isSele = false;
      let that = this;
      return new Promise((resolve, reject) => {
        // 如果不可选支付
        if (!this.payList.length) {
          Toast("当前没有配置支付方式");
          that.getDetail();
          return reject();
        }
        // 如果只有一个支付方式
        if (this.payList.length == 1) {
          let item = this.payList[0];

          if (item.receiptType == 1) {
            resolve({
              index: 1,
              item,
            });
          } else {
            resolve({
              index: 2,
              item,
            });
          }
          return;
        }
        // 如果在线支付
        uni.showActionSheet({
          itemList: ["微信支付", "支付宝支付"],
          success(res) {
            let index = res.tapIndex + 1;
            let item;
            item = that.payList.filter((it) => it.receiptType == index)[0];
            if (!item) {
              Toast("暂不支持该支付方式");
              return reject();
            }
            resolve({
              index,
              item,
            });
          },
          fail(err) {
            that.nextClick = true;
            that.getDetail();
            reject(err);
          },
        });
      });
    },

    // 微信支付
    async wxPay(info) {
      if (!info.appId) {
        Toast("支付成功");
        uni.redirectTo({
          url: "/pages/prescription/preDetail?businessId=" + this.businessId,
        });
        return;
      }
      try {
        await wxPay(info);
        this.getStatus();
        timer = setInterval(this.getStatus, 2000);
      } catch (error) {
        Toast("取消支付");
        this.nextClick = true;
        this.getDetail();
      }
    },

    // 查询支付状态
    async getStatus() {
      num--;
      try {
        let {
          data: { regStatus },
        } = await queryRegisterPayStatus({
          ghId: this.ghId,
        });

        if (regStatus == 2) {
          Toast("支付成功");
          uni.redirectTo({
            url: "/pages/prescription/preDetail?businessId=" + this.businessId,
          });
          clearInterval(timer);
          num = 3;
          timer = null;
        }
      } catch (error) {
        clearInterval(timer);
        num = 3;
        timer = null;
      }
    },

    //跳转选择收货地址
    navigatorFun() {
      let { drugstoreId, docId } = this.prescriptionDetail.proBusinessInfo;
      let url = "/pages/address/hospitalAddress?businessId=" + this.businessId;
      url += "&docId=" + docId;
      if (drugstoreId) {
        url += "&drugstoreId=" + drugstoreId;
      }
      // 未生成业务订单之前可以修改收货地址
      if (this.status == 0) {
        uni.navigateTo({
          url,
        });
      }
    },
    //根据药店获取药品信息
    async getDrugStoragePrice(drugList) {
      console.log("drugList", drugList);
      const prescriptions = this.prescriptionDetail.prescriptions;
      // 价位drugTotalReal
      let realMoney = 0,
        shouldMoney = 0,
        discount = 0;
      prescriptions.forEach((item) => {
        let cost = 0;
        item.details.forEach((citem) => {
          // 拼接id
          // const DRUGID =
          //   citem.drugId + "_" + citem.quan * (citem.herbalNum || 1);
          const DRUGID = citem.drugId + "_" + citem.quan; //改动（05.24）
          const d = drugList[DRUGID];
          // if (d.drugId == citem.drugId) {
          if (d.drugId) {
            //realMoney += Number(d.realMoney) * 100;
            //shouldMoney += Number(d.shouldMoney) * 100;
            //cost += d.price * 100 * citem.quan;
            if (item.proPrescriptionMasterVO.prescriptionType == 3) {
              //改动
              let herbalNum = item.proPrescriptionMasterVO.herbalNum;
              shouldMoney += Number(d.shouldMoney) * 100 * herbalNum;
              realMoney += Number(d.realMoney) * 100 * herbalNum;
            } else {
              shouldMoney += Number(d.shouldMoney) * 100;
              realMoney += Number(d.realMoney) * 100;
            }
            cost += (d.price * citem.quan).toFixed(2) * 100; //改动
            citem.price = d.price;
            // // 实际
            // citem.drugTotalReal = d.realMoney;
            // // citem.herbalNum = 1;
            // // 应付
            // citem.drugTotal = d.shouldMoney;
            if (item.proPrescriptionMasterVO.prescriptionType == 3) {
              //改动 5.24新加判断（小数点后几位计算的逻辑单价*克数 保留俩位小数后再乘以付数）
              let num = item.proPrescriptionMasterVO.herbalNum;
              // 实际
              citem.drugTotalReal = d.realMoney * num;
              // 应付
              citem.drugTotal = d.shouldMoney * num;
            } else {
              // 实际
              citem.drugTotalReal = d.realMoney;
              // 应付
              citem.drugTotal = d.shouldMoney;
            }
            citem.platformPurchasePrice = d.platformPurchasePrice;
            citem.pmaId = d.pmaId;
            citem.activeName = d.activeName;
            citem.yfkcId = d.yfkcId;
          }
        });
        discount = shouldMoney - realMoney;
        console.log("shouldMoney", shouldMoney);
        // 如果草药
        if (item.proPrescriptionMasterVO.prescriptionType == 3) {
          let herbalNum = item.proPrescriptionMasterVO.herbalNum;
          item.proPrescriptionMasterVO.cost = (cost * herbalNum) / 100;
        } else {
          item.proPrescriptionMasterVO.cost = cost / 100;
        }
      });
      this.discount = discount / 100;
      this.shouldMoney = shouldMoney / 100;
      this.realMoney = realMoney / 100;
      console.log("选药店", this.djCostNum);
      console.log("选择药店以后得", this.shouldMoney);
      console.log("实际支付", this.realMoney);
      // 重新执行
      this.setDrugs(this.prescriptionDetail);

      this.isNext = true;
      return Promise.resolve();
    },
    //联系电话
    call() {
      uni.makePhoneCall({
        // 手机号
        phoneNumber: this.address.telNo,
      });
    },
    // 去医院
    goMap() {
      if (this.address.drugstoreImg) {
        delete this.address.drugstoreImg;
      }
      uni.navigateTo({
        url: `../address/goAddress?address=${JSON.stringify(this.address)}`,
      });
    },

    // 手机号隐藏中间四位数
    setPhone(str) {
      return myJsTools.phone(str);
    },
  },
};
</script>

<style lang="scss" scoped>
.pre_head {
  background-color: #fff;
  border-radius: 8rpx;
  padding: 24rpx 32rpx;
  margin: 24rpx 0;

  .head_item {
    @include flex(lr);
    font-size: 28rpx;
    color: #666;
    line-height: 50rpx;

    .bold {
      color: #333;
    }
  }
}

// 弹窗
.pop {
  padding: 0 32rpx 50rpx;
  background-color: #fff;
  border-radius: 16rpx 16rpx 0px 0px;

  .pop_title {
    height: 88rpx;
    @include flex;
    position: relative;
    font-size: 32rpx;

    .uni-icons {
      position: absolute;
      right: 0;
    }
  }

  .pop_text {
    color: #999;
    font-size: 24rpx;
    line-height: 40rpx;
  }
}

// 快递选择
.logistics {
  width: 100%;
  background-color: #fff;
  margin-top: 24rpx;
  border-radius: 8rpx;
  padding: 24rpx;
  box-sizing: border-box;

  .logist_title {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 24rpx;
    position: relative;
    padding-left: 24rpx;
    font-weight: bold;

    &::before {
      content: "";
      display: block;
      width: 6rpx;
      height: 28rpx;
      @include bg_theme;
      position: absolute;
      left: 0;
      top: calc(50% - 14rpx);
    }

    .uni-icons {
      margin-left: 10rpx;
    }
  }

  .item {
    height: 76rpx;
    padding: 0 24rpx;
    @include flex(lr);
    background-color: #f5f5f5;

    border-radius: 8rpx;
    margin-top: 24rpx;

    &:nth-child(1) {
      margin-top: 0rpx;
    }

    .name {
      font-size: 28rpx;
    }

    .right {
      @include flex;

      text {
        font-size: 28rpx;
        color: red;
        padding-right: 30rpx;
      }

      image {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }

  .paytype {
    background: #ffffff;
    // background: aqua;
  }
}

.prescriptionDetail {
  background: #f5f5f5;
}

.cfDetailView {
  box-sizing: border-box;
  padding: 24rpx 32rpx 142rpx 24rpx;
  min-height: 100vh;

  .address {
    display: block;
    margin: 24rpx 0;
    border-radius: 8rpx;
    border-bottom: none;
    color: #333333;
    background: #ffffff;
    font-size: 28rpx;
    line-height: 40rpx;
    padding: 26rpx 32rpx;
    position: relative;

    &::after {
      content: "";
      display: block;
      width: 94%;
      height: 6rpx;
      position: absolute;
      bottom: 0;
      left: 3%;
      background: repeating-linear-gradient(
        -50deg,
        #fff 0%,
        #fff 3%,
        #14a0e6 3%,
        #14a0e6 7%
      );
    }

    &.sele {
      &::after {
        display: none;
      }
    }

    view:first-child {
      width: auto;
      flex: none;
    }

    .address-top {
      @include flex(lr);

      .title {
        font-weight: bold;
        position: relative;
        padding-left: 24rpx;

        &::before {
          content: "";
          display: block;
          width: 6rpx;
          height: 28rpx;
          @include bg_theme;
          position: absolute;
          left: 0;
          top: calc(50% - 14rpx);
        }
      }

      .right {
        @include flex;

        .text {
          color: #999;

          &.black {
            color: #333;
          }
        }
      }
    }

    .address-tip {
      display: flex;
      margin-top: 24upx;
      font-size: 24upx;

      .content {
        flex: 1;
        color: #999;
        line-height: 34upx;
        margin-left: 62upx;
      }
    }
  }
}

.label {
  border-radius: 8rpx 8rpx 0 0;
  font-weight: bold;
  position: relative;
  @include flex(left);
  height: 60rpx;
  padding-left: 56rpx;
  font-size: 28rpx;
  background-color: #fff;

  &::before {
    content: "";
    display: block;
    width: 6rpx;
    height: 28rpx;
    @include bg_theme;
    position: absolute;
    left: 38rpx;
    top: calc(50% - 14rpx);
  }
}

.cfDetail {
  padding: 0 32rpx;
  background: #ffffff;
  border-bottom: 1px dashed #f5f5f5;

  &:last-child {
    border-bottom: none;
  }

  .total {
    padding-bottom: 24rpx;
    margin-top: 24upx;

    text {
      color: #ff0000;
    }

    text-align: right;
    color: #333333;
    font-size: 28rpx;
  }

  .title {
    color: #333333;
    font-size: 28rpx;
    position: relative;
    padding: 30rpx 0rpx 0;
    font-weight: 600;
    @include flex(lr);

    label {
      flex: 1;

      uni-checkbox {
        margin-right: 6rpx;
      }
    }
  }

  .drugName {
    display: flex;
    align-items: center;
    color: #333333;
    font-size: 28rpx;
    font-weight: 500;
    margin-top: 10rpx;

    uni-checkbox {
      margin-right: 20rpx;
    }
  }

  .drugInfo {
    color: #333;
    font-size: 28rpx;

    view {
      margin-top: 16rpx;
    }

    .total {
      padding-bottom: 24rpx;

      text {
        color: #ff0000;
      }

      text-align: right;
    }
  }

  .cyCf {
    color: #333333;
    font-size: 28rpx;
    border-top: 1px solid #eee;

    .drugName {
      margin-top: 18rpx;
    }

    .total {
      font-size: 28rpx;
      padding-bottom: 24rpx;

      text {
        color: #ff0000;
      }

      text-align: right;
    }

    .price {
      @include flex(lr);
    }

    .useMethods,
    .djCf {
      margin: 16rpx 0;
    }
  }
}

// 药品总价
.price_count {
  padding: 0 32rpx;
  border-radius: 0 0 8rpx 8rpx;
  @include flex(right);
  height: 94rpx;
  font-size: 28rpx;
  background-color: #fff;

  view {
    @include flex;
    margin-left: 24rpx;

    text {
      color: red;
      padding-left: 10rpx;
    }
  }
}

.list_detail,
.cy_item {
  width: 100%;
  padding-bottom: 20upx;
}

.footer {
  position: fixed;
  bottom: 0;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.5);
  padding: 0 32rpx;
  height: 104rpx;
  box-sizing: border-box;
  color: #333333;
  font-size: 28rpx;
  width: 100%;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;

  text {
    color: #ff0000;
  }

  .btns {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
  }

  .btns .btn {
    width: 158rpx;
    height: 56rpx;
    @include bg_theme;
    border-radius: 28rpx;
    font-size: 26rpx;
    font-weight: 500;
    color: #ffffff;
    @include flex;

    &.sx {
      background-color: #d8d8d8;
    }
  }

  .btns .btn:first-child {
    margin-left: 24rpx;
  }

  .btns .btn:nth-child(2) {
    background-color: #ffffff;
    @include border_theme;
    @include font_theme;
  }
}

.xyCf {
  .price {
    position: relative;

    view {
      position: absolute;
      right: -5px;
      top: -10px;
    }
  }
}

.checkBoxView {
  display: flex;
  align-items: flex-start;
}

//地址 S
.address-ps {
  .address-distribution {
    .address-distribution-default,
    .address-distribution-icon {
      display: inline-block;
      line-height: 32upx;
      background-color: #666;
      border-radius: 8upx;
      font-size: 22upx;
      padding: 0 10upx;
      color: #fff;
      margin-right: 10upx;
    }

    .address-distribution-content {
      font-size: 28upx;
      font-weight: 600;
      color: #333;
    }
  }

  .address-distribution-contact {
    @include flex(lr);
    color: #666;
    font-size: 26upx;
    margin-top: 12upx;

    .address-distribution-contact-l {
      text {
        &:nth-child(1) {
          margin-right: 10upx;
        }
      }
    }
  }
}

.address-zt {
  .address-self {
    display: flex;

    image,
    img {
      width: 140upx;
      height: 140upx;
      margin-right: 26upx;
      object-fit: cover;
      border-radius: 8rpx;
      border: 1px solid #f5f5f5;
    }

    .address-self-text {
      flex: 1;

      .address-self-text-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10upx;

        .address-self-text-top-l {
          font-size: 28upx;
          color: #333;
        }

        .address-self-text-top-r {
          display: inline-block;
          line-height: 32upx;
          background-color: #666;
          border-radius: 8upx;
          font-size: 22upx;
          padding: 0 10upx;
          color: #fff;
        }
      }

      .address-self-text-map {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #666;
        font-size: 26upx;
        margin-top: 10upx;

        &:nth-child(3) {
          justify-content: flex-end;
        }
      }
    }
  }

  .address-self-contact {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 24upx;

    .address-self-btn {
      width: 160upx !important;
      line-height: 60upx;
      text-align: center;
      @include border_theme(1px);
      border-radius: 40upx;
      font-size: 28upx;
      font-weight: 400;
      margin-left: 16upx;
      @include font_theme;
    }
  }
}

// 价格摘要
.price_list {
  margin-top: 24upx;
  box-sizing: border-box;
  width: 100%;
  border-radius: 8upx;
  background-color: #fff;
  padding: 24upx;
  font-size: 28upx;
  color: $k-title;

  .price_count {
    padding: 0;
  }

  // 标题
  .label {
    padding-left: 24rpx;

    &::before {
      left: 0;
    }
  }

  .list_item {
    margin-top: 24upx;
    background-color: $k-page-bg-color;
    border-radius: 8upx;
    padding: 24upx;
    line-height: 28upx;

    .item {
      @include flex(lr);

      .item_label {
        width: 150upx;
        flex: none;
      }

      .item_num {
        flex: 1;
        text-align: left;
      }

      .item_price {
        color: red;
      }
    }

    .item_cont {
      @include flex(lr);
      padding-top: 28upx;

      .item_label {
        width: 150upx;
        flex: none;
      }

      .item_detail {
        flex: 1;
      }
    }
  }

  .list_count {
    padding-top: 24upx;
    text-align: right;
  }
}

.empty {
  text-align: center;
  font-size: 28rpx;
  color: #333;
  padding: 40rpx 0;

  text {
    color: red;
  }
}

.price-class {
  color: #ff0000;
  margin: 16rpx 0;
}

.cy_item_pop {
  display: flex;
  flex-wrap: wrap;
}

.cy_item_pop_name {
  margin-right: 5px;
}

.pop_box {
  padding: 0 32rpx 50rpx;
  background-color: #fff;
  border-radius: 16rpx;
  //width: 90%;
  width: 90vw;
  margin: 0 auto;
  font-size: 28rpx;

  .pop_title {
    height: 100rpx;
    @include flex;
    position: relative;
    font-size: 36rpx;
    font-weight: bold;
  }

  .cyCf {
    color: #333333;
    font-size: 28rpx;

    .drugName {
      margin-top: 18rpx;
    }
  }
}

.btn-box {
  display: flex;
  justify-content: space-around;
  margin-top: 30rpx;
  font-size: 36rpx;

  .btn-left {
    color: #999;
  }

  .btn-right {
    color: #14a0e6;
  }
}
</style>
