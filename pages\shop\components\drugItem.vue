<template>
  <!-- 药品卡片 -->
  <view class="drug_card">
    <!-- 信息 -->
    <view class="card" @click="toDrugDetail(item)">
      <view style="position: relative;">
        <img
          v-if="item.drugImg"
          :data-src="errUrl"
          :src="item.drugImgUrl"
          :class="['card_img', item.drugType == '025.9' ? 'gx' : '']"
          alt=""
        />
        <!-- 商品图片 -->
        <image
          src="/static/shop/drug.png"
          v-else
          mode="aspectFill"
          :class="['card_img', item.drugType == '025.9' ? 'gx' : '']"
        ></image>
        <view
          v-if="(item.drugKc == 0 || !item.drugKc) && flag != 'artcle'"
          class="drug-coverUp"
        >
          <img class="card_img" src="/static/ysq.png" alt="" />
        </view>
        <view v-if="item.drugType == '025.9'" class="drug-coverUp cfCover">
          处方药 <br />
          依规定不展示包装
        </view>
      </view>

      <!-- 文字 -->
      <view class="card_text">
        <view style="display: flex">
          <view
            v-if="item.drugType != '025.8' && item.drugType"
            class="drugTypeName"
            :style="getDrugTypeColor(item)"
            >{{ item.drugTypeName || '' }}</view
          >
          <view class="text_title">{{ item.drugName }}</view>
        </view>
        <view class="text_info">规格：{{ item.gg }}</view>
        <view v-if="flag != 'artcle'" class="text_info"
          >药店：{{ item.drugstoreName
          }}{{ item.isProprietary == '1' ? '（自营）' : '' }}</view
        >
        <view class="active" v-show="item.activeName"
          >单品{{ item.activeName }}</view
        >
        <view v-if="flag != 'artcle'" class="text_price">
          <text>单价：</text>
          ￥{{ item.price }}
        </view>
        <view v-if="flag === 'artcle'" class="text_detail"> 查看详情 </view>
      </view>
    </view>
    <!-- 库存操作 -->
    <view class="drug_action" v-if="flag != 'artcle'">
      <text class="count">库存{{ item.drugKc }}件</text>
      <!-- 操作 -->
      <view
        class="action"
        v-show="
          item.quan &&
          item.drugKc &&
          item.drugId !== '72d263bd0ebd48e49e9063320efa0ff7'
        "
      >
        <image @click="reduce" class="icon" src="/static/shop/del.png"></image>
        <text class="num">{{ item.quan }}</text>
        <image @click="add" class="icon" src="/static/shop/add.png"></image>
      </view>
      <view
        class="action"
        v-show="
          item.drugKc && item.drugId == '72d263bd0ebd48e49e9063320efa0ff7'
        "
      >
        <image @click="reduce" class="icon" src="/static/shop/del.png"></image>
        <input
          type="number"
          class="num"
          v-model="item.quan"
          @blur="onQuantityBlur"
          style="text-align: center"
        />
        <image @click="add" class="icon" src="/static/shop/add.png"></image>
      </view>
      <!-- 添加 有库存 且未选中 -->
      <view
        class="action"
        v-show="
          !item.quan &&
          item.drugKc &&
          item.drugId !== '72d263bd0ebd48e49e9063320efa0ff7'
        "
      >
        <image @click="add" class="icon" src="/static/shop/add.png"></image>
      </view>
    </view>
  </view>
</template>

<script>
import { mapActions } from 'vuex'
import { showModal } from '@/common/js/pay.js'
import drugRulesMixin from '@/mixins/drugRules.js'
export default {
  name: 'DrugCard',
  mixins: [drugRulesMixin],
  props: {
    index: Number,
    item: {
      type: Object,
      default: () => {
        return {}
      },
    },
    isScan: {
      type: Boolean,
      default: false,
    },
    flag: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      errUrl: require('../../../static/images/Pharmacy-default.png'),
      oldQuan: '',
    }
  },
  async created() {
    // await this.initDrugRules()
  },
  methods: {
    ...mapActions('shop', ['changeDrugQuan']),
    getDrugTypeColor(item) {
      const map = {
        '025.6': 'rgb(244, 92, 98)',
        '025.7': 'rgb(105, 211, 161)',
        '025.8': '#FFB800',
        '025.9': 'rgb(107, 202, 215)',
      }
      return `background:${map[item.drugType]} !important`
    },


    add() {
      const addQuantity = this.getAddQuantity(this.item.drugId, this.item.quan)
      console.log('addQuantity', addQuantity)
      this.$emit('add', this.item, this.index, addQuantity)
    },
    reduce() {
      const reduceQuantity = this.getReduceQuantity(this.item.drugId, this.item.quan)
      this.$emit('reduce', this.item, this.index, reduceQuantity)
    },
    // 数量输入框失焦事件
    onQuantityBlur(e) {
      if (this.item.drugId === '72d263bd0ebd48e49e9063320efa0ff7') {
        const value = Number(e.detail.value)
        if (isNaN(value)) {
          // 如果输入无效，重置为0
          this.item.quan = 0
          this.item.inputQuan = 0
          this.changeDrugQuan({ ...this.item, amount: 0 })
          return
        }
        // 如果输入大于等于30，设为30
        if (value >= 30) {
          showModal(
            '',
            '同一就诊人限购 30 盒，若需购买30盒以上，请重新扫码添加新就诊人再次下单、或在补充就诊人信息页面切换/添加新就诊人。',
            '我已知晓，关闭',
            '取消',
            false
          )
          this.item.quan = 30
          this.item.inputQuan = 30
          this.changeDrugQuan({ ...this.item, amount: 30 })
          return
        }
        if (value > this.item.drugKc) {
          uni.showToast({
            title: '已达库存上限',
            icon: 'none',
          })
          this.item.quan = this.item.drugKc
          this.item.inputQuan = this.item.drugKc
          this.changeDrugQuan({ ...this.item, amount: this.item.drugKc })
          return
        }
        // 根据特殊规则处理数量
        if (value === 0) {
          // 如果输入为0，清零
          this.item.quan = 0
          this.item.inputQuan = 0
          this.changeDrugQuan({ ...this.item, amount: 0 })
        } else if (value > 0 && value < 5) {
          // 如果输入大于0但小于5，设置为0
          // 清零，即减去当前全部数量
          this.item.quan = 0
          this.item.inputQuan = 0
          this.changeDrugQuan({ ...this.item, amount: 0 })
        } else if (value >= 5) {
          // 如果输入大于等于5，正常设置数量
          this.item.quan = value
          this.item.inputQuan = value
          this.changeDrugQuan({ ...this.item, amount: value })
        }
      }
    },
    toDrugDetail(item) {
      if (this.flag == 'artcle') {
        uni.navigateTo({
          url: `/pages/shop/detail/drugArticl?id=${item.drugId}`,
        })
        return
      }
      this.$store.commit('shop/SET_DrugDetailStore', {
        drugstoreName: item.drugstoreName,
        drugstoreId: item.drugstoreId,
      })
      let { drugKc, quan, yfkcId, drugId } = item
      uni.navigateTo({
        url:
          '/pages/shop/detail/drug?id=' +
          drugId +
          '&drugKc=' +
          drugKc +
          '&quan=' +
          quan +
          '&yfkcId=' +
          yfkcId +
          '&drugstoreName=' +
          item.drugstoreName +
          '&drugstoreId=' +
          item.drugstoreId +
          `&isScan=${this.isScan ? 1 : 0}`,
      })
    },
  },
}
</script>

<style lang="scss" scoped>

.drug_card {
  width: 100%;
  padding: 24rpx;
  background-color: #fafafa;
  border-radius: 16rpx;
  margin-bottom: 20rpx;

  .card {
    @include flex;

    .card_img {
      width: 128rpx;
      height: 128rpx;
      border-radius: 8rpx;
      object-fit: cover;
      flex: none;
    }

    .card_text {
      overflow: hidden;
      flex: 1;
      min-height: 128rpx;
      padding-left: 24rpx;
      @include flex(lr);
      flex-direction: column;
      align-items: stretch;

      .text_title {
        font-size: 28rpx;
        font-weight: bold;
      }

      .text_info {
        font-size: 24rpx;
        color: #999;
      }

      .active {
        color: red;
        font-size: 24rpx;
      }

      .text_price {
        font-size: 28rpx;
        color: #ff3b30;
        font-weight: bold;

        text {
          font-size: 24rpx;
          font-weight: normal;
          color: #999;
        }
      }
    }
  }

  .drug_action {
    margin-top: 10rpx;
    @include flex(lr);
    height: 40rpx;

    .count {
      min-width: 128rpx;
      text-align: center;
      font-size: 20rpx;
      color: #999;
    }

    .action {
      @include flex;

      .icon {
        width: 32rpx;
        height: 32rpx;
      }

      .num {
        width: 60rpx;
        height: 36rpx;
        @include flex;
        font-size: 28rpx;
        background-color: #fff;
        margin: 0 16rpx;
        border-radius: 4rpx;
      }
    }
  }
}
.drugTypeName {
  white-space: nowrap;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: red;
  color: white;
  padding: 0 7px;
  border-radius: 6px;
  margin-right: 5px;
  height: 18px;
}
.letter {
  // 超出一行自动省略号
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.drug-coverUp {
  width: 128rpx;
  height: 128rpx;
  background: rgba(255, 255, 255, 0.5);
  position: absolute;
  top: 0;
  left: 0;
}
.cfCover {
  text-align: center;
  font-size: 10px;
  background: none !important;
  display: flex;
  align-items: center;
}
.gx {
  filter: blur(2px);
}
.text_detail {
  width: 80px;
  color: white;
  text-align: center;
  background: linear-gradient(135deg, #516afb 0%, #859bff 99.97%);
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 12px;
}
</style>
