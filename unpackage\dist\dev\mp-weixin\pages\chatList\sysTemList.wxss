/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*
	字体颜色相关
*/
/*
	背景色 分割线色
*/
/*
	主题色
*/
/*
		渐变可调角度
*/
/*
	定义弹性布局
 */
/*
	单行文本溢出隐藏
*/
.empty.data-v-254fe4bf {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  margin-top: 320rpx;
}
.content-container.data-v-254fe4bf {
  height: calc(100vh - 100px);
}
.admibMsg.data-v-254fe4bf {
  width: 90%;
  margin: auto;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
  background: #ffffff;
  padding: 32rpx;
  border-radius: 8rpx;
  border-bottom: 1px solid #ebebeb;
  border-radius: 5px;
  background: white;
  box-shadow: 0px 1px 4px rgba(121, 72, 234, 0.15);
}
.admibMsg .leftImg.data-v-254fe4bf {
  position: relative;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.admibMsg .leftImg .unread.data-v-254fe4bf {
  width: 14rpx;
  height: 14rpx;
  border-radius: 50%;
  position: absolute;
  right: 0px;
  top: 0px;
  background: #ff5050;
}
.admibMsg .contentDiv.data-v-254fe4bf {
  overflow: hidden;
}
.admibMsg .tips.data-v-254fe4bf {
  width: 80rpx;
  height: 62rpx;
  object-fit: contain;
  border-radius: 8rpx;
}
.admibMsg .title.data-v-254fe4bf {
  color: #333333;
  font-size: 15px;
  font-weight: 600;
}
.admibMsg .content.data-v-254fe4bf {
  font-size: 14px;
  color: #333333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #666666;
  margin-top: 12rpx;
}
.admibMsg .time.data-v-254fe4bf {
  color: #999999;
  font-size: 12px;
}
.emptyImg.data-v-254fe4bf {
  width: 286rpx;
  height: 226rpx;
  margin-bottom: 40rpx;
}