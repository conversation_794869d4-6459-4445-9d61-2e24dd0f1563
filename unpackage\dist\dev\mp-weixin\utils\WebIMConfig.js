"use strict";
const store_index = require("../store/index.js");
const config = {
  xmppURL: "wss://im-api-wechat.easemob.com/websocket",
  apiURL: "https://a1.easemob.com",
  appkey: store_index.store.getters.allEnv[store_index.store.getters.envKey].VUE_APP_HXKEY,
  Host: "easemob.com",
  https: false,
  isHttpDNS: true,
  isMultiLoginSessions: true,
  isWindowSDK: false,
  isSandBox: false,
  isDebug: false,
  autoReconnectNumMax: 5,
  autoReconnectInterval: 2,
  /**
   * webrtc supports WebKit and https only
   */
  isWebRTC: false,
  /*
   * Set to auto sign-in
   */
  isAutoLogin: true
};
exports.config = config;
