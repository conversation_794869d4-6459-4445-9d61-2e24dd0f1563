"use strict";
const plugins_xeUtils_isArray = require("./isArray.js");
const plugins_xeUtils_arrayEach = require("./arrayEach.js");
function flattenDeep(array, deep) {
  var result = [];
  plugins_xeUtils_arrayEach.arrayEach(array, function(vals) {
    result = result.concat(plugins_xeUtils_isArray.isArray(vals) ? deep ? flattenDeep(vals, deep) : vals : [vals]);
  });
  return result;
}
function flatten(array, deep) {
  if (plugins_xeUtils_isArray.isArray(array)) {
    return flattenDeep(array, deep);
  }
  return [];
}
exports.flatten = flatten;
