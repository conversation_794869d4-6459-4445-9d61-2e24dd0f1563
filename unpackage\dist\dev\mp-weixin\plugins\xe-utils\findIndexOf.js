"use strict";
const plugins_xeUtils_helperCreateiterateIndexOf = require("./helperCreateiterateIndexOf.js");
var findIndexOf = plugins_xeUtils_helperCreateiterateIndexOf.helperCreateiterateIndexOf(function(obj, iterate, context) {
  for (var index = 0, len = obj.length; index < len; index++) {
    if (iterate.call(context, obj[index], index, obj)) {
      return index;
    }
  }
  return -1;
});
exports.findIndexOf = findIndexOf;
