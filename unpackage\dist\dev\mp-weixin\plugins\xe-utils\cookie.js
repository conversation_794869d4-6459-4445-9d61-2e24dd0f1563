"use strict";
const plugins_xeUtils_setupDefaults = require("./setupDefaults.js");
const plugins_xeUtils_staticDocument = require("./staticDocument.js");
const plugins_xeUtils_staticDecodeURIComponent = require("./staticDecodeURIComponent.js");
const plugins_xeUtils_staticEncodeURIComponent = require("./staticEncodeURIComponent.js");
const plugins_xeUtils_isArray = require("./isArray.js");
const plugins_xeUtils_isObject = require("./isObject.js");
const plugins_xeUtils_isDate = require("./isDate.js");
const plugins_xeUtils_isUndefined = require("./isUndefined.js");
const plugins_xeUtils_includes = require("./includes.js");
const plugins_xeUtils_keys = require("./keys.js");
const plugins_xeUtils_assign = require("./assign.js");
const plugins_xeUtils_arrayEach = require("./arrayEach.js");
const plugins_xeUtils_helperNewDate = require("./helperNewDate.js");
const plugins_xeUtils_helperGetDateTime = require("./helperGetDateTime.js");
const plugins_xeUtils_getWhatYear = require("./getWhatYear.js");
const plugins_xeUtils_getWhatMonth = require("./getWhatMonth.js");
const plugins_xeUtils_getWhatDay = require("./getWhatDay.js");
function toCookieUnitTime(unit, expires) {
  var num = parseFloat(expires);
  var nowdate = plugins_xeUtils_helperNewDate.helperNewDate();
  var time = plugins_xeUtils_helperGetDateTime.helperGetDateTime(nowdate);
  switch (unit) {
    case "y":
      return plugins_xeUtils_helperGetDateTime.helperGetDateTime(plugins_xeUtils_getWhatYear.getWhatYear(nowdate, num));
    case "M":
      return plugins_xeUtils_helperGetDateTime.helperGetDateTime(plugins_xeUtils_getWhatMonth.getWhatMonth(nowdate, num));
    case "d":
      return plugins_xeUtils_helperGetDateTime.helperGetDateTime(plugins_xeUtils_getWhatDay.getWhatDay(nowdate, num));
    case "h":
    case "H":
      return time + num * 60 * 60 * 1e3;
    case "m":
      return time + num * 60 * 1e3;
    case "s":
      return time + num * 1e3;
  }
  return time;
}
function toCookieUTCString(date) {
  return (plugins_xeUtils_isDate.isDate(date) ? date : new Date(date)).toUTCString();
}
function cookie(name, value, options) {
  if (plugins_xeUtils_staticDocument.staticDocument) {
    var opts, expires, values, result, cookies, keyIndex;
    var inserts = [];
    var args = arguments;
    if (plugins_xeUtils_isArray.isArray(name)) {
      inserts = name;
    } else if (args.length > 1) {
      inserts = [plugins_xeUtils_assign.assign({ name, value }, options)];
    } else if (plugins_xeUtils_isObject.isObject(name)) {
      inserts = [name];
    }
    if (inserts.length > 0) {
      plugins_xeUtils_arrayEach.arrayEach(inserts, function(obj) {
        opts = plugins_xeUtils_assign.assign({}, plugins_xeUtils_setupDefaults.setupDefaults.cookies, obj);
        values = [];
        if (opts.name) {
          expires = opts.expires;
          values.push(plugins_xeUtils_staticEncodeURIComponent.staticEncodeURIComponent(opts.name) + "=" + plugins_xeUtils_staticEncodeURIComponent.staticEncodeURIComponent(plugins_xeUtils_isObject.isObject(opts.value) ? JSON.stringify(opts.value) : opts.value));
          if (expires) {
            if (isNaN(expires)) {
              expires = expires.replace(/^([0-9]+)(y|M|d|H|h|m|s)$/, function(text, num, unit) {
                return toCookieUTCString(toCookieUnitTime(unit, num));
              });
            } else if (/^[0-9]{11,13}$/.test(expires) || plugins_xeUtils_isDate.isDate(expires)) {
              expires = toCookieUTCString(expires);
            } else {
              expires = toCookieUTCString(toCookieUnitTime("d", expires));
            }
            opts.expires = expires;
          }
          plugins_xeUtils_arrayEach.arrayEach(["expires", "path", "domain", "secure"], function(key) {
            if (!plugins_xeUtils_isUndefined.isUndefined(opts[key])) {
              values.push(opts[key] && key === "secure" ? key : key + "=" + opts[key]);
            }
          });
        }
        plugins_xeUtils_staticDocument.staticDocument.cookie = values.join("; ");
      });
      return true;
    } else {
      result = {};
      cookies = plugins_xeUtils_staticDocument.staticDocument.cookie;
      if (cookies) {
        plugins_xeUtils_arrayEach.arrayEach(cookies.split("; "), function(val) {
          keyIndex = val.indexOf("=");
          result[plugins_xeUtils_staticDecodeURIComponent.staticDecodeURIComponent(val.substring(0, keyIndex))] = plugins_xeUtils_staticDecodeURIComponent.staticDecodeURIComponent(val.substring(keyIndex + 1) || "");
        });
      }
      return args.length === 1 ? result[name] : result;
    }
  }
  return false;
}
function hasCookieItem(value) {
  return plugins_xeUtils_includes.includes(cookieKeys(), value);
}
function getCookieItem(name) {
  return cookie(name);
}
function setCookieItem(name, value, options) {
  cookie(name, value, options);
  return cookie;
}
function removeCookieItem(name, options) {
  cookie(name, "", plugins_xeUtils_assign.assign({ expires: -1 }, plugins_xeUtils_setupDefaults.setupDefaults.cookies, options));
}
function cookieKeys() {
  return plugins_xeUtils_keys.keys(cookie());
}
function cookieJson() {
  return cookie();
}
plugins_xeUtils_assign.assign(cookie, {
  has: hasCookieItem,
  set: setCookieItem,
  setItem: setCookieItem,
  get: getCookieItem,
  getItem: getCookieItem,
  remove: removeCookieItem,
  removeItem: removeCookieItem,
  keys: cookieKeys,
  getJSON: cookieJson
});
exports.cookie = cookie;
