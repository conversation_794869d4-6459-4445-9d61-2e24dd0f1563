"use strict";
const plugins_xeUtils_hasOwnProp = require("./hasOwnProp.js");
const plugins_xeUtils_isArray = require("./isArray.js");
function helperCreateIterateHandle(prop, useArray, restIndex, matchValue, defaultValue) {
  return function(obj, iterate, context) {
    if (obj && iterate) {
      if (prop && obj[prop]) {
        return obj[prop](iterate, context);
      } else {
        if (useArray && plugins_xeUtils_isArray.isArray(obj)) {
          for (var index = 0, len = obj.length; index < len; index++) {
            if (!!iterate.call(context, obj[index], index, obj) === matchValue) {
              return [true, false, index, obj[index]][restIndex];
            }
          }
        } else {
          for (var key in obj) {
            if (plugins_xeUtils_hasOwnProp.hasOwnProp(obj, key)) {
              if (!!iterate.call(context, obj[key], key, obj) === matchValue) {
                return [true, false, key, obj[key]][restIndex];
              }
            }
          }
        }
      }
    }
    return defaultValue;
  };
}
exports.helperCreateIterateHandle = helperCreateIterateHandle;
