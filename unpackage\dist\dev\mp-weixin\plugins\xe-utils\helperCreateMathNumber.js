"use strict";
const plugins_xeUtils_helperMultiply = require("./helperMultiply.js");
const plugins_xeUtils_toNumber = require("./toNumber.js");
const plugins_xeUtils_toNumberString = require("./toNumberString.js");
function helperCreateMathNumber(name) {
  return function(num, digits) {
    var numRest = plugins_xeUtils_toNumber.toNumber(num);
    var rest = numRest;
    if (numRest) {
      digits = digits >> 0;
      var numStr = plugins_xeUtils_toNumberString.toNumberString(numRest);
      var nums = numStr.split(".");
      var intStr = nums[0];
      var floatStr = nums[1] || "";
      var fStr = floatStr.substring(0, digits + 1);
      var subRest = intStr + (fStr ? "." + fStr : "");
      if (digits >= floatStr.length) {
        return plugins_xeUtils_toNumber.toNumber(subRest);
      }
      subRest = numRest;
      if (digits > 0) {
        var ratio = Math.pow(10, digits);
        rest = Math[name](plugins_xeUtils_helperMultiply.helperMultiply(subRest, ratio)) / ratio;
      } else {
        rest = Math[name](subRest);
      }
    }
    return rest;
  };
}
exports.helperCreateMathNumber = helperCreateMathNumber;
