/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*
	字体颜色相关
*/
/*
	背景色 分割线色
*/
/*
	主题色
*/
/*
		渐变可调角度
*/
/*
	定义弹性布局
 */
/*
	单行文本溢出隐藏
*/
:root {
  --textColor: #337fff;
}
.su-title-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}
.adaptive-image {
  width: 100%;
  height: auto;
}
.uni-fab {
  bottom: 160rpx !important;
}
.uni-fab__plus {
  bottom: 160rpx !important;
}
 p {
  text-wrap: wrap !important;
}
 rich-text {
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
  /* 标准属性 */
  overflow: hidden;
}
view {
  white-space: pre-wrap;
}
text {
  white-space: pre-wrap;
}
 .van-cell {
  line-height: 20rpx !important;
}page{--status-bar-height:25px;--top-window-height:0px;--window-top:0px;--window-bottom:0px;--window-left:0px;--window-right:0px;--window-magin:0px}[data-c-h="true"]{display: none !important;}