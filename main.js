import App from './App'
import './interceptor'
import { createSSRApp } from 'vue'
import store from './store'
import XEUtils from '@/plugins/xe-utils'
import myJsTools from './common/js/myJsTools.js'
import UniImage from '@/components/common/UniImage.vue'
//解决控制台 touchstart 事件警告
import 'default-passive-events'
export function createApp() {
	const app = createSSRApp(App)
	app.use(store)
	app.config.globalProperties.$xeu = XEUtils
	uni.$xeu = XEUtils

	// 全局注册 UniImage 组件
	app.component('UniImage', UniImage)

	// 注意：v-img 指令已被 UniImage 组件替代，如果确认所有地方都已迁移完成，可以移除下面的指令定义
	// 自定义指令 异步获取图片（已废弃，请使用 UniImage 组件）
	// app.directive('img', {
	//   bind(el, binding) {
	//     console.log(binding.value)
	//     if (!binding.value || binding.value == 'null') return
	//     myJsTools.downAndSaveImg(
	//       binding.value,
	//       (url) => {
	//         if (!url) {
	//           el.src = el.dataset.src
	//           return
	//         }
	//         el.src = url
	//         // 如果存在参数 添加点击事件预览
	//         if (binding.arg == 'click')
	//           el.onclick = (e) => {
	//             // 阻止事件继续传播
	//             e.stopPropagation()
	//             myJsTools.previewImg(el.src)
	//           }
	//       },
	//       () => {
	//         let src = el.dataset.src
	//         if (src) el.src = src
	//       }
	//     )
	//   },
	// })
	return {
		app
	}
}