"use strict";
const plugins_xeUtils_arrayEach = require("./arrayEach.js");
const plugins_xeUtils_keys = require("./keys.js");
const plugins_xeUtils_isArray = require("./isArray.js");
const plugins_xeUtils_clone = require("./clone.js");
var objectAssignFns = Object.assign;
function handleAssign(destination, args, isClone) {
  var len = args.length;
  for (var source, index = 1; index < len; index++) {
    source = args[index];
    plugins_xeUtils_arrayEach.arrayEach(plugins_xeUtils_keys.keys(args[index]), isClone ? function(key) {
      destination[key] = plugins_xeUtils_clone.clone(source[key], isClone);
    } : function(key) {
      destination[key] = source[key];
    });
  }
  return destination;
}
var assign = function(target) {
  if (target) {
    var args = arguments;
    if (target === true) {
      if (args.length > 1) {
        target = plugins_xeUtils_isArray.isArray(target[1]) ? [] : {};
        return handleAssign(target, args, true);
      }
    } else {
      return objectAssignFns ? objectAssignFns.apply(Object, args) : handleAssign(target, args);
    }
  }
  return target;
};
exports.assign = assign;
