"use strict";
const plugins_xeUtils_helperCreateTreeFunc = require("./helperCreateTreeFunc.js");
const plugins_xeUtils_arrayEach = require("./arrayEach.js");
const plugins_xeUtils_assign = require("./assign.js");
function searchTreeItem(parentAllow, parent, obj, iterate, context, path, node, parseChildren, opts) {
  var paths, nodes, rest, isAllow, hasChild;
  var rests = [];
  var hasOriginal = opts.original;
  var sourceData = opts.data;
  var mapChildren = opts.mapChildren || parseChildren;
  plugins_xeUtils_arrayEach.arrayEach(obj, function(item, index) {
    paths = path.concat(["" + index]);
    nodes = node.concat([item]);
    isAllow = parentAllow || iterate.call(context, item, index, obj, paths, parent, nodes);
    hasChild = parseChildren && item[parseChildren];
    if (isAllow || hasChild) {
      if (hasOriginal) {
        rest = item;
      } else {
        rest = plugins_xeUtils_assign.assign({}, item);
        if (sourceData) {
          rest[sourceData] = item;
        }
      }
      rest[mapChildren] = searchTreeItem(isAllow, item, item[parseChildren], iterate, context, paths, nodes, parseChildren, opts);
      if (isAllow || rest[mapChildren].length) {
        rests.push(rest);
      }
    } else if (isAllow) {
      rests.push(rest);
    }
  });
  return rests;
}
var searchTree = plugins_xeUtils_helperCreateTreeFunc.helperCreateTreeFunc(function(parent, obj, iterate, context, path, nodes, parseChildren, opts) {
  return searchTreeItem(0, parent, obj, iterate, context, path, nodes, parseChildren, opts);
});
exports.searchTree = searchTree;
