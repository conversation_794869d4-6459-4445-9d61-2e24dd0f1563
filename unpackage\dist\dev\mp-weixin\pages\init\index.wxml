<view class="page data-v-f48d61de"><view class="initial-container data-v-f48d61de"><view class="initial-content data-v-f48d61de" bindtap="{{b}}"><image class="data-v-f48d61de" src="{{a}}" mode="widthFix"></image><view class="text data-v-f48d61de">神州(天津)互联网医院</view></view></view><prop-center wx:if="{{c}}" class="data-v-f48d61de" u-s="{{['d']}}" bindconfirmPropCenter="{{d}}" u-i="f48d61de-0" bind:__l="__l"> 网络异常，请退出程序，重新进入 </prop-center></view>