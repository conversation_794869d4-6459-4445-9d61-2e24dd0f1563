"use strict";
const plugins_xeUtils_isArray = require("./isArray.js");
const plugins_xeUtils_arrayEach = require("./arrayEach.js");
const plugins_xeUtils_objectEach = require("./objectEach.js");
function each(obj, iterate, context) {
  if (obj) {
    return (plugins_xeUtils_isArray.isArray(obj) ? plugins_xeUtils_arrayEach.arrayEach : plugins_xeUtils_objectEach.objectEach)(obj, iterate, context);
  }
  return obj;
}
exports.each = each;
