<template>
  <div class="invoice">
    <!-- 开关 -->
    <div class="top" v-if="!id && !orderNo">
      <text>是否开票</text>
      <switch
        :checked="isNext"
        style="transform:scale(0.7)"
        @change="switchChange"
      />
    </div>

    <div class="list" v-show="isNext">
      <div class="history">
        <span>开票历史</span>

        <p @click="remove">
          <image src="/static/images/search/del.png" />
          <span>清空</span>
        </p>
      </div>

      <block v-if="false">
        <div
          class="item"
          v-for="(item, index) in list"
          :key="index"
          @click="setPrevVal(item)"
        >
          <p>
            <span>{{ item.invoiceType == 1 ? '个人' : '企业' }}</span>
            {{ item.invoiceTitle }}
          </p>
        </div>
      </block>

      <block v-else>
        <div
          class="item"
          v-for="(it, index) in list"
          :key="index"
          @click="setItem(it)"
        >
          <p>
            <span>{{ it.invoiceType == 1 ? '个人' : '企业' }}</span>
            {{ it.invoiceTitle }}
          </p>
          <span class="icon" v-if="item === JSON.stringify(it)"></span>
        </div>
      </block>
    </div>

    <!-- 提交 -->
    <FOOTER @click="send(true)" v-if="!id && !orderNo && !isNext"
      >确认提交</FOOTER
    >

    <!-- 更新 -->
    <FOOTER @click="send" showLeft @leftClick="toAdd" v-else>
      <template #left>添加抬头</template>
      确认提交
    </FOOTER>
  </div>
</template>

<script>
import { getList, setIsNext, getIsNext, clear } from './store';
import { updateOrderInvoice } from '@/api/shop';
import { Toast } from '@/common/js/pay';
import FOOTER from '@/components/footer_button/button.vue';

export default {
  name: 'Invoice',
  components: { FOOTER },
  data() {
    return {
      // 发票列表
      list: [],
      // 是否开票
      isNext: false,
      // 订单来
      orderNo: '',
      // 订单详情 id
      id: '',
      // 已选
      item: '',
      invoiceType: '',
      invoiceTitle: '',
    };
  },
  onLoad(v) {
    this.orderNo = v.orderNo || '';
    this.id = v.id || '';
    const { invoiceTitle, invoiceType } = v;
    this.invoiceType = invoiceType;
    this.invoiceTitle = invoiceTitle;
    this.isNext = getIsNext();
    if (this.id || this.orderNo) {
      this.isNext = true;
    }
  },
  onShow() {
    this.list = getList();
    // 如果存在
    if (this.invoiceTitle) {
      let obj = this.list.find(
        (item) => item.invoiceTitle === this.invoiceTitle
      );
      if (obj) {
        this.item = JSON.stringify(obj);
      }
    }
  },
  methods: {
    // 清空
    remove() {
      uni.showModal({
        title: '是否要清空所有发票信息',
        success(res) {
          clear();
          this.list = [];
        },
      });
    },
    setItem(item) {
      let obj = JSON.stringify(item);
      // 取消选中
      if (obj === this.item) {
        this.item = '';
      } else {
        this.item = JSON.stringify(item);
      }
    },
    send(isSend) {
      if (!this.item && isSend) {
        this.setPrevVal({
          invoiceType: 3,
          invoiceTitle: '',
          invoiceMemo: '',
          invoiceTaxNo: '',
        });
        return;
      }

      if (!this.item) {
        Toast('请选择发票信息');
        return;
      }

      let val = JSON.parse(this.item);

      if (!this.id && !this.orderNo) {
        this.setPrevVal(val);
        return;
      }
      if (this.id) {
        this.setPrevVal(val);
      } else {
        this.upOrder(val);
      }
    },
    // 更新订单发票
    async upOrder() {
      if (!this.item) return;
      const val = JSON.parse(this.item);
      val.orderNo = this.orderNo;
      await updateOrderInvoice(val);
      let arr = getCurrentPages();
      let prev = arr[arr.length - 2];
      let index = prev.list.findIndex((item) => item.orderNo == this.orderNo);
      let item = prev.list[index];
      item.invoicetitle = '发票抬头';
      prev.$set(prev.list, index, item);
      uni.navigateBack();
    },
    // 开关变化
    switchChange(e) {
      let isNext = e.target.value;
      this.isNext = isNext;
      setIsNext(isNext);
      // 如果关闭
      if (!isNext) {
        if (!this.orderNo) this.setPrevVal({});
      }
    },
    // 设置上一页发票
    async setPrevVal(val) {
      if (this.id) {
        val.orderNo = this.id;
        await updateOrderInvoice(val);
      }
      let arr = getCurrentPages();
      let prev = arr[arr.length - 2];
      if (prev) {
        prev.invoice = val;
      }
      if (val.invoiceType) uni.navigateBack();
    },
    // 跳转新增
    toAdd() {
      uni.navigateTo({
        url: '/pages/shop/invoice/add',
      });
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background-color: #fff;
}
.top {
  height: 92rpx;
  @include flex(lr);
  font-size: 28rpx;
  padding: 0 32rpx;
}

.list {
  .history {
    background-color: #f5f5f5;
    padding: 0 32rpx;
    height: 92rpx;
    @include flex(lr);
    font-size: 28rpx;
    color: #666;

    p {
      @include flex;

      image {
        width: 32rpx;
        height: 32rpx;
      }

      span {
        font-size: 24rpx;
        color: #999;
      }
    }
  }

  .item {
    height: 92rpx;
    @include flex(lr);
    font-size: 26rpx;
    color: #999;
    padding: 0 32rpx;
    border-bottom: 1px solid #f5f5f5;

    p {
      @include flex(left);

      span {
        font-size: 28rpx;
        width: 130rpx;
        flex: none;
      }
    }

    .icon {
      width: 20rpx;
      height: 10rpx;
      border-left: 6rpx solid $k-theme-color;
      border-bottom: 6rpx solid $k-theme-color;
      transform: rotate(-45deg);
    }
  }
}
</style>
