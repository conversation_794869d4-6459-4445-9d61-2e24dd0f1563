"use strict";
const plugins_xeUtils_helperNumberDecimal = require("./helperNumberDecimal.js");
const plugins_xeUtils_toNumberString = require("./toNumberString.js");
const plugins_xeUtils_multiply = require("./multiply.js");
function helperNumberAdd(addend, augend) {
  var str1 = plugins_xeUtils_toNumberString.toNumberString(addend);
  var str2 = plugins_xeUtils_toNumberString.toNumberString(augend);
  var ratio = Math.pow(10, Math.max(plugins_xeUtils_helperNumberDecimal.helperNumberDecimal(str1), plugins_xeUtils_helperNumberDecimal.helperNumberDecimal(str2)));
  return (plugins_xeUtils_multiply.multiply(addend, ratio) + plugins_xeUtils_multiply.multiply(augend, ratio)) / ratio;
}
exports.helperNumberAdd = helperNumberAdd;
