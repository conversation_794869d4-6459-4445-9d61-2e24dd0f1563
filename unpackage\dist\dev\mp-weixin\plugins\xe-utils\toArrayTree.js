"use strict";
const plugins_xeUtils_setupDefaults = require("./setupDefaults.js");
const plugins_xeUtils_each = require("./each.js");
const plugins_xeUtils_orderBy = require("./orderBy.js");
const plugins_xeUtils_clone = require("./clone.js");
const plugins_xeUtils_eqNull = require("./eqNull.js");
const plugins_xeUtils_remove = require("./remove.js");
const plugins_xeUtils_assign = require("./assign.js");
function strictTree(array, optChildren) {
  plugins_xeUtils_each.each(array, function(item) {
    if (item[optChildren] && !item[optChildren].length) {
      plugins_xeUtils_remove.remove(item, optChildren);
    }
  });
}
function toArrayTree(array, options) {
  var opts = plugins_xeUtils_assign.assign({}, plugins_xeUtils_setupDefaults.setupDefaults.treeOptions, options);
  var optStrict = opts.strict;
  var optKey = opts.key;
  var optParentKey = opts.parentKey;
  var optChildren = opts.children;
  var optMapChildren = opts.mapChildren;
  var optSortKey = opts.sortKey;
  var optReverse = opts.reverse;
  var optData = opts.data;
  var result = [];
  var treeMap = {};
  var idsMap = {};
  var id, treeData, parentId;
  if (optSortKey) {
    array = plugins_xeUtils_orderBy.orderBy(plugins_xeUtils_clone.clone(array), optSortKey);
    if (optReverse) {
      array = array.reverse();
    }
  }
  plugins_xeUtils_each.each(array, function(item) {
    id = item[optKey];
    idsMap[id] = true;
  });
  plugins_xeUtils_each.each(array, function(item) {
    id = item[optKey];
    if (optData) {
      treeData = {};
      treeData[optData] = item;
    } else {
      treeData = item;
    }
    parentId = item[optParentKey];
    treeMap[id] = treeMap[id] || [];
    treeMap[parentId] = treeMap[parentId] || [];
    treeMap[parentId].push(treeData);
    treeData[optKey] = id;
    treeData[optParentKey] = parentId;
    treeData[optChildren] = treeMap[id];
    if (optMapChildren) {
      treeData[optMapChildren] = treeMap[id];
    }
    if (!optStrict || optStrict && plugins_xeUtils_eqNull.eqNull(parentId)) {
      if (!idsMap[parentId]) {
        result.push(treeData);
      }
    }
  });
  if (optStrict) {
    strictTree(array, optChildren);
  }
  return result;
}
exports.toArrayTree = toArrayTree;
