const getters = {
  envKey: (state) => state.global.envKey,
  allEnv: (state) => state.global.allEnv,
   // 消息撤回
   msgRecall: (state) => state.msgRecall,
   // 接诊状态
   getJzStatus: (state) => state.jz_status,
   // 测试视频通话
   getVideoObj: (state) => state.videoObj,
   // 获取系统通知详情
   getNotice: (state) => state.notice_detail,
   // 聊天列表
   getChatList: (state) => state.chatList,
   // 医生列表
   getChatListDoc: (state) => state.chatListDoc,
   // appid
   getAppId: (state) => state.appId,
   // 微信信息
   getWxInfo: (state) => state.wxInfo,
   // 用户信息
   getProPfInfo: (state) => state.proPfInfo,
   // 全局参数
   getWholeArg: (state) => state.wholeArg,
   // 购物车
   shopList: (state) => state.shop.shopList,
   // 购物价位
   totalMoney: (state) => state.shop.totalMoney,
   // 药品数量
   drugNum: (state) => state.shop.drugNum,
   // 失效药
   unableList: (state) => state.shop.unableList,
   // 已选药店id列表
   drugStoreList: (state) => state.shop.drugStoreList,
   diagList: (state) => state.diagList,
   drugList: (state) => state.drugList,
   drugRules: (state) => state.drugRules,
   checkStore: (state) => state.shop.checkStore,
   getDrugDetailStore: (state) => state.shop.drugDetailStore,
   scanCheckDrugList: (state) => state.shop.scanCheckDrugList,
   getGroupList: (state) => state.groupChatInfo,
   getGroupChatAllList: (state) => state.groupChatAllList,
   getSelectedOrders: (state) => state.selectedOrders,
};
export default getters;
