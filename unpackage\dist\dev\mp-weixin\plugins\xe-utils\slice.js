"use strict";
const plugins_xeUtils_toNumber = require("./toNumber.js");
function slice(array, startIndex, endIndex) {
  var result = [];
  var argsSize = arguments.length;
  if (array) {
    startIndex = argsSize >= 2 ? plugins_xeUtils_toNumber.toNumber(startIndex) : 0;
    endIndex = argsSize >= 3 ? plugins_xeUtils_toNumber.toNumber(endIndex) : array.length;
    if (array.slice) {
      return array.slice(startIndex, endIndex);
    }
    for (; startIndex < endIndex; startIndex++) {
      result.push(array[startIndex]);
    }
  }
  return result;
}
exports.slice = slice;
