<template>
  <!-- 智能导诊 -->
  <view class="guidance">
    <!-- 记录 -->
    <view class="cont_list">
      <!-- 左侧助手消息 -->
      <GUIDTEXT>
        <!-- 标题 -->
        <view class="tip_title">
          <image
            class="tip_icon"
            src="/static/images/guidance/ws.png"
            mode="aspectFill"
          />
          <text>我们竭诚为您服务</text>
        </view>
        <!-- 选项 -->
        <view class="list_opt">
          <!-- 单个 -->
          <view
            class="list_opt_item"
            v-for="(item, index) in menu"
            :key="index"
            @click="start(item)"
          >
            <view class="opt_left">
              <text class="left_title">{{ item.diagnosisTitle }}</text>
              <text class="left_info">{{ item.diagnosisInfo }}</text>
            </view>
            <!-- 箭头 -->
            <uni-icons type="arrowright" color="#333" size="20"></uni-icons>
          </view>
        </view>
      </GUIDTEXT>

      <block v-for="item in list">
        <!-- 左侧系统消息 -->
        <GUIDTEXT v-if="!item.isUser">{{ item.msg }}</GUIDTEXT>
        <!-- 右侧用户消息 -->
        <USERTEXT v-else>
          <block v-if="!item.rate">
            {{ item.msg }}
          </block>
          <block v-else>
            <rate
              class="rate"
              size="30"
              :value="item.msg"
              disabled
              :star_empty="empt_src"
              :star_fill="act_src"
            ></rate>
          </block>
        </USERTEXT>
      </block>

      <!-- 建议 -->
      <PROPOSAL v-if="proposal" :proposal="proposal" />

      <!-- 疾病 -->
      <DISEASE v-if="diseaseList.length" :list="diseaseList" />

      <!-- 医生列表 -->
      <DOCLIST v-if="docList && docList.length" :tip="tip" :list="docList" />
    </view>

    <!-- 底部消息 -->
    <view class="footer_opt" v-if="lastMsg.aftertype">
      <!-- 填空 -->
      <view class="type_input" v-if="lastMsg.aftertype == 2">
        <input
          type="text"
          v-model="input"
          :placeholder="lastMsg.afterQuestionTitle"
          confirmType="send"
        />
        <button @click="sendInput">发送</button>
      </view>

      <!-- 评价 -->
      <view class="type_rate" v-if="lastMsg.aftertype == 3">
        <rate
          class="rate"
          @change="rateChange"
          size="44"
          :star_empty="empt_src"
          :star_fill="act_src"
        ></rate>
        <button @click="sendRate">发送</button>
      </view>

      <!-- 单选 -->
      <view class="type_radio" v-if="lastMsg.aftertype == 1">
        <view
          class="radio_item"
          v-for="(item, key) in lastMsg.afterAnswer"
          :key="key"
          @click="setRadio(item, key)"
          >{{ item }}</view
        >
      </view>

      <!-- 多选 -->
      <view class="type_check" v-if="lastMsg.aftertype == 5">
        <view
          class="check_item"
          :class="checkList.includes(key) ? 'act' : ''"
          v-for="(item, key) in lastMsg.afterAnswer"
          :key="key"
          @click="setCheck(item, key)"
          >{{ item }}</view
        >
      </view>

      <!-- 多选按钮 -->
      <button
        class="check_but"
        @click="sendCheck"
        v-if="lastMsg.aftertype == 5"
      >
        确定
      </button>
    </view>
  </view>
</template>

<script>
import { Toast } from "@/common/js/pay.js";
import {
  getIntelligentGuideListNoPage,
  getIntelligentGuideStartNodeAfterNode,
  getIntelligentGuideAfterNode,
} from "@/api/base.js";

// 助手文本
import GUIDTEXT from "./com/guidText.vue";
// 用户文本
import USERTEXT from "./com/userText.vue";
// 疾病
import DISEASE from "./com/disease.vue";
// 医生列表
import DOCLIST from "./com/docList.vue";
// 建议
import PROPOSAL from "./com/proposal.vue";

let BOX;

export default {
  components: {
    GUIDTEXT,
    USERTEXT,
    DISEASE,
    DOCLIST,
    PROPOSAL,
  },
  data() {
    return {
      // 聊天列表
      list: [],
      // 初始菜单
      menu: [],
      // 参数
      param: {
        processId: "",
        matches: "",
        nodeId: "",
        type: "",
        contentFormat: "",
      },
      // 最后信息
      lastMsg: {},
      // 填空
      input: "",
      // 评分
      rate: 0,
      // 病症
      diseaseList: [],
      // 健康建议
      proposal: "",
      // 提示
      tip: {},
      // 医生列表
      docList: [],
      // 多选
      checkList: [],
      // 多选文案
      checkListStr: [],
      // 未选择评价
      empt_src: require("../../static/images/question/assess.png"),
      // 已选择评价
      act_src: require("../../static/images/question/assess_active1.png"),
    };
  },
  created() {
    this.getStart();
  },
  methods: {
    // 获取流程
    async getStart() {
      let { data } = await getIntelligentGuideListNoPage();
      this.menu = data;
    },
    // 开始
    start(item) {
      console.log(item);
      // 如果当前有流程 禁止点击
      if (this.param.processId) return;
      this.param.processId = item.processId;
      // 调用下一步
      this.getNext(item.diagnosisTitle, true);

      // 列表容器
      BOX = document.querySelector(".cont_list");
    },
    // 获取下一步
    async getNext(msg, isFrist = false) {
      if (this.checkListStr.length) {
        msg = this.checkListStr.join();
      }
      // 放入消息
      this.list.push({
        msg,
        rate: this.lastMsg.aftertype == 3 ? true : false,
        isUser: true,
      });
      let data;
      // 初始节点
      if (isFrist) {
        let res = await getIntelligentGuideStartNodeAfterNode(this.param);
        data = res.data;
      } else {
        // 下级节点
        let res = await getIntelligentGuideAfterNode(this.param);

        data = res.data;
      }

      // 选项
      data.afterAnswer ? (data.afterAnswer = JSON.parse(data.afterAnswer)) : "";

      // 下一个题目id
      this.param.nodeId = data.afterNodeId;
      // 题目类型
      this.param.type = data.aftertype;
      this.lastMsg = data;
      // 如果结束
      if (data.isEnd == 1) {
        let {
          doctorList,
          endNode: { nodeTitle, nodeTypeCode, nodeContent },
        } = data;
        console.log(nodeContent, "nodeContent");
        // 医生列表
        this.docList = doctorList;

        // 放入消息
        // this.list.push({
        //   msg: nodeTitle,
        //   isUser: false,
        // });

        // 疾病诊断
        if (nodeTypeCode == 1) {
          this.diseaseList = JSON.parse(nodeContent);
        }

        // 医生推荐
        if (nodeTypeCode == 2) {
          this.tip = JSON.parse(nodeContent);
        }

        // 健康建议
        if (nodeTypeCode == 3) {
          let proposal = JSON.parse(nodeContent);
          // 存在图片
          if (proposal.urls) {
            proposal.urls = proposal.urls.split("|");
          }
          this.proposal = proposal;
        }
        return;
      }
      // 放入消息
      this.list.push({
        msg: data.afterQuestionTitle,
        isUser: false,
      });
      await this.$nextTick();
      BOX.scrollTop = BOX.scrollHeight;
    },
    // 单选提交
    setRadio(item, key) {
      this.param.matches = key;
      this.getNext(item);
    },
    // 多选
    setCheck(item, key) {
      // 存在
      if (this.checkList.includes(key)) {
        let index = this.checkList.findIndex((v) => v == key);
        this.checkList.splice(index, 1);
        this.checkListStr.splice(index, 1);
        return;
      }
      this.checkList.push(key);
      this.checkListStr.push(item);
    },
    // 提交多选
    sendCheck() {
      if (!this.checkList.length) {
        Toast("请选择");
        return;
      }
      let str = this.checkList.join("");
      this.getNext(str);
      this.checkList = [];
      this.checkListStr = [];
    },
    // 提交input
    sendInput() {
      if (!this.input) {
        Toast("不可为空");
        return;
      }
      let {
        afterAnswer: { contentFormat, contentLength },
      } = this.lastMsg;

      // 数字类型
      if (contentFormat == 2) {
        if (!Number(this.input)) {
          Toast("只能输入数字");
          return;
        }
        if (this.input.length > contentLength) {
          Toast("只能输入" + contentLength + "位");
          return;
        }
      }
      this.param.matches = this.input;
      this.param.contentFormat = contentFormat;
      this.getNext(this.input);
      this.input = "";
    },
    // 评分
    rateChange(e) {
      this.rate = e.value;
    },
    // 提交评分
    sendRate() {
      this.param.matches = this.rate;
      this.getNext(this.rate);
      this.rate = 0;
    },
  },
};
</script>

<style lang="scss" scoped>
.guidance {
  * {
    box-sizing: border-box;
  }
  display: flex;
  flex-direction: column;
  align-items: stretch;
  height: 100vh;

  .cont_list {
    padding: 32rpx;
    flex: 1;
    overflow-y: scroll;

    .tip_title {
      width: 468rpx;
      @include flex(left);
      height: 88rpx;
      margin-top: -20rpx;

      .tip_icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 10rpx;
      }

      text {
        font-size: 28rpx;
        color: #333;
        font-weight: bold;
      }
    }

    .list_opt {
      .list_opt_item {
        @include flex;
        height: 132rpx;
        border-top: 1px solid #eee;

        .opt_left {
          flex: 1;

          .left_title {
            display: block;
            font-size: 28rpx;
            color: #333;
          }

          .left_info {
            font-size: 24rpx;
            color: #999;
          }
        }

        .uni-icons {
          flex: none;
        }
      }
    }
  }

  .footer_opt {
    flex: none;
    width: 100%;
    background-color: #fff;
    position: sticky;
    bottom: 0;
    padding: 32rpx;

    .type_input {
      @include flex(lr);

      input {
        flex: 1;
        height: 64rpx;
        background: #fafafa;
        border-radius: 38rpx;
        border: 2rpx solid #ebebeb;
        padding: 0 24rpx;
        font-size: 28rpx;
      }

      button {
        padding: 0;
        width: 104rpx;
        height: 64rpx;
        border-radius: 4rpx;
        @include bg_theme;
        color: #fff;
        @include flex;
        flex: none;
        margin-left: 24rpx;
        font-size: 28rpx;
      }
    }

    .type_radio {
      @include flex(left);
      flex-wrap: wrap;
      // gap: 32rpx;

      .radio_item {
        min-width: 170rpx;
        min-height: 76rpx;
        font-size: 28rpx;
        margin-right: 32rpx;
        margin-bottom: 32rpx;
        text-align: center;
        @include flex;
        padding: 10rpx 24rpx;
        background-color: #fff;
        border-radius: 100rpx;
        @include font_theme;
        border: 1px solid rgba(0, 0, 0, 0.1);
      }
    }

    .type_rate {
      @include flex(lr);

      .rate {
        flex: 1;
      }

      button {
        padding: 0;
        width: 104rpx;
        height: 64rpx;
        border-radius: 4rpx;
        @include bg_theme;
        color: #fff;
        @include flex;
        flex: none;
        margin-left: 24rpx;
        font-size: 28rpx;
      }
    }

    .type_check {
      @include flex(left);
      flex-wrap: wrap;
      // gap: 32rpx;

      .check_item {
        height: 76rpx;
        padding: 0 32rpx;
        @include flex;
        margin-right: 32rpx;
        margin-bottom: 32rpx;
        color: #333;
        border-radius: 38rpx;
        background-color: #fff;
        font-size: 28rpx;

        border: 1px solid rgba(0, 0, 0, 0.1);

        &.act {
          background-color: #d6f1ff;
          @include font_theme;
        }
      }
    }

    .check_but {
      margin-top: 24rpx;
      width: 100%;
      height: 84rpx;
      border-radius: 12rpx;
      @include bg_theme;
      font-size: 32rpx;
      color: #fff;
    }
  }
}
::v-deep.input-placeholder {
  display: block;
  white-space: pre-wrap;
  word-break: normal;
  line-height: 30rpx;
}
</style>
