<template>
  <div class="detail">
    <!-- 状态 -->
    <STATUS :refundAuditStatus="detail.refundAuditStatus" />

    <!-- 容器 -->
    <div class="mt_warp">
      <!-- 取消申请相关 -->
      <REFUSE
        v-if="refundAuditList.length && detail.refundAuditStatus != 2"
        :list="refundAuditList"
      />

      <!-- 快递信息 -->
      <LOGISTICS
        v-if="detail.deliveryType != 2"
        :name="detail.deliveryName"
        :address="detail.deliveryAddressdeTail"
        :telNo="detail.deliveryTelNo"
      />

      <!-- 药店信息 -->
      <div class="drug_store" v-if="detail.deliveryType == 2">
        <p class="title">药店信息</p>
        <DRUGSTORE :detail="drugStore" />
      </div>

      <ROW
        :label="detail.deliveryType == 2 ? '自提时间' : '期望配送时间'"
        :value="detail.getgoodsTime"
      />

      <!-- 药品信息 -->
      <div class="drug">
        <p class="title">药品信息</p>
        <DRUGLIST :list="drugList" :name="detail.subjectName" />
      </div>

      <ROW
        v-if="invoice.invoiceType"
        label="发票信息"
        :value="
          invoice.invoiceType == 1
            ? `个人（${invoice.invoiceTitle}）`
            : invoice.invoiceTitle
        "
        showIcon
        @click="showTip"
      />

      <!-- 订单信息 -->
      <div class="order">
        <p class="title">订单信息</p>
        <p>
          订单编号<span>{{ orderNo }}</span>
        </p>
        <p>
          创建时间<span>{{ detail.addTime }}</span>
        </p>
        <p v-if="detail.payTime">
          支付时间<span>{{ detail.payTime }}</span>
        </p>
        <p v-if="detail.auditTime">
          审核时间<span>{{ detail.auditTime }}</span>
        </p>
        <p v-if="detail.deliveryTime">
          发货时间<span>{{ detail.deliveryTime }}</span>
        </p>
        <p v-if="detail.receiveTime">
          收货时间<span>{{ detail.receiveTime }}</span>
        </p>
        <p v-if="detail.refundApplicationTime">
          退费时间<span>{{ detail.refundApplicationTime }}</span>
        </p>
        <p v-if="detail.transactionClosingTime">
          关闭时间<span>{{ detail.transactionClosingTime }}</span>
        </p>
      </div>
    </div>

    <!-- 弹框 -->
    <uni-popup ref="popup" type="bottom">
      <div class="pop">
        <div class="pop_title">
          <text>发票信息</text>
          <uni-icons type="closeempty" @click="hideTip"></uni-icons>
        </div>
        <div class="pop_text">
          <p>抬头类型: {{ invoice.invoiceType == 1 ? '个人' : '单位' }}</p>
          <p v-if="invoice.invoiceType == 2">
            公司税号: {{ invoice.invoiceTaxNo }}
          </p>
          <p>公司税号: {{ invoice.invoiceTaxNo }}</p>
          <p>备注: {{ invoice.invoiceMemo || '暂无备注' }}</p>
        </div>
      </div>
    </uni-popup>

    <!-- 底部 -->
    <div class="footer" v-if="detail.refundAuditStatus != 0">
      <span class="but act" @click="toRefund">
        再次取消
      </span>
    </div>
  </div>
</template>

<script>
import { findDrugStoreDetail } from '@/api/base';
import {
  orderFastMallNewList,
  queryFastMallOrderStatusIschecking,
  tradeRefundFastMallOrder,
} from '@/api/shop';
import STATUS from './com/cancel.vue';
import REFUSE from './com/refuse.vue';
import REASONS from './com/reasons.vue';
import DRUGLIST from './com/drugList.vue';
import ROW from './com/row.vue';
import LOGISTICS from './com/logistics.vue';
import DRUGSTORE from './com/drugStore.vue';
import { Toast } from '@/common/js/pay';

export default {
  name: 'CancelDetail',
  components: {
    ROW,
    REFUSE,
    REASONS,
    STATUS,
    DRUGLIST,
    LOGISTICS,
    DRUGSTORE,
  },
  data() {
    return {
      // 订单号
      orderNo: '',
      // 药品列表
      drugList: [],
      // 详情
      detail: {},
      // 药店详情
      drugStore: {},
      // 发票信息
      invoice: {},
      // 审核状态列表
      refundAuditList: [],
    };
  },
  onLoad(v) {
    this.orderNo = v.orderNo;
    this.getDetail();
  },
  onShow() {
    // this.getDetail();
  },
  methods: {
    // 获取详情
    async getDetail() {
      let { data } = await orderFastMallNewList(this.orderNo);
      let item = data;
      item.deliverytype = item.deliveryType;
      item.logisticsstatus = item.logisticsStatus;
      this.drugList = item.drugList;

      let {
        orderAuditList,
        invoiceMemo,
        invoiceTaxNo,
        invoiceTitle,
        invoiceType,
        // 审核情况列表
        refundAuditList,
        // 退费申请状态 0待审核 1驳回 2通过
        refundAuditStatus,
      } = item;

      // 发票信息
      this.invoice = {
        invoiceMemo,
        invoiceTaxNo,
        invoiceTitle,
        invoiceType,
      };
      delete item.invoiceMemo;
      delete item.invoiceTaxNo;
      delete item.invoiceTitle;
      delete item.invoiceType;
      delete item.drugList;
      delete item.drugCode;
      delete item.refundAuditList;

      // 不通过相关
      this.reasons = orderAuditList;

      if (orderAuditList.length) {
        item.auditTime = orderAuditList[0].auditTime;
      }

      // 退费申请相关
      if (refundAuditStatus > -1) {
        this.refundAuditList = refundAuditList;
      }

      // 赋值
      this.detail = item;
      if (item.deliveryType == 2) {
        this.getDrugStroe();
      }
    },
    // 获取药店信息
    async getDrugStroe() {
      const drugstoreId = this.detail.subjectId;
      let { data } = await findDrugStoreDetail({ drugstoreId });
      this.drugStore = data;
    },

    // 跳转取消申请
    async toRefund() {
      const { orderNo } = this;
      const {
        data: { status },
      } = await queryFastMallOrderStatusIschecking(orderNo);
      // 已申请退款
      if (status == 5) {
        Toast('您已提交取消申请，请耐心等待');
        return;
      }
      // 订单关闭
      if (status == 6) {
        Toast('订单已关闭');
        return;
      }
      // 可直接退费
      if (status == 0 || status == 1) {
        await tradeRefundFastMallOrder(orderNo);
        Toast('成功取消');
        this.getDetail();
        return;
      }
      // 需要提交申请
      let url = '/pages/shopOrder/refund?orderNo=' + orderNo;
      uni.navigateTo({ url });
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background-color: #f5f5f5;
}

.detail {
  * {
    box-sizing: border-box;
  }

  padding-bottom: 132rpx;

  .mt_warp {
    margin-top: -32rpx;
    border-radius: 32rpx 32rpx 0 0;
    overflow: hidden;

    .drug_store {
      // margin-top: 24rpx;
      background-color: #fff;

      .title {
        font-size: 28rpx;
        line-height: 88rpx;
        padding: 0 32rpx;
        border-bottom: 1px solid #f5f5f5;
      }
    }

    .drug {
      padding: 0 32rpx;
      background-color: #fff;
      margin-top: 24rpx;

      .title {
        height: 88rpx;
        @include flex(left);
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
        border-bottom: 1px solid #f5f5f5;
      }
    }

    .order {
      margin-top: 24rpx;
      background-color: #fff;
      padding: 0 32rpx 12rpx;
      font-size: 28rpx;

      p {
        line-height: 60rpx;
        span {
          margin-left: 24rpx;
        }
      }

      .title {
        font-weight: bold;
        line-height: 70rpx;
      }
    }
  }

  .footer {
    width: 100%;
    height: 108rpx;
    background: #ffffff;
    box-shadow: 0px 0px 2rpx 0px rgba(0, 0, 0, 0.12);
    border-radius: 16rpx 16rpx 0px 0px;
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 2;
    @include flex(right);
    padding: 0 32rpx;
    box-sizing: border-box;

    .but {
      width: 158rpx;
      height: 56rpx;
      border-radius: 28rpx;
      @include flex;
      font-size: 26rpx;
      @include font_theme;
      margin-left: 24rpx;
      padding: 0;
      @include border_theme;
      margin-right: inherit;

      &.act {
        @include bg_theme;
        color: #fff;
      }
    }
  }
}

.pop {
  padding: 0 32rpx 50rpx;
  background-color: #fff;
  border-radius: 16rpx 16rpx 0px 0px;

  .pop_title {
    height: 88rpx;
    @include flex;
    position: relative;
    font-size: 32rpx;

    .uni-icons {
      position: absolute;
      right: 0;
    }
  }

  .pop_text {
    color: #999;
    font-size: 24rpx;
    line-height: 60rpx;
  }
}
</style>
