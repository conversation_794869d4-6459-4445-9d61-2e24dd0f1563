"use strict";
const plugins_xeUtils_setupDefaults = require("./setupDefaults.js");
const plugins_xeUtils_helperStringUpperCase = require("./helperStringUpperCase.js");
const plugins_xeUtils_helperGetDateFullYear = require("./helperGetDateFullYear.js");
const plugins_xeUtils_helperGetDateMonth = require("./helperGetDateMonth.js");
const plugins_xeUtils_toStringDate = require("./toStringDate.js");
const plugins_xeUtils_getYearWeek = require("./getYearWeek.js");
const plugins_xeUtils_getYearDay = require("./getYearDay.js");
const plugins_xeUtils_assign = require("./assign.js");
const plugins_xeUtils_isValidDate = require("./isValidDate.js");
const plugins_xeUtils_isFunction = require("./isFunction.js");
const plugins_xeUtils_padStart = require("./padStart.js");
function handleCustomTemplate(date, formats, match, value) {
  var format = formats[match];
  if (format) {
    if (plugins_xeUtils_isFunction.isFunction(format)) {
      return format(value, match, date);
    } else {
      return format[value];
    }
  }
  return value;
}
var dateFormatRE = /\[([^\]]+)]|y{2,4}|M{1,2}|d{1,2}|H{1,2}|h{1,2}|m{1,2}|s{1,2}|S{1,3}|Z{1,2}|W{1,2}|D{1,3}|[aAeEq]/g;
function toDateString(date, format, options) {
  if (date) {
    date = plugins_xeUtils_toStringDate.toStringDate(date);
    if (plugins_xeUtils_isValidDate.isValidDate(date)) {
      var result = format || plugins_xeUtils_setupDefaults.setupDefaults.parseDateFormat || plugins_xeUtils_setupDefaults.setupDefaults.formatString;
      var hours = date.getHours();
      var apm = hours < 12 ? "am" : "pm";
      var formats = plugins_xeUtils_assign.assign({}, plugins_xeUtils_setupDefaults.setupDefaults.parseDateRules || plugins_xeUtils_setupDefaults.setupDefaults.formatStringMatchs, options ? options.formats : null);
      var fy = function(match, length) {
        return ("" + plugins_xeUtils_helperGetDateFullYear.helperGetDateFullYear(date)).substr(4 - length);
      };
      var fM = function(match, length) {
        return plugins_xeUtils_padStart.padStart(plugins_xeUtils_helperGetDateMonth.helperGetDateMonth(date) + 1, length, "0");
      };
      var fd = function(match, length) {
        return plugins_xeUtils_padStart.padStart(date.getDate(), length, "0");
      };
      var fH = function(match, length) {
        return plugins_xeUtils_padStart.padStart(hours, length, "0");
      };
      var fh = function(match, length) {
        return plugins_xeUtils_padStart.padStart(hours <= 12 ? hours : hours - 12, length, "0");
      };
      var fm = function(match, length) {
        return plugins_xeUtils_padStart.padStart(date.getMinutes(), length, "0");
      };
      var fs = function(match, length) {
        return plugins_xeUtils_padStart.padStart(date.getSeconds(), length, "0");
      };
      var fS = function(match, length) {
        return plugins_xeUtils_padStart.padStart(date.getMilliseconds(), length, "0");
      };
      var fZ = function(match, length) {
        var zoneHours = date.getTimezoneOffset() / 60 * -1;
        return handleCustomTemplate(date, formats, match, (zoneHours >= 0 ? "+" : "-") + plugins_xeUtils_padStart.padStart(zoneHours, 2, "0") + (length === 1 ? ":" : "") + "00");
      };
      var fW = function(match, length) {
        return plugins_xeUtils_padStart.padStart(handleCustomTemplate(date, formats, match, plugins_xeUtils_getYearWeek.getYearWeek(date, (options ? options.firstDay : null) || plugins_xeUtils_setupDefaults.setupDefaults.firstDayOfWeek)), length, "0");
      };
      var fD = function(match, length) {
        return plugins_xeUtils_padStart.padStart(handleCustomTemplate(date, formats, match, plugins_xeUtils_getYearDay.getYearDay(date)), length, "0");
      };
      var parseDates = {
        yyyy: fy,
        yy: fy,
        MM: fM,
        M: fM,
        dd: fd,
        d: fd,
        HH: fH,
        H: fH,
        hh: fh,
        h: fh,
        mm: fm,
        m: fm,
        ss: fs,
        s: fs,
        SSS: fS,
        S: fS,
        ZZ: fZ,
        Z: fZ,
        WW: fW,
        W: fW,
        DDD: fD,
        D: fD,
        a: function(match) {
          return handleCustomTemplate(date, formats, match, apm);
        },
        A: function(match) {
          return handleCustomTemplate(date, formats, match, plugins_xeUtils_helperStringUpperCase.helperStringUpperCase(apm));
        },
        e: function(match) {
          return handleCustomTemplate(date, formats, match, date.getDay());
        },
        E: function(match) {
          return handleCustomTemplate(date, formats, match, date.getDay());
        },
        q: function(match) {
          return handleCustomTemplate(date, formats, match, Math.floor((plugins_xeUtils_helperGetDateMonth.helperGetDateMonth(date) + 3) / 3));
        }
      };
      return result.replace(dateFormatRE, function(match, skip) {
        return skip || (parseDates[match] ? parseDates[match](match, match.length) : match);
      });
    }
    return "Invalid Date";
  }
  return "";
}
exports.toDateString = toDateString;
