"use strict";
const plugins_xeUtils_isFunction = require("./isFunction.js");
const plugins_xeUtils_isArray = require("./isArray.js");
const plugins_xeUtils_each = require("./each.js");
const plugins_xeUtils_findIndexOf = require("./findIndexOf.js");
function helperCreatePickOmit(case1, case2) {
  return function(obj, callback) {
    var item, index;
    var rest = {};
    var result = [];
    var context = this;
    var args = arguments;
    var len = args.length;
    if (!plugins_xeUtils_isFunction.isFunction(callback)) {
      for (index = 1; index < len; index++) {
        item = args[index];
        result.push.apply(result, plugins_xeUtils_isArray.isArray(item) ? item : [item]);
      }
      callback = 0;
    }
    plugins_xeUtils_each.each(obj, function(val, key) {
      if ((callback ? callback.call(context, val, key, obj) : plugins_xeUtils_findIndexOf.findIndexOf(result, function(name) {
        return name === key;
      }) > -1) ? case1 : case2) {
        rest[key] = val;
      }
    });
    return rest;
  };
}
exports.helperCreatePickOmit = helperCreatePickOmit;
