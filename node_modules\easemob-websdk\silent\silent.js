!function(e,r){"object"==typeof exports&&"object"==typeof module?module.exports=r():"function"==typeof define&&define.amd?define([],r):"object"==typeof exports?exports.websdk=r():e.websdk=r()}(self,(function(){return(self.webpackChunkwebsdk=self.webpackChunkwebsdk||[]).push([[624],{8277:function(e,r,t){t.r(r),t.d(r,{clearRemindTypeForConversation:function(){return u},getPushPerformLanguage:function(){return f},getSilentModeForAll:function(){return s},getSilentModeForConversation:function(){return p},getSilentModeForConversations:function(){return l},getSilentModeRemindTypeConversations:function(){return h},setPushPerformLanguage:function(){return d},setSilentModeForAll:function(){return i},setSilentModeForConversation:function(){return c}}),t(8706),t(1629),t(4346),t(8598),t(2062),t(739),t(9432),t(6099),t(3362),t(3500);var o=t(8678),n=t(1750),a=t(2056);function i(e){if(!(e.options instanceof Object))throw Error('Invalid parameter: "options"');var r=e.options.paramType;if("number"!=typeof r||r<0||r>2)throw Error('Invalid parameter: "options of paramType"');if(0===r){if("string"!=typeof e.options.remindType)throw Error('Invalid parameter: "options of remindType"')}else if(1===r){if("number"!=typeof e.options.duration)throw Error('Invalid parameter: "options of duration"')}else if(2===r){var t=e.options,i=t.startTime,s=t.endTime;if(!(i instanceof Object&&Object.keys(i).length))throw Error('Invalid parameter: "options of startTime"');if(!i.hours||"number"!=typeof i.hours||!i.minutes||"number"!=typeof i.minutes)throw Error('Invalid parameter: "options of startTime of hours or minutes"');if(!(s instanceof Object&&Object.keys(s).length))throw Error('Invalid parameter: "options of endTime"');if(!s.hours||"number"!=typeof s.hours||!s.minutes||"number"!=typeof s.minutes)throw Error('Invalid parameter: "options of endTime of hours or minutes"')}var c=n.dO.call(this).error;if(c)return Promise.reject(c);var u=this.context,p=u.accessToken,l=u.orgName,d=u.appName,f=u.userId,h=u.jid,m={};switch(r){case 0:m={type:e.options.remindType};break;case 1:m={ignoreDuration:e.options.duration};break;case 2:var v=e.options;i=v.startTime,s=v.endTime,m={ignoreInterval:"".concat(i.hours,":").concat(i.minutes,"-").concat(s.hours,":").concat(s.minutes)}}var y={url:"".concat(this.apiUrl,"/").concat(l,"/").concat(d,"/users/").concat(f,"/notification/user/").concat(f,"?resource=").concat(h.clientResource),type:"PUT",dataType:"json",data:JSON.stringify(m),headers:{Authorization:"Bearer "+p,"Content-Type":"application/json"},success:e.success,error:e.error};return a.vF.debug("Call setSilentModeForAll:",e),o.RD.call(this,y)}function s(e){var r=n.dO.call(this).error;if(r)return Promise.reject(r);var t=this.context,i=t.accessToken,s=t.orgName,c=t.appName,u=t.userId,p={url:"".concat(this.apiUrl,"/").concat(s,"/").concat(c,"/users/").concat(u,"/notification/user/").concat(u),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:null==e?void 0:e.success,error:null==e?void 0:e.error};return a.vF.debug("Call getSilentModeForAll:",e),o.RD.call(this,p)}function c(e){if("string"!=typeof e.conversationId||!e.conversationId)throw Error('Invalid parameter: "conversationId"');if("string"!=typeof e.type||!e.type)throw Error('Invalid parameter: "type"');if(!(e.options instanceof Object))throw Error('Invalid parameter: "options"');var r=e.options.paramType;if("number"!=typeof r||r<0||r>2)throw Error('Invalid parameter: "options of paramType"');if(0===r){if("string"!=typeof e.options.remindType)throw Error('Invalid parameter: "options of remindType"')}else if(1===r){if("number"!=typeof e.options.duration)throw Error('Invalid parameter: "options of duration"')}else if(2===r){var t=e.options,i=t.startTime,s=t.endTime;if(!(i instanceof Object&&Object.keys(i).length))throw Error('Invalid parameter: "options of startTime"');if(!i.hours||"number"!=typeof i.hours||!i.minutes||"number"!=typeof i.minutes)throw Error('Invalid parameter: "options of startTime of hours or minutes"');if(!(s instanceof Object&&Object.keys(s).length))throw Error('Invalid parameter: "options of endTime"');if(!s.hours||"number"!=typeof s.hours||!s.minutes||"number"!=typeof s.minutes)throw Error('Invalid parameter: "options of endTime of hours or minutes"')}var c=n.dO.call(this).error;if(c)return Promise.reject(c);var u=this.context,p=u.accessToken,l=u.orgName,d=u.appName,f=u.userId,h=u.jid,m="chatgroup",v={};switch(r){case 0:v={type:e.options.remindType};break;case 1:v={ignoreDuration:e.options.duration};break;case 2:var y=e.options;i=y.startTime,s=y.endTime,v={ignoreInterval:"".concat(i.hours,":").concat(i.minutes,"-").concat(s.hours,":").concat(s.minutes)}}"singleChat"===e.type&&(m="user");var g={url:"".concat(this.apiUrl,"/").concat(l,"/").concat(d,"/users/").concat(f,"/notification/").concat(m,"/").concat(e.conversationId,"?resource=").concat(h.clientResource),type:"PUT",dataType:"json",data:JSON.stringify(v),headers:{Authorization:"Bearer "+p,"Content-Type":"application/json"},success:e.success,error:e.error};return a.vF.debug("Call setSilentModeForConversation:",e),o.RD.call(this,g)}function u(e){if("string"!=typeof e.conversationId||!e.conversationId)throw Error('Invalid parameter: "conversationId"');if("string"!=typeof e.type||!e.type)throw Error('Invalid parameter: "type"');var r=n.dO.call(this).error;if(r)return Promise.reject(r);var t=this.context,i=t.accessToken,s=t.orgName,c=t.appName,u=t.userId,p=t.jid,l="chatgroup";"singleChat"===e.type&&(l="user");var d={url:"".concat(this.apiUrl,"/").concat(s,"/").concat(c,"/users/").concat(u,"/notification/").concat(l,"/").concat(e.conversationId,"?resource=").concat(p.clientResource),type:"PUT",dataType:"json",data:JSON.stringify({type:"DEFAULT"}),headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return a.vF.debug("Call clearRemindTypeForConversation:",e),o.RD.call(this,d)}function p(e){if("string"!=typeof e.conversationId||!e.conversationId)throw Error('Invalid parameter: "conversationId"');if("string"!=typeof e.type||!e.type)throw Error('Invalid parameter: "type"');var r=n.dO.call(this).error;if(r)return Promise.reject(r);var t=this.context,i=t.accessToken,s=t.orgName,c=t.appName,u=t.userId,p="chatgroup";"singleChat"===e.type&&(p="user");var l={url:"".concat(this.apiUrl,"/").concat(s,"/").concat(c,"/users/").concat(u,"/notification/").concat(p,"/").concat(e.conversationId),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return a.vF.debug("Call getSilentModeForConversation:",e),o.RD.call(this,l)}function l(e){if(!Array.isArray(e.conversationList))throw Error('Invalid parameter: "conversationList"');var r=n.dO.call(this).error;if(r)return Promise.reject(r);var t=this.context,i=t.accessToken,s=t.orgName,c=t.appName,u=t.userId,p=[],l=[];e.conversationList.forEach((function(e){"singleChat"===e.type?p.push(e.id):l.push(e.id)}));var d=p.length?p.join(","):"",f=l.length?l.join(","):"",h={url:"".concat(this.apiUrl,"/").concat(s,"/").concat(c,"/users/").concat(u,"/notification?user=").concat(d,"&group=").concat(f),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e.success,error:e.error};return a.vF.debug("Call getSilentModeForConversations:",e),o.RD.call(this,h)}function d(e){if("string"!=typeof e.language||!e.language)throw Error('Invalid parameter: "language"');var r=n.dO.call(this).error;if(r)return Promise.reject(r);var t={translationLanguage:e.language},i=this.context,s=i.accessToken,c=i.orgName,u=i.appName,p=i.userId,l={url:"".concat(this.apiUrl,"/").concat(c,"/").concat(u,"/users/").concat(p,"/notification/language"),type:"PUT",dataType:"json",data:JSON.stringify(t),headers:{Authorization:"Bearer "+s,"Content-Type":"application/json"},success:e.success,error:e.error};return a.vF.debug("Call setPushPerformLanguage:",e),o.RD.call(this,l)}function f(e){var r=n.dO.call(this).error;if(r)return Promise.reject(r);var t=this.context,i=t.accessToken,s=t.orgName,c=t.appName,u=t.userId,p={url:"".concat(this.apiUrl,"/").concat(s,"/").concat(c,"/users/").concat(u,"/notification/language"),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:null==e?void 0:e.success,error:null==e?void 0:e.error};return a.vF.debug("Call getPushPerformLanguage:",e),o.RD.call(this,p)}function h(e){var r=n.dO.call(this).error;if(r)return Promise.reject(r);if("number"!=typeof e.pageSize)throw Error('Invalid parameter: "pageSize"');var t=this.context,i=t.accessToken,s=t.orgName,c=t.appName,u=t.userId,p={limit:e.pageSize||10,cursor:e.cursor};e.cursor||delete p.cursor;var l={url:"".concat(this.apiUrl,"/").concat(s,"/").concat(c,"/users/").concat(u,"/notification/mute/type"),type:"GET",dataType:"json",data:p,headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return a.vF.debug("Call getSilentModeRemindTypeConversations:",e),o.RD.call(this,l).then((function(e){return e.data?{data:{conversations:e.data.map((function(e){return"user"in e?{conversationId:e.user,type:"singleChat",remindType:e.value}:{conversationId:e.group,type:"groupChat",remindType:e.value}})),cursor:e.cursor},type:e.type}:e}))}}},function(e){return e(e.s=8277)}])}));