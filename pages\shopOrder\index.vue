<template>
  <!-- 商城订单 -->
  <div class="shop_order">
    <NAV @navChange="change" />

    <div class="list" v-if="list.length">
      <ITEM
        v-for="(item, index) in list"
        :item="item"
        :index="index"
        :key="item.orderNo"
        @showCode="showCode"
        @change="getItem"
      />
    </div>

    <EMPTY v-if="!list.length" />

    <!-- 弹框 -->
    <uni-popup ref="popup" type="center">
      <EWM v-if="code" :code="code" />
    </uni-popup>
  </div>
</template>

<script>
import ITEM from './com/item.vue';
import NAV from './com/nav.vue';
import EMPTY from './com/empty.vue';
import EWM from './com/ewm.vue';
import { queryOrderFastMall } from '@/api/shop';

let timer;
export default {
  name: 'ShopOrder',
  components: {
    ITEM,
    NAV,
    EMPTY,
    EWM,
  },
  data() {
    return {
      list: [],
      index: 0,
      query: {
        limit: 10,
        page: 1,
        orderStatus: '',
        patientid: uni.getStorageSync('patientIdList') || [],
      },
      total: 0,
      // 取药码
      code: '',
    };
  },
  onLoad() {
    this.getList();
  },
  onPullDownRefresh() {
    this.query.page = 1;
    this.getList();
  },
  onReachBottom() {
    if (this.list.length >= this.total) return;
    clearTimeout(timer);

    timer = setTimeout(() => {
      this.query.page += 1;
      this.getList();
    }, 300);
  },
  methods: {
    // 显示取药码
    showCode(v) {
      this.code = v;
      this.$refs.popup.open();
    },
    // 导航变动
    change(v) {
      this.query.page = 1;
      if (v == 0) {
        this.query.orderStatus = '';
      } else {
        this.query.orderStatus = v;
      }
      uni.pageScrollTo({
        scrollTop: 0,
      });
      this.getList();
    },
    // 获取列表
    async getList() {
      try {
        const { data } = await queryOrderFastMall(this.query);
        const { rows, total } = data;
        if (this.query.page > 1) {
          this.list = [...this.list, ...rows];
        } else {
          this.list = rows;
        }
        this.total = total;
      } catch (error) {}
      uni.stopPullDownRefresh();
    },
    // 更新单条数据
    async getItem({ orderNo, index }) {
      let obj = {
        limit: 1,
        page: 1,
        patientid: uni.getStorageSync('patientIdList'),
        orderNo,
      };
      let { data } = await queryOrderFastMall(obj);
      let item = data.rows[0];
      this.$set(this.list, index, item);
    },
  },
};
</script>

<style lang="scss" scoped>
.shop_order {
  padding: 110rpx 32rpx 24rpx;
}
</style>
