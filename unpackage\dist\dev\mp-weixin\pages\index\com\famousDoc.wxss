/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*
	字体颜色相关
*/
/*
	背景色 分割线色
*/
/*
	主题色
*/
/*
		渐变可调角度
*/
/*
	定义弹性布局
 */
/*
	单行文本溢出隐藏
*/
.new-doc-item.data-v-16bd5f8b {
  display: flex;
  border-bottom: 1px solid #f5f5f5;
  padding: 20rpx 0;
}
.new-doc-item .docImg.data-v-16bd5f8b {
  margin-right: 24rpx;
}
.new-doc-item .docImg .doc_head.data-v-16bd5f8b {
  width: 110rpx;
  height: 110rpx;
}
.new-doc-item .new-doc-content.data-v-16bd5f8b {
  flex: 1;
}
.new-doc-item .new-doc-content-dept.data-v-16bd5f8b {
  font-size: 28rpx;
  font-weight: 400;
  line-height: 40rpx;
  margin-top: 10rpx;
  color: #666666;
}
.fam_list.data-v-16bd5f8b {
  background: #fff;
  border-radius: 8rpx;
}
.fam_list .fam_title.data-v-16bd5f8b {
  padding: 0 10rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  display: flex;
  justify-content: space-between;
}
.list-box.data-v-16bd5f8b {
  display: flex;
  flex-direction: column;
  margin-left: 20rpx;
}
.list-card.data-v-16bd5f8b {
  padding: 16rpx;
  background-color: #FBFBFD;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
  height: 360rpx;
  text-align: center;
}
.list-box-1.data-v-16bd5f8b {
  width: 50%;
  text-align: center;
}
.card_more.data-v-16bd5f8b {
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #874ff0;
}
.doc_head.data-v-16bd5f8b {
  width: 104rpx;
  height: 104rpx;
  border-radius: 50%;
  flex: none;
  object-fit: cover;
}
.doc_name.data-v-16bd5f8b {
  font-size: 28rpx;
  color: #333333;
  font-weight: bold;
}
.desc_tag.data-v-16bd5f8b {
  margin-top: 16rpx;
  display: flex;
  flex-direction: row;
}
.desc_tag_text.data-v-16bd5f8b {
  padding: 0 12rpx;
  height: 36rpx;
  line-height: 36rpx;
  border-radius: 18rpx;
  background-color: #e8f6fd;
  color: #999999;
  font-size: 20rpx;
  overflow: hidden;
  max-width: 120rpx;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.desc_tag_1.data-v-16bd5f8b {
  margin: 0 2rpx;
}
.desc_two.data-v-16bd5f8b {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #333;
}
.desc_two text.data-v-16bd5f8b:last-child {
  margin-left: 24rpx;
}
.host.data-v-16bd5f8b {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #a6aab2;
  text-align: center;
  /* 超出部分...代替*/
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  overflow: hidden;
}