"use strict";
const common_request_request = require("../common/request/request.js");
function getConfigInfoByKey(data) {
  return common_request_request.http({
    url: "business/sysconfig/getConfigInfoByKey",
    method: "post",
    data
  });
}
function randomDoctor(param = {}) {
  return common_request_request.http({
    url: "basic/usercollectdoc/randomDoctor",
    param,
    method: "post"
  });
}
function findDoctorByUserID(param = {}) {
  return common_request_request.http({
    url: "basic/usercollectdoc/findDoctorByUserId",
    param,
    method: "post"
  });
}
function getSysPlatformConfigByKeyList(arr = []) {
  return common_request_request.http({
    url: "basic/sysPlatformConfig/getSysPlatformConfigByKeyList",
    param: {
      configKeyList: arr
    },
    method: "post"
  });
}
function selectElectronicArticleListPage(param) {
  return common_request_request.http({
    url: "basic/patientRegister/selectElectronicArticleListPage",
    param,
    method: "post"
  });
}
function patientHomeRecommendDoctor(param = {}) {
  return common_request_request.http({
    url: "basic/usercollectdoc/patientHomeRecommendDoctor",
    param,
    method: "post"
  });
}
function queryPatientHomeCarouselMapListPage(param = {}) {
  return common_request_request.http({
    url: "dictionary/dicPatientCarouselMap/queryPatientHomeCarouselMapListPage",
    param,
    method: "post"
  });
}
function hisPatientLoginByTelPhoneAndCaptcha(param = {}) {
  return common_request_request.http({
    url: "basic/hisLogin/hisPatientLoginByTelPhoneAndCaptcha",
    param,
    method: "post"
  });
}
function findDiagTreatmentSubject(param = {}) {
  return common_request_request.http({
    url: "basic/dicDiagTreatmentSubject/findDiagTreatmentSubject",
    param,
    method: "post"
  });
}
exports.findDiagTreatmentSubject = findDiagTreatmentSubject;
exports.findDoctorByUserID = findDoctorByUserID;
exports.getConfigInfoByKey = getConfigInfoByKey;
exports.getSysPlatformConfigByKeyList = getSysPlatformConfigByKeyList;
exports.hisPatientLoginByTelPhoneAndCaptcha = hisPatientLoginByTelPhoneAndCaptcha;
exports.patientHomeRecommendDoctor = patientHomeRecommendDoctor;
exports.queryPatientHomeCarouselMapListPage = queryPatientHomeCarouselMapListPage;
exports.randomDoctor = randomDoctor;
exports.selectElectronicArticleListPage = selectElectronicArticleListPage;
