"use strict";
const plugins_xeUtils_isEmpty = require("./isEmpty.js");
const plugins_xeUtils_isObject = require("./isObject.js");
const plugins_xeUtils_isFunction = require("./isFunction.js");
const plugins_xeUtils_property = require("./property.js");
const plugins_xeUtils_each = require("./each.js");
function createiterateEmpty(iterate) {
  return function() {
    return plugins_xeUtils_isEmpty.isEmpty(iterate);
  };
}
function groupBy(obj, iterate, context) {
  var groupKey;
  var result = {};
  if (obj) {
    if (iterate && plugins_xeUtils_isObject.isObject(iterate)) {
      iterate = createiterateEmpty(iterate);
    } else if (!plugins_xeUtils_isFunction.isFunction(iterate)) {
      iterate = plugins_xeUtils_property.property(iterate);
    }
    plugins_xeUtils_each.each(obj, function(val, key) {
      groupKey = iterate ? iterate.call(context, val, key, obj) : val;
      if (result[groupKey]) {
        result[groupKey].push(val);
      } else {
        result[groupKey] = [val];
      }
    });
  }
  return result;
}
exports.groupBy = groupBy;
