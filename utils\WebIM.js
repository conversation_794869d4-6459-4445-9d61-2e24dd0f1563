import websdk from 'easemob-websdk/uniApp/Easemob-chat';
import config from "./WebIMConfig";
// 多人通话
import emedia from "easemob-emedia";

// 配置
emedia.config({
  restPrefix: config.apiURL,
  appkey: config.appkey,
  consoleLogger: false,
});

console.group = console.group || {};
console.groupEnd = console.groupEnd || {};

var window = {};
let WebIM = (window.WebIM = websdk);
window.WebIM.config = config;
//var DOMParser = window.DOMParser = xmldom.DOMParser;
//let document = window.document = new DOMParser().parseFromString("<?xml version='1.0'?>\n", "text/xml");

function ack(receiveMsg) {
  // 处理未读消息回执
  var bodyId = receiveMsg.id; // 需要发送已读回执的消息id
  var ackMsg = new WebIM.message("read", WebIM.conn.getUniqueId());
  console.log('receiveMsg',receiveMsg)
  ackMsg.set({
    id: bodyId,
    to: receiveMsg.to,
    chatType: "groupChat", // 会话类型，这里为群聊。
  });
  // if(receiveMsg.chatType==='groupchat'){
  //   ackMsg.setChatType('groupChat')
  // }
  let pages = getCurrentPages();
  let curPage = pages[pages.length - 1];
  let curParam = curPage.options || curPage.$route.query;
  if (curParam && curParam.param) {
    let param = JSON.parse(curParam.param);
    if (receiveMsg.from == param.docId.toLowerCase()) {
      // 如果是语音 且mid存在
      if (receiveMsg.messType == "voice" && receiveMsg.ext.mid) {
        curPage.audioMsg(receiveMsg);
        return;
      }
      // 发送回执
      WebIM.conn.send(ackMsg.body);
    }
  }
}

WebIM.isDebug = function (option) {
  if (option) {
    WebIM.config.isDebug = option.isDebug;
    openDebug(WebIM.config.isDebug);
  }

  function openDebug(value) {
    function ts() {
      var d = new Date();
      var Hours = d.getHours(); // 获取当前小时数(0-23)
      var Minutes = d.getMinutes(); // 获取当前分钟数(0-59)
      var Seconds = d.getSeconds(); // 获取当前秒数(0-59)
      return (
        (Hours < 10 ? "0" + Hours : Hours) +
        ":" +
        (Minutes < 10 ? "0" + Minutes : Minutes) +
        ":" +
        (Seconds < 10 ? "0" + Seconds : Seconds) +
        " "
      );
    }
  }
};

/**
 * Set autoSignIn as true (autoSignInName and autoSignInPwd are configured below),
 * You can auto signed in each time when you refresh the page in dev model.
 */
WebIM.config.autoSignIn = false;
if (WebIM.config.autoSignIn) {
  WebIM.config.autoSignInName = "lwz2";
  WebIM.config.autoSignInPwd = "1";
}

WebIM.parseEmoji = function (msg) {
  if (
    typeof WebIM.Emoji === "undefined" ||
    typeof WebIM.Emoji.map === "undefined"
  ) {
    return msg;
  }
  var emoji = WebIM.Emoji,
    reg = null;
  var msgList = [];
  var objList = [];
  for (var face in emoji.map) {
    if (emoji.map.hasOwnProperty(face)) {
      while (msg.indexOf(face) > -1) {
        msg = msg.replace(face, "^" + emoji.map[face] + "^");
      }
    }
  }
  var ary = msg.split("^");
  var reg = /^e.*g$/;
  for (var i = 0; i < ary.length; i++) {
    if (ary[i] != "") {
      msgList.push(ary[i]);
    }
  }
  for (var i = 0; i < msgList.length; i++) {
    if (reg.test(msgList[i])) {
      var obj = {};
      obj.data = msgList[i];
      obj.type = "emoji";
      objList.push(obj);
    } else {
      var obj = {};
      obj.data = msgList[i];
      obj.type = "txt";
      objList.push(obj);
    }
  }
  return objList;
};

WebIM.time = function () {
  var date = new Date();
  var Hours = date.getHours();
  var Minutes = date.getMinutes();
  var Seconds = date.getSeconds();
  var time =
    date.getFullYear() +
    "-" +
    (date.getMonth() + 1) +
    "-" +
    date.getDate() +
    " " +
    (Hours < 10 ? "0" + Hours : Hours) +
    ":" +
    (Minutes < 10 ? "0" + Minutes : Minutes) +
    ":" +
    (Seconds < 10 ? "0" + Seconds : Seconds);
  return time;
};

WebIM.Emoji = {
  path: "/static/images/faces/",
  map: {
    "[):]": "ee_1.png",
    "[:D]": "ee_2.png",
    "[;)]": "ee_3.png",
    "[:-o]": "ee_4.png",
    "[:p]": "ee_5.png",
    "[(H)]": "ee_6.png",
    "[:@]": "ee_7.png",
    "[:s]": "ee_8.png",
    "[:$]": "ee_9.png",
    "[:(]": "ee_10.png",
    "[:'(]": "ee_11.png",
    "[<o)]": "ee_12.png",
    "[(a)]": "ee_13.png",
    "[8o|]": "ee_14.png",
    "[8-|]": "ee_15.png",
    "[+o(]": "ee_16.png",
    "[|-)]": "ee_17.png",
    "[:|]": "ee_18.png",
    "[*-)]": "ee_19.png",
    "[:-#]": "ee_20.png",
    "[^o)]": "ee_21.png",
    "[:-*]": "ee_22.png",
    "[8-)]": "ee_23.png",
    "[(|)]": "ee_24.png",
    "[(u)]": "ee_25.png",
    "[(S)]": "ee_26.png",
    "[(*)]": "ee_27.png",
    "[(#)]": "ee_28.png",
    "[(R)]": "ee_29.png",
    "[({)]": "ee_30.png",
    "[(})]": "ee_31.png",
    "[(k)]": "ee_32.png",
    "[(F)]": "ee_33.png",
    "[(W)]": "ee_34.png",
    "[(D)]": "ee_35.png",
  },
};

WebIM.EmojiObj = {
  // 相对 emoji.js 路径
  path: "/static/images/faces/",
  map1: {
    "[):]": "ee_1.png",
    "[:D]": "ee_2.png",
    "[;)]": "ee_3.png",
    "[:-o]": "ee_4.png",
    "[:p]": "ee_5.png",
    "[(H)]": "ee_6.png",
    "[:@]": "ee_7.png",
  },
  map2: {
    "[:s]": "ee_8.png",
    "[:$]": "ee_9.png",
    "[:(]": "ee_10.png",
    "[:'(]": "ee_11.png",
    "[<o)]": "ee_12.png",
    "[(a)]": "ee_13.png",
    "[8o|]": "ee_14.png",
  },
  map3: {
    "[8-|]": "ee_15.png",
    "[+o(]": "ee_16.png",
    "[|-)]": "ee_17.png",
    "[:|]": "ee_18.png",
    "[*-)]": "ee_19.png",
    "[:-#]": "ee_20.png",
    "[del]": "del.png",
  },
  map4: {
    "[^o)]": "ee_21.png",
    "[:-*]": "ee_22.png",
    "[8-)]": "ee_23.png",
    "[(|)]": "ee_24.png",
    "[(u)]": "ee_25.png",
    "[(S)]": "ee_26.png",
    "[(*)]": "ee_27.png",
  },
  map5: {
    "[(#)]": "ee_28.png",
    "[(R)]": "ee_29.png",
    "[({)]": "ee_30.png",
    "[(})]": "ee_31.png",
    "[(k)]": "ee_32.png",
    "[(F)]": "ee_33.png",
    "[(W)]": "ee_34.png",
    "[(D)]": "ee_35.png",
  },
};

WebIM.conn = new WebIM.connection({
  isMultiLoginSessions: WebIM.config.isMultiLoginSessions,
  https:
    typeof WebIM.config.https === "boolean"
      ? WebIM.config.https
      : location.protocol === "https:",
  url: WebIM.config.xmppURL,
  apiUrl: WebIM.config.apiURL,
  isHttpDNS: WebIM.config.isHttpDNS,
  isAutoLogin: true,
  appKey: WebIM.config.appkey,
  heartBeatWait: WebIM.config.heartBeatWait,
  autoReconnectNumMax: WebIM.config.autoReconnectNumMax,
  autoReconnectInterval: WebIM.config.autoReconnectInterval,
});

// async response
WebIM.conn.listen({
  onOpened(message) {},

  onReconnect() {},

  onSocketConnected() {},

  onClosed() {
    WebIM.conn.close();
  },

  onInviteMessage(message) {},
  onDeliveredMessage(message) {},

  onReadMessage(message) {
    message.status = "read";
    // 消息漫游会触发事件
    if (!app.$store.state.chatList.id) return;
    app.$store.commit("updateMessageStatus", message);
  },

  onPresence(message) {},

  onRoster(message) {},

  onVideoMessage(message) {
    console.log(message,'onVideoMessage')
    if(message.type==='groupchat'){
      message.type = "receive";
      message.content = message.url;
      message.messType = message.contentsType.toLowerCase();
      app.$store.dispatch('setGroupChatList',message)
      ack(message);
      return
    }
    message.type = "receive";
    message.content = message.url;
    message.messType = message.contentsType.toLowerCase();

    app.$store.dispatch("setChatList", message);
    ack(message);
  },

  onAudioMessage(message) {
    console.log(message,'onAudioMessage')
    if(message.type==='groupchat'){
      message.type = "receive";
      message.content = message.url;
      message.duration = message.length;
      message.messType = message.contentsType.toLowerCase();
      app.$store.dispatch('setGroupChatList',message)
      ack(message);
      return
    }
    message.type = "receive";
    message.content = message.url;
    message.duration = message.length;
    message.messType = message.contentsType.toLowerCase();
    app.$store.dispatch("setChatList", message);

    ack(message);
  },

  onCmdMessage(message) {},

  onLocationMessage(message) {},

  // 接收文本消息
  onTextMessage(message) {
    console.info(
      "onTextMessage---------------------------收到文本消息",
      message
    );
    if(message.type==='groupchat'&&message.from != "admin"){
      console.log('groupchat---------')
      message.chatType='groupchat'
      message.type = "receive";
      message.content = message.data;
      message.messType = message.contentsType.toLowerCase();
      app.$store.dispatch('receivedMsg',message)
      // - - - - - - - - - - -
      app.$store.dispatch('setGroupChatList',message)
      // 用于测试视频通话
      if (message.ext && message.ext.conferenceId) {
        message.messType = "call";
        // 判断发送已读回执
        ack(message);
        // 是否可以通话 默认为true 若接口返回没有时间或失效了 改为false
        message.ext.isNext = true;
        app.$store.dispatch("setGroupChatList", message);
        return;
      }
      if (message.from == "admin") {
        message.patientId = "admin";
        let msg = {
          content: message.data,
          ext: message.ext,
          from: message.from,
          id: message.id,
          messType: message.contentsType.toLowerCase(),
          status: "read",
          time: message.time,
          to: message.to,
          type: "defaultMsg",
        };
        // 审核处方
        if (msg.ext.type == "HZ_WZ_SF") {
          msg.from = msg.ext.docId;
          app.$store.dispatch("setGroupChatList", msg);
          uni.$emit("reLoadPage", msg);
          return;
        }
        ack(message);
        app.$store.dispatch("setGroupChatList", message);
      } else if (message.from == uni.getStorageSync("userId")) {
        message.type = "send";
        message.from = message.ext.patientId;
        message.status = "unread";
        app.$store.dispatch("setGroupChatList", message);
      } else {
        ack(message);
        app.$store.dispatch("setGroupChatList", message);
      }
      return;
    }
    //
    message.type = "receive";
    message.content = message.data;
    message.messType = message.contentsType.toLowerCase();
    // 用于测试视频通话
    if (message.ext && message.ext.conferenceId) {
      message.messType = "call";
      // 判断发送已读回执
      ack(message);
      // 是否可以通话 默认为true 若接口返回没有时间或失效了 改为false
      message.ext.isNext = true;
      app.$store.dispatch("setChatList", message);
      return;
    }
    // SET_VIDEOOBJ

    // - - - - - - - - - - -
    if (message.from == "admin") {
      message.patientId = "admin";
      let msg = {
        content: message.data,
        ext: message.ext,
        from: message.from,
        id: message.id,
        messType: message.contentsType.toLowerCase(),
        status: "read",
        time: message.time,
        to: message.to,
        type: "defaultMsg",
      };
      // 审核处方
      if (msg.ext.type == "HZ_WZ_SF") {
        msg.from = msg.ext.docId;
        app.$store.dispatch("setChatList", msg);
        uni.$emit("reLoadPage", msg);
        return;
      }
      ack(message);
      app.$store.dispatch("setChatList", message);
    } else if (message.from == uni.getStorageSync("userId")) {
      message.type = "send";
      message.from = message.ext.patientId;
      message.status = "unread";
      app.$store.dispatch("setChatList", message);
    } else {
      ack(message);
      app.$store.dispatch("setChatList", message);
    }
  },
  // 接收表情消息
  onEmojiMessage(message) {},

  // 图片消息
  onPictureMessage(message) {
    console.log(message,'onPictureMessage')
    if(message.type==='groupchat'){
      message.type = "receive";
      message.content = message.url;
      message.messType = message.contentsType.toLowerCase();
      app.$store.dispatch('setGroupChatList',message)
      ack(message);
      return
    }
    message.type = "receive";
    message.content = message.url;
    message.messType = message.contentsType.toLowerCase();

    app.$store.dispatch("setChatList", message);

    ack(message);
  },

  onFileMessage(message) {},
  // 消息撤回回执
  onRecallMessage(message) {
    console.log(".....onRecallMessage", message);
    message.type = "reWithdraw";
    app.$store.dispatch("setRecallMsg", message);
  },

  // 各种异常
  onError(error) {
    console.log(error);
  },
});

// export default WebIM;
module.exports = {
  default: WebIM,
  emedia,
};
