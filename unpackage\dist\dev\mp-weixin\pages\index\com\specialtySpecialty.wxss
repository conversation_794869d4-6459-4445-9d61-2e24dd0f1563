/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*
	字体颜色相关
*/
/*
	背景色 分割线色
*/
/*
	主题色
*/
/*
		渐变可调角度
*/
/*
	定义弹性布局
 */
/*
	单行文本溢出隐藏
*/
.spe_box.data-v-fd11c83e {
  background: #fff;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}
.spe_box .spe_title.data-v-fd11c83e {
  padding: 0 32rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.spe_box .spe_img.data-v-fd11c83e {
  width: 94%;
  height: 160rpx;
  border-radius: 8rpx;
  margin: 20rpx;
}