!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.websdk=t():e.websdk=t()}(self,(function(){return(self.webpackChunkwebsdk=self.webpackChunkwebsdk||[]).push([[226],{5658:function(e,t,n){n.r(t),n.d(t,{LocalCache:function(){return D},LocalCacheApi:function(){return r}});var r={};let o,s;n.r(r),n.d(r,{clearConversationUnreadCount:function(){return U},getLocalConversation:function(){return j},getLocalConversations:function(){return R},removeLocalConversation:function(){return N},setLocalConversationCustomField:function(){return P}}),n(2675),n(9463),n(2259),n(4423),n(3792),n(2062),n(3288),n(6099),n(3362),n(7764),n(2953),n(5276),n(9085);const a=new WeakMap,i=new WeakMap,c=new WeakMap,u=new WeakMap,l=new WeakMap;let d={get(e,t,n){if(e instanceof IDBTransaction){if("done"===t)return i.get(e);if("objectStoreNames"===t)return e.objectStoreNames||c.get(e);if("store"===t)return n.objectStoreNames[1]?void 0:n.objectStore(n.objectStoreNames[0])}return p(e[t])},set(e,t,n){return e[t]=n,!0},has(e,t){return e instanceof IDBTransaction&&("done"===t||"store"===t)||t in e}};function v(e){return"function"==typeof e?(t=e)!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?(s||(s=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])).includes(t)?function(...e){return t.apply(h(this),e),p(a.get(this))}:function(...e){return p(t.apply(h(this),e))}:function(e,...n){const r=t.call(h(this),e,...n);return c.set(r,e.sort?e.sort():[e]),p(r)}:(e instanceof IDBTransaction&&function(e){if(i.has(e))return;const t=new Promise(((t,n)=>{const r=()=>{e.removeEventListener("complete",o),e.removeEventListener("error",s),e.removeEventListener("abort",s)},o=()=>{t(),r()},s=()=>{n(e.error||new DOMException("AbortError","AbortError")),r()};e.addEventListener("complete",o),e.addEventListener("error",s),e.addEventListener("abort",s)}));i.set(e,t)}(e),n=e,(o||(o=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])).some((e=>n instanceof e))?new Proxy(e,d):e);var t,n}function p(e){if(e instanceof IDBRequest)return function(e){const t=new Promise(((t,n)=>{const r=()=>{e.removeEventListener("success",o),e.removeEventListener("error",s)},o=()=>{t(p(e.result)),r()},s=()=>{n(e.error),r()};e.addEventListener("success",o),e.addEventListener("error",s)}));return t.then((t=>{t instanceof IDBCursor&&a.set(t,e)})).catch((()=>{})),l.set(t,e),t}(e);if(u.has(e))return u.get(e);const t=v(e);return t!==e&&(u.set(e,t),l.set(t,e)),t}const h=e=>l.get(e);function f(e,t,{blocked:n,upgrade:r,blocking:o,terminated:s}={}){const a=indexedDB.open(e,t),i=p(a);return r&&a.addEventListener("upgradeneeded",(e=>{r(p(a.result),e.oldVersion,e.newVersion,p(a.transaction),e)})),n&&a.addEventListener("blocked",(e=>n(e.oldVersion,e.newVersion,e))),i.then((e=>{s&&e.addEventListener("close",(()=>s())),o&&e.addEventListener("versionchange",(e=>o(e.oldVersion,e.newVersion,e)))})).catch((()=>{})),i}const y=["get","getKey","getAll","getAllKeys","count"],g=["put","add","delete","clear"],b=new Map;function m(e,t){if(!(e instanceof IDBDatabase)||t in e||"string"!=typeof t)return;if(b.get(t))return b.get(t);const n=t.replace(/FromIndex$/,""),r=t!==n,o=g.includes(n);if(!(n in(r?IDBIndex:IDBObjectStore).prototype)||!o&&!y.includes(n))return;const s=async function(e,...t){const s=this.transaction(e,o?"readwrite":"readonly");let a=s.store;return r&&(a=a.index(t.shift())),(await Promise.all([a[n](...t),o&&s.done]))[0]};return b.set(t,s),s}var I;I=d,d={...I,get:(e,t,n)=>m(e,t)||I.get(e,t,n),has:(e,t)=>!!m(e,t)||I.has(e,t)};var C=n(2056),w=n(564),T=n(8232),S=function(){return S=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},S.apply(this,arguments)},M=function(e,t,n,r){return new(n||(n=Promise))((function(o,s){function a(e){try{c(r.next(e))}catch(e){s(e)}}function i(e){try{c(r.throw(e))}catch(e){s(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,i)}c((r=r.apply(e,t||[])).next())}))},L=function(e,t){var n,r,o,s,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return s={next:i(0),throw:i(1),return:i(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function i(s){return function(i){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&s[0]?r.return:s[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,s[1])).done)return o;switch(r=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,r=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],r=0}finally{n=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,i])}}},B=new Date("2023/01/01"),D=function(){function e(e){var t=e.user,n=e.dbName,r=e.version,o=e.onInit;this.localCache=void 0,this.user="",this.conversationMap={},this.onInit=o,this.init({user:t,dbName:n,version:r})}return e.prototype.init=function(t){return M(this,void 0,void 0,(function(){var n,r,o,s;return L(this,(function(a){switch(a.label){case 0:C.vF.debug("init local cache",t),n=t.dbName,r=t.version,o=t.user,this.user=o,a.label=1;case 1:return a.trys.push([1,3,,4]),[4,f(n,r,{upgrade:function(e,t,n,r){if(t<1){C.vF.debug("IndexedDB oldVersion",t);var o=e.createObjectStore("message",{keyPath:"id"});o.createIndex("sessionId","sessionId"),o.createIndex("time","time"),o.createIndex("serverMsgId","serverMsgId",{unique:!0}),o.createIndex("sessionTime",["sessionId","time"],{unique:!0}),e.createObjectStore("conversationList",{keyPath:"sessionId"}).createIndex("updateTime","updateTime")}if(t<2){C.vF.debug("IndexedDB version",2);var s=r.objectStore("message");s.deleteIndex("sessionTime"),s.createIndex("sessionTime",["sessionId","time"])}}})];case 2:return s=a.sent(),this.localCache=s,e.instance||(e.instance=this,this.onInit()),[3,4];case 3:return a.sent(),C.vF.debug("open db error"),[3,4];case 4:return[2]}}))}))},e.getInstance=function(){return e.instance},e.prototype.close=function(){var t;null===(t=this.localCache)||void 0===t||t.close(),this.clearConversationMap(),e.instance=void 0,C.vF.debug("close db success")},e.prototype.storeConversation=function(e,t,n,r){return M(this,void 0,void 0,(function(){var o,s,a,i,c;return L(this,(function(u){switch(u.label){case 0:return u.trys.push([0,4,,5]),o=(0,w.u0)({conversationId:e,conversationType:t}),s=S({conversationId:e,conversationType:t,sessionId:o},n),(i=this.conversationMap[o])?[3,2]:[4,this.getConversationBySessionId(o)];case 1:i=u.sent(),u.label=2;case 2:return a=i||s,this.conversationMap[o]||(this.conversationMap[o]=a),a&&(r&&(this.conversationMap[o].unReadCount=(a.unReadCount||0)+1,s.unReadCount=this.conversationMap[o].unReadCount||0),s=S(S({},a),s)),[4,this.putConversationToDB(s)];case 3:return u.sent(),[3,5];case 4:return c=u.sent(),C.vF.debug("store conversation error",c),[3,5];case 5:return[2]}}))}))},e.prototype.putConversationToDB=function(e){var t;return M(this,void 0,void 0,(function(){var n;return L(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,null===(t=this.localCache)||void 0===t?void 0:t.put("conversationList",e)];case 1:return r.sent(),[3,3];case 2:return n=r.sent(),C.vF.debug("put conversation to local error",n),[3,3];case 3:return[2]}}))}))},e.prototype.getConversationBySessionId=function(e){var t;return M(this,void 0,void 0,(function(){var n;return L(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,null===(t=this.localCache)||void 0===t?void 0:t.get("conversationList",e)];case 1:return[2,r.sent()];case 2:return n=r.sent(),C.vF.debug("get conversation by sessionId error",n),[3,3];case 3:return[2]}}))}))},e.prototype.removeConversationBySessionId=function(e){var t;return M(this,void 0,void 0,(function(){return L(this,(function(n){try{return this.conversationMap[e]&&delete this.conversationMap[e],[2,null===(t=this.localCache)||void 0===t?void 0:t.delete("conversationList",e)]}catch(e){C.vF.debug("remove conversation by sessionId error",e)}return[2]}))}))},e.prototype.getConversationList=function(){var e;return M(this,void 0,void 0,(function(){var t,n,r;return L(this,(function(o){switch(o.label){case 0:t=[],o.label=1;case 1:return o.trys.push([1,6,,7]),[4,null===(e=this.localCache)||void 0===e?void 0:e.transaction("conversationList").store.index("updateTime").openCursor(null,"prev")];case 2:n=o.sent(),o.label=3;case 3:return n?(t.push(n.value),[4,n.continue()]):[3,5];case 4:return n=o.sent(),[3,3];case 5:return[2,t];case 6:return r=o.sent(),C.vF.debug("get local conversationList error",r),[3,7];case 7:return[2]}}))}))},e.prototype.storeMessage=function(e,t,n){return M(this,void 0,void 0,(function(){var r,o,s,a,i,c,u,l,d;return L(this,(function(v){switch(v.label){case 0:return v.trys.push([0,5,,6]),r=S({},e),o={},s=!1,a=r.to,i=!1,"chatRoom"===r.chatType||r.chatThread||r.isChatThread?[2]:("groupChat"===r.chatType?s=Boolean(r.from&&r.from!==this.user):"singleChat"===r.chatType&&(c=r.to.indexOf("/"),u=c>-1?r.to.substring(0,c):r.to,this.user===u&&r.from&&r.from!==this.user&&(s=!0,a=r.from||r.to)),"audio"!==r.type&&"video"!==r.type&&"file"!==r.type&&"combine"!==r.type&&"img"!==r.type||(delete r.onFileUploadComplete,delete r.onFileUploadError,delete r.onFileUploadProgress),"combine"!==r.type&&(delete r.success,delete r.fail),Object.assign(r,{sessionId:(0,w.u0)({conversationId:a,conversationType:null==r?void 0:r.chatType}),status:t,serverMsgId:r.id}),[4,this.getMessageByServerMsgId(r.id)]);case 1:return(null==(l=v.sent())?void 0:l.status)===T.q.SUCCESS?[3,3]:[4,this.putMessageToDB(r)];case 2:v.sent(),v.label=3;case 3:return t>T.q.INPROGRESS&&Object.assign(o,{updateTime:r.time,lastMessageId:r.id}),s&&(i=!0,Object.assign(o,{lastMessageFromOtherId:r.id})),[4,this.storeConversation(a,r.chatType,o,!n&&i)];case 4:return v.sent(),[3,6];case 5:return d=v.sent(),C.vF.debug("store message error",d),[3,6];case 6:return[2]}}))}))},e.prototype.updateLocalMessage=function(e,t){return void 0===t&&(t={}),M(this,void 0,void 0,(function(){var n,r;return L(this,(function(o){switch(o.label){case 0:return o.trys.push([0,5,,6]),[4,this.getMessageByServerMsgId(e)];case 1:return(n=o.sent())?[4,this.putMessageToDB(S(S({},n),t))]:[3,4];case 2:return o.sent(),[4,this.updateLocalConversation((0,w.u0)({conversationId:n.to,conversationType:n.chatType}),{lastMessageId:t.serverMsgId,updateTime:n.time})];case 3:o.sent(),o.label=4;case 4:return[3,6];case 5:return r=o.sent(),C.vF.debug("update local message error",r),[3,6];case 6:return[2]}}))}))},e.prototype.updateLocalConversation=function(e,t){return M(this,void 0,void 0,(function(){var n=this;return L(this,(function(r){try{return[2,this.getConversationBySessionId(e).then((function(r){if(r){var o=S(S({},r),t);n.putConversationToDB(o),n.conversationMap[e]=o}}))]}catch(e){C.vF.debug("update local conversation error",e)}return[2]}))}))},e.prototype.removeMsgByServerMsgId=function(e){var t,n;return M(this,void 0,void 0,(function(){var r,o;return L(this,(function(s){switch(s.label){case 0:return s.trys.push([0,2,,3]),[4,null===(t=this.localCache)||void 0===t?void 0:t.getFromIndex("message","serverMsgId",e)];case 1:return r=s.sent(),[2,null===(n=this.localCache)||void 0===n?void 0:n.delete("message",(null==r?void 0:r.id)||"")];case 2:return o=s.sent(),C.vF.debug("remove msg by serverId error",o),[3,3];case 3:return[2]}}))}))},e.prototype.removeMsgBySessionId=function(e){var t;return M(this,void 0,void 0,(function(){var n,r;return L(this,(function(o){switch(o.label){case 0:return o.trys.push([0,6,,7]),[4,null===(t=this.localCache)||void 0===t?void 0:t.transaction("message","readwrite").store.index("sessionId").openCursor(IDBKeyRange.only(e))];case 1:n=o.sent(),o.label=2;case 2:return n?[4,n.delete()]:[3,5];case 3:return o.sent(),[4,n.continue()];case 4:return n=o.sent(),[3,2];case 5:return[3,7];case 6:return r=o.sent(),C.vF.debug("remove msg by sessionId",r),[3,7];case 7:return[2]}}))}))},e.prototype.putMessageToDB=function(e){var t;return M(this,void 0,void 0,(function(){var n;return L(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,null===(t=this.localCache)||void 0===t?void 0:t.put("message",e)];case 1:return[2,r.sent()];case 2:return n=r.sent(),C.vF.debug("put msg to local error",n),[3,3];case 3:return[2]}}))}))},e.prototype.getConversationLastMessage=function(e,t){var n,r;return M(this,void 0,void 0,(function(){var o,s,a;return L(this,(function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),o=(0,w.u0)({conversationId:e,conversationType:t}),[4,null===(r=null===(n=this.localCache)||void 0===n?void 0:n.transaction("message").store)||void 0===r?void 0:r.index("sessionTime").openCursor(IDBKeyRange.bound([o,B.getTime()],[o,(new Date).getTime()]),"prev")];case 1:return(s=i.sent())?[2,s.value]:[2,void 0];case 2:return a=i.sent(),C.vF.debug("get conversation last msg error",a),[3,3];case 3:return[2]}}))}))},e.prototype.getMessageByServerMsgId=function(e){var t;return M(this,void 0,void 0,(function(){var n;return L(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,null===(t=this.localCache)||void 0===t?void 0:t.getFromIndex("message","serverMsgId",e)];case 1:return[2,r.sent()];case 2:return n=r.sent(),C.vF.debug("get msg by serverId error",n),[3,3];case 3:return[2]}}))}))},e.prototype.clearStoreData=function(e){var t;return M(this,void 0,void 0,(function(){var n;return L(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,null===(t=this.localCache)||void 0===t?void 0:t.clear(e)];case 1:return[2,r.sent()];case 2:return n=r.sent(),C.vF.debug("clear store data error",n),[3,3];case 3:return[2]}}))}))},e.prototype.clearConversationMap=function(){this.conversationMap={}},e}(),E=n(346),F=n(1750),A=n(1531);function k(e){return k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},k(e)}var x=function(e,t,n,r){return new(n||(n=Promise))((function(o,s){function a(e){try{c(r.next(e))}catch(e){s(e)}}function i(e){try{c(r.throw(e))}catch(e){s(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,i)}c((r=r.apply(e,t||[])).next())}))},O=function(e,t){var n,r,o,s,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return s={next:i(0),throw:i(1),return:i(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function i(s){return function(i){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&s[0]?r.return:s[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,s[1])).done)return o;switch(r=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,r=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],r=0}finally{n=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,i])}}},_=["singleChat","groupChat"];function R(){var e,t;return x(this,void 0,void 0,(function(){var n,r,o,s=this;return O(this,(function(a){switch(a.label){case 0:if(C.vF.debug("Call getLocalConversations"),!F.dO.call(this))return n=E.A.create({type:A.C.REST_PARAMS_STATUS,message:"appkey or token error"}),[2,Promise.reject(n)];a.label=1;case 1:return a.trys.push([1,4,,5]),[4,null===(t=null===(e=this._localCache)||void 0===e?void 0:e.getInstance())||void 0===t?void 0:t.getConversationList()];case 2:return r=a.sent()||[],[4,Promise.all(r.map((function(e){var t=e.conversationId,n=e.lastMessageId,r=e.unReadCount,o=e.conversationType,a=e.customField;return x(s,void 0,void 0,(function(){var e,s,i,c;return O(this,(function(u){switch(u.label){case 0:return s={conversationId:t,conversationType:o,unReadCount:r||0,customField:a},n?[4,null===(c=null===(i=this._localCache)||void 0===i?void 0:i.getInstance())||void 0===c?void 0:c.getMessageByServerMsgId(n)]:[3,2];case 1:return e=u.sent(),[3,3];case 2:e=void 0,u.label=3;case 3:return[2,(s.lastMessage=e,s)]}}))}))})))];case 3:return[2,{type:0,data:a.sent()}];case 4:throw o=a.sent(),E.A.create({type:A.C.LOCAL_DB_OPERATION_FAILED,message:"getLocalConversations is failed",data:o});case 5:return[2]}}))}))}function P(e){var t,n;return x(this,void 0,void 0,(function(){var r,o,s,a,i;return O(this,(function(c){switch(c.label){case 0:if(C.vF.debug("Call setLocalConversationCustomField",e),!F.dO.call(this))return r=E.A.create({type:A.C.REST_PARAMS_STATUS,message:"appkey or token error"}),[2,Promise.reject(r)];if(o=e.conversationType,s=e.conversationId,a=e.customField,!_.includes(o))throw Error('"Invalid parameter conversationType": '+o);if("string"!=typeof s||""===s)throw Error('Invalid parameter: "conversationId"');if("object"!==k(a))throw Error('Invalid parameter: "customField"');c.label=1;case 1:return c.trys.push([1,3,,4]),[4,null===(n=null===(t=this._localCache)||void 0===t?void 0:t.getInstance())||void 0===n?void 0:n.updateLocalConversation((0,w.u0)({conversationId:s,conversationType:o}),{customField:a})];case 2:return c.sent(),[3,4];case 3:throw i=c.sent(),E.A.create({type:A.C.LOCAL_DB_OPERATION_FAILED,message:"setLocalConversationCustomField is failed",data:i});case 4:return[2]}}))}))}function j(e){var t,n,r,o;return x(this,void 0,void 0,(function(){var s,a,i,c,u,l,d,v,p;return O(this,(function(h){switch(h.label){case 0:if(C.vF.debug("Call getLocalConversation",e),!F.dO.call(this))return s=E.A.create({type:A.C.REST_PARAMS_STATUS,message:"appkey or token error"}),[2,Promise.reject(s)];if(a=e.conversationType,i=e.conversationId,!_.includes(a))throw Error('"Invalid parameter conversationType": '+a);if("string"!=typeof i||""===i)throw Error('Invalid parameter: "conversationId"');h.label=1;case 1:return h.trys.push([1,8,,9]),[4,null===(n=null===(t=this._localCache)||void 0===t?void 0:t.getInstance())||void 0===n?void 0:n.getConversationBySessionId((0,w.u0)({conversationId:i,conversationType:a}))];case 2:return c=h.sent(),v={type:0},c?(p={conversationId:c.conversationId,conversationType:a,unReadCount:c.unReadCount||0,customField:c.customField},c.lastMessageId?[4,null===(o=null===(r=this._localCache)||void 0===r?void 0:r.getInstance())||void 0===o?void 0:o.getMessageByServerMsgId(c.lastMessageId)]:[3,4]):[3,6];case 3:return l=h.sent(),[3,5];case 4:l=void 0,h.label=5;case 5:return p.lastMessage=l,u=p,[3,7];case 6:u=void 0,h.label=7;case 7:return[2,(v.data=u,v)];case 8:throw d=h.sent(),E.A.create({type:A.C.LOCAL_DB_OPERATION_FAILED,message:"getLocalConversation is failed",data:d});case 9:return[2]}}))}))}function N(e){var t,n,r,o;return x(this,void 0,void 0,(function(){var s,a,i,c,u,l,d;return O(this,(function(v){switch(v.label){case 0:if(C.vF.debug("Call removeLocalConversation",e),!F.dO.call(this))return s=E.A.create({type:A.C.REST_PARAMS_STATUS,message:"appkey or token error"}),[2,Promise.reject(s)];if(a=e.conversationType,i=e.conversationId,c=e.isRemoveLocalMessage,u=void 0===c||c,!_.includes(a))throw Error('"Invalid parameter conversationType": '+a);if("string"!=typeof i||""===i)throw Error('Invalid parameter: "conversationId"');l=(0,w.u0)({conversationId:i,conversationType:a}),v.label=1;case 1:return v.trys.push([1,5,,6]),[4,null===(n=null===(t=this._localCache)||void 0===t?void 0:t.getInstance())||void 0===n?void 0:n.removeConversationBySessionId(l)];case 2:return v.sent(),u?[4,null===(o=null===(r=this._localCache)||void 0===r?void 0:r.getInstance())||void 0===o?void 0:o.removeMsgBySessionId(l)]:[3,4];case 3:v.sent(),v.label=4;case 4:return[3,6];case 5:throw d=v.sent(),E.A.create({type:A.C.LOCAL_DB_OPERATION_FAILED,message:"removeLocalConversation is failed",data:d});case 6:return[2]}}))}))}function U(e){var t,n;return x(this,void 0,void 0,(function(){var r,o,s,a;return O(this,(function(i){switch(i.label){case 0:if(C.vF.debug("Call clearConversationUnreadCount",e),!F.dO.call(this))return r=E.A.create({type:A.C.REST_PARAMS_STATUS,message:"appkey or token error"}),[2,Promise.reject(r)];if(o=e.conversationType,s=e.conversationId,!_.includes(o))throw Error('"Invalid parameter conversationType": '+o);if("string"!=typeof s||""===s)throw Error('Invalid parameter: "conversationId"');i.label=1;case 1:return i.trys.push([1,3,,4]),[4,null===(n=null===(t=this._localCache)||void 0===t?void 0:t.getInstance())||void 0===n?void 0:n.updateLocalConversation((0,w.u0)({conversationId:s,conversationType:o}),{unReadCount:0,unreadCountClearTimestamp:(new Date).getTime()})];case 2:return i.sent(),[3,4];case 3:throw a=i.sent(),E.A.create({type:A.C.LOCAL_DB_OPERATION_FAILED,message:"clearConversationUnreadCount is failed",data:a});case 4:return[2]}}))}))}}},function(e){return e(e.s=5658)}])}));