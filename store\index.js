import { createStore } from 'vuex'
import global from '@/store/modules/global'
import getters from './getters'
const indexedDB={}
const store = createStore({
  state: {
    isLast: false,
    chatList: [],
    groupChatInfo: {},
    groupChatAllList: [],
    chatListDoc: [],
    appId: uni.getStorageSync('appId') || '',
    wxInfo: uni.getStorageSync('wxInfo') || '',
    proPfInfo: uni.getStorageSync('proPfInfo') || '',
    wholeArg: uni.getStorageSync('wholeArg') || '',
    // - - - - - -
    videoObj: {},
    // 系统通知详情
    notice_detail: '',
    // 接诊状态
    jz_status: '',
    // 消息撤回
    msgRecall: '',
    diagList: [],
    drugList: [],
    selectedOrders: [],
    drugRules: [] // 药品加减规则配置
  },
  mutations: {
    // 接诊状态 用于页面中监听
    SET_JZ_STATUS(state) {
      state.jz_status = new Date().getTime()
    },
    SET_MSG_RECALL(state, val) {
      state.msgRecall = val.mid
    },
    // 测试视频通话
    SET_VIDEOOBJ(state, obj) {
      state.videoObj = obj
    },

    // 设置通知详情
    SET_NOTICE_DETAIL(state, val) {
      state.notice_detail = val
    },

    // 设置聊天
    SET_CHATLIST(state, val) {
      state.chatList = val
    },
    SET_CHATLISTDOC(state, val) {
      state.chatListDoc = val
    },
    setOneChatList(state, value) {
      let list
      if (JSON.stringify(state.chatList).indexOf(value.chatId) == -1) {
        let obj = {
          id: value.chatId,
          chatList: value.chatRecordList
        }
        indexedDB.updateCardStatus(obj).then((res) => {
          if (res) {
            store.commit('getChatListId', {
              chatId: value.chatId
            })
            store.dispatch('getChatListDoc')
          }
        })
      } else {
        Array.isArray(state.chatList) &&
          state.chatList.map((item, index) => {
            if (value.chatId == item.id) {
              list = item.chatRecordList
              if (list[0].time >= value.chatRecordList[0].time) {
                item.chatRecordList = value.chatRecordList
              } else {
                let s = false
                let m = item.chatRecordList.length - 1
                for (; m >= 0; m--) {
                  let el = list[m]
                  let val = value.chatRecordList[0]
                  if (val.time > el.time) {
                    let arr = list.slice(0, m + 1)
                    arr = arr.concat(value.chatRecordList)
                    item.chatRecordList = arr
                    let obj = {
                      id: value.chatId,
                      chatList: arr
                    }
                    indexedDB.updateCardStatus(obj).then((res) => {
                      if (res) {
                        store.commit('getChatListId', {
                          chatId: value.chatId
                        })
                        store.dispatch('getChatListDoc')
                      }
                    })
                    break
                  }
                }
              }
            }
          })
      }
    },
    //挂号成功，签到成功
    setEmptyList(state, value) {
      let id = value.patientId.toLowerCase() + ',' + value.docId.toLowerCase()
      let arr = []
      state.chatListDoc.map((item) => {
        arr.push(item.id)
      })
      if (arr.indexOf(id) == -1) {
        // 如果是空数组
        if (Array.isArray(state.chatList)) {
          // 赋值空对象
          state.chatList = {
            id: id,
            chatRecordList: [],
            top: false
          }
        }

        let obj = {
          id: id,
          docId: value.docId,
          docImg: value.docImg,
          docName: value.docName
        }
        indexedDB.putData(obj)
      }
    },
    //消息状态已读，未读
    updateMessageStatus(state, message) {
      let param = uni.getStorageSync('chatItem')
      let obj = {
        id: state.chatList.id,
        message: message
      }
      indexedDB.updateChatStatus(obj).then((res) => {
        // if(res.id == state.chatList.id) {
        state.chatList = res
        uni.setStorageSync('chatList', state.chatList)
        // }
      })
    },
    async setAllChatList(state, value) {
      for (let i = 0; i < value.length; i++) {
        let res = await indexedDB.updateChatDataValList({
          id: value[i].id,
          data: value[i]
        })
      }
      state.chatListDoc = Object.assign([], value)
    },
    getChatSearchName(state, name) {
      indexedDB.searchName(name).then((res) => {
        state.chatListDoc = res
      })
    },
    getChatListId(state, value) {
      indexedDB.reaData(value.chatId).then((res) => {
        if (res) state.chatList = res
        if (value.cabllack && typeof value.cabllack == 'function') {
          value.cabllack()
        }
      })
    },
    updateCardStatus(state, value) {
      indexedDB.updateCardStatus(value)
    },
    //消息撤回 暂时无用
    setRecallMsg(state, value) {
      let chatItem
      state.chatListDoc.map((item, index) => {
        let list = item.chatRecordList
        list.map((el, elIndex) => {
          if (el.id == value.mid) {
            el.type = 'reWithdraw'
            chatItem = item
          }
        })
      })
      state.chatListDoc = Object.assign([], state.chatListDoc)
    },
    // 存储appId
    setAppId(state, value) {
      state.appId = value
      uni.setStorageSync('appId', value)
    },
    // 存储微信信息
    setWxInfo(state, value) {
      state.wxInfo = value
      uni.setStorageSync('wxInfo', value)
    },
    // 存储用户信息
    setProPfInfo(state, value) {
      state.proPfInfo = value
      uni.setStorageSync('proPfInfo', value)
    },
    // 存储全局参数
    setWholeArg(state, value) {
      state.wholeArg = value
      uni.setStorageSync('wholeArg', value)
    },
    setDiagList(state, value) {
      state.diagList = value
    },
    addDrugList(state, value) {
      // state.drugList = value;
      state.drugList.push({
        ...value
      })
    },
    setDrugList(state, value) {
      state.drugList = value
    },
    setDrugRules(state, value) {
      state.drugRules = value
    },
    setGroupList(state, value) {
      state.groupChatInfo = value
      state.isLast = false
    },
    setSelectedOrders(state, value) {
      state.selectedOrders = value
    }
  },
  // 异步分发
  actions: {
    // 获取药品加减规则配置
    async getDrugRules({ commit }) {
      try {
        const { data } = await getDrugShoppingCartConfigs()
        if (data && Array.isArray(data)) {
          console.log(data, 'getDrugRules')
          commit('setDrugRules', data)
        } else {
          // 如果接口返回数据格式不正确，使用默认规则
          commit('setDrugRules', [])
        }
      } catch (error) {
        console.error('获取药品规则配置失败:', error)
        // 接口调用失败时使用默认规则
        commit('setDrugRules', [])
      }
    },
    receivedMsg({ commit, dispatch, state }, payload) {
      console.log('sss')
      state.groupChatAllList.push(payload)
    },
    setGroupChatList({ commit, dispatch, state }, payload) {
      if (!state.groupChatInfo.groupId) {
        return
      }
      console.log('setGroupChatList', payload)
      if (payload.to !== state.groupChatInfo.groupId) {
        return
      }
      if (payload.show) {
        const index = state.groupChatInfo?.chatRecordList?.findIndex((v) => v.isFill)
        if (index > -1) {
          state.groupChatInfo.chatRecordList[index] = payload
        }
        return
      }
      if (payload.isFill) {
        const index = state.groupChatInfo?.chatRecordList?.findIndex((v) => v.isFill)
        if (index > -1) {
          state.groupChatInfo.chatRecordList[index] = payload
        } else {
          state.groupChatInfo.chatRecordList.push(payload)
        }
        return
      }
      if (state.groupChatInfo.chatRecordList) {
        if (state.groupChatInfo?.chatRecordList?.findIndex((v) => v.id === (payload.mid || payload.id)) < 0) {
          state.groupChatInfo.chatRecordList.push(payload)
        } else {
          state.groupChatInfo.chatRecordList = state.groupChatInfo.chatRecordList.map((v) => {
            if (v.id === (payload.mid || payload.id)) {
              v = payload
            }
            return {
              ...v
            }
          })
        }
      }
    },
    async getCurrentGroupChatList({ state, commit, dispatch }, value) {
      // 最后一页
      if (state.isLast) {
        return
      }
      const historyMessages = await WebIM.conn.getHistoryMessages({
        targetId: value.groupId, // 单聊为对端用户 ID，群组聊天为群组 ID。
        chatType: 'groupChat', // 会话类型：单聊、群组聊天和聊天室分别为 `singleChat`、`groupChat` 和 `chatRoom`。
        cursor: value.cursor || -1,
        pageSize: 20, // 每次获取的消息数量，取值范围为 [1,50]，默认值为 `20`。
        searchDirection: 'up', // 消息搜索方向。`up` 表示按消息时间戳递减的方向获取，即先获取最新消息；`down` 表示按消息时间戳递增的方向获取，即先获取最老的消息。
        searchOptions: {}
      })
      console.log('getHistoryMessages------')
      console.log(historyMessages)
      state.isLast = historyMessages.isLast
      const messages = historyMessages.messages.reverse().map((v) => {
        v.messType = map[v.type] || v.type
        v.duration = v.length
        if (v.from == uni.getStorageSync('userId')) {
          v.type = 'send'
        } else {
          v.type = 'receive'
        }
        return {
          ...v,
          status: 'read',
          content: v.msg || v.url
        }
      })
      const chatRecordList = state.groupChatInfo?.chatRecordList || []
      const oldList = state.groupChatInfo?.oldList || []
      state.groupChatInfo = {
        ...value,
        groupId: value.groupId,
        oldList: [...messages, ...oldList],
        chatRecordList: [...messages, ...chatRecordList].filter((v) => {
          if (v.ext && v.ext.messType === 'updatePatient') {
            return false
          }
          return true
        }),
        dataVal: {
          ...value
        },
        time: '',
        userId: uni.getStorageSync('userId')
      }
    },
    //消息撤回 暂时无用
    setRecallMsg({ state, commit, dispatch }, value) {
      commit('SET_MSG_RECALL', value)
      let chatListDoc = Object.assign([], state.chatListDoc)
      let chatItem
      console.log('chatListDoc', chatListDoc)
      chatListDoc.map((item, index) => {
        let list = item.chatRecordList
        list.map((el) => {
          if (el.id == value.mid || el.mid == value.mid) {
            if (value.type == 'reWithdraw') {
              el.type = 'reWithdraw'
            }
            if (value.type == 'withdraw') {
              el.type = 'withdraw'
            }
            console.log(item)
            chatItem = item
          }
        })
      })

      commit('SET_CHATLISTDOC', chatListDoc)
      if (chatItem) {
        dispatch('setOneChatList', {
          chatId: chatItem.id,
          chatRecordList: chatItem.chatRecordList
        })
      }
    },
    // 用户不存在时候 清空本地聊天记录
    clearDB() {
      indexedDB.clearDB()
    },
    // 获取所有聊天列表
    async getChatListDoc({ commit }) {
      let res = await indexedDB.readAll()
      commit('SET_CHATLISTDOC', res)
      return Promise.resolve()
    },
    // 设置视频邀请状态
    async setVideoStatus({ dispatch }, obj) {
      try {
        await indexedDB.upVideoInvitaionStatus(obj)

        await dispatch('getChatListId', {
          chatId: obj.id
        })
      } catch (e) {}
      return Promise.resolve()
    },

    // 设置聊天列表
    async setChatList({ commit, dispatch }, payload) {
      let status = 'unread'
      if (payload.type != 'send') {
        let pages = getCurrentPages()
        let curPage = pages[pages.length - 1]
        let curParam = curPage.options || curPage.$route.query
        if (curParam && curParam.param) {
          let param = (curParam = JSON.parse(curParam.param))
          if (param && param.docId) {
            var id = param.docId ? param.docId.toLowerCase() : ''
            if (id == payload.from) {
              status = 'read'
            }
          }
        } else {
          status = payload.status || 'unread'
        }
        var id = payload.patientId || payload.ext.patientId.toLowerCase() + ',' + payload.from.toLowerCase()
        if (payload.from == 'admin') {
          id = 'admin,admin'
        }

        if (payload.ext && payload.ext.type == 'lb') {
          let isFeedback = 0 // 是否反馈 0否，1是
          payload.ext.isFeedback = isFeedback
        } else if (payload.ext && payload.ext.type == 'zz') {
          let isSuccess = 0
          payload.ext.isSuccess = isSuccess
        } else if (payload.ext && payload.ext.type == 'lisOrder') {
          let businessCode = payload.ext.businessCode
        } else if (payload.ext && payload.ext.type == 'zdyfw') {
          let isSuccess = 0
          payload.ext.isSuccess = isSuccess
        } else if (payload.ext && payload.ext.type == 'blsq') {
          let isSuccess = 1
          payload.ext.isSuccess = isSuccess
        } else if (payload.ext && payload.ext.type == 'jcd') {
          let isSuccess = 1
          payload.ext.isSuccess = isSuccess
        }

        // 问诊状态
        let statusArr = ['HZ_WZ_YSJZ', 'HZ_WZ_JSWZ', 'HZ_WZ_YSTZ', 'HZ_WZ_JJJZ']
        // 如果有更新
        if (statusArr.includes(payload.ext.type)) {
          commit('SET_JZ_STATUS')
        }
      } else {
        var id = payload.ext.patientId.toLowerCase() + ',' + payload.to.toLowerCase()
        if (!status) {
          status = 'unread'
        }
      }
      payload.status = status
      let obj = {
        id: id,
        data: payload
      }
      console.log(obj)
      await indexedDB.updateChatList(obj)
      await dispatch('getChatListId', {
        chatId: obj.id
      })
      await dispatch('getChatListDoc')
    },
    // 聊天列表id
    async getChatListId({ dispatch, commit, state }, obj) {
      await indexedDB.reaData(obj.chatId).then((res) => {
        // 如果当前是空的
        // if (Array.isArray(state.chatList)) {
        //   commit("SET_CHATLIST", res);
        // }
        // // 如果要更新聊天记录 跟当前一样 则更新
        // if (res.id == state.chatList.id) {
        //   commit("SET_CHATLIST", res);
        // }
        // //找回聊天记录
        // if (obj.updata) {
        //   commit("SET_CHATLIST", res);
        // }
        commit('SET_CHATLIST', res)
        return Promise.resolve()
      })
    },

    async setOneChatList({ dispatch, commit, state }, value) {
      let list
      if (JSON.stringify(state.chatList).indexOf(value.chatId) > -1) {
        let obj = {
          id: value.chatId,
          chatList: value.chatRecordList
        }
        let res = await indexedDB.updateCardStatus(obj)

        if (res) {
          await dispatch('getChatListId', {
            chatId: value.chatId
          })
          await dispatch('getChatListDoc')
        }
      } else {
        state.chatListDoc.map((item, index) => {
          if (value.chatId == item.id) {
            list = item.chatRecordList
            if (list[0].time >= value.chatRecordList[0].time) {
              item.chatRecordList = value.chatRecordList
            } else {
              let s = false
              let m = item.chatRecordList.length - 1
              for (; m >= 0; m--) {
                let el = list[m]
                let val = value.chatRecordList[0]
                if (val.time > el.time) {
                  let arr = list.slice(0, m + 1)
                  arr = arr.concat(value.chatRecordList)
                  item.chatRecordList = arr
                  let obj = {
                    id: value.chatId,
                    chatList: arr
                  }
                  indexedDB.updateCardStatus(obj).then((res) => {
                    if (res) {
                      commit('getChatListId', {
                        chatId: value.chatId
                      })
                      dispatch('getChatListDoc')
                    }
                  })
                  break
                }
              }
            }
          }
        })
      }
    }
  },
  modules: {
    global
  },
  getters
})
export default store
