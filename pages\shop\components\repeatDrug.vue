<template>
  <view class="warp">
    <view class="pharmacy_drug" v-for="(item, index) in list" :key="index">
      <!-- 药店 -->
      <view
        class="pharmacy_item"
        v-for="(pharmacy, p) in item.drugStoreList"
        :key="p"
      >
        <!-- 药店名称 -->
        <view class="pharmacy_name">
          <text>
            {{ pharmacy.drugStoreName }}
            <block v-if="pharmacy.isProprietary == 1">（自营）</block>
          </text>

          <text class="r" v-if="showTelNo" @click="call">联系药店</text>
        </view>
        <!-- 药品列表 -->
        <view class="durg_list">
          <!-- 单个药品 -->
          <view
            class="durg_item"
            v-for="drug in pharmacy.shoppingCartList"
            :key="drug.dsscId"
          >
            <UniImage v-if="drug.drugImg"
              :src="drug.drugImg"
              :data-src="errUrl"
              class="left"
            />
            <image
              v-else
              class="left"
              src="/static/images/Pharmacy-default.png"
            ></image>
            <!-- 内容 -->
            <view class="right">
              <!-- 药品名称 -->
              <view class="drug_name">{{ drug.drugName }}</view>
              <!-- 规格 -->
              <view class="drug_info">规格: {{ drug.gg }}</view>
              <!-- 活动 -->
              <view class="drug_info red" v-if="drug.activeName">
                单品{{ drug.activeName }}</view
              >
              <!-- 价位数量 -->
              <view class="right_menu">
                <view class="price"
                  >￥{{ drug.realyPay | toFixed }}
                  <text class="del">￥{{ drug.shoudlePay }}</text>
                </view>

                <!-- 操作 -->
                <view class="action">
                  <image
                    @click="reduce(drug)"
                    class="icon"
                    src="/static/shop/del.png"
                  ></image>
                  <text class="num">{{ drug.quan }}</text>
                  <image
                    @click="add(drug)"
                    class="icon"
                    src="/static/shop/add.png"
                  ></image>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="durg_count">
        <view class="right">
          <view class="item">
            <text>总价: </text> ￥{{ item.totalMoney | number }}
          </view>
          <view class="item">
            <text>优惠: </text> ￥{{ item.disTotalMoney | number }}
          </view>
          <view class="item">
            <text>实付:</text> ￥{{ item.totalRealyMoney | number }}
          </view>
        </view>
      </view>

      <!-- 药品有数量超过 12 -->
      <view class="drug_tip" v-show="isTip">
        <text>提示</text>
        <view
          >药店将按照您上传的有效处方，合理审核药品数量，若超过处方用量，药店可能驳回订单，请留意审核结果进行调整。</view
        >
      </view>
    </view>
  </view>
</template>

<script>
import { Toast } from '@/common/js/pay';
import { mapActions } from 'vuex';

export default {
  name: 'PharmacyList',
  props: {
    list: {
      type: Array,
      default: () => {
        return [];
      },
    },
    // 显示联系药店
    showTelNo: {
      type: Boolean,
      default: !false,
    },
    // 药店联系方式
    telNo: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      isTip: false,
      totalMoney: 0,
      errUrl: require('../../../static/shop/drug.png'),
    };
  },
  watch: {
    list: {
      handler(n) {
        if (n.length) this.getNum(n[0].drugStoreList);
      },
      deep: true,
    },
  },
  methods: {
    ...mapActions('shop', ['addDrugItem', 'reduceDrugItem']),
    getNum(arr) {
      let isTip = false;
      arr.forEach((k) => {
        k.shoppingCartList.forEach((j) => {
          if (j.quan > 12) isTip = true;
        });
      });
      this.isTip = isTip;
    },
    // 提示
    showTip() {
      this.$emit('showTip', 'wl');
    },
    // 减少
    async reduce(item) {
      await this.reduceDrugItem(item);
      let { quan, yfkcId } = item;
      quan = Number(quan) - 1;
      if (quan == 0) {
        this.$emit('remove', yfkcId);
        return;
      }
      this.$emit('reduce', { quan, yfkcId });
    },
    // 增加
    async add(item) {
      await this.addDrugItem(item);
      let { quan, yfkcId, drugKc } = item;
      quan = Number(quan) + 1;
      if (quan > drugKc) {
        Toast('该药品已达库存上限');
        return;
      }
      this.$emit('add', { quan, yfkcId });
    },
    // 拨打电话
    call() {
      uni.makePhoneCall({
        phoneNumber: this.telNo,
      });
    },
  },
  filters: {
    number(val) {
      if (!val) return val;
      return Number(val).toFixed(2);
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}

.warp {
  .durg_count {
    @include flex(right);
    align-items: flex-start;
    padding: 0 32rpx;
    font-size: 28rpx;
    height: 60rpx;
    margin-top: 20rpx;

    text {
      color: #333;
      font-weight: normal;
      margin-right: 4rpx;
      font-size: 22rpx;
    }

    .right {
      @include flex;
      color: #ff3b30;
      font-weight: bold;

      .item {
        margin-left: 16rpx;

        &:nth-child(1) {
          margin-left: 0;
        }
      }
    }
  }

  .drug_tip {
    @include flex;
    align-items: flex-start;
    padding: 0 32rpx 24rpx;
    font-size: 24rpx;

    text {
      flex: none;
      word-break: break-all;
      padding-right: 50rpx;
    }

    view {
      color: #999;
    }
  }
}

.pharmacy_drug {
  background-color: #fff;
  border-radius: 8rpx;
  margin-top: 24rpx;

  .pharmacy_item {
    width: 100%;
    padding: 0 32rpx;
    border-bottom: 1px dashed #eee;

    .pharmacy_name {
      font-size: 28rpx;
      font-weight: bold;
      height: 88rpx;
      @include flex(lr);
      position: relative;
      padding-left: 16rpx;
      border-bottom: 1px solid #eee;

      &::before {
        content: '';
        display: block;
        width: 6rpx;
        height: 28rpx;
        position: absolute;
        left: 0;
        @include bg_theme;
      }

      .r {
        width: 140rpx;
        height: 50rpx;
        @include flex;
        border-radius: 30rpx;
        @include border_theme;
        @include font_theme;
        font-size: 24rpx;
      }
    }
  }
}

.durg_list {
  .durg_item {
    @include flex;
    padding: 16rpx 0;

    &:last-child {
      .right {
        border-bottom: none;
      }
    }

    .left {
      width: 128rpx;
      height: 128rpx;
      margin-right: 24rpx;
      border: 1px solid #eee;
      border-radius: 8rpx;
      flex: none;
    }

    .right {
      flex: 1;
      min-height: 128rpx;
      @include flex(lr);
      flex-direction: column;
      align-items: stretch;
      border-bottom: 1px solid #eee;

      .drug_name {
        flex: 1;
        font-size: 28rpx;
        font-weight: bold;
      }

      .drug_info {
        flex: 1;
        font-size: 24rpx;
        color: #999;

        &.red {
          color: red;
        }
      }

      .right_menu {
        flex: 2;
        @include flex(lr);
        align-items: flex-end;

        .price {
          font-size: 28rpx;
          color: #ff3b30;
          font-weight: bold;

          .del {
            font-size: 24rpx;
            color: #999;
            padding-left: 10rpx;
            text-decoration: line-through;
          }
        }

        .action {
          @include flex;

          .icon {
            width: 32rpx;
            height: 32rpx;
          }

          .num {
            width: 60rpx;
            height: 36rpx;
            @include flex;
            font-size: 28rpx;
            background-color: #fff;
            margin: 0 16rpx;
            border-radius: 4rpx;
          }
        }
      }
    }
  }
}
</style>
