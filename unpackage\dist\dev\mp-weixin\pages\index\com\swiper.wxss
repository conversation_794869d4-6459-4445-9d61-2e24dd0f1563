/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*
	字体颜色相关
*/
/*
	背景色 分割线色
*/
/*
	主题色
*/
/*
		渐变可调角度
*/
/*
	定义弹性布局
 */
/*
	单行文本溢出隐藏
*/
.swiper.data-v-6757a06a {
  background: #fff;
  border-radius: 8rpx;
}
.swiper_title.data-v-6757a06a {
  height: 76rpx;
  padding-left: 32rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.swiper_warp.data-v-6757a06a {
  height: 250rpx;
  overflow: hidden;
}
.swiper_warp.data-v-6757a06a .desc_tag {
  height: 50rpx;
  overflow: hidden !important;
}