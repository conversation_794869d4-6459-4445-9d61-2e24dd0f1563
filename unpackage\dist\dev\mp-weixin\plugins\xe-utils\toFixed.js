"use strict";
const plugins_xeUtils_round = require("./round.js");
const plugins_xeUtils_toValueString = require("./toValueString.js");
const plugins_xeUtils_helperStringRepeat = require("./helperStringRepeat.js");
const plugins_xeUtils_helperNumberOffsetPoint = require("./helperNumberOffsetPoint.js");
function toFixed(num, digits) {
  digits = digits >> 0;
  var str = plugins_xeUtils_toValueString.toValueString(plugins_xeUtils_round.round(num, digits));
  var nums = str.split(".");
  var intStr = nums[0];
  var floatStr = nums[1] || "";
  var digitOffsetIndex = digits - floatStr.length;
  if (digits) {
    if (digitOffsetIndex > 0) {
      return intStr + "." + floatStr + plugins_xeUtils_helperStringRepeat.helperStringRepeat("0", digitOffsetIndex);
    }
    return intStr + plugins_xeUtils_helperNumberOffsetPoint.helperNumberOffsetPoint(floatStr, Math.abs(digitOffsetIndex));
  }
  return intStr;
}
exports.toFixed = toFixed;
