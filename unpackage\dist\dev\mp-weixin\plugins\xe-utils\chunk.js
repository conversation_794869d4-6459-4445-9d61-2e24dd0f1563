"use strict";
const plugins_xeUtils_isArray = require("./isArray.js");
function chunk(array, size) {
  var index;
  var result = [];
  var arrLen = size >> 0 || 1;
  if (plugins_xeUtils_isArray.isArray(array)) {
    if (arrLen >= 0 && array.length > arrLen) {
      index = 0;
      while (index < array.length) {
        result.push(array.slice(index, index + arrLen));
        index += arrLen;
      }
    } else {
      result = array.length ? [array] : array;
    }
  }
  return result;
}
exports.chunk = chunk;
