"use strict";
const plugins_xeUtils_toValueString = require("./toValueString.js");
const plugins_xeUtils_helperStringSubstring = require("./helperStringSubstring.js");
const plugins_xeUtils_helperStringLowerCase = require("./helperStringLowerCase.js");
var kebabCacheMaps = {};
function kebabCase(str) {
  str = plugins_xeUtils_toValueString.toValueString(str);
  if (kebabCacheMaps[str]) {
    return kebabCacheMaps[str];
  }
  if (/^[A-Z]+$/.test(str)) {
    return plugins_xeUtils_helperStringLowerCase.helperStringLowerCase(str);
  }
  var rest = str.replace(/^([a-z])([A-Z]+)([a-z]+)$/, function(text, prevLower, upper, nextLower) {
    var upperLen = upper.length;
    if (upperLen > 1) {
      return prevLower + "-" + plugins_xeUtils_helperStringLowerCase.helperStringLowerCase(plugins_xeUtils_helperStringSubstring.helperStringSubstring(upper, 0, upperLen - 1)) + "-" + plugins_xeUtils_helperStringLowerCase.helperStringLowerCase(plugins_xeUtils_helperStringSubstring.helperStringSubstring(upper, upperLen - 1, upperLen)) + nextLower;
    }
    return plugins_xeUtils_helperStringLowerCase.helperStringLowerCase(prevLower + "-" + upper + nextLower);
  }).replace(/^([A-Z]+)([a-z]+)?$/, function(text, upper, nextLower) {
    var upperLen = upper.length;
    return plugins_xeUtils_helperStringLowerCase.helperStringLowerCase(plugins_xeUtils_helperStringSubstring.helperStringSubstring(upper, 0, upperLen - 1) + "-" + plugins_xeUtils_helperStringSubstring.helperStringSubstring(upper, upperLen - 1, upperLen) + (nextLower || ""));
  }).replace(/([a-z]?)([A-Z]+)([a-z]?)/g, function(text, prevLower, upper, nextLower, index) {
    var upperLen = upper.length;
    if (upperLen > 1) {
      if (prevLower) {
        prevLower += "-";
      }
      if (nextLower) {
        return (prevLower || "") + plugins_xeUtils_helperStringLowerCase.helperStringLowerCase(plugins_xeUtils_helperStringSubstring.helperStringSubstring(upper, 0, upperLen - 1)) + "-" + plugins_xeUtils_helperStringLowerCase.helperStringLowerCase(plugins_xeUtils_helperStringSubstring.helperStringSubstring(upper, upperLen - 1, upperLen)) + nextLower;
      }
    }
    return (prevLower || "") + (index ? "-" : "") + plugins_xeUtils_helperStringLowerCase.helperStringLowerCase(upper) + (nextLower || "");
  });
  rest = rest.replace(/([-]+)/g, function(text, flag, index) {
    return index && index + flag.length < rest.length ? "-" : "";
  });
  kebabCacheMaps[str] = rest;
  return rest;
}
exports.kebabCase = kebabCase;
