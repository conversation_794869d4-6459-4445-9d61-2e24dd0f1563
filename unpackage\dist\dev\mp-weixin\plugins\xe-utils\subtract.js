"use strict";
const plugins_xeUtils_helperNumberDecimal = require("./helperNumberDecimal.js");
const plugins_xeUtils_toNumberString = require("./toNumberString.js");
const plugins_xeUtils_toNumber = require("./toNumber.js");
const plugins_xeUtils_toFixed = require("./toFixed.js");
function subtract(num1, num2) {
  var subtrahend = plugins_xeUtils_toNumber.toNumber(num1);
  var minuend = plugins_xeUtils_toNumber.toNumber(num2);
  var str1 = plugins_xeUtils_toNumberString.toNumberString(subtrahend);
  var str2 = plugins_xeUtils_toNumberString.toNumberString(minuend);
  var digit1 = plugins_xeUtils_helperNumberDecimal.helperNumberDecimal(str1);
  var digit2 = plugins_xeUtils_helperNumberDecimal.helperNumberDecimal(str2);
  var ratio = Math.pow(10, Math.max(digit1, digit2));
  var precision = digit1 >= digit2 ? digit1 : digit2;
  return parseFloat(plugins_xeUtils_toFixed.toFixed((subtrahend * ratio - minuend * ratio) / ratio, precision));
}
exports.subtract = subtract;
