"use strict";
const common_vendor = require("../../../common/vendor.js");
const api_base = require("../../../api/base.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      errUrl: require("../../../static/images/topbanner.png"),
      swiperTime: 5e3
    };
  },
  created() {
    this.getConfigInfoByKeys();
  },
  methods: {
    //获取轮播图参数
    async getConfigInfoByKeys() {
      let res = await api_base.getConfigInfoByKey({
        configKey: "interval_time_for_patient"
      });
      let newArr = res.data;
      this.swiperTime = (newArr == null ? void 0 : newArr.configValue) > 0 ? Number(newArr == null ? void 0 : newArr.configValue) * 1e3 : 5e3;
      console.log(newArr);
    },
    goDetail(item) {
      console.log("999", item);
      if (item.title === "AI医生") {
        common_vendor.index.navigateTo({
          url: "/pages/aiAssistant/previewImages?title=" + item.title
        });
        return;
      }
      if (item.title === "AI健康咨询") {
        common_vendor.index.navigateTo({
          url: "/pages/aiAssistant/previewImages?flag=2&title=" + item.title
        });
        return;
      }
      if (item.carouselType == 1) {
        common_vendor.index.navigateTo({
          url: "/pages/index/com/swiperDetail?obj=" + JSON.stringify(item.contentInfo) + "&title=" + item.title
        });
      } else {
        let linkUrl = item.contentInfo;
        common_vendor.index.navigateTo({
          url: "/pages/webview/webview?url=" + encodeURIComponent(linkUrl)
        });
      }
    }
  }
};
if (!Array) {
  const _component_UniImage = common_vendor.resolveComponent("UniImage");
  _component_UniImage();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.list.length > 0
  }, $props.list.length > 0 ? {
    b: common_vendor.f($props.list, (item, index, i0) => {
      return common_vendor.e({
        a: item.carouselImg
      }, item.carouselImg ? {
        b: common_vendor.o(($event) => $options.goDetail(item), index),
        c: "9d3f9f4c-0-" + i0,
        d: common_vendor.p({
          src: item.carouselImg,
          ["lazy-load"]: true,
          mode: "aspectFill",
          ["default-src"]: $data.errUrl,
          ["error-src"]: $data.errUrl
        })
      } : {}, {
        e: index
      });
    }),
    c: $data.swiperTime
  } : {
    d: common_assets._imports_0$3
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-9d3f9f4c"]]);
wx.createComponent(Component);
