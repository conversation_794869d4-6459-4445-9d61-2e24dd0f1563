<view class="menu data-v-6a3572d7"><block wx:for="{{a}}" wx:for-item="item"><view wx:if="{{item.a}}" class="{{['menu_item', 'data-v-6a3572d7', item.g && 'three']}}" bindtap="{{item.h}}" key="{{item.i}}"><view class="menu_item_image data-v-6a3572d7" style="{{'background:' + item.e}}"><image class="icon data-v-6a3572d7" src="{{item.b}}" style="{{'width:' + item.c + ';' + ('height:' + item.d)}}"/></view><view class="text data-v-6a3572d7"><text class="bold data-v-6a3572d7">{{item.f}}</text></view></view></block></view>