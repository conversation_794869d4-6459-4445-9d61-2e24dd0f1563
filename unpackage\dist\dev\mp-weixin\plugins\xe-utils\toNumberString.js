"use strict";
const plugins_xeUtils_helperStringRepeat = require("./helperStringRepeat.js");
const plugins_xeUtils_helperNumberOffsetPoint = require("./helperNumberOffsetPoint.js");
function toNumberString(num) {
  var rest = "" + num;
  var scienceMatchs = rest.match(/^([-+]?)((\d+)|((\d+)?[.](\d+)?))e([-+]{1})([0-9]+)$/);
  if (scienceMatchs) {
    var isNegative = num < 0;
    var absFlag = isNegative ? "-" : "";
    var intNumStr = scienceMatchs[3] || "";
    var dIntNumStr = scienceMatchs[5] || "";
    var dFloatNumStr = scienceMatchs[6] || "";
    var sciencFlag = scienceMatchs[7];
    var scienceNumStr = scienceMatchs[8];
    var floatOffsetIndex = scienceNumStr - dFloatNumStr.length;
    var intOffsetIndex = scienceNumStr - intNumStr.length;
    var dIntOffsetIndex = scienceNumStr - dIntNumStr.length;
    if (sciencFlag === "+") {
      if (intNumStr) {
        return absFlag + intNumStr + plugins_xeUtils_helperStringRepeat.helperStringRepeat("0", scienceNumStr);
      }
      if (floatOffsetIndex > 0) {
        return absFlag + dIntNumStr + dFloatNumStr + plugins_xeUtils_helperStringRepeat.helperStringRepeat("0", floatOffsetIndex);
      }
      return absFlag + dIntNumStr + plugins_xeUtils_helperNumberOffsetPoint.helperNumberOffsetPoint(dFloatNumStr, scienceNumStr);
    }
    if (intNumStr) {
      if (intOffsetIndex > 0) {
        return absFlag + "0." + plugins_xeUtils_helperStringRepeat.helperStringRepeat("0", Math.abs(intOffsetIndex)) + intNumStr;
      }
      return absFlag + plugins_xeUtils_helperNumberOffsetPoint.helperNumberOffsetPoint(intNumStr, intOffsetIndex);
    }
    if (dIntOffsetIndex > 0) {
      return absFlag + "0." + plugins_xeUtils_helperStringRepeat.helperStringRepeat("0", Math.abs(dIntOffsetIndex)) + dIntNumStr + dFloatNumStr;
    }
    return absFlag + plugins_xeUtils_helperNumberOffsetPoint.helperNumberOffsetPoint(dIntNumStr, dIntOffsetIndex) + dFloatNumStr;
  }
  return rest;
}
exports.toNumberString = toNumberString;
