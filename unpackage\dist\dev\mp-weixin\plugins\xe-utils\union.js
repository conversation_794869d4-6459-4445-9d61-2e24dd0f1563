"use strict";
const plugins_xeUtils_uniq = require("./uniq.js");
const plugins_xeUtils_toArray = require("./toArray.js");
function union() {
  var args = arguments;
  var result = [];
  var index = 0;
  var len = args.length;
  for (; index < len; index++) {
    result = result.concat(plugins_xeUtils_toArray.toArray(args[index]));
  }
  return plugins_xeUtils_uniq.uniq(result);
}
exports.union = union;
