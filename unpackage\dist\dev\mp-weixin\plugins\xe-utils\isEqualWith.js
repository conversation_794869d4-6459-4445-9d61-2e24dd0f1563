"use strict";
const plugins_xeUtils_helperEqualCompare = require("./helperEqualCompare.js");
const plugins_xeUtils_helperDefaultCompare = require("./helperDefaultCompare.js");
const plugins_xeUtils_isFunction = require("./isFunction.js");
const plugins_xeUtils_isUndefined = require("./isUndefined.js");
function isEqualWith(obj1, obj2, func) {
  if (plugins_xeUtils_isFunction.isFunction(func)) {
    return plugins_xeUtils_helperEqualCompare.helperEqualCompare(obj1, obj2, function(v1, v2, key, obj12, obj22) {
      var result = func(v1, v2, key, obj12, obj22);
      return plugins_xeUtils_isUndefined.isUndefined(result) ? plugins_xeUtils_helperDefaultCompare.helperDefaultCompare(v1, v2) : !!result;
    }, func);
  }
  return plugins_xeUtils_helperEqualCompare.helperEqualCompare(obj1, obj2, plugins_xeUtils_helperDefaultCompare.helperDefaultCompare);
}
exports.isEqualWith = isEqualWith;
