<template>
  <!-- 购物车栏 -->
  <view class="cars">
    <!-- 内容 -->
    <view class="cont" :class="{ act: shopList.length }">
      <!-- 左侧 -->
      <view class="left" @click="showList">
        <!-- 左侧图标 -->
        <view class="left_icon">
          <!-- 空 -->
          <image v-if="!shopList.length" src="/static/shop/car.png" />
          <!-- 非空 -->
          <image v-else src="/static/shop/car_act.png" />
          <text v-if="shopList.length && false">{{ drugNum }}</text>
        </view>
        <!-- 价格 -->
        <view class="left_price" v-if="shopList.length"
          >￥{{ realyTotalMoney }}
          <text class="del" v-show="realyTotalMoney != shouldTotalMoney"
            >￥{{ shouldTotalMoney }}</text
          >
        </view>
      </view>
      <!-- 右侧按钮 -->
      <view class="button" @click="toSubmit()">我选好了</view>
    </view>

    <!-- 弹出层 -->
    <uni-popup ref="list" type="bottom" @change="statusChange">
      <!-- 列表容器 -->
      <view class="pup_list">
        <!-- 数量 操作 -->
        <view class="count_menu">
          <view class="left">
            已选药品
            <text>共{{ sele_num }}件</text>
          </view>
          <view class="right">
            <!--            <view v-if="isMultiple" class="item" @click="seleAll">-->
            <!--              <image-->
            <!--                src="/static/inspection/radio_act.png"-->
            <!--                v-if="drugStoreList.length == shopList.length"-->
            <!--              />-->
            <!--              <image src="/static/inspection/radio.png" v-else />-->
            <!--              <text>全选</text>-->
            <!--            </view>-->
            <view class="item" @click="showConfirm = true">
              <image src="/static/shop/remove.png"></image>
              <text>清空药品</text>
            </view>
          </view>
        </view>
        <!-- 药品列表 -->
        <!-- <view class="durg_list"> -->
        <scroll-view class="durg_list" scroll-with-animation scroll-y="true">
          <view
            class="drug_store"
            v-for="item in shopList"
            :key="item.drugStoreID"
          >
            <!-- 药店名称 -->
            <view class="store_name" @click="seleStore(item.drugStoreID)">
              <image
                src="/static/inspection/radio_act.png"
                v-if="drugStoreList.includes(item.drugStoreID)"
              />
              <image src="/static/inspection/radio.png" v-else />
              {{ item.drugStoreName }}</view
            >

            <view class="list">
              <!-- 单个药品 -->
              <view
                class="durg_item"
                v-for="(item, index) in item.shoppingCartList"
                :key="item.dsscId"
              >
                <view style="position: relative">
                  <UniImage v-if="item.drugImg"
                    :src="item.drugImg"
                    :data-src="errUrl"
                    :class="['left', item.drugType == '025.9' ? 'gx' : '']"
                  />
                  <image
                    v-else
                    class="left"
                    src="/static/shop/drug.png"
                  ></image>
                  <view
                    v-if="item.drugKc == 0 || !item.drugKc"
                    class="drug-coverUp"
                  >
                    <img class="card_img" src="/static/ysq.png" alt="" />
                  </view>
                  <view
                    v-if="item.drugType == '025.9'"
                    class="drug-coverUp cfCover"
                  >
                    处方药 <br />
                    依规定不展示包装
                  </view>
                  <view
                    v-if="item.quan > item.drugKc * 1"
                    class="outOfStock-coverUp"
                  >
                    库存不足
                  </view>
                </view>

                <!-- 内容 -->
                <view class="right">
                  <!-- 药品名称 -->
                  <view class="drug_name">{{ item.drugName }}</view>
                  <!-- 规格 -->
                  <view class="drug_info">规格: {{ item.gg }}</view>
                  <!-- 活动 -->
                  <view class="drug_active" v-show="item.activeName">
                    单品{{ item.activeName }}</view
                  >
                  <!-- 操作 -->
                  <view class="right_menu">
                    <view class="price"
                      >￥{{ item.realyPay | toFixed }}
                      <text
                        class="del"
                        v-show="item.shoudlePay != item.realyPay"
                        >￥{{ item.shoudlePay | toFixed }}</text
                      >
                    </view>
                    <!-- 操作 -->
                    <view class="action">
                      <image
                        class="icon"
                        @click="reduce(item, index)"
                        src="/static/shop/del.png"
                      ></image>
                      <text
                        class="num"
                        v-if="
                          item.drugId !== '72d263bd0ebd48e49e9063320efa0ff7'
                        "
                        >{{ item.quan }}</text
                      >
                      <input
                        v-else
                        type="number"
                        class="num"
                        :value="item.quan"
                        style="text-align: center"
                        @blur="onQuantityBlur($event, item, index)"
                      />
                      <image
                        v-if="item.quan <= item.drugKc * 1 && item.drugKc"
                        class="icon"
                        @click="add(item, index)"
                        src="/static/shop/add.png"
                      ></image>
                    </view>
                  </view>
                  <view v-if="item.quan > item.drugKc * 1" class="outOfLimit"
                    >商品仅剩余 {{ Math.floor(item.drugKc || 0) }} 件</view
                  >
                </view>
              </view>
            </view>
          </view>

          <view class="unable_label" v-if="unableList.length">
            失效商品
            <text @click="clearUnable">清空失效商品</text>
          </view>

          <!-- 单个药品 -->
          <view
            class="durg_item unable"
            v-for="(item, index) in unableList"
            :key="item.dsscId"
          >
            <view style="position: relative">
              <UniImage v-if="item.drugImg"
                :src="item.drugImg"
                :data-src="errUrl"
                class="left"
              />
              <image v-else class="left" src="/static/shop/drug.png"></image>
              <view
                v-if="item.drugKc == 0 || !item.drugKc"
                class="drug-coverUp"
              >
                <img class="card_img" src="/static/ysq.png" alt="" />
              </view>
              <view
                v-if="item.drugType == '025.9'"
                class="drug-coverUp cfCover"
              >
                处方药 <br />
                依规定不展示包装
              </view>
              <view v-if="item.mallSelling == 0" class="drug-coverUp">
                <img class="card_img" src="/static/xj.png" alt="" />
              </view>
            </view>

            <!-- 内容 -->
            <view class="right">
              <!-- 药品名称 -->
              <view class="drug_name">{{ item.drugName }}</view>
              <!-- 规格 -->
              <view class="drug_info">规格: {{ item.gg }}</view>
              <!-- 操作 -->
              <view class="right_menu">
                <text class="price">￥{{ item.price | toFixed }}</text>
                <!-- 原因 -->
                <view class="info">
                  <text v-if="item.mallSelling == 0">商品已下架</text>
                  <text v-else>商品库存不足</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 底部占位 -->
          <view class="zhawei"></view>
        </scroll-view>
        <!-- </view> -->
      </view>
    </uni-popup>

    <!-- 询问框 -->
    <Confirm
      @confirmPropCenter="clearList"
      :type="2"
      @cancelPropCenter="showConfirm = false"
      v-if="showConfirm"
    >
      是否要清空所有药品？
    </Confirm>

    <!-- 某个药品数量超过 12 -->
    <Confirm
      @confirmPropCenter="nextConfirm"
      :type="2"
      @cancelPropCenter="showList"
      v-if="showCancel"
    >
      根据国家卫健委、国家医保局发布《长期处方管理规范（试行）》，您选择的药品数量已超过长期处方最大用量12盒，是否仍确定购买？
      如不继续，请返回重新修改数量。
    </Confirm>
    <Confirm
      :type="500"
      @cancelPropCenter="showOffConfirm = false"
      v-if="showOffConfirm"
    >
      <view>
        <view>因政策要求，请您分开结算一下商品</view>
        <view style="margin: 20px 0">
          <u-radio-group v-model="isOFF" :wrap="true">
            <u-radio
              v-for="(item, index) in offList"
              :key="index"
              :name="item.value"
              :disabled="item.disabled"
            >
              {{ item.text }}
            </u-radio>
          </u-radio-group>
        </view>
        <view class="offbtn">
          <view class="btn cancel" @click="showOffConfirm = false"
            >返回购物车</view
          >
          <view class="btn" @click="toSubmit(2)">{{
            isOFF == 0 ? '去开方' : '去结算'
          }}</view>
        </view>
      </view>
    </Confirm>
  </view>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import { getSysPlatformConfigByKeyList } from '@/api/base.js'
import { getShoppingCartOrderCantPay } from '@/api/shop.js'
import Confirm from '@/components/propCenter/propCenter.vue'
import Login from '@/mixins/login.js'
import drugRulesMixin from '@/mixins/drugRules.js'
import { Toast, showModal } from '@/common/js/pay.js'
import { batchGetDrugLimitNum, checkMaxQuan } from '../../../api/base'
export default {
  name: 'ShopCards',
  mixins: [Login, drugRulesMixin],
  components: {
    Confirm,
  },
  data() {
    return {
      offList: [
        {
          text: '处方药-开方后方可结算-件',
          value: 0,
        },
        {
          text: '非处方药-件',
          value: 1,
        },
      ],
      isOFF: 0,
      // 数量限制
      showCancel: false,
      isBindShow: false,
      show: false,
      showConfirm: false,
      errUrl: require('../../../static/shop/drug.png'),
      realyTotalMoney: 0,
      shouldTotalMoney: 0,
      // 是否可多选
      isMultiple: false,
      // 已选数量
      sele_num: 0,
      showOffConfirm: false,
      isOnlyOtc: false,
    }
  },
  computed: {
    ...mapGetters([
      'shopList',
      'unableList',
      'totalMoney',
      'drugNum',
      'drugStoreList',
    ]),
  },
  watch: {
    drugStoreList: function () {
      this.setPrice()
    },
    shopList: function () {
      this.setPrice()
      // 获取所有id
      let list = []
      this.shopList.forEach((v) => {
        list.push(v.drugStoreID)
      })
      let arr = []
      // 查询不存在的
      this.drugStoreList.forEach((v) => {
        let n = list.find((k) => k == v)
        if (n) arr.push(n)
      })
      this.$store.commit('shop/SET_DRUGSTORE_LIST', arr)
    },
  },
  async created() {
    this.isOnlyOtc = false
    await this.initDrugRules()
    await this.getConfig()
    await this.getCartList()
    // if (this.shopList[0]) this.seleStore(this.shopList[0].drugStoreID);
  },
  methods: {
    ...mapActions('shop', [
      'getCartList',
      'addDrugItem',
      'reduceDrugItem',
      'clearCart',
      'clearUnable',
      'changeDrugQuan',
    ]),

    // 获取配置
    async getConfig() {
      let { data } = await getSysPlatformConfigByKeyList([
        'shoppingOnlineFastBuyDrug',
      ])
      if (data.length) {
        // 1 为新流程 不可多选
        this.isMultiple = data[0].configValue == 1 ? false : true
      }
    },
    // 全选
    seleAll() {
      if (this.drugStoreList.length == this.shopList.length) {
        this.$store.commit('shop/SET_DRUGSTORE_LIST', [])
        return
      }
      let arr = []
      this.shopList.forEach((v) => {
        arr.push(v.drugStoreID)
      })
      this.$store.commit('shop/SET_DRUGSTORE_LIST', arr)
    },
    setPrice() {
      let r = 0
      let p = 0
      let n = 0
      this.shopList.forEach((item) => {
        // 如果存在
        if (this.drugStoreList.includes(item.drugStoreID)) {
          r += Number(item.realyTotalMoney) * 100
          p += Number(item.shouldTotalMoney) * 100
          n += item.shoppingCartList.length
        }
      })
      this.realyTotalMoney = (r / 100).toFixed(2)
      this.shouldTotalMoney = (p / 100).toFixed(2)
      this.sele_num = n
    },
    // 单选/多选药店
    seleStore(id) {
      // 如果只能单选
      if (!this.isMultiple || true) {
        this.$store.commit('shop/SET_DRUGSTORE_LIST', [id])
        return
      }

      // 不存在
      if (!this.drugStoreList.length) {
        this.$store.commit('shop/SET_DRUGSTORE_LIST', [id])
        return
      }

      const list = this.drugStoreList

      let index = list.indexOf(id)

      if (index == -1) {
        list.push(id)
        this.$store.commit('shop/SET_DRUGSTORE_LIST', list)
        return
      }

      list.splice(index, 1)
      this.$store.commit('shop/SET_DRUGSTORE_LIST', list)
    },
    async tokaifan() {
      // 找到已选药店的所有药品
      let arr = []
      // 循环重组所有药品
      this.drugStoreList.forEach((v) => {
        let item = this.shopList.find((k) => k.drugStoreID === v)
        arr = [...arr, ...item.shoppingCartList]
      })
      arr = arr.filter((v) => v.otc == 0)

      // 校验特定药品的最大购买数量
      let checkMaxQuantityPromises = []
      let userId = uni.getStorageSync('userId')

      // 检查每种药品是否超出最大购买数量
      for (let drug of arr) {
        checkMaxQuantityPromises.push(
          checkMaxQuan({
            userId: userId,
            drugId: drug.drugId,
            quan: drug.quan,
          })
        )
      }

      // 等待所有检查完成
      const checkResults = await Promise.all(checkMaxQuantityPromises)
      console.log(checkResults, 999)
      // 如果有任何一个药品检查未通过，则不允许跳转
      if (checkResults.some((result) => result.data !== true)) {
        Toast('本次活动同一账号最多可购买60盒，请重新选择')
        return
      }
      console.log(arr)
      uni.removeStorageSync('checkCf')
      uni.removeStorageSync('addPatientFlag')
      this.$store.commit('setDiagList', [])
      this.$store.commit('setDrugList', arr)
      uni.navigateTo({
        url: `/pages/quickContinuation/index?source=shop`,
      })
    },
    // 药品限制数量
    async vaDrugLimitNum(drugs) {
      const drugIds = drugs.map((v) => v.drugId)
      const res = await batchGetDrugLimitNum({ drugIds })
      if (!res.data) {
        return true
      }
      const drugLimitNumList = res.data.filter((v) => v.limitNum != 0)
      if (drugLimitNumList.length === 0) {
        return true
      }
      const fails = drugs.filter((v) => {
        const find = drugLimitNumList.find((item) => item.drugId === v.drugId)
        return find && find.limitNum < v.quan
      })
      if (fails.length) {
        const find = drugLimitNumList.find(
          (item) => item.drugId === fails[0].drugId
        )
        if (find) {
          uni.showToast({
            title: `${fails[0].drugName}单次开具数量限制${find.limitNum}，请核实！`,
            icon: 'none',
          })
          return false
        }
      }
      return true
    },
    // 去提交
    async toSubmit(flag) {
      if (!this.drugStoreList.length) {
        Toast('未选择药品')
        return
      }
      if (flag == 3) {
      }
      if (this.drugStoreList.length) {
        let result = []
        // 循环重组所有药品
        this.drugStoreList.forEach((v) => {
          let item = this.shopList.find((k) => k.drugStoreID === v)
          if (
            item.shoppingCartList.some(
              (a) => Math.floor(a.drugKc) < Math.floor(a.quan)
            )
          ) {
            result.push(
              ...item.shoppingCartList.filter((a) => a.drugKc < a.quan)
            )
          }
        })
        if (result.length) {
          Toast('药品库存不足，请重新选择')
          return
        }
      }
      const drugList = this.shopList.reduce((acc, cur) => {
        return acc.concat(cur.shoppingCartList)
      }, [])
      if (!(await this.vaDrugLimitNum(drugList))) {
        return
      }
      if (!flag) {
        if (this.drugStoreList.length > 1) {
          Toast('只能选择一个药店')
          return
        }
        console.log(this.shopList, this.drugStoreList)
        const count = this.shopList
          .filter((v) => this.drugStoreList.includes(v.drugStoreID))
          .reduce((acc, cur) => {
            return acc + cur.shoppingCartList.filter((a) => a.otc == 0).length
          }, 0)
        console.log(count)
        //  如处方药数量超过5种，则toast提示【根据政策要求，单一处方内处方药数量不可超过5种】
        if (count > 5) {
          Toast('根据政策要求，单一处方内处方药数量不可超过5种')
          return
        }
        const isHaveNoOtc = this.shopList
          .filter((v) => this.drugStoreList.includes(v.drugStoreID))
          .some((v) => v.shoppingCartList.some((a) => a.otc != 0))
        // 如处方药数量未超过5种（包含5）且购物车内存在非处方药，则显示 分开结算 弹窗 count<=5 && isHaveNoOtc
        if (count <= 5 && count > 0 && isHaveNoOtc) {
          const num = this.shopList
            .filter((v) => this.drugStoreList.includes(v.drugStoreID))
            .reduce((acc, cur) => {
              return (
                acc +
                cur.shoppingCartList
                  .filter((a) => a.otc == 0)
                  .reduce((a, b) => a + b.quan * 1, 0)
              )
            }, 0)
          const num2 = this.shopList
            .filter((v) => this.drugStoreList.includes(v.drugStoreID))
            .reduce((acc, cur) => {
              return (
                acc +
                cur.shoppingCartList
                  .filter((a) => a.otc != 0)
                  .reduce((a, b) => a + b.quan * 1, 0)
              )
            }, 0)
          this.offList = [
            {
              text: `处方药-开方后方可结算-${num}件`,
              value: 0,
            },
            {
              text: `非处方药-${num2}件`,
              value: 1,
            },
          ]
          this.showOffConfirm = true
          return
        }
        if (count <= 5 && count > 0 && !isHaveNoOtc) {
          this.isOnlyOtc = true
          this.tokaifan()
          return
        }
        this.isOnlyOtc = false
      } else {
        if (flag == 2) {
          if (this.isOFF == 0) {
            this.tokaifan()
            return
          }
        }
      }
      // 判断是否多选药店
      if (this.drugStoreList.length > 1) {
        // 找到已选药店的所有药品
        let arr = []
        // 循环重组所有药品
        this.drugStoreList.forEach((v) => {
          let item = this.shopList.find((k) => k.drugStoreID === v)
          arr = [...arr, ...item.shoppingCartList]
        })
        if (!arr.length) {
          Toast('未选择药品')
          return
        }

        let index = -1
        // 存药品id
        let list = []
        arr.forEach((k, i) => {
          if (list.includes(k.drugId)) {
            index = i
          } else {
            list.push(k.drugId)
          }
        })

        // 如果存在
        // if (index > -1) {
        //   let str = arr[index].drugName;
        //   Toast(str + ' 药品重复，无法提交需求');
        //   return;
        // }
      }
      // 药品数量为0 不可提交
      if (this.drugNum == 0) return
      if (!this.hasInfo()) return

      if (this.drugStoreList.length == 1 && !this.isMultiple) {
        let isNext = true

        let item = this.shopList.find(
          (k) => this.drugStoreList[0] === k.drugStoreID
        )

        let arr = item.shoppingCartList

        arr.forEach((v) => {
          if (v.quan > 12) {
            isNext = false
          }
        })

        if (!isNext) {
          this.showCancel = true
          return false
        }
      }

      try {
        // 验证库存是否满足
        await getShoppingCartOrderCantPay(this.drugStoreList)
        console.log(this.drugStoreList, 999)
        this.showList()
        let old =
          '/pages/shop/submit/submit?list=' +
          JSON.stringify(this.drugStoreList) +
          '&isOFF=' +
          this.isOFF
        let now = '/pages/shop/submit/quick?id=' + this.drugStoreList[0]
        uni.navigateTo({
          url: this.isMultiple ? old : now,
        })
      } catch (error) {
        this.getCartList()
      }
    },
    // 继续提交
    async nextConfirm() {
      try {
        this.showCancel = false
        // 验证库存是否满足
        await getShoppingCartOrderCantPay(this.drugStoreList)

        this.showList()
        let old =
          '/pages/shop/submit/submit?list=' + JSON.stringify(this.drugStoreList)
        let now = '/pages/shop/submit/quick?id=' + this.drugStoreList[0]
        uni.navigateTo({
          url: this.isMultiple ? old : now,
        })
      } catch (error) {
        this.getCartList()
      }
    },
    // 监听弹出状态
    statusChange(v) {
      if (!v.show) {
        this.show = false
        this.$emit('close')
      }
      if (v.show) {
        this.getCartList()
      }
    },
    // 数量减少
    async reduce(item, amount = 1) {
      // 超出库存
      if (item.quan > item.drugKc * 1) {
        item.quan = Math.floor(item.drugKc || 0)
        let { drugKc, yfkcId } = item
        if (Number(quan) > 1) {
          await dispatch('upShopList', {
            quan: item.quan,
            yfkcId,
            drugKc,
          })
        } else {
          // 调用移除
          await dispatch('removeDurgItem', [yfkcId])
        }
        this.$emit('itemChange', item)
        return
      }

      // 使用规则计算减购数量
      amount = this.getReduceQuantity(item.drugId, item.quan)

      await this.reduceDrugItem({ ...item, amount })
      item.quan = item.quan * 1 - amount * 1
      this.$emit('itemChange', item)
    },
    // 数量增加
    async add(item, amount = 1) {
      // 使用规则计算加购数量
      amount = this.getAddQuantity(item.drugId, item.quan)

      await this.addDrugItem({ ...item, amount })
      item.quan = item.quan * 1 + amount * 1
      this.$emit('itemChange', item)
    },
    // 清空购物车
    async clearList() {
      this.showConfirm = false
      this.showList()
      await this.clearCart()
      this.$emit('clear')
    },
    // 数量输入框失焦事件
    async onQuantityBlur(e, item, index) {
      if (item.drugId === '72d263bd0ebd48e49e9063320efa0ff7') {
        const value = Number(e.detail.value)
        if (isNaN(value)) {
          // 如果输入无效，重置为0
          item.quan = 0
          this.changeDrugQuan({ ...item, amount: 0 })
          this.$emit('itemChange', item)
          return
        }
        // 如果输入大于等于60，设为60
        if (value >= 30) {
          showModal(
            '',
            '同一就诊人限购 30 盒，若需购买30盒以上，请重新扫码添加新就诊人再次下单、或在补充就诊人信息页面切换/添加新就诊人。',
            '我已知晓，关闭',
            '取消',
            false
          )
          item.quan = 30
          this.changeDrugQuan({ ...item, amount: 30 })
          this.$emit('itemChange', item)
          return
        }
        if (value > item.drugKc) {
          uni.showToast({
            title: '已达库存上限',
            icon: 'none',
          })
          item.quan = item.drugKc
          this.changeDrugQuan({ ...item, amount: item.drugKc })
          this.$emit('itemChange', item)
          return
        }
        // 根据特殊规则处理数量
        if (value === 0) {
          // 如果输入为0，清零
          item.quan = 0
          this.changeDrugQuan({ ...item, amount: 0 })
          this.$emit('itemChange', item)
        } else if (value > 0 && value < 5) {
          // 如果输入大于0但小于5，设置为0
          item.quan = 0
          this.changeDrugQuan({ ...item, amount: 0 })
          this.$emit('itemChange', item)
        } else if (value >= 5) {
          // 如果输入大于等于5，正常设置数量
          item.quan = value
          this.changeDrugQuan({ ...item, amount: value })
          this.$emit('itemChange', item)
        }
      }
    },
    // 显示列表
    showList() {
      if (this.show) {
        this.show = false
        this.$refs.list.close()
        this.showCancel = false
        this.showOffConfirm = false
        return
      }
      this.show = true
      this.$refs.list.open()
    },
  },
}
</script>

<style lang="scss" scoped>
.offbtn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .btn {
    background: #736bff;
    color: white;
    padding: 3px 15px;
    border-radius: 14px;
    font-size: 14px;
  }
  .cancel {
    background: #999 !important;
  }
}
.cars {
  width: 100%;
  padding: 48rpx 24rpx;
  box-sizing: border-box;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 999;

  .cont {
    width: 100%;
    height: 88rpx;
    @include flex;
    border-radius: 44rpx;
    overflow: hidden;
    box-sizing: border-box;
    position: relative;
    z-index: 100;
    transition: all 0.8s;

    &.act {
      .left {
        background-color: #e7f5fc;
      }

      .button {
        @include bg_theme;
      }
    }

    .left {
      flex: 1;
      height: 100%;
      @include flex(left);
      background-color: #333;
      padding: 0 48rpx;

      .left_icon {
        width: 64rpx;
        height: 64rpx;
        flex: none;
        position: relative;

        text {
          min-width: 30rpx;
          height: 30rpx;
          padding: 0 4rpx;
          background-color: #ff5050;
          @include flex;
          font-size: 24rpx;
          color: #fff;
          border-radius: 16rpx;
          line-height: 24rpx;
          position: absolute;
          right: 0;
          top: 0;
        }

        image {
          width: 100%;
          height: 100%;
        }
      }

      .left_price {
        flex: 1;
        text-align: left;
        font-size: 32rpx;
        font-weight: bold;
        padding-left: 30rpx;

        .del {
          margin-left: 12rpx;
          font-size: 24rpx;
          color: #666;
          text-decoration: line-through;
        }
      }
    }

    .button {
      width: 216rpx;
      flex: none;
      background-color: #999999;
      color: #fff;
      height: 100%;
      @include flex;
      font-size: 28rpx;
    }
  }

  .pup_list {
    background-color: #f5f5f5;
    max-height: 70vh;
    overflow-y: scroll;

    .count_menu {
      background-color: #fafafa;
      height: 88rpx;
      @include flex(lr);
      padding: 0 24rpx;
      position: sticky;
      top: 0;
      z-index: 1;

      .left {
        font-size: 28rpx;
        font-weight: bold;

        text {
          font-size: 28rpx;
          color: #999;
          font-weight: normal;
          padding-left: 10rpx;
        }
      }

      .right {
        @include flex;

        .item {
          @include flex;
          margin-left: 24rpx;
          image {
            width: 32rpx;
            height: 32rpx;
            margin-right: 10rpx;
          }

          text {
            color: #999;
            font-size: 24rpx;
          }
        }
      }
    }

    .durg_list {
      padding: 24rpx;
      height: calc(70vh - 88rpx);
      overflow-y: scroll;
      box-sizing: border-box;

      .drug_store {
        background-color: #fff;
        border-radius: 8rpx;
        padding: 0 24rpx;
        margin-bottom: 24rpx;

        .store_name {
          font-size: 32rpx;
          height: 80rpx;
          @include flex(left);
          border-bottom: 1px solid #f5f5f5;

          image {
            width: 30rpx;
            height: 30rpx;
            margin-right: 12rpx;
          }
        }
      }

      .zhawei {
        height: 188rpx;
      }

      .unable_label {
        @include flex(lr);
        font-size: 28rpx;
        height: 80rpx;
        font-weight: bold;

        text {
          color: red;
          font-weight: normal;
        }
      }

      .durg_item {
        @include flex;
        padding: 16rpx 0;

        &.unable {
          background-color: #fff;
          padding: 16rpx;
        }

        .left {
          width: 128rpx;
          height: 128rpx;
          margin-right: 24rpx;
          border: 1px solid #eee;
          border-radius: 8rpx;
          flex: none;
        }

        .right {
          flex: 1;
          min-height: 128rpx;
          @include flex(lr);
          flex-direction: column;
          align-items: stretch;
          border-bottom: 1px solid #eee;

          .drug_name {
            flex: 1;
            font-size: 28rpx;
            font-weight: bold;
          }

          .drug_info {
            flex: 1;
            font-size: 24rpx;
            color: #999;
          }

          .drug_active {
            flex: 1;
            font-size: 24rpx;
            line-height: 40rpx;
            color: red;
          }

          .right_menu {
            flex: 2;
            @include flex(lr);

            .price {
              font-size: 28rpx;
              color: #ff3b30;
              font-weight: bold;

              .del {
                font-size: 20rpx;
                color: #666;
                text-decoration: line-through;
                margin-left: 12rpx;
              }
            }

            .info {
              font-size: 28rpx;
            }

            .action {
              @include flex;

              .icon {
                width: 32rpx;
                height: 32rpx;
              }

              .num {
                width: 60rpx;
                height: 36rpx;
                @include flex;
                font-size: 28rpx;
                background-color: #fff;
                margin: 0 16rpx;
                border-radius: 4rpx;
              }
            }
          }
        }
      }
    }
  }
}
.drug-coverUp {
  width: 128rpx;
  height: 128rpx;
  background: rgba(255, 255, 255, 0.5);
  position: absolute;
  top: 0;
  left: 0;
  img {
    width: 128rpx;
    height: 128rpx;
  }
}
.cfCover {
  text-align: center;
  font-size: 10px;
  background: none !important;
  display: flex;
  align-items: center;
}
.gx {
  filter: blur(2px);
}
.outOfLimit {
  font-size: 12px;
  color: red !important;
}
.outOfStock-coverUp {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 128rpx;
  height: 50rpx;
  background: rgba(189, 189, 189, 0.7);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgb(56, 171, 248);
  font-size: 12px !important;
}
</style>
