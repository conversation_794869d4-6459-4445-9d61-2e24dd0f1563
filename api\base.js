import http from "../common/request/request.js";

// 获取全局参数
export function getConfigInfo(param = {}) {
    return http({
        url: "business/sysconfig/getConfigInfo",
        param,
        method: "post",
    });
}

// 获取全局参数
export function getConfigInfoByKey(data) {
    return http({
        url: "business/sysconfig/getConfigInfoByKey",
        method: "post",
        data: data,
    });
}

// 获取医院信息
export function findAllHospital(param = {}) {
    return http({
        url: "basic/hospital/findAllHospital",
        param,
        method: "post",
    });
}

// 查询推荐的医生
export function randomDoctor(param = {}) {
    return http({
        url: "basic/usercollectdoc/randomDoctor",
        param,
        method: "post",
    });
}

// 查询收藏的医生
export function findDoctorByUserID(param = {}) {
    return http({
        url: "basic/usercollectdoc/findDoctorByUserId",
        param,
        method: "post",
    });
}

// 取消关注
export function cancelCollectDoctor(param = {}) {
    return http({
        url: "basic/usercollectdoc/cancel",
        param,
        method: "post",
    });
}

// 关注医生
export function addCollectDoctor(param = {}) {
    return http({
        url: "basic/usercollectdoc/add",
        param,
        method: "post",
    });
}

// 绑定医生
export function saveUserRelation(param = {}) {
    return http({
        url: "basic/userPatienfollowDoc/binding",
        param,
        method: "post",
    });
}

// 更换绑定医生
export function changeApply(param = {}) {
    return http({
        url: "basic/userRelation/changeApply",
        param,
        method: "post",
    });
}

// 获取所有的科室
export function getAllDept(param = {}) {
    return http({
        url: "basic/dept/dept/findAllDept",
        param,
        method: "post",
    });
}

// 查询某一科室下的所有医生
export function findDoctorByDeptID(param = {}) {
    return http({
        url: "basic/doctor/searchDoctor",
        param,
        method: "post",
    });
}

// 查询某一科室下的所有医生
export function findDoctorByDeptIDFZ(param = {}) {
    return http({
        url: "basic/doctor/searchDoctorFZ",
        param,
        method: "post",
    });
}

// 查询所有问诊类型
export function findAllVisitType(param = {}) {
    return http({
        url: "basic/dicvisittype/findAllVisitType",
        param,
        method: "post",
    });
}

// 查询所有的医生职称列表
export function findDoctorProfType(param = {}) {
    return http({
        url: "basic/doctor/findDoctorProfType",
        param,
        method: "post",
    });
}

// 医生主页查询医生信息
export function findDoctorByID(param = {}) {
    return http({
        url: "basic/doctor/findDoctorByID",
        param,
        method: "post",
    });
}

// 获取医生主页排班信息{该医生在该科室的所有排班信息的汇总}
export function findAllByType(param = {}) {
    return http({
        url: "basic/dicvisittype/findDoctorAppointNum",
        param,
        method: "post",
    });
}

// 查询医生主页，评价信息
export function findDocEvaluateListPage(param = {}) {
    return http({
        url: "order/payOrder/findDocEvaluateListPage",
        param,
        method: "post",
    });
}

// 查询医生所属的科室
export function findDocDept(param = {}) {
    return http({
        url: "basic/dept/findDocDept",
        param,
        method: "post",
    });
}

// 判断该医生是否被关注过
export function getIsExist(param = {}) {
    return http({
        url: "basic/usercollectdoc/isExist",
        param,
        method: "post",
    });
}

// 关注该医生
export function addKeepDoc(param = {}) {
    return http({
        url: "basic/usercollectdoc/add",
        param,
        method: "post",
    });
}

// 取消关注该医生
export function cancelKeepDoc(param = {}) {
    return http({
        url: "basic/usercollectdoc/cancel",
        param,
        method: "post",
    });
}

// 修改患者端服务提示状态
export function updateIsServiceReminderStatus(param = {}) {
    return http({
        url: "basic/propatient/updateIsServiceReminderStatus",
        param,
        method: "post",
    });
}

// 查询出诊费用明细
export function findVisitTypePrice(param = {}) {
    return http({
        url: "basic/dicvisittype/findVisitTypePrice",
        param,
        method: "post",
    });
}

// 查询就诊协议内容
export function findVisitAgreement(param = {}) {
    return http({
        url: "basic/sysvisitagreement/findVisitAgreement",
        param,
        method: "post",
    });
}

// 查询患者个性化单价
export function getPersonalityPrice(param = {}) {
    return http({
        url: "basic/provisitreal/getPersonalityPrice",
        param,
        method: "post",
    });
}

// 患者签署就诊协议(post请求)
export function addProsignagreement(param = {}) {
    return http({
        url: "business/prosignagreement/add",
        param,
        method: "post",
    });
}

// 获取快捷回复信息
export function getQuickInputInfo(param = {}) {
    return http({
        url: "basic/dicquickinput/getQuickInputInfo",
        param,
        method: "post",
    });
}

// 添加病情描述
export function savePatientRecords(param = {}) {
    return http({
        url: "business/patientrecords/savePatientRecords",
        param,
        method: "post",
    });
}

// 获取缴费支付的订单信息(微信支付加密串)
export function getRegisterPayInfo(param = {}) {
    return http({
        url: "business/paymentBusiness/getRegisterPayInfo",
        param,
        method: "post",
    });
}

// 查询支付状态
export function queryRegisterPayStatus(param = {}) {
    return http({
        url: "business/paymentBusiness/queryRegisterPayStatus",
        param,
        method: "post",
    });
}

// 撤销挂号(post请求)
export function cancelRegister(param = {}) {
    return http({
        url: "business/paymentBusiness/cancel",
        param,
        method: "post",
    });
}
export function cancelWaitOrder(param = {}) {
    return http({
        url: "business/paymentBusiness/cancelWaitOrder",
        param,
        method: "post",
    });
}
// 获取挂号成功-订单详情(post请求)
export function getRegisterOrderDetail(param = {}) {
    return http({
        url: "business/proregister/getOrderDetail",
        param,
        method: "post",
    });
}

// 挂号成功，更新聊天列表（聊天页表删除后，需重新更新）
export function updateChatList(param = {}) {
    return http({
        url: "business/proregister/updateChatOperation",
        param,
        method: "post",
    });
}

// 费用信息(post请求)
export function getRegPayInfo(param = {}) {
    return http({
        url: "business/proregister/getRegPayInfo",
        param,
        method: "post",
    });
}

// 患者端-我的-我的处方列表
export function getPreBussList(param = {}) {
    return http({
        url: "basic/proPatientBusiness/getPreBussList",
        param,
        method: "post",
    });
}

// 本次检查单
export function getProPacsList(param = {}) {
    return http({
        url: "basic/proPacsInfoController/getProPacsList",
        param,
        method: "post",
    });
}

// 本次检验单
export function getProLisList(param = {}) {
    return http({
        url: "basic/proLisInfoController/getProLisList",
        param,
        method: "post",
    });
}

// 患者慢病续方处方列表
export function getDocPatientPreList(param = {}) {
    return http({
        url: "business/proPrescriptionController/getDocPatientPreList",
        param,
        method: "post",
    });
}

// 查询药店信息详情
export function findDrugStoreDetail(param = {}) {
    return http({
        url: "basic/dicDrugStore/findDrugStoreDetail",
        param,
        method: "post",
    });
}
// 查询排班类型
export function getVisitRealType(param = {}) {
    return http({
        url: "basic/doctor/getVisitRealType",
        param,
        method: "post",
    });
}

// 医生查询个人中心基本信息接口
export function getDoctorInfo(param = {}) {
    return http({
        url: "basic/docBasicInfo/getDoctorInfo",
        param,
        method: "post",
    });
}

// 绑定用户时候， 有信息重复情况 身份证号一样 手机号不一样
export function patientLogOneDataChangeTelNo(param = {}) {
    return http({
        url: "basic/hisLogin/patientLogOneDataChangeTelNo",
        param,
        method: "post",
    });
}

// 绑定用户 两个信息重复
export function patientLogTwoData(param = {}) {
    return http({
        url: "basic/hisLogin/patientLogTwoData",
        param,
        method: "post",
    });
}

// 患者注册 20019
export function verifyPatient(param = {}) {
    return http({
        url: "basic/hisLogin/verifyPatient",
        param,
        method: "post",
    });
}

// 根据就诊人id获取相关医生列表 （关注列表）
export function getMyDocList(param) {
    return http({
        url: "basic/propatientWeb/findPatientAttentionDocList",
        method: "post",
        param,
    });
}

// 根据就诊人id获取相关医生列表 （绑定列表）
export function getMyBangDocList(param) {
    return http({
        url: "basic/userPatienfollowDoc/findPatientAttentionDocList",
        method: "post",
        param,
    });
}

// 查询快递信息
export function LogisticTrackQuery(
    logisticCode,
    customerName = "",
    shipperCode = ""
) {
    return http({
        url: "basic/Logistic/LogisticTrackQuery",
        method: "post",
        param: {
            logisticCode,
            customerName,
            orderCode: "",
            shipperCode,
        },
    });
}

export function queryLogisticInfoById(param) {
    return http({
        url: "basic/Logistic/queryLogisticInfoById",
        method: "post",
        param,
    });
}
// 查询疾病列表
export function getDicDisease(param) {
    return http({
        url: "basic/dicDisease/getDicDisease",
        method: "post",
        param,
    });
}

// 获取系统群发详情
export function getSendContentBySendId(sendId) {
    return http({
        url: "basic/proflocksend/getSendContentBySendId",
        method: "post",
        param: {
            sendId,
        },
    });
}

// 最新系统配置 05 19
export function getSysPlatformConfigByKeyList(arr = []) {
    return http({
        url: "basic/sysPlatformConfig/getSysPlatformConfigByKeyList",
        param: {
            configKeyList: arr,
        },
        method: "post",
    });
}

//根据主键查询平台全局参数
export function getSysPlatformConfigBykey(param = {}) {
    return http({
        url: "basic/sysPlatformConfig/getSysPlatformConfigBykey",
        param,
        method: "post",
    });
}

// 获取药品加减规则配置
export function getDrugShoppingCartConfigs() {
    return http({
        url: "basic/sysPlatformConfig/getConfigFromNacos",
        param: {
            researchKey: "drugShoppingcartConfigs"
        },
        method: "post",
    });
}

// 查询处方模板详情
export function getDrugPrescriptionPreInfoScanCode(param) {
    return http({
        url: "basic/doccommonprescription/getDrugPrescriptionPreInfoScanCode",
        param,
        method: "post",
    });
}

/*
	  智能导诊内容
 */

// 查询初始节点问题
export function getIntelligentGuideStartNodeAfterNode(param) {
    return http({
        url: "basic/intelligentGuideProcess/getIntelligentGuideStartNodeAfterNode",
        param,
        method: "post",
    });
}

// 查询导诊分页
export function getIntelligentGuideListNoPage() {
    return http({
        url: "basic/intelligentGuide/getIntelligentGuideListNoPage",
        method: "post",
        param: {
            isDisable: 0,
        },
    });
}

// 查询下级节点
export function getIntelligentGuideAfterNode(param) {
    return http({
        url: "basic/intelligentGuideProcess/getIntelligentGuideAfterNode",
        param,
        method: "post",
    });
}

// 查询电子号条
export function selectElectronicArticleListPage(param) {
    return http({
        url: "basic/patientRegister/selectElectronicArticleListPage",
        param,
        method: "post",
    });
}

// 查询挂号排队号
export function myRegQueue(regId) {
    return http({
        url: "business/paymentBusiness/myRegQueue",
        param: {
            regId,
        },
        method: "post",
    });
}

// 获取当前科室医生号别的排班
export function getVisitReal(param) {
    return http({
        url: "basic/patientAppoint/getVisitReal",
        param,
        method: "post",
    });
}

// 线下验证预约
export function checkAppointOffLine(param) {
    return http({
        url: "basic/patientAppoint/checkAppointOffLine",
        param,
        method: "post",
    });
}

// 查询医生线下排班号别分类信息（医生主页用）
export function getDocOfflineRoster(param) {
    return http({
        url: "basic/doctor/getDocOfflineRoster",
        param,
        method: "post",
    });
}

// 物流提示
export const queryLogisticsTipsNotPlatform = () => {
    return http({
        url: "basic/shoppingOnlineStore/queryLogisticsTipsNotPlatform",
        method: "post",
    });
};

// 物流提示 新版列表
export const queryLogisticsTipsNotPlatformList = (subjectId) => {
    return http({
        url: "basic/shoppingOnlineStore/queryLogisticsTipsNotPlatformList",
        param: { subjectId },
        method: "post",
    });
};

/**
 * @description 代理商邀请医生注册
 * @param {String} captcha 验证码
 * @param {String} supplierId 体系
 * @param {String} telNo 手机号
 * @param {String} userId 用户id
 * */

export function agentUserInviteDocRegister(param) {
    return http({
        url: "basic/hisLogin/agentUserInviteDocRegister",
        param,
        method: "post",
    });
}

/**
 * @description 快捷开方-患者快捷开方完善个人信息
 * @param {String} appid 公众号appid
 * @param telNo 手机号
 * @param name 名称
 * @param openid 用户openid
 * */

export function patientFastKFCompleteInfo(param) {
    return http({
        url: "basic/hisLogin/patientFastKFCompleteInfo",
        param,
        method: "post",
    });
}

/**
 * @description 快捷开方-无用户时默认生成用户
 * @param {String} appid 公众号appid
 * @param {String} openid 用户openid
 * */

export function fastFkGenerateUser(param) {
    return http({
        url: "basic/hisLogin/fastFkGenerateUser",
        param,
        method: "post",
    });
}

/**
 * @description 快捷开方-快捷开方补录处方信息
 * @param {Object} param 参数
 * */

export function createFastPrescription(param) {
    return http({
        url: "business/proPrescriptionController/createFastPrescription",
        param,
        method: "post",
    });
}

// 根据就诊人id 查询就诊人
export function findPatientInfoByPatientId(patientId) {
    return http({
        url: "basic/propatient/findPatientInfoByPatientId",
        param: { patientId },
        method: "post",
    });
}

// 获取药品详情
export function findDrugById(drugId, drugStoreId, openId) {
    return http({
        url: "basic/dicDrugController/findDrugById",
        param: {
            drugId,
            drugStoreId,
            openid: openId,
        },
        method: "post",
    });
}

//名医推荐
export function patientHomeRecommendDoctor(param = {}) {
    return http({
        url: "basic/usercollectdoc/patientHomeRecommendDoctor",
        param,
        method: "post",
    });
}

//查询患者端首页轮播图
export function queryPatientHomeCarouselMapListPage(param = {}) {
    return http({
        url: "dictionary/dicPatientCarouselMap/queryPatientHomeCarouselMapListPage",
        param,
        method: "post",
    });
}
//特色科室列表
export function findCharacteristicDept(param = {}) {
    return http({
        url: "basic/dept/dept/findCharacteristicDept",
        param,
        method: "post",
    });
}

// HIS患者根据手机号和验证码登录
export function hisPatientLoginByTelPhoneAndCaptcha(param = {}) {
    return http({
        url: "basic/hisLogin/hisPatientLoginByTelPhoneAndCaptcha",
        param,
        method: "post",
    });
}
// 查询特色科室
export function findDiagTreatmentSubject(param = {}) {
    return http({
        url: "basic/dicDiagTreatmentSubject/findDiagTreatmentSubject",
        param,
        method: "post",
    });
}
export function sysQualificationInfo(param = {}) {
    return http({
        url: "basic/sysQualificationInfo/info",
        param,
        method: "get",
    });
}
export function dicSzHospitalList(param = {}) {
    return http({
        url: "basic/dicSzHospital/list",
        param,
        method: "post",
    });
}
export function paymentBusinessrefund(param = {}) {
    return http({
        url: "business/paymentBusiness/refund",
        param,
        method: "post",
    });
}
export function cancelMessage(param = {}) {
    return http({
        url: "chat/hxMessages/cancelMessage",
        param,
        method: "post",
    });
}
export function getDrugStoreInfoPage(param = {}) {
    return http({
        url: "basic/dicDrugStore/getDrugStoreInfoDoctor",
        param,
        method: "post",
    });
}
export function queryStoresByDrugId(param = {}) {
    return http({
        url: "basic/drugStorageYf/queryStoresByDrugId",
        param,
        method: "post",
    });
}
export function getYxOrderStatus(param = {}) {
    return http({
        url: "order/payOrder/getYxOrderStatus",
        param,
        method: "post",
    });
}
export function queryQuickPrescription(param = {}) {
    return http({
        url: "business/quickPrescription/queryQuickPrescription",
        param,
        method: "post",
    });
}
export function batchGetDrugLimitNum(param = {}) {
    return http({
        url: "basic/webDicDrug/batchGetDrugLimitNum",
        param,
        method: "post",
    });
}
export function findOfflineDrugById(drugId, drugStoreId, openId) {
    return http({
        url: "basic/dicDrugController/findOfflineDrugById",
        param: {
            drugId,
            drugStoreId,
            openid: openId,
        },
        method: "post",
    });
}

// 查询快递信息 圆心 物流轨迹
export function LogisticTrackQuerySanF(logisticCode, yxOrderSn) {
    return http({
        url: "order/payOrderPatient/orderTrace",
        method: "post",
        param: {
            logisticsCode: logisticCode,
            yxOrderSn,
        },
    });
}

//查询快递公司信息 (神州)
export function queryLogisticInfo(data) {
    return http({
        url: "/basic/Logistic/queryLogisticInfo",
        method: "post",
        data,
    });
}

// 查询物流轨迹 神州自营
export function trackQuerySZ(data) {
    return http({
        url: "/basic/Logistic/trackQuery",
        method: "post",
        param: {
            logisticCode: data.code,
            orderCode: data.orderCode,
        },
    });
}
export function getPeddingIssueList(param) {
    return http({
        url: "basic/szFinancialInvoice/getPeddingIssueList",
        method: "post",
        param,
    });
}
// 提交发票申请
export function batchSave(param) {
    return http({
        url: "basic/szFinancialInvoice/batchSave",
        method: "post",
        param,
    });
}
export function szFinancialInvoiceDisable(param) {
    return http({
        url: "basic/szFinancialInvoice/disable",
        method: "post",
        param,
    });
}
export function szFinancialInvoiceUpdate(param) {
    return http({
        url: "basic/szFinancialInvoice/update",
        method: "post",
        param,
    });
}
export function szFinancialInvoiceInfo(param) {
    return http({
        url: "basic/szFinancialInvoice/info",
        method: "post",
        param,
    });
}
export function getInvoiceList(param) {
    return http({
        url: "basic/szFinancialInvoice/getInvoiceList",
        method: "post",
        param,
    });
}
export function getVisitingPersonInfo(param = {}) {
    return http({
        url: "basic/proofpatient/getVisitingPersonInfo",
        param,
        method: "post",
    });
}
export function listPageDicArticleDrug(param = {}) {
    return http({
        url: "basic/webDrugLink/listPageDicArticleDrug",
        param,
        method: "post",
    });
}
export function findArticleDrugById(param = {}) {
    return http({
        url: "basic/dicDrugController/findArticleDrugById",
        param,
        method: "post"
    })
}
// 查询首次用药
export function queryFirstEatDrug(param = {}) {
    return http({
        url: "business/patientMedicationPushLog/confirmDrug",
        param,
        method: "post"
    });
}

// 根据医生ID查询DTP药店信息
export function getDoctorDtpDrugstoreInfo(param = {}) {
    return http({
        url: "basic/doctorDtpDrugstore/info",
        param,
        method: "post",
    });
}

export function checkMaxQuan(data) {
    return http({
        url: '/business/paymentBusiness/checkMaxQuan',
        method: 'post',
        data
    });
}

export function checkMaxQuanPatient(data) {
    return http({
        url: '/business/paymentBusiness/checkMaxQuanPatient',
        method: 'post',
        data
    });
}