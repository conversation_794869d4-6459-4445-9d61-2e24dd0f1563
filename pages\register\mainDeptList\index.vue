<template>
  <view class="body-view">
    <scroll-view class="deptMenu" scroll-y>
      <view class="drug-slider">
        <block v-for='(item, index) in deptList' :key="index">
          <view class="drug-slider-item" :class="currentSliderIndex === index ? 'active' : ''"
                @click="changeDrugType(item, index)">
            <image v-if="currentSliderIndex === index" src="/static/doc/u-4.png" alt="" mode="widthFix"/>
            {{ item.subjectName }}
          </view>
        </block>
      </view>
    </scroll-view>
    <view style="flex: 1;margin-left: 10px">
      <template v-if="docList.length">
        <scroll-view
            scroll-y="true"
            class="doc_list_box"
            @scrolltolower="getMore"
        >
          <div class="list_warp">
            <template v-for="item in docList">
              <view style="border-bottom: 0.5px solid rgba(229, 229, 229, 1);padding-bottom: 7px">
                <view class="doc_box" @click="goDocHomePage(item)" >
                  <div
                      :class="
                        item.isOnline == 0
                          ? 'docImg_box_unOnline'
                          : 'docImg_box_Online'
                      "
                      style="width: 106rpx;height: 106rpx"
                  >
                    <img
                        :src="
                          item.docImg
                            ? item.docImg
                            : '/static/images/docHead.png'
                        "
                        class="header_img"
                    ></img>
                    <div
                        :style="{
                          color: item.isOnline == 0 ? '#ffffff' : '#ffffff',
                          textAlign: 'center',
                          marginTop: '-15px',
                          zIndex: 10,
                          position: 'relative',
                          fontSize: '10px',
                        }"
                    >
                      <div v-if="item.isOnline == 0" style=" width: 37px;height: 15px;border-radius: 15px;background: #c7c7c7;margin: auto;display: flex;align-items: center;justify-content: space-evenly;color: #efefef">
                        <span style="font-size: 8px">离线</span>
                      </div>
                      <div v-else style=" width: 37px;height: 15px;border-radius: 15px;background: #0AC2B2;margin: auto;display: flex;align-items: center;justify-content: space-evenly;color: #ffffff">
                        <span style="font-size: 8px">在线</span>
                      </div>
                    </div>
                  </div>

                  <view class="doc_box_right">
                    <view class="doc_box_right_top">
                      <view class="doc_box_right_name" style="flex: 1">
                        <view style="display: flex;align-items: center;justify-content: space-between">{{ item.docName }}
                          <view class="hint_box">
                            <template v-for="element in item.visitType">
                              <view
                                  :class="
                                element.visitTypeCode == 1
                                  ? 'one'
                                  : element.visitTypeCode == 3
                                  ? 'three'
                                  :element.visitTypeName=='视频'? 'three':'two'
                              "
                              >
                                {{ element.visitTypeName }}</view
                              >
                            </template>
                          </view>
                        </view>
                        <text style="font-size: 12px;color: rgba(102, 102, 102, 1);font-weight: normal">
                          {{ item.docProf }}</text>
                        <view
                        ><text
                            class="dep"
                            v-for="(deptName, index) in item.dept"
                        >{{ deptName }}</text>
                          <view style="color: rgba(153, 153, 153, 1);font-size: 12px">
                            {{
                              isShowWorkHosName
                                  ? item.workHosName
                                  : item.hosName
                            }}
                          </view>

                        </view>
                      </view>

                    </view>

                    <view class="little_label">
                      <template v-for="element in item.docLable">
                        <text>{{ element.lableName }}</text>
                      </template>
                    </view>
                  </view>
                  <view v-if="item.isReferral == '1'" class="zz-logo"
                  >转诊</view
                  >
                </view>
                <view class="doc_box_right_content">
                  <view class="docName_desp">
                    <image class="docName_desp_image" src="/static/doc/5.png"></image>
                    <text>接诊量:{{ item.consultationCount }}</text>
                  </view>
                  <text class="long">|</text>
                  <view class="docName_desp">
                    <image class="docName_desp_image" src="/static/doc/6.png"></image>
                    <text>好评数:{{ item.percentage }}%</text>
                  </view>
                </view>
              </view>
            </template>
            <view v-if="isShowMore">
              <uni-load-more :status="status"></uni-load-more>
            </view>
          </div>
        </scroll-view>
      </template>
      <view class="empty_list" v-else>
        <image src="/static/images/index/office_empty.png" />
        <view> 暂无医生 </view>
      </view>
    </view>
<!--    <div class="fixed">-->
<!--      <view class="scroll-list">-->
<!--        <scroll-view-->
<!--          class="top-menu-view"-->
<!--          scroll-x="true"-->
<!--          :scroll-left="scrollLeft"-->
<!--          scroll-with-animation-->
<!--        >-->
<!--          <view-->
<!--            class="menu-topic-view"-->
<!--            v-for="(item, index) in deptList"-->
<!--            :id="item.subjectCode"-->
<!--            :key="index"-->
<!--            :data-current="index"-->
<!--            @click="swichMenu(index, item.subjectCode)"-->
<!--          >-->
<!--            <view-->
<!--              :class="currentTab == index ? 'menu-topic-act' : 'menu-topic'"-->
<!--            >-->
<!--              <text class="menu-topic-text">{{ item.subjectName }}</text>-->
<!--            </view>-->
<!--          </view>-->
<!--        </scroll-view>-->
<!--        <view class="moreData" @click="togglePage()">-->
<!--          <uni-icons type="list"></uni-icons>-->
<!--        </view>-->
<!--      </view>-->
<!--    </div>-->
<!--    &lt;!&ndash; 内容 &ndash;&gt;-->
<!--    <swiper-->
<!--      class="swiper-box-list"-->
<!--      :current="currentTab"-->
<!--      @change="swiperChange"-->
<!--    >-->
<!--      <swiper-item-->
<!--        class="swiper-topic-list"-->
<!--        v-for="(itm, index) in deptList"-->
<!--        :key="itm.subjectCode"-->
<!--      >-->
<!--        <view class="swiper-item">-->
<!--          <template v-if="docList.length">-->
<!--            <scroll-view-->
<!--              scroll-y="true"-->
<!--              class="doc_list_box"-->
<!--              @scrolltolower="getMore"-->
<!--            >-->
<!--              <div class="list_warp">-->
<!--                <template v-for="item in docList">-->
<!--                  <view class="doc_box" @click="goDocHomePage(item)">-->
<!--                    <div-->
<!--                      :class="-->
<!--                        item.isOnline == 0-->
<!--                          ? 'docImg_box_unOnline'-->
<!--                          : 'docImg_box_Online'-->
<!--                      "-->
<!--                    >-->
<!--                      <image-->
<!--                        :src="-->
<!--                          item.docImg-->
<!--                            ? item.docImg-->
<!--                            : '/static/images/docHead.png'-->
<!--                        "-->
<!--                        mode="aspectFill"-->
<!--                        class="header_img"-->
<!--                      ></image>-->
<!--                      <div-->
<!--                        :style="{-->
<!--                          color: item.isOnline == 0 ? '#ffffff' : '#ffffff',-->
<!--                          background:-->
<!--                            item.isOnline == 0 ? '#eeeeee' : '#0AC2B2',-->
<!--                          textAlign: 'center',-->
<!--                          marginTop: '-20px',-->
<!--                          zIndex: 10,-->
<!--                          position: 'relative',-->
<!--                        }"-->
<!--                      >-->
<!--                        {{ item.isOnline == 0 ? "离线" : "在线" }}-->
<!--                      </div>-->
<!--                    </div>-->

<!--                    <view class="doc_box_right">-->
<!--                      <view class="doc_box_right_top">-->
<!--                        <view class="doc_box_right_name">-->
<!--                          <view>{{ item.docName }} {{ item.docProf }}</view>-->
<!--                          <view-->
<!--                            ><text-->
<!--                              class="dep"-->
<!--                              v-for="(deptName, index) in item.dept"-->
<!--                              >{{ deptName }}</text-->
<!--                            >-->
<!--                            {{-->
<!--                              isShowWorkHosName-->
<!--                                ? item.workHosName-->
<!--                                : item.hosName-->
<!--                            }}</view-->
<!--                          >-->
<!--                        </view>-->
<!--                        <view class="hint_box">-->
<!--                          <template v-for="element in item.visitType">-->
<!--                            <view-->
<!--                              :class="-->
<!--                                element.visitTypeCode == 1-->
<!--                                  ? 'one'-->
<!--                                  : element.visitTypeCode == 3-->
<!--                                  ? 'three'-->
<!--                                  : 'two'-->
<!--                              "-->
<!--                            >-->
<!--                              {{ element.visitTypeName }}</view-->
<!--                            >-->
<!--                          </template>-->
<!--                        </view>-->
<!--                      </view>-->

<!--                      <view class="doc_box_right_content">-->
<!--                        <text>好评数:{{ item.percentage }}%</text>-->
<!--                        <text class="long">|</text>-->
<!--                        <text>接诊量:{{ item.consultationCount }}</text>-->
<!--                        &lt;!&ndash;                        <text class="long">|</text>&ndash;&gt;-->
<!--                        &lt;!&ndash;                        <text>平均响应:{{ item.responseTime }}分钟</text>&ndash;&gt;-->
<!--                      </view>-->

<!--                      <view class="little_label">-->
<!--                        <template v-for="element in item.docLable">-->
<!--                          <text>{{ element.lableName }}</text>-->
<!--                        </template>-->
<!--                      </view>-->
<!--                    </view>-->
<!--                    <view v-if="item.isReferral == '1'" class="zz-logo"-->
<!--                      >转诊</view-->
<!--                    >-->
<!--                  </view>-->
<!--                </template>-->
<!--                <view v-if="isShowMore">-->
<!--                  <uni-load-more :status="status"></uni-load-more>-->
<!--                </view>-->
<!--              </div>-->
<!--            </scroll-view>-->
<!--          </template>-->
<!--          <view class="empty_list" v-else>-->
<!--            <image src="/static/images/index/office_empty.png" />-->
<!--            <view> 暂无医生 </view>-->
<!--          </view>-->
<!--        </view>-->
<!--      </swiper-item>-->
<!--    </swiper>-->

<!--    &lt;!&ndash; 遮罩 &ndash;&gt;-->
<!--    <view class="mask" v-show="isShowMask" @click="togglePage()"></view>-->
<!--    <view class="sub-menu-class" v-show="isShowMask">-->
<!--      <block v-for="(item, index) in deptList" :key="item.subjectCode">-->
<!--        <view class="deptItem" @click="swichMenu(index, item.subjectCode)">-->
<!--          {{ item.subjectName }}-->
<!--        </view>-->
<!--      </block>-->
<!--    </view>-->
  </view>
</template>

<script>
import {
  findDoctorByDeptID,
  getAllDept,
  findDiagTreatmentSubject,
} from "@/api/base.js";
import myJsTools from "@/common/js/myJsTools.js";
export default {
  data() {
    return {
      currentSliderIndex:0,
      scrollInto: "",
      isShowWorkHosName: uni.getStorageSync("isShowWorkHosName") || false,
      docListQuery: {
        appid: uni.getStorageSync("appId"),
        openid: uni.getStorageSync("wxInfo").openId,
        subjectCode: "",
        searchKey: "",
        page: 1,
        limit: 10,
      },
      // 所有科室
      listQuery: {
        deptName: "",
        page: 1,
        limit: 100,
      },
      total: "",
      isShowMore: false,
      status: "loading",
      isShowMask: false,
      scrollLeft: 0,
      deptList: [],
      currentTab: "",
      docList: [],
    };
  },
  watch: {

  },
  onLoad(option) {
    this.docListQuery.subjectCode = option.subjectCode||'';
  },
  onShow() {
    this.docList = [];
    this.docListQuery.page = 1;
    this.getDocList();
  },
  onReady() {
    this.getAllDept();
  },
  // 下拉刷新
  onPullDownRefresh() {
    this.docList = [];
    this.docListQuery.page = 1;
    this.getDocList();
  },
  methods: {
    changeDrugType(item,index){
      this.currentSliderIndex=index
      this.docList = [];
      this.docListQuery.subjectCode = item.subjectCode;
      this.docListQuery.page = 1;
      this.isShowMask = false;
      this.getDocList();
    },
    goDocHomePage(e) {
      let { hosId, docId } = e;
      uni.setStorageSync("hosId", hosId);
      uni.navigateTo({
        url: "/pages/register/docHomePage/index?docId=" + docId,
      });
    },
    async getDocList() {
      uni.stopPullDownRefresh();
      this.docListQuery.openid = uni.getStorageSync("wxInfo").openId;
      let {
        data: { rows, total },
      } = await findDoctorByDeptID(this.docListQuery);
      let docList = rows;
      this.total = total;
      // 循环获取头像
      for (let i = 0; i < docList.length; i++) {
        let obj = docList[i];
        // 存在头像
        if (obj.docImg) {
          myJsTools.downAndSaveImg(obj.docImg, (url) => {
            obj.docImg = url;
          });
        }
      }
      // 拼接数组
      // this.docList = [...this.docList, ...docList];
      this.docList = [...this.docList, ...this.updOCList(docList)];
      console.log(this.docList,456789)
    },
    //重新格式化医生数组,将重复医生归类
    updOCList(arr) {
      let newArr = [];
      arr.forEach((item, index) => {
        let i = newArr.findIndex((nitem) => {
          return nitem.docId == item.docId;
        });
        if (i == -1) {
          item.dept = [item.deptName];
          newArr.push(item);
        } else {
          newArr[i].dept.push(item.deptName);
        }
      });
      return newArr;
    },
    // 获取所有科室列表
    async getAllDept() {
      let { data } = await findDiagTreatmentSubject();
      this.deptList =[{subjectName:"全部",subjectCode:null},...data];
      let index = this.getIndex(this.deptList, this.docListQuery.subjectCode);
      this.currentSliderIndex = index; // 设置当前选中的标签索引
      // this.$nextTick(() => {
      //   this.swichMenu(index, this.docListQuery.subjectCode);
      // });
    },
    getIndex(data, subjectCode) {
      if (subjectCode) {
        return data.findIndex((item) => item.subjectCode == subjectCode);
      } else {
        //点击查看更多
        this.currentTab = 0;
        this.docListQuery.subjectCode = data?.length && data[0].subjectCode;
        return 0;
      }
    },
    // 加载更多
    getMore() {
      this.isShowMore = true;
      // 共几页
      let num = Math.ceil(this.total / this.docListQuery.limit);
      if (this.docListQuery.page < num) {
        this.docListQuery.page += 1;
        this.getDocList();
        this.isShowMore = false;
      } else {
        this.status = "noMore";
      }
    },
    togglePage() {
      this.isShowMask = !this.isShowMask;
    },
    // 模糊搜索
    search(e) {
      this.docList = [];
      this.docListQuery.page = 1;
      this.docListQuery.searchKey = e.value;
      this.getDocList();
    },
    // 清空搜索框时,重置
    changeInput(e) {
      if (e.value == "") {
        this.docList = [];
        this.docListQuery.page = 1;
        this.docListQuery.searchKey = "";
        this.getDocList();
      }
    },
    setScrollLeft() {
      const query = wx.createSelectorQuery();
      query.selectAll(".menu-topic-view").boundingClientRect();
      console.log("query", query);
      query.exec((res) => {
        console.log("res", res);
        let num = 0;
        for (let i = 0; i < this.currentTab; i++) {
          num += res[0][i].width;
        }
        this.scrollLeft = Math.ceil(num) - 50;
      });
    },
    swichMenu(index, code) {
      if (this.currentTab == index) {
        return;
      } else {
        console.log(code,456,index)
        this.docList = [];
        this.docListQuery.subjectCode = code;
        this.docListQuery.page = 1;
        this.isShowMask = false;
        this.currentTab = index;
        this.currentSliderIndex = index; // 同步更新currentSliderIndex
        this.setScrollLeft();
      }
    },
    swiperChange(e) {
      console.log(e,999);
      this.docList = [];
      this.currentTab = e.detail.current;
      let subjectCode = this.deptList.find(
        (item, index) => index === this.currentTab
      ).subjectCode;
      this.docListQuery.subjectCode = subjectCode;
      this.docListQuery.page = 1;
      this.isShowMask = false;
      this.setScrollLeft();
    },
  },
};
</script>

<style scoped lang="scss">
.docImg_box_unOnline {
  border: 1px solid #CCCCCC;
  margin-right: 20upx;
  border-radius: 50%;
}
.docImg_box_Online {
  border: 1px solid #0AC2B2;
  margin-right: 20upx;
  border-radius: 50%;
}

.empty_list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  font-weight: 400;
  color: rgba(158, 163, 173, 1);
  position: absolute;
  top: 50%;
  left: 60%;
  transform: translate(-50%, -50%);
}

.doc_list_box {
  // padding: 32rpx;
  box-sizing: border-box;
  overflow-y: auto;
  background: #fff;
  flex: 1;
  height: 100vh;

  .list_warp {
    padding: 32rpx;
  }

  .doc_box {

    @include flex(lr);
    align-items: flex-start;
    padding: 24rpx 0;
    position: relative;

    .header_img {
      width: 104rpx;
      height: 104rpx;
      border-radius: 50%;
      margin-right: 0 !important;
    }
  }
}

.scroll-list {
  position: relative;
  display: flex;
  height: 88rpx;
  font-size: 28rpx;
  border-bottom: 1px solid #ebebeb;
  background: #fbfbfb;

  .top-menu-view {
    display: flex;
    // position: fixed;
    z-index: 100;
    top: 88rpx;
    left: 0;
    white-space: nowrap;
    height: 88rpx;
    line-height: 88rpx;
    width: 90%;
    .menu-topic-view {
      display: inline-block;
      white-space: nowrap;
      height: 86rpx;
      position: relative;

      .menu-topic-text {
        font-size: 28rpx;
        color: #303133;
        padding: 10rpx;
        font-weight: 500;
      }

      .menu-topic-act .menu-topic-text {
        @include font_theme;
      }
    }
  }

  .moreData {
    right: 0;
    height: 88rpx;
    line-height: 88rpx;
    font-size: 28rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80rpx;
  }
}

.mask {
  z-index: 1;
  position: fixed;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0);
  transition: background-color 0.15s linear;
  background-color: rgba(0, 0, 0, 0.5);
}

.sub-menu-class {
  position: absolute;
  left: 0;
  right: 0;
  top: 45rpx;
  max-height: 550rpx;
  min-height: 500rpx;
  height: 500rpx;
  overflow-y: auto;
  background-color: #ffffff;
  padding: 20rpx;
  z-index: 11;
  box-shadow: 0 5px 5px rgba(0, 0, 0, 0.1);
  transition: transform 0.15s linear;
  transform: translate3d(0, calc(44px + 1rpx), 0);

  .deptItem {
    width: 30%;
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    margin-right: 20rpx;
    margin-bottom: 20rpx;
    display: block;
    background: #e7eaef;
    font-size: 28rpx;
    float: left;
    border-radius: 8rpx;
  }
}

/* 搜索框样式 */
.search-container {
  width: 100%;
  position: relative;
  z-index: 14;
}

::v-deep.uni-searchbar {
  padding: 0 20upx;
  height: 88rpx;
  border-bottom: 2rpx solid #ebebeb;
}

::v-deep.uni-searchbar__box {
  height: 88rpx;
  background: #ffffff !important;
  border-radius: 0px !important;
  border: none;
}

.search-container {
  position: relative;
  z-index: 15;
}

.body-view {
  width: 100%;
  position: relative;
  overflow: hidden;
  height: 100vh;
  display: flex;
  background: white;
  justify-content: space-between;
}
.deptMenu{
  width: 170rpx;
  height: 100vh;
  overflow: auto;
}
.swiper-box-list {
  padding: 32rpx;
  flex: 1;
  background-color: #ffffff;
  // height: calc(100vh - 251rpx);
  // margin-top: 176rpx;
  // margin-bottom: 30rpx;

  .swiper-topic-list {
    width: 100%;

    .swiper-item {
      height: 100%;

      .doc_list_box {
        height: 100%;
        overflow-y: auto;
      }
    }
  }
}

/* 隐藏滚动条，但依旧具备可以滚动的功能 */
::v-deep.uni-scroll-view::-webkit-scrollbar {
  display: none;
}
</style>
<style scoped lang="scss">
.page {
  display: flex;
  flex-direction: column;
}

/* 搜索框样式 */
.search-container {
  width: 100%;
  position: relative;
  z-index: 14;
}

::v-deep.uni-searchbar {
  padding: 0 20upx;
  height: 88rpx;
  border-bottom: 2rpx solid #ebebeb;
}

::v-deep.uni-searchbar__box {
  height: 88rpx;
  background: #ffffff !important;
  border-radius: 0px !important;
  border: none;
}

.fixed {
  width: 100%;
  position: sticky;
  // position: fixed;
  height: 85rpx;
  flex: none;
  top: 0;
  z-index: 99999;
}

.search-container {
  position: relative;
  z-index: 15;
}

/* 筛选条件 */
.query-container {
  width: 100%;
  height: 84rpx;
}

/* 医生列表 */
.doc_list_box {
  // padding: 32rpx;
  box-sizing: border-box;
  overflow-y: auto;
  background: #fff;
  flex: 1;

  .list_warp {
    padding: 32rpx;
  }

  .doc_box {
    @include flex(lr);
    align-items: flex-start;
    padding: 24rpx 0 16rpx 0;
    position: relative;
    .header_img {
      width: 104rpx;
      height: 104rpx;
      border-radius: 50%;
      margin-right: 20rpx;
    }
  }
}

// 标签
.little_label {
  width: 100%;

  text {
    display: inline-block;
    background: #e8f6fd;
    border-radius: 18rpx;
    font-size: 22rpx;
    @include font_theme;
    margin-right: 16upx;
    padding: 0 12rpx;
    height: 36rpx;
    line-height: 36upx;
    font-weight: 400;
  }
}

.doc_box .doc_box_right {
  flex: 1;
}

.doc_box .hint_box {
  display: flex;
}

.doc_box .hint_box view {
  width: 88rpx;
  height: 36rpx;
  border-radius: 8rpx;
  color: #ffffff;
  font-size: 24rpx;
  text-align: center;
  line-height: 36rpx;
}

.doc_box .hint_box view {
  margin-left: 10rpx;
}

.doc_box .hint_box view:first-child {
  margin-left: 0;
}

.doc_box .hint_box .one {
  color: rgba(135, 79, 240, 1);
  border-radius: 1px;
  background: rgba(242, 245, 255, 1);
  font-size: 10px;
}

.doc_box .hint_box .two {
  background: rgba(222, 252, 235, 1);
  color: rgba(0, 186, 173, 1);
  font-size: 10px;
}
.two{
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px !important;
  padding-top: 0 !important;
}
.one{
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px !important;
  padding-top: 0 !important;
}
.three{
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px !important;
  padding-top: 0 !important;
}
.doc_box .hint_box .three {
  background: rgba(222, 244, 252, 1);
  color: rgba(0, 120, 215, 1);
  font-size: 10px;
}

.doc_box .doc_box_right_top {
  display: flex;
  justify-content: space-between;
}

.doc_box .doc_box_right_name {
  font-size: 28rpx;
  font-weight: bold;
  color: rgba(51, 51, 51, 1);
}

.doc_box .doc_box_right_name view:last-child {
  font-weight: 500;
  font-size: 28rpx;
  color: #444444;
  padding-top: 6rpx;
}

// 描述
.doc_box_right_content {
  @include flex(lr);
  font-size: 24rpx;
  color: #a6aab2;
  padding: 6upx 0;
}

/* 转诊标志 */
.zz-logo {
  width: 88rpx;
  height: 36rpx;
  border-radius: 8rpx;
  color: #ffffff;
  background: #159f5c;
  font-size: 24rpx;
  text-align: center;
  line-height: 36rpx;
  position: absolute;
  top: 70rpx;
  right: 0;
}

/* 列表为空提示 */
.empty_list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  font-weight: 400;
  color: rgba(158, 163, 173, 1);
  position: absolute;
  top: 50%;
  left: 60%;
  transform: translate(-50%, -50%);
}

.dep {
  margin-right: 8rpx;
  font-size: 12px;
  color: rgba(102, 102, 102, 1);
}

.empty_list image {
  margin-bottom: 40rpx;
  width: 386rpx;
  height: 324rpx;
}
.drug-slider {
  background: rgba(250, 250, 250, 1);
  overflow: auto;
  width: 176rpx;
  .drug-slider-item {
    width: 100%;
    height: 80rpx;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-size: 12px;
    color: rgba(102, 102, 102, 1) !important;
    image{
      margin-right: 10px;
      width: 18px;
      height: 18px;
    }
    &.active {
      color: rgba(131, 106, 255, 1) !important;
      position: relative;
      background: #fff;
      border-radius: 16rpx 0rpx 0rpx 16rpx;
    }
  }
}

.drug-slider-content {
  padding: 20rpx;
  flex: 1;
  overflow: auto;

  .drug-slider-content_wrapper {
    height: 100%
  }
}
.docName_desp{
  display: flex;
  align-items: center;
}
.docName_desp_image {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
}
</style>
