"use strict";
const plugins_xeUtils_isSymbol = require("./isSymbol.js");
const plugins_xeUtils_isDate = require("./isDate.js");
const plugins_xeUtils_isArray = require("./isArray.js");
const plugins_xeUtils_isRegExp = require("./isRegExp.js");
const plugins_xeUtils_isError = require("./isError.js");
const plugins_xeUtils_isNull = require("./isNull.js");
function getType(obj) {
  if (plugins_xeUtils_isNull.isNull(obj)) {
    return "null";
  }
  if (plugins_xeUtils_isSymbol.isSymbol(obj)) {
    return "symbol";
  }
  if (plugins_xeUtils_isDate.isDate(obj)) {
    return "date";
  }
  if (plugins_xeUtils_isArray.isArray(obj)) {
    return "array";
  }
  if (plugins_xeUtils_isRegExp.isRegExp(obj)) {
    return "regexp";
  }
  if (plugins_xeUtils_isError.isError(obj)) {
    return "error";
  }
  return typeof obj;
}
exports.getType = getType;
