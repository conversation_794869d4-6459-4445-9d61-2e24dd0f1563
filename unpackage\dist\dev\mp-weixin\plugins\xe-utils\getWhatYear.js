"use strict";
const plugins_xeUtils_staticStrFirst = require("./staticStrFirst.js");
const plugins_xeUtils_staticStrLast = require("./staticStrLast.js");
const plugins_xeUtils_helperGetDateFullYear = require("./helperGetDateFullYear.js");
const plugins_xeUtils_getWhatMonth = require("./getWhatMonth.js");
const plugins_xeUtils_toStringDate = require("./toStringDate.js");
const plugins_xeUtils_isValidDate = require("./isValidDate.js");
function getWhatYear(date, offset, month) {
  var number;
  date = plugins_xeUtils_toStringDate.toStringDate(date);
  if (plugins_xeUtils_isValidDate.isValidDate(date)) {
    if (offset) {
      number = offset && !isNaN(offset) ? offset : 0;
      date.setFullYear(plugins_xeUtils_helperGetDateFullYear.helperGetDateFullYear(date) + number);
    }
    if (month || !isNaN(month)) {
      if (month === plugins_xeUtils_staticStrFirst.staticStrFirst) {
        return new Date(plugins_xeUtils_helperGetDateFullYear.helperGetDateFullYear(date), 0, 1);
      } else if (month === plugins_xeUtils_staticStrLast.staticStrLast) {
        date.setMonth(11);
        return plugins_xeUtils_getWhatMonth.getWhatMonth(date, 0, plugins_xeUtils_staticStrLast.staticStrLast);
      } else {
        date.setMonth(month);
      }
    }
  }
  return date;
}
exports.getWhatYear = getWhatYear;
