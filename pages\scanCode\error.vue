<template>
  <!-- 支付成功 -->
  <view class="pay_success">
    <image src="/static/shop/error.png" mode="widthFix"></image>

    <view class="text">
      抱歉，支付失败，请检查网络是否正常，或者您的余额是否不足
    </view>

    <view class="but">
      <button @click="back">再次支付</button>
    </view>
  </view>
</template>

<script>
export default {
  methods: {
    back() {
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.pay_success {
  height: 100vh;
  padding-top: 80rpx;
  background-color: #f5f5f5;
  @include flex(left);
  flex-direction: column;

  image {
    width: 386rpx;
  }

  .text {
    width: 458rpx;
    font-size: 28rpx;
    line-height: 40rpx;
    margin-top: 40rpx;
    text-align: center;
  }

  .but {
    padding-top: 90rpx;

    button {
      width: 334rpx;
      height: 84rpx;
      background: #ff5050;
      border-radius: 42rpx;
      font-size: 32rpx;
      color: #fff;
    }
  }
}
</style>
