<template>
  <!-- 支付成功 -->
  <view class="pay_success">
    <image src="/static/shop/success.png" mode="widthFix"></image>

    <view class="text" v-if="type == 1">
      支付成功，可在我的订单中跟踪订单信息
    </view>

    <view class="text" v-else>
      <p>您的订单提交成功，请等待药店审核</p>
      可在我的商城订单中跟踪订单信息
    </view>

    <!-- 按钮组 -->
    <view class="buts">
      <button class="one" @click="lockDetail">查看订单</button>
      <button class="two" @click="toShop">返回商城</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      orderNo: '',
      type: 1,
    };
  },
  onLoad(opt) {
    this.orderNo = opt.id;
    if (opt.type) this.type = opt.type;
  },
  methods: {
    // 回商城首页
    toShop() {
      uni.reLaunch({
        url: '../index',
      });
    },
    // 查看订单
    lockDetail() {
      if (this.type == 1) {
        uni.redirectTo({
          url:
            '/pages/order/detail/drugStatus?orderNo=' +
            this.orderNo +
            '&source=2',
        });
      } else {
        uni.redirectTo({
          url: '/pages/shopOrder/detail?orderNo=' + this.orderNo,
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.pay_success {
  height: 100vh;
  padding-top: 80rpx;
  background-color: #f5f5f5;
  @include flex(left);
  flex-direction: column;

  image {
    width: 386rpx;
  }

  .text {
    max-width: 500rpx;
    text-align: center;
    font-size: 28rpx;
    line-height: 40rpx;
    margin-top: 40rpx;
  }

  .buts {
    width: 100%;
    padding: 0 30rpx;
    @include flex(lr);

    button {
      margin-top: 94rpx;
      width: 320rpx;
      height: 84rpx;
      border-radius: 42rpx;
      @include border_theme;
      font-size: 32rpx;
      @include flex;

      &.one {
        @include font_theme;
      }

      &.two {
        @include bg_theme;
        color: #fff;
      }
    }
  }
}
</style>
