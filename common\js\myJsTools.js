import { downloadImg } from "../../api/oss.js";
import { patientToDoctorSendMessageToMq } from "../../api/chat";

// 手机号隐藏中间四位
const phone = (str) => {
  if (str.length < 11) return str;
  return str.substr(0, 3) + "****" + str.substr(7, 10);
};

// ms转化为时分秒
const formatDuring = (mss) => {
  var days = parseInt(mss / (1000 * 60 * 60 * 24));
  var hours = parseInt((mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  var minutes = parseInt((mss % (1000 * 60 * 60)) / (1000 * 60));
  var seconds = (mss % (1000 * 60)) / 1000;
  days = days == 0 ? "" : days + " 天 ";
  hours = hours == 0 ? "" : hours + " 小时 ";
  minutes = minutes == 0 ? "" : minutes + " 分钟 ";
  seconds = seconds == 0 ? "" : seconds + " 秒 ";
  return days + hours + minutes + seconds;
};

const formatTime = (date) => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  const second = date.getSeconds();

  return (
    [year, month, day].map(formatNumber).join("/") +
    " " +
    [hour, minute, second].map(formatNumber).join(":")
  );
};

const formatTimeDate = (date) => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  return [year, month, day].map(formatNumber).join("-");
};

const getDate = (type = null, number = 0) => {
  var nowdate = new Date();
  switch (type) {
    case "day": //取number天前、后的时间
      nowdate.setTime(nowdate.getTime() + 24 * 3600 * 1000 * number);
      var y = nowdate.getFullYear();
      var m = nowdate.getMonth() + 1;
      if (m < 10) m = "0" + m;
      var d = nowdate.getDate();
      if (d < 10) d = "0" + d;
      var retrundate = y + "-" + m + "-" + d;
      break;
    case "week": //取number周前、后的时间
      var weekdate = new Date(nowdate + 7 * 24 * 3600 * 1000 * number);
      var y = weekdate.getFullYear();
      var m = weekdate.getMonth() + 1;
      if (m < 10) m = "0" + m;
      var d = weekdate.getDate();
      if (d < 10) d = "0" + d;
      var retrundate = y + "-" + m + "-" + d;
      break;
    case "month": //取number月前、后的时间
      nowdate.setMonth(nowdate.getMonth() + number);
      var y = nowdate.getFullYear();
      var m = nowdate.getMonth() + 1;
      if (m < 10) m = "0" + m;
      var d = nowdate.getDate();
      if (d < 10) d = "0" + d;
      var retrundate = y + "-" + m + "-" + d;
      break;
    case "year": //取number年前、后的时间
      nowdate.setFullYear(nowdate.getFullYear() + number);
      var y = nowdate.getFullYear();
      var m = nowdate.getMonth() + 1;
      var d = nowdate.getDate();
      var retrundate = y + "-" + m + "-" + d;
      break;
    default:
      //取当前时间
      var y = nowdate.getFullYear();
      var m = nowdate.getMonth() + 1;
      if (m < 10) m = "0" + m;
      var d = nowdate.getDate();
      if (d < 10) d = "0" + d;
      var retrundate = y + "-" + m + "-" + d;
  }
  return retrundate;
};

const formatNumber = (n) => {
  n = n.toString();
  return n[1] ? n : "0" + n;
};

// 根据身份证号返回出生年月
const getBirthdayFromIdCard = function (idCard) {
  var birthday = "";
  if (idCard != null && idCard != "") {
    if (idCard.length == 15) {
      birthday = "19" + idCard.substr(6, 6);
    } else if (idCard.length == 18) {
      birthday = idCard.substr(6, 8);
    }
    birthday = birthday.replace(/(.{4})(.{2})/, "$1-$2-");
  }
  return birthday;
};
const getItem = function (key) {
  if (!window.localStorage) {
    alert("请检查浏览器版本，该版本不支持localStorage存储");
  } else {
    var ms = "mystorage";
    var storage = window.localStorage;
    var mydata = storage.getItem(ms);
    if (!mydata) {
      return false;
    }
    mydata = JSON.parse(mydata);
    return mydata.data[key];
  }
};
// 下载oss图片
const downAndSaveImg = async (ossName, callback, errCallback) => {
  try {
    let res = await downloadImg({
      imgName: ossName,
    });
    if (res.code != 20000) return;
    let url = res.data.url.split("-internal").join("").split("?")[0];
    if (url) {
      // 设置缩略图
      // url += '?x-oss-process=image/resize,s_80,m_lfit';
      // 默认缩略图
      callback(url);
      return Promise.resolve(url);
    }
  } catch (error) {
    return Promise.reject("下载失败 -", ossName);
  }
};

// 预览原图
const previewImg = (url) => {
  let src = url.split("?")[0];
  uni.previewImage({
    urls: [src],
  });
};

// 获取url中的参数
const request = function (paras) {
  var url = location.href;
  console.log("查看当前链接", url);
  var paraString = url.substring(url.indexOf("?") + 1, url.length).split("&");
  var paraObj = {};
  for (var i = 0, j; (j = paraString[i]); i++) {
    paraObj[j.substring(0, j.indexOf("=")).toLowerCase()] = decodeURIComponent(
      j.substring(j.indexOf("=") + 1, j.length)
    );
  }
  var returnValue = paraObj[paras.toLowerCase()];

  if (typeof returnValue == "undefined") {
    return "";
  } else {
    return returnValue;
  }
};

// 获取url中的参数
const getUrlParam = function () {
  var url = location.href;
  var paraString = url.substring(url.indexOf("?") + 1, url.length).split("&");
  var paraObj = {};
  for (var i = 0, j; (j = paraString[i]); i++) {
    paraObj[j.substring(0, j.indexOf("="))] = decodeURIComponent(
      j.substring(j.indexOf("=") + 1, j.length)
    );
  }
  return paraObj;
};

let timer;

const msgReadFun = function (message) {
  clearTimeout(timer);
  timer = setTimeout(() => {
    let chatList = app.$store.getters.getChatList;
    chatList.chatRecordList.map((msg) => {
      if (msg.mid == message.mid) {
        if (msg.status != "recall") {
          if (msg.status != "read") {
            let item = {
              patientId: msg.ext?.patientId,
              patientName: msg.ext?.patientName,
              userId: uni.getStorageSync("userId"),
              type: "chat",
            };
            let param = {
              receiverIdArray: [msg.to],
              jgExpandMap: item,
              message: "患者给你发送了消息",
              patientId: item.patientId,
            };
            // 需要控制不要一次发送多条
            // 暂时不通知医生
            patientToDoctorSendMessageToMq(param);
          }
        }
      }
    });
  }, 2000);
};

// 尝试压缩图片
function setImgZip(obj, callback) {
  var file = obj;
  var reader = new FileReader();
  reader.readAsDataURL(file);
  reader.onload = function (e) {
    dealImage(
      this.result,
      {
        quality: 0.1,
      },
      function (base) {
        //调用
        var blob = dataURLtoBlob(base);
        var newFile = new File([blob], file.name, {
          type: file.type,
        });

        let r = new FileReader(); //本地预览
        r.onload = function () {
          callback && callback(r.result);
        };
        r.readAsDataURL(newFile); //Base64
      }
    );
  };
}

//将base64转换为blob
function dataURLtoBlob(dataurl) {
  var arr = dataurl.split(","),
    mime = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], {
    type: mime,
  });
}

/**
 * 图片压缩，默认同比例压缩
 * @param {Object} path
 * pc端传入的路径可以为相对路径，但是在移动端上必须传入的路径是照相图片储存的绝对路径
 * @param {Object} obj
 * obj 对象 有 width， height， quality(0-1)
 * @param {Object} callback
 * 回调函数有一个参数，base64的字符串数据
 */
function dealImage(path, obj, callback) {
  var img = new Image();
  img.src = path;
  img.onload = function () {
    var that = this;
    // 默认按比例压缩
    var w = that.width,
      h = that.height,
      scale = w / h;
    w = obj.width || w;
    h = obj.height || w / scale;
    var quality = obj.quality || 0.1; // 默认图片质量为0.7
    //生成canvas
    var canvas = document.createElement("canvas");
    var ctx = canvas.getContext("2d");
    // 创建属性节点
    var anw = document.createAttribute("width");
    anw.nodeValue = w;
    var anh = document.createAttribute("height");
    anh.nodeValue = h;
    canvas.setAttributeNode(anw);
    canvas.setAttributeNode(anh);
    ctx.drawImage(that, 0, 0, w, h);
    // 图像质量
    if (obj.quality && obj.quality <= 1 && obj.quality > 0) {
      quality = obj.quality;
    }
    // quality值越小，所绘制出的图像越模糊
    var base64 = canvas.toDataURL("image/jpeg", quality);
    // 回调函数返回base64的值
    callback(base64);
  };
}

export default {
  phone,
  formatTime: formatTime,
  getBirthdayFromIdCard,
  formatTimeDate,
  formatDuring,
  getDate,
  downAndSaveImg,
  request,
  msgReadFun,
  setImgZip,
  previewImg,
  getItem,
  getUrlParam,
};
