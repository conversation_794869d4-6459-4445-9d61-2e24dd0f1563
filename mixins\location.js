/*
  Author: 王可 (<EMAIL>)
  wx.js (c) 2021
  Desc: 获取经纬度解析
  Created:  2021/12/20下午3:46:27
  Modified: 2021/12/21下午3:42:51
*/
import { getJSSDKSign } from '@/api/share.js';
const jweixin = require('jweixin-module');

const getLocation = () => {

  return new Promise((resolve, reject) => {
    jweixin.getLocation({
      type: 'gcj02', // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
      success: function (res) {
        var latLng = new qq.maps.LatLng(res.latitude, res.longitude)
        resolve({
          latLng,
          lat: res.latitude,
          lng: res.longitude
        })
      },
      fail: reject
    })
  })
}


export default {

  methods: {

    // 调起分享sdk
    async wxsdkInit() {

      return new Promise(async (resolve, reject) => {


        const appId = uni.getStorageSync('appId');
        let { data: info } = await getJSSDKSign({
          appid: appId,
          url: window.location.href.split('#')[0],
        });

        jweixin.config({
          debug: false,
          appId,
          timestamp: info.timestamp,
          nonceStr: info.nonceStr,
          signature: info.signature,
          jsApiList: ['getLocation'],
        });


        jweixin.ready(async function () {

          try {

            let latLng = await getLocation();

            var getAddr = new qq.maps.Geocoder({
              complete: function (res) {
                var allAddress = res.detail.addressComponents
                let wxaddress = {
                  province: allAddress.province,
                  city: allAddress.city,
                  ...latLng
                }
                delete wxaddress.latLng;
                uni.setStorageSync('shop_city', wxaddress)
                console.log('设置成功');
                resolve(wxaddress)
              }
            })

            getAddr.getAddress(latLng.latLng);
          } catch (error) {

            reject(error)
          }

        })

        jweixin.error(function (res) {
          console.log('微信js-sdk 配置失败000' + res.errMsg)
          reject(res)
        })

      })
    },
  },
};
