<template>
  <!-- 在线购药首页 -->
  <view class="shop_index">
    <!-- 搜索 -->
    <view class="search">
      <view class="address">
        <uni-icons type="location-filled" size="14" color="#999"></uni-icons>
        <text class="city">{{ drugStoreInfo.drugstoreName }}</text>
      </view>
      <view class="cont">
        <uni-icons type="search" size="20" color="#C1C1C1"></uni-icons>
        <!--        <view class="text">搜索要购买的商品</view>-->
        <input class="text" type="text" placeholder="搜索要购买的商品" v-model="drugName" />
        <view class="but" @click="getList">搜索</view>
      </view>
      <view class="order" v-if="isYnw" @click="toOrder">我的订单</view>
    </view>
    <!-- 主体 -->
    <view class="shop_cont">
      <!-- 药品列表 -->
      <view class="drug_list">
        <scroll-view class="scroll" :scroll-top="scrollTop" scroll-with-animation scroll-y="true" @scroll="scroll" @scrolltolower="loadMoreData">
          <view class="drug_item" v-for="(item, index) in list" :key="item.yfkcId">
            <DRUG :item="item" :isScan="true" :index="index" @reduce="reduce" @add="add" />
          </view>
          <!-- 空列表 -->
          <view class="empty" v-if="!list.length">
            <image src="/static/images/index/box_empty.png" mode="widthFix" class="img"></image>
            <text>暂无数据</text>
          </view>

          <!-- 到底了 -->
          <view class="list_footer" v-if="list.length">已经到底了</view>
        </scroll-view>
      </view>
    </view>
    <FooterButton v-if="isHave" @click="submit">预支付并提交</FooterButton>
    <!-- 某个药品数量超过 12 -->
    <Confirm @confirmPropCenter="nextConfirm" :type="2" @cancelPropCenter="showList" v-if="showCancel">
      根据国家卫健委、国家医保局发布《长期处方管理规范（试行）》，您选择的药品数量已超过长期处方最大用量12盒，是否仍确定购买？
      如不继续，请返回重新修改数量。
    </Confirm>
  </view>
</template>

<script>
import LOCATION from "@/mixins/location.js";
import Confirm from "@/components/propCenter/propCenter.vue";
import { getDrugStoreTypeList, getOfflineDrug } from "@/api/shop.js";
import FooterButton from "@/components/footer_button/button.vue";
import { getSysPlatformConfigByKeyList } from "@/api/base.js";
import DRUG from "./components/drugItem.vue";
import CART from "./components/cart.vue";
import { mapGetters, mapActions } from "vuex";
import { getCityById } from "./map";
import Login from "@/mixins/login.js";
import { batchGetDrugLimitNum, getDrugStoreInfoPage } from "../../api/base";
import { Toast } from "../../common/js/pay";
import { getDrugStoreInfoPage2, getShoppingCartOrderCantPay } from "../../api/shop";
import { getDoctorDtpDrugstoreInfo } from "../../api/base";
import drugRulesMixin from '@/mixins/drugRules.js'
// 是否第一次
let isFrist = true;

export default {
  name: "ShopIndex",
  mixins: [Login, LOCATION, drugRulesMixin],
  components: {
    DRUG,
    CART,
    FooterButton,
    Confirm,
  },
  data () {
    return {
      scrollTop: 0,
      // 分类
      menu: [],
      showCancel: false,
      // 当前选中
      act: -1,
      // 二级分类
      sub_menu: [],
      // 列表
      list: [],
      // 搜索条件
      dstId: "",
      // 显示购物车
      showCart: false,
      // 位置
      location: {},
      // 是否ynw
      isYnw: false,
      totalNum: 0,
      drugName: "",
      listquery: {
        page: 1,
        limit: 20,
      },
      scanInfo: {},
      drugStoreInfo: {},
      flag: '',
      projectId: '',
      didOnlyId: '',
      isHave: true
    };
  },
  computed: {
    ...mapGetters([
      "drugNum",
      "shopList",
      "checkStore",
      "scanCheckDrugList",
      "drugStoreList",
    ]),
  },
  async onLoad (v) {
    this.isHave = true
    console.log(v);
    this.scanInfo = { docId: v.docId }
    this.flag = v.flag;
    this.projectId = v.projectId;
    this.didOnlyId = v.didOnlyId;
    await this.getConfig();
    let isHide = uni.getStorageSync("hideTab");
    if (isHide && isHide == 1) {
      uni.hideTabBar();
    }
  },
  async created () {
    this.$store.dispatch("shop/setScanCheckDrugList", []);
    let cityInfo = uni.getStorageSync("shop_city") || "";
    if (cityInfo) {
      this.location = cityInfo;
    }
    if (!this.checkStore.drugstoreName) {
      await this.getDrugStoreInfoPage();
    }
    // await this.getMenu();
    await this.getDrugStoreInfoPage2()
    await this.initList();
  },
  async onShow () {
    await this.initDrugRules()
  },
  methods: {
    ...mapActions("shop", ["addDrugItem", "reduceDrugItem"]),
    showList () {
      this.showCancel = false;
    },
    async getDrugStoreInfoPage2 () {
      try {
        let drugstoreId = '9999'
        if (!this.flag && !this.projectId) {
          const dtpRes = await getDoctorDtpDrugstoreInfo({
            docId: this.scanInfo.docId || ""
          });
          if (dtpRes && dtpRes.data) {
            drugstoreId = dtpRes.data.dtpDrugstoreId;
          }
          if (!dtpRes || !dtpRes.data || !dtpRes.data.dtpDrugstoreId) {
            this.isHave = false
            drugstoreId = '9999'
            console.log('获取医生DTP药店信息失败')
            uni.showToast({
              title: '此二维码已过期，请联系医生提供最新购药二维码',
              icon: 'none',
              duration: 2000
            })
            setTimeout(() => {
              WeixinJSBridge.call('closeWindow');
            }, 1000)
          }
        }
        const res = await getDrugStoreInfoPage2({
          page: 1,
          limit: 9999,
          drugstoreId: drugstoreId || '9999',
          dpsId: '9ffa142fbd22518253bd6445e657a017'
        });

        if (!res.data.rows.length) {
          return
        }
        this.drugStoreInfo = res.data.rows[0]
      } catch (error) {
        console.error('获取药店信息失败:', error);
      }
    },
    nextConfirm () {
      let old =
        "/pages/shop/submit/scanSubmit?list=" +
        JSON.stringify(this.drugStoreList) +
        "&isOFF=" +
        1 + '&projectId=' + (this.projectId || '') + '&didOnlyId=' + (this.didOnlyId || '');
      uni.navigateTo({
        url: old,
      });
    },
    // 药品限制数量
    async vaDrugLimitNum (drugs) {
      const drugIds = drugs.map((v) => v.drugId);
      const res = await batchGetDrugLimitNum({ drugIds });
      if (!res.data) {
        return true;
      }
      const drugLimitNumList = res.data.filter((v) => v.limitNum != 0);
      if (drugLimitNumList.length === 0) {
        return true;
      }
      const fails = drugs.filter((v) => {
        const find = drugLimitNumList.find((item) => item.drugId === v.drugId);
        return find && find.limitNum < v.quan;
      });
      if (fails.length) {
        const find = drugLimitNumList.find(
          (item) => item.drugId === fails[0].drugId
        );
        if (find) {
          uni.showToast({
            title: `${fails[0].drugName}单次开具数量限制${find.limitNum}，请核实！`,
            icon: "none",
          });
          return false;
        }
      }
      return true;
    },
    async submit () {
      if (!this.scanCheckDrugList.length) {
        Toast("未选择药品");
        return;
      }

      //  如处方药数量超过5种，则toast提示【根据政策要求，单一处方内处方药数量不可超过5种】
      if (this.scanCheckDrugList.length > 5) {
        Toast('根据政策要求，单一处方内处方药数量不可超过5种');
        return;
      }
      if (this.scanCheckDrugList.length) {
        if (
          this.scanCheckDrugList.some(
            (a) => Math.floor(a.drugKc) < Math.floor(a.quan)
          )
        ) {
          Toast("药品库存不足，请重新选择");
          return;
        }
      }
      if (!(await this.vaDrugLimitNum(this.scanCheckDrugList))) {
        return;
      }
      // 判断是否多选药店
      if (this.drugStoreList.length > 1) {
        // 找到已选药店的所有药品
        let arr = [];
        // 循环重组所有药品
        this.drugStoreList.forEach((v) => {
          let item = this.shopList.find((k) => k.drugStoreID === v);
          arr = this.scanCheckDrugList;
        });
        if (!arr.length) {
          Toast("未选择药品");
          return;
        }

        let index = -1;
        // 存药品id
        let list = [];
        arr.forEach((k, i) => {
          if (list.includes(k.drugId)) {
            index = i;
          } else {
            list.push(k.drugId);
          }
        });
      }
      if (!this.hasInfo()) return;

      if (this.drugStoreList.length == 1) {
        let isNext = true;

        let arr = this.scanCheckDrugList;

        arr.forEach((v) => {
          if (v.quan > 12) {
            isNext = false;
          }
        });

        if (!isNext) {
          this.showCancel = true;
          return false;
        }
      }

      try {
        let old =
          '/pages/shop/submit/scanSubmit?list=' +
          JSON.stringify(this.drugStoreList) + '&isOFF=' + 1 + '&scanInfo=' + JSON.stringify(this.scanInfo) + '&projectId=' + (this.projectId || '') + '&didOnlyId=' + (this.didOnlyId || '');
        uni.removeStorageSync('isAgree')
        uni.navigateTo({
          url: old,
        });
      } catch (error) { }
    },
    // 获取配置
    loadMoreData () {
      if (this.list.length >= this.totalNum) return;
      this.listquery.page += 1;
      this.getList();
    },
    initList () {
      this.list = [];
      this.listquery.page = 1;
      this.getList();
    },
    async getConfig () {
      let { data } = await getSysPlatformConfigByKeyList([
        "shoppingOnlineFastBuyDrug",
      ]);
      if (data.length) {
        // 1 为新流程 不可多选
        this.isYnw = data[0].configValue == 1 ? true : false;
      }
    },
    async getDrugStoreInfoPage () {
      const res = await getDrugStoreInfoPage({});
      const storeList = res.data;
      this.$store.commit("shop/SETCHECKSTORE", storeList[0]);
    },
    // 去订单
    toOrder () {
      if (!this.hasInfo()) return;
      uni.navigateTo({
        url: "/pages/shopOrder/index",
      });
    },
    // 获取当前城市
    async getCity () {
      let infos = {
        city: "正在获取中",
      };
      this.location = infos;
      let info;
      try {
        info = await this.wxsdkInit();
      } catch (error) {
        info = await getCityById();
      }
      this.location = info;
      uni.setStorageSync("shop_city", info);
      isFrist = false;
    },
    // 跳转选择地址
    toAddress () {
      if (!this.hasInfo()) return;
      // uni.navigateTo({
      //   url: "/pages/shop/address/location?city=" + this.location.city,
      // });
      uni.navigateTo({
        url: "/pages/shop/store?city=" + JSON.stringify(this.location),
      });
    },
    // 购物车药品变化
    itemChange (item) {
      // 查找当前列表指定药品的下标
      const index = this.list.findIndex((v) => v.yfkcId == item.yfkcId);
      if (index == -1) return;
      let it = this.list[index];
      it.quan = item.quan;
      // 修改当前药品数量
      this.$set(this.list, index, it);
    },
    // 药品减少
    async reduce (item, index, amount = 1) {
      await this.reduceDrugItem({ ...item, isScan: true, amount });
      item.quan -= amount;
      if (item.quan <= 0) item.quan = 0;
      this.$set(this.list, index, item);
    },
    // 药品增加
    async add (item, index, amount = 1) {
      await this.addDrugItem({ ...item, isScan: true, amount });
      item.quan += amount;
      this.$set(this.list, index, item);
    },
    // 显示购物车
    setShowCart () {
      if (!this.drugNum) return;
      this.showCart = true;
      this.$refs.cart.showList();
    },
    // 点击一级菜单
    setMenu (n) {
      this.listquery.page = 1;
      this.listquery.limit = 10;
      this.list = [];
      if (this.act == n) return;
      this.act = n;
      if (n == -1) {
        this.dstId = "";
        this.sub_menu = [];
      }
      if (n > -1) {
        // 改变二级菜单
        this.sub_menu = this.menu[n].children;
        this.dstId = this.menu[n].dstId;
      }
      this.scrollTop = 0;
      this.getList();
    },
    // 点击二级菜单
    setSubMenu (id) {
      this.listquery.page = 1;
      this.listquery.limit = 10;
      if (!id) {
        if (this.act == -1) {
          this.dstId = "";
        } else {
          this.dstId = this.menu[this.act].dstId;
        }
      } else {
        if (this.dstId == id) return;
        this.dstId = id;
      }

      this.scrollTop = 0;
      this.getList();
    },
    // 滚动到底
    scrollFooter () { },
    // 监听滚动
    scroll (e) {
      const { scrollTop } = e.detail;
      this.scrollTop = scrollTop;
    },
    // 去搜索
    toSearch () {
      uni.navigateTo({
        url: "./search/search",
      });
    },
    // 获取分类
    getMenu () {
      console.log("this.checkStore", this.checkStore);
      getDrugStoreTypeList({ drugStoreId: this.checkStore.drugstoreId }).then(
        (res) => {
          let menu = [];
          let arr = [];
          res.data.forEach((item) => {
            if (item.grade == 1) {
              menu.push(item);
            } else if (item.grade == 2) {
              arr.push(item);
            }
          });
          menu.forEach((v) => {
            v.children = arr.filter((item) => item.pdstId == v.dstId);
          });
          // 分类
          this.menu = menu;
        }
      );
    },
    // 获取药品列表
    async getList () {
      const { lat, lng, province } = this.location;

      const drugId = uni.getStorageSync("ynw_drugId");

      let res = await getOfflineDrug({
        searchCondition: this.drugName,
        projectId: this.projectId,
        drugstoreId: this.drugStoreInfo.drugstoreId
      });
      this.list = (res.data).map(v => {
        return {
          ...v,
          subjectId: v.drugstoreId
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
uni-page-body {
  height: 100%;
  touch-action: none;

  * {
    box-sizing: border-box;
  }
}

.shop_index {
  background-color: #fff;
  height: 100%;
  @include flex;
  flex-direction: column;
  align-items: stretch;

  .search {
    height: 88rpx;
    padding: 0 24rpx 0 0;
    @include flex;
    flex: none;
    .address {
      flex: none;
      @include flex;
      font-size: 28rpx;
      width: 20%;

      .city {
        @include hide;
      }
      image {
        width: 120rpx;
        height: 40rpx;
      }
    }

    .cont {
      width: 100%;
      padding: 0 24rpx;
      height: 60rpx;
      border-radius: 30rpx;
      background-color: #f5f5f5;
      @include flex(lr);

      .text {
        font-size: 28rpx;
        color: #c1c1c1;
        flex: 1;
        padding-left: 20rpx;
      }

      .but {
        font-size: 28rpx;
        @include font_theme;
      }
    }

    .order {
      flex: none;
      margin-left: 14rpx;
      width: 148rpx;
      height: 60rpx;
      @include bg_theme;
      color: #fff;
      font-size: 24rpx;
      @include flex;
      border-radius: 30rpx;
    }
  }

  .shop_cont {
    flex: 1;
    @include flex;
    align-items: stretch;
    overflow: hidden;

    .menu {
      width: 186rpx;
      overflow-y: scroll;
      background-color: #fafafa;
      flex: none;

      .menu_item {
        height: 88rpx;
        width: 100%;
        @include flex;
        position: relative;
        font-size: 26rpx;
        color: #333;
        text-align: center;
        padding: 0 14rpx;

        &.act {
          @include font_theme;
          background-color: #fff;
          font-weight: bold;

          &::before {
            content: "";
            display: block;
            position: absolute;
            width: 6rpx;
            height: 24rpx;
            @include bg_theme;
            left: 8rpx;
            top: calc(50% - 12rpx);
            border-radius: 3rpx;
          }
        }
      }
    }

    .cont_list {
      width: calc(100% - 186rpx);
      padding: 0 20rpx;
      @include flex;
      flex-direction: column;
      align-items: stretch;
      justify-items: flex-start;

      .list_nav {
        height: 56rpx;
        overflow-x: scroll;
        white-space: nowrap;
        overflow-y: hidden;
        flex: none;

        .span {
          padding: 0 24rpx;
          height: 100%;
          @include flex;
          display: inline-flex;
          font-size: 24rpx;
          background-color: #fafafa;
          color: #333;
          margin-right: 12rpx;
          border-radius: 6rpx;

          &.act {
            @include font_theme;
            background-color: #e7f5fc;
          }
        }
      }

      .round {
        min-width: 88rpx;
        height: 88rpx;
        position: fixed;
        bottom: 154rpx;
        z-index: 2;
        right: 24rpx;
        border-radius: 44rpx;
        background-color: #fafafa;
        @include flex;
        box-shadow: 0 0 20rpx #ddd;

        &.act {
          background-color: #e2f0f7;
        }

        image {
          width: 64rpx;
          height: 64rpx;
        }

        .num {
          min-width: 30rpx;
          height: 30rpx;
          padding: 0 4rpx;
          background-color: #ff5050;
          @include flex;
          font-size: 24rpx;
          color: #fff;
          border-radius: 16rpx;
          line-height: 24rpx;
          position: absolute;
          right: 0;
          top: 0;
        }

        .show {
          @include flex;
          padding: 0 24rpx;

          image {
            width: 44rpx;
            height: 44rpx;
            margin-right: 8rpx;
          }

          text {
            font-size: 28rpx;
            @include font_theme;
          }
        }
      }
    }
  }

  .drug_list {
    width: 100%;
    height: calc(100% - 56px) !important;
    padding: 15rpx 40rpx;
    overflow-y: scroll;

    .scroll {
      height: 100%;

      .empty {
        width: 100%;
        height: 80%;
        @include flex;
        flex-direction: column;

        .img {
          width: 100%;
        }

        text {
          font-size: 24rpx;
          color: #999;
          padding-top: 30rpx;
        }
      }
    }

    .list_footer {
      text-align: center;
      padding: 20rpx 0 100rpx;
      font-size: 24rpx;
      color: #999;
    }
  }
}
</style>
