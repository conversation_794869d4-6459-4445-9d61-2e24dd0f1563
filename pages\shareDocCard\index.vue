<template>
  <view class="page">
    <view class="page-container">
      <view class="doc-info">
        <image
          :src="docInfo.docImg ? docInfo.docImg : '/static/images/docHead.png'"
          mode="aspectFill"
          class="header_img"
        ></image>
        <view class="info-content">
          <view>{{ docInfo.docName }} {{ docInfo.docProf }}</view>
          <view class="office"
            >{{ docInfo.deptStr }} {{ docInfo.hosName }}</view
          >
          <view class="little_label">
            <template v-for="element in docInfo.docLable">
              <text>{{ element }}</text>
            </template>
          </view>
        </view>
      </view>
      <view class="wrapper">
        <view class="qrimg">
          <tkiQrcode
            ref="qrcode"
            :val="codeUrl"
            :size="size"
            background="#ffffff"
            foreground="#000000"
            pdground="#000000"
            icon="/static/images/logo-img.png"
            :iconSize="iconsize"
            :onval="onval"
            :loadMake="loadMake"
          ></tkiQrcode>
        </view>
      </view>
      <view class="font_size">微信扫一扫或长按识别二维码</view>
    </view>
  </view>
</template>

<script>
import { createQrCode } from '../../api/share.js';
import { getDoctorInfo } from '../../api/base.js';
import myJsTools from '@/common/js/myJsTools.js';
import tkiQrcode from '../../components/tki-qrcode/tki-qrcode.vue';
export default {
  components: {
    tkiQrcode,
  },
  data() {
    return {
      docInfo: {},
      info: {
        codeType: '',
        docName: '',
        docId: '',
        hosId: '',
      },
      codeUrl: '',
      size: 300,
      iconsize: 45,
      onval: true,
      loadMake: true,
      src: '',
    };
  },
  onLoad(option) {
    this.info = {
      codeType: option.doCodeType,
      docName: '',
      docId: option.docId,
      hosId: option.hosId,
      openid: option.openid,
    };
    // 存在用户id
    if (option.userId) {
      this.info.patientUserId = option.userId;
    }
    this.getDoctorInfo();
  },
  methods: {
    getDoctorInfo() {
      getDoctorInfo({
        docId: this.info.docId,
      }).then((res) => {
        if (res.code == 20000) {
          this.docInfo = res.data;
          if (res.data.docLabelStr) {
            this.docInfo.docLable = res.data.docLabelStr.split(',');
          }
          this.info.docName = res.data.docName;
          if (res.data.docImg) {
            myJsTools.downAndSaveImg(res.data.docImg, (url) => {
              this.docInfo.docImg = url;
            });
          }
          this.getCodeUrl();
        }
      });
    },
    getCodeUrl() {
      createQrCode(this.info).then((res) => {
        this.codeUrl = res.data.url;
      });
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
}

.doc-info {
  width: 590rpx;
  background: #f5f5f5;
  border-radius: 16rpx;
  padding: 28rpx 24rpx;
  box-sizing: border-box;
  display: flex;
}

.doc-info .header_img {
  width: 128rpx;
  height: 128rpx;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.6);
  margin-right: 32rpx;
  flex: none;
}

.doc-info .info-content {
  flex: 1;

  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  line-height: 44rpx;
}

.doc-info .office {
  font-size: 24rpx;
  font-weight: 400;
  color: #666666;
  line-height: 34rpx;
  margin-top: 4rpx;
}

.little_label {
  overflow: hidden;
  height: 45rpx;
}

.little_label text {
  background: rgba(232, 246, 253, 1);
  border-radius: 18rpx;
  font-size: 22rpx;
  @include font_theme;
  margin-right: 20rpx;
  padding: 4rpx 10rpx;
}

/* 二维码 */
.wrapper {
  margin-top: 80rpx;
}

.qrimg {
  width: 100%;
  height: 100%;
}

.font_size {
  margin-top: 46rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #5f5f5f;
  line-height: 52rpx;
}
</style>
