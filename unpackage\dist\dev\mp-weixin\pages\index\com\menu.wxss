/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*
	字体颜色相关
*/
/*
	背景色 分割线色
*/
/*
	主题色
*/
/*
		渐变可调角度
*/
/*
	定义弹性布局
 */
/*
	单行文本溢出隐藏
*/
.menu_item_image.data-v-6a3572d7 {
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(170.85deg, #874ff0 0%, #c5bbf2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}
.menu_item_image .icon.data-v-6a3572d7 {
  width: 60rpx;
  height: 46rpx;
}
.menu.data-v-6a3572d7 {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  justify-content: space-around;
  flex-wrap: wrap;
  margin-top: 24rpx;
}
.menu .menu_item.data-v-6a3572d7 {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  border-radius: 16rpx;
  background-color: #fff;
  width: 50px;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}
.menu .menu_item.three.data-v-6a3572d7 {
  flex-direction: column;
  width: 212rpx;
  height: 212rpx;
  justify-content: space-between;
  padding: 20rpx 28rpx;
}
.menu .menu_item.three .icon.data-v-6a3572d7 {
  width: 100rpx;
  height: 100rpx;
}
.menu .menu_item.three .text.data-v-6a3572d7 {
  align-items: stretch;
  flex: 1;
  font-size: 14px;
}
.menu .menu_item.three .text .bold.data-v-6a3572d7 {
  text-align: center;
}
.menu .menu_item .icon.data-v-6a3572d7 {
  width: 100rpx;
  height: 100rpx;
}
.menu .menu_item .text.data-v-6a3572d7 {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin-top: 16rpx;
  font-size: 20rpx;
  color: #c1c1c1;
}
.menu .menu_item .text .bold.data-v-6a3572d7 {
  font-size: 28rpx;
  color: #505a69;
  margin-bottom: 6rpx;
  white-space: nowrap;
}