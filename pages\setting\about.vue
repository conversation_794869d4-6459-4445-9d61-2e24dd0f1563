<template>
  <view class="qualifications">
    <view>
      <view class="title">经营资质</view>
      <view class="item">
        <view>名称：</view>
        <view style="flex: 1">{{ info.bussName }}</view>
      </view>
      <view class="item">
        <view>法定代表：</view>
        <view style="flex: 1">{{ info.bussLegalRepresentative }}</view>
      </view>
      <view class="item">
        <view>社会统一信用代码：</view>
        <view style="flex: 1">{{ info.bussUnifiedSocialCreditCode }}</view>
      </view>
      <view class="item">
        <view>资质类型：</view>
        <view style="flex: 1">{{ info.bussType }}</view>
      </view>
      <view class="item">
        <view>有效期至：</view>
        <view style="flex: 1">{{ info.bussExpirationDate }}</view>
      </view>
      <view class="item">
        <image class="imageFile" mode="widthFix" :src="info.bussPic" alt />
      </view>
    </view>
    <view style="margin-top: 10px">
      <view class="title">行业资质</view>
      <view class="item">
        <view>名称：</view>
        <view style="flex: 1">{{ info.industryName }}</view>
      </view>
      <view class="item">
        <view>法定代表：</view>
        <view style="flex: 1">{{ info.industryLegalRepresentative }}</view>
      </view>
      <view class="item">
        <view>证明文号：</view>
        <view style="flex: 1">{{ info.industryCertCode }}</view>
      </view>
      <view class="item">
        <view>资质类型：</view>
        <view style="flex: 1">{{ info.industryType }}</view>
      </view>
      <view class="item">
        <view>有效期至：</view>
        <view style="flex: 1">{{ info.industryExpirationDate }}</view>
      </view>
      <view class="item">
        <image class="imageFile" mode="widthFix" :src="info.industryPic" alt />
      </view>
    </view>
  </view>
</template>

<script >
import {sysQualificationInfo} from "../../api/base";

export default {
  data() {
    return {
      info:{}
    };
  },
  created() {
    this.infoQualificationInfo()
  },
  methods:{
    async infoQualificationInfo(){
      const res = await sysQualificationInfo();
      const data = res.data || {};
      this.info = {
        ...data,
      };
    }
  }
}
</script>

<style scoped lang="scss">
.qualifications {
  padding: 20px;
  .title {
    font-size: 15px;
    font-weight: bolder;
    margin-bottom: 7px;
  }
  .item {
    display: flex;
    margin-bottom: 4px;
  }
}
</style>
