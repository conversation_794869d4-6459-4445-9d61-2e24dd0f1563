<template>
  <!-- 并且描述 -->
  <view class="desc">
    <view class="title">
      病情描述
    </view>
    <!-- 内容 -->
    <view class="desc_item">
      <text class="label">标题</text>
      <text>{{ item.recordsTitle }}</text>
    </view>

    <view class="desc_item">
      <text class="label">首诊诊断</text>
      <text>{{ item.diagnosisDisease }}</text>
    </view>

    <view class="desc_item">
      <text class="label">首诊医院</text>
      <text>{{ item.firstVisitHos }}</text>
    </view>

    <view class="desc_item">
      <text class="label">患病时长</text>
      <text>{{ item.sickTime }}{{ item.timeUnit || "天" }}</text>
    </view>

    <view class="desc_item">
      <text class="label">病情描述</text>
      <text>{{ item.diseaseDescription }}</text>
    </view>

    <view class="desc_item">
      <text class="label">期望帮助</text>
      <text>{{ item.expectHelp }}</text>
    </view>

    <!-- 图片 -->
    <view class="desc_img">
      <view class="label">
        病情照片
        <text style="color: rgba(131, 106, 255, 1)">（仅限本人和接诊医生可看）</text>
      </view>
      <!-- 图片容器 -->
      <view class="img_list" v-if="item.diseaseImg.length">
        <UniImage class="img"
          :src="img" :preview="true"
          v-for="(img, index) in item.diseaseImg"
          :key="index"
        />
      </view>
      <!-- 空图片 -->
      <view class="img_empty" v-else> 暂无病情照片 </view>
    </view>

    <view class="desc_img" v-if="false">
      <view class="label">
        其他照片
        <text>（仅限本人和接诊医生可看）</text>
      </view>
      <!-- 图片容器 -->
      <view class="img_list" v-if="item.medicalImg.length">
        <UniImage class="img"
          :src="img" :preview="true"
          v-for="(img, index) in item.medicalImg"
          :key="index"
        />
      </view>
      <!-- 空图片 -->
      <view class="img_empty" v-else> 暂无其他照片 </view>
    </view>
  </view>
</template>

<script>
import TITLE from "@/pages/inspect/com/itemTitle.vue";

export default {
  components: {
    TITLE,
  },
  props: {
    item: {
      type: Object,
      default: () => {},
    },
  },
};
</script>

<style lang="scss" scoped>
.title{
  font-size: 15px !important;
  font-weight: bold;
  line-height: 40px;
  border-bottom: 0.5px solid rgba(217, 217, 217, 1);
}
.desc {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 10rpx 32rpx;
  box-shadow: 0px 1px 4px  rgba(121, 72, 234, 0.15);
  .desc_item {
    min-height: 70rpx;
    padding: 12rpx 0;
    @include flex(left);
    font-size: 12px;
    color: #333;
    box-sizing: border-box;
    color: rgba(102, 102, 102, 1) !important;
    justify-content: space-between;
    .label {
      width: 160rpx;
      flex: none;
    }
  }

  .desc_img {
    padding-bottom: 10rpx;
    border-bottom: 1px solid #ebebeb;

    &:last-child {
      border-bottom: none;
    }

    .label {
      height: 84rpx;
      @include flex(left);
      font-size: 12px;
      color: rgba(102, 102, 102, 1);

      text {
        color: #999999;
      }
    }

    .img_list {
      @include flex(left);
      flex-wrap: wrap;

      .img {
        width: 200rpx;
        height: 200rpx;
        border-radius: 8rpx;
        margin-right: 10rpx;
        margin-bottom: 10rpx;

        &:nth-child(3n) {
          margin-right: 0;
        }
      }
    }

    .img_empty {
      text-align: center;
      line-height: 100rpx;
      font-size: 28rpx;
      color: #666;
    }
  }
}
</style>
