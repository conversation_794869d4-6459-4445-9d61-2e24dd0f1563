"use strict";
const plugins_xeUtils_slice = require("./slice.js");
function after(count, callback, context) {
  var runCount = 0;
  var rests = [];
  return function() {
    var args = arguments;
    runCount++;
    if (runCount <= count) {
      rests.push(args[0]);
    }
    if (runCount >= count) {
      callback.apply(context, [rests].concat(plugins_xeUtils_slice.slice(args)));
    }
  };
}
exports.after = after;
