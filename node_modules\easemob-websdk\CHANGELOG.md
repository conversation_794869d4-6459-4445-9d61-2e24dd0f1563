# Changes to easemob chat

## 4.15.1 (June 6, 2025)

### 新增特性

1. 获取加入的群组数量

自 WEB SDK 4.15.1 开始，增加 `getJoinedGroupCount`方法，用来获取加入的群组数量。

2. 屏蔽群消息

自 WEB SDK 4.15.1 开始，增加 `blockGroupMessage`方法，用来屏蔽群组消息，增加`unblockGroupMessage`方法解除屏蔽。

### 修复

1. 修复撤回消息时报错。

## 4.15.0 (May 20, 2025)

### 新增特性

1. 获取群成员

自 WEB SDK 4.15.0 开始，增加`getGroupMembers`方法，用来获取群成员， 同时返回成员角色， 原`listGroupMembers`方法废弃。

2. 获取聊天室成员

自 WEB SDK 4.15.0 开始，增加`getChatRoomMembers`方法，用来获取聊天室成员， 同时返回成员角色， 原`listChatRoomMembers`方法废弃。

3. 当群组发生以下操作时，SDK 改为仅回调单条事件，事件中包含所有加入/退出的成员：

-   创建群组时拉多人入群
-   批量成员加入或退出群组
-   因解散群组导致用户退出
    调整前：SDK 会为每个加入/退出的成员单独回调一条事件。

自 WEB SDK 4.15.0 开始，群组事件增加 `membersPresence`, `membersAbsence` 事件，表示多人加入群组、退出群组。

4. 修改 token 将要过期事件 `onTokenWillExpire` 的触发时机，SDK 会在 Token 有效期达到 80% 时（之前版本为 50% ）回调即将过期通知。

## 4.14.0 (Apr 18, 2025)

### 新增特性

-   支持 [GIF 图片消息]。
-   支持 [群组头像功能]。
-   支持 [附件鉴权功能]。该功能需要联系商务开通，开通后必须调用 SDK 的 API 才能下载消息附件。
-   支持 [支持自定义设备平台]。
-   unipush 推送安卓平台支持 Google FCM

## 4.13.0 (Mar 12, 2025)

### 新增特性

1. `modifyMessage` 发送后修改消息接口新增可修改的消息类型：
   支持修改的消息类型如下：
    - 文本消息：支持修改 `msg` 和 `ext` 字段。
    - 自定义消息：支持修改 `customEvent` 、 `customExts` 和 `ext` 字段。
    - 图片/语音/视频/文件/位置/合并消息：仅支持修改 `ext` 字段。
    - 命令消息：不支持修改。
2. 小程序 SDK 支持运行到微信小游戏平台

### 优化

1. SDK 内部捕获重试 DNS 失败的错误

## 4.12.0 (Jan 9, 2025)

### 新增特性

1. `onModifiedMessage` 事件回调中增加 ext 字段。
2. 加入聊天室 `joinChatRoom` 成功回调新增当前聊天室信息。

### 修复

1. 格式化小程序 SDK API 请求失败错误码， 和 Web 端统一。
2. 修复 uniapp sdk 在 4.36 版本运行到鸿蒙平台，无限重连的问题。
3. 修复偶现无法拉取消息的问题。

### 优化

1. 优化重连时间间隔
2. 小程序 SDK 支持 httpDNS, 默认开启

## 4.11.0 (Nov 29, 2024)

### 新增特性

1. 获取历史消息时，消息体内增加 isRead, isDelivery 字段，只支持单聊。
2. 禁言回调中携带禁言到期的时间戳。
3. 去掉 sockjs
4. 群组/聊天室禁言事件增加 userId 字段，表示被禁言的人
5. uniapp sdk 支持鸿蒙系统

### 修复

1. 修复 pin 消息事件 conversationId 不对

## 4.10.0 (Oct 10, 2024)

### 新增特性

1. 聊天室修改公告时回调中增加公告内容， updateAnnouncement 事件中增加 announcement 字段，表示更新的公告
2. 增加错误码 ：
   WEBIM_USER_ALREADY_LOGIN 208 用户已登录
   MESSAGE_SEND_TIMEOUT 512 发送消息超时
3. 增加 onShow 方法， 小程序或 uniapp 在 onShow 生命周期里执行这个方法， 可以优化重连速度

### 优化

登录方法优化， 调整 open 方法执行 open（）.then 时机， 调整为在连接鉴权成功之后触发， 在 open（）.then 中就可以发送消息， 不用再等 onConnected 事件， 同时鉴权失败等登录的错误会在 open.catch 中抛出

# 4.9.2 (Sep 19, 2024)

-   feat: `removeHistoryMessages` support chatRoom.

# 4.9.1 (Sep 5, 2024)

-   feat: support uni-push plugin.
-   chore: fix type errors and debug logs.

# 4.9.0 (August 29, 2024)

-   Add `onOfflineMessageSyncStart` and `onOfflineMessageSyncFinish` event.
-   Add multi device events `setSilentModeForConversation` and `removeSilentModeForConversation`.
-   Fix `ChatroomEvent` and `GroupEvent` missed `reason` field.
-   PIN message support single chat.
-   Add callback error when get DNS failed
-   Remove redundant judgment at reconnecting.
-   chore: fix type errors and debug logs

# 4.8.1 (Jul 15, 2024)

-   Fix uploading logs takes too much time

# 4.8.1-beta (Jul 2, 2024)

-   Fix the mini program is not able to report logs
-   Fix not export types issue

# 4.8.0 (June 28, 2024)

-   `onDisconnected` event add callback params to give the disconnected reason.
-   Adds allow extension field to be carried when the device logs in and passes them to the kicked device.
    -   `setLoginInfoCustomExt` Set the device's login extension field.
    -   `onDisconnected` In a multi-device login scenario, if the current device is kicked offline by the newly logged-in device, the event received by the kicked device will carry the extended information of the new device.
-   Adds `Connection Parameters#isFixedDeviceId` initialization params, default is `true`, use fixed deviceId, precede version SDK is used random deviceId.
-   `destroyChatRoom` supports the chatroom owner to destroy a chatroom.
-   `joinChatRoom` supports passing in extension field and whether to exit other joined chatrooms when joining a chatroom.
-   Adds report SDK log feature.
-   Increase the timeout period for SDK to connect to the server.
-   Fix on uniapp vue3 platform SDK reconnect failed.

# 4.7.0 (April 26, 2024)

-   `recallMessage` methods add custom field `ext` params.
-   Adds `getJoinedChatRooms` method to get the current user has joined chat room list.
-   Adds `logger.setConsoleLogVisibility` method to sets whether to output logs to the console.
-   Fix `allowGroupAck` status error

# 4.6.0 (April 2, 2024)

-   Adds pin message feature.
    -   pinMessage
    -   unpinMessage
    -   getServerPinnedMessages
-   Adds `onMessagePinEvent` callback to handle the message pin event.
-   `onModifiedMessage` support custom message.
-   `getHistoryMessages` support get chat room history message.
-   Support custom chat room ID.
-   Optimize token login failed error code for make the reason more clearly.
-   Fix the message field `onlineState` status error.

# 4.5.1 (Feb 21, 2024)

-   Format the attachment file url.
-   Fixed bug when UniApp run ios platform send receiverList msg

# 4.5.0 (Jan 30, 2024)

-   The member absence and presence events of chat rooms and groups have added a `memberCount` field
-   Adds the `deleteAllMessagesAndConversations` methods, which can clear the chat history of the current user, including messages and conversations
-   Adds the getSelfIdsOnOtherPlatform method, which can get a list of login IDs of the current user on other login devices, and realize sending messages to specified devices
-   Removed unnecessary unique fields from the Web local database
-   Format the customExts field of the most recent custom message in the conversation list
-   Fix the issue of pulling messages repeatedly
-   Fix the issue of abnormal message order in the `onMessage` callback
-   Fix the error of introducing the MiniCore plugin in vite electron
-   Fix the issue of abnormal API request parameters for `updateOwnUserInfo` after introducing the WeChat SDK in H5

# 4.4.0 (Dec 22, 2023)

-   Adds conversation Conversation Mark
    -   addConversationMark
    -   removeConversationMark
    -   getServerConversationsByFilter
-   Adds Attach Slice Upload
-   Adds `onMessage` Event
-   Adds thumbnail to video message

# 4.3.1 (Dec 13, 2023)

-   Adds use replaced message contents
-   Adds MiniCore SDK logger
-   Verify that the `open` parameter user is not defined
-   Types `SendMsgResult` add a message field
-   Fixed the socket not closing when the network is offline on some device

# 4.3.0 (Nov 13, 2023)

-   Adds contact API
    -   setContactRemark
    -   getAllContacts
    -   getContactsWithCursor
-   Adds chatRoom broadcast
-   Adds token expiration callback

# 4.2.1 (SEP 26, 2023)

-   Adds LocalCache API
    -   getLocalConversations
    -   setLocalConversationCustomField
    -   getLocalConversation
    -   removeLocalConversation
    -   clearConversationUnreadCount
-   onGroupEvent `joinPublicGroupDeclined` callback add the applicant userID

# 4.2.0 (Jul 27, 2023)

-   Adds editing message after sent function
-   Adds message merging and forwarding function
-   Fix sending unnecessary delivery messages

# 4.1.7 (Jun 8, 2023)

-   Adds the `pinConversation` method to pin or unpin a conversation.
-   Adds the `getServerPinnedConversations` method to gets the list of pinned conversations from the server with pagination.
-   Adds the `getServerConversations` method to get the sorted list of conversations from the server with pagination.
-   Adds the feature of sending targeted messages in a group or chat room conversation by adding the `receiverList` parameter to the `create` method.
-   Adds the `isLast` parameter to the `getHistoryMessages` method to indicate whether the returned data is that of the last page.
-   Adds the `thumbnailWidth` and `thumbnailHeight` parameters to the `create` method to allow you to set the width and height of the thumbnail when sending an image.
-   Adds the following errors to error codes 2 (WEBIM_CONNCTION_AUTH_ERROR) and 50 (MAX_LIMIT) and to explain the reasons for SDK login failures on the console:
    -   Error code 50: The number of daily active users (DAU) has exceeded the upper limit; the number of online users has exceeds the upper limit; the number of monthly active users (MAU) has exceeded the upper limit.
    -   Error code 2: The token is invalid.
-   Adds success and failure callbacks (Promise) for the execution of the following methods:
    -   addContact: Adds a contact.
    -   deleteContact: Deletes a contact.
    -   acceptContactInvite: Accepts a friend request.
    -   declineContactInvite: Rejects a friend request.
    -   addUsersToBlocklist: Adds a friend to the block list.
    -   removeUserFromBlocklist: Removes a friend from the block list.

## 4.1.5 (April 13, 2023)

-   Adds four parameters, `from`, `msgTypes`, `startTime`, and `endTime` in `searchOptions` in the ` getHistoryMessages` method to allow you to retrieve historical messages from the server by message sender, message type, or period.

Adds The error code 511, MESSAGE_SIZE_LIMIT, to notify you when the size of the message body exceeds the upper limit.

## 4.1.4 (Mar 16, 2023)

-   Add Group Member Attributes feature
-   Fix `getHistoryMessages` catch error failed
-   Optimize the implementation of chat room join and leave to improve performance
-   Add `deliverOnlineOnly` field in the message creation parameters

## 4.1.3 (Feb 20, 2023)

-   `getConversationlist` Getting the conversation list supports paging
-   Group notification event added `op: create`
-   `useOwnUploadFun` Self-uploaded images support the `size` field
-   ios miniapp multi-device login kick failed
-   Alipay mini program reconnection failed
-   Fixes HIM-8557- Important -- getConversationlist api returns missing fields
-   Added interface to check if you are on the chat room mute list
-   Login failures caused by authentication are not retried

## 4.1.2 (Nov 7, 2022)

-   Chat room message priority
-   Added unidirectional delete roaming function
-   Create a group, Modify group information, Support 'ext' fields
-   Optimize the timing of trigger message send failures
-   'muteGroupMembers' api supports blocking multiple users at the same time
-   Fix Attachment message file_length field is invalid

## 4.1.1 (Sep 26, 2022)

-   Fix chat room KV
-   Fix miniCore uploadFile method
-   miniCore support 'isHttpDns' = false

## 4.1.0 (Sep 16, 2022)

-   SDK modular split
-   Chat room KV
-   Add log callback
-   Add in-line comments
-   Fix file upload failure without callback error
-   `getJoinedGroups` supports getting your own roles
-   Fix the compatibility with Internet Explorer
-   Fixed bug where UniApp could not find 'addEventListener' when running on the phone
-   Optimize reconnection logic

## 4.0.9 (July 29, 2022)

-   Add disaster preparedness strategies
-   Optimize protocols to reduce data volume
-   Fixed message delay in some cases
-   Add a resourceId to the group and chat room APIs
-   The getJoinedGroups API allows you to return the number of group members and their roles

## 4.0.8 (Jun 17, 2022)

-   'GetGroupInfo' supports bulk query
-   Add group event:onGroupEvent
-   Add chatroom event:onChatroomEvent
-   Add the limit when sending group messages
-   Invite to join the group callback returns the group name

## 4.0.7 (May 25, 2022)

-   Add chatThread feature
-   Add the API to the session list to parse the last message
-   Modify the implementation of obtaining roaming messages
-   Add the mark of offline message in the message

## 4.0.5 (May 15, 2022)

-   Add reporting API for content auditing
-   Add push setting API to support different push configuration
-   Add the data reporting function
-   The API for getting groups that you have joined supports paging
-   When creating a group, the SDK supports setting the number of group members
-   Add thumbnail address parameter to receive picture message
-   Fixed group chat message cache issues

## 4.0.4 (April 19, 2022)

-   Add presence feature
-   Add translation feature
-   Optimizing miniProgram does not require the isHttpDNS parameter
-   Fixed failure to modify desc when creating a group
-   Fixed SSR compatibility
-   Fixed uni_SDK not running in browser

## 4.0.3 (January 19, 2022)

-   Fixed 'renewtoken' failed to replace the token
-   Add 'downloadGroupSharedFile' api
-   'fetchGroupSharedFileList' supports paging

## 4.0.2 (January 14, 2022)

-   Add delete session api
-   Add field 'buildingName' to the location message
-   Add restrictions on messages sent by non-friends
-   Add the error event of sending failure due to global mute
-   Support DD mini program
-   Fixed alipay mini program login problem
-   Fixed missing onChannelMessage callback
-   Fixed some known bugs

## 4.0.1 (December 10, 2021)

-   4.0.1 version init
