"use strict";
const plugins_xeUtils_staticStrFirst = require("./staticStrFirst.js");
const plugins_xeUtils_staticStrLast = require("./staticStrLast.js");
const plugins_xeUtils_staticParseInt = require("./staticParseInt.js");
const plugins_xeUtils_helperGetDateFullYear = require("./helperGetDateFullYear.js");
const plugins_xeUtils_helperGetDateMonth = require("./helperGetDateMonth.js");
const plugins_xeUtils_helperGetDateTime = require("./helperGetDateTime.js");
const plugins_xeUtils_toStringDate = require("./toStringDate.js");
const plugins_xeUtils_isValidDate = require("./isValidDate.js");
function getWhatDay(date, offset, mode) {
  date = plugins_xeUtils_toStringDate.toStringDate(date);
  if (plugins_xeUtils_isValidDate.isValidDate(date) && !isNaN(offset)) {
    date.setDate(date.getDate() + plugins_xeUtils_staticParseInt.staticParseInt(offset));
    if (mode === plugins_xeUtils_staticStrFirst.staticStrFirst) {
      return new Date(plugins_xeUtils_helperGetDateFullYear.helperGetDateFullYear(date), plugins_xeUtils_helperGetDateMonth.helperGetDateMonth(date), date.getDate());
    } else if (mode === plugins_xeUtils_staticStrLast.staticStrLast) {
      return new Date(plugins_xeUtils_helperGetDateTime.helperGetDateTime(getWhatDay(date, 1, plugins_xeUtils_staticStrFirst.staticStrFirst)) - 1);
    }
  }
  return date;
}
exports.getWhatDay = getWhatDay;
