<template>
	<view class="container">

		<view class="list">

			<view class="set_box" v-for="(item,index) in talk" :key="index" @click="openDetail(item)">

				<view class="set_time">
					<view>{{item.date}}</view>
				</view>
				<!-- 步骤条 -->
				<view class="set-1">
					<view class="set-2">
						<view class="tlak_o">
							<view class="docter like_">
								<view class="doc_say like_o">
									<view class="doc_head like_h">
										<text class="docName name_">{{item.deptName}} {{item.docName}}</text>
										<view class="status" v-if="item.jzStatus == '0'">
											待签到
										</view>
										<view class="status" v-if="item.jzStatus == '1'">
											待接诊
										</view>
										<view style="color: #FF5050;" class="status" v-if="item.jzStatus == '2'">
											接诊中
										</view>
										<view style="color: #333333;" class="status" v-if="item.jzStatus == '3'">
											已结束
										</view>
										<view class="status" v-if="item.jzStatus == '4'">
											已退诊
										</view>
										<view class="status" v-if="item.jzStatus == '5'">
											已失效
										</view>
									</view>
									<view class="doc_talk like_talk">
										<view>主诉：{{item.recordsTitle}}</view>
										<view>诊断：{{item.diagName}}</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 年份 -->
				<view class="year" v-if="index <= talk.length - 2 && item.year != talk[index + 1].year">
					<!-- <view class="text">{{talk[index + 1].year}} </view> -->
					<view class="text">{{item.year}} </view>
				</view>

			</view>
		</view>

	</view>
</template>

<script>
	export default {
		props: {
			talk: {
				default: []
			},
			pageParam: {
				default: {}
			}
		},
		data() {
			return {

			}
		},
		methods: {
			openDetail(item) {
				uni.navigateTo({
					url: '/pages/patientRecord/medicalDetail?docId=' + item.docId + "&regId=" + item.regId +
						"&patientId=" + item.patientId + "&jzStatus=" + item.jzStatus,
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		margin: 24rpx 32rpx;
		padding-bottom: 30upx;
		border-radius: 8upx;
		overflow: hidden;

		.list {
			background-color: #fff;
			padding-bottom: 10upx;
		}

		.set_box:last-child {
			.set-2::after {
				display: none;
			}
		}
	}

	.set_box {
		width: 100%;
		margin-bottom: 30upx;
		display: flex;
		flex-wrap: wrap;
		padding: 10upx 0;


		.year {
			width: 18%;
			height: 30upx;
			font-size: 32upx;
			@include flex;
			position: relative;
			top: 20upx;

			&::after {
				content: '';
				width: 20upx;
				height: 20upx;
				border-radius: 10upx;
				@include bg_theme;
				position: absolute;
				right: -20upx;
				top: 5upx;
			}

			.text {
				@include bg_theme;
				padding: 2upx 6upx;
				color: #fff;
				border-radius: 8upx;
			}
		}
	}

	.status {
		position: absolute;
		right: 32rpx;
		font-size: 28rpx;
		@include font_theme;
		@include flex;
		line-height: 36rpx;
		top: 6rpx;
	}

	/* 左侧时间 */
	.set_time {
		width: 18%;
		position: relative;
		@include flex;
		flex-direction: column;

		view {
			color: #333333;
			font-size: 28rpx;
			line-height: 40rpx;
			height: 40rpx;
			padding: 0 10rpx;
			background: #F5F5F5;
		}

	}


	/* 右侧内容 */


	.set-1 {
		flex: none;
		width: 82%;
		padding-left: 20px;
		box-sizing: border-box;
	}

	.set-2 {
		min-height: 100rpx;
		border-radius: 10upx;
		position: relative;

		.tlak_o {
			width: 100%;
			min-height: 100rpx;
			position: relative;

			.like_ {
				width: 100%;
				min-height: 100rpx;

				.like_o {
					width: 100%;
					height: 100%;

					.like_h {
						color: #999999;
						font-weight: 600;

						.name_ {
							margin-right: 20rpx;
							font-size: 28rpx;
							color: #333333;
							line-height: 40rpx;
						}

						.time_ {
							font-size: 24rpx;
						}
					}

					.like_talk {
						width: 95%;

						view {
							color: #999999;
							font-size: 28rpx;
							line-height: 40rpx;
						}

						.like_img {
							width: 100%;
							min-height: 100rpx;
							display: flex;
							justify-content: flex-start;
							flex-wrap: wrap;

							image {
								width: 30%;
								height: 180rpx;
								margin-bottom: 20rpx;
								margin-right: 2%;
							}
						}
					}
				}
			}
		}
	}

	.set-2::after {
		content: '';
		top: 50%;
		left: -30upx;
		/* 定位 距离*/
		border-left: #E5E5E5 1px solid;
		height: 140%;
		position: absolute;
	}

	.set-2::before {
		content: "\e64d";
		color: #FFFFFF;
		border: 1px solid #E5E5E5;
		border-radius: 50%;
		width: 30rpx;
		height: 30rpx;
		z-index: 9;
		position: absolute;
		left: -45upx;
		top: 50%;
		margin-top: -15upx;
		/* 移动到左边 */
		font-weight: bold;
		/* 图标样式在复制的图标文件中复制相对应的图标样式必须存在 */
		font-family: "iconfont" !important;
		/* 图标样式在复制的图标文件中复制相对应的图标样式必须存在 */
		font-size: 0;
		background: #FFFFFF;
		/* 图标大小 */
		font-style: normal;
		/* 图标样式在复制的图标文件中复制相对应的图标样式必须存在 */
		-webkit-font-smoothing: antialiased;
		/* 图标样式在复制的图标文件中复制相对应的图标样式必须存在 */
		-moz-osx-font-smoothing: grayscale;
		/* 图标样式在复制的图标文件中复制相对应的图标样式必须存在 */
	}

	@font-face {
		font-family: "iconfont";
		src: url('//at.alicdn.com/t/font_1337773_f06f5a7las.eot?t=1565581133395');
		/* IE9 */
		src: url('//at.alicdn.com/t/font_1337773_f06f5a7las.eot?t=1565581133395#iefix') format('embedded-opentype'),
			/* IE6-IE8 */
			url('data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAAOIAAsAAAAAB8QAAAM7AAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCDMgqCRIIjATYCJAMUCwwABCAFhG0HZhvNBsiemjwJqJERRCC3tX4YOM4iqNbCnr29uw8wKGBUsUAKqDyCjY5HcEzCALron1wr8wDk8hKBFpIc5yaXfWZJoIBIQkF1KlvVqWuFfZ3c/CBw0p8fcMJJsQY6uIuEMk8188LgXgfHTJceiXlrm0tGajTgaMCz2T6yyf5BXh+Ef9BdxMSIS9cTaKmvYGbzFhRXA1sZ2C0QO7cRAdhadnmkNTSEqufYLM4qNdKx9AJn+M/Hb2OiQVIWwL6rNufIIP1bj29voaZspAiK/byITqLAaEAmdvX6d7KQ+dEstCw2tjQL0FJDkvZald8e//3y+WglCIaW0E/e5R8vERWvuTWY5RYy3/ywXBtKXBEF395EBN++FCQ8V5CWsmEt4CBgHMz1Ad8rde7s2LbD7oUhT3l+eWHbhxwOB+LFrfkVL7I3FIAWdpdWvfHiYt8FltYWdzy1Pz/WfPhQ/elT7efPvUJiVlRsIj8+18X3az7+5Wb48vVrbnvo7OndVe1cPc55k2ebWG7OmMgYX01bT3vJnNlJWtemes0mn3vI/PJl80NQQqVErfcH/Ys5Fy3evrVoyyZNDLSq/cp5EyYsKTbb69bD/+5fmPs/cFwxG9ABoCNdTY+AKrwdi2npHk0Xu4vMH/Q7Wl+4+Yftkv83kMHXTiVutOlsnh68ZYPZit+hmDmRSxVwlDnvxvI8WOq3gz4VCy3xE8Id/d7H2GV2M6Ghi4ak1gcKDYORGTsaSq2Mg0rDTGhplLyTW+mEsRC5BUYadyC0dwpJWw+h0N53ZMb+glJX/6DSPgpoaUN4nLOVobGtZdYuJHAkQ7EbqiVqwMjF7UVrJSKKTmCmitNrETOpUzAiNLxZykMGxPY4wNRBIjnHEDOqh7nOzZBOR6GRUQ2SeKiKc2NiWBju+6JQieqBvbkYRMAhMkjUDVKTUAaYzufsW99fCSEUOgI20rMQr4UwJurloQihwmcg8/SGWT3v8hKTDkQkjsMgjKH0oFzHi+hgnoKM/VtpIBIulGpF1ihRmDcLz9WHnm/Uv0BZNAD40zJSFJGjjEq77dsEg1ahKoF2qbupYvP0KNS2e69WbmucSNZAOpEa5XpUagAAAA==') format('woff2'),
			url('//at.alicdn.com/t/font_1337773_f06f5a7las.woff?t=1565581133395') format('woff'),
			url('//at.alicdn.com/t/font_1337773_f06f5a7las.ttf?t=1565581133395') format('truetype'),
			/* chrome, firefox, opera, Safari, Android, iOS 4.2+ */
			url('//at.alicdn.com/t/font_1337773_f06f5a7las.svg?t=1565581133395#iconfont') format('svg');
		/* iOS 4.1- */
	}

	.iconfont {
		font-family: "iconfont" !important;
		font-size: 16px;
		font-style: normal;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
	}

	.icon-yuandian:before {
		content: "\e64d";
	}

	.icon-dingweiweizhi:before {
		content: "\e619";
	}
</style>
