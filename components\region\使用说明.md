### 组件说明
因为 uniapp 的 picker 组件不支持在 APP 端选择省市区，所以就通过进一步封装 uniapp 的 picker 组件实现了简单的各端（支付宝小程序除外）通用的省市区选择器。
注释：因为 uniapp 的 picker 组件的 mode = multiSelector（多列选择器）模式本身就不支持支付宝小程序，所以此组件也不支持支付宝小程序。

### 省市区数据来源
省市区数据定义在了 json 文件中，路径为 '@/static/biaofun-region/region.json' 

### 插件 props 属性
- disabled：[Boolean] 是否禁用插件；
- placeholder：[String] 没有选择省市区时 placeholder 的值；
- defaultValue：[Array] 初始化时选中的值，例：['山西省', '大同市', '南郊区']

### 插件事件
- change(region)：选择省市区（点击右上角确认）后的回调事件，region 为 Array(Object) 类型，其表示的意思为：[选中的省(Object), 选中的市(Object), 选中的区(Object)]