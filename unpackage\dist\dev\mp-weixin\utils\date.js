"use strict";
const utils_myJsTools = require("./myJsTools.js");
const date = {};
date.setSTime = function(formatMonth) {
  var formatMonth = formatMonth;
  if (formatMonth) {
    formatMonth = new Date(formatMonth);
  } else {
    formatMonth = /* @__PURE__ */ new Date();
  }
  var year = formatMonth.getFullYear();
  var month = formatMonth.getMonth() + 1;
  month = month < 10 ? "0" + month : month;
  return year + "-" + month + "-01 00:00:00";
};
date.setNTime = function(formatMonth) {
  var formatMonth = formatMonth;
  if (formatMonth) {
    formatMonth = new Date(formatMonth);
  } else {
    formatMonth = /* @__PURE__ */ new Date();
  }
  var year = formatMonth.getFullYear();
  var month = formatMonth.getMonth() + 1;
  month = month < 10 ? "0" + month : month;
  formatMonth.setMonth(formatMonth.getMonth() + 1);
  formatMonth.setDate(0);
  var day = formatMonth.getDate();
  return year + "-" + month + "-" + day + " 23:59:59";
};
date.prevMonth = function(formatMonth) {
  var formatMonth = formatMonth;
  if (formatMonth) {
    formatMonth = new Date(formatMonth);
  } else {
    formatMonth = /* @__PURE__ */ new Date();
  }
  formatMonth.setMonth(formatMonth.getMonth() - 1);
  var year = formatMonth.getFullYear();
  var month = formatMonth.getMonth() + 1;
  month = month < 10 ? "0" + month : month;
  var day = formatMonth.getDate();
  day = day < 10 ? "0" + day : day;
  var hour = formatMonth.getHours();
  hour = hour < 10 ? "0" + hour : hour;
  var minute = formatMonth.getMinutes();
  minute = minute < 10 ? "0" + minute : minute;
  var s = formatMonth.getSeconds();
  s = s < 10 ? "0" + s : s;
  return year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + s;
};
date.prevYear = function(formatMonth) {
  var formatMonth = formatMonth;
  if (formatMonth) {
    formatMonth = new Date(formatMonth);
  } else {
    formatMonth = /* @__PURE__ */ new Date();
  }
  formatMonth.setFullYear(formatMonth.getFullYear() - 1);
  var year = formatMonth.getFullYear();
  var month = formatMonth.getMonth() + 1;
  month = month < 10 ? "0" + month : month;
  var day = formatMonth.getDate();
  day = day < 10 ? "0" + day : day;
  var hour = formatMonth.getHours();
  hour = hour < 10 ? "0" + hour : hour;
  var minute = formatMonth.getMinutes();
  minute = minute < 10 ? "0" + minute : minute;
  var s = formatMonth.getSeconds();
  s = s < 10 ? "0" + s : s;
  return year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + s;
};
date.getFormatDate = function(style) {
  var d = /* @__PURE__ */ new Date();
  var year = d.getFullYear();
  var month = d.getMonth() + 1;
  month = month < 10 ? "0" + month : month;
  var day = d.getDate();
  day = day < 10 ? "0" + day : day;
  return year + style + month + style + day;
};
date.getFormatDateOne = function(style = "-") {
  var d = new Date((/* @__PURE__ */ new Date()).getTime() + 1e3 * 60 * 60 * 24);
  var year = d.getFullYear();
  var month = d.getMonth() + 1;
  month = month < 10 ? "0" + month : month;
  var day = d.getDate();
  day = day < 10 ? "0" + day : day;
  return year + style + month + style + day;
};
date.getFormatTime = function() {
  var d = /* @__PURE__ */ new Date();
  var hour = d.getHours();
  hour = hour < 10 ? "0" + hour : hour;
  var minute = d.getMinutes();
  minute = minute < 10 ? "0" + minute : minute;
  return hour + ":" + minute;
};
date.add0 = function(m) {
  return m < 10 ? "0" + m : m;
};
date.format = function(shijianchuo) {
  var time = new Date(parseInt(shijianchuo));
  var y = time.getFullYear();
  var m = time.getMonth() + 1;
  var d = time.getDate();
  var h = time.getHours();
  var mm = time.getMinutes();
  var s = time.getSeconds();
  return y + "-" + date.add0(m) + "-" + date.add0(d) + " " + date.add0(h) + ":" + date.add0(mm) + ":" + date.add0(s);
};
date.dateDif = function(time, difference) {
  time = new Date(time).getTime() - difference;
  time = date.format(time);
  return time;
};
date.dayDif = function(time, difference) {
  time = new Date(time).getTime() - difference;
  time = date.format(time);
  var timeArr = time.split(" ");
  return timeArr[0].slice(0, 10);
};
date.dayDifAdd = function(time) {
  time = new Date(time).getTime() + 1e3 * 30 * 60;
  time = date.format(time);
  var timeArr = time.split(" ");
  return timeArr[1].slice(0, 5);
};
date.timeDif = function(time, difference) {
  time = new Date(time).getTime() - difference;
  time = date.format(time);
  var timeArr = time.split(" ");
  return timeArr[1].slice(0, 5);
};
date.getNowTime = function(date2) {
  date2 = new Date(date2);
  this.year = date2.getFullYear();
  this.month = date2.getMonth() + 1;
  this.date = date2.getDate();
  this.hour = date2.getHours() < 10 ? "0" + date2.getHours() : date2.getHours();
  this.minute = date2.getMinutes() < 10 ? "0" + date2.getMinutes() : date2.getMinutes();
  this.second = date2.getSeconds() < 10 ? "0" + date2.getSeconds() : date2.getSeconds();
  this.milliSeconds = date2.getMilliseconds();
  var currentTime = this.year + "-" + this.month + "-" + this.date + " " + this.hour + ":" + this.minute + ":" + this.second;
  return currentTime;
};
date.getNowDate = function(date2) {
  date2 = new Date(date2);
  this.year = date2.getFullYear();
  this.month = date2.getMonth() + 1 < 10 ? "0" + (date2.getMonth() + 1) : date2.getMonth() + 1;
  this.date = date2.getDate() < 10 ? "0" + date2.getDate() : date2.getDate();
  this.hour = date2.getHours() < 10 ? "0" + date2.getHours() : date2.getHours();
  this.minute = date2.getMinutes() < 10 ? "0" + date2.getMinutes() : date2.getMinutes();
  this.second = date2.getSeconds() < 10 ? "0" + date2.getSeconds() : date2.getSeconds();
  this.milliSeconds = date2.getMilliseconds();
  var currentTime = this.year + "-" + this.month + "-" + this.date;
  return currentTime;
};
date.dateAdd = function(startDate, num) {
  startDate = new Date(startDate);
  startDate = +startDate - 1e3 * 60 * 60 * (num * 24);
  startDate = new Date(startDate);
  return startDate.getFullYear() + "-" + (startDate.getMonth() + 1) + "-" + startDate.getDate();
};
date.DateDifference = function(faultDate, endTime, configTime) {
  var stime = new Date(faultDate.replace(/-/g, "/")).getTime();
  var etime = new Date(endTime.replace(/-/g, "/")).getTime();
  let config = utils_myJsTools.myJsTools.getItem("global_config");
  if (config) {
    config.inquiry_duration;
  }
  var usedTime = configTime * 3600 * 1e3 - (etime - stime);
  var days = Math.floor(usedTime / (24 * 3600 * 1e3));
  var leave1 = usedTime % (24 * 3600 * 1e3);
  var hours = Math.floor(leave1 / (3600 * 1e3));
  var leave2 = leave1 % (3600 * 1e3);
  var minutes = Math.floor(leave2 / (60 * 1e3));
  var time = {
    days,
    hours,
    minutes,
    usedTime
  };
  return time;
};
date.DateDifferenceMinutes = function(faultDate, endTime) {
  var stime = faultDate;
  var etime = (/* @__PURE__ */ new Date()).getTime();
  var usedTime = etime - stime;
  var minutes = Math.floor(usedTime / (60 * 1e3));
  return minutes;
};
date.DateDifferenceMsgTime = function(faultDate) {
  var stime = faultDate;
  var etime = (/* @__PURE__ */ new Date()).getTime();
  var usedTime = etime - stime;
  var days = Math.floor(usedTime / (24 * 3600 * 1e3));
  var leave1 = usedTime % (24 * 3600 * 1e3);
  var hours = Math.floor(leave1 / (3600 * 1e3));
  var leave2 = leave1 % (3600 * 1e3);
  var minutes = Math.floor(leave2 / (60 * 1e3));
  let times = {
    minutes,
    hours,
    days
  };
  return times;
};
function getMonthWeek(a, b, c) {
  var date2 = new Date(a, parseInt(b) - 1, c), w = date2.getDay(), d = date2.getDate();
  return Math.ceil((d + 6 - w) / 7);
}
date.getWeek = function(dateString) {
  var dateArray = dateString.split("-");
  var day = new Date(dateArray[0], parseInt(dateArray[1] - 1), dateArray[2]);
  return "星期" + "日一二三四五六".charAt(day.getDay());
};
date.getNowDateAndNowWeek = function(time) {
  var timestamp = time;
  var serverDate = new Date(time);
  var sundayTiem = timestamp + (7 - serverDate.getDay()) * 24 * 60 * 60 * 1e3;
  var SundayData = new Date(sundayTiem);
  var tomorrowY = SundayData.getFullYear();
  var tomorrowM = SundayData.getMonth() + 1 < 10 ? "0" + (SundayData.getMonth() + 1) : SundayData.getMonth() + 1;
  var tomorrowD = SundayData.getDate() < 10 ? "0" + SundayData.getDate() : SundayData.getDate();
  var mondayTime = timestamp - (serverDate.getDay() - 1) * 24 * 60 * 60 * 1e3;
  var mondayData = new Date(mondayTime);
  var mondayY = mondayData.getFullYear();
  var mondayM = mondayData.getMonth() + 1 < 10 ? "0" + (mondayData.getMonth() + 1) : mondayData.getMonth() + 1;
  var mondayD = mondayData.getDate() < 10 ? "0" + mondayData.getDate() : mondayData.getDate();
  var nowWeek = getMonthWeek(tomorrowY, tomorrowM, tomorrowD);
  var config = {
    SunDay: tomorrowY + "/" + tomorrowM + "/" + tomorrowD,
    Monday: mondayY + "/" + mondayM + "/" + mondayD,
    nowWeek
  };
  return config;
};
date.getWeekByNow = function() {
  var now = /* @__PURE__ */ new Date();
  var day = now.getDay();
  var weeks = new Array("7", "1", "2", "3", "4", "5", "6");
  var week = weeks[day];
  return week;
};
date.getBetWeen = function() {
  let strdate = (/* @__PURE__ */ new Date()).getTime();
  let enddate = strdate + 14 * 24 * 60 * 60 * 1e3;
  return this.getNowDate(enddate);
};
date.getDateDiff = function(dateTimeStamp) {
  if (dateTimeStamp) {
    dateTimeStamp = new Date(Number(dateTimeStamp)).getTime();
    var minute = 1e3 * 60, hour = minute * 60, day = hour * 24, month = day * 30, year = month * 12, now = (/* @__PURE__ */ new Date()).getTime(), diffValue = now - dateTimeStamp, result = "";
    var yearC = diffValue / year, monthC = diffValue / month, weekC = diffValue / (7 * day), dayC = diffValue / day, hourC = diffValue / hour, minC = diffValue / minute;
    if (yearC >= 1) {
      result = parseInt(yearC) + "年前";
    } else if (monthC >= 1) {
      result = parseInt(monthC) + "月前";
    } else if (weekC >= 1) {
      result = parseInt(weekC) + "周前";
    } else if (dayC >= 1) {
      result = parseInt(dayC) + "天前";
    } else if (hourC >= 1) {
      result = parseInt(hourC) + "小时前";
    } else if (minC >= 1) {
      result = parseInt(minC) + "分钟前";
    } else {
      result = "刚刚";
    }
    return result;
  }
};
exports.date = date;
