import { <PERSON><PERSON><PERSON> } from '../group/group';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '../chatroom/chatroom';
import { Presence } from '../presence/presence';
import { Silent } from '../silent/silent';
import { Thread } from '../thread/thread';
import { Translation } from '../translation/translation';
import { LocalCache } from '../localCache/localCache';
import { Message } from '../message/index';
import { Contact } from '../contact/contact';
import { AsyncResult } from '../types/common';
import { MessagesType } from '../types/message';
import { EventHandlerType } from '../types/eventHandler';
import { MessageBody } from '../types/message';
import { SendMsgResult } from '../types/indexApi';
export type MiniCoreOptions = {
	/** Whether DNS is enabled or not. It is enabled by default. The private cloud should be turned off */
	isHttpDNS?: boolean;
	/** The URL of the specified message server. This command is used when DNS is not enabled. Usually this API is used for specific customers and need to contact account manager to get it. */
	url?: string;
	/** The URL of the specified REST server. This command is used when DNS is not enabled. Usually this API is used for specific customers and need to contact account manager to get it. */
	apiUrl?: string;
	/** Whether to enable the delivery receipt function. - `true`: Enable; - (Default)`false`: Do not enable. */
	delivery?: boolean;
	/** The unique ID of the login device is random by default and can be set by yourself. This parameter is required for multiple terminals and multiple devices. */
	deviceId?: string;
	/** Whether to use your own upload function, for example, when uploading images and files to your server. - `true`: Use your own upload function; - (Default)`false`: Do not use your own upload function. */
	useOwnUploadFun?: boolean;
	/** When the moderation service replaces the content of a message, whether the adjusted message is returned to the sender. - `true`: Return the adjusted message to the sender. - `false`: Return the original message to the sender. */
	useReplacedMessageContents?: boolean;
	/** The maximum number of reconnection. */
	autoReconnectNumMax?: number;
	/** Custom platform, optional values [1-100]. Please refer to the multi device login document for details. */
	customOSPlatform?: number;
	/** Customize device name.  The device name can only be set through this parameter after configuring customOSPlatform. */
	customDeviceName?: string;
} & (
	| {
			/** The unique application key registered on the console. */
			appKey: string;
			appId?: string;
	  }
	| {
			/** The unique application id registered on the console. */
			appId: string;
			appKey?: string;
	  }
);

export interface OpenParams {
	/** The user ID. */
	username: string;
	/** @deprecated The password. It is recommended to use {@link accessToken} params.*/
	password?: string;
	/** Token required to connect to the message service. */
	accessToken?: string;
	/** @deprecated It is recommended to use {@link accessToken} params. */
	agoraToken?: string;
}
export interface NewTokenResult {
	/** Whether success. */
	status: boolean;
	/** The new token. */
	token?: string;
	/** The expire time. */
	expire?: number;
}

export interface LoginResult {
	/** The access token. */
	/** 授权Token。 */
	accessToken: string;
	expireTimestamp?: number;
	duration?: number;
}

export interface DownloadCombineMessagesParams {
	/** The file url. */
	url: string;
	/** The file secret. */
	secret: string;
}

interface BaseUserInfo {
	/** Whether the current user is enabled. - `true`: Yes; - `false`: No.*/
	activated: boolean;
	/** The timestamp when the current user is created. */
	created: number;
	/** The time when the user information is last edited. */
	modified: number;
	/** The display name in the message push notification. */
	nickname: string;
	/** The user type. */
	type: string;
	/** The user ID. */
	username: string;
	/** The user uuid on the chat server. */
	uuid: string;
}
declare class MiniCore {
	/** The name. */
	name: string;
	/** The URL of the specified message server. This command is used when DNS is not enabled. Usually this API is used for specific customers and need to contact account manager to get it. */
	url: string;
	/** The URL of the specified REST server. This command is used when DNS is not enabled. Usually this API is used for specific customers and need to contact account manager to get it. */
	apiUrl: string;
	/** @hidden */
	orgName: string;
	/** @hidden */
	appName: string;
	/** Whether to enable DNS. - (Default)`true`: Enable; - `false`: Do not enable. */
	isHttpDNS: boolean;
	/** Whether to use HTTPS only. By default, the browser determines whether to use HTTPS only according to the domain name. */
	https: boolean;
	/** Your app key. */
	appKey: string;
	/** Login user token. */
	token: string;
	/** The user id. */
	user: string;
	/** @hidden */
	logOut: boolean;
	/** The heartbeat interval (in seconds). The default value is 30,000s. */
	heartBeatWait: number;
	/** Whether to enable the delivery receipt function. - `true`: Enable; - (Default)`false`: Do not enable. */
	delivery?: boolean;
	/** The unique web device ID. By default, it is a random number. */
	deviceId?: string;
	/** The SDK version. */
	version: string;
	context: {
		jid: {
			appKey: string;
			clientResource: string;
			domain: string;
			name: string;
		};
		userId: string;
		appKey: string;
		restTokenData: string;
		status: number;
		appName: string;
		orgName: string;
		root: any;
		accessToken: string;
	};
	Message: typeof Message;
	private _queues;
	private _load_msg_cache;
	private dnsArr;
	private restIndex;
	private hostIndex;
	private restHosts;
	private socketHost;
	private max_cache_length;
	private unSendMsgArr;
	private sock;
	private isReport;
	private mSync;
	// eslint-disable-next-line no-undef
	[key: string]: any;
	constructor(options: MiniCoreOptions);
	usePlugin(this: MiniCore, plugin: any, name?: string): any;
	isOpened(): boolean;
	open(params: OpenParams): Promise<LoginResult>;
	send(params: MessageBody): Promise<SendMsgResult>;
	/** Set custom extensions for login information. When a multi-device login is kicked, the custom extension information will be passed to the kicked device. */
	setLoginInfoCustomExt(ext: string): void;
	/** Closes the connection. */
	close(): void;
	registerUser(params: {
		username: string;
		password: string;
		nickname?: string;
	}): Promise<AsyncResult<BaseUserInfo>>;
	/** Updates token. */
	renewToken(token: string): Promise<NewTokenResult>;

	/** Download combined message. */
	downloadAndParseCombineMessage(
		options: DownloadCombineMessagesParams
	): Promise<MessagesType[]>;
	/**
	 * Adds listening events.
	 *
	 * ```typescript
	 * connection.addEventHandler('customId', {
	 * 	onTextMessage: (message) => {
	 * 		console.log(message)
	 * 	}
	 * })
	 * ```
	 */
	addEventHandler(id: string, handler: EventHandlerType): void;

	private clearTokenTimeout;
	private tokenExpireTimeCountDown;
	private stopHeartBeat;
	private setContext;
	private getDNS;
	private setRestUrl;
	private setSocketUrl;
	private online;
	private offline;
	private reconnect;
	private clear;
	contact: Contact;
	group: GroupApi;
	chatroom: ChatroomApi;
	presence: Presence;
	silent: Silent;
	thread: Thread;
	translation: Translation;
	localCache: LocalCache;
}
declare const miniCore: typeof MiniCore;
export type { MiniCore };
export default miniCore;
