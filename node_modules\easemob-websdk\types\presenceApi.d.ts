/**
 * presence types
 * @module TYPES
 */
interface SubscribePresence {
	/** The expiration time of the presence subscription. */
	expiry: number;
	/** The presence extension information. */
	ext: string;
	/** The presence update time, which is generated by the server. */
	last_time: number;
	/** The details of the current presence state. */
	status: object;
	/** The user ID of the presence publisher. */
	uid: string;
}
interface SubscribePresenceResult {
	/** The subscribe result. */
	result: SubscribePresence[];
}
interface SubscribeUserListType {
	/** The user ID of the presence publisher. */
	uid: string;
	/** The expiration time of the presence subscription. */
	expiry: number;
}
interface GetSubscribedType {
	/** subscribe users list */
	sublist: SubscribeUserListType[];
	/** The total number of subscribe users  */
	totalnum: number;
}
interface GetSubscribedPresenceListResult {
	/** Fetch all subscribe result. */
	result: GetSubscribedType;
}
interface StatusDetailsType {
	device: string;
	status: number;
}
interface PresenceType {
	/** The user ID of the presence publisher. */
	userId: string;
	/** The details of the current presence state. */
	statusDetails: StatusDetailsType[];
	/** The presence extension information. */
	ext: string;
	/** The presence update time, which is generated by the server. */
	lastTime: number;
	/** The expiration time of the presence subscription. */
	expire: number;
}
export type {
	SubscribePresence,
	SubscribePresenceResult,
	SubscribeUserListType,
	GetSubscribedType,
	GetSubscribedPresenceListResult,
	PresenceType,
	StatusDetailsType,
};
