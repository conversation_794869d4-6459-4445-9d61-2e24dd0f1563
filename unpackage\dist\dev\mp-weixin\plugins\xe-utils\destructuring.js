"use strict";
const plugins_xeUtils_keys = require("./keys.js");
const plugins_xeUtils_slice = require("./slice.js");
const plugins_xeUtils_includes = require("./includes.js");
const plugins_xeUtils_arrayEach = require("./arrayEach.js");
const plugins_xeUtils_assign = require("./assign.js");
function destructuring(destination, sources) {
  if (destination && sources) {
    var rest = plugins_xeUtils_assign.assign.apply(this, [{}].concat(plugins_xeUtils_slice.slice(arguments, 1)));
    var restKeys = plugins_xeUtils_keys.keys(rest);
    plugins_xeUtils_arrayEach.arrayEach(plugins_xeUtils_keys.keys(destination), function(key) {
      if (plugins_xeUtils_includes.includes(restKeys, key)) {
        destination[key] = rest[key];
      }
    });
  }
  return destination;
}
exports.destructuring = destructuring;
