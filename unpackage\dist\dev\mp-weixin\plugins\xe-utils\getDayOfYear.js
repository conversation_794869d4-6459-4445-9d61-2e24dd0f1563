"use strict";
const plugins_xeUtils_getWhatYear = require("./getWhatYear.js");
const plugins_xeUtils_toStringDate = require("./toStringDate.js");
const plugins_xeUtils_isValidDate = require("./isValidDate.js");
const plugins_xeUtils_isLeapYear = require("./isLeapYear.js");
function getDayOfYear(date, year) {
  date = plugins_xeUtils_toStringDate.toStringDate(date);
  if (plugins_xeUtils_isValidDate.isValidDate(date)) {
    return plugins_xeUtils_isLeapYear.isLeapYear(plugins_xeUtils_getWhatYear.getWhatYear(date, year)) ? 366 : 365;
  }
  return NaN;
}
exports.getDayOfYear = getDayOfYear;
