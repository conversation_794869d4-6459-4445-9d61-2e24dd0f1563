"use strict";
const plugins_xeUtils_helperGetDateTime = require("./helperGetDateTime.js");
const plugins_xeUtils_now = require("./now.js");
const plugins_xeUtils_toStringDate = require("./toStringDate.js");
const plugins_xeUtils_isDate = require("./isDate.js");
var timestamp = function(str, format) {
  if (str) {
    var date = plugins_xeUtils_toStringDate.toStringDate(str, format);
    return plugins_xeUtils_isDate.isDate(date) ? plugins_xeUtils_helperGetDateTime.helperGetDateTime(date) : date;
  }
  return plugins_xeUtils_now.now();
};
exports.timestamp = timestamp;
