/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*
	字体颜色相关
*/
/*
	背景色 分割线色
*/
/*
	主题色
*/
/*
		渐变可调角度
*/
/*
	定义弹性布局
 */
/*
	单行文本溢出隐藏
*/
.wrapper.data-v-bbdbdb82 {
  position: fixed;
  width: 100%;
  height: 100vh;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}
.wrapper .block.data-v-bbdbdb82 {
  width: 592rpx;
  min-height: 352rpx;
  box-sizing: border-box;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.wrapper .block .title.data-v-bbdbdb82 {
  width: 100%;
  flex: 1;
  color: #333;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: normal;
  text-align: center;
}
.btn_footer.data-v-bbdbdb82 {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.btn_footer view.data-v-bbdbdb82 {
  width: 242rpx;
  height: 68rpx;
  border-radius: 46rpx;
  color: #ffffff;
  font-weight: 600;
  font-size: 28rpx;
  text-align: center;
  line-height: 68rpx;
  box-sizing: border-box;
}
.btn_footer .cancel.data-v-bbdbdb82 {
  border: 1px solid #836aff;
  color: #836aff;
  background: transparent;
  margin-right: 36rpx;
}
[data-theme=nx] .btn_footer .cancel.data-v-bbdbdb82 {
  border: 1px solid #107dff;
}
[data-theme=test] .btn_footer .cancel.data-v-bbdbdb82 {
  border: 1px solid #2db99d;
}
[data-theme=nx] .btn_footer .cancel.data-v-bbdbdb82 {
  color: #107dff;
}
[data-theme=test] .btn_footer .cancel.data-v-bbdbdb82 {
  color: #2db99d;
}
.btn_footer .confirm.data-v-bbdbdb82 {
  background-color: #836aff;
}
[data-theme=nx] .btn_footer .confirm.data-v-bbdbdb82 {
  background-color: #107dff;
}
[data-theme=test] .btn_footer .confirm.data-v-bbdbdb82 {
  background-color: #2db99d;
}
.forbid.data-v-bbdbdb82 {
  background: #888888 !important;
}