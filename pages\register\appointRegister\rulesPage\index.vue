<template>
  <!-- 服务说明 -->
  <view class="page-container">
    <!-- 头部 -->
    <Docter :infoDetail="appointInfoDetail" />

    <!-- 主体内容 -->
    <view class="bg_wh">
      <!-- 问诊信息 -->
      <view class="patient_box">
        <view class="person_info">
          <text>就诊人</text>
          <text>{{ patientInfo.patientName }}</text>
        </view>
        <view class="person_info">
          <text>问诊类型</text>
          <text>{{ appointInfoDetail.visitTypeName }}</text>
        </view>
        <view class="person_info">
          <text>服务类型</text>
          <text>{{
            appointInfoDetail.consultationCode == 0 ? "咨询" : "复诊"
          }}</text>
        </view>
      </view>

      <!-- 图片 -->
      <view class="img_box">
        <template v-for="item in msgList">
          <image :src="item.url"></image>
        </template>
      </view>

      <!-- 问诊说明 -->
      <view
        class="content_font"
        v-if="appointInfoDetail.consultationCode == 1"
        style="color: #ff0000"
      >
        您选择的是复诊服务，请确定您曾与此医生以面诊方式首诊或在下一步上传过往病例照片。医生会根据您的病例信息与所上传资料确定您是否符合在线复诊条件。若医生不接诊，系统将为您退款。
      </view>
      <view class="content_font" v-else style="color: #ff0000">
        您选择的是咨询服务，只能进行健康、就医或用药方面咨询与建议，医生不能为您开具处方。
      </view>

      <view class="content_font">
        到达预约时间，请及时签到，签到后，医生根据实际情况与您交流，医生将与您通过{{
          appointInfoDetail.visitTypeName
        }}、图片、文字进行交流。单次{{
          appointInfoDetail.visitTypeName
        }}交流为15分钟，超时将自动挂断，您可继续通过图片、文字交流，交流自医生接诊{{
          time
        }}小时内有效。由于医生工作特殊性，接诊可能不及时，请您耐心等待 若医生{{
          hour
        }}小时未回复，系统将为您退款
      </view>
    </view>

    <view class="fiexd">
      <!-- 协议 -->
      <view class="margin_t_60">
        <label @click="checked = !checked">
          <checkbox value="1" :checked="checked" color="#fff" />
        </label>
        同意<text class="blue_click" @click="openProtocol(8)"
          >《互联网诊疗风险告知及知情同意书 》</text
        >
      </view>

      <!-- 确认首诊 -->
      <view class="has_frist" v-if="appointInfoDetail.consultationCode == 1">
        <checkbox
          value="1"
          @click="isHasFrist = !isHasFrist"
          :checked="isHasFrist"
          color="#fff"
        />
        <text @click="isHasFrist = !isHasFrist"
          >我确认已在线下首诊并有就诊病历</text
        >
      </view>
    </view>
    <PROPCENTER
      v-if="ProtocolShow"
      :buttontype="buttontype"
      :buttonCount="count"
      buttonText="阅读并同意 "
      @confirmPropCenter="confirmPropCenter"
    >
      <view class="across_provinces">
        <view class="title">互联网诊疗风险告知及知情同意书</view>
        <!-- 文案 -->
        <view class="info" v-html="content"> </view>
      </view>
    </PROPCENTER>
    <!-- 按钮 -->
    <view class="footer" @click="getNext">
      <view class="btn"> 下一步 </view>
    </view>

    <modelToast v-if="priceToast" :father="this" :type="1">
      医生设置了您的问诊价格为{{ personalPrice }}元,请您知晓
    </modelToast>
  </view>
</template>

<script>
import PROPCENTER from "@/components/propCenter/propCenter.vue";
import Docter from "@/components/doctor_header/doctor_header.vue";
import { getDocPatientBlackList } from "@/api/register";
import modelToast from "@/components/modelToast/modelToast.vue";
import { findPatientByPatientId } from "@/api/user.js";
import {
  findVisitAgreement,
  getPersonalityPrice,
  addProsignagreement,
  savePatientRecords,
  getSysPlatformConfigByKeyList,
} from "@/api/base.js";
import myJsTools from "@/common/js/myJsTools.js";
export default {
  components: {
    Docter,
    modelToast,
    PROPCENTER,
  },
  data() {
    return {
      appointInfoDetail: {},
      docInfoDetail: {},
      patientInfo: {},
      info: {},
      time: "",
      hour: "",
      // 是否有过首诊
      isHasFrist: false,
      userInfo: {},
      priceInfo: {},
      priceToast: false,
      personalPrice: "",
      checked: true,
      isRequired: false,
      content: "",
      count: 5, //阅读倒计时  与const seconds = 5 保持一致否则第一次加载出现null
      timer: null, //时间戳
      buttontype: true, //PROPCENTER组件设置按钮禁用样式
      ProtocolShow: true, //窗口显示隐藏切换
    };
  },
  async onLoad() {
    let appointInfoDetail = uni.getStorageSync("appointInfoDetail") || {};
    console.log(appointInfoDetail)
    // 如果头像存在 但不是完整地址
    if (
      appointInfoDetail.docImg &&
      appointInfoDetail.docImg.indexOf("http") == -1
    ) {
      myJsTools.downAndSaveImg(appointInfoDetail.docImg, (url) => {
        appointInfoDetail.docImg = url;
      });
    }
    if (appointInfoDetail.consultationCode == 0) {
      this.isHasFrist = true;
    }
    this.appointInfoDetail = appointInfoDetail;
    let consultationCode = appointInfoDetail.consultationCode;
    let visitTypeCode = appointInfoDetail.visitTypeCode;
    let msgList = [];
    if (visitTypeCode == "2") {
      if (consultationCode == "0") {
        // 咨询
        msgList = [
          {
            url: "/static/images/appoint/audio_base.png",
          },
          {
            url: "/static/images/appoint/base.png",
          },
        ];
      } else {
        // 复诊
        msgList = [
          {
            url: "/static/images/appoint/audio_base.png",
          },
          {
            url: "/static/images/appoint/base.png",
          },
          {
            url: "/static/images/appoint/medical_doc.png",
          },
          {
            url: "/static/images/appoint/medical.png",
          },
        ];
      }
    } else if (visitTypeCode == "3") {
    } else {
      if (consultationCode == "0") {
        // 咨询
        msgList = [
          {
            url: "/static/images/appoint/video_base.png",
          },
          {
            url: "/static/images/appoint/base.png",
          },
        ];
      } else {
        // 复诊
        msgList = [
          {
            url: "/static/images/appoint/video_base.png",
          },
          {
            url: "/static/images/appoint/base.png",
          },
          {
            url: "/static/images/appoint/medical_doc.png",
          },
          {
            url: "/static/images/appoint/medical.png",
          },
        ];
      }
    }

    this.msgList = msgList;

    let res = await findPatientByPatientId({
      patientId: uni.getStorageSync("patientId"),
    });
    let patientInfo = res.data;
    uni.setStorageSync("patientInfo", patientInfo);
    this.patientInfo = patientInfo;

    let wholeArg = uni.getStorageSync("wholeArg");
    wholeArg.map((item) => {
      if (item.configKey == "js_no_receive_duration") {
        this.time = item.configValue;
      }
      if (item.configKey == "ss_no_receive_duration") {
        this.hour = item.configValue;
      }
    });

    // this.getVisitAgreement();
    this.getPersonalityPriceFun();
    this.modalProtocol();
  },
  methods: {
    // 查询配置
    async getConfig() {
      let { data } = await getSysPlatformConfigByKeyList([
        "patient_condition_param_must",
      ]);
      if (data && data.length) {
        let item = data[0];
        if (item.configValue && item.configValue == 1) {
          this.isRequired = true;
        }
      }
    },
    //查看协议
    openProtocol(type) {
      uni.navigateTo({
        url: "/pages/protocol/index?type=" + type,
      });
    },
    //弹窗协议
    async modalProtocol() {
      let { data } = await findVisitAgreement({
        agreementType: "8",
      });
      this.content = data.agreementContent;
      // 按钮读秒定时器
      const seconds = 5;
      if (!this.timer) {
        this.count = seconds;
        this.timer = setInterval(() => {
          if (this.count > 1 && this.count <= seconds) {
            this.count--;
          } else {
            this.buttontype = false;
            clearInterval(this.timer);
            this.timer = null;
          }
        }, 1000);
      }
    },
    confirmPropCenter() {
      if (this.buttontype) return;
      this.ProtocolShow = false;
    },
    // 获取用户协议,价格与服务说明信息
    async getVisitAgreement() {
      uni.showLoading({
        mask: true,
      });

      try {
        let res = await findVisitAgreement({
          agreementType: "1",
        });
        this.userInfo = res.data;
        uni.hideLoading();
      } catch (error) {
        uni.hideLoading();
      }
    },
    // 获取自定义个性价格
    async getPersonalityPriceFun() {
      console.log('ssssssssssss')
      let appointInfoDetail = this.appointInfoDetail;
      let { consultationCode, docId, visitTypeCode, visitPrice } =
        appointInfoDetail;
      let para = {
        consultationCode,
        docId,
        visitTypeCode,
        patientId: uni.getStorageSync("patientId"),
      };
      let res = await getPersonalityPrice(para);
      if (res.data && (res.data.personalityPrice || res.data.personalityPrice == 0)) {
        let personalPrice = res.data.personalityPrice;
        let price = visitPrice;
        if (personalPrice != price) {
          appointInfoDetail.visitPrice = personalPrice;
          this.appointInfoDetail = appointInfoDetail;
          this.personalPrice = personalPrice;
          this.priceToast = true;
          uni.setStorageSync("appointInfoDetail", appointInfoDetail);
        }
      }
    },
    // 点击自定义个性价格弹框按钮
    modeConfirm() {
      this.priceToast = false;
    },
    // 下一步
    async getNext() {
      if (this.checked) {
        if (!this.isHasFrist) {
          uni.showToast({
            title: "请确认是否已在线下首诊",
            icon: "none",
          });
          return;
        }

        uni.showLoading({
          title: "加载中",
        });
        try {
          const { docId } = this.appointInfoDetail;
          await getDocPatientBlackList({
            patientId: uni.getStorageSync("patientId"),
            docId,
          });
          let res = await findVisitAgreement({ agreementType: "1" });
          uni.hideLoading();
          if (!res.data) return;
          let {
            agreementId,
            agreementName: agreementNama,
            agreementVersions,
          } = res.data;
          let list = {
            agreementId,
            agreementNama,
            agreementVersions,
            patientId: uni.getStorageSync("patientId"),
          };
          res = await addProsignagreement(list);
          let appointInfoDetail = uni.getStorageSync("appointInfoDetail");
          appointInfoDetail.psaId = res.data.uuid;

          await this.getConfig();

          // 获取prid
          let prId = await this.getPrid();
          appointInfoDetail.prId = prId;
          uni.setStorageSync("appointInfoDetail", appointInfoDetail);

          if (this.isRequired) {
            uni.redirectTo({
              url:
                "/pages/register/appointRegister/diseaseDetail/index?prId=" +
                prId,
            });
          } else {
            uni.redirectTo({
              url:
                "/pages/register/appointRegister/orderDetail/index?isShowDisease=1&prId=" +
                prId,
            });
          }
        } catch (error) {
          uni.hideLoading();
        }
      } else {
        uni.showToast({
          title: "请勾选协议",
          icon: "none",
        });
      }
    },
    // 生成空prid
    async getPrid() {
      let obj = {
        patientId: this.patientInfo.patientId,
        patientName: this.patientInfo.patientName,
      };
      let { data } = await savePatientRecords(obj);
      uni.setStorageSync("prId", data.prId);
      return data.prId;
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  box-sizing: border-box;
  padding-bottom: 108rpx;
}

// 多选缩放
::v-deep .uni-checkbox-input {
  transform: scale(0.7);
}

// 选中后背景颜色
::v-deep .uni-checkbox-input-checked {
  @include bg_theme;
}

.fiexd {
  width: 100%;
  position: sticky;
  bottom: 108rpx;
  background: #fbfbfb;
  z-index: 1;
  padding: 32rpx 0;
}

/* 主体内容 */
.bg_wh {
  padding: 0rpx 32rpx;
}

.padding_24 {
  padding: 0 24rpx;
}

/* 问诊信息 */
.patient_box {
  margin-top: 40rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: 0 32rpx;
}

.person_info {
  height: 92rpx;
  border-bottom: 1px solid #ebebeb;
  box-sizing: border-box;
  color: #333333;
  font-size: 28rpx;
  display: flex;
  align-items: center;
}

.person_info text:first-child {
  width: 180rpx;
}

.person_info:last-child {
  border-bottom: none;
}

/* 图片 */
.img_box {
  @include flex(left);
  margin-top: 24rpx;
  padding: 0 20rpx;
}

.img_box image {
  width: 174rpx;
  height: 232rpx;
  vertical-align: top;
}

/* 问诊说明 */
.content_font {
  padding: 24rpx 40rpx;
  color: #333333;
  font-size: 28rpx;
  line-height: 40rpx;
}

/* 协议信息 */
.margin_t_60 {
  font-size: 28rpx;
  @include flex;

  .blue_click {
    @include font_theme;
    padding: 0 10rpx;
  }

  ::v-deepuni-checkbox .uni-checkbox-input.uni-checkbox-input-checked {
    color: #fff;
    @include bg_theme;
    @include border_theme;
  }
}

/* 底部按钮 */
.footer {
  width: 100%;
  height: 108rpx;
  padding: 0 32upx;
  position: fixed;
  bottom: 0;
  left: 0;
  @include flex;
  background-color: #fff;
  border-radius: 16upx 16upx 0 0;
  box-sizing: border-box;

  .btn {
    width: 100%;
    height: 84rpx;
    border-radius: 42upx;
    @include bg_theme;
    font-size: 32upx;
    @include flex;
    color: #fff;
  }
}

.has_frist {
  @include flex;
  font-size: 28rpx;
  margin-top: 20rpx;

  ::v-deepuni-checkbox .uni-checkbox-input.uni-checkbox-input-checked {
    color: #fff;
    @include bg_theme;
    @include border_theme;
  }
}
.across_provinces {
  margin-bottom: 40rpx;
  .title {
    font-size: 28rpx;
  }
  .info {
    height: 70vh;
    overflow: auto;
    padding: 24rpx 10rpx;
    font-size: 28rpx;
    line-height: 42rpx;
    font-weight: normal;
    text-indent: 40rpx;
    text-align: left;
  }
}
</style>
