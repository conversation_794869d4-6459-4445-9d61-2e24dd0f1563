"use strict";
const plugins_xeUtils_eachTree = require("./eachTree.js");
function filterTree(obj, iterate, options, context) {
  var result = [];
  if (obj && iterate) {
    plugins_xeUtils_eachTree.eachTree(obj, function(item, index, items, path, parent, nodes) {
      if (iterate.call(context, item, index, items, path, parent, nodes)) {
        result.push(item);
      }
    }, options);
  }
  return result;
}
exports.filterTree = filterTree;
