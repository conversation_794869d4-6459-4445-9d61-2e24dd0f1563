"use strict";
const plugins_xeUtils_eqNull = require("./eqNull.js");
const plugins_xeUtils_isNumber = require("./isNumber.js");
const plugins_xeUtils_toNumberString = require("./toNumberString.js");
function toValueString(obj) {
  if (plugins_xeUtils_isNumber.isNumber(obj)) {
    return plugins_xeUtils_toNumberString.toNumberString(obj);
  }
  return "" + (plugins_xeUtils_eqNull.eqNull(obj) ? "" : obj);
}
exports.toValueString = toValueString;
