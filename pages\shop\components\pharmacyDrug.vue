<template>
  <view class="warp">
    <view class="pharmacy_drug" v-for="(item, index) in list" :key="index">
      <!-- 药店 -->
      <view
        class="pharmacy_item"
        v-for="(pharmacy, p) in item.drugStoreList"
        :key="p"
      >
        <!-- 药店名称 -->
        <view class="pharmacy_name">
          <text
            >{{ pharmacy.drugStoreName }}
            <block v-if="pharmacy.isProprietary == 1">（自营）</block></text
          >
          <text class="r" v-if="showTelNo" @click="call">联系药店</text>
        </view>
        <!-- 药品列表 -->
        <view class="durg_list">
          <!-- 单个药品 -->
          <view
            class="durg_item"
            v-for="drug in pharmacy.shoppingCartList"
            :key="drug.dsscId"
          >
            <UniImage v-if="drug.drugImg"
              :src="drug.drugImg"
              :data-src="errUrl"
              class="left"
            />
            <image
              v-else
              class="left"
              src="/static/images/Pharmacy-default.png"
            ></image>
            <!-- 内容 -->
            <view class="right">
              <!-- 药品名称 -->
              <view class="drug_name">{{ drug.drugName }}</view>
              <!-- 规格 -->
              <view class="drug_info">规格: {{ drug.gg }}</view>
              <!-- 活动 -->
              <view class="drug_info red" v-if="drug.activeName">
                单品{{ drug.activeName }}</view
              >
              <!-- 价位数量 -->
              <view class="right_menu">
                <view class="price"
                  >￥{{ drug.realyPay }}
                  <text v-if="drug.realyPay != drug.shoudlePay" class="del"
                    >￥{{ drug.shoudlePay }}</text
                  >
                </view>
                <text class="num">x{{ drug.quan }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="durg_count">
        <view class="right">
          <view class="item">
            <text>总价: </text> ￥{{ item.totalMoney | toFixed }}
          </view>
          <view v-if="isScan!==1" class="item">
            <text>优惠: </text> ￥{{ item.disTotalMoney | toFixed }}
          </view>
          <view class="item">
            <text>实付:</text> ￥{{ item.totalRealyMoney | toFixed }}
          </view>
        </view>
      </view>

      <!-- 药品有数量超过 12 -->
      <view class="drug_tip" v-show="isTip && isQuick">
        <text>提示</text>
        <view
          >药店将按照您上传的有效处方，合理审核药品数量，若超过处方用量，药店可能驳回订单，请留意审核结果进行调整。</view
        >
      </view>

      <!-- 物流选择 -->
      <view class="logist" v-if="list.length > 1">
        <view class="sele_but">
          <text @click.stop="showTip"
            >请选择配送方式
            <uni-icons type="help" color="#333" size="14"></uni-icons>
          </text>
        </view>

        <!-- 物流选择 -->
        <view class="logistics">
          <view class="item" @click="setLogist(index, 0)">
            <text class="name">普通快递</text>
            <view class="right">
              <text>￥{{ item.ordinaryLogMoney }}</text>
              <image
                src="/static/shop/sele_act.png"
                v-if="item.sele_delivery == 0"
              ></image>
              <image src="/static/shop/sele.png" v-else></image>
            </view>
          </view>

<!--          <view class="item" @click="setLogist(index, 1)">-->
<!--            <text class="name">顺丰快递</text>-->
<!--            <view class="right">-->
<!--              <text>￥{{ item.specialLogMoney }}</text>-->
<!--              <image-->
<!--                src="/static/shop/sele_act.png"-->
<!--                v-if="item.sele_delivery == 1"-->
<!--              ></image>-->
<!--              <image src="/static/shop/sele.png" v-else></image>-->
<!--            </view>-->
<!--          </view>-->
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'PharmacyList',
  props: {
    list: {
      type: Array,
      default: () => {
        return [];
      },
    },
    // 显示联系药店
    showTelNo: {
      type: Boolean,
      default: false,
    },
    // 药店联系方式
    telNo: {
      type: String,
      default: '',
    },
    // 是否快捷开方
    isQuick: {
      type: Boolean,
      default: true,
    },
    isScan:{
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      isTip: false,
      totalMoney: 0,
      errUrl: require('../../../static/shop/drug.png'),
    };
  },
  watch: {
    list(n, o) {
      if (n.length) this.getNum(n[0].drugStoreList);
    },
  },
  methods: {
    getNum(arr) {
      let isTip = false;
      arr.forEach((k) => {
        k.shoppingCartList.forEach((j) => {
          if (j.quan > 12) isTip = true;
        });
      });
      this.isTip = isTip;
    },
    // 提示
    showTip() {
      this.$emit('showTip', 'wl');
    },
    // 设置物流方式
    setLogist(index, n) {
      // 下标 方式
      this.$emit('logist', index, n);
    },
    // 拨打电话
    call() {
      uni.makePhoneCall({
        phoneNumber: this.telNo,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}

.warp {
  .durg_count {
    @include flex(right);
    align-items: flex-start;
    padding: 0 32rpx;
    font-size: 28rpx;
    height: 60rpx;
    margin-top: 20rpx;

    text {
      color: #333;
      font-weight: normal;
      margin-right: 4rpx;
      font-size: 22rpx;
    }

    .right {
      @include flex;
      color: #ff3b30;
      font-weight: bold;

      .item {
        margin-left: 16rpx;
        font-size: 28rpx;

        &:nth-child(1) {
          margin-left: 0;
        }
      }
    }
  }

  .drug_tip {
    @include flex;
    align-items: flex-start;
    padding: 0 32rpx 24rpx;
    font-size: 24rpx;

    text {
      flex: none;
      word-break: break-all;
      padding-right: 50rpx;
    }

    view {
      color: #999;
    }
  }

  .logist {
    width: 100%;
    background-color: #fff;
    border-radius: 8rpx;
    font-size: 28rpx;
    padding: 0 24rpx;
    margin-bottom: 24rpx;
    border-top: 1px solid #eee;

    .sele_but {
      @include flex(lr);
      height: 88rpx;

      .uni-icons {
        margin-left: 8rpx;
      }

      .right {
        @include flex;

        .name {
          color: #666;
        }
      }
    }

    .logistics {
      width: 100%;
      padding-bottom: 24rpx;

      .item {
        height: 76rpx;
        padding: 0 24rpx;
        @include flex(lr);
        background-color: #f5f5f5;
        border-radius: 8rpx;

        &:last-child {
          margin-top: 24rpx;
        }

        .name {
          font-size: 28rpx;
        }

        .right {
          @include flex;

          text {
            font-size: 28rpx;
            color: red;
            padding-right: 30rpx;
          }

          image {
            width: 32rpx;
            height: 32rpx;
          }
        }
      }
    }
  }
}

.pharmacy_drug {
  background-color: #fff;
  border-radius: 8rpx;
  margin-top: 24rpx;

  .pharmacy_item {
    width: 100%;
    padding: 0 32rpx;
    border-bottom: 1px dashed #eee;

    .pharmacy_name {
      font-size: 28rpx;
      font-weight: bold;
      height: 88rpx;
      @include flex(lr);
      position: relative;
      padding-left: 16rpx;
      border-bottom: 1px solid #eee;

      &::before {
        content: '';
        display: block;
        width: 6rpx;
        height: 28rpx;
        position: absolute;
        left: 0;
        @include bg_theme;
      }

      .r {
        width: 140rpx;
        height: 50rpx;
        @include flex;
        border-radius: 30rpx;
        @include border_theme;
        @include font_theme;
        font-size: 24rpx;
      }
    }
  }
}

.durg_list {
  .durg_item {
    @include flex;
    padding: 16rpx 0;

    &:last-child {
      .right {
        border-bottom: none;
      }
    }

    .left {
      width: 128rpx;
      height: 128rpx;
      margin-right: 24rpx;
      border: 1px solid #eee;
      border-radius: 8rpx;
      flex: none;
    }

    .right {
      flex: 1;
      min-height: 128rpx;
      @include flex(lr);
      flex-direction: column;
      align-items: stretch;
      border-bottom: 1px solid #eee;

      .drug_name {
        flex: 1;
        font-size: 28rpx;
        font-weight: bold;
      }

      .drug_info {
        flex: 1;
        font-size: 24rpx;
        color: #999;

        &.red {
          color: red;
        }
      }

      .right_menu {
        flex: 2;
        @include flex(lr);
        align-items: flex-end;

        .price {
          font-size: 28rpx;
          color: #ff3b30;
          font-weight: bold;

          .del {
            padding-left: 10rpx;
            font-size: 20rpx;
            color: #666;
            text-decoration: line-through;
          }
        }

        .num {
          font-size: 28rpx;
        }
      }
    }
  }
}
</style>
