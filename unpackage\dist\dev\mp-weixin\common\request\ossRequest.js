"use strict";
const common_vendor = require("../vendor.js");
const common_js_pay = require("../js/pay.js");
const store_index = require("../../store/index.js");
let baseUrl = store_index.store.getters.allEnv[store_index.store.getters.envKey].ossUrl;
const http = ({ url = "", param = {}, ...other } = {}) => {
  return new Promise((resolve, reject) => {
    if (other.method == "post") {
      param = reSetData(param);
    }
    common_vendor.index.request({
      url: getUrl(url),
      data: param,
      header: {
        Authorization: common_vendor.index.getStorageSync("proPfInfo").token || "",
        firmId: store_index.store.getters.allEnv[store_index.store.getters.envKey].firmId,
        version: store_index.store.getters.allEnv[store_index.store.getters.envKey].version,
        clientType: store_index.store.getters.allEnv[store_index.store.getters.envKey].clientType
      },
      ...other,
      success: (res) => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          if (res.data.code == 2e4 || res.data.code == 20011) {
            resolve(res.data);
          } else {
            common_js_pay.Toast(res.data.message);
            reject(res);
          }
        } else {
          reject(res);
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
};
const getUrl = (url) => {
  if (url.indexOf("://") == -1) {
    url = baseUrl + url;
  }
  return url;
};
const reSetData = (requestData) => {
  let timestamp = (/* @__PURE__ */ new Date()).getTime() + "";
  let nonce = guid();
  let token_info = common_vendor.index.getStorageSync("proPfInfo").token;
  if (requestData) {
    common_vendor.gBase64.encode(JSON.stringify(requestData));
  } else {
    let map = {};
    common_vendor.gBase64.encode(JSON.stringify(map));
  }
  let data = {
    data: JSON.stringify(requestData)
  };
  data.timestamp = timestamp;
  data.nonce = nonce;
  if (token_info) {
    data.token = token_info;
  }
  let s = "";
  Object.keys(data).sort().forEach((k) => {
    if (data[k] && data[k].length > 0) {
      if (s.length > 0) {
        s += "&";
      }
      s += k + "=" + data[k];
    }
  });
  let md5Data = common_vendor.md5(s).toLocaleUpperCase();
  data.sign = md5Data;
  return data;
};
const guid = () => {
  return S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4();
};
const S4 = () => {
  return ((1 + Math.random()) * 65536 | 0).toString(16).substring(1);
};
exports.http = http;
