"use strict";
const plugins_xeUtils_helperCreateiterateIndexOf = require("./helperCreateiterateIndexOf.js");
var findLastIndexOf = plugins_xeUtils_helperCreateiterateIndexOf.helperCreateiterateIndexOf(function(obj, iterate, context) {
  for (var len = obj.length - 1; len >= 0; len--) {
    if (iterate.call(context, obj[len], len, obj)) {
      return len;
    }
  }
  return -1;
});
exports.findLastIndexOf = findLastIndexOf;
