"use strict";
const plugins_xeUtils_isNumber = require("./isNumber.js");
const plugins_xeUtils_isArray = require("./isArray.js");
const plugins_xeUtils_isString = require("./isString.js");
const plugins_xeUtils_isRegExp = require("./isRegExp.js");
const plugins_xeUtils_isDate = require("./isDate.js");
const plugins_xeUtils_isBoolean = require("./isBoolean.js");
const plugins_xeUtils_isUndefined = require("./isUndefined.js");
const plugins_xeUtils_keys = require("./keys.js");
const plugins_xeUtils_every = require("./every.js");
function helperEqualCompare(val1, val2, compare, func, key, obj1, obj2) {
  if (val1 === val2) {
    return true;
  }
  if (val1 && val2 && !plugins_xeUtils_isNumber.isNumber(val1) && !plugins_xeUtils_isNumber.isNumber(val2) && !plugins_xeUtils_isString.isString(val1) && !plugins_xeUtils_isString.isString(val2)) {
    if (plugins_xeUtils_isRegExp.isRegExp(val1)) {
      return compare("" + val1, "" + val2, key, obj1, obj2);
    }
    if (plugins_xeUtils_isDate.isDate(val1) || plugins_xeUtils_isBoolean.isBoolean(val1)) {
      return compare(+val1, +val2, key, obj1, obj2);
    } else {
      var result, val1Keys, val2Keys;
      var isObj1Arr = plugins_xeUtils_isArray.isArray(val1);
      var isObj2Arr = plugins_xeUtils_isArray.isArray(val2);
      if (isObj1Arr || isObj2Arr ? isObj1Arr && isObj2Arr : val1.constructor === val2.constructor) {
        val1Keys = plugins_xeUtils_keys.keys(val1);
        val2Keys = plugins_xeUtils_keys.keys(val2);
        if (func) {
          result = func(val1, val2, key);
        }
        if (val1Keys.length === val2Keys.length) {
          return plugins_xeUtils_isUndefined.isUndefined(result) ? plugins_xeUtils_every.every(val1Keys, function(key2, index) {
            return key2 === val2Keys[index] && helperEqualCompare(val1[key2], val2[val2Keys[index]], compare, func, isObj1Arr || isObj2Arr ? index : key2, val1, val2);
          }) : !!result;
        }
        return false;
      }
    }
  }
  return compare(val1, val2, key, obj1, obj2);
}
exports.helperEqualCompare = helperEqualCompare;
