"use strict";
const common_vendor = require("../common/vendor.js");
const myJsTools = {};
myJsTools.setItem = function(key, value) {
  try {
    const ms = "mystorage";
    let mydata = common_vendor.index.getStorageSync(ms);
    if (!mydata) {
      mydata = { data: {} };
    } else if (typeof mydata === "string") {
      mydata = JSON.parse(mydata);
    }
    mydata.data[key] = value;
    common_vendor.index.setStorageSync(ms, mydata);
  } catch (e) {
    console.error("存储数据失败:", e);
    common_vendor.index.showToast({
      title: "存储数据失败",
      icon: "none"
    });
  }
};
myJsTools.setItemAsync = function(key, value, success, fail) {
  const ms = "mystorage";
  common_vendor.index.getStorage({
    key: ms,
    success: (res) => {
      let mydata = res.data;
      if (typeof mydata === "string") {
        mydata = JSON.parse(mydata);
      }
      mydata.data[key] = value;
      common_vendor.index.setStorage({
        key: ms,
        data: mydata,
        success,
        fail
      });
    },
    fail: () => {
      const mydata = { data: {} };
      mydata.data[key] = value;
      common_vendor.index.setStorage({
        key: ms,
        data: mydata,
        success,
        fail
      });
    }
  });
};
myJsTools.getItem = function(key) {
  try {
    const ms = "mystorage";
    let mydata = common_vendor.index.getStorageSync(ms);
    if (!mydata) {
      return null;
    }
    if (typeof mydata === "string") {
      mydata = JSON.parse(mydata);
    }
    return mydata.data[key] || null;
  } catch (e) {
    console.error("获取数据失败:", e);
    return null;
  }
};
myJsTools.getItemAsync = function(key, success, fail) {
  const ms = "mystorage";
  common_vendor.index.getStorage({
    key: ms,
    success: (res) => {
      try {
        let mydata = res.data;
        if (typeof mydata === "string") {
          mydata = JSON.parse(mydata);
        }
        const value = mydata.data[key] || null;
        success && success(value);
      } catch (e) {
        console.error("解析数据失败:", e);
        fail && fail(e);
      }
    },
    fail: (err) => {
      console.log("获取存储数据失败，返回默认值:", err);
      success && success(null);
    }
  });
};
myJsTools.removeItem = function(key) {
  try {
    const ms = "mystorage";
    let mydata = common_vendor.index.getStorageSync(ms);
    if (!mydata) {
      return;
    }
    if (typeof mydata === "string") {
      mydata = JSON.parse(mydata);
    }
    delete mydata.data[key];
    common_vendor.index.setStorageSync(ms, mydata);
  } catch (e) {
    console.error("删除数据失败:", e);
  }
};
myJsTools.removeItemAsync = function(key, success, fail) {
  const ms = "mystorage";
  common_vendor.index.getStorage({
    key: ms,
    success: (res) => {
      let mydata = res.data;
      if (typeof mydata === "string") {
        mydata = JSON.parse(mydata);
      }
      delete mydata.data[key];
      common_vendor.index.setStorage({
        key: ms,
        data: mydata,
        success,
        fail
      });
    },
    fail
  });
};
myJsTools.clearItem = function() {
  try {
    const ms = "mystorage";
    common_vendor.index.removeStorageSync(ms);
  } catch (e) {
    console.error("清空数据失败:", e);
  }
};
myJsTools.clearItemAsync = function(success, fail) {
  const ms = "mystorage";
  common_vendor.index.removeStorage({
    key: ms,
    success,
    fail
  });
};
myJsTools.HashMap = function() {
  var length = 0;
  var obj = new Object();
  this.isEmpty = function() {
    return length == 0;
  };
  this.containsKey = function(key) {
    return key in obj;
  };
  this.containsValue = function(value) {
    for (var key in obj) {
      if (obj[key] == value) {
        return true;
      }
    }
    return false;
  };
  this.put = function(key, value) {
    if (!this.containsKey(key)) {
      length++;
    }
    obj[key] = value;
  };
  this.get = function(key) {
    return this.containsKey(key) ? obj[key] : null;
  };
  this.remove = function(key) {
    if (this.containsKey(key) && delete obj[key]) {
      length--;
    }
  };
  this.values = function() {
    var _values = new Array();
    for (var key in obj) {
      _values.push(obj[key]);
    }
    return _values;
  };
  this.keySet = function() {
    var _keys = new Array();
    for (var key in obj) {
      _keys.push(key);
    }
    return _keys;
  };
  this.size = function() {
    return length;
  };
  this.clear = function() {
    length = 0;
    obj = new Object();
  };
};
exports.myJsTools = myJsTools;
