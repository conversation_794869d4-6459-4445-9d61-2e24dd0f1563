<script setup>
import { onLaunch, onShow } from '@dcloudio/uni-app'
import { checkPermission } from '@/utils/auth'

const initApp = async () => {
  // checkPermission()
}
onLaunch((e) => {
  console.log(e, 'onLaunch')
  // initApp()
})

onShow((e) => {

})

// 提供给外部调用的登录成功后的方法
uni.$login = {
  success: async () => {
   
  }
}
</script>

<style lang="scss">

:root {
  --textColor: #337fff;
}

.su-title-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.adaptive-image {
  width: 100%;
  height: auto;
}

.uni-fab {
  bottom: 160rpx !important;
}

.uni-fab__plus {
  bottom: 160rpx !important;
}

:deep(p) {
  text-wrap: wrap !important;
}

:deep(rich-text) {
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
  /* 标准属性 */
  overflow: hidden;
}

view {
  white-space: pre-wrap;
}

text {
  white-space: pre-wrap;
}

:deep(.van-cell) {
  line-height: 20rpx !important;
}
</style>
