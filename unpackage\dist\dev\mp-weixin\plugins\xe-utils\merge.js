"use strict";
const plugins_xeUtils_isArray = require("./isArray.js");
const plugins_xeUtils_isPlainObject = require("./isPlainObject.js");
const plugins_xeUtils_each = require("./each.js");
function handleMerge(target, source) {
  if (plugins_xeUtils_isPlainObject.isPlainObject(target) && plugins_xeUtils_isPlainObject.isPlainObject(source) || plugins_xeUtils_isArray.isArray(target) && plugins_xeUtils_isArray.isArray(source)) {
    plugins_xeUtils_each.each(source, function(obj, key) {
      target[key] = handleMerge(target[key], obj);
    });
    return target;
  }
  return source;
}
var merge = function(target) {
  if (!target) {
    target = {};
  }
  var args = arguments;
  var len = args.length;
  for (var source, index = 1; index < len; index++) {
    source = args[index];
    if (source) {
      handleMerge(target, source);
    }
  }
  return target;
};
exports.merge = merge;
