"use strict";
const plugins_xeUtils_staticStrUndefined = require("./staticStrUndefined.js");
const plugins_xeUtils_staticDocument = require("./staticDocument.js");
const plugins_xeUtils_staticWindow = require("./staticWindow.js");
const plugins_xeUtils_assign = require("./assign.js");
const plugins_xeUtils_arrayEach = require("./arrayEach.js");
function isBrowseStorage(storage) {
  try {
    var testKey = "__xe_t";
    storage.setItem(testKey, 1);
    storage.removeItem(testKey);
    return true;
  } catch (e) {
    return false;
  }
}
function isBrowseType(type) {
  return navigator.userAgent.indexOf(type) > -1;
}
function browse() {
  var $body, isChrome, isEdge;
  var isMobile = false;
  var result = {
    isNode: false,
    isMobile,
    isPC: false,
    isDoc: !!plugins_xeUtils_staticDocument.staticDocument
  };
  if (!plugins_xeUtils_staticWindow.staticWindow && typeof process !== plugins_xeUtils_staticStrUndefined.staticStrUndefined) {
    result.isNode = true;
  } else {
    isEdge = isBrowseType("Edge");
    isChrome = isBrowseType("Chrome");
    isMobile = /(Android|webOS|iPhone|iPad|iPod|SymbianOS|BlackBerry|Windows Phone)/.test(navigator.userAgent);
    if (result.isDoc) {
      $body = plugins_xeUtils_staticDocument.staticDocument.body || plugins_xeUtils_staticDocument.staticDocument.documentElement;
      plugins_xeUtils_arrayEach.arrayEach(["webkit", "khtml", "moz", "ms", "o"], function(core) {
        result["-" + core] = !!$body[core + "MatchesSelector"];
      });
    }
    plugins_xeUtils_assign.assign(result, {
      edge: isEdge,
      firefox: isBrowseType("Firefox"),
      msie: !isEdge && result["-ms"],
      safari: !isChrome && !isEdge && isBrowseType("Safari"),
      isMobile,
      isPC: !isMobile,
      isLocalStorage: isBrowseStorage(plugins_xeUtils_staticWindow.staticWindow.localStorage),
      isSessionStorage: isBrowseStorage(plugins_xeUtils_staticWindow.staticWindow.sessionStorage)
    });
  }
  return result;
}
exports.browse = browse;
