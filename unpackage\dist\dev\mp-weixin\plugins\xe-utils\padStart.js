"use strict";
const plugins_xeUtils_toValueString = require("./toValueString.js");
const plugins_xeUtils_isUndefined = require("./isUndefined.js");
const plugins_xeUtils_helperStringRepeat = require("./helperStringRepeat.js");
function padStart(str, targetLength, padString) {
  var rest = plugins_xeUtils_toValueString.toValueString(str);
  targetLength = targetLength >> 0;
  padString = plugins_xeUtils_isUndefined.isUndefined(padString) ? " " : "" + padString;
  if (rest.padStart) {
    return rest.padStart(targetLength, padString);
  }
  if (targetLength > rest.length) {
    targetLength -= rest.length;
    if (targetLength > padString.length) {
      padString += plugins_xeUtils_helperStringRepeat.helperStringRepeat(padString, targetLength / padString.length);
    }
    return padString.slice(0, targetLength) + rest;
  }
  return rest;
}
exports.padStart = padStart;
