"use strict";
const plugins_xeUtils_staticStrFirst = require("./staticStrFirst.js");
const plugins_xeUtils_staticStrLast = require("./staticStrLast.js");
const plugins_xeUtils_staticDayTime = require("./staticDayTime.js");
const plugins_xeUtils_helperGetDateFullYear = require("./helperGetDateFullYear.js");
const plugins_xeUtils_helperGetDateTime = require("./helperGetDateTime.js");
const plugins_xeUtils_helperGetDateMonth = require("./helperGetDateMonth.js");
const plugins_xeUtils_toStringDate = require("./toStringDate.js");
const plugins_xeUtils_isValidDate = require("./isValidDate.js");
const plugins_xeUtils_isNumber = require("./isNumber.js");
function getWhatMonth(date, offsetMonth, offsetDay) {
  var monthNum = offsetMonth && !isNaN(offsetMonth) ? offsetMonth : 0;
  date = plugins_xeUtils_toStringDate.toStringDate(date);
  if (plugins_xeUtils_isValidDate.isValidDate(date)) {
    if (offsetDay === plugins_xeUtils_staticStrFirst.staticStrFirst) {
      return new Date(plugins_xeUtils_helperGetDateFullYear.helperGetDateFullYear(date), plugins_xeUtils_helperGetDateMonth.helperGetDateMonth(date) + monthNum, 1);
    } else if (offsetDay === plugins_xeUtils_staticStrLast.staticStrLast) {
      return new Date(plugins_xeUtils_helperGetDateTime.helperGetDateTime(getWhatMonth(date, monthNum + 1, plugins_xeUtils_staticStrFirst.staticStrFirst)) - 1);
    } else if (plugins_xeUtils_isNumber.isNumber(offsetDay)) {
      date.setDate(offsetDay);
    }
    if (monthNum) {
      var currDate = date.getDate();
      date.setMonth(plugins_xeUtils_helperGetDateMonth.helperGetDateMonth(date) + monthNum);
      if (currDate !== date.getDate()) {
        date.setDate(1);
        return new Date(plugins_xeUtils_helperGetDateTime.helperGetDateTime(date) - plugins_xeUtils_staticDayTime.staticDayTime);
      }
    }
  }
  return date;
}
exports.getWhatMonth = getWhatMonth;
