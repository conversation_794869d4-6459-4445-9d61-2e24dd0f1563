"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  props: {
    item: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    return {
      errUrl: require("../../../static/images/docHead.png"),
      isShowWorkHosName: common_vendor.index.getStorageSync("isShowWorkHosName") || false
    };
  },
  methods: {
    // 去医生主页
    toDoc(item) {
      common_vendor.index.setStorageSync("hosId", item.hosId);
      common_vendor.index.navigateTo({
        url: "/pages/register/docHomePage/index?docId=" + item.docId
      });
    }
  }
};
if (!Array) {
  const _component_UniImage = common_vendor.resolveComponent("UniImage");
  _component_UniImage();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.item.docImg
  }, $props.item.docImg ? {
    b: common_vendor.n($props.item.isOnline == 0 ? "docImg_box_unOnline" : "docImg_box_Online"),
    c: common_vendor.p({
      src: $props.item.docImg,
      ["default-src"]: $data.errUrl,
      ["error-src"]: $data.errUrl
    })
  } : {
    d: common_assets._imports_0$5,
    e: common_vendor.n($props.item.isOnline == 0 ? "docImg_box_unOnline" : "docImg_box_Online")
  }, {
    f: $props.item.isOnline == 0
  }, $props.item.isOnline == 0 ? {} : {}, {
    g: $props.item.isOnline == 0 ? "#ffffff" : "#ffffff",
    h: common_vendor.t($props.item.docName),
    i: common_vendor.t($props.item.deptName),
    j: common_vendor.t($props.item.docProf),
    k: common_assets._imports_1$3,
    l: common_vendor.t($data.isShowWorkHosName ? $props.item.workHosName : $props.item.hosName),
    m: common_assets._imports_2$1,
    n: common_vendor.t($props.item.consultationCount),
    o: common_assets._imports_3$1,
    p: common_vendor.t($props.item.percentage),
    q: common_vendor.o(($event) => $options.toDoc($props.item))
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-5fac3b5e"]]);
wx.createComponent(Component);
