/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*
	字体颜色相关
*/
/*
	背景色 分割线色
*/
/*
	主题色
*/
/*
		渐变可调角度
*/
/*
	定义弹性布局
 */
/*
	单行文本溢出隐藏
*/
.page.data-v-f48d61de {
  background-color: #fff;
}

/* 主体内容 */
.initial-content.data-v-f48d61de {
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.initial-content image.data-v-f48d61de {
  display: block;
  width: 170rpx;
  border-radius: 8rpx;
  margin: 0 auto;
}
.initial-content .title.data-v-f48d61de {
  font-size: 44rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #836aff;
  margin-top: 28rpx;
  line-height: 60rpx;
}
[data-theme=nx] .initial-content .title.data-v-f48d61de {
  color: #107dff;
}
[data-theme=test] .initial-content .title.data-v-f48d61de {
  color: #2db99d;
}
.toast-text.data-v-f48d61de {
  margin-top: 20rpx;
}
.initial-content .text.data-v-f48d61de {
  font-size: 30rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  color: #4a4a4a;
  line-height: 60rpx;
}