<template>
  <view class="chat_detail" ref="element">
    <!-- 顶部固定区域 -->
    <view class="nav_top_fixed">
      <view class="docName">
        {{ pageParam.docName }}
      </view>
      <view class="info" v-show="visitTypeName && pageParam.bussType != 7">
        服务信息（{{ visitTypeName }}{{ isSubsequent }}
        <text style="color: rgba(131, 106, 255, 1)">
          {{ cost }}元/{{ visitDuration }}/次)
        </text>
      </view>
      <view class="info" v-show="visitTypeName && pageParam.bussType == 7">
        服务信息（快速续方)
      </view>
      <view class="nav_top_fixed_left" @click="goBack">
        <image src="../../static/left-jt.png" class="backIcon"></image>
      </view>
    </view>
    <view
      v-if="
        pageParam.bussType == 7 &&
        pageParam.ywStatus == 1 &&
        ksxfWait.minute != 0
      "
      class="assignDoctorsTime"
      style="display: flex; align-items: center"
    >
      <text>正为您分配医生请耐心等待</text>
      <uni-countdown
        color="#333"
        background-color="#FDF8DB"
        border-color="#FDF8DB"
        :minute="ksxfWait.minute"
        :second="ksxfWait.seconds"
        :show-colon="false"
        :showDay="false"
      ></uni-countdown>
    </view>
    <!-- 顶部提示 -->

    <view
      class="nav_tips"
      v-show="
        showTips &&
        pageParam.regId &&
        (pageParam.ywStatus == '1' || pageParam.ywStatus == '2') &&
        pageParam.bussType != 7
      "
    >
      <text class="tips_text" @click="openConDetail"
        >问诊中可以<text class="theme">点击</text>追加病情资料</text
      >
      <image
        src="/static/images/connect/del.png"
        class="closeIcon"
        @click="showTips = false"
      />
    </view>

    <!-- 内容 -->
    <view class="chatBox" @click.stop="changePanel">
      <view
        v-for="(item, index) in list"
        class="m-item"
        :key="item.time + index"
        @longpress.stop="onLongPress(item, index)"
      >
        <!-- 医生消息 -->
        <view
          class="chat-sender"
          v-if="item.type == 'receive' && item.content && item.from != 'admin'"
        >
          <!-- 文本模块 -->
          <MsgText
            @head="docInfoFun"
            :imgUrl="docImg"
            @double="setTextZoom"
            :content="renderTxt(item.content)"
            v-if="
              item.messType == 'text' &&
              !item.ext.cfbusinessCode &&
              !item.ext.czbType &&
              !item.ext.groupInfoId &&
              !item.ext.regId &&
              !item.ext.sendId &&
              !item.ext.plsId &&
              !item.ext.pliId &&
              !item.ext.referralId &&
              item.ext.type != 'zdyfw' &&
              !item.ext.authorizeId &&
              !item.ext.ppiId &&
              !item.ext.conferenceId
            "
          />

          <!-- 处方建议 -->
          <Proposal
            @head="docInfoFun"
            :imgUrl="docImg"
            :diagName="item.ext.diagName"
            :hzStstus="item.ext.hzStstus"
            isSubsequent="0"
            v-if="
              item.messType == 'text' &&
              item.ext.cfbusinessCode &&
              item.ext.isSubsequent == '0'
            "
            @click="getCfDetail(item)"
          />

          <!-- 处方 -->
          <Proposal
            @head="docInfoFun"
            :imgUrl="docImg"
            :diagName="item.ext.diagName"
            prescription
            :day="item.ext.prescriptionIndate"
            :hzStstus="item.ext.hzStstus"
            :businessId="item.ext.businessId"
            isSubsequent="1"
            v-if="
              item.messType == 'text' &&
              item.ext.cfbusinessCode &&
              item.ext.isSubsequent == '1'
            "
            @click="getCfDetail(item)"
          />

          <!-- 门诊加号 -->
          <AddDiag
            @head="docInfoFun"
            :imgUrl="docImg"
            :docName="pageParam.docName"
            @click="openLineCard(item)"
            v-if="item.messType == 'text' && item.ext.czbType"
          />

          <!-- 量表 -->
          <Gauge
            @head="docInfoFun"
            :imgUrl="docImg"
            :title="item.ext.title"
            :isFeedback="item.ext.isFeedback"
            :status="item.ext.isSuccess"
            @click="openScaleDetail(item)"
            v-if="item.messType == 'text' && item.ext.sendId"
          />
          <!--复诊提醒-->
          <fztx
            @head="docInfoFun"
            :imgUrl="docImg"
            :title="item.ext.title"
            :status="item.ext.isSuccess"
            @click="openFzDetail(item)"
            v-if="item.messType == 'text' && item.ext.type === 'fztx'"
          />
          <!-- 服务 -->
          <Serve
            @head="docInfoFun"
            :imgUrl="docImg"
            :title="item.ext.customBussTitle"
            :info="item.ext.customBussMemo"
            :isSuccess="item.ext.isSuccess"
            @click="openServiceDetail(item)"
            v-if="item.messType == 'text' && item.ext.type == 'zdyfw'"
          />

          <!-- 群发 -->
          <Group
            @head="docInfoFun"
            :imgUrl="docImg"
            :title="item.ext.title"
            @click="openFsSend(item)"
            v-if="item.messType == 'text' && item.ext.groupInfoId"
          />

          <!-- 检查单 -->
          <Inspection
            @head="docInfoFun"
            :imgUrl="docImg"
            :title="item.ext.ppiDiag"
            :info="item.content"
            @click="openChecklist(item)"
            v-if="item.messType == 'text' && item.ext.ppiId"
            :status="item.ext.orderStatus"
          />

          <!-- 检验单 -->
          <Inspection
            @head="docInfoFun"
            :imgUrl="docImg"
            isInspect
            :title="item.ext.ppiDiag"
            @click="openOrderDetail(item)"
            :status="item.ext.orderStatus"
            v-if="item.messType == 'text' && item.ext.pliId"
          />

          <!-- 随访 -->
          <Follow
            @head="docInfoFun"
            :imgUrl="docImg"
            :title="item.ext.title"
            @click="openFollowDetail(item)"
            v-if="item.messType == 'text' && item.ext.plsId"
          />

          <!-- 咨询小结 -->
          <Consulting
            @head="docInfoFun"
            :imgUrl="docImg"
            @click="consultationSummary(item)"
            v-if="item.messType == 'text' && item.ext.type == 'zxxj'"
          />

          <!-- 转诊 -->
          <Referral
            @head="docInfoFun"
            :imgUrl="docImg"
            @click="openZz(item)"
            :docName="item.ext.docName"
            :status="item.ext.isSuccess"
            v-if="item.messType == 'text' && item.ext.referralId"
          />

          <!-- 病例授权 -->
          <Author
            @head="docInfoFun"
            :imgUrl="docImg"
            :status="item.ext.isSuccess"
            @click="openMedicalDetail(item)"
            v-if="item.messType == 'text' && item.ext.authorizeId"
          />

          <!-- 图片消息 -->
          <ChatImg
            @head="docInfoFun"
            :imgUrl="docImg"
            :imgSrc="item.content"
            v-if="item.messType == 'image'"
          />

          <!-- 音频消息 -->
          <CharAudio
            @head="docInfoFun"
            :imgUrl="docImg"
            :audioSrc="item.content"
            :time="item.duration"
            :id="item.id"
            @click="audioClick"
            :index="index"
            :play="item.play"
            v-if="item.messType == 'voice'"
          />

          <!-- 视频消息 -->
          <block v-if="item.messType == 'video'">
            <CharVideo
              @head="docInfoFun"
              :imgUrl="docImg"
              :videoSrc="item.content"
              :id="item.id"
              :key="item.id"
              :poster="item.thumbnailLocalPath"
            />
          </block>

          <!-- 视频邀请 -->
          <VideoInvitation
            @head="docInfoFun"
            :imgUrl="docImg"
            :ext="item.ext"
            :docInfo="docDetail"
            :content="item.content"
            @setIsNext="setIsNext(item.id)"
            v-if="item.ext && (item.ext.conferenceId || item.ext.channel)"
          />
        </view>

        <!-- 用户消息 -->
        <block class="chat-receiver" v-if="item.type == 'send' && item.content">
          <!-- 文字消息 -->
          <UserText
            :imgUrl="patientImg"
            @click="openMedicalRecord"
            :content="renderTxt(item.content)"
            :item="item"
            :isLongPress="item.isLongPress"
            @double="setTextZoom"
            @withdraw="onWithdraw(item)"
            v-if="
              item.messType == 'text' &&
              !item.ext.cfbusinessCode &&
              !item.ext.czbType &&
              !item.ext.groupInfoId &&
              !item.ext.regId &&
              !item.ext.sendId &&
              !item.ext.plsId &&
              !item.ext.plmId &&
              !item.ext.referralId &&
              !item.ext.customBussinessId &&
              item.ext.type != 'mbxf'
            "
          />

          <!-- 图片消息 -->
          <UserImg
            @click="openMedicalRecord"
            :imgUrl="patientImg"
            :imgSrc="item.content"
            v-if="item.messType == 'image'"
          />

          <!-- 音频消息 -->
          <UserAudio
            @head="openMedicalRecord"
            :imgUrl="patientImg"
            :audioSrc="item.content"
            :time="item.duration"
            :id="item.id"
            @click="audioClick"
            :index="index"
            :play="item.play"
            v-if="item.messType == 'voice'"
          />

          <!-- 视频消息 -->
          <block
            v-if="
              item.type == 'send' && item.content && item.messType == 'video'
            "
          >
            <UserVideo
              @head="openMedicalRecord"
              :imgUrl="patientImg"
              :videoSrc="item.content"
              :id="item.id"
              :poster="item.thumbnailLocalPath"
            />
          </block>

          <!-- 慢病续方 -->
          <Continue
            @head="openMedicalRecord"
            :imgUrl="patientImg"
            :title="item.ext.diagName"
            v-if="item.messType == 'text' && item.ext.type == 'mbxf'"
            @click="sendContinueUrl(item)"
          />
          <!-- 快速续方 -->
          <Continue
            @head="openMedicalRecord"
            :imgUrl="patientImg"
            :title="item.ext.diagName"
            name="快速续方"
            v-if="item.messType == 'text' && item.ext.type == 'ksxf'"
          />
          <!--          @click="sendContinueUrl(item)"-->
        </block>
        <block
          class="chat-receiver"
          v-if="item.type == 'send' && !item.content"
        >
          <Continue
            @head="openMedicalRecord"
            :imgUrl="patientImg"
            :title="item.ext.diagName"
            name="快速续方"
            v-if="item.messType == 'text' && item.ext.type == 'ksxf'"
          />
        </block>
        <!-- 撤回消息 -->
        <view class="chat-center" v-if="item.type == 'withdraw'">
          <text class="tipsText">你撤回了一条消息</text>
          <!--          <text-->
          <!--            class="editMsg"-->
          <!--            v-if="item.messType == 'text'"-->
          <!--            @click="editMsg(item)"-->
          <!--            >重新编辑</text-->
          <!--          >-->
        </view>

        <!-- 撤回消息 -->
        <view class="chat-center" v-if="item.type == 'reWithdraw'">
          <text class="tipsText">对方撤回了一条消息</text>
        </view>

        <!-- 处方通过 -->
        <view
          class="chat-center"
          v-if="item.ext.type == 'HZ_WZ_SF' && item.type == 'defaultMsg'"
        >
          <span class="tipsText">{{ item.content }}</span>
          <!--                  <span-->
          <!--                    class="editMsg"-->
          <!--                    v-if="item.messType == 'text'"-->
          <!--                    @click="getCfDetail(item)"-->
          <!--                    >查看</span-->
          <!--                  >-->
        </view>

        <!-- 间隔时间 -->
        <view
          class="chat_time"
          v-if="
            index < list.length - 1 &&
            list[index + 1].time - item.time > 1000 * 60 * 5
          "
        >
          <text>{{ timeOut(list[index + 1].time) }}</text>
        </view>
      </view>

      <!-- 新消息 -->
      <view class="newInfoTips" v-if="newInfoTips" @click="goDown('down')">
        <img src="../../static/images/chat/down.png" class="downImg" />
        <view>{{ newChatNum }}条新消息 <text>|</text></view>
        <img
          src="../../static/images/chat/close_tip.png"
          class="closeTip"
          @click.stop="goDown('close')"
        />
      </view>

      <!-- </scroll-view> -->
    </view>

    <!-- 底部 -->
    <view class="footer" ref="footerRef" id="footer">

      <view v-if="pageParam.ywStatus == 1" class="cancleW" @click="cancleW">取消问诊</view>
      <view v-if="pageParam.ywStatus == '2' || surplusTimes > 0">
        <view class="countdown" v-if="isCountdown && pageParam.ywStatus == '2'">
          <image
            src="../../static/images/message.png"
            class="successIcon"
          ></image>

          <view style="display: flex; align-items: center">
            <text>问诊中</text>
            <uni-countdown
              color="rgba(44, 199, 147, 1)"
              background-color="rgba(232, 250, 244, 1)"
              border-color="rgba(232, 250, 244, 1)"
              :day="usedTime.days"
              :hour="usedTime.hours"
              :minute="usedTime.minutes"
              :show-colon="false"
              :second="0"
            ></uni-countdown>
            <text>后结束</text>
          </view>
          <image
            src="../../static/images/connect/del.png"
            class="closeIcon"
            @click="closeCountDown"
          ></image>
        </view>

        <view class="operateView">
          <image
            src="../../static/images/chat/voice.png"
            class="videoBtn"
            v-if="!recordShow"
            @click="changePanelStatus('recordShow')"
          ></image>
          <image
            src="../../static/images/chat/jianpan.png"
            class="videoBtn"
            v-if="recordShow"
            @click="changePanelStatus('recordHide')"
          ></image>
          <view
            class="recordBtn"
            v-if="recordShow"
            @touchmove.prevent="handletouchmove"
            @touchstart="handletouchstart"
            @touchend="handletouchend"
          >
            按住说话，松开结束
          </view>
          <view v-show="showAnimation" class="recordDiv">
            <image
              src="../../static/images/chat/recordBtn.png"
              class="recordBtn"
            ></image>
            <image src="../../static/images/chat/1.gif" alt></image>
          </view>
          <editor
            id="editor"
            @focus="changePanelStatus('input')"
            @blur="changePanelStatus('input')"
            class="ql-container uni-input"
            @input="changeInput"
            v-if="!recordShow"
            :read-only="recordShow"
          ></editor>
          <image
            src="../../static/images/chat/face.png"
            class="faceBtn"
            v-if="!sendVal"
            @click="changePanelStatus('emoji')"
          ></image>
          <image
            src="../../static/images/chat/other.png"
            class="addBtn"
            @click="changePanelStatus('addPanel')"
            v-if="!sendVal"
          ></image>
          <!-- 尝试解决ios 键盘聚焦需要点击两次才能发送问题 -->
          <view
            class="sendBtn"
            v-if="sendVal"
            @touchend.prevent="sendMsg()"
            @click="sendMsg()"
          >
            发送
          </view>
        </view>

        <!-- 功能面板 -->
        <view class="functionPanel" v-if="funPanelShow">
          <view class="panel" @click="openPic">
            <image src="../../static/images/chat/photo.png" mode=""></image>
            <view> 照片 </view>
          </view>
          <view class="panel" @click="openVideo">
            <image src="../../static/images/chat/video.png" mode=""></image>
            <view> 视频 </view>
          </view>
          <view class="panel" @click="openReason">
            <image src="../../static/images/chat/camera.png" mode=""></image>
            <view> 投诉 </view>
          </view>
          <view v-if="pageParam.bussType == 7" class="panel" @click="sendContinue">
            <image src="../../static/images/chat/continue.png" mode=""></image>
            <view> 快速续方 </view>
          </view>
        </view>

        <!-- 表情 -->
        <!--        <view class="emoji" v-if="emojiShow">-->
        <!--          <image-->
        <!--            @click="selectEmoji(value, key)"-->
        <!--            :src="emoji.path + value"-->
        <!--            class="emojiImg"-->
        <!--            v-for="(value, key, index) in emoji.map"-->
        <!--            :key="index"-->
        <!--          ></image>-->
        <!--        </view>-->
        <!-- 表情-->
        <view class="words" v-if="emojiShow">
          <view class="emojiBox">
            <view
              v-for="(item, index) in emojis"
              class="emoji-item"
              :key="index"
              @click="addEmoji(item)"
            >
              {{ item }}
            </view>
          </view>
        </view>
      </view>

      <!-- 如果待接诊 并且没有赠送次数 -->
      <view
        v-if="
          pageParam.ywStatus == '1' &&
          surplusTimes <= 0 &&
          pageParam.bussType != 7
        "
        class="waitJz"
      >
        医生将于{{
          js_no_receive_duration
        }}小时内接诊，如超时未接诊将为您自动退款
      </view>

      <!-- 没有进行中订单 有赠送次数 -->
      <view
        v-if="
          (pageParam.ywStatus == null ||
            pageParam.ywStatus == '3' ||
            pageParam.ywStatus == '4' ||
            pageParam.ywStatus == '5') &&
          surplusTimes > 0 &&
          pageParam.bussType != 7
        "
        class="waitJz"
      >
        剩余回复次数{{ surplusTimes }}次
      </view>

      <view
        class="endWz"
        v-if="
          (pageParam.ywStatus == null ||
            pageParam.ywStatus == '3' ||
            pageParam.ywStatus == '4' ||
            pageParam.ywStatus == '5') &&
          surplusTimes <= 0
        "
      >
        <view class="tips">本次就诊已结束，您可以选择再次问诊</view>

        <!-- 排班组件 -->
        <VISIT
          v-if="showTab"
          :list="visitInfo"
          @setNum="changeActive"
          :act="active"
          @click="getAdvisory"
          :isFz="isFz"
          :isZx="isZx"
          :isDesc="isDesc"
        />
      </view>
    </view>

    <uni-popup ref="popup" type="bottom" class="selPicPopup">
      <view @tap="openPic">图片</view>
      <view @click="openVideo">视频</view>
      <view @tap="closePopup">取消</view>
    </uni-popup>

    <!-- 投诉 -->
    <uni-popup ref="complaintPopup" type="center" class="complaintPopup">
      <view class="complaintView">
        <image
          src="../../static/images/chat/complaint_logo.png"
          class="complaintLogo"
        ></image>
        <view class="title">投诉原因</view>
        <view class="uni-list">
          <radio-group @change="radioChange">
            <label
              class="uni-list-cell uni-list-cell-pd"
              v-for="(item, index) in complaintInfo"
              :key="index"
            >
              <view>
                <radio
                  style="transform: scale(0.7)"
                  :value="item.optionCode"
                  :checked="item.optionCode == complainData.complainCauseCode"
                />
              </view>
              <view>{{ item.optionName }}</view>
            </label>
            <label class="uni-list-cell uni-list-cell-pd">
              <view>
                <radio
                  style="transform: scale(0.7)"
                  value="qt"
                  :checked="'qt' == complainData.complainCauseCode"
                />
              </view>
              <view>其他原因</view>
            </label>
            <view class="qtReason">
              <textarea
                v-model="message"
                class="textAreaView"
                placeholder-style="color:#999999;font-size:12px;"
                auto-height
                placeholder="请输入其它原因"
              />
              <image
                v-if="!imgBase64"
                @click="chooseImgFun"
                src="../../static/images/chat/complaint_photo.png"
                class="uploadImg"
              ></image>
              <image
                v-if="imgBase64"
                @click="chooseImgFun"
                :src="imgBase64"
                class="uploadImg"
              ></image>
            </view>
          </radio-group>
        </view>
        <view class="btns">
          <view class="cancel" @click="closeReason">取消</view>
          <view class="commit" @click="saveComplaintInfo">确定</view>
        </view>
      </view>
    </uni-popup>

    <!-- 文字放大 -->
    <textZoom v-show="showZoom" :text="zoom_text" @concal="showZoom = false" />
  </view>
</template>

<script>
import { Toast } from "@/common/js/pay.js";
var timer;
// ios播放amr 或者mp3转为amr
const BenzAMRRecorder = require("benz-amr-recorder");

// 引入录音
import REC from "@/common/js/rec.js";

// 医生首页排班
import VISIT from "@/pages/register/docHomePage/com/dntList.vue";

import avatar from "../../static/images/docHead.png";
import date from "../../utils/date";

// 文本消息
import MsgText from "./doc_components/text.vue";
// 处方建议
import Proposal from "./doc_components/proposal.vue";
// 门诊加号
import AddDiag from "./doc_components/addDiagnosis.vue";
// 量表
import Gauge from "./doc_components/gauge.vue";
// 服务
import Serve from "./doc_components/serve.vue";
// 群发
import Group from "./doc_components/groupMsg.vue";
// 检查单
import Inspection from "./doc_components/inspection.vue";
// 随访
import Follow from "./doc_components/followUp.vue";
// 咨询小结
import Consulting from "./doc_components/consulting.vue";
// 转诊
import Referral from "./doc_components/referral.vue";
import fztx from "./doc_components/fztx.vue";
// 病例授权
import Author from "./doc_components/author.vue";
// 图片消息
import ChatImg from "./doc_components/img.vue";
// 音频消息
import CharAudio from "./doc_components/audio.vue";
// 视频消息
import CharVideo from "./doc_components/video.vue";

// 视频邀请
import VideoInvitation from "./doc_components/pv.vue";

// 用户文字
import UserText from "./user_components/text.vue";
// 用户图片
import UserImg from "./user_components/img.vue";
// 用户音频
import UserAudio from "./user_components/audio.vue";
// 用户视频
import UserVideo from "./user_components/video.vue";
// 慢病续方
import Continue from "./user_components/continue.vue";

// 文字放大
import textZoom from "@/components/textZoom/index.vue";

import {
  findCustomServicePayStatus,
  getGiveTimesById,
  getIsCountdown,
  getMedicalAuthorizeStatus,
  getPrescriptionCard,
  getQuestionStatus,
  getReferralRecord,
  saveProComplain,
  setCloseCountdown,
  updateSurplusTimes,
} from "@/api/chat";
import {
  findAllByType,
  getConfigInfo,
  findVisitTypePrice,
  findDoctorByID,
  getSysPlatformConfigByKeyList,
} from "@/api/base";

import { checkLisAppointStatus, checkPacsAppointStatus } from "@/api/inspect";

// 查接诊状态
import { getPatientReceive, getPatientReceiveStatus } from "@/api/user.js";

import { findPatientByPatientId } from "@/api/chatCardDetail";
import { getSysOptionConfig } from "@/api/cf";
import * as myJsTools from "@/common/js/myJsTools";
import { uploadImg } from "@/api/oss";
import {
  getDocPatientBlackList,
  queryAllocateTime,
  queryQuickPrescription,
} from "../../api/register";
import {cancelMessage, paymentBusinessrefund} from "../../api/base";
import {pregOrderDetailNew} from "../../api/order";

import FOOTER from '@/components/footer_button/button.vue';
// 滚动变量
var scroll_timer;

export default {
  components: {
    textZoom,
    MsgText,
    Proposal,
    AddDiag,
    Gauge,
    Serve,
    Group,
    Inspection,
    Follow,
    Consulting,
    Referral,
    fztx,
    Author,
    ChatImg,
    CharAudio,
    CharVideo,
    VideoInvitation,
    UserText,
    UserImg,
    UserAudio,
    UserVideo,
    Continue,
    VISIT,
    FOOTER
  },
  data() {
    return {
      zoom_text: "",
      // 显示文本放大
      showZoom: false,
      // 是否第一次请求数据
      init: true,
      // 音频实例
      audio: "",
      // 播放的下标
      audio_index: 0,
      // 播放的整条数据
      audio_item: "",
      list: [],
      funPanelShow: false, //功能面板显示控制
      emoji: this.$im.Emoji, //表情列表
      emojiShow: false, //表情显示控制
      recordShow: false, //录音按钮显示
      inputVal: "", //文本框绑定值
      sendVal: "", //发送值
      editorCtx: "", //富文本值
      msgHeight: "525", //聊天界面的高度
      id: "", //患者id
      docId: "", //医生id
      userId: uni.getStorageSync("userId"), //用户id
      docInfo: {}, //医生信息
      defaultImg: 'this.src="' + avatar + '"', //默认头像
      showAnimation: false, //录音样式控制
      pageParam: {},
      scrollTop: 0,
      isCountdown: false,
      usedTime: {}, //倒计时
      js_no_receive_duration: "",
      active: 0,
      subsequent: [], //复诊排班
      inquiryWay: [], //咨询排班
      visitInfo: [],
      patientInfo: "",
      complainData: {},
      complaintInfo: [], //投诉原因
      message: "", //投诉原因其他原因文本
      imgBase64: "", //其他原因图片
      surplusTimes: "", //赠送次数
      showTips: true, //完善问诊信息是否显示
      chatId: "", //聊天记录标记id
      visitDuration: "", //时长
      isSubsequent: "", //服务类型
      visitTypeName: "", //问诊类型
      cost: "", //金额

      docDetail: {},
      nowChatList: [],
      oneList: [], //单人聊天记录

      listQuery: {
        page: 1,
        limit: 20,
        size: 1,
        total: 0,
      },
      scrollHeight: 0,
      newInfoTips: false, //新消息提示
      bottomHeight: 0, //消息提示高度
      newChatNum: 0, //新消息条数
      interviewTop: 0, //倒计时高度
      lastMsgId: "", //上一条消息的滚动高度
      patientImg: "",
      docImg: "",
      // 是否第一次
      isFrist: true,
      // 是否开启咨询
      isZx: false,
      // 是否开启复诊
      isFz: false,
      // 是否倒序 默认 咨询在前
      isDesc: false,
      // 是否显示tab
      showTab: false,

      showHeight: document.documentElement.clientHeight, //实时屏幕高度
      refreshIfNeeded: false,
      ksxf: 0,
      ksxfWait: {
        minute: 0,
        seconds: 0,
      },
      emojis: [
        "😀",
        "😁",
        "😂",
        "🤣",
        "😅",
        "😆",
        "😉",
        "😊",
        "😋",
        "😎",
        "😍",
        "😘",
        "😙",
        "😚",
        "🤗",
        "😡",
        "😢",
        "🤯",
        "🤡",
        "🌹",
        "👍",
      ],
    };
  },
  computed: {
    // 监听接诊退诊
    jzStatus() {
      return this.$store.getters.getJzStatus;
    },
    msgRecall() {
      return this.$store.getters.msgRecall;
    },
    async chatList() {
      let chat_list = this.$store.getters.getChatList;
      if (chat_list.id != this.chatId) return;
      let list = [];
      let timer;
      list = chat_list.chatRecordList || [];
      if (list.length == 1) {
        clearTimeout(timer);
        timer = setTimeout(() => {
          if (list.length == 1 && this.oneList.length == 0) {
            //解决病例授权定时器第二次复制时会改变状态加&&this.oneList.length==0
            this.list = [...list];
            this.oneList = list;
            console.log("kankan", this.list);
          }
        }, 1500);
        //当只有一条处方信息的时候诊断信息重新赋值
        // 要添加的信息
        let item = list[0];
        // 判断是处方 并且没有诊断名称
        if (item.ext.type == "cf" && !item.ext.diagName) {
          item = await this.getDetailCf(item);
        }
        return;
      }
      if (this.list.length && this.oneList.length) {
        clearTimeout(timer);
        if (this.oneList.length < list.length) {
          // 列表容器
          let chatBox = document.querySelector(".chatBox");

          // 要添加的信息
          let item = list[this.oneList.length];

          // 判断是处方 并且没有诊断名称
          if (item.ext.type == "cf" && !item.ext.diagName) {
            item = await this.getDetailCf(item);
          }

          this.list.push(list[this.oneList.length]);
          // 最后一条是未读
          if (list[this.oneList.length].type == "receive") {
            // 如果滚动条高度 - 当前滚动高度 大于40
            if (
              chatBox.scrollHeight - chatBox.scrollTop - chatBox.clientHeight >
              30
            ) {
              this.newChatNum++;
              this.newInfoTips = true;
            }
            if (
              chatBox.scrollTop + chatBox.clientHeight ==
              chatBox.scrollHeight
            ) {
              this.$nextTick(function () {
                chatBox.scrollTop = chatBox.scrollHeight;
              });
            }
          } else {
            this.$nextTick(function () {
              chatBox.scrollTop = chatBox.scrollHeight;
            });
          }
          this.oneList.push(list[this.oneList.length]);
          // 获取最后一条消息
          let msg = this.oneList[this.oneList.length - 1];
          // 如果是医生发送的消息 是未读状态
          if (msg.type == "receive" && msg.status != "read") {
            // 改变已读
            msg.status = "read";
            // 更新本地
            this.$store.commit("updateMessageStatus", msg);
          }
        }

        // 如果发生变化，但数量没变
        if (list.length == this.oneList.length) {
        }
      }
      // 初次没有消息时候，第一条消息不会显示 后续要改
      return list;
    },
  },
  watch: {
    list: function (val) {
      this.toBottom();
    },
    // 监听撤回
    msgRecall() {
      console.log("---------------------------9999-99999");
      let id = this.msgRecall;
      let index = this.list.findIndex((v) => v.id == id);
      if (index > -1) {
        console.log("---------------------------9999-99999");
        let item = this.list[index];
        item.type = "reWithdraw";
        this.$set(this.list, index, item);
      }
    },
    chatList(n) {},
    jzStatus(n, o) {
      if (!n) return;
      this.getJzStatus();
    },
  },
  created() {
    setTimeout(() => {
      this.chatId = this.id.toLowerCase() + "," + this.docId.toLowerCase();
      this.$store
        .dispatch("getChatListId", { chatId: this.chatId })
        .then(() => {
          let list = this.$store.getters.getChatList;
          console.log(list, "list1111111");
          list.chatRecordList.map((item) => {
            this.oneList.push(item);
          });
          this.list = [];
          this.listQuery.total = this.listQuery.minNum = this.oneList.length;
          this.listQuery.size = this.oneList.length / this.listQuery.limit;
          //初始化加载数据
          this.initData();
        });

      REC.InitRec();
    }, 1000);
  },
  mounted() {
    let chatBox = document.querySelector(".chatBox");
    let t;
    chatBox.onscroll = (e) => {
      let num = e.target.scrollTop;
      console.log(num)

      clearTimeout(t);
      t = setTimeout(() => {
        let scrollNum =
          chatBox.scrollHeight - chatBox.scrollTop - chatBox.clientHeight;
        this.scrollTop = num;
        if (scrollNum < 30) {
          this.newInfoTips = false;
          this.newChatNum = 0;
        }
      }, 1000);
      this.scrollView(num);
    };
    uni.$on("reLoadPage", () => {
      setTimeout(() => {
        this.chatId = this.id.toLowerCase() + "," + this.docId.toLowerCase();
        this.$store
          .dispatch("getChatListId", { chatId: this.chatId })
          .then(() => {
            let list = this.$store.getters.getChatList;
            list.chatRecordList.map((item) => {
              this.oneList.push(item);
            });
            this.list = [];
            this.listQuery.total = this.listQuery.minNum = this.oneList.length;
            this.listQuery.size = this.oneList.length / this.listQuery.limit;
            //初始化加载数据
            this.initData();
          });

        REC.InitRec();
      }, 1000);
    });
  },
  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    //页面传参，挂号信息
    let param = uni.getStorageSync("chatItem");
    let list = this.$store.getters.getChatList;
    this.pageParam = param;
    if (this.pageParam.bussType == 7) {
      const allocateTime = await queryAllocateTime({
        regId: this.pageParam.regId,
      });
      const minute = date.DateDifferenceMinutes(
        Date.parse(new Date(allocateTime.data))
      );
      const info = this.getMinutesSeconds(
        Date.parse(new Date(allocateTime.data)),
        new Date().getTime()
      );
      if (minute <= 29) {
        this.ksxfWait.minute = minute <= 0 ? 29 : 29 - minute;
        this.ksxfWait.seconds = 60 - info.seconds;
      } else {
        this.ksxfWait.minute = 0;
      }
    }

    this.ksxf = options.ksxf || 0;
    this.docId = param.docId;
    this.id = param.patientId;
    this.patientImg = param.patientImg;
    // this.chatId = this.id.toLowerCase() + "," + this.docId.toLowerCase();
    // // 如果通过浏览器前进 重新设置id
    // if (Array.isArray(list)) {
    //   this.$store.dispatch("getChatListId", { chatId: this.chatId });
    // }
    // 类型
    let { visitTypeCode, cost, isSubsequent, visitDuration } = param;
    if (visitTypeCode == 1) {
      this.visitTypeName = "图文";
    } else if (visitTypeCode == 2) {
      this.visitTypeName = "语音";
    } else if (visitTypeCode == 4) {
      this.visitTypeName = "视频";
    }
    if (isSubsequent == "0") {
      this.isSubsequent = "咨询";
    } else {
      this.isSubsequent = "复诊";
    }
    // 价格
    this.cost = cost;
    // 时长
    this.visitDuration = visitDuration;
    // 排班开启情况
    await this.getConfig();
    // 获取用户头像
    this.getPatientImg();
    // 获取当前接诊状态
    this.getJzStatus();
    // 获取就诊人信息
    this.getPatientInfo();
    // 环信登录
    this.WebIMLogin();
    // 获取医生信息
    this.getDocDetail();
    // 全局参数
    this.getWholeArg();
    // 获取投诉原因
    this.getComplaintInfo();
    uni.onWindowResize((res) => {
      this.scrollToBottom();
    });
  },
  async onShow() {
    if (this.docDetail && this.docDetail.hosId) {
      uni.setStorageSync("hosId", this.docDetail.hosId);
    }

    if (this.scrollTop > 0) {
      let chatBox = document.querySelector(".chatBox");
      chatBox.scrollTop = this.scrollTop;
    }

    // 获取赠送次数
    this.getGiveTimesByIdFun();
    //每次进入聊天页面重新修改状态
    if (this.list.length) {
      this.changeFeedstatus();
    }
    let pages = getCurrentPages(); // 获取当前页面栈
    let currentPage = pages[pages.length - 1]; // 当前页面
    if (currentPage.refreshIfNeeded) {
      currentPage.refreshIfNeeded = false;
      //授权返回重新获取病例状态
      this.changeCaseStatus();
    }
    if (this.isFrist) return;
    // 获取当前接诊状态
    this.getJzStatus();
  },
  methods: {
   async cancleW(){
     let param = {
       docId: this.docId,
       patientId: this.pageParam.patientId,
     };
     let res = await getPatientReceive(param);
     if (res.code != 20000) return;

     let obj = res.data;

     let ywStatus = obj == null ? null : obj.ywStatus;
     this.pageParam.ywStatus = ywStatus;
     if(ywStatus!=1){
       return
     }
     const data={
       regId: this.pageParam.regId
     }
     paymentBusinessrefund(data).then(v=>{
       uni.showToast({
         title: '取消成功！',
         icon: 'none',
       })
       setTimeout(()=>{
         uni.navigateBack({
           delta: 1
         })
       },1000)
     })
    },
    addEmoji(item) {
      uni
        .createSelectorQuery()
        .select("#editor")
        .context((res) => {
          this.editorCtx = res.context;
          this.editorCtx.setContents({
            html: this.inputVal + item,
          });
          this.inputVal = this.inputVal + item;
          this.sendVal += item;
        })
        .exec();
    },
    getMinutesSeconds(timestamp1, timestamp2) {
      // 将时间戳转换为日期对象
      var date1 = new Date(timestamp1);
      var date2 = new Date(timestamp2);

      // 计算两个日期之间的差值（毫秒）
      var difference = Math.abs(date2 - date1);

      // 将差值转换为分钟和秒
      var minutes = Math.floor(difference / 60000);
      var seconds = Math.floor((difference % 60000) / 1000);

      return { minutes: minutes, seconds: seconds };
    },
    //调取病例授权的状态
    async changeCaseStatus() {
      this.list.forEach(async (item, index) => {
        if (item.ext && item.ext.type == "blsq") {
          let authorizeId = item.ext.authorizeId;
          await getMedicalAuthorizeStatus({
            authorizeId: authorizeId,
          }).then((res) => {
            let isSuccess = res.data.authorizeStatus;
            item.ext.isSuccess = isSuccess;
            this.$set(this.oneList, index, item);
          });
        }
      });
    },
    async changeFeedstatus() {
      // 判断该量表的状态，是否已填写
      this.list.forEach(async (item, index) => {
        if (item.ext.type == "lb") {
          let { patientId, sendId } = item.ext;
          if (!sendId) return;
          let res = await getQuestionStatus({ sendId, patientId });
          let isFeedback = res.data.isFeedback; // 是否反馈 0否，1是
          item.ext.isFeedback = isFeedback;
          this.$set(this.oneList, index, item);
        }
      });
    },
    // 查询配置
    async getConfig() {
      let { data } = await getSysPlatformConfigByKeyList([
        "display_consultative",
        "display_fllow",
        "consultation_follow_up_order",
      ]);
      data.forEach((v) => {
        // 开启咨询
        if (v.configKey == "display_consultative") {
          this.isZx = v.configValue == 0 ? false : true;
        }
        // 开启复诊
        if (v.configKey == "display_fllow") {
          this.isFz = v.configValue == 0 ? false : true;
        }
        // 1 咨询前 2 复诊前
        if (v.configKey == "consultation_follow_up_order") {
          let str = v.configValue.substring(0, 1);
          if (str == 1 || str == 2) {
            this.isDesc = str == 1 ? false : true;
          }
        }
      });
    },
    // 设置文本放大
    setTextZoom(e) {
      this.zoom_text = e;
      this.showZoom = true;
    },
    // 更新自定义服务
    async getServerStatus(id) {
      if (!id) return;
      let that = this;
      let res = await findCustomServicePayStatus({
        customBussinessId: id,
      });
      let isSuccess = res.data.status;
      let list = this.list;
      list.forEach((v, i) => {
        // 医生信息 并且有扩展信息
        if (v.type == "receive" && v.ext) {
          // 自定义服务 并且是要修改值的数据
          if (v.ext.type == "zdyfw" && v.ext.customBussinessId == id) {
            v.ext.isSuccess = isSuccess;
            that.$set(that.list, i, v);
          }
        }
      });
    },
    // 查询当前接诊状态
    async getJzStatus() {
      let param = {
        docId: this.docId,
        patientId: this.pageParam.patientId,
      };
      let res = await getPatientReceive(param);
      if (res.code != 20000) return;

      let obj = res.data;

      // 如果不是初次请求 并且状态没有改变 则不重新执行请求
      if (
        !this.isFrist &&
        (obj == null || obj.ywStatus == this.pageParam.ywStatus)
      ) {
        return;
      }
      let ywStatus = obj == null ? null : obj.ywStatus;
      // if(){}
      this.pageParam.ywStatus = ywStatus;
      //null 无挂号 1待接诊 2接诊中 3已结束 4退诊 5已失效
      if (ywStatus == null || ywStatus >= 3) {
        // 如果有排班信息
        if (this.inquiryWay.length || this.subsequent.length) return;
        // 获取咨询排班
        if (this.isZx) {
          await this.getDocVisit();
        }
        if (this.isFz) {
          await this.getFzVisit();
        }
        if (this.inquiryWay.length || this.subsequent.length) {
          this.showTab = true;
        }
        if (!this.inquiryWay.length && !this.subsequent.length) {
          this.showTab = false;
        }
        if (this.isDesc && this.isFz) {
          this.changeActive(1);
        } else this.changeActive(0);
      } else {
        // 获取倒计时是否显示及计算倒计时
        this.getIsCountdownClosed();
        // 如果投诉原因不为空
        if (this.complaintInfo.length) return;
        // 获取投诉原因
        this.getComplaintInfo();
      }

      // 请求过状态 下次onShow 才会触发
      this.isFrist = false;
    },
    // 收到处方消息查询状态
    async getDetailCf(item) {
      var cfbusinessCode = item.ext.cfbusinessCode;
      var para = {
        businessCode: cfbusinessCode,
      };
      let res = await getPrescriptionCard(para);

      item.ext.diagName = res.data.diagName;
      item.ext.hzStstus = res.data.hzStstus;
      item.ext.businessId = res.data.businessId;
      item.ext.prescriptionIndate = res.data.prescriptionIndate;
      return item;
    },
    // 两条消息 间隔超过五分钟 显示后一条消息发送时间
    timeOut(time) {
      let d = new Date(Number(time));
      let h = d.getHours();
      let m = d.getMinutes();

      if (h < 10) h = "0" + h;
      if (m < 10) m = "0" + m;

      // 当前日期
      let n = date.getFormatDate("-");
      n = new Date(n).getTime();

      let y = d.getFullYear();
      let mo = d.getMonth() + 1;
      let day = d.getDate();
      if (mo < 10) mo = "0" + mo;
      if (day < 10) day = "0" + day;

      let o = new Date(y + "-" + mo + "-" + day).getTime();

      // 如果是当天
      if (o == n) return h + ":" + m;

      return y + "-" + mo + "-" + day + " " + h + ":" + m;
    },
    // 更新视频邀请状态
    async setIsNext(id) {
      await this.$store.dispatch("setVideoStatus", {
        id: this.chatId,
        msgId: id,
      });
      this.list.forEach((v) => {
        if (v.id == id) {
          v.ext.isNext = false;
        }
      });
    },
    // 回到底部
    toBottom() {
      // 第一次加载
      if (this.init) {
        clearTimeout(timer);
        timer = setTimeout(() => {
          // 列表容器
          let chatBox = document.querySelector(".chatBox");
          this.$nextTick(function () {
            // 初始化结束
            this.init = false;
            chatBox.scrollTop = chatBox.scrollHeight;
          });
        }, 300);
      }
    },
    // 音频点击
    audioClick(e) {
      let { id, index, src } = e;

      let list = this.list;

      list.forEach((v, i) => {
        if (v.messType == "voice") {
          // 不是当前点击
          if (v.id != id) {
            // 全部设置false
            v.play = false;
            if (this.audio) this.audio.stop();
          }

          // 点击的音频id
          if (v.id == id) {
            // 如果没播放
            if (!v.play) {
              v.play = true;
              // 初始化
              this.audio = new BenzAMRRecorder();
              // 加载地址播放
              this.audio.initWithUrl(src).then((res) => {
                this.audio.play();
              });

              // 完成回调
              this.audio.onEnded(() => {
                v.play = false;
                this.$set(this.list, i, v);
              });
            } else {
              this.audio.stop();
            }
          }
          this.$set(this.list, i, v);
        }
      });
    },
    getPatientImg() {
      if (this.patientImg && this.patientImg.indexOf("http") == -1) {
        myJsTools.downAndSaveImg(this.patientImg, (url) => {
          this.patientImg = url;
        });
      }
    },
    goDown(type) {
      this.newChatNum = 0;
      this.newInfoTips = false;
      if (type != "close") {
        // this.scrollToBottom();
        let chatBox = document.querySelector(".chatBox");
        chatBox.scrollTop = chatBox.scrollHeight;
      }
    },
    async initData() {
      let minNum = this.listQuery.minNum - this.listQuery.limit;
      if (minNum < 0) {
        minNum = 0;
      }
      for (var i = this.listQuery.minNum; i > minNum; i--) {
        if (i < 0) {
          break;
        } else {
          try {
            await this.ack(this.oneList[i - 1], i - 1);
            this.list.unshift(this.oneList[i - 1]);
          } catch (e) {
            this.list.unshift(this.oneList[i - 1]);
          }
        }
      }
      this.listQuery.minNum = minNum;
      this.listQuery.page++;
    },
    // 滚动事件
    scrollView(num) {
      // 没有数据则不执行
      if (this.listQuery.minNum == 0) return;
      if (num > 10) return;

      clearTimeout(scroll_timer);
      scroll_timer = setTimeout(() => {
        this.initData();
      }, 300);
    },
    // 获取医生信息
    async getDocDetail() {
      let res = await findDoctorByID({
        docId: this.pageParam.docId,
        deptId: this.pageParam.deptId,
      });
      this.docDetail = res.data;
      if (res.data.docImg) {
        myJsTools.downAndSaveImg(res.data.docImg, (url) => {
          this.docImg = url;
        });
      }
    },
    // 点击去问诊
    getAdvisory(e) {
      let msg = e;
      let docDetail = this.docDetail;
      let num = this.active;
      let date = myJsTools.formatTimeDate(new Date());
      if (msg.surplusNnm == 0) {
        Toast("该医生已无号");
      } else {
        // 判断如果为医院统一价格，获取费用明细
        if (msg.isSwitch == 0) {
          if (
            msg.visitTypeCode == "2" ||
            msg.visitTypeCode == "3" ||
            msg.visitTypeCode == "4"
          ) {
            date = "";
          }
          let para = {
            date,
            docId: docDetail.docId,
            type: msg.visitTypeCode,
            consultationCode: num,
            hosId: uni.getStorageSync("hosId"),
            dntId: msg.dntId,
          };
          findVisitTypePrice(para).then((res) => {
            if (res.data.length == 0) {
              Toast("该医生已无号");
            } else {
              let infoDetail = {
                type: num,
                dntName: res.data[0].dntName || msg.dntName,
                docId: docDetail.docId,
                docName: docDetail.docName,
                visitDuration: msg.visitDuration,
                visitPrice: msg.visitPrice,
                docProf: docDetail.docProf,
                deptName: docDetail.deptName,
                deptId: docDetail.deptId,
                docImg: this.docImg,
                docTel: docDetail.telNo,
                visitTypeCode: msg.visitTypeCode,
                // visitTypeId: msg.visitTypeId, // 后台未返回
                visitTypeName: msg.visitTypeName,
                week: msg.week || "",
                apw: msg.apw || "",
                visitDate: msg.visitDate || "",
                docLable: docDetail.docLable,
                details: res.data,
                isSwitch: msg.isSwitch,
                vrId: msg.vrId || "",
                dntId: msg.dntId,
              };
              if (
                infoDetail.visitTypeCode == "2" ||
                infoDetail.visitTypeCode == "3" ||
                infoDetail.visitTypeCode == "4"
              ) {
                infoDetail.consultationCode = infoDetail.type;
                uni.setStorageSync("appointInfoDetail", infoDetail);
                uni.navigateTo({
                  url: "/pages/register/appointRegister/reserve/index",
                });
              } else {
                uni.setStorageSync("infoDetail", infoDetail);
                uni.navigateTo({
                  url:
                    "/pages/personalCenter/patientManage/index?action=" +
                    "selectPatient",
                });
              }
            }
          });
        } else {
          let infoDetail = {
            type: num,
            dntName: msg.dntName,
            docId: docDetail.docId,
            docName: docDetail.docName,
            visitDuration: msg.visitDuration,
            visitPrice: msg.visitPrice,
            docProf: docDetail.docProf,
            deptName: docDetail.deptName,
            deptId: docDetail.deptId,
            docImg: this.docImg,
            docTel: docDetail.telNo,
            visitTypeCode: msg.visitTypeCode,
            // visitTypeId: msg.visitTypeId, // 后台未返回
            visitTypeName: msg.visitTypeName,
            week: msg.week || "",
            apw: msg.apw || "",
            visitDate: msg.visitDate || "",
            docLable: docDetail.docLable,
            details: "",
            isSwitch: msg.isSwitch,
            vrId: msg.vrId || "",
            dntId: msg.dntId,
          };
          if (
            infoDetail.visitTypeCode == "2" ||
            infoDetail.visitTypeCode == "3" ||
            infoDetail.visitTypeCode == "4"
          ) {
            infoDetail.consultationCode = infoDetail.type;
            uni.setStorageSync("appointInfoDetail", infoDetail);
            uni.navigateTo({
              url: "/pages/register/appointRegister/reserve/index",
            });
          } else {
            uni.setStorageSync("infoDetail", infoDetail);
            uni.navigateTo({
              url:
                "/pages/personalCenter/patientManage/index?action=" +
                "selectPatient",
            });
          }
        }
      }
    },
    async getWholeArg() {
      // 获取全局参数
      let res = await getConfigInfo();
      this.$store.commit("setWholeArg", res.data);
      let config = res.data; //全局参数
      config.forEach((element) => {
        if (element.configKey == "js_no_receive_duration") {
          this.js_no_receive_duration = element.configValue;
        }
      });
    },
    // 环信登录
    WebIMLogin() {
      let _this = this;
      let options = {
        apiUrl: _this.$im.config.apiURL,
        user: uni.getStorageSync("userId"),
        pwd: uni.getStorageSync("userId"),
        grant_type: uni.getStorageSync("userId"),
        appKey: _this.$im.config.appkey,
        success: function () {
          // _this.getWholeArg();
          wx.switchTab({
            url: "/pages/index/index",
          });
        },
        error: function (err) {
          _this.isModelToastShow = true;
        },
      };
      _this.$im.conn.open(options).then(res=>{
        wx.switchTab({
          url: "/pages/index/index",
        });
      }).catch(err=>{
        _this.isModelToastShow = true;
      });
    },
    // 获取医生给患者的赠送次数
    async getGiveTimesByIdFun() {
      let para = {};
      para.patientId = this.pageParam.patientId;
      para.docId = this.pageParam.docId;
      let res = await getGiveTimesById(para);
      if (res.data) {
        let surplusTimes = res.data.surplusTimes;
        this.surplusTimes = surplusTimes;
        this.scrollToBottom();
      }
    },
    openConDetail() {
      uni.navigateTo({
        url:
          "/pages/chatCardDetail/conditionInformation?regId=" +
          this.pageParam.regId,
      });
    },
    // 更新剩余次数
    async updateSurplusTimesFun() {
      let ywStatus = this.pageParam.ywStatus;
      let surplusTimes = this.surplusTimes;
      if (ywStatus != 2 && surplusTimes && surplusTimes >= 1) {
        let para = {};
        para.patientId = this.pageParam.patientId;
        para.docId = this.pageParam.docId;
        await updateSurplusTimes(para);
        let surplusTimes = this.surplusTimes;
        surplusTimes--;
        this.surplusTimes = surplusTimes;
      }
    },
    chooseImgFun() {
      let _this = this;
      uni.chooseImage({
        count: 1, //默认9
        sizeType: ["compressed"], //可以指定是原图还是压缩图，默认二者都有
        sourceType: ["album"], //从相册选择
        success: function (res) {
          if (res.tempFiles.length > 0) {
            for (var i = 0; i < res.tempFiles.length; i++) {
              let el = res.tempFiles[i];
              var reader = new FileReader();
              reader.readAsDataURL(el);
              reader.onload = function () {
                var base = reader.result;
                _this.imgBase64 = base;
              };
            }
          }
        },
      });
    },
    // 获取投诉原因并默认选中第一个
    async getComplaintInfo() {
      let res = await getSysOptionConfig({
        optionType: 1,
      });
      this.complaintInfo = res.data;
      this.complainData = {
        complainCauseCode: res.data[0].optionCode,
      };
    },
    radioChange(e) {
      this.complainData.complainCauseCode = e.detail.value;
    },
    // 保存投诉原因
    async saveComplaintInfo() {
      if (this.complainData.complainCauseCode == "qt") {
        if (this.message == "") {
          Toast("请填写投诉原因");
          return;
        } else {
          var complainData = this.complainData;
          complainData.complainCause = this.message;
          complainData.complainCauseCode = "";
          if (this.imgBase64) {
            let res = await this.uploadImgFun();
            this.complainData.complainImg = res.data.url;
          }
          this.complainData = Object.assign({}, complainData);
        }
      } else {
        var complaintInfo = this.complaintInfo;
        for (var i = 0; i < complaintInfo.length; i++) {
          if (
            this.complainData.complainCauseCode == complaintInfo[i].optionCode
          ) {
            this.complainData.complainCause = complaintInfo[i].optionName;
          }
        }
      }
      this.complaintDoc();
    },
    uploadImgFun(element) {
      let para = {
        imgBody: this.imgBase64.split(",")[1],
        folderType: 14,
        otherId: this.pageParam.patientId,
      };
      return uploadImg(para);
    },
    //提交投诉
    async complaintDoc() {
      let { docId, docName, patientId, patientName, regId } = this.pageParam;
      this.complainData.docId = docId;
      this.complainData.complainType = "2";
      this.complainData.docName = docName;
      this.complainData.patientId = patientId;
      this.complainData.patientName = patientName;
      this.complainData.regId = regId;

      await saveProComplain(this.complainData);
      Toast("投诉成功");
      this.closeReason();
      // 默认值
      this.complainData = {
        complainCauseCode: this.complaintInfo[0].optionCode,
      };
      this.message = "";
      this.imgBase64 = "";
    },
    //打开投诉原因
    openReason() {
      this.$refs.complaintPopup.open();
      this.changePanel();
    },
    //关闭投诉原因
    closeReason() {
      this.$refs.complaintPopup.close();
    },
    // 获取就诊人信息
    async getPatientInfo(orderInfo) {
      let patientId = this.pageParam.patientId;
      let res = await findPatientByPatientId({ patientId });
      let patientInfo = res.data;
      this.patientInfo = patientInfo;
    },
    //病例资料
    openMedicalDetail(item) {
      let { regIdSq, authorizeId, isSuccess, hosId } = item.ext;
      let { docId, patientId } = this.pageParam;
      uni.setStorageSync("hosId", hosId);
      uni.navigateTo({
        url:
          "/pages/patientRecord/medicalDetail?docId=" +
          docId +
          "&regId=" +
          regIdSq +
          "&type=chat&authorizeId=" +
          authorizeId +
          "&isSuccess=" +
          isSuccess +
          "&patientId=" +
          patientId,
      });
    },
    // 检查单
    async openChecklist({ ext }) {
      let {
        data: { paymentType, status },
      } = await checkPacsAppointStatus(ext.ppiId);
      if (status == 0) return;
      let url;
      // 如果已选择支付方式 到院
      if (paymentType == 1) {
        url = "/pages/inspect/pacsDetails?id=" + ext.ppiId;

        // 在线支付未支付
      } else if (paymentType == 2 && status == 1) {
        url = "/pages/inspect/pay/pacs?id=" + ext.ppiId;

        // 未选择支付方式
      } else if (!paymentType) {
        url = "/pages/inspect/pacsOrder?id=" + ext.ppiId;

        // 其他情况 直接去详情
      } else {
        url = "/pages/inspect/pacsDetails?id=" + ext.ppiId;
      }

      uni.navigateTo({
        url,
      });
    },
    // 点击检验
    async openOrderDetail({ ext }) {
      let {
        data: { paymentType, status },
      } = await checkLisAppointStatus(ext.pliId);
      if (status == 0) return;
      let url;
      // 如果已选择支付方式 到院
      if (paymentType == 1) {
        url = "/pages/inspect/lisDetails?id=" + ext.pliId;

        // 在线支付未支付
      } else if (paymentType == 2 && status == 1) {
        url = "/pages/inspect/pay/lis?id=" + ext.pliId;

        // 未选择支付方式
      } else if (!paymentType) {
        url = "/pages/inspect/lisOrder?id=" + ext.pliId;

        // 其他情况 直接去详情
      } else {
        url = "/pages/inspect/lisDetails?id=" + ext.pliId;
      }

      uni.navigateTo({
        url,
      });
    },
    changeActive(num) {
      this.active = num;
      this.setVisitInfo();
    },
    // 获取医生排班信息
    async getDocVisit() {
      // 获取医生的排班
      let { data } = await findAllByType({
        deptId: this.pageParam.deptId,
        docId: this.pageParam.docId,
        consultationCode: 0,
        hosId: uni.getStorageSync("hosId"),
      });
      this.inquiryWay = data;
    },
    // 获取复诊排班
    async getFzVisit() {
      let { data } = await findAllByType({
        deptId: this.pageParam.deptId,
        docId: this.pageParam.docId,
        consultationCode: 1,
        hosId: uni.getStorageSync("hosId"),
      });
      this.subsequent = data;
    },
    // 根据不同的num,显示不同的排班内容
    setVisitInfo() {
      let num = this.active;
      let inquiryWay = this.inquiryWay; // 咨询
      let subsequent = this.subsequent; // 复诊
      let obj;
      if (num == "0") {
        obj = inquiryWay;
      } else {
        obj = subsequent;
      }

      this.visitInfo = obj;
    },
    // 打开患者资料服务页面
    openMedicalRecord() {
      return;
      let obj = {
        docId: this.pageParam.docId,
        docImg: this.pageParam.docImg,
        patientId: this.pageParam.patientId,
        patientImg: this.pageParam.patientImg,
      };
      uni.navigateTo({
        url:
          "/pages/patientRecord/medicalRecords?param=" +
          JSON.stringify(this.pageParam),
      });
    },
    openFzDetail(item) {
      uni.navigateTo({
        url: "/pages/register/docHomePage/index?" + `docId=${item.ext.docId}`,
      });
    },
    //questionnaire 量表
    async openScaleDetail(item) {
      let { sendId, patientId, isFeedback } = item.ext;
      if (!sendId) return;
      // 判断该量表的状态，是否已填写
      let res = await getQuestionStatus({
        sendId: sendId,
        patientId: this.pageParam.patientId,
      });
      isFeedback = res.data.isFeedback; // 是否反馈 0否，1是
      item.ext.isFeedback = isFeedback;
      let list = this.list;
      let index = -1;
      list.forEach((v, k) => {
        if (v.id === item.id) {
          index = k;
        }
      });
      this.$set(this.list, index, item);
      let param = {
        sendId,
        patientId,
        docId: this.pageParam.docId,
      };
      if (isFeedback == "1") {
        uni.navigateTo({
          url:
            "/pages/chatCardDetail/questionnaireRead?param=" +
            JSON.stringify(param),
        });
      } else {
        uni.navigateTo({
          url:
            "/pages/chatCardDetail/questionnaire?action=" +
            "chatRoom" +
            "&param=" +
            JSON.stringify(param),
        });
      }
    },
    //问诊加号
    openLineCard(item) {
      let { vrTempType, czbType } = item.ext;
      let param = {
        docId: this.pageParam.docId,
      };
      if (czbType == "1") {
        uni.navigateTo({
          url: "/pages/chatCardDetail/lineVisit?param=" + JSON.stringify(param),
        });
      } else if (czbType == "2") {
        uni.navigateTo({
          url:
            "/pages/chatCardDetail/offineVisit?docId=" +
            this.pageParam.docId +
            "&patientId=" +
            this.pageParam.patientId,
        });
      } else {
        return;
      }
    },
    //随访
    openFollowDetail(item) {
      let param = {
        patientId: item.ext.patientId,
        plsId: item.ext.plsId,
        docId: this.pageParam.docId,
      };
      uni.navigateTo({
        url:
          "/pages/chatCardDetail/followUpPlan?param=" + JSON.stringify(param),
      });
    },
    // 根据业务id 更新处方卡片状态
    setCFstatus(businessId) {
      let list = this.list;
      for (let i = list.length - 1; i <= 0; i--) {
        let item = list[i];
        if (item.type == "receive" && item.ext.businessId == businessId) {
          // 改为已通过审核
          item.ext.hzStstus = "1";
          break;
        }
      }
    },
    //处方
    async getCfDetail(item) {
      if (!item.ext.businessId) return;
      let businessCode = item.ext.cfbusinessCode || item.ext.businessCode;
      // 需查询状态
      let {
        data: { payStatus, businessId, hzStstus },
      } = await getPrescriptionCard({
        businessCode,
      });
      let url;
      // 未支付
      if (payStatus == 0 && hzStstus != 7) {
        url = "/pages/prescription/prescriptionDetail?businessId=" + businessId;
      } else {
        url = "/pages/prescription/preDetail?businessId=" + businessId;
      }
      uni.navigateTo({ url });
    },
    //咨询小结
    consultationSummary(item) {
      var regId = item.ext.regId;
      uni.navigateTo({
        url: "/pages/chatCardDetail/conSummary?regId=" + regId,
      });
    },

    //群发
    openFsSend(item) {
      let param = {
        patientId: item.ext.patientId,
        groupInfoId: item.ext.groupInfoId,
        docId: this.pageParam.docId,
      };
      uni.navigateTo({
        url: "/pages/chatCardDetail/fsSend?param=" + JSON.stringify(param),
      });
    },

    //转诊
    async openZz(item) {
      let param = {
        groupInfoId: item.ext.docId,
        deptId: item.ext.deptId,
        docName: item.ext.docName,
        docId: item.ext.docId,
        patientId: this.id,
        referralId: item.ext.referralId,
        isSuccess: item.ext.isSuccess,
        regId: this.pageParam.regId,
        oldDocId: this.pageParam.docId,
      };
      // 需要转诊
      if (item.ext.isSuccess == "0") {
        let obj = {
          docId: item.ext.docId,
          patientId: this.pageParam.patientId,
          visitTypeCode: "1",
        };
        try {
          let res = await getPatientReceiveStatus(obj);
          // 查询该医生是否可预约
          if (
            res.data &&
            res.data.isAllowInquiry == "1" &&
            res.data.isPayOrder == "1"
          ) {
            uni.navigateTo({
              url:
                "/pages/chatCardDetail/referral?param=" + JSON.stringify(param),
            });
            return;
          } else {
            Toast("已挂该医生的号，无需重复挂号");
            return;
          }
        } catch (error) {
          uni.navigateTo({
            url:
              "/pages/chatCardDetail/referral?param=" + JSON.stringify(param),
          });
        }
      } else {
        Toast("已转诊成功");
      }
    },
    //自定义服务页面
    openServiceDetail(item) {
      let id = item.ext.customBussinessId;
      uni.navigateTo({
        url: "/pages/chatCardDetail/customService?id=" + id,
      });
    },
    // 关闭倒计时
    async closeCountDown() {
      var param = {
        regId: this.pageParam.regId,
        patientCloseTime: "1",
      };
      await setCloseCountdown(param);
      this.isCountdown = false;
      this.scrollToBottom();
    },
    // 查询该次挂号是否已关闭问诊倒计时的显示
    async getIsCountdownClosed() {
      var param = {
        regId: this.pageParam.regId,
      };
      let res = await getIsCountdown(param);
      if (!res.data) return;
      let {
        presentTime,
        receiveTime,
        inquiryDuration,
        isSubsequent,
        visitDuration,
        cost,
        patientCloseTime,
      } = res.data;

      if (patientCloseTime != 1) {
        this.isCountdown = true;

        this.usedTime = date.DateDifference(
          receiveTime,
          presentTime,
          inquiryDuration
        );
        if (this.usedTime.usedTime <= 0) {
          this.isCountdown = false;
        }
      } else {
        this.isCountdown = false;
      }
      this.visitDuration = visitDuration;
      this.cost = cost;
      if (isSubsequent == "0") {
        this.isSubsequent = "咨询";
      } else if (isSubsequent == "1") {
        this.isSubsequent = "复诊";
      }
    },

    scrollToBottom() {
      return;
    },
    //聊天页面发送已读回执
    async ack(el, index) {
      // 如果是未读发送已读回执
      if (el.status == "unread") {
        var bodyId = el.id; // 需要发送已读回执的消息id
        var ackMsg = new this.$im.message("read", this.$im.conn.getUniqueId());
        ackMsg.set({
          id: bodyId,
          to: el.from,
        });

        // 如果语音消息 带有mid
        if (el.messType == "voice" && el.ext.mid) {
          this.audioMsg(el);
        } else {
          this.$im.conn.send(ackMsg.body);
        }
        el.status = "read";
        this.$store.commit("updateMessageStatus", el);
      }
      //已读请求接口返回卡片状态
      if (el.ext && el.ext.type == "cf") {
        var cfbusinessCode = el.ext.cfbusinessCode;
        var para = {
          businessCode: cfbusinessCode,
        };
        await getPrescriptionCard(para).then((res) => {
          el.ext.diagName = res.data.diagName;
          el.ext.hzStstus = res.data.hzStstus;
          el.ext.businessId = res.data.businessId;
          el.ext.prescriptionIndate = res.data.prescriptionIndate;
          this.$set(this.oneList, index, el);
        });
      } else if (el.ext && el.ext.type == "lb") {
        let sendId = el.ext.sendId;
        // 判断该量表的状态，是否已填写
        await getQuestionStatus({
          sendId: sendId,
          patientId: this.pageParam.patientId,
        }).then((res) => {
          let isFeedback = res.data.isFeedback; // 是否反馈 0否，1是
          el.ext.isFeedback = isFeedback;
          this.$set(this.oneList, index, el);
        });
      } else if (el.ext && el.ext.type == "zz") {
        let referralId = el.ext.referralId;
        await getReferralRecord({
          referralId: referralId,
        }).then((res) => {
          let isSuccess = res.data.referralStatus;
          el.ext.isSuccess = isSuccess;
          this.$set(this.oneList, index, el);
        });
      } else if (el.ext && el.ext.type == "lisOrder") {
        let businessCode = el.ext.businessCode;
      } else if (el.ext && el.ext.type == "zdyfw") {
        let customBussinessId = el.ext.customBussinessId;
        await findCustomServicePayStatus({
          customBussinessId: customBussinessId,
        }).then((res) => {
          let isSuccess = res.data.status;
          el.ext.isSuccess = isSuccess;
          this.$set(this.oneList, index, el);
        });
      } else if (el.ext && el.ext.type == "blsq") {
        let authorizeId = el.ext.authorizeId;
        await getMedicalAuthorizeStatus({
          authorizeId: authorizeId,
        }).then((res) => {
          let isSuccess = res.data.authorizeStatus;
          el.ext.isSuccess = isSuccess;
          this.$set(this.oneList, index, el);
        });
      } else if (el.ext && el.ext.type == "jcd") {
        let ppiId = el.ext.ppiId;
        await checkPacsAppointStatus(ppiId).then((res) => {
          let { status, appointTime } = res.data;
          if (status) el.ext.orderStatus = status;
          if (appointTime) el.ext.time = appointTime;
          this.$set(this.oneList, index, el);
        });

        // 检验单
      }
      if (el.ext && el.ext.type == "jyd") {
        await checkLisAppointStatus(el.ext.pliId).then((res) => {
          let { status, appointTime } = res.data;
          if (status) el.ext.orderStatus = status;
          if (appointTime) el.ext.time = appointTime;
          this.$set(this.oneList, index, el);
        });
      }
    },
    //返回上一页
    goBack() {
      uni.navigateBack({
        delta: 1,
      });
    },
    //拍照选择视频还是图片
    openPopup() {
      this.$refs.popup.open();
    },
    //关闭弹窗
    closePopup() {
      this.$refs.popup.close();
    },
    // 根据不同参数改变不同面板的显示 表情，录音 功能面板
    changePanelStatus(type) {
      if (type == "emoji") {
        this.emojiShow = true;
        this.recordShow = false;
        this.funPanelShow = false;
      }
      if (type == "addPanel") {
        this.emojiShow = false;
        this.recordShow = false;
        this.funPanelShow = true;
      }
      if (type == "recordShow") {
        this.emojiShow = false;
        this.recordShow = true;
        this.funPanelShow = false;
      }
      if (type == "recordHide" || type == "input") {
        this.emojiShow = false;
        this.recordShow = false;
        this.funPanelShow = false;
      }
      this.scrollToBottom();
      window.scrollTo(0, 0);
    },
    handletouchstart(e) {
      e.preventDefault();
      try {
        e.stopPropagation(); //非IE浏览器
      } catch (e) {
        window.event.cancelBubble = true; //IE浏览器
      }
      this.time = setTimeout(() => {
        this.startRecord(e);
      }, 500); //这里设置定时器，定义长按1000毫秒触发长按事件，时间可以自己改，
      return false;
    },
    handletouchend() {
      clearTimeout(this.time); //清除定时器
      if (this.time != 0) {
        //处理点击时间
        this.endRecord();
      }
      return false;
    },
    handletouchmove() {
      return;
      this.endRecord();
      clearTimeout(this.time); //清除定时器
      this.time = 0;
    },
    //开始录音
    startRecord() {
      let timer;
      let _this = this;
      REC.startRec()
        .then(() => {
          _this.showAnimation = true;
          let n = 0;
          timer = setInterval(() => {
            n += 1;
            if (n >= 59) {
              clearInterval(timer);
              n = 0;
              _this.endRecord();
            }
          }, 1000);
        })
        .catch(() => {
          clearInterval(timer);
          _this.showAnimation = false;
        });
    },
    //结束录音
    async endRecord() {
      this.showAnimation = false;
      try {
        let { blob, duration } = await REC.stopRec();
        // 上传录音内容并发送环信消息
        this.uploadAudio(blob, duration);
      } catch (error) {
        this.showAnimation = false;
      }
    },
    //打开相册
    openPic() {
      let _this = this;
      uni.chooseImage({
        count: 6, //默认9
        sizeType: ["compressed"], //可以指定是原图还是压缩图，默认二者都有
        sourceType: ["album"], //从相册选择
        success: async function (res) {
          if (res.tempFilePaths.length > 0) {
            for (var i = 0; i < res.tempFilePaths.length; i++) {
              let el = res.tempFilePaths[i];
              let file = res.tempFiles[i];
              await _this.upLoadImage(el, file);
            }
          }
        },
      });
    },
    // 选择视频
    openVideo() {
      let _this = this;
      uni.chooseVideo({
        count: 1,
        sourceType: ["camera", "album"],
        success: function (res) {
          // self.src = res.tempFilePath;
          //判断视频大小不能超过10MB
          let isLt2M = res.size / 1024 / 1024 < 10;
          if (!isLt2M) {
            Toast("请上传10M以内的视频");
            return false;
          } else {
            _this.upLoadVideo(res);
          }
        },
      });
    },
    //上传语音 发送语音消息
    uploadAudio(src, duration) {
      if (this.pageParam.ywStatus != "2" && this.surplusTimes > 0) {
        this.updateSurplusTimesFun();
      } else if (this.pageParam.ywStatus != "2" && !this.surplusTimes) {
        Toast("当前不可发送消息");
        return;
      }
      var _this = this;
      var tempFilePath = src;
      var str = _this.$im.config.appkey.split("#");
      uni.uploadFile({
        url: "https://a1.easemob.com/" + str[0] + "/" + str[1] + "/chatfiles",
        filePath: tempFilePath,
        name: "file",
        success: (uploadFileRes) => {
          let data = JSON.parse(uploadFileRes.data);

          var id = _this.$im.conn.getUniqueId(); // 生成本地消息id
          var msg = new _this.$im.message("audio", id); // 创建图片消息
          var option = {
            body: {
              type: "audio/mp3",
              url: data.uri + "/" + data.entities[0].uuid,
              length: duration,
              filename: "audio.mp3",
            },
            ext: {
              patientId: _this.id,
              patientName: _this.pageParam.patientName,
            },
            duration: duration,
            to: _this.docId, // 接收消息对象
            success: function (argument, id) {
              let msg = {
                type: "send",
                from: _this.userId,
                to: _this.docId,
                messType: "voice",
                duration: parseInt(duration),
                time: new Date().getTime(),
                id: id,
                content: data.uri + "/" + data.entities[0].uuid,
                ext: {
                  patientId: _this.id,
                  patientName: _this.pageParam.patientName,
                },
              };
              _this.$store.dispatch("setChatList", msg);
              myJsTools.msgReadFun(msg);
            },
          };
          msg.set(option);
          _this.$im.conn.send(msg.body);
        },
      });
    },
    //上传视频，发送视频消息
    upLoadVideo(res) {
      this.closePopup();
      if (this.pageParam.ywStatus != "2" && this.surplusTimes > 0) {
        this.updateSurplusTimesFun();
      } else if (this.pageParam.ywStatus != "2" && !this.surplusTimes) {
        Toast("当前不可发送消息");
        return;
      }
      var _this = this;
      var tempFilePath = res.tempFilePath;
      var file = res.tempFile;
      var str = _this.$im.config.appkey.split("#");
      // 加载
      uni.showLoading();
      uni.uploadFile({
        url: "https://a1.easemob.com/" + str[0] + "/" + str[1] + "/chatfiles",
        filePath: tempFilePath,
        name: "file",
        // 超时
        timeout: 60000,
        success: (uploadFileRes) => {
          let data = JSON.parse(uploadFileRes.data);

          var id = _this.$im.conn.getUniqueId(); // 生成本地消息id
          var msg = new _this.$im.message("video", id); // 创建图片消息
          var option = {
            body: {
              type: "video",
              url: data.uri + "/" + data.entities[0].uuid,
            },
            ext: {
              patientId: _this.id,
              patientName: _this.pageParam.patientName,
            },
            duration: res.duration,
            to: _this.docId, // 接收消息对象
            success: function (argument, id) {
              let msg = {
                type: "send",
                from: _this.userId,
                to: _this.docId,
                messType: "video",
                time: new Date().getTime(),
                id: id,
                ext: {
                  patientId: _this.id,
                  patientName: _this.pageParam.patientName,
                },
                content: data.uri + "/" + data.entities[0].uuid,
              };
              _this.$store.dispatch("setChatList", msg);
              myJsTools.msgReadFun(msg);
              uni.hideLoading();
            },
            fail: (err) => {
              uni.hideLoading();
            },
          };
          msg.set(option);
          _this.$im.conn.send(msg.body);
        },
        fail: (err) => {
          uni.hideLoading();
          Toast("视频上传失败" + err);
        },
      });
    },
    //上传图片，发送图片消息
    upLoadImage(res, file) {
      return new Promise((resolve) => {
        this.closePopup();
        if (this.pageParam.ywStatus != "2" && this.surplusTimes > 0) {
          this.updateSurplusTimesFun();
        } else if (this.pageParam.ywStatus != "2" && !this.surplusTimes) {
          return;
        }
        var _this = this;
        var tempFilePaths = res;
        var str = _this.$im.config.appkey.split("#");
        uni.uploadFile({
          url: "https://a1.easemob.com/" + str[0] + "/" + str[1] + "/chatfiles",
          filePath: tempFilePaths,
          name: "file",
          success: (uploadFileRes) => {
            _this.sendImgInfo(uploadFileRes, file).then((res) => {
              resolve();
            });
          },
        });
      });
    },
    sendImgInfo(uploadFileRes, file) {
      let _this = this;
      return new Promise((resolve) => {
        let data = JSON.parse(uploadFileRes.data);
        var id = _this.$im.conn.getUniqueId(); // 生成本地消息id
        var msg = new _this.$im.message("img", id); // 创建图片消息
        var option = {
          body: {
            type: "file",
            url: data.uri + "/" + data.entities[0].uuid,
            length: file.size,
            filename: file.name,
            filetype: file.type,
          },
          ext: {
            patientId: _this.id,
            patientName: _this.pageParam.patientName,
          },
          to: _this.docId, // 接收消息对象
          success: function (argument, id) {
            let msg = {
              type: "send",
              from: _this.userId,
              id: id,
              to: _this.docId,
              time: new Date().getTime(),
              ext: {
                patientId: _this.id,
                patientName: _this.pageParam.patientName,
              },
              messType: "image",
              content: data.uri + "/" + data.entities[0].uuid,
            };
            _this.$store.dispatch("setChatList", msg);
            myJsTools.msgReadFun(msg);
            resolve();
          },
        };
        msg.set(option);
        _this.$im.conn.send(msg.body);
      });
    },
    // 选择表情
    selectEmoji(item, key) {
      let path = "http://llootong.cn/cloud/hisImg/static/faces/" + item;
      let Img = `<img class='text-emoji ${key}' id="${key}" style='width:22px;height:22px;display: inline-block;vertical-align: middle;' name='${item}' src='${path}' />`; // img是要插入的图片表情
      uni
        .createSelectorQuery()
        .select("#editor")
        .context((res) => {
          this.editorCtx = res.context;

          this.editorCtx.setContents({
            html: this.inputVal + Img,
          });
          this.inputVal = this.inputVal + Img;
          this.sendVal += key;
        })
        .exec();
    },
    // 预览图片
    _previewImage(image) {
      var imgArr = [];
      imgArr.push(image);
      //预览图片
      uni.previewImage({
        urls: imgArr,
        current: imgArr[0],
      });
    },
    // 转换图片表情
    customEmoji(value) {
      return `<image src="http://llootong.cn/cloud/hisImg/static/faces/${value}" style="width:30px;height:30px"></image>`;
    },
    // 收到的消息处理之后进行显示，文字和表情
    renderTxt(txt = "") {
      let rnTxt = [];
      let match = null;
      const regex = /(\[.*?\])/g;
      let start = 0;
      let index = 0;
      while ((match = regex.exec(txt))) {
        index = match.index;
        if (index > start) {
          rnTxt.push(txt.substring(start, index));
        }
        if (match[1] in this.$im.Emoji.map) {
          const v = this.$im.Emoji.map[match[1]];
          rnTxt.push(this.customEmoji(v));
        } else {
          rnTxt.push(match[1]);
        }
        start = index + match[1].length;
      }
      rnTxt.push(txt.substring(start, txt.length));
      return rnTxt.toString().replace(/,/g, "");
    },
    // 改变数据
    changeInput(item) {
      this.inputVal = item.detail.html;
      this.sendVal = "";
      let html = item.detail.delta.ops;

      for (let i = 0; i < html.length; i++) {
        const ele = html[i];
        if (ele.attributes && ele.attributes.class) {
          this.sendVal = this.sendVal + ele.attributes.class.split(" ")[1];
        } else {
          this.sendVal = this.sendVal + ele.insert;
        }
      }
    },
    onWithdraw(item) {
      let that = this;
      let option = {};
      // 用户消息撤回
      if (item.type == "send" && item.content) {
        option = {
          // 要撤回消息的消息 ID。
          mid: item.mid || item.id,
          // 消息接收方：单聊为对方用户 ID，群聊和聊天室分别为群组 ID 和聊天室 ID。
          to: item.to,
          // 会话类型：单聊、群聊和聊天室分别为 `singleChat`、`groupChat` 和 `chatRoom`。
          chatType: "singleChat",
          // 撤回消息自定义字段。
          ext: "item.ext",
          success(e) {
            cancelMessage({ msgId: item.mid }).then((res) => {
              item.type = "withdraw";
              that.$store.dispatch("setRecallMsg", item);
            });
          },
          fail(e) {
            console.log("撤回失败", e);
            uni.showToast({ title: "撤回失败:" + e.reason, icon: "none" });
          },
        };
      }
      // 医生消息患者不可以撤回
      if (item.type == "receive" && item.content && item.from != "admin") {
        return;
      }
      this.$im.conn.recallMessage(option);
    },
    onLongPress(item, index) {
      // 根据item.time时间戳判断是否在2分钟内
      let time = new Date().getTime() - item.time;
      if (time > 120000) {
        return;
      }
      // 医生消息患者不可以撤回
      if (item.type == "receive" && item.content && item.from != "admin") {
        return;
      }
      item.isLongPress = !item.isLongPress;
      this.$set(this.list, index, item);
    },
    scrollMsg(event){
      // 获取当前滚动条的位置
      const scrollTop = event.detail.scrollTop;
      console.log(event)
      // 判断滚动条是否从顶部开始滚动
      if (scrollTop === 0 && this.lastScrollTop !== 0) {
        // 滚动条到达顶部
        console.log('滚动条到达顶部');
        // 可以在这里执行需要的操作
      }
    },
    // 点击页面空白处关闭所有面板
    changePanel() {
      this.funPanelShow = false;
      this.emojiShow = false;
      this.list.forEach((item, index) => {
        item.isLongPress = false;
        this.$set(this.list, index, item);
      });
      this.scrollToBottom();
    },
    // 语音消息发送已读
    audioMsg(item) {
      let id = this.$im.conn.getUniqueId();
      let msg = new this.$im.message("cmd", id);
      msg.set({
        to: item.from, //接收消息对象
        action: item.ext.mid, //用户自定义，cmd消息必填
        ext: {
          patientId: item.ext.patientId,
        }, //用户自扩展的消息内容（群聊用法相同）
        success: function (id, serverMsgId) {}, //消息发送成功回调
        fail: function (e) {},
      });
      this.$im.conn.send(msg.body);
    },
    //发送文字消息
    sendMsg() {
      console.log("查看this.pageParam.ywStatus", this.pageParam.ywStatus);
      if (this.pageParam.ywStatus != "2" && this.surplusTimes > 0) {
        this.updateSurplusTimesFun();
      } else if (this.pageParam.ywStatus != "2" && !this.surplusTimes) {
        return;
      }

      let _this = this;
      let id = this.$im.conn.getUniqueId();
      let msg = new this.$im.message("txt", id);
      console.log("查看message", msg);
      msg.set({
        msg: this.sendVal,
        from: _this.userId,
        to: _this.docId,
        roomType: false,
        ext: {
          patientId: _this.id,
          patientName: _this.pageParam.patientName,
        },
        success(id, serverMsgId) {
          let msg = {
            type: "send",
            from: _this.userId,
            ext: {
              patientId: _this.id,
              patientName: _this.pageParam.patientName,
            },
            time: new Date().getTime(),
            to: _this.docId,
            messType: "text",
            content: _this.sendVal,
            mid: serverMsgId,
          };
          _this.$store.dispatch("setChatList", msg);
          myJsTools.msgReadFun(msg);
          uni
            .createSelectorQuery()
            .select("#editor")
            .context((res) => {
              this.editorCtx = res.context;
              this.editorCtx.setContents({
                html: "",
              });
              _this.sendVal = "";
              _this.inputVal = "";
            })
            .exec();
          // disp.fire('em.chat.sendSuccess', id, me.userMessage);
        },
        error(err) {
          console.log("查看错误信息", err);
        },
      });
      try {
        this.$im.conn.send(msg.body);
      } catch (e) {
        console.log("查看环信发送拦截错误", e);
      }

      // this.$im.conn.send(msg.body);
    },
    //慢病续方跳转
    async sendContinue() {
      // uni.navigateTo({
      //   url: "../continuedPrescription/myFormula",
      // });
      await getDocPatientBlackList({
        patientId: this.id,
        docId: this.docId,
      });
      const res = await queryQuickPrescription({ regId: this.pageParam.regId });
      let _this = this;
      let id = this.$im.conn.getUniqueId();
      let msg = new this.$im.message("txt", id);
      let strText = "患者给你发了一张快速续方，请点下方卡片击查看";
      msg.set({
        msg: strText,
        from: _this.userId,
        to: _this.docId,
        roomType: false,
        ext: {
          type: "ksxf",
          businessId: _this.pageParam.regId,
          diagName: res.data.quickPrescriptionDiags[0].diagName,
          hzStstus: "0",
          patientId: _this.id,
        },
        success(id, serverMsgId) {
          //
          let msg = {
            type: "send",
            from: _this.userId,
            ext: {
              type: "ksxf",
              businessId: _this.pageParam.regId,
              diagName: res.data.quickPrescriptionDiags[0].diagName,
              hzStstus: "0",
              patientId: _this.id,
            },
            time: new Date().getTime(),
            to: _this.docId,
            messType: "text",
            content: "",
            mid: serverMsgId,
          };
          _this.$store.dispatch("setChatList", msg);
          myJsTools.msgReadFun(msg);
        },
        fail(e) {},
      });
      try {
        this.$im.conn.send(msg.body);
        Toast("已向医生申请续方，请耐心等待医生响应");
        // setTimeout(() => {
        //   uni.navigateBack({
        //     delta: 2,
        //   });
        // }, 1500);
      } catch (e) {}
    },
    //卡片慢病需方跳转
    sendContinueUrl(item) {
      uni.navigateTo({
        url:
          "../continuedPrescription/myFormulaDetail?businessId=" +
          item.ext.businessId +
          "&docId=" +
          this.docId +
          "&patientId=" +
          this.patientInfo.patientId +
          "&look=1",
      });
    },
    //跳转医生信息
    docInfoFun() {
      uni.navigateTo({
        url:
          "/pages/register/docHomePage/index?docId=" +
          this.pageParam.docId +
          "&action=" +
          "index",
      });
    },
  },
};
</script>

<style scoped lang="scss">
// 外层容器
.chat_detail {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  position: fixed;
}

// 时间间隔
.chat_time {
  @include flex;
  font-size: 28upx;
  margin-bottom: 20upx;

  text {
    padding: 4upx 10upx;
    color: #999;
    background-color: #eee;
    border-radius: 6upx;
  }
}

// 顶部导航
.nav_top_fixed {
  flex: none;
  width: 100%;
  height: 44px;
  @include flex;
  flex-direction: column;
  position: sticky;
  top: 0;
  z-index: 99;
  color: #333333;
  background-color: #fff;
  box-sizing: border-box;

  .docName {
    font-size: 32rpx;
    font-weight: 400;
  }

  .info {
    font-size: 24rpx;
    color: rgba(102, 102, 102, 1);
  }

  .nav_top_fixed_left {
    width: 50rpx;
    height: 50rpx;
    position: absolute;
    left: 15px;
    @include flex;

    .backIcon {
      width: 22rpx;
      height: 44rpx;
    }
  }
}

// 顶部提示
.nav_tips {
  flex: none;
  width: 100%;
  height: 80upx;
  @include flex;
  position: sticky;
  top: 44px;
  z-index: 99;
  background-color: #fff;

  .tips_text {
    color: #ffffff;
    font-size: 24rpx;
    border-radius: 320px;
    background: rgba(67, 85, 120, 0.6);
    padding: 6rpx 24rpx;
  }

  .closeIcon {
    width: 32rpx;
    height: 32rpx;
  }

  .theme {
    color: rgba(77, 97, 140, 1);
  }
}

// 聊天内容
.chatBox {
  padding: 20rpx;
  flex: 1;
  width: 100%;
  transition: all 0.5s;
  overflow-y: scroll;
  position: relative;
  box-sizing: border-box;

  #msgDiv {
    background-color: #fafafa;
    font-family: -apple-system;
    overflow-x: hidden;
    box-sizing: border-box;
    font-family: "-apple-system", "Helvetica Neue", "Roboto", "Segoe UI",
      sans-serif;
  }

  .chatbox_scroll {
    height: 100%;
    overflow-y: scroll;
  }
}

// 底部
.footer {
  flex: none;
  position: sticky;
  bottom: 0;
  width: 100%;
  z-index: 99;
  background: #f0f0f0;
  box-sizing: border-box;
}

.operateView {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx 0rpx 24rpx;
  margin-bottom: 10px;
  image {
    width: 44rpx;
    height: 44rpx;
  }

  .uni-input {
    background: #ffffff;
    margin: 0rpx 16rpx;
    width: 70%;
    border-radius: 40rpx;
    padding: 10rpx 20rpx;
  }

  .addBtn {
    margin-left: 34rpx;
  }
}

.functionPanel {
  display: flex;
  padding: 20rpx 0;

  image {
    width: 128rpx;
    height: 128rpx;
  }

  .panel {
    width: 33%;
    text-align: center;
    color: #666666;
    font-size: 22rpx;
  }
}

.ql-container {
  display: block;
  position: relative;
  box-sizing: border-box;
  font-size: 28rpx;
  -webkit-user-select: text;
  user-select: text;
  outline: none;
  overflow: hidden;
  width: 100%;
  height: 64rpx;
  min-height: 64rpx;
  display: flex;
  align-items: center;
  line-height: 64rpx;
  padding-top: 5px;
}

.emoji {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;

  image {
    width: 60rpx;
    height: 60rpx;
    padding-left: 12rpx;
    padding-top: 12rpx;
  }
}

.sendBtn {
  @include bg_theme;
  width: 120rpx;
  height: 64rpx;
  font-size: 28rpx;
  color: #ffffff;
  @include flex;
  border-radius: 16rpx;
  margin-left: 8rpx;
}

.overLay {
  position: absolute;
  right: 24rpx;
  top: -30rpx;

  .passImg {
    width: 50rpx;
    height: 82rpx;
  }
}

.recordDiv {
  position: absolute;
  left: 50%;
  top: 40%;
  transform: translate(-50%, -50%);
  background: #ffffff;
  display: flex;
  padding: 84rpx;
  border-radius: 16rpx;
  border: 0.5px solid rgba(0, 0, 0, 0.08);
}

.user_name {
  flex: 1;
  text-align: center;
  color: #333333;
  font-size: 36rpx;
}

image {
  will-change: transform;
}

// 消息撤回
.chat-center {
  clear: both;
  line-height: 36rpx;
  text-align: center;
  font-size: 22rpx;
  padding: 30rpx 0;

  .tipsText {
    color: #ffffff;
    background: rgba(0, 0, 0, 0.16);
    border-radius: 20rpx;
    padding: 0 18rpx;
    line-height: 36rpx;
  }

  .editMsg {
    @include font_theme;
    margin-left: 16rpx;
    font-size: 28rpx;
  }
}

.msgDiv button {
  padding: 40rpx;
}

.recordBtn {
  height: 100rpx;
  background: #ffffff;
  line-height: 100rpx;
  color: #999;
  font-size: 28rpx;
  text-align: center;
  margin: 0rpx 16rpx;
  width: 70%;
  border-radius: 40rpx;
}

.recordDiv {
  position: fixed;
  left: 50%;
  top: 40%;
  transform: translate(-50%, -50%);
  background: #ffffff;
  display: flex;
  padding: 84rpx;
  border-radius: 16rpx;
  border: 0.5px solid rgba(0, 0, 0, 0.08);
}

::v-deepuni-button:after {
  border: none;
}

.countdown {
  background: rgba(232, 250, 244, 1);
  color: #333333;
  font-size: 28rpx;
  line-height: 40rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;
  .successIcon {
    width: 46rpx;
    height: 36rpx;
    margin-right: 16rpx;
  }
  .closeIcon {
    width: 32rpx;
    height: 32rpx;
    margin-left: 16rpx;
  }
}

::v-deep.uni-countdown__splitor {
  padding: 0px;
}

::v-deep.uni-countdown__number {
  margin: 0px;
}

::v-deep.uni-video-container {
  z-index: 1;
}

.waitJz {
  background: #fdf8db;
  color: #333333;
  font-size: 28rpx;
  padding: 24rpx 0;
  text-align: center;
}

.endWz {
  background: #ffffff;
  .tips {
    background: #fdf8db;
    color: #333333;
    font-size: 28rpx;
    text-align: center;
    padding: 20rpx 0;
  }
  .tabs {
    display: flex;

    text-align: center;
    .tab {
      padding: 24rpx 0;
      margin: 0 118rpx;
      color: #666666;
      font-size: 34rpx;
    }
    .active {
      @include font_theme;
      @include border_theme(3px, bottom);
    }
  }
}

.complaintPopup {
  width: 100%;
  z-index: 100;
  ::v-deep.uni-popup__wrapper-box {
    width: 80%;
  }
}

.complaintView {
  background: #ffffff;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #333333;
  position: relative;
  padding: 80rpx 38rpx 0;
  .title {
    text-align: center;
    font-weight: 600;
  }
  .uni-list-cell-pd {
    display: flex;
    align-items: center;
  }
  .btns {
    display: flex;
    padding: 36rpx 0;

    view {
      width: 250rpx;
      height: 68rpx;
      @include flex;
      background: #ffffff;
      border-radius: 46rpx;
      font-weight: 600;
    }
    .cancel {
      @include font_theme;
      @include border_theme;
    }
    .commit {
      @include bg_theme;
      margin-left: 40rpx;
      color: #ffffff;
    }
  }
  .complaintLogo {
    width: 160rpx;
    height: 160rpx;
    position: absolute;
    top: -10%;
    left: 35%;
  }
  ::v-deep.uni-list-cell {
    margin-top: 36rpx;
  }
  ::v-deepuni-textarea {
    height: 40rpx;
    padding: 24rpx;
    width: 90%;
  }
  .qtReason {
    border: 1px solid #ebebeb;
    margin-top: 10rpx;
  }
  .uploadImg {
    width: 120rpx;
    height: 120rpx;
    margin-top: 14rpx;
    margin-left: 22rpx;
    margin-bottom: 22rpx;
  }
}

// 新消息提示
.newInfoTips {
  @include font_theme;
  font-size: 26rpx;
  position: sticky;
  bottom: 0;
  width: auto;
  // right: 32rpx;
  float: right;
  display: inline-flex;
  background: #ffffff;
  padding: 16rpx 28rpx;
  border-radius: 34rpx;
  border: 0.5px solid rgba(0, 0, 0, 0.08);

  text {
    color: #e7e7e7;
    margin: 0 16rpx 0 8rpx;
  }

  .downImg,
  .closeTip {
    width: 32rpx;
    height: 32rpx;
  }
}

.selPicPopup {
  z-index: 999999;
  view {
    background: #ffffff;
    text-align: center;
    padding: 32rpx 0rpx;
    border-bottom: 0.2px solid #e0e0e0;
    color: #333333;
    font-size: 28rpx;
  }
}
.assignDoctorsTime {
  width: 100%;
  display: flex;
  justify-content: center;

  background: white;
  color: red !important;
  ::v-deep .uni-countdown__number {
    color: red !important;
  }
  ::v-deep .uni-countdown__splitor {
    color: red !important;
  }
}
.emojiBox {
  min-height: 150px;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  .emoji-item {
    box-sizing: border-box;
    padding: 7px;
    font-size: 21px;
  }
}

.m-item {
  position: relative;
}
.cancleW{
  width: 80%;
  text-align: center;
  margin: auto;
  line-height: 30px;
  border:  1px solid #d1bafb;
  border-radius: 20px;
  margin-top: 10px;
  margin-bottom: 10px;
  color:black;
}
</style>
