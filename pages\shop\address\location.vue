<template>
  <div class="location">
    <!-- 定位 -->
    <div class="top">
      <p>{{ city }}</p>
      <button @click="toMap">
        <uni-icons type="location-filled" size="14" color="#14a0e6"></uni-icons>
        <text>重新定位</text>
      </button>
    </div>

    <!-- 收货地址 -->
    <div class="address" v-if="list.length">
      <p class="title">我的收货地址</p>

      <div class="list">
        <uni-swipe-action>
          <uni-swipe-action-item
            :right-options="options"
            v-for="item in list"
            :key="item.addressId"
            @click="bindClick($event, item)"
          >
            <div class="item" @click.stop="setCity(item)">
              <div class="info">
                <span class="label" v-show="item.isDefault == '1'">默认</span>
                <span class="label" v-if="item.lableName">{{
                  item.lableName
                }}</span>
                <span> {{ item.deliveryName }}</span>
                <span>{{ item.telNo }}</span>
              </div>

              <div class="detail">
                {{ item.addressArea }} {{ item.addressDetail }}
              </div>
            </div>
          </uni-swipe-action-item>
        </uni-swipe-action>
      </div>
    </div>

    <div class="empty" v-if="!list.length">
      <image
        src="/static/images/index/address_emity.png"
        mode="widthFix"
      ></image>
      <span>您还没有收货地址，赶快添加一个吧</span>
    </div>

    <!-- 底部按钮 -->
    <FooterButton @click="toAdd">新增收货地址</FooterButton>
  </div>
</template>

<script>
/*
  Author: 王可 (<EMAIL>)
  location.vue (c) 2021
  Desc: 定位
  Created:  2021/11/2上午9:30:12
  Modified: 2021/12/17下午2:57:45
*/
import { uniSwipeAction, uniSwipeActionItem } from '@dcloudio/uni-ui';

import FooterButton from '@/components/footer_button/button.vue';

import { getCityByLocation } from '../map';


import { findAddressByUserId, dicdeliveryaddressDelete } from '@/api/address';

export default {
  name: 'Location',
  components: {
    FooterButton,
    uniSwipeAction,
    uniSwipeActionItem,
  },
  data() {
    return {
      // 当前城市
      city: '',
      list: [],
      options: [
        {
          text: '删除',
          style: {
            backgroundColor: '#FF5050',
          },
        },
      ],
    };
  },
  onLoad({ city }) {
    this.city = city;
  },
  onShow() {
    this.getAddressList();
  },
  methods: {
    // 左滑点击
    bindClick({ index }, item) {
      const that = this;
      // 删除
      uni.showModal({
        title: '提示',
        content: '确定删除吗？',
        success(res) {
          if (res.confirm) {
            that.delAddress(item.ddaId);
          }
        },
      });
    },
    async delAddress(ddaId) {
      await dicdeliveryaddressDelete({
        ddaId,
      });
      this.getAddressList();
    },
    //获取收货地址列表
    async getAddressList() {
      let res = await findAddressByUserId({
        userId: uni.getStorageSync('userId'),
        isDefaul: '',
      });
      this.list = res.data;
    },
    // 去新增地址
    toAdd() {
      uni.navigateTo({
        url: '/pages/address/newAddress',
      });
    },
    // 选择地图
    toMap() {
      uni.navigateTo({
        url: '/pages/address/selectAddress?act=shop',
      });
    },
    // 设置经纬度
    async setCity(item) {
      const { addressArea, addressDetail, latitude, longitude } = item;
      let { province } = await getCityByLocation(latitude + ',' + longitude);
      uni.setStorageSync('shop_city', {
        city: addressArea + '' + addressDetail,
        lat: latitude,
        lng: longitude,
        province,
      });
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss" scoped>
.location {
  padding-bottom: 128rpx;

  .top {
    height: 90rpx;
    padding-left: 24rpx;
    @include flex(lr);
    background-color: #fff;
    border-bottom: 1px solid #f5f5f5;

    p {
      font-size: 32rpx;
      flex: 1;
      @include hide;
    }

    button {
      flex: none;
      font-size: 28rpx;
      @include font_theme;

      text {
        margin-left: 10rpx;
      }
    }
  }

  .address {
    padding: 0 24rpx;
    background-color: #fff;

    .title {
      font-size: 28rpx;
      line-height: 80rpx;
    }

    .list {
      .item {
        flex: 1;
        padding: 24rpx 0;
        border-bottom: 1px solid #f5f5f5;

        .info {
          margin-bottom: 10rpx;

          span {
            font-size: 28rpx;
            margin-right: 24rpx;

            &.label {
              display: inline-block;
              height: 32rpx;
              padding: 0 6rpx;
              border-radius: 4rpx;
              background-color: #666;
              font-size: 22rpx;
              color: #fff;
            }
          }
        }
      }

      .detail {
        font-size: 28rpx;
        color: #999;
        line-height: 40rpx;
      }
    }
  }

  .empty {
    width: 100%;
    height: 70vh;
    @include flex;
    flex-direction: column;

    image {
      width: 60%;
      margin-bottom: 20rpx;
    }

    span {
      font-size: 28rpx;
      color: #666;
    }
  }
}
</style>
