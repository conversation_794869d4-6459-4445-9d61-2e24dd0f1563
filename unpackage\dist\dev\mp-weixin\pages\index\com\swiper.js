"use strict";
const common_vendor = require("../../../common/vendor.js");
const TITLE = () => "../../inspect/com/itemTitle.js";
const DOC = () => "./doc.js";
const _sfc_main = {
  components: {
    TITLE,
    DOC
  },
  props: {
    list: {
      type: Array,
      default: () => []
    }
  }
};
if (!Array) {
  const _component_TITLE = common_vendor.resolveComponent("TITLE");
  const _component_DOC = common_vendor.resolveComponent("DOC");
  (_component_TITLE + _component_DOC)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.p({
      title: "推荐医生"
    }),
    b: common_vendor.f($props.list, (item, index, i0) => {
      return {
        a: "6757a06a-1-" + i0,
        b: common_vendor.p({
          item
        }),
        c: index
      };
    })
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-6757a06a"]]);
wx.createComponent(Component);
