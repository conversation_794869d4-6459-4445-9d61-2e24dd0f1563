<template>
  <view class="his">
    <view class="warp_cont">
      <view class="searchView">
        <uni-search-bar
          :radius="100"
          cancelButton="auto"
          bgColor="#FFF"
          maxlength="20"
          placeholder="输入聊天记录进行查找"
          @input="searchChat"
        ></uni-search-bar>
        <image
          src="../../static/images/chat/my.png"
          class="myIcon"
          @tap="openMyPage"
        ></image>
      </view>
      <view class="tabs">
        <view
          :class="active == 0 ? 'tab active' : 'tab'"
          @click="changeActive(0)"
          >全部</view
        >
        <view
          :class="active == 1 ? 'tab active' : 'tab'"
          @click="changeActive(1)"
          >图片</view
        >
      </view>
      <view class="historyDiv" v-if="active == 1">
        <view v-for="(item, index) in imgHisList" :key="index">
          <view class="dateTitle">{{ item.dateTime }}</view>
          <view class="imgDiv">
            <image
              style="width: 24%; height: 95px"
              :src="src"
              v-for="(src, srcIndex) in item.imgList"
              class="historyImg"
              @tap="_previewImage(item.imgList)"
              :key="srcIndex"
              :style="
                (srcIndex + 1) % 4 != 0
                  ? 'margin-right:5px'
                  : 'margin-right:0px'
              "
            ></image>
          </view>
        </view>
      </view>

      <view class="chatBox" v-if="active == 0 && list.length">
        <scroll-view
          :scroll-top="scrollTop"
          scroll-y="true"
          class="scroll-Y"
          id="msgDiv"
          ref="msgDiv"
          @scroll="scrollView"
        >
          <view
            v-for="(item, index) in list"
            class="m-item"
            :key="index"
            :id="item.id || item.mid"
          >
            <!-- 医生消息 -->
            <view
              class="chat-sender"
              v-if="item.type == 'receive' && item.content"
            >
              <!-- 文本模块 -->
              <MsgText
                @head="docInfoFun"
                :imgUrl="docImg"
                :content="renderTxt(item.content)"
                v-if="
                  item.messType == 'text' &&
                  !item.ext.cfbusinessCode &&
                  !item.ext.czbType &&
                  !item.ext.groupInfoId &&
                  !item.ext.regId &&
                  !item.ext.sendId &&
                  !item.ext.plsId &&
                  !item.ext.plmId &&
                  !item.ext.referralId &&
                  !item.ext.customBusinessI &&
                  !item.ext.authorizeId &&
                  !item.ext.ppiId
                "
              />

              <!-- 处方建议 -->
              <Proposal
                @head="docInfoFun"
                :imgUrl="docImg"
                :diagName="item.ext.diagName"
                v-if="
                  item.messType == 'text' &&
                  item.ext.cfbusinessCode &&
                  item.ext.isSubsequent == '0'
                "
                @click="disabledClickCard()"
              />

              <!-- 处方 -->
              <Proposal
                @head="docInfoFun"
                :imgUrl="docImg"
                :diagName="item.ext.diagName"
                prescription
                :day="item.ext.prescriptionIndate"
                :hzStstus="item.ext.hzStstus"
                v-if="
                  item.messType == 'text' &&
                  item.ext.cfbusinessCode &&
                  item.ext.isSubsequent == '1'
                "
                @click="disabledClickCard()"
              />

              <!-- 门诊加号 -->
              <AddDiag
                @head="docInfoFun"
                :imgUrl="docImg"
                :docName="pageParam.docName"
                @click="disabledClickCard()"
                v-if="item.messType == 'text' && item.ext.czbType"
              />

              <!-- 量表 -->
              <Gauge
                @head="docInfoFun"
                :imgUrl="docImg"
                :title="item.ext.title"
                :isFeedback="item.ext.isFeedback"
                :status="item.ext.isSuccess"
                @click="disabledClickCard()"
                v-if="item.messType == 'text' && item.ext.sendId"
              />

              <!-- 服务 -->
              <Serve
                @head="docInfoFun"
                :imgUrl="docImg"
                :title="item.ext.customBussTitle"
                :info="item.ext.customBussMemo"
                :isSuccess="item.ext.isSuccess"
                @click="disabledClickCard()"
                v-if="item.messType == 'text' && item.ext.customBussinessId"
              />

              <!-- 群发 -->
              <Group
                @head="docInfoFun"
                :imgUrl="docImg"
                :title="item.ext.title"
                @click="disabledClickCard()"
                v-if="item.messType == 'text' && item.ext.groupInfoId"
              />

              <!-- 检查单 -->
              <Inspection
                @head="docInfoFun"
                :imgUrl="docImg"
                :title="item.ext.ppiDiag"
                :info="item.content"
                @click="disabledClickCard()"
                v-if="item.messType == 'text' && item.ext.ppiId"
              />

              <!-- 检验单 -->
              <Inspection
                @head="docInfoFun"
                :imgUrl="docImg"
                isInspect
                @click="disabledClickCard()"
                v-if="item.messType == 'text' && item.ext.plmId"
              />

              <!-- 随访 -->
              <Follow
                @head="docInfoFun"
                :imgUrl="docImg"
                :title="item.ext.title"
                @click="disabledClickCard()"
                v-if="item.messType == 'text' && item.ext.plsId"
              />

              <!-- 咨询小结 -->
              <Consulting
                @head="docInfoFun"
                :imgUrl="docImg"
                @click="disabledClickCard()"
                v-if="item.messType == 'text' && item.ext.type == 'zxxj'"
              />

              <!-- 转诊 -->
              <Referral
                @head="docInfoFun"
                :imgUrl="docImg"
                @click="disabledClickCard()"
                :docName="item.ext.docName"
                :status="item.ext.isSuccess"
                v-if="item.messType == 'text' && item.ext.referralId"
              />

              <!-- 病例授权 -->
              <Author
                @head="docInfoFun"
                :imgUrl="docImg"
                :status="item.ext.isSuccess"
                @click="disabledClickCard()"
                v-if="item.messType == 'text' && item.ext.authorizeId"
              />

              <!-- 图片消息 -->
              <ChatImg
                @head="docInfoFun"
                :imgUrl="docImg"
                :imgSrc="item.content"
                v-if="item.messType == 'image'"
              />

              <!-- 音频消息 -->
              <CharAudio
                @head="docInfoFun"
                :imgUrl="docImg"
                :audioSrc="item.content"
                :time="item.duration"
                @click="audioPlay"
                :index="index"
                :play="item.play"
                v-if="item.messType == 'voice'"
              />

              <!-- 视频消息 -->
              <CharVideo
                @head="docInfoFun"
                :imgUrl="docImg"
                :videoSrc="item.content"
                :id="item.id"
                :poster="item.thumbnailLocalPath"
                v-if="item.messType == 'video'"
              />

              <!-- 视频邀请 -->
              <VideoInvitation
                @head="docInfoFun"
                :imgUrl="docImg"
                :ext="item.ext"
                :content="item.content"
                v-if="item.ext && (item.ext.conferenceId || item.ext.channel)"
              />
            </view>

            <!-- 患者消息 -->
            <view
              class="chat-receiver"
              v-if="item.type == 'send' && item.content"
            >
              <!-- 文字消息 -->
              <UserText
                :imgUrl="patientImg"
                @click="openMedicalRecord"
                :content="renderTxt(item.content)"
                v-if="
                  item.messType == 'text' &&
                  !item.ext.cfbusinessCode &&
                  !item.ext.czbType &&
                  !item.ext.groupInfoId &&
                  !item.ext.regId &&
                  !item.ext.sendId &&
                  !item.ext.plsId &&
                  !item.ext.plmId &&
                  !item.ext.referralId &&
                  !item.ext.customBussinessId &&
                  item.ext.type != 'mbxf'
                "
              />

              <!-- 图片消息 -->
              <UserImg
                @click="openMedicalRecord"
                :imgUrl="patientImg"
                :imgSrc="item.content"
                v-if="item.messType == 'image'"
              />

              <!-- 音频消息 -->
              <UserAudio
                @head="openMedicalRecord"
                :imgUrl="patientImg"
                :audioSrc="item.content"
                :time="item.duration"
                @click="audioPlay"
                :index="index"
                :play="item.play"
                v-if="item.messType == 'voice'"
              />

              <!-- 视频消息 -->
              <UserVideo
                @head="openMedicalRecord"
                :imgUrl="patientImg"
                :videoSrc="item.content"
                v-if="item.messType == 'video'"
                :id="item.id"
                :poster="item.thumbnailLocalPath"
              />

              <!-- 慢病续方 -->
              <Continue
                @head="openMedicalRecord"
                :imgUrl="patientImg"
                :title="item.ext.diagName"
                v-if="item.messType == 'text' && item.ext.type == 'mbxf'"
                @click="disabledClickCard()"
              />
            </view>

            <!-- 撤回消息 -->
            <view class="chat-center" v-if="item.type == 'withdraw'">
              <text class="tipsText">你撤回了一条消息</text>
              <text class="editMsg" v-if="item.messType == 'text'"
                >重新编辑</text
              >
            </view>

            <!-- 撤回消息 -->
            <view class="chat-center" v-if="item.type == 'reWithdraw'">
              <text class="tipsText">对方撤回了一条消息</text>
            </view>

            <view
              class="chat-center"
              v-if="item.ext.type == 'shcf' && item.type == 'defaultMsg'"
            >
              <span class="tipsText">{{ item.content }}</span>
              <span
                class="editMsg"
                v-if="item.messType == 'text'"
                @click="disabledClickCard('cf', item)"
                >查看</span
              >
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 空记录 -->
      <view class="empt_list" v-if="!list.length && active == 0">
        <image src="/static/images/index/box_empty.png" mode="widthFix"></image>
        <text>暂无聊天记录</text>
      </view>
      <!-- 图片空状态 -->
      <view class="empt_list" v-if="!imgHisList.length && active == 1">
        <image src="/static/images/index/box_empty.png" mode="widthFix"></image>
        <text>暂无图片记录</text>
      </view>
    </view>
    <uni-popup
      ref="complaintPopup"
      type="center"
      class="complaintPopup tipsPopup"
    >
      <view class="complaintView">
        <view class="content">
          <div>请到聊天界面点击卡片进行查看</div>
        </view>
        <view class="btns">
          <button @click="closePopup">确定</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { Toast } from "@/common/js/pay.js";
import date from "@/utils/date";
import {
  findCustomServicePayStatus,
  getMedicalAuthorizeStatus,
  getPrescriptionCard,
  getProPacsStatus,
  getQuestionStatus,
  getReferralRecord,
} from "../../api/chat";
import avatar from "../../static/images/docHead.png";
import Tools from "@/utils/myJsTools";
import myJsTools from "@/common/js/myJsTools.js";
import { getPatientPrescriptionOrderInfo } from "../../api/cf";
import { findPatientByPatientId } from "../../api/chatCardDetail";

// 文本消息
import MsgText from "./doc_components/text.vue";
// 处方建议
import Proposal from "./doc_components/proposal.vue";
// 门诊加号
import AddDiag from "./doc_components/addDiagnosis.vue";
// 量表
import Gauge from "./doc_components/gauge.vue";
// 服务
import Serve from "./doc_components/serve.vue";
// 群发
import Group from "./doc_components/groupMsg.vue";
// 检查单
import Inspection from "./doc_components/inspection.vue";
// 随访
import Follow from "./doc_components/followUp.vue";
// 咨询小结
import Consulting from "./doc_components/consulting.vue";
// 转诊
import Referral from "./doc_components/referral.vue";
// 病例授权
import Author from "./doc_components/author.vue";
// 图片消息
import ChatImg from "./doc_components/img.vue";
// 音频消息
import CharAudio from "./doc_components/audio.vue";
// 视频消息
import CharVideo from "./doc_components/video.vue";
// 视频邀请
import VideoInvitation from "./doc_components/pv.vue";

// 用户文字
import UserText from "./user_components/text.vue";
// 用户图片
import UserImg from "./user_components/img.vue";
// 用户音频
import UserAudio from "./user_components/audio.vue";
// 用户视频
import UserVideo from "./user_components/video.vue";
// 慢病续方
import Continue from "./user_components/continue.vue";

// ios播放amr 或者mp3转为amr
const BenzAMRRecorder = require("benz-amr-recorder");

export default {
  name: "hisChatList",
  components: {
    MsgText,
    Proposal,
    AddDiag,
    Gauge,
    Serve,
    Group,
    Inspection,
    Follow,
    Consulting,
    Referral,
    Author,
    ChatImg,
    CharAudio,
    CharVideo,
    VideoInvitation,
    UserText,
    UserImg,
    UserAudio,
    UserVideo,
    Continue,
  },
  data() {
    return {
      active: 0,
      imgHisList: [],
      chatId: "",
      pageParam: {},
      style: {
        pageHeight: 0,
        contentViewHeight: 0,
        footViewHeight: 90,
        mitemHeight: 0,
      },
      scrollTop: 0,
      oneList: [], //单人聊天记录
      list: [],
      listQuery: {
        page: 1,
        limit: 20,
        size: 1,
        total: 0,
      },
      scrollHeight: 0,
      docInfo: {}, //医生信息
      defaultImg: 'this.src="' + avatar + '"', //默认头像
      docImg: "",
      patientImg: "",

      audio_index: -1,
      audio_item: "",
      audio: "",
    };
  },

  onLoad(options) {
    this.pageParam = JSON.parse(options.param);
  },
  onShow() {
    this.init();
  },
  methods: {
    async init() {
      //获取头像
      this.docImg = this.pageParam.docImg;
      this.patientImg = this.pageParam.patientImg;
      this.getDocImg();
      this.chatId =
        this.pageParam.patientId.toLowerCase() +
        "," +
        this.pageParam.docId.toLowerCase();
      let obj = {
        chatId: this.chatId,
        updata: true,
      };
      await this.$store.dispatch("getChatListId", obj);
      let list = this.$store.getters.getChatList;
      if (list.chatRecordList) {
        list.chatRecordList.map((item) => {
          this.oneList.push(item);
        });
      }
      this.list = [];
      this.listQuery.total = this.listQuery.minNum = this.oneList.length;
      this.listQuery.size = this.oneList.length / this.listQuery.limit;
      //初始化加载数据
      this.initData();
      this.getPatientInfo();
    },
    // 获取就诊人信息
    getPatientInfo(orderInfo) {
      let patientId = this.pageParam.patientId;
      findPatientByPatientId({
        patientId: patientId,
      }).then((res) => {
        let patientInfo = res.data;

        this.patientInfo = patientInfo;
      });
    },
    // 界面 输入框聚焦 失焦 及点击表情 功能面板 收发消息时界面适配
    scrollToBottom: function () {
      let that = this;
      setTimeout(() => {
        let that = this;
        let query = uni.createSelectorQuery();
        query.selectAll(".m-item").boundingClientRect();
        query.select("#msgDiv").boundingClientRect();
        query.exec((res) => {
          that.style.mitemHeight = 0;
          res[0].forEach(
            (rect) =>
              (that.style.mitemHeight =
                that.style.mitemHeight + rect.height + 40)
          ); //获取所有内部子元素的高度
          if (that.style.mitemHeight > that.style.contentViewHeight - 100) {
            //判断子元素高度是否大于显示高度
            that.scrollTop =
              that.style.mitemHeight - that.style.contentViewHeight; //用子元素的高度减去显示的高度就获益获得序言滚动的高度
          }
        });
      }, 300);
    },
    // 禁止点击卡片
    disabledClickCard(type, item) {
      this.$refs.complaintPopup.open();
    },
    closePopup() {
      this.$refs.complaintPopup.close();
    },
    //自定义服务页面
    openServiceDetail(item) {
      let id = item.ext.customBussinessId;
      uni.navigateTo({
        url: "/pages/chatCardDetail/customService?id=" + id,
      });
    },
    //questionnaire 量表
    openScaleDetail(item) {
      let param = {
        sendId: item.ext.sendId,
        patientId: item.ext.patientId,
        docId: this.pageParam.docId,
      };
      if (item.ext.isFeedback == "1") {
        uni.navigateTo({
          url:
            "/pages/chatCardDetail/questionnaireRead?param=" +
            JSON.stringify(param),
        });
      } else {
        uni.navigateTo({
          url:
            "/pages/chatCardDetail/questionnaire?action=" +
            "chatRoom" +
            "&param=" +
            JSON.stringify(param),
        });
      }
    },
    // 点击播放语音
    audioPlay(e) {
      let { index, src } = e;

      // 如果正在播放
      if (this.audio_item && this.audio_item.play) {
        // this.audio.stop();
        this.audio_item.play = false;
        this.$set(this.list, this.audio_index, this.audio_item);
      }
      // 初始化
      this.audio = new BenzAMRRecorder();
      // 完成回调
      this.audio.onEnded(() => {
        this.audio_item.play = false;
        this.$set(this.list, this.audio_index, this.audio_item);
      });

      // 加载地址播放
      this.audio.initWithUrl(src).then((res) => {
        this.audio.play();
      });

      let item = this.list[index];
      item.play = true;
      this.$set(this.list, index, item);
      this.audio_index = index;
      this.audio_item = item;
    },
    // 打开患者资料服务页面
    openMedicalRecord() {
      return;
      this.pageParam.visitTypeName = this.visitTypeName;
      this.pageParam.isSubsequent = this.isSubsequent;
      this.pageParam.cost = this.cost;
      this.pageParam.visitDuration = this.visitDuration;
      uni.navigateTo({
        url:
          "/pages/patientRecord/medicalRecords?param=" +
          JSON.stringify(this.pageParam),
      });
    },
    //随访
    openFollowDetail(item) {
      let param = {
        patientId: item.ext.patientId,
        plsId: item.ext.plsId,
        docId: this.pageParam.docId,
      };
      uni.navigateTo({
        url:
          "/pages/chatCardDetail/followUpPlan?param=" + JSON.stringify(param),
      });
    },
    //检查单
    openChecklist(item) {
      if (item.ext.isSuccess == 1) {
        uni.navigateTo({
          url: "/pages/inspect/inspectDetails?ppiId=" + item.ext.ppiId,
        });
      } else {
        uni.navigateTo({
          url: "/pages/inspect/inspectPayState?ppiId=" + item.ext.ppiId,
        });
      }
    },
    //病例资料
    openMedicalDetail(item) {
      let param = {
        docId: this.pageParam.docId,
      };
      let itemVal = {
        regId: item.ext.regIdSq,
        type: "chat",
        authorizeId: item.ext.authorizeId,
        isSuccess: item.ext.isSuccess,
      };
      uni.navigateTo({
        url:
          "/pages/patientRecord/medicalDetail?docId=" +
          this.pageParam.docId +
          "&regId=" +
          item.ext.regIdSq +
          "&type=chat&authorizeId=" +
          item.ext.authorizeId +
          "&isSuccess=" +
          item.ext.isSuccess,
      });
    },
    //转诊
    openZz(item) {
      let param = {
        groupInfoId: item.ext.docId,
        deptId: item.ext.deptId,
        docName: item.ext.docName,
        docId: item.ext.docId,
        patientId: this.id,
        referralId: item.ext.referralId,
        isSuccess: item.ext.isSuccess,
        regId: this.pageParam.regId,
        oldDocId: this.pageParam.docId,
      };
      if (item.ext.isSuccess == "0") {
        uni.navigateTo({
          url: "/pages/chatCardDetail/referral?param=" + JSON.stringify(param),
        });
      } else {
        Toast("已转诊成功");
      }
    },
    // 转换图片表情
    customEmoji(value) {
      return `<image src="http://imizan.cn/cloud/h5/static/images/faces/${value}" style="width:15px;height:15px"></image>`;
    },
    getDocImg() {
      if (this.docImg) {
        myJsTools.downAndSaveImg(this.docImg, (url) => {
          this.docImg = url;
        });
      }
      if (this.patientImg) {
        myJsTools.downAndSaveImg(this.patientImg, (url) => {
          this.patientImg = url;
        });
      }
    },
    //跳转医生信息
    docInfoFun() {
      uni.navigateTo({
        url:
          "/pages/register/docHomePage/index?docId=" +
          this.pageParam.docId +
          "&action=" +
          "index",
      });
    },
    //问诊加号
    openLineCard(item) {
      var czbType = item.ext.czbType;
      let param = {};
      param.docId = this.pageParam.docId;
      if (czbType == "1") {
        uni.navigateTo({
          url: "/pages/chatCardDetail/lineVisit?param=" + JSON.stringify(param),
        });
      } else if (czbType == "2") {
      } else {
        return;
      }
    },
    openOrderDetail(item) {
      var orderInfo = item.ext;
      let patientInfo = this.patientInfo;
      let loginInfo = {};
      loginInfo.openid = uni.getStorageSync("wxInfo").openId;
      loginInfo.appid = uni.getStorageSync("appId");
      loginInfo.patient_name = patientInfo.patientName;
      loginInfo.id_no = patientInfo.idNo;
      loginInfo.tel_no = patientInfo.telNo;
    },
    //咨询小结
    consultationSummary(item) {
      var regId = item.ext.regId;
      uni.navigateTo({
        url: "/pages/chatCardDetail/conSummary?regId=" + regId,
      });
    },
    //群发
    openFsSend(item) {
      let param = {
        patientId: item.ext.patientId,
        groupInfoId: item.ext.groupInfoId,
        docId: this.pageParam.docId,
      };
      uni.navigateTo({
        url: "/pages/chatCardDetail/fsSend?param=" + JSON.stringify(param),
      });
    },
    //处方
    getCfDetail(item) {
      if (item.ext.isSubsequent == "1" && item.ext.hzStstus == "0") {
        return false;
      }
      uni.removeStorageSync("nowAddress");
      var businessId = item.ext.businessId;
      // uni.navigateTo({
      // 	url: '/pages/prescription/prescriptionDetail?businessId=' + businessId,
      // })
      getPatientPrescriptionOrderInfo({
        businessId: businessId,
      }).then((res) => {
        let obj = res.data[0];
        uni.navigateTo({
          url:
            "/pages/prescription/prescriptionDetail?businessId=" + businessId,
        });
      });
    },
    // 收到的消息处理之后进行显示，文字和表情
    renderTxt(txt = "") {
      let rnTxt = [];
      let match = null;
      const regex = /(\[.*?\])/g;
      let start = 0;
      let index = 0;
      while ((match = regex.exec(txt))) {
        index = match.index;
        if (index > start) {
          rnTxt.push(txt.substring(start, index));
        }
        if (match[1] in this.$im.Emoji.map) {
          const v = this.$im.Emoji.map[match[1]];
          rnTxt.push(this.customEmoji(v));
        } else {
          rnTxt.push(match[1]);
        }
        start = index + match[1].length;
      }
      rnTxt.push(txt.substring(start, txt.length));
      return rnTxt.toString().replace(/,/g, "");
    },
    //聊天页面发送已读回执
    ack(el, index) {
      // 如果是未读发送已读回执
      if (el.status == "unread") {
        var bodyId = el.id; // 需要发送已读回执的消息id
        var ackMsg = new this.$im.message("read", this.$im.conn.getUniqueId());
        ackMsg.set({
          id: bodyId,
          to: el.from,
        });
        this.$im.conn.send(ackMsg.body);
        el.status = "read";
        this.$store.commit("updateMessageStatus", el);
      }
      //已读请求接口返回卡片状态
      if (el.ext && el.ext.type == "cf") {
        var cfbusinessCode = el.ext.cfbusinessCode;
        var para = {
          businessCode: cfbusinessCode,
        };
        getPrescriptionCard(para).then((res) => {
          el.ext.diagName = res.data.diagName;
          el.ext.hzStstus = res.data.hzStstus;
          el.ext.businessId = res.data.businessId;
          el.ext.prescriptionIndate = res.data.prescriptionIndate;
          this.$set(this.oneList, index, el);
        });
      } else if (el.ext && el.ext.type == "lb") {
        let sendId = el.ext.sendId;
        // 判断该量表的状态，是否已填写
        getQuestionStatus({
          sendId: sendId,
          patientId: this.pageParam.patientId,
        }).then((res) => {
          let isFeedback = res.data.isFeedback; // 是否反馈 0否，1是
          el.ext.isFeedback = isFeedback;
          this.$set(this.oneList, index, el);
        });
      } else if (el.ext && el.ext.type == "zz") {
        let referralId = el.ext.referralId;
        getReferralRecord({
          referralId: referralId,
        }).then((res) => {
          let isSuccess = res.data.referralStatus;
          el.ext.isSuccess = isSuccess;
          this.$set(this.oneList, index, el);
        });
      } else if (el.ext && el.ext.type == "lisOrder") {
        let businessCode = el.ext.businessCode;
      } else if (el.ext && el.ext.type == "zdyfw") {
        let customBussinessId = el.ext.customBussinessId;
        findCustomServicePayStatus({
          customBussinessId: customBussinessId,
        }).then((res) => {
          let isSuccess = res.data.status;
          el.ext.isSuccess = isSuccess;
          this.$set(this.oneList, index, el);
        });
      } else if (el.ext && el.ext.type == "blsq") {
        let authorizeId = el.ext.authorizeId;
        getMedicalAuthorizeStatus({
          authorizeId: authorizeId,
        }).then((res) => {
          let isSuccess = res.data.authorizeStatus;
          el.ext.isSuccess = isSuccess;
          this.$set(this.oneList, index, el);
        });
      } else if (el.ext && el.ext.type == "jcd") {
        let ppiId = el.ext.ppiId;
        getProPacsStatus({
          ppiId: ppiId,
        }).then((res) => {
          //检查单状态
          let isSuccess = res.data.status;
          el.ext.isSuccess = isSuccess;
          this.$set(this.oneList, index, el);
        });
      }
    },
    // 搜索历史记录
    searchChat(e) {
      this.list = [];
      var arr = [];
      if (!e.value) {
        let list = this.$store.getters.getChatList;
        list.chatRecordList.map((item) => {
          this.oneList.push(item);
        });
      } else {
        let list = this.oneList;
        var len = this.oneList.length;
        for (var i = 0; i < len; i++) {
          //如果字符串中不包含目标字符会返回-1
          if (JSON.stringify(list[i]).indexOf(e.value) >= 0) {
            arr.push(list[i]);
          }
        }
        this.oneList = arr;
      }
      this.listQuery.total = this.listQuery.minNum = this.oneList.length;
      this.listQuery.size = this.oneList.length / this.listQuery.limit;
      this.initData();
    },
    async initData() {
      let minNum = this.listQuery.minNum - this.listQuery.limit;
      if (minNum < 0) {
        minNum = 0;
      }
      for (var i = this.listQuery.minNum; i > minNum; i--) {
        if (i < 0) {
          break;
        } else {
          await this.ack(this.oneList[i - 1], i - 1);
          this.list.unshift(this.oneList[i - 1]);
        }
      }
      this.listQuery.minNum = minNum;
      this.listQuery.page++;

      this.$nextTick(() => {
        let container = document.querySelector("#msgDiv");
        if (!container) return;
        this.scrollHeight = container.scrollHeight;
      });
    },
    scrollView(e) {
      let that = this;
      let container = this.$refs.msgDiv;
      let positionVal = container.getScrollPosition();
      let scrollTop;
      let query = uni.createSelectorQuery().in(this);
      query.select("#msgDiv").boundingClientRect();
      query.exec((res) => {
        if (
          parseInt(positionVal.scrollTop) + parseInt(res[0].height) + 100 >=
          positionVal.scrollHeight
        ) {
          if (that.newInfoTips) {
            that.newChatNum = 0;
            that.newInfoTips = false;
          }
        }
      });
      //这里的2秒钟定时是为了避免滑动频繁，节流
      setTimeout(() => {
        if (this.listQuery.minNum == 0) {
          return;
        }
        //滑到顶部时触发下次数据加载
        if (e.target.scrollTop <= 5) {
          e.target.scrollTop = 10;
          this.initData();
          //这里的定时是为了在列表渲染之后才使用scrollTo。
          setTimeout(() => {
            this.scrollTop =
              container.getScrollPosition().scrollHeight -
              this.scrollHeight -
              30;
            this.scrollHeight = container.getScrollPosition().scrollHeight;
          }, 100);
        }
      }, 2000);
    },
    openMyPage() {
      uni.navigateTo({
        url: "/pages/chatList/roamChat?param=" + JSON.stringify(this.pageParam),
      });
    },
    search(e) {
      this.$store.commit("getChatSearchName", e.value);
    },
    changeActive(i) {
      this.active = i;
      if (i == 1) {
        this.getImgList();
      }
    },
    // 预览图片
    _previewImage(image) {
      var imgArr = image;
      uni.previewImage({
        urls: imgArr,
        current: imgArr[0],
      });
    },
    getImgList() {
      let list = this.oneList;
      let arr = [];
      let temp = new Tools.HashMap();
      for (let i = 0; i < list.length; i++) {
        let item = list[i];
        if (item.messType == "image") {
          let time = date.format(item.time).split(" ")[0];
          if (temp.containsKey(time)) {
            let temp_item = temp.get(time);
            temp_item.imgList.unshift(item.content);
            temp.put(time, temp_item);
          } else {
            let temp_item = {};
            temp_item.dateTime = time;
            temp_item.imgList = [item.content];
            temp.put(time, temp_item);
          }
        }
      }
      var allImgList = temp.keySet();
      for (var k = 0; k < allImgList.length; k++) {
        var tem_item = temp.get(allImgList[k]);
        arr.push(tem_item);
      }
      arr.sort(function (a, b) {
        return a.dateTime - b.dateTime; //时间反序
      });
      this.imgHisList = arr;
    },
  },
};
</script>

<style scoped lang="scss">
uni-page-body {
  height: 100%;
}

.warp_cont {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.tabs {
  display: flex;
  height: 90rpx;
  background: #ffffff;
  text-align: center;
  position: sticky;
  box-sizing: border-box;
  top: 90rpx;
  z-index: 6;

  .tab {
    padding: 24rpx 0;
    margin: 0 152rpx;
    color: #666666;
    font-size: 34rpx;
  }

  .active {
    @include font_theme;
    @include border_theme(3px, bottom);
  }
}

// empt
.empt_list {
  width: 100%;

  image {
    margin: 0 auto 30upx;
    display: block;
    margin-top: 40upx;
  }

  text {
    display: block;
    text-align: center;
    color: #999;
    font-size: 28upx;
  }
}

.searchView {
  display: flex;
  height: 90rpx;
  align-items: center;
  background: #ffffff;
  position: sticky;
  top: 0;
  z-index: 6;

  .uni-searchbar {
    width: 85%;
  }
}

.historyDiv {
  background: #fff !important;
}

.myIcon {
  width: 22px;
  height: 22px;
}

.imgDiv {
  display: flex;
  flex-wrap: wrap;
}

.dateTitle {
  padding: 11px 16px;
  color: #666666;
  font-size: 11px;
}

.historyImg {
  width: 24%;
  height: 95px;
  margin-top: 5px;
  object-fit: cover;
}

.chatBox {
  padding: 0rpx 20rpx;
  background: #ffffff;
  width: 100%;
  flex: 1;
  overflow: hidden;
  box-sizing: border-box;
}

#msgDiv {
  background-color: #fff;
  font-family: -apple-system;
  overflow: scroll;
  height: 100%;
  box-sizing: border-box;
  font-family: "-apple-system", "Helvetica Neue", "Roboto", "Segoe UI",
    sans-serif;
}

.msgDiv button {
  padding: 40rpx;
}

.btns {
  display: flex;
  text-align: center;
  margin: 36rpx 0;

  button {
    line-height: 98rpx;
    text-align: center;
    font-size: 32rpx;
    border-radius: 50rpx;
    width: 50%;
  }

  .refund {
    @include border_theme;
    @include font_theme;
    background: #ffffff;
  }

  .commit {
    border: none;
    @include bg_theme;
    color: #ffffff;
    margin-left: 16rpx;
  }
}

.complaintPopup {
  width: 100%;

  ::v-deep.uni-popup__wrapper-box {
    width: 70%;
    background: #ffffff;
    padding: 20px;
    color: #333333;
    font-size: 16px;
    line-height: 22px;

    text-align: center;
    font-weight: 600;
    border-radius: 20px;
  }

  .content {
    margin-top: 28px;
    margin-bottom: 19px;

    div {
      font-size: 12px;
      line-height: 17px;
    }
  }

  .btns {
    margin: 0 auto;
    text-align: center;

    button {
      width: 45%;
      background: #fff;
      @include font_theme;
      font-size: 16px;
      line-height: 34px;
    }

    :nth-child(1) {
      @include border_theme;
    }

    :nth-child(2) {
      @include bg_theme;
      color: #fff;
      margin-left: 20px;
    }
  }
}

.tipsPopup {
  width: 60%;

  button {
    margin: 0 auto;
  }
}
</style>
