"use strict";
const plugins_xeUtils_staticHGKeyRE = require("./staticHGKeyRE.js");
const plugins_xeUtils_helperGetHGSKeys = require("./helperGetHGSKeys.js");
const plugins_xeUtils_hasOwnProp = require("./hasOwnProp.js");
function has(obj, property) {
  if (obj) {
    if (plugins_xeUtils_hasOwnProp.hasOwnProp(obj, property)) {
      return true;
    } else {
      var prop, arrIndex, objProp, matchs, rest, isHas;
      var props = plugins_xeUtils_helperGetHGSKeys.helperGetHGSKeys(property);
      var index = 0;
      var len = props.length;
      for (rest = obj; index < len; index++) {
        isHas = false;
        prop = props[index];
        matchs = prop ? prop.match(plugins_xeUtils_staticHGKeyRE.staticHGKeyRE) : "";
        if (matchs) {
          arrIndex = matchs[1];
          objProp = matchs[2];
          if (arrIndex) {
            if (rest[arrIndex]) {
              if (plugins_xeUtils_hasOwnProp.hasOwnProp(rest[arrIndex], objProp)) {
                isHas = true;
                rest = rest[arrIndex][objProp];
              }
            }
          } else {
            if (plugins_xeUtils_hasOwnProp.hasOwnProp(rest, objProp)) {
              isHas = true;
              rest = rest[objProp];
            }
          }
        } else {
          if (plugins_xeUtils_hasOwnProp.hasOwnProp(rest, prop)) {
            isHas = true;
            rest = rest[prop];
          }
        }
        if (isHas) {
          if (index === len - 1) {
            return true;
          }
        } else {
          break;
        }
      }
    }
  }
  return false;
}
exports.has = has;
