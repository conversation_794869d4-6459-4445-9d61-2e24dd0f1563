"use strict";
const common_vendor = require("../../common/vendor.js");
const api_base = require("../../api/base.js");
const utils_myJsTools = require("../../utils/myJsTools.js");
const common_js_regex = require("../../common/js/regex.js");
const api_user = require("../../api/user.js");
const emedia = require("@/utils/WebIM.js")["emedia"];
const _sfc_main = {
  name: "initlogin",
  components: {},
  data() {
    return {
      telNo: "",
      captcha: "",
      // 图标展示
      icon: CONFIG_ENV.VUE_APP_SHARE,
      codeinfo: {
        sendAuthCode: true,
        auth_time: 0,
        btnText: "发送验证码"
      }
      //验证码时间及是否可点击
    };
  },
  // 如果有token信息.则直接进主页
  onShow() {
  },
  methods: {
    //验证码字段验证
    codeBlur() {
      var re = /^\d{6}$/;
      if (!this.captcha) {
        Toast("验证码不能为空");
        return;
      } else if (!re.test(this.captcha)) {
        Toast("请输入正确的验证码");
        return false;
      } else {
        return true;
      }
    },
    //手机号正则表达式
    telBlur() {
      var re = /^1\d{10}$/;
      if (!this.telNo) {
        Toast("手机号不能为空");
        return false;
      } else if (!re.test(this.telNo)) {
        Toast("请输入正确的手机号");
        return false;
      } else {
        return true;
      }
    },
    //登录
    login() {
      if (this.codeBlur() && this.telBlur()) {
        var capParam = {
          captcha: this.captcha,
          telNo: this.telNo
          // appid:"wxa44359c5ebfba07f",
          // openid:"opL5Fw22wX6jucu1JIcNEM6sMaDM"
        };
        api_base.hisPatientLoginByTelPhoneAndCaptcha(capParam).then((res) => {
          console.log(res, "用户登录返回信息");
          if (res.data && res.data.userId) {
            let proPfInfo = res.data;
            this.$store.commit("setProPfInfo", proPfInfo);
            common_vendor.index.setStorageSync("userId", proPfInfo.userId);
            let myUsername = proPfInfo.userId.toLowerCase();
            common_vendor.index.setStorageSync("myUsername", myUsername);
            common_vendor.index.setStorageSync("tel", res.data.telNo || "");
            common_vendor.index.setStorageSync("patientIdList", proPfInfo.patientIdList || []);
            utils_myJsTools.myJsTools.setItem("proPfInfo", proPfInfo);
            let hxidIsregist = proPfInfo.hxidIsregist;
            if (hxidIsregist == "1") {
              console.log("去登录");
              this.WebIMLogin();
            } else {
              this.WebIMRegister();
            }
          }
        });
      }
    },
    // 环信登录
    WebIMLogin() {
      let _this = this;
      let userId = common_vendor.index.getStorageSync("userId");
      let options = {
        apiUrl: _this.$im.config.apiURL,
        user: userId,
        pwd: userId,
        grant_type: userId,
        appKey: _this.$im.config.appkey,
        success: function(res) {
          console.log("登陆成功", res);
          let memName = _this.$im.config.appkey + "_" + userId;
          emedia.mgr.setIdentity(memName, res.access_token);
          _this.toPath();
        },
        error: function(err) {
          _this.isModelToastShow = true;
        }
      };
      _this.$im.conn.open(options);
    },
    toPath() {
      common_vendor.index.reLaunch({
        url: "/pages/index/index"
      });
      return;
    },
    // 环信注册
    WebIMRegister() {
      let _this = this;
      let userId = common_vendor.index.getStorageSync("userId");
      let options = {
        username: userId,
        password: userId,
        nickname: userId,
        appKey: _this.$im.config.appkey,
        success: function() {
          _this.updateHxidIsregistStatusFun();
        },
        error: function(err) {
          if (err.statusCode == 400) {
            _this.updateHxidIsregistStatusFun();
            return;
          }
          _this.isModelToastShow = true;
        },
        apiUrl: _this.$im.config.apiURL
      };
      _this.$im.conn.registerUser(options);
    },
    // 更改用户环信状态
    async updateHxidIsregistStatusFun() {
      let userId = common_vendor.index.getStorageSync("userId");
      await api_user.updateHxidIsregistStatus({
        userId
      });
      this.WebIMLogin();
    },
    // 获取验证码
    async sendCode() {
      let telNo = this.telNo;
      if (common_js_regex.regex.telBlur(telNo)) {
        if (this.codeinfo.auth_time > 0)
          return;
        await api_user.sendCaptcha({
          telNo
        });
        this.codeinfo.auth_time = 60;
        var auth_timetimer = setInterval(() => {
          this.codeinfo.auth_time--;
          this.codeinfo.btnText = this.codeinfo.auth_time + "s后重新发送";
          if (this.codeinfo.auth_time <= 0) {
            this.codeinfo.sendAuthCode = true;
            this.codeinfo.auth_time = 0;
            this.codeinfo.btnText = "重新发送";
            clearInterval(auth_timetimer);
          }
        }, 1e3);
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $data.icon,
    b: $data.telNo,
    c: common_vendor.o(($event) => $data.telNo = $event.detail.value),
    d: $data.captcha,
    e: common_vendor.o(($event) => $data.captcha = $event.detail.value),
    f: common_vendor.t($data.codeinfo.btnText),
    g: $data.codeinfo.auth_time > 0 ? 1 : "",
    h: common_vendor.o((...args) => $options.sendCode && $options.sendCode(...args)),
    i: common_vendor.o((...args) => $options.login && $options.login(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-74c74a87"]]);
wx.createPage(MiniProgramPage);
