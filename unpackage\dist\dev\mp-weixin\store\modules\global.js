"use strict";
const env = require("../../env.js");
const global = {
  state: {
    // 环境key
    envKey: env.env.DEFAULT_ENV_KEY,
    // 所有环境
    allEnv: env.env.DEFAULT_ALL_ENV
  },
  mutations: {
    SET_envKey: (state, envKey) => {
      state.envKey = envKey;
      storage.set(STORE_KEY_ENUM.envKey, envKey);
    },
    SET_allEnv: (state, allEnv) => {
      state.allEnv = allEnv;
      storage.set(STORE_KEY_ENUM.allEnv, allEnv);
    }
  },
  actions: {}
};
exports.global = global;
