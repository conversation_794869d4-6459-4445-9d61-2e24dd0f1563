<template>
  <view class="lineVisit">
    <view class="tips">
      <text class="line"></text>
      <text class="tip_title">点击选择您要预约的日期</text>
      <view class="tagTips" v-if="false">
        <text class="whiteBor"></text><text>医院</text
        ><text class="blackBor"></text><text>个人</text>
      </view>
    </view>
    <uni-calendar
      v-if="hackReset"
      :insert="true"
      :lunar="false"
      :date="visitDate"
      :start-date="startTime"
      :end-date="endDate"
      :showMonth="false"
      :selected="visitDates"
      @change="selevtVisitDate"
    />
    <view class="tips">
      <text class="line"></text>
      <text>请选择您要预约的时间段</text>
    </view>

    <!-- 列表 -->
    <view class="list" v-if="list.length">
      <view class="list_item">
        <view
          class="item"
          v-for="(item, index) in list"
          :key="index"
          @click="toAppoint(item)"
        >
          <view class="item_time">{{ item.startendTime }}</view>
          <view class="item_num">剩余号: {{ item.surplusNum }}</view>
        </view>
      </view>

      <view
        class="more"
        v-if="totalList.length > 3 && showTotal"
        @click="totalShow"
        >查看更多</view
      >
    </view>

    <!-- 空白 -->
    <view class="empty" v-if="!list.length">
      <text>今日暂无排班，请选择其他日期</text>
    </view>
  </view>
</template>

<script>
import uniCalendar from "@/components/uni-calendar/uni-calendar.vue";

import { getVisitReal, checkAppointOffLine } from "@/api/base";

import myJsTools from "@/common/js/myJsTools.js";

export default {
  props: ["pageParam"],
  components: {
    uniCalendar,
  },
  data() {
    return {
      // 所有排班
      all: {},
      visitDate: myJsTools.getDate("day", 0),
      startTime: myJsTools.getDate("day", 0),
      visitDates: [],
      endDate: myJsTools.getDate("day", 7),
      hackReset: true,
      query: {
        docId: "",
        // 1 医院 2 医生
        vrTempType: "",
        // 号别
        dntId: "",
      },
      list: [],
      totalList: [],
      showTotal: false,
    };
  },
  created() {
    this.query.docId = this.pageParam.docId;
    this.query.dntId = this.pageParam.dntId || "";
    this.query.vrTempType = this.pageParam.vrTempType;
    this.getVisitList();
  },
  methods: {
    // 获取医生排班
    async getVisitList() {
      let { docId, dntId, vrTempType } = this.query;
      let { data } = await getVisitReal({
        docId,
        dntId,
        vrTempType,
      });
      this.setResult(data);
    },
    // 整理值
    setResult(list) {
      let obj = {};

      list.forEach((v) => {
        if (obj[v.visitTime]) {
          obj[v.visitTime].push(v);
        } else {
          obj[v.visitTime] = [v];
        }
      });

      let arr = [];

      for (const key in obj) {
        let v = {
          date: key,
          info: "可预约",
        };
        let s = new Date(v.date).getTime();
        let now = new Date(this.visitDate);
        // 大于等于今天
        if (s >= now) {
          arr.push(v);
        }
      }
      this.all = obj;
      console.log(this.all);
      this.visitDates = arr;

      if (!arr.length) return;
      let str = this.visitDate;
      if (obj[str]) {
        // 设置初始值
        if (obj[str].length > 3) {
          this.showTotal = true;
          let totalObj = JSON.parse(JSON.stringify(obj[str]));
          this.totalList = JSON.parse(JSON.stringify(totalObj)); //存放所有排班
          this.list = totalObj.splice(0, 3); //只存放前三个
        } else {
          this.totalList = obj[str];
          this.list = obj[str];
        }
      }
    },

    // 设置排班
    selevtVisitDate(v) {
      let key = v.fulldate;
      // 判断是否存在
      if (this.all.hasOwnProperty(key)) {
        if (this.all[key].length > 3) {
          this.showTotal = true;
          let totalObj = JSON.parse(JSON.stringify(this.all[key]));
          this.totalList = JSON.parse(JSON.stringify(totalObj));
          this.list = totalObj.splice(0, 3);
        } else {
          this.totalList = this.all[key];
          this.list = this.all[key];
        }
      } else {
        this.list = [];
      }
    },
    //显示所有排班
    totalShow() {
      this.list = this.totalList;
      this.showTotal = false;
    },
    // 点击预约
    async toAppoint(v) {
      let { docId, patientId } = this.pageParam;
      let { startTime, endTime, visitTime, vrId } = v;
      let { data } = await checkAppointOffLine({
        docId,
        patientId,
        startTime,
        endTime,
        visitTime,
      });
      uni.navigateTo({
        url: `/pages/chatCardDetail/outpatientDepartment?docId=${docId}&visitTime=${visitTime}&startTime=${startTime}&endTime=${endTime}&patientId=${patientId}&vrId=${vrId}`,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.lineVisit {
  padding-bottom: 20upx;

  .tips {
    color: #333333;
    font-size: 28rpx;
    padding: 26rpx 32rpx;
    background: #ffffff;
    display: flex;
    align-items: center;
    position: relative;

    .line {
      width: 6rpx;
      height: 28rpx;
      margin-right: 16rpx;
      display: inline-block;
      @include bg_theme;
      border-radius: 2rpx;
    }

    .tip_title {
      font-weight: bold;
    }

    .tagTips {
      position: absolute;
      right: 32rpx;
      top: 26rpx;

      .blackBor,
      .whiteBor {
        border-radius: 50%;
        width: 16rpx;
        height: 16rpx;
        border: 1px solid #cacaca;
        display: inline-block;
        margin-right: 6rpx;
        background: #ffffff;
      }

      .blackBor {
        margin-left: 56rpx;
        background: #000000;
        border: 1px solid #000000;
      }
    }
  }

  .list {
    width: 686rpx;
    box-sizing: border-box;
    margin: 24rpx auto;
    padding: 0 24rpx 1px;
    background-color: #fff;
    box-shadow: 0px 4rpx 40rpx 0px rgba(0, 0, 0, 0.1);
    border-radius: 16rpx;

    .list_item {
      .item {
        height: 124rpx;
        @include flex(lr);
        font-size: 28rpx;
        color: #333;
        border-bottom: 1px solid #ebebeb;

        &:last-child {
          border: none;
        }

        .item_num {
          width: 40%;
          text-align: center;
          color: #666;
        }
      }
    }

    .more {
      height: 92rpx;
      @include border_theme;
      @include flex;
      @include font_theme;
      font-size: 36rpx;
      border-radius: 46rpx;
      margin-bottom: 24rpx;
    }
  }

  .empty {
    font-size: 28rpx;
    color: #666;
    height: 160rpx;
    @include flex;
  }

  /* 日历样式 */
  ::v-deep.uni-calendar {
    width: 100%;
    overflow: hidden;
  }

  ::v-deep.uni-calendar__backtoday {
    display: none;
  }

  ::v-deep.uni-calendar__content {
    width: 100%;
    height: 100%;
  }

  ::v-deep.uni-calendar__box {
    width: 100%;
    height: 100%;
  }

  ::v-deep.uni-calendar-item__weeks-box-item {
    width: 48px;
    height: 48px;
  }

  ::v-deep .uni-calendar-item--isDay .uni-calendar-item--extra {
    color: #fff;
  }

  ::v-deep.uni-calendar-item--checked {
    background: #ff5050;

    .uni-calendar-item__weeks-box-item {
      background: inherit !important;
    }
  }

  ::v-deep.uni-calendar-item--isDay {
    background: #ff5050;
  }
}
</style>
