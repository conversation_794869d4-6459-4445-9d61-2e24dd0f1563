<template>
	<!-- 病情描述 -->
	<view>
		<view class="page-container">
			<view class="base-info">
				<view class="input-box">
					<text :class="{ x: isRequired }">标题</text>
					<textarea v-model="info.recordsTitle" placeholder="填写您的症状或特征、性质以及持续时间" maxlength="20" :focus="true" style="text-align: right" />
				</view>

				<view class="input-box">
					<text :class="{ x: isRequired }">首诊诊断</text>
					<input type="text" v-model="info.diagnosisDisease" placeholder="请输入已确认诊断" maxlength="50" style="text-align: right" />
				</view>

				<view class="input-box">
					<text :class="{ x: isRequired }">首诊医院</text>
					<!--          <input-->
					<!--            type="text"-->
					<!--            :value="info.firstVisitHos"-->
					<!--            v-model="info.firstVisitHos"-->
					<!--            placeholder="请输入首诊医院名称"-->
					<!--            maxlength="50"-->
					<!--            style="text-align: right"-->
					<!--          />-->
					<view class="select-box" @click="openHospitalPopup">
						<view style="margin-right: 10px; flex: 1; font-size: 12px; color: rgba(131, 106, 255, 1)">{{ info.firstVisitHos || '选择' }}</view>
						<uni-icon type="right" color="rgba(166, 166, 166, 1)" size="25"/>
					</view>
					<uni-popup ref="hospitalPopup" type="bottom" @change="onPopupChange">
						<view class="popup-content">
							<view class="popup-top">
								<view class="cancel" @click="closePopup">取消</view>
								<view class="success" @click="sureEvent">完成</view>
							</view>
							<view style="padding: 20px">
								<uni-search-bar
									:radius="100"
									placeholder="请输入"
									v-model="keyword"
									@input="onSearchInput"
									@confirm="getSearch"
									@clear="onSearchClear"
								></uni-search-bar>
							</view>
							<scroll-view scroll-y class="hospital-scroll-view" @scrolltolower="loadMoreHospitals">
								<view class="hospital-list">
									<view
										class="hospital-item"
										v-for="(item, index) in hosList"
										:key="index"
										:class="{ 'hospital-item-selected': selectedHospitalIndex === index }"
										@click="selectHospital(index)"
									>
										{{ item.name }}
									</view>
								</view>
								<view v-if="isLoadingMore" class="loading-more">加载中...</view>
							</scroll-view>
						</view>
					</uni-popup>
				</view>

				<view class="input-box">
					<text :class="{ x: isRequired }">患病时长</text>
					<input type="number" v-model="info.sickTime" placeholder="填写患病时长" maxlength="6" style="text-align: right" />
					<view class="select-day" @click="selectUnit">
						<view class="select_time">{{ info.timeUnit }}</view>
						<image src="/static/images/sx.png" class="icon_sx"></image>
					</view>
				</view>

				<view class="title_format" :class="{ x: isRequired }">病情描述（详细描述您的病情，症状，治疗经过）</view>
				<view class="content">
					<textarea placeholder="详细描述您的病情，症状，治疗经过" maxlength="200" v-model="info.diseaseDescription" class="other-content" />
					<view class="char-count" style="bottom: -22px">{{ info.diseaseDescription.length }}/200</view>
				</view>
				<view class="title_format" :class="{ x: isRequired }">期望获得的帮助</view>
				<view class="content">
					<textarea placeholder="详细描述您的期望帮助" maxlength="200" v-model="info.expectHelp" class="other-content" />
					<view class="char-count" style="bottom: 10px">{{ info.expectHelp.length }}/200</view>
					<view class="shortcut">
						<view style="color: #999; font-size: 24rpx; line-height: 34rpx">快捷输入</view>
						<view class="shortcut_box">
							<template v-for="item in shortcutList">
								<view class="shortcut_little" @click="getShortcut(item.quickInputName)">{{ item.quickInputName }}</view>
							</template>
						</view>
					</view>
				</view>
			</view>
			<view style="height: 16rpx; background: #f2f2f2"></view>
			<view class="img-info">
				<view class="title_format" :class="{ x: isRequired }">
					病情照片
					<text class="font_hint">（仅限本人和接诊医生可看）</text>
				</view>
				<view class="describe_check_image">
					<view class="font_hint_pb">请上传患者历史就诊病例图片，最多9张</view>
					<view class="uploader">
						<template v-for="(item, index) in fileListLi">
							<view class="img-box">
								<image class="img-list" :src="item.url" mode="" @click="previewImg(item.url)"></image>
								<image src="/static/images/question/image-delete.png" mode="" class="delete-img" @click="delectImg1(index)"></image>
							</view>
						</template>
						<view v-if="fileListLi.length < 9" class="img-box img_box_shang" @click="chooseImage1">
							<view>
								<image src="/static/images/pic.png"></image>
								<view>上传图片</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 下一步 -->
		<FooterButton @click="getSubmit()">保存</FooterButton>

		<!-- 选择患病时长单位 -->
		<propBottom v-if="unitShow" :actions="columns" @propConfirm="propConfirm" @propCancel="propCancel"></propBottom>
	</view>
</template>

<script>
import { Toast } from '@/common/js/pay.js';
import FooterButton from '@/components/footer_button/button.vue';
import propBottom from '@/components/propBottom/propBottom.vue';
import { getQuickInputInfo, savePatientRecords, getSysPlatformConfigByKeyList } from '@/api/base.js';
import { uploadImg } from '@/api/oss.js';
import myJsTools from '@/common/js/myJsTools.js';
import { dicSzHospitalList } from '../../../../api/base';
export default {
	components: {
		propBottom,
		FooterButton
	},
	data() {
		return {
			info: {
				recordsTitle: '', // 标题
				diagnosisDisease: '', // 首诊疾病
				firstVisitHos: '', // 首诊医院
				sickTime: '', // 患病时长
				timeUnit: '天', // 患病时长单位
				diseaseDescription: '', // 病情描述
				expectHelp: '' // 期望获得的帮助
			},
			// 病情，其他照片
			fileListLi: [],
			disease: [], // 提交时，用以缓存图片上传oss后返回的文件名
			diseaseIndex: 0,
			medical: [], // 提交时，用以缓存图片上传oss后返回的文件名
			medicalIndex: 0,
			diseaseImgLen: -1,
			medicalImgLen: -1,
			isFirstVisitHos: false,
			diseaseImgOne: [], // 第二次进入页面，页面中缓存的病情图片（oss下载下来的图片路径）
			medicalImgOne: [], // 第二次进入页面，页面中缓存的其他照片，或者修改进入，页面中之前保存的其他照片(oss)
			unitShow: false,
			columns: [
				{
					name: '天',
					value: '0'
				},
				{
					name: '周',
					value: '1'
				},
				{
					name: '月',
					value: '2'
				},
				{
					name: '年',
					value: '3'
				}
			],
			shortcutList: [], // 快捷输入
			prId: '',
			patientRecordsInfo: {}, // 缓存提交的文字数据
			isRequired: false,
			isFirst: false,
			hosList: [],
			allList: [],
			checkHos: [],
			checkHosInfo: {},
			keyword: '',
			indicatorStyle: `height: 50px;`,
			currentPage: 1,
			totalPages: 1,
			isLoadingMore: false,
			selectedHospitalIndex: null
		};
	},
	async onLoad(e) {
		this.isFirst = true;
		this.prId = e.prId;
		try {
			await this.getConfig();
		} catch (error) {
			console.log(error);
		}
		this.getQuickInputInfoFun();
		this.getHosList();
	},
	methods: {
		// uni-popup 相关方法
		openHospitalPopup() {
			this.$refs.hospitalPopup.open();
		},
		onPopupChange(e) {
			this.isFirstVisitHos = e.show;
		},
		closePopup() {
			this.isFirstVisitHos = false;
			this.$refs.hospitalPopup.close();
		},
		// uni-search-bar 相关方法
		onSearchInput(value) {
			this.keyword = value;
		},
		onSearchClear() {
			this.keyword = '';
			this.currentPage = 1;
			this.getHosList(); // 清空搜索时重新加载所有医院
		},
		getHosList() {
			const data = {
				page: this.currentPage,
				limit: 20
			};
			if (this.keyword) {
				data.name = this.keyword;
			}

			dicSzHospitalList(data)
				.then((res) => {
					if (this.currentPage > 1) {
						this.hosList = [...this.hosList, ...(res.data.rows || [])];
						this.allList = [...this.allList, ...(res.data.rows || [])];
					} else {
						this.hosList = res.data.rows || [];
						this.allList = res.data.rows || [];
					}
					this.totalPages = Math.ceil(res.data.total / 20);
					this.isLoadingMore = false;
				})
				.catch(() => {
					this.isLoadingMore = false;
				});
		},
		getSearch(value) {
			this.keyword = value;
			this.currentPage = 1;
			this.hosList = [];
			this.isLoadingMore = true;

			const data = {
				page: 1,
				limit: 20,
				name: value
			};

			dicSzHospitalList(data)
				.then((res) => {
					this.hosList = res.data.rows || [];
					this.totalPages = Math.ceil(res.data.total / 20);
					this.isLoadingMore = false;
				})
				.catch(() => {
					this.isLoadingMore = false;
				});
		},
		sureEvent() {
			if (this.selectedHospitalIndex === null) {
				this.info.firstVisitHos = this.hosList[0]?.name;
			} else {
				this.info.firstVisitHos = this.hosList[this.selectedHospitalIndex]?.name;
			}
			this.closePopup();
		},
		bindChange(e) {
			const val = e.detail.value;
			this.checkHos = val;
			this.checkHosInfo = this.hosList[val[0]];
		},
		// 查询配置
		async getConfig() {
			let { data } = await getSysPlatformConfigByKeyList(['patient_condition_param_must']);
			if (data && data.length) {
				let item = data[0];
				if (item.configValue && item.configValue == 1) {
					this.isRequired = true;
				}
			}
		},
		// 选择患病时长,默认"天"
		selectUnit() {
			this.unitShow = true;
		},
		// 接收选择患病时长组件的返回值
		propConfirm(evt) {
			this.unitShow = false;
			this.info.timeUnit = evt.name;
		},
		// 取消选择患病时长单位
		propCancel() {
			this.unitShow = false;
		},
		// 查询快捷输入标签
		async getQuickInputInfoFun() {
			let { data } = await getQuickInputInfo({
				page: 1,
				limit: 20,
				quickType: 1
			});
			this.shortcutList = data.rows;
		},
		// 操作快捷输入
		getShortcut(evt) {
			this.info.expectHelp = this.info.expectHelp + evt;
		},
		// 上传病情图片
		chooseImage1() {
			let _this = this;
			uni.chooseImage({
				count: 9, //默认9
				sizeType: ['original', 'compressed'],
				sourceType: ['album'], //从相册选择
				success: (res) => {
					let tempFiles = res.tempFiles;
					if (tempFiles.length > 0) {
						for (let i = 0; i < tempFiles.length; i++) {
							let el = tempFiles[i];
							myJsTools.setImgZip(el, (dataUrl) => {
								if (_this.fileListLi.length < 9) {
									_this.fileListLi.push({
										base64: dataUrl.split(',')[1],
										url: dataUrl,
										name: ''
									});
								}
							});
						}
					}
				}
			});
		},

		// 删除病历照片
		delectImg1(index) {
			this.fileListLi.splice(index, 1);
		},

		// 预览图片
		previewImg(img) {
			myJsTools.previewImg(img);
		},
		// 下一步
		getSubmit() {
			let obj = this.info;

			obj.patientId = uni.getStorageSync('patientId');
			obj.patientName = uni.getStorageSync('patientInfo').patientName;

			let recordsTitle = this.info.recordsTitle.trim();
			let diseaseDescription = this.info.diseaseDescription.trim();

			// 是否全必填
			if (this.isRequired) {
				if (!recordsTitle) {
					Toast('请填写标题');
					return;
				}

				if (!obj.diagnosisDisease) {
					Toast('请输入首诊诊断');
					return;
				}

				if (!obj.firstVisitHos) {
					Toast('请输入首诊医院');
					return;
				}

				let reg = /^[0-9]{1,}$/;
				if (!reg.test(obj.sickTime)) {
					Toast('患病时长请填写正数');
					return;
				}

				if (!diseaseDescription) {
					Toast('请输入病情描述');
					return;
				}

				if (!obj.expectHelp) {
					Toast('请输入期望帮助');
					return;
				}

				if (!this.fileListLi.length) {
					Toast('请上传病情照片');
					return;
				}
			} else {
				if (!recordsTitle) {
					Toast('请填写标题');
					return;
				}

				if (obj.sickTime) {
					let reg = /^[0-9]{1,}$/;
					if (!reg.test(obj.sickTime)) {
						Toast('患病时长请填写正数');
						return;
					}
				}

				if (!diseaseDescription) {
					Toast('请输入病情描述');
					return;
				}
			}
			if (!this.isFirst) {
				return;
			}
			// 将病情资料中病情图片以上的内容进行暂时缓存
			this.patientRecordsInfo = obj;
			// 1.遍历病情图片,上传到oss,缓存oss返回的文件名
			this.uploadDiseaseImg();
		},
		async uploadDiseaseImg() {
			try {
				this.isFirst = false;
				let fileListLi = this.fileListLi;
				let disease = this.disease;
				if (fileListLi.length > 0) {
					for (let i = 0; i < fileListLi.length; i++) {
						let res = await this.uploadImgFun(fileListLi[i]);
						disease.push(res.data.url);
					}
					this.disease = disease;
					this.patientRecordsInfo.diseaseImg = JSON.stringify(this.disease);
				} else {
					this.patientRecordsInfo.diseaseImg = '';
				}
				this.patientRecordsInfo.medicalImg = '';
				this.savePatientRecordsFun();
			} catch (e) {
				this.isFirst = true;
			}
		},
		uploadImgFun(element) {
			let para = {
				folderType: 11,
				imgBody: element.base64,
				otherId: uni.getStorageSync('patientId')
			};
			return uploadImg(para);
		},

		// 提交
		async savePatientRecordsFun() {
			try {
				this.isFirst = false;
				let patientRecordsInfo = this.patientRecordsInfo;
				patientRecordsInfo.prId = this.prId;
				await savePatientRecords(patientRecordsInfo);
				Toast('保存成功');

				setTimeout(() => {
					this.isFirst = true;
					if (this.isRequired) {
						uni.redirectTo({
							url: '/pages/register/appointRegister/orderDetail/index?&prId=' + this.prId
						});
					} else {
						uni.reLaunch({
							url: '/pages/index/index'
						});
					}
				}, 1500);
			} catch (e) {
				this.isFirst = true;
				uni.hideLoading();
			}
		},
		loadMoreHospitals() {
			if (this.isLoadingMore || this.currentPage >= this.totalPages) return;

			this.isLoadingMore = true;
			this.currentPage++;
			this.getHosList();
		},
		selectHospital(index) {
			this.selectedHospitalIndex = index;
		}
	}
};
</script>

<style scoped lang="scss">
.input-box {
	.textarea-placeholder,
	.input-placeholder {
		font-size: 12px;
		display: flex;
		align-items: center;
		color: rgba(153, 153, 153, 1) !important;
		justify-content: right;
		margin-right: 10px;
	}
}
.textarea-placeholder,
.input-placeholder {
	font-size: 12px;
	display: flex;
	color: rgba(153, 153, 153, 1) !important;
}
.x {
	font-size: 14px;

	&::before {
		content: '*';
		color: red;
		margin-right: 10rpx;
	}
}

.page-container {
	padding-bottom: 120rpx;
	box-sizing: border-box;
	overflow-y: auto;
	background-color: #ffffff;
}
.base-info,
.img-info {
	padding: 0 30rpx 30rpx 30rpx;
}

.input-box {
	width: 100%;
	display: flex;
	height: 95rpx;
	padding: 16rpx 0;
	align-items: center;
	box-sizing: border-box;
	border-bottom: 1rpx solid #ebebeb;
	font-size: 32rpx;
	font-weight: 400;
}

.input-box text {
	display: inline-block;
	width: 180rpx;
	line-height: 88rpx;
	flex: none;
	color: rgba(51, 51, 51, 1);
}

.input-box input {
	flex: 1;
	/* height: 88rpx; */
	/* line-height: 88rpx; */
	padding-left: 28rpx;
	color: rgba(102, 102, 102, 1);
}

.input-box textarea {
	flex: 1;
	height: 88rpx;
	/* line-height: 44rpx; */
	padding-left: 28rpx;
	color: rgba(102, 102, 102, 1);
}

.input-box .select-day {
	width: 102rpx;
	@include flex(center);
	border: 1px solid $k-hr-color;
	border-radius: 4upx;
	box-sizing: border-box;
	height: 60upx;

	.select_time {
		font-size: 24upx;
		color: $k-title;
		font-weight: 500;
		padding-right: 10upx;
	}

	.icon_sx {
		width: 30upx;
		height: 30upx;
	}
}

.title_format {
	font-size: 14px;
	font-weight: 400;
	color: #333333;
	line-height: 22px;
	margin: 38rpx 0 20rpx 0;
}
.content {
	position: relative;
	.char-count {
		position: absolute;
		right: 20rpx;
		bottom: 30rpx;
		font-size: 24rpx;
		color: #999;
		background: rgba(255, 255, 255, 0.9);
		padding: 0 10rpx;
		border-radius: 4rpx;
	}
}
.other-content {
	width: 100%;
	box-sizing: border-box;
	border-radius: 8rpx;
	border: 1px solid rgba(229, 229, 229, 1);
	font-size: 32rpx;
	line-height: 44rpx;
	padding: 30rpx 16rpx;
	height: 240rpx;
}

.shortcut {
	padding: 20rpx 0;
}

.shortcut_box {
	display: flex;
	align-items: center;
	flex-direction: row;
	flex-wrap: wrap;
}

.shortcut_little {
	color: #75777a;
	background-color: #eeeeee;
	border-radius: 19rpx;
	font-size: 24rpx;
	text-align: center;
	padding: 4rpx 32rpx;
	margin-right: 28rpx;
	margin-top: 20rpx;
}

.font_hint {
	font-size: 12px;
	color: rgba(131, 106, 255, 1);
	font-weight: 500;
}

/* 病情/其他图片 */
.describe_check_image {
	color: #666666;
	border: 1px solid #ebedf0;
	border-radius: 8rpx;
	padding: 18rpx;
	font-size: 28rpx;
}

.font_hint_pb {
	font-size: 12px;
	color: #999;
}

.uploader {
	display: flex;
	align-items: center;
	flex-direction: row;
	flex-wrap: wrap;
}

.img_box_shang,
.img-box {
	width: 198rpx;
	height: 198rpx;
	border-radius: 8px;
	border: 1px dashed rgba(131, 106, 255, 1);
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 18rpx;
	text-align: center;
	color: #666666;
	margin-top: 20rpx;
	position: relative;
}

.img-box:not(:nth-child(3n + 1)) {
	margin-left: 22rpx;
}

.img-list {
	display: block;
	width: 100%;
	height: 100%;
}

.img_box_shang image {
	width: 40rpx;
	height: 38rpx;
}

.delete-img {
	width: 40rpx;
	height: 40rpx;
	position: absolute;
	top: -10rpx;
	right: -10rpx;
}

/* 底部按钮 */
.footer {
	width: 100%;
	position: fixed;
	bottom: 0;
	left: 0;

	.btns {
		height: 108rpx;
		@include flex(lr);
		padding: 0 32upx;
		box-sizing: border-box;
		background: #ffffff;
		font-size: 32rpx;

		.samll-btn {
			width: 334rpx;
			height: 84rpx;
			color: #333;
			background: #f5f5f5;
			border-radius: 42rpx;
			font-weight: 500;
			line-height: 84rpx;
			text-align: center;
			box-sizing: border-box;

			&.active {
				@include bg_theme;
				color: #ffffff;
			}

			&.cancel {
				@include border_theme;
				background: #ffffff;
				@include font_theme;
			}
		}
	}
}

.footer .btn {
	width: 100%;
	height: 84rpx;
	text-align: center;
	line-height: 84rpx;
	font-size: 32rpx;
	font-weight: 400;
	color: #333;
	background: #f5f5f5;
}
.footer .active {
	@include bg_theme;
	color: #ffffff;
}
.select-box {
	display: flex;
	align-items: center;
	text-align: right;
	flex: 1;
}
.picker-view {
	width: 750rpx;
	height: 400rpx;
	margin-top: 20rpx;
}
.item {
	line-height: 100rpx;
	text-align: center;
}
.popup-content {
	background: #fff;
	border-radius: 20rpx 20rpx 0 0;
	max-height: 80vh;
	overflow: hidden;
}

.popup-top {
	display: flex;
	align-items: center;
	line-height: 50px;
	justify-content: space-between;
	padding: 0 20px;
	border-bottom: 1px solid #eee;
	.success {
		color: #15a0e6;
	}
	.cancel {
		color: #666;
	}
}
::v-deep .footer_button uni-button {
	width: 353px;
	height: 36px;
	opacity: 1;
	border-radius: 20px;
	background: linear-gradient(135deg, rgba(81, 106, 251, 1) 0%, rgba(133, 155, 255, 1) 99.97%);
	color: white;
	font-size: 14px !important ;
}
.hospital-scroll-view {
	height: 400rpx;
	overflow-y: auto;
}
.hospital-list {
	padding: 20rpx;
}

.hospital-item {
	height: 84rpx;
	line-height: 84rpx;
	padding: 0 20rpx;
	border-bottom: 1px solid #eee;
	font-size: 28rpx;
	color: #333;
	text-align: center;
	&.hospital-item-selected {
		color: #836aff;
		background-color: #f5f5f5;
	}
}

.loading-more {
	padding: 20rpx;
	text-align: center;
	font-size: 24rpx;
	color: #999;
}
</style>
