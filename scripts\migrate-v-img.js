/**
 * v-img 指令迁移工具
 * 用于批量替换项目中的 v-img 指令为 UniImage 组件
 * 
 * 使用方法：
 * node scripts/migrate-v-img.js
 */

const fs = require('fs')
const path = require('path')

// 需要处理的文件扩展名
const FILE_EXTENSIONS = ['.vue', '.nvue']

// 需要扫描的目录
const SCAN_DIRS = ['pages', 'components']

// 替换规则
const REPLACE_RULES = [
  {
    // 基础 v-img 指令
    pattern: /<img\s+([^>]*?)v-img="([^"]+)"([^>]*?)>/g,
    replacement: '<UniImage $1:src="$2"$3>'
  },
  {
    // 带点击预览的 v-img:click 指令
    pattern: /<img\s+([^>]*?)v-img:click="([^"]+)"([^>]*?)>/g,
    replacement: '<UniImage $1:src="$2" :preview="true"$3>'
  },
  {
    // img 标签替换为 UniImage（如果有 v-img）
    pattern: /<img(\s+[^>]*?v-img[^>]*?)>/g,
    replacement: '<UniImage$1>'
  }
]

// 统计信息
let stats = {
  filesScanned: 0,
  filesModified: 0,
  replacements: 0
}

/**
 * 递归扫描目录
 */
function scanDirectory(dir) {
  const files = []
  
  function scan(currentDir) {
    const items = fs.readdirSync(currentDir)
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory()) {
        // 跳过 node_modules 和 .git 目录
        if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
          scan(fullPath)
        }
      } else if (stat.isFile()) {
        const ext = path.extname(item)
        if (FILE_EXTENSIONS.includes(ext)) {
          files.push(fullPath)
        }
      }
    }
  }
  
  scan(dir)
  return files
}

/**
 * 处理单个文件
 */
function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    let newContent = content
    let fileModified = false
    let fileReplacements = 0
    
    // 应用所有替换规则
    for (const rule of REPLACE_RULES) {
      const matches = newContent.match(rule.pattern)
      if (matches) {
        newContent = newContent.replace(rule.pattern, rule.replacement)
        fileReplacements += matches.length
        fileModified = true
      }
    }
    
    // 如果文件被修改，写回文件
    if (fileModified) {
      fs.writeFileSync(filePath, newContent, 'utf8')
      stats.filesModified++
      stats.replacements += fileReplacements
      console.log(`✅ ${filePath} - 替换了 ${fileReplacements} 处`)
    }
    
    stats.filesScanned++
    
  } catch (error) {
    console.error(`❌ 处理文件失败: ${filePath}`, error.message)
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始迁移 v-img 指令到 UniImage 组件...\n')
  
  const startTime = Date.now()
  
  // 扫描所有需要处理的文件
  const allFiles = []
  for (const dir of SCAN_DIRS) {
    if (fs.existsSync(dir)) {
      const files = scanDirectory(dir)
      allFiles.push(...files)
      console.log(`📁 扫描目录 ${dir}: 找到 ${files.length} 个文件`)
    } else {
      console.log(`⚠️  目录不存在: ${dir}`)
    }
  }
  
  console.log(`\n📊 总共找到 ${allFiles.length} 个文件需要处理\n`)
  
  // 处理所有文件
  for (const file of allFiles) {
    processFile(file)
  }
  
  const endTime = Date.now()
  const duration = ((endTime - startTime) / 1000).toFixed(2)
  
  // 输出统计信息
  console.log('\n' + '='.repeat(50))
  console.log('📈 迁移完成统计:')
  console.log(`   扫描文件: ${stats.filesScanned}`)
  console.log(`   修改文件: ${stats.filesModified}`)
  console.log(`   总替换数: ${stats.replacements}`)
  console.log(`   耗时: ${duration}s`)
  console.log('='.repeat(50))
  
  if (stats.filesModified > 0) {
    console.log('\n✨ 迁移完成！请检查修改的文件并测试功能是否正常。')
    console.log('\n📝 后续步骤:')
    console.log('1. 确保 UniImage 组件已在 main.js 中全局注册')
    console.log('2. 检查修改的文件，确认替换结果正确')
    console.log('3. 运行项目测试功能是否正常')
    console.log('4. 可以考虑移除 main.js 中的 v-img 指令定义')
  } else {
    console.log('\n🎉 没有找到需要替换的 v-img 指令。')
  }
}

// 运行主函数
if (require.main === module) {
  main()
}

module.exports = {
  scanDirectory,
  processFile,
  REPLACE_RULES
}
