"use strict";
const plugins_xeUtils_isArray = require("./isArray.js");
const plugins_xeUtils_hasOwnProp = require("./hasOwnProp.js");
function forOf(obj, iterate, context) {
  if (obj) {
    if (plugins_xeUtils_isArray.isArray(obj)) {
      for (var index = 0, len = obj.length; index < len; index++) {
        if (iterate.call(context, obj[index], index, obj) === false) {
          break;
        }
      }
    } else {
      for (var key in obj) {
        if (plugins_xeUtils_hasOwnProp.hasOwnProp(obj, key)) {
          if (iterate.call(context, obj[key], key, obj) === false) {
            break;
          }
        }
      }
    }
  }
}
exports.forOf = forOf;
