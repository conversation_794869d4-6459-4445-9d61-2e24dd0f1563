/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*
	字体颜色相关
*/
/*
	背景色 分割线色
*/
/*
	主题色
*/
/*
		渐变可调角度
*/
/*
	定义弹性布局
 */
/*
	单行文本溢出隐藏
*/
.card .item.data-v-ea5d7690 {
  padding: 0 32rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  position: relative;
  margin-bottom: 24rpx;
  border-radius: 8px;
  background: linear-gradient(135.9deg, #859bff 0%, #eaf6ff 100%);
}
.card .item.data-v-ea5d7690:last-child {
  margin-bottom: 0;
}
.card .item.data-v-ea5d7690::before {
  left: -15rpx;
}
.card .item.data-v-ea5d7690::after {
  right: -15rpx;
}
.card .item .type.data-v-ea5d7690 {
  height: 80rpx;
  border-bottom: 1px solid #ebebeb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.card .item .type .left.data-v-ea5d7690 {
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  left: -50rpx;
}
.card .item .type .left .icon.data-v-ea5d7690 {
  width: 92rpx;
  height: 60rpx;
  margin-right: 16rpx;
  position: relative;
  top: 6rpx;
}
.card .item .type .left text.data-v-ea5d7690 {
  font-weight: bold;
}
.card .item .type .status.data-v-ea5d7690 {
  width: 84rpx;
  height: 40rpx;
  opacity: 1;
  border-radius: 30rpx;
  background: #859bff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20rpx;
}
.card .item .item_cont.data-v-ea5d7690 {
  padding: 10rpx 0;
  border-bottom: 1px solid #ebebeb;
}
.card .item .item_cont .cont_item.data-v-ea5d7690 {
  line-height: 50rpx;
  color: #ffffff;
}
.card .item .item_cont .cont_item .bold.data-v-ea5d7690 {
  font-weight: bold;
  color: #ffffff;
}
.card .item .item_time.data-v-ea5d7690 {
  line-height: 80rpx;
  color: white;
}
.card .item .item_time text.data-v-ea5d7690 {
  color: #ff3b30;
}
.card .item .item_footer.data-v-ea5d7690 {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15rpx;
  position: absolute;
  bottom: -10rpx;
  left: 0;
}
.card .item .item_footer text.data-v-ea5d7690 {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: #f5f5f5;
}