"use strict";
const plugins_xeUtils_helperCreateTreeFunc = require("./helperCreateTreeFunc.js");
const plugins_xeUtils_each = require("./each.js");
function eachTreeItem(parent, obj, iterate, context, path, node, parseChildren, opts) {
  var paths, nodes;
  plugins_xeUtils_each.each(obj, function(item, index) {
    paths = path.concat(["" + index]);
    nodes = node.concat([item]);
    iterate.call(context, item, index, obj, paths, parent, nodes);
    if (item && parseChildren) {
      paths.push(parseChildren);
      eachTreeItem(item, item[parseChildren], iterate, context, paths, nodes, parseChildren);
    }
  });
}
var eachTree = plugins_xeUtils_helperCreateTreeFunc.helperCreateTreeFunc(eachTreeItem);
exports.eachTree = eachTree;
