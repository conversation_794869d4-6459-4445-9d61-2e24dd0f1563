/*
  Author: 王可 (<EMAIL>)
  wx.js (c) 2021
  Desc: 代付微信sdk
  Created:  2021/12/20下午3:46:27
  Modified: 2021/12/21下午3:42:51
*/
import { getJSSDKSign } from '@/api/share.js';
const jweixin = require('jweixin-module');
import Tips from '@/components/payment/tip.vue';
import urlConfig from '@/common/request/config.js';


export default {
  components: {
    Tips,
  },
  data() {
    return {
      showTip: false,
    };
  },
  created() {
    this.sdkInit();
  },

  methods: {

    // 调起分享sdk
    async sdkInit() {
      let _this = this;
      const appId = uni.getStorageSync('appId');
      let { data: info } = await getJSSDKSign({
        appid: appId,
        url: window.location.href.split('#')[0],
      });

      jweixin.config({
        debug: false,
        appId,
        timestamp: info.timestamp,
        nonceStr: info.nonceStr,
        signature: info.signature,
        jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData', 'checkJsApi', 'openLocation', 'getLocation'],
      });

    },
    // 设置参数
    async setShare(total, orderNo, source, ghId) {
      const { shareLogoUrl, rootUrl } = urlConfig;
      const appId = uni.getStorageSync('appId');
      const title = '有一笔订单请你帮我付';
      const desc = total + '元';
      let link =
        rootUrl +
        '#/pages/payment/index?orderNo=' +
        orderNo +
        '&source=' +
        source +
        '&appid=' +
        appId;
      if (source == 1) {
        link += '&ghId=' + ghId;
      }

      // 好友
      jweixin.updateAppMessageShareData({
        title,
        desc,
        link,
        imgUrl: shareLogoUrl,
      });

      // 朋友圈
      jweixin.updateTimelineShareData({
        title,
        desc,
        link,
        imgUrl: shareLogoUrl,
      });
      return Promise.resolve();
    },
  },
};
