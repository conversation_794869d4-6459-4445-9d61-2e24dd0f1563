!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.websdk=t():e.websdk=t()}(self,(function(){return(self.webpackChunkwebsdk=self.webpackChunkwebsdk||[]).push([[77],{4490:function(e,t,r){var n=r(6518),a=r(9504),o=r(4376),s=a([].reverse),i=[1,2];n({target:"Array",proto:!0,forced:String(i)===String(i.reverse())},{reverse:function(){return o(this)&&(this.length=this.length),s(this)}})},9359:function(e,t,r){r.r(t),r.d(t,{acceptContactInvite:function(){return H},acceptInvitation:function(){return J},addContact:function(){return F},addConversationMark:function(){return me},addReaction:function(){return te},addToBlackList:function(){return V},addUsersToBlacklist:function(){return W},addUsersToBlocklist:function(){return Y},declineContactInvite:function(){return K},declineInvitation:function(){return q},deleteAllMessagesAndConversations:function(){return Ie},deleteContact:function(){return G},deleteConversation:function(){return w},deleteReaction:function(){return re},deleteSession:function(){return _},fetchHistoryMessages:function(){return U},fetchUserInfoById:function(){return P},getAllContacts:function(){return he},getBlacklist:function(){return C},getBlocklist:function(){return E},getContacts:function(){return j},getContactsWithCursor:function(){return fe},getConversationlist:function(){return N},getHistoryMessages:function(){return M},getReactionDetail:function(){return oe},getReactionList:function(){return ne},getReactionlist:function(){return ae},getRoster:function(){return S},getSelfIdsOnOtherPlatform:function(){return Te},getServerConversations:function(){return ce},getServerConversationsByFilter:function(){return ye},getServerPinnedConversations:function(){return le},getServerPinnedMessages:function(){return je},getSessionList:function(){return R},getTokenExpireTimestamp:function(){return B},modifyMessage:function(){return ee},pinConversation:function(){return de},pinMessage:function(){return Ee},recallMessage:function(){return $},removeConversationMark:function(){return ge},removeFromBlackList:function(){return X},removeHistoryMessages:function(){return ie},removeRoster:function(){return L},removeUserFromBlackList:function(){return Q},removeUserFromBlocklist:function(){return Z},reportMessage:function(){return se},setContactRemark:function(){return pe},unbindPushToken:function(){return be},unpinMessage:function(){return Se},updateCurrentUserNick:function(){return x},updateOwnUserInfo:function(){return A},updateUserInfo:function(){return z},uploadPushToken:function(){return k},uploadToken:function(){return b}}),r(2675),r(9463),r(2259),r(8706),r(2008),r(1629),r(4423),r(4346),r(3792),r(8598),r(2062),r(4490),r(4554),r(739),r(3288),r(2010),r(9085),r(9432),r(6099),r(3362),r(8781),r(7764),r(3500),r(2953);var n=r(1750),a=r(1531),o=r(346),s=r(8678),i=r(4723),c=r(8801),u=r(3887),l=r.n(u),d=r(2056),p=r(3893),h=r(565),f=r(8232),v=r(564),m=r(5323),g=function(){return g=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var a in t=arguments[r])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},g.apply(this,arguments)},y=function(e,t,r,n){return new(r||(r=Promise))((function(a,o){function s(e){try{c(n.next(e))}catch(e){o(e)}}function i(e){try{c(n.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?a(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,i)}c((n=n.apply(e,t||[])).next())}))},T=function(e,t){var r,n,a,o,s={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:i(0),throw:i(1),return:i(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function i(o){return function(i){return function(o){if(r)throw new TypeError("Generator is already executing.");for(;s;)try{if(r=1,n&&(a=2&o[0]?n.return:o[0]?n.throw||((a=n.return)&&a.call(n),0):n.next)&&!(a=a.call(n,o[1])).done)return a;switch(n=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!((a=(a=s.trys).length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){s.label=o[1];break}if(6===o[0]&&s.label<a[1]){s.label=a[1],a=o;break}if(a&&s.label<a[2]){s.label=a[2],s.ops.push(o);break}a[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(e){o=[6,e],n=0}finally{r=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,i])}}},I={singleChat:"chat",chatRoom:"chatroom",groupChat:"groupchat"};function C(e){var t=n.dO.call(this).error;if(t)return Promise.reject(t);var r=this.context,a=r.orgName,o=r.appName,i=r.accessToken,c={url:this.apiUrl+"/"+a+"/"+o+"/users/"+this.user+"/blocks/users",dataType:"json",type:"GET",headers:{Authorization:"Bearer "+i},success:function(t){var r={};t.data.forEach((function(e){r[e]={name:e}})),"function"==typeof(null==e?void 0:e.success)&&e.success(t)},error:function(t){"function"==typeof(null==e?void 0:e.error)&&e.error(t)}};return d.vF.debug("Call getBlocklist"),s.RD.call(this,c,p.jz.GET_BLACK_LIST)}var E=C,S=j;function j(e){var t=this,r=n.dO.call(this).error;if(r)return Promise.reject(r);var a=this.context,o=a.orgName,i=a.appName,c=a.accessToken,u={url:this.apiUrl+"/"+o+"/"+i+"/users/"+this.user+"/contacts/users",dataType:"json",type:"GET",headers:{Authorization:"Bearer "+c},success:function(r){var n=[];r.data.forEach((function(e){n.push({name:e,subscription:"both",jid:t.context.jid})})),"function"==typeof(null==e?void 0:e.success)&&e.success(n)},error:function(t){"function"==typeof(null==e?void 0:e.error)&&e.error(t)}};return d.vF.debug("Call getContacts"),s.RD.call(this,u,p.jz.GET_CONTACTS)}function b(e){return d.vF.debug("Call uploadPushToken"),m.B.call(this,e)}var k=b;function R(e){var t=n.dO.call(this).error;if(t)return Promise.reject(t);var r=this.context,a=r.orgName,o=r.appName,i=r.accessToken,c={url:"".concat(this.apiUrl,"/").concat(a,"/").concat(o,"/users/").concat(this.user,"/user_channels"),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"},success:e&&(null==e?void 0:e.success),error:e&&(null==e?void 0:e.error)};return d.vF.debug("Call getSessionList"),s.RD.call(this,c)}function N(e){var t=this,r=n.dO.call(this).error;if(r)return Promise.reject(r);var a=this.context,o=a.orgName,i=a.appName,c=a.accessToken,u=!!(e&&"number"==typeof e.pageNum&&"number"==typeof e.pageSize&&e.pageNum>0&&e.pageSize>0),l={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(i,"/users/").concat(this.user,"/user_channels").concat(u?"/page":""),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+c,"Content-Type":"application/json"},success:e&&(null==e?void 0:e.success),error:e&&(null==e?void 0:e.error)};return u&&(l.data={pageNum:e.pageNum,pageSize:e.pageSize}),d.vF.debug("Call getConversationlist"),s.RD.call(this,l,p.jz.GET_SESSION_LIST).then((function(e){return O.call(t,e)}))}function O(e){var t=this,r=e.data.channel_infos;return null==r||r.forEach((function(e){e.meta&&"{}"!==JSON.stringify(e.meta)?(e.meta.payload=JSON.parse(e.meta.payload),e.lastMessage=h.h.call(t,e.meta,{formatCustomExts:!1})):e.lastMessage=e.meta,delete e.meta})),e}function _(e){if(e&&"string"!=typeof e.channel)throw Error("Invalid parameter channel: ".concat(e.channel));if(e&&"singleChat"!==e.chatType&&"groupChat"!==e.chatType)throw Error("Invalid parameter chatType: ".concat(e.chatType));if(e&&"boolean"!=typeof e.deleteRoam)throw Error("Invalid parameter deleteRoam: ".concat(e.deleteRoam));var t=n.dO.call(this).error;if(t)return Promise.reject(t);var r,a=this.context,o=a.orgName,i=a.appName,c=a.accessToken,u=a.jid;r="singleChat"===e.chatType?"chat":"groupChat";var l={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(i,"/user/").concat(this.user,"/user_channel?resource=").concat(u.clientResource),dataType:"json",type:"DELETE",data:JSON.stringify({channel:e.channel,type:r,delete_roam:e.deleteRoam}),headers:{Authorization:"Bearer "+c,"Content-Type":"application/json"},success:e.success,error:e.error};return d.vF.debug("Call deleteSession",e),s.RD.call(this,l,p.jz.DELETE_SESSION)}var w=_;function A(e,t){var r=n.dO.call(this).error;if(r)return Promise.reject(r);var a=this.context,o=a.orgName,i=a.appName,c=a.accessToken,u=["nickname","avatarurl","mail","phone","gender","sign","birth","ext"],l={},h=s.Wp.getEnvInfo();if("wx"===h.platform||"qq"===h.platform)if("string"==typeof e&&void 0!==t){if(!u.includes(e))throw new Error("illegal key, only these keys: nickname, avatarurl, mail, phone, gender, sign, birth, ext are allowed");l[e]=t}else{if("[object Object]"!==Object.prototype.toString.call(e))throw new Error("illegal params");u.forEach((function(t){u.includes(t)&&void 0!==e[t]&&(l[t]=e[t])}))}else if("string"==typeof e){if(!u.includes(e))throw new Error("illegal key, only these keys: nickname, avatarurl, mail, phone, gender, sign, birth, ext are allowed");l=e+"="+t}else{if("[object Object]"!==Object.prototype.toString.call(e))throw new Error("illegal params");var f=[];u.forEach((function(t){if(u.includes(t)&&void 0!==e[t]){var r=encodeURIComponent(t),n=encodeURIComponent(e[t]);f.push(r+"="+n)}})),l=f.join("&")}var v={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(i,"/metadata/user/").concat(this.user),type:"PUT",data:l,dataType:"json",headers:{Authorization:"Bearer "+c,"Content-Type":"application/x-www-form-urlencoded"}};return d.vF.debug("Call updateOwnUserInfo",e),s.RD.call(this,v,p.jz.UPDATE_USER_INFO)}var z=A;function P(e,t){var r=n.dO.call(this).error;if(r)return Promise.reject(r);var a,o=this.context,i=o.orgName,c=o.appName,u=o.accessToken,l=[];if("string"==typeof e)l=[e];else{if("[object Array]"!==Object.prototype.toString.call(e))throw new Error("illegal params");l=e}a="string"==typeof t?[t]:t&&"[object Array]"===Object.prototype.toString.call(t)?t:["nickname","avatarurl","mail","phone","gender","sign","birth","ext"];var h={url:"".concat(this.apiUrl,"/").concat(i,"/").concat(c,"/metadata/user/get"),type:"POST",data:JSON.stringify({targets:l,properties:a}),dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"}};return d.vF.debug("Call fetchUserInfoById",e),s.RD.call(this,h,p.jz.GET_USER_INFO)}function x(e){var t=n.dO.call(this).error;if(t)return Promise.reject(t);var r=this.context,a=r.orgName,o=r.appName,i=r.accessToken,c={url:"".concat(this.apiUrl,"/").concat(a,"/").concat(o,"/users/").concat(this.user),type:"PUT",dataType:"json",data:JSON.stringify({nickname:e}),headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return d.vF.debug("Call updateCurrentUserNick",e),s.RD.call(this,c)}function B(e){if("string"!=typeof e||""===e)throw Error('Invalid parameter: "token"');var t=this.context,r=t.orgName,n=t.appName,a={url:"".concat(this.apiUrl,"/").concat(r,"/").concat(n,"/sdk/users/").concat(this.user,"/token/expires"),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+e,"Content-Type":"application/json"}};return d.vF.debug("Call getTokenExpireTimestamp",e),s.RD.call(this,a,p.jz.SDK_INTERNAL)}function U(e){var t=this;return new Promise((function(r,a){if(!e.queue)throw Error('Invalid parameter: "specified"');var o=n.dO.call(t).error;if(o)return Promise.reject(o);(function e(t){var n=this,o=t.count||20;D.call(this,{count:o,isGroup:!!t.isGroup,queue:t.queue,start:t.start,format:t.format,success:function(a){if(a.msgs.length>=o||a.is_last){var s=a.msgs.splice(0,o).reverse();t.success&&t.success(s),r(s)}else e.call(n,g(g({},t),{start:null}))},fail:function(e){a(e),t.fail&&t.fail(e)}})}).call(t,e),d.vF.debug("Call fetchHistoryMessages",e)}))}function D(e){var t=this,r=e.queue,n=this.mr_cache[r]||(this.mr_cache[r]={msgs:[]}),i=this.context.userId,u=e.start||-1,d=e.count||20;if(n.msgs.length>=d||n.is_last)"function"==typeof e.success&&e.success(n);else{n&&n.next_key&&(u=n.next_key),e.start&&(u=e.start);var h={queue:r+(e.isGroup?"@conference.easemob.com":"@easemob.com"),start:u,end:-1},f=this.context,v=f.orgName,m=f.appName,g={url:"".concat(this.apiUrl,"/").concat(v,"/").concat(m,"/users/").concat(i,"/messageroaming"),dataType:"json",type:"POST",headers:{Authorization:"Bearer "+this.token,"Content-Type":"application/json"},data:JSON.stringify(h),success:function(r){return y(t,void 0,void 0,(function(){var t,a,o,s,i,u,d=this;return T(this,(function(p){switch(p.label){case 0:if(t=null==r?void 0:r.data,!r.data.msgs)return"function"==typeof e.success&&e.success(n),n.is_last=!0,n.next_key="",[2];a=t.msgs,o=a.length,n.is_last=t.is_last,n.next_key=t.next_key,s=function(t){return y(d,void 0,void 0,(function(){var r,n,a,o,s;return T(this,(function(i){switch(i.label){case 0:for(r=[],t=l().atob(t),n=0,a=t.length;n<a;++n)r.push(t.charCodeAt(n));return o=(o=this.context.root.lookup("easemob.pb.Meta")).decode(r),s={errorCode:0,reason:""},1!==o.ns?[3,2]:[4,c.Ay.call(this,o,s,!0,e.format)];case 1:return[2,i.sent()];case 2:return[2]}}))}))},i=0,p.label=1;case 1:return i<o?[4,s(a[i].msg)]:[3,4];case 2:(u=p.sent())&&n.msgs.push(u),p.label=3;case 3:return i++,[3,1];case 4:return"function"==typeof e.success&&e.success(n),[2]}}))}))},error:function(e){if(e.error&&e.error_description){var r=o.A.create({type:a.C.WEBIM_CONNCTION_AJAX_ERROR,message:"fetch history messages error",data:e});t.onError&&t.onError(r)}}};s.RD.call(this,g,p.jz.GET_HISTORY_MSG).catch((function(t){"function"==typeof e.fail&&e.fail(t)}))}}function M(e){var t=this;return new Promise((function(r,a){var o=e.targetId,i=e.cursor,u=void 0===i?-1:i,h=e.pageSize,f=void 0===h?20:h,v=e.chatType,m=e.searchDirection,g=e.searchOptions,I=void 0===g?{}:g,C=I.msgTypes,E=void 0===C?[]:C,S=I.startTime,j=void 0===S?null:S,b=I.endTime,k=void 0===b?null:b,R=I.from,N=void 0===R?null:R;if("string"!=typeof o||""===o)throw Error('"Invalid parameter": "targetId"');if(N&&"string"!=typeof N)throw Error('"Invalid parameter": "searchOptions.from"');if(E&&!Array.isArray(E))throw Error('"Invalid parameter": "searchOptions.msgTypes"');if(j&&"number"!=typeof j)throw Error('"Invalid parameter": "searchOptions.startTime"');if(k&&"number"!=typeof j)throw Error('"Invalid parameter": "searchOptions.endTime"');var O=n.dO.call(t).error;if(O)return Promise.reject(O);var _=t.context,w=_.orgName,A=_.appName,z=_.userId,P="singleChat"===e.chatType?"@easemob.com":"@conference.easemob.com",x={queue:"".concat(o).concat(P),start:u,pull_number:f,is_positive:"down"===m,msgType:E.join(",")||"",end:-1,startTime:j,endTime:k,userId:"singleChat"===v?null:N},B={url:"".concat(t.apiUrl,"/").concat(w,"/").concat(A,"/users/").concat(z,"/messageroaming"),dataType:"json",type:"POST",headers:{Authorization:"Bearer "+t.token,"Content-Type":"application/json"},data:JSON.stringify(x),success:function(n){return y(t,void 0,void 0,(function(){var t,a,o,s,i,u,d,p,h=this;return T(this,(function(f){switch(f.label){case 0:t=null==n?void 0:n.data,a=t.msgs||[],o=function(e){return y(h,void 0,void 0,(function(){var t,r,n,a,o;return T(this,(function(s){switch(s.label){case 0:for(t=[],e=l().atob(e),r=0,n=e.length;r<n;++r)t.push(e.charCodeAt(r));return a=(a=this.context.root.lookup("easemob.pb.Meta")).decode(t),o={errorCode:0,reason:""},1!==a.ns?[3,2]:[4,c.Ay.call(this,a,o,!0,!0)];case 1:return[2,s.sent()];case 2:return[2]}}))}))},s=[],i=0,f.label=1;case 1:return i<a.length?[4,o(a[i].msg)]:[3,4];case 2:(u=f.sent())&&s.push(u),f.label=3;case 3:return i++,[3,1];case 4:return t.msgs=s,d={cursor:t.next_key,messages:s,isLast:t.is_last},null===(p=e.success)||void 0===p||p.call(e,d),r(d),[2]}}))}))},error:e.fail};s.RD.call(t,B,p.jz.REST_FETCHHISTORYMESSAGE).catch((function(e){a(e)})),d.vF.debug("Call getHistoryMessages",e)}))}function F(e,t){return y(this,void 0,void 0,(function(){var r,a,o,i,c,u,l;return T(this,(function(h){switch(h.label){case 0:if(r=n.dO.call(this).error)return[2,Promise.reject(r)];if("string"!=typeof e||""===e)throw Error('"Invalid parameter": "to"');return d.vF.debug("Call addContact",e,t),a=this.context,o=a.orgName,i=a.appName,c=a.accessToken,u=a.jid,l={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(i,"/users/").concat(this.user,"/contacts/apply?resource=").concat(u.clientResource),type:"POST",dataType:"json",data:JSON.stringify({usernames:[e],reason:t}),headers:{Authorization:"Bearer "+c,"Content-Type":"application/json"}},[4,s.RD.call(this,l,p.jz.ROSTER_ADD)];case 1:return h.sent(),[2]}}))}))}var L=G;function G(e){return y(this,void 0,void 0,(function(){var t,r,a,o,i,c,u;return T(this,(function(l){switch(l.label){case 0:if(t=n.dO.call(this).error)return[2,Promise.reject(t)];if("string"!=typeof e||""===e)throw Error('"Invalid parameter": "to"');return d.vF.debug("Call deleteContact",e),r=this.context,a=r.orgName,o=r.appName,i=r.accessToken,c=r.jid,u={url:"".concat(this.apiUrl,"/").concat(a,"/").concat(o,"/users/").concat(this.user,"/contacts/users/").concat(e,"?resource=").concat(c.clientResource),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}},[4,s.RD.call(this,u,p.jz.ROSTER_REMOVE)];case 1:return l.sent(),[2]}}))}))}function J(e){return y(this,void 0,void 0,(function(){var t,r,a,o,i,c,u;return T(this,(function(l){switch(l.label){case 0:if(t=n.dO.call(this).error)return[2,Promise.reject(t)];if("string"!=typeof e||""===e)throw Error('"Invalid parameter": "to"');return d.vF.debug("Call acceptInvitation",e),r=this.context,a=r.orgName,o=r.appName,i=r.accessToken,c=r.jid,u={url:"".concat(this.apiUrl,"/").concat(a,"/").concat(o,"/users/").concat(this.user,"/contacts/accept/users/").concat(e,"?resource=").concat(c.clientResource),type:"POST",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}},[4,s.RD.call(this,u,p.jz.ROSTER_ACCEPT)];case 1:return l.sent(),[2]}}))}))}var H=J;function q(e){return y(this,void 0,void 0,(function(){var t,r,a,o,i,c,u;return T(this,(function(l){switch(l.label){case 0:if(t=n.dO.call(this).error)return[2,Promise.reject(t)];if("string"!=typeof e||""===e)throw Error('"Invalid parameter": "to"');return d.vF.debug("Call declineInvitation",e),r=this.context,a=r.orgName,o=r.appName,i=r.accessToken,c=r.jid,u={url:"".concat(this.apiUrl,"/").concat(a,"/").concat(o,"/users/").concat(this.user,"/contacts/decline/users/").concat(e,"?resource=").concat(c.clientResource),type:"POST",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}},[4,s.RD.call(this,u,p.jz.ROSTER_DECLINE)];case 1:return l.sent(),[2]}}))}))}var K=q;function V(e){return y(this,void 0,void 0,(function(){var t,r,a,o,i,c,u,l,h;return T(this,(function(f){switch(f.label){case 0:if(t=n.dO.call(this).error)return[2,Promise.reject(t)];if(r=e.name,a=[],"string"==typeof r){if(""===r)throw Error('"Invalid parameter": "name"');a=[r]}else{if(!Array.isArray(r))throw Error('"Invalid parameter": "name"');a=r}return d.vF.debug("Call addToBlockList",e),o=this.context,i=o.orgName,c=o.appName,u=o.accessToken,l=o.jid,h={url:"".concat(this.apiUrl,"/").concat(i,"/").concat(c,"/sdk/user/").concat(this.user,"/blocks?resource=").concat(l.clientResource),type:"POST",dataType:"json",data:JSON.stringify({usernames:a}),headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"}},[4,s.RD.call(this,h,p.jz.ROSTER_BAN).then((function(e){return{type:e.type,data:{userIds:(null==e?void 0:e.data)||[]}}}))];case 1:return[2,f.sent()]}}))}))}var W=V,Y=V;function X(e){return y(this,void 0,void 0,(function(){var t,r,a,o,i,c,u,l,h;return T(this,(function(f){switch(f.label){case 0:if(t=n.dO.call(this).error)return[2,Promise.reject(t)];if(r=e.name,a=[],"string"==typeof r){if(""===r)throw Error('"Invalid parameter": "name"');a=[r]}else{if(!Array.isArray(r))throw Error('"Invalid parameter": "name"');a=r}return d.vF.debug("Call removeFromBlockList",e),o=this.context,i=o.orgName,c=o.appName,u=o.accessToken,l=o.jid,h={url:"".concat(this.apiUrl,"/").concat(i,"/").concat(c,"/sdk/user/").concat(this.user,"/blocks?resource=").concat(l.clientResource),type:"DELETE",dataType:"json",data:JSON.stringify({usernames:a}),headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"}},[4,s.RD.call(this,h,p.jz.ROSTER_ALLOW)];case 1:return f.sent(),[2]}}))}))}var Q=X,Z=X;function $(e){var t=this,r=(null==e?void 0:e.ext)||"";if("string"!=typeof r)throw Error('"Invalid parameter": "ext"',r);var n=this.getUniqueId(),a={id:n,to:e.to};this._msgHash[n]=g({},a);var o="";void 0!==e.chatType?o=e.chatType:void 0!==e.type&&(o="chat"===e.type?"singleChat":e.type);var s={id:n,type:"recall",chatType:o,ackId:e.mid,to:e.to,isChatThread:e.isChatThread||!1,metaExt:r,success:function(r){return y(t,void 0,void 0,(function(){var t,n,a,s,i,c,u,l,d;return T(this,(function(p){switch(p.label){case 0:return p.trys.push([0,5,,6]),"singleChat"!==o&&"groupChat"!==o?[3,4]:[4,null===(a=null===(n=this._localCache)||void 0===n?void 0:n.getInstance())||void 0===a?void 0:a.removeMsgByServerMsgId(e.mid)];case 1:return p.sent(),[4,null===(i=null===(s=this._localCache)||void 0===s?void 0:s.getInstance())||void 0===i?void 0:i.getConversationLastMessage(e.to,o)];case 2:return t=p.sent(),[4,null===(u=null===(c=this._localCache)||void 0===c?void 0:c.getInstance())||void 0===u?void 0:u.updateLocalConversation((0,v.u0)({conversationId:e.to,conversationType:o}),{lastMessageId:null==t?void 0:t.serverMsgId})];case 3:p.sent(),p.label=4;case 4:return null===(l=null==e?void 0:e.success)||void 0===l||l.call(e,r),[3,6];case 5:return p.sent(),null===(d=null==e?void 0:e.success)||void 0===d||d.call(e,r),[3,6];case 6:return[2]}}))}))},fail:e.fail};return d.vF.debug("Call recallMessage",e),this.mSync.send(s,this)}function ee(e){var t=e||{},r=t.messageId,n=t.modifiedMessage;if(d.vF.debug("Call modifyMessage",r,n),""===r)throw Error('Invalid parameter: "messageId"');return this.mSync.send(g({editMessageId:r},n))}function te(e){return y(this,void 0,void 0,(function(){var t,r,a,o,i,c,u,l,d;return T(this,(function(p){switch(p.label){case 0:if("string"!=typeof e.messageId||!e.messageId)throw Error("Invalid parameter messageId: ".concat(e.messageId));if("string"!=typeof e.reaction||!e.reaction)throw Error("Invalid parameter reaction: ".concat(e.reaction));return(t=n.dO.call(this).error)?[2,Promise.reject(t)]:(r=this.context,a=r.orgName,o=r.appName,i=r.accessToken,c=e.reaction,u=e.messageId,l={msgId:u,message:c},d={url:"".concat(this.apiUrl,"/").concat(a,"/").concat(o,"/reaction/user/").concat(this.user),type:"POST",data:JSON.stringify(l),dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}},[4,s.RD.call(this,d)]);case 1:return p.sent(),[2]}}))}))}function re(e){return y(this,void 0,void 0,(function(){var t,r,a,o,i,c,u,l;return T(this,(function(d){switch(d.label){case 0:if("string"!=typeof e.reaction||!e.reaction)throw Error("Invalid parameter reactionId: ".concat(e.reaction));return(t=n.dO.call(this).error)?[2,Promise.reject(t)]:(r=this.context,a=r.orgName,o=r.appName,i=r.accessToken,c=e.messageId,u=e.reaction,l={url:"".concat(this.apiUrl,"/").concat(a,"/").concat(o,"/reaction/user/").concat(this.user,"?msgId=").concat(c,"&message=").concat(u),type:"DELETE",dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}},[4,s.RD.call(this,l)]);case 1:return d.sent(),[2]}}))}))}function ne(e){if("string"!=typeof e.chatType||!e.chatType)throw Error("Invalid parameter chatType: ".concat(e.chatType));if(!e.messageId)throw Error("Invalid parameter messageId: ".concat(e.messageId));var t=n.dO.call(this).error;if(t)return Promise.reject(t);var r=this.context,a=r.orgName,o=r.appName,i=r.accessToken,c=e.chatType,u=e.messageId,l={msgIdList:"string"==typeof u?[u]:u,msgType:"singleChat"===c?"chat":"groupchat",groupId:e.groupId||null},d={url:"".concat(this.apiUrl,"/").concat(a,"/").concat(o,"/reaction/user/").concat(this.user),type:"GET",data:l,dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return s.RD.call(this,d).then((function(e){var t=e.data;return null==t||t.forEach((function(e){null==e||e.reactionList.forEach((function(e){e.isAddedBySelf=e.state,delete e.state,delete e.reactionId}))})),e}))}var ae=ne;function oe(e){if("string"!=typeof e.reaction||!e.reaction)throw Error("Invalid parameter reaction: ".concat(e.reaction));if("string"!=typeof e.messageId||!e.messageId)throw Error("Invalid parameter messageId: ".concat(e.messageId));var t=n.dO.call(this).error;if(t)return Promise.reject(t);var r=this.context,a=r.orgName,o=r.appName,i=r.accessToken,c=e.cursor,u=e.pageSize,l={msgId:e.messageId,message:e.reaction,currentPage:c||null,pageSize:u||20},d={url:"".concat(this.apiUrl,"/").concat(a,"/").concat(o,"/reaction/user/").concat(this.user,"/detail"),type:"GET",data:l,dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}};return s.RD.call(this,d).then((function(e){return e.data.isAddedBySelf=e.data.state,delete e.data.state,delete e.data.reactionId,e}))}function se(e){return y(this,void 0,void 0,(function(){var t,r,a,o,i,c,u,l,d,p,h;return T(this,(function(f){switch(f.label){case 0:if("string"!=typeof e.reportType||!e.reportType)throw Error("Invalid parameter reportType: ".concat(e.reportType));if("string"!=typeof e.messageId||!e.messageId)throw Error("Invalid parameter messageId: ".concat(e.messageId));if("string"!=typeof e.reportReason||!e.reportReason)throw Error("Invalid parameter messageId: ".concat(e.reportReason));return(t=n.dO.call(this).error)?[2,Promise.reject(t)]:(r=this.context,a=r.orgName,o=r.appName,i=r.accessToken,c=e.reportType,u=e.reportReason,l=e.messageId,d={username:this.user,reportType:c,reportReason:u},p="".concat(this.apiUrl,"/").concat(a,"/").concat(o,"/user/").concat(this.user,"/moderation/report/message/").concat(l),h={url:p,type:"POST",data:JSON.stringify(d),dataType:"json",headers:{Authorization:"Bearer "+i,"Content-Type":"application/json"}},[4,s.RD.call(this,h)]);case 1:return f.sent(),[2]}}))}))}function ie(e){var t;return y(this,void 0,void 0,(function(){var r,a,o,i,c,u,l,p,h;return T(this,(function(f){switch(f.label){case 0:if("string"!=typeof e.targetId||""===e.targetId)throw Error('"Invalid parameter targetId": '+e.targetId);if(!["singleChat","groupChat","chatRoom"].includes(e.chatType))throw Error('"Invalid parameter chatType": '+e.chatType);if(e.beforeTimeStamp&&("number"!=typeof e.beforeTimeStamp||e.beforeTimeStamp<0||(null===(t=e.beforeTimeStamp)||void 0===t?void 0:t.toString().length)>18))throw Error('"Invalid parameter beforeTimeStamp": '+e.beforeTimeStamp);if(e.messageIds&&!(Array.isArray(e.messageIds)&&e.messageIds.length>0&&e.messageIds.length<=20))throw Error('"Invalid parameter messageIds": '+e.messageIds);if(!e.messageIds&&!e.beforeTimeStamp)throw Error("messageIds or beforeTimeStamp field is required.");return(r=n.dO.call(this).error)?[2,Promise.reject(r)]:(a=this.context,o=a.orgName,i=a.appName,c=a.userId,u="singleChat"===e.chatType?"userId":"groupId",l="singleChat"===e.chatType?"chat":"group",p=e.messageIds?"".concat(this.apiUrl,"/").concat(o,"/").concat(i,"/sdk/message/roaming/").concat(l,"/user/").concat(c,"?").concat(u,"=").concat(e.targetId,"&msgIdList=").concat(e.messageIds,"&resource=").concat(this.clientResource):"".concat(this.apiUrl,"/").concat(o,"/").concat(i,"/sdk/message/roaming/").concat(l,"/user/").concat(c,"/time?").concat(u,"=").concat(e.targetId,"&delTime=").concat(e.beforeTimeStamp,"&&resource=").concat(this.clientResource),h={url:p,dataType:"json",type:"DELETE",headers:{Authorization:"Bearer "+this.token,"Content-Type":"application/json"}},d.vF.debug("Call removeHistoryMessages",e),[4,s.RD.call(this,h)]);case 1:return f.sent(),[2]}}))}))}function ce(e){var t=this,r=n.dO.call(this).error;if(r)return Promise.reject(r);if((null==e?void 0:e.pageSize)&&"number"!=typeof e.pageSize)throw Error("Invalid parameter pageSize: ".concat(e.pageSize));if((null==e?void 0:e.cursor)&&"string"!=typeof e.cursor)throw Error("Invalid parameter cursor: ".concat(e.cursor));var a=this.context,o=a.orgName,i=a.appName,c=a.accessToken,u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(i,"/sdk/user/").concat(this.user,"/user_channels/list?"),type:"GET",dataType:"json",data:{limit:(null==e?void 0:e.pageSize)||20,cursor:(null==e?void 0:e.cursor)||"",need_mark:!0},headers:{Authorization:"Bearer "+c,"Content-Type":"application/json"}};return d.vF.debug("Call getServerConversations",e),new Promise((function(e,r){s.RD.call(t,u,p.jz.REST_FETCH_CONVERSATIONS).then((function(r){return y(t,void 0,void 0,(function(){var t,n,a=this;return T(this,(function(o){switch(o.label){case 0:return t=ue.call(this,r),this._localCache?(n=t.data.conversations.map((function(e){return y(a,void 0,void 0,(function(){var t,r,n,a;return T(this,(function(o){switch(o.label){case 0:return[4,null===(r=null===(t=this._localCache)||void 0===t?void 0:t.getInstance())||void 0===r?void 0:r.storeMessage(e.lastMessage,f.q.SUCCESS,!0)];case 1:return o.sent(),[4,null===(a=null===(n=this._localCache)||void 0===n?void 0:n.getInstance())||void 0===a?void 0:a.updateLocalConversation((0,v.u0)({conversationId:e.conversationId,conversationType:e.conversationType}),{unReadCount:e.unReadCount})];case 2:return o.sent(),[2]}}))}))})),[4,Promise.all(n)]):[3,2];case 1:o.sent(),o.label=2;case 2:return e(t),[2]}}))}))})).catch((function(e){r(e)}))}))}function ue(e){var t=this,r=e.data,n=r.cursor,a=r.channel_infos,o=[];null==a||a.forEach((function(e){var r=null;(null==e?void 0:e.meta)&&"{}"!==JSON.stringify(e.meta)&&(e.meta.payload=JSON.parse(e.meta.payload),"delivery"!==(r=h.h.call(t,e.meta)).type&&"read"!==r.type&&"channel"!==r.type&&(r.chatType=h.p[r.chatType]));var n={conversationId:e.session_to,conversationType:"chat"===e.session_type?"singleChat":"groupChat",isPinned:e.is_top,pinnedTime:e.is_top?e.update_top_status_time:0,unReadCount:e.unread_num,lastMessage:r};e.marks&&(n.marks=e.marks.map((function(e){return i.s[e]}))),o.push(n)}));var s={conversations:o,cursor:n||""};return{type:e.type,data:s}}function le(e){var t=this,r=n.dO.call(this).error;if(r)return Promise.reject(r);if((null==e?void 0:e.pageSize)&&"number"!=typeof e.pageSize)throw Error("Invalid parameter pageSize: ".concat(e.pageSize));if((null==e?void 0:e.cursor)&&"string"!=typeof e.cursor)throw Error("Invalid parameter cursor: ".concat(e.cursor));var a=this.context,o=a.orgName,i=a.appName,c=a.accessToken,u={url:"".concat(this.apiUrl,"/").concat(o,"/").concat(i,"/sdk/user/").concat(this.user,"/user_channels/list?"),type:"GET",dataType:"json",data:{limit:(null==e?void 0:e.pageSize)||20,cursor:(null==e?void 0:e.cursor)||"",is_top:!0,need_mark:!0},headers:{Authorization:"Bearer "+c,"Content-Type":"application/json"}};return d.vF.debug("Call getServerPinnedConversations",e),s.RD.call(this,u,p.jz.GET_SESSION_LIST).then((function(e){return ue.call(t,e)}))}function de(e){return y(this,void 0,void 0,(function(){var t,r,a,o,i,c,u,l,h,f,v,m,g,y;return T(this,(function(T){if(t=n.dO.call(this).error)return[2,Promise.reject(t)];if(r=e.conversationId,a=e.conversationType,o=e.isPinned,i="singleChat"===a?"chat":"groupChat","string"!=typeof r||""===r)throw Error("Invalid parameter conversationId: ".concat(r));if(!["singleChat","groupChat"].includes(a))throw Error("Invalid parameter conversationType: ".concat(a));if("boolean"!=typeof o)throw Error("Invalid parameter isPinned: ".concat(o));return c=this.context,u=c.orgName,l=c.appName,h=c.accessToken,f=c.jid,v={type:i,to:r},m=o?"":"type=".concat(i,"&to=").concat(r,"&"),g="".concat(this.apiUrl,"/").concat(u,"/").concat(l,"/sdk/user/").concat(this.user,"/user_channel/top?").concat(m,"resource=").concat(f.clientResource),y={url:g,type:o?"POST":"DELETE",dataType:"json",headers:{Authorization:"Bearer "+h,"Content-Type":"application/json"}},o&&(y.data=JSON.stringify(v)),d.vF.debug("Call pinConversation",e),[2,s.RD.call(this,y,p.jz.PIN_CONVERSATION).then((function(e){var t=e.type,r=e.data;return{type:t,data:{isPinned:r.is_top||!1,pinnedTime:r.is_top?r.update_top_status_time:0}}}))]}))}))}function pe(e){return y(this,void 0,void 0,(function(){var t,r,a,o,i,c,u,l,h,f;return T(this,(function(v){switch(v.label){case 0:if(t=e.userId,r=e.remark,a=n.dO.call(this).error)return[2,Promise.reject(a)];if("string"!=typeof t||""===t)throw Error('Invalid parameter: "userId"');if("string"!=typeof r)throw Error('Invalid parameter: "remark"');return d.vF.debug("Call setContactRemark",e),o=this.context,i=o.orgName,c=o.appName,u=o.accessToken,l=o.jid,h="".concat(this.apiUrl,"/").concat(i,"/").concat(c,"/users/").concat(this.context.userId,"/contacts/users/").concat(t,"?resource=").concat(l.clientResource),f={url:h,type:"PUT",data:JSON.stringify({remark:r}),dataType:"json",headers:{Authorization:"Bearer "+u,"Content-Type":"application/json"}},[4,s.RD.call(this,f,p.jz.ROSTER_SET_CONTACT_REMARK)];case 1:return v.sent(),[2]}}))}))}function he(){var e=n.dO.call(this).error;if(e)return Promise.reject(e);var t=this.context,r=t.orgName,a=t.appName,o=t.accessToken,i={url:"".concat(this.apiUrl,"/").concat(r,"/").concat(a,"/users/").concat(this.user,"/contacts/users?needReturnRemark=true"),dataType:"json",type:"GET",headers:{Authorization:"Bearer "+o}};return d.vF.debug("Call getAllContacts"),s.RD.call(this,i,p.jz.ROSTER_GET_ALL_CONTACTS_REMARKS).then((function(e){var t=((null==e?void 0:e.entities)||[]).map((function(e){return{userId:e.username,remark:e.remark}}));return{type:e.type,data:t}}))}function fe(e){var t=n.dO.call(this).error;if(t)return Promise.reject(t);d.vF.debug("Call getContactsWithCursor",e);var r=e||{},a=r.pageSize,o=void 0===a?20:a,i=r.cursor,c=void 0===i?"":i;if(o&&"number"!=typeof o)throw Error("Invalid parameter pageSize: ".concat(e.pageSize));var u=this.context,l=u.orgName,h=u.appName,f=u.accessToken,v={url:"".concat(this.apiUrl,"/").concat(l,"/").concat(h,"/users/").concat(this.user,"/contacts?needReturnRemark=true&limit=").concat(o,"&cursor=").concat(c),dataType:"json",type:"GET",headers:{Authorization:"Bearer "+f}};return s.RD.call(this,v,p.jz.ROSTER_GET_ALL_CONTACTS_REMARKS_FROM_SERVER_BY_PAGE).then((function(e){var t,r,n=(null===(t=null==e?void 0:e.data)||void 0===t?void 0:t.contacts)||[],a=(null===(r=null==e?void 0:e.data)||void 0===r?void 0:r.cursor)||"",o=n.map((function(e){return{userId:e.username,remark:e.remark}}));return{type:e.type,data:{cursor:a,contacts:o}}}))}function ve(e){return y(this,void 0,void 0,(function(){var t,r,a,o,c,u,l,f,v,m,g,y,C,E,S;return T(this,(function(T){switch(T.label){case 0:if(t=n.dO.call(this).error)return[2,Promise.reject(t)];if(d.vF.debug("Call markConversation",e),a=(r=e||{}).conversations,o=void 0===a?[]:a,c=r.mark,u=r.isMarked,!Array.isArray(o))throw Error("Invalid parameter conversations");if(l=o.map((function(e){if(!e.conversationId||!["singleChat","groupChat"].includes(e.conversationType))throw Error("Invalid parameter conversations");return{to:e.conversationId,type:I[e.conversationType]}})),"boolean"!=typeof u)throw Error("Invalid parameter isMarked: ".concat(u));if("number"!=typeof c)throw Error("Invalid parameter mark: ".concat(c));return f=this.context,v=f.orgName,m=f.appName,g=f.accessToken,y=f.userId,C=f.jid,E={mark:i.s[c],targets:l},S={url:"".concat(this.apiUrl,"/").concat(v,"/").concat(m,"/sdk/user/").concat(y,"/user_channels/mark?resource=").concat(C.clientResource),dataType:"json",data:JSON.stringify(E),type:u?"Post":"Delete",headers:{Authorization:"Bearer "+g}},[4,s.RD.call(this,S,p.jz.MARK_CONVERSATION).then((function(e){var t,r=(null===(t=e.data)||void 0===t?void 0:t.ignore)||[];r&&Array.isArray(r)&&r.length>0&&d.vF.debug("markConversation has ignored conversations",r.map((function(e){return{conversationId:e.to,conversationType:h.p[e.type]}})))}))];case 1:return T.sent(),[2]}}))}))}function me(e){return y(this,void 0,void 0,(function(){var t,r;return T(this,(function(n){return d.vF.debug("Call addConversationMark",e),t=e.conversations,r=e.mark,[2,ve.call(this,{conversations:t,mark:r,isMarked:!0})]}))}))}function ge(e){return y(this,void 0,void 0,(function(){var t,r;return T(this,(function(n){return d.vF.debug("Call removeConversationMark",e),t=e.conversations,r=e.mark,[2,ve.call(this,{conversations:t,mark:r,isMarked:!1})]}))}))}function ye(e){var t,r,a=this,o=n.dO.call(this).error;if(o)return Promise.reject(o);if(d.vF.debug("Call getServerConversationsByFilter",e),(null==e?void 0:e.pageSize)&&"number"!=typeof e.pageSize)throw Error("Invalid parameter pageSize: ".concat(e.pageSize));if((null==e?void 0:e.cursor)&&"string"!=typeof e.cursor)throw Error("Invalid parameter cursor: ".concat(e.cursor));if("number"!=typeof(null===(t=null==e?void 0:e.filter)||void 0===t?void 0:t.mark))throw Error("Invalid parameter mark: ".concat(null===(r=null==e?void 0:e.filter)||void 0===r?void 0:r.mark));var c=this.context,u=c.orgName,l=c.appName,h=c.accessToken,f="".concat(this.apiUrl,"/").concat(u,"/").concat(l,"/sdk/user/").concat(this.user,"/user_channels/mark/search?"),v=e.pageSize,m=e.cursor,g=((null==e?void 0:e.filter)||{}).mark,y={url:f,type:"GET",dataType:"json",data:{limit:v||10,cursor:m||"",need_mark:!0,mark:i.s[g]},headers:{Authorization:"Bearer "+h,"Content-Type":"application/json"}};return s.RD.call(this,y,p.jz.GET_SESSION_LIST).then((function(e){return ue.call(a,e)}))}function Te(){var e=n.dO.call(this).error;if(e)return Promise.reject(e);d.vF.debug("Call getSelfIdsOnOtherPlatform");var t=this.context,r=t.orgName,a=t.appName,o=t.accessToken,i=t.userId,c=t.jid,u={url:"".concat(this.apiUrl,"/").concat(r,"/").concat(a,"/users/").concat(i,"/resources"),type:"GET",dataType:"json",headers:{Authorization:"Bearer "+o,"Content-Type":"application/json"}};return s.RD.call(this,u,p.jz.USER_LOGGEDIN_OTHER_PLATFORM).then((function(e){var t,r=null===(t=e.data)||void 0===t?void 0:t.filter((function(e){return e.res!==c.clientResource})),n=null==r?void 0:r.map((function(e){return"".concat(i,"/").concat(e.res)}));return{type:e.type,data:n}}))}function Ie(){var e,t,r,a,o,i;return y(this,void 0,void 0,(function(){var c,u,l,h,f,v,m,g,y;return T(this,(function(T){switch(T.label){case 0:return(c=n.dO.call(this).error)?[2,Promise.reject(c)]:(d.vF.debug("Call deleteAllMessagesAndConversations"),u=this.context,l=u.orgName,h=u.appName,f=u.accessToken,v=u.userId,m=u.jid,g="".concat(this.apiUrl,"/").concat(l,"/").concat(h,"/sdk/message/roaming/user/").concat(v,"/delete/all?resource=").concat(m.clientResource),y={url:g,type:"POST",dataType:"json",headers:{Authorization:"Bearer "+f,"Content-Type":"application/json"}},[4,s.RD.call(this,y,p.jz.REST_DELETE_MESSAGES_CONVERSATIONS)]);case 1:return T.sent(),null===(t=null===(e=this._localCache)||void 0===e?void 0:e.getInstance())||void 0===t||t.clearConversationMap(),[4,null===(a=null===(r=this._localCache)||void 0===r?void 0:r.getInstance())||void 0===a?void 0:a.clearStoreData("conversationList")];case 2:return T.sent(),[4,null===(i=null===(o=this._localCache)||void 0===o?void 0:o.getInstance())||void 0===i?void 0:i.clearStoreData("message")];case 3:return T.sent(),[2]}}))}))}function Ce(e){return y(this,void 0,void 0,(function(){var t,r,a,o,i,c,u,l,h,f,v,m,g,y,C;return T(this,(function(T){switch(T.label){case 0:if(t=n.dO.call(this).error)return[2,Promise.reject(t)];if(d.vF.debug("Call setMessagePinStatus",e),a=(r=e||{}).conversationId,o=void 0===a?"":a,i=r.conversationType,c=r.messageId,u=r.isPinned,"string"!=typeof o||""===o)throw Error("Invalid parameter conversationId");if("string"!=typeof c||""===c)throw Error("Invalid parameter messageId");if("boolean"!=typeof u)throw Error("Invalid parameter isPinned");return l=this.context,h=l.orgName,f=l.appName,v=l.accessToken,m=l.userId,g=l.jid,y={to:o,type:I[i],pin_msg_id:c},C={url:"".concat(this.apiUrl,"/").concat(h,"/").concat(f,"/sdk/user/").concat(m,"/user_channel/pin?resource=").concat(g.clientResource),dataType:"json",data:JSON.stringify(y),type:u?"Post":"Delete",headers:{Authorization:"Bearer "+v}},[4,s.RD.call(this,C,p.jz.REST_PIN_MESSAGE)];case 1:return T.sent(),[2]}}))}))}function Ee(e){return y(this,void 0,void 0,(function(){var t,r,n,a;return T(this,(function(o){return d.vF.debug("Call pinMessage",e),r=(t=e||{}).conversationType,n=t.conversationId,a=t.messageId,[2,Ce.call(this,{conversationId:n,conversationType:r,messageId:a,isPinned:!0})]}))}))}function Se(e){return y(this,void 0,void 0,(function(){var t,r,n,a;return T(this,(function(o){return d.vF.debug("Call unpinMessage",e),r=(t=e||{}).conversationType,n=t.conversationId,a=t.messageId,[2,Ce.call(this,{conversationId:n,conversationType:r,messageId:a,isPinned:!1})]}))}))}function je(e){var t=this,r=n.dO.call(this).error;if(r)return Promise.reject(r);d.vF.debug("Call getServerPinnedMessages",e);var a=e.conversationId,o=e.conversationType,i=e.pageSize,c=e.cursor;if("string"!=typeof a||""===a)throw Error("Invalid parameter conversationId");if(i&&"number"!=typeof i)throw Error("Invalid parameter pageSize: ".concat(i));if(c&&"string"!=typeof c)throw Error("Invalid parameter cursor: ".concat(c));var u=this.context,l=u.orgName,f=u.appName,v=u.accessToken,m={url:"".concat(this.apiUrl,"/").concat(l,"/").concat(f,"/sdk/user/").concat(this.user,"/user_channel/pin"),type:"GET",dataType:"json",data:{to:a,type:I[o],limit:i||10,cursor:c||""},headers:{Authorization:"Bearer "+v,"Content-Type":"application/json"}};return s.RD.call(this,m,p.jz.GET_SESSION_LIST).then((function(e){return{type:0,data:{list:e.data.msg_infos.map((function(e){e.message.payload=JSON.parse(e.message.payload);var r=e.message,n=e.pin_opt_at,a=e.pin_operator;return{message:h.h.call(t,r,{formatChatType:!0}),pinTime:n,operatorId:a}})),cursor:e.data.cursor||""}}}))}function be(){var e=this;d.vF.debug("Call unbindPushToken");var t={deviceId:this.clientResource,deviceToken:"",notifierName:this.pushCertificateName};return m.B.call(this,t).then((function(t){return e.isRegisterPush=!1,t}))}}},function(e){return e(e.s=9359)}])}));